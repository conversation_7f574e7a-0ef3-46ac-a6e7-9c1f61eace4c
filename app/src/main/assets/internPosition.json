{"dataVersion": 4.2, "internPosition": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100101, "positionType": 0, "name": "Java", "rank": 0, "mark": 0}], "positionType": 0, "name": "Java", "rank": 0, "value": "Java程序员使用Java语言进行跨平台应用程序的开发与维护，擅长处理大规模数据，主导构建企业级应用。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100102, "positionType": 0, "name": "C/C++", "rank": 0, "mark": 0}], "positionType": 0, "name": "C++", "rank": 0, "value": "C++程序员主要在系统编程、游戏开发、实时系统等领域工作，对算法和数据结构有深厚理解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101301, "positionType": 0, "name": "机器学习", "rank": 0, "mark": 0}], "positionType": 0, "name": "机器学习", "rank": 0, "value": "机器学习职业涉及开发和应用算法，使计算机、软件和机器能从数据中学习。他们设计模型，利用数据训练模型，并将模型应用于预测、自动决策或增强人类理解等任务中。这类职业通常需要具备编程、统计学、线性代数、数据分析、机器学习理论和实践等领域的知识，同时也需要有解决复杂问题的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100109, "positionType": 0, "name": "Python", "rank": 0, "mark": 0}], "positionType": 0, "name": "Python", "rank": 0, "value": "Python开发者专注于使用Python语言进行各类应用开发，如网络爬虫、数据分析，以及AI和机器学习等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101310, "positionType": 0, "name": "大模型算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101311, "positionType": 0, "name": "规控算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101312, "positionType": 0, "name": "SLAM算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100104, "positionType": 0, "name": "数据挖掘", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100115, "positionType": 0, "name": "搜索算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100117, "positionType": 0, "name": "自然语言处理算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100118, "positionType": 0, "name": "推荐算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100120, "positionType": 0, "name": "算法工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101308, "positionType": 0, "name": "自动驾驶系统工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101309, "positionType": 0, "name": "风控算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101301, "positionType": 0, "name": "机器学习", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101302, "positionType": 0, "name": "深度学习", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101305, "positionType": 0, "name": "语音算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101306, "positionType": 0, "name": "图像算法", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101307, "positionType": 0, "name": "算法研究员", "rank": 0, "mark": 0}], "positionType": 0, "name": "算法工程师", "rank": 0, "value": "算法工程师负责设计和优化算法，处理和解决各类复杂问题，尤其在人工智能和机器学习领域有显著作用。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100514, "positionType": 0, "name": "爬虫工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100104, "positionType": 0, "name": "数据挖掘", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100122, "positionType": 0, "name": "数据采集", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100506, "positionType": 0, "name": "ETL工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100507, "positionType": 0, "name": "数据仓库", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100508, "positionType": 0, "name": "数据开发", "rank": 0, "mark": 0}], "positionType": 0, "name": "数据开发", "rank": 0, "value": "数据开发人员负责构建、优化数据处理流程，以及开发相应的数据处理工具，对海量数据进行分析并产出有价值的信息。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100511, "positionType": 0, "name": "数据分析师", "rank": 0, "mark": 0}], "positionType": 0, "name": "数据分析师", "rank": 0, "value": "数据分析师通过运用各种统计分析方法，从大量数据中提取有价值的信息，以指导企业决策。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000019, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100125, "positionType": 0, "name": "区块链工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100101, "positionType": 0, "name": "Java", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100102, "positionType": 0, "name": "C/C++", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100103, "positionType": 0, "name": "PHP", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100106, "positionType": 0, "name": "C#", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100107, "positionType": 0, "name": ".NET", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100109, "positionType": 0, "name": "Python", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100114, "positionType": 0, "name": "Node.js", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100116, "positionType": 0, "name": "Golang", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100121, "positionType": 0, "name": "语音/视频/图形开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100123, "positionType": 0, "name": "全栈工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100124, "positionType": 0, "name": "GIS工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100199, "positionType": 0, "name": "其他后端开发", "rank": 0, "mark": 0}], "positionType": 0, "name": "后端开发", "rank": 0, "value": "后端开发人员负责服务器端的程序编写，以及系统设计，保证服务器与用户的交互过程顺畅，处理业务逻辑，保证数据的安全和稳定。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000021, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100301, "positionType": 0, "name": "测试工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100302, "positionType": 0, "name": "自动化测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100303, "positionType": 0, "name": "功能测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100304, "positionType": 0, "name": "性能测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100305, "positionType": 0, "name": "测试开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100307, "positionType": 0, "name": "游戏测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100308, "positionType": 0, "name": "硬件测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100309, "positionType": 0, "name": "软件测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100310, "positionType": 0, "name": "渗透测试", "rank": 0, "mark": 0}], "positionType": 0, "name": "测试", "rank": 0, "value": "测试工程师的工作重点是设计测试方案，并进行产品的功能、性能、安全性等方面的测试，确保产品达到预期的效果。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000022, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100410, "positionType": 0, "name": "技术文档工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100401, "positionType": 0, "name": "运维工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100402, "positionType": 0, "name": "运维开发工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100403, "positionType": 0, "name": "网络工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100404, "positionType": 0, "name": "系统工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100405, "positionType": 0, "name": "IT技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100406, "positionType": 0, "name": "系统管理员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100409, "positionType": 0, "name": "DBA", "rank": 0, "mark": 0}], "positionType": 0, "name": "运维/技术支持", "rank": 0, "value": "运维/技术支持主要维护企业的IT系统、网络和应用，解决使用问题，保证信息安全。他们可能是系统管理员、网络工程师、技术文档工程师、IT技术支持或运维工程师等，需掌握深厚的专业知识和技能。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000023, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100601, "positionType": 0, "name": "项目经理/主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100603, "positionType": 0, "name": "项目专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100605, "positionType": 0, "name": "实施顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100606, "positionType": 0, "name": "实施工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100607, "positionType": 0, "name": "需求分析工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100817, "positionType": 0, "name": "硬件项目经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "技术项目管理", "rank": 0, "value": "技术项目管理职位在互联网/AI行业中，是负责项目整个生命周期的管理，包括项目计划、任务分配、风险控制、进度跟踪和结果评估等，以确保项目的顺利进行和目标实现。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000025, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100212, "positionType": 0, "name": "技术美术", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100202, "positionType": 0, "name": "Android", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100203, "positionType": 0, "name": "iOS", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100208, "positionType": 0, "name": "JavaScript", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100209, "positionType": 0, "name": "U3D", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100210, "positionType": 0, "name": "Cocos", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100211, "positionType": 0, "name": "UE4", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100901, "positionType": 0, "name": "前端开发工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "前端/移动开发", "rank": 0, "value": "前端/移动开发是计算机软件开发的重要部分，关注用户界面和用户体验的设计和实现。他们可能是技术美术、前端开发工程师等职务。这些职务需要良好的设计和编程技能，以及对用户需求和行为的理解。他们需要持续跟踪和学习新的设计趋势和技术，以创造出更好的用户体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000026, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100407, "positionType": 0, "name": "网络安全", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100408, "positionType": 0, "name": "系统安全", "rank": 0, "mark": 0}], "positionType": 0, "name": "网络/系统安全", "rank": 0, "value": "网络或系统安全专家致力于保护公司的网络和信息系统免受各种攻击，如黑客攻击、数据泄漏等，他们通过设立防火墙、加密数据等手段确保信息安全。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000027, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101299, "positionType": 0, "name": "销售技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 160303, "positionType": 0, "name": "客户成功", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101201, "positionType": 0, "name": "售前技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101202, "positionType": 0, "name": "售后技术支持", "rank": 0, "mark": 0}], "positionType": 0, "name": "销售技术支持", "rank": 0, "value": "销售技术支持人员负责提供技术知识，支持销售团队解答客户的技术问题，协助销售团队达成销售目标，同时提升客户对产品或服务的满意度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 10000028, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130121, "positionType": 0, "name": "数据标注/AI训练师", "rank": 0, "mark": 0}], "positionType": 0, "name": "数据标注/AI训练师", "rank": 0, "value": "数据标注/AI训练师是AI和机器学习项目的关键角色之一。他们负责对原始数据进行标注和分类，为算法训练提供“教材”，从而帮助机器学习模型理解和学习现实世界。该职务通常需要具有细心、耐心和良好的数据理解能力。", "mark": 0}], "positionType": 0, "name": "互联网/AI", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 110110, "positionType": 0, "name": "AI产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110101, "positionType": 0, "name": "产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110103, "positionType": 0, "name": "移动产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110105, "positionType": 0, "name": "数据产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110106, "positionType": 0, "name": "电商产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110108, "positionType": 0, "name": "产品专员/助理", "rank": 0, "mark": 0}], "positionType": 0, "name": "互联网产品经理", "rank": 0, "value": "互联网产品经理通常负责制定产品战略、管理产品开发过程，以及协调相关团队，他们的目标是提供最符合市场需求的互联网产品。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120305, "positionType": 0, "name": "系统策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110107, "positionType": 0, "name": "游戏策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 110303, "positionType": 0, "name": "游戏制作人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120303, "positionType": 0, "name": "游戏数值策划", "rank": 0, "mark": 0}], "positionType": 0, "name": "游戏策划/制作", "rank": 0, "value": "游戏策划/制作人员负责游戏的整体规划和制作过程，他们的工作包括创新游戏概念、设定故事剧情、管理开发进度等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 110109, "positionType": 0, "name": "硬件产品经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "硬件产品经理", "rank": 0, "value": "硬件产品经理负责硬件产品从构思到市场的全过程，包括产品设计、制造、市场推广等，他们需要了解市场需求和技术趋势。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 110111, "positionType": 0, "name": "化妆品产品经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "化妆品产品经理", "rank": 0, "value": "化妆品产品经理主要负责化妆品的市场调研、产品策划、产品开发和产品推广等工作，他们需要了解并跟踪化妆品市场的最新动态。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 11000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180501, "positionType": 0, "name": "金融产品经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "金融产品经理", "rank": 0, "value": "金融产品经理主要负责金融产品的设计、开发和推广，他们需要深入理解金融市场趋势和客户需求，以创建具有竞争力的金融产品。", "mark": 0}], "positionType": 0, "name": "产品", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130104, "positionType": 0, "name": "内容运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130111, "positionType": 0, "name": "新媒体运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130113, "positionType": 0, "name": "微信运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130122, "positionType": 0, "name": "直播运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170108, "positionType": 0, "name": "视频运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "内容运营", "rank": 0, "value": "内容运营人员负责制定并执行内容策略，通过创建、发布、管理、优化各类内容，以吸引并留住目标用户，促进产品和品牌的发展。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130111, "positionType": 0, "name": "新媒体运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "新媒体运营", "rank": 0, "value": "新媒体运营主要通过运营微博、微信、短视频等新媒体平台，发布吸引人的内容，以提升品牌知名度和用户粘性。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130102, "positionType": 0, "name": "产品运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "产品运营", "rank": 0, "value": "产品运营主要负责产品的推广和优化，包括但不限于产品上线策划、活动策划、用户行为分析、产品优化改进等工作。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140105, "positionType": 0, "name": "SEO", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140106, "positionType": 0, "name": "SEM", "rank": 0, "mark": 0}], "positionType": 0, "name": "SEO/SEM", "rank": 0, "value": "SEO/SEM专家利用搜索引擎优化（SEO）和搜索引擎营销（SEM）的技巧，提高网站在搜索引擎中的排名，吸引更多的用户流量。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130301, "positionType": 0, "name": "售前客服", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130302, "positionType": 0, "name": "售后客服", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130303, "positionType": 0, "name": "网络客服", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130304, "positionType": 0, "name": "客服经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130305, "positionType": 0, "name": "客服专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130306, "positionType": 0, "name": "客服主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130308, "positionType": 0, "name": "电话客服", "rank": 0, "mark": 0}], "positionType": 0, "name": "客服", "rank": 0, "value": "客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，从而增强用户对公司或产品的信任和满意度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130203, "positionType": 0, "name": "文案编辑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130204, "positionType": 0, "name": "网站编辑", "rank": 0, "mark": 0}], "positionType": 0, "name": "编辑", "rank": 0, "value": "编辑主要负责内容的审核、修改和优化，他们需要有强大的语言组织能力和对内容的敏锐洞察力，以提供高质量的内容。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130203, "positionType": 0, "name": "文案编辑", "rank": 0, "mark": 0}], "positionType": 0, "name": "文案策划", "rank": 0, "value": "文案策划者负责创作各种宣传文案，包括广告、活动推广、产品说明等，以吸引目标受众。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130126, "positionType": 0, "name": "淘宝运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130127, "positionType": 0, "name": "天猫运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130128, "positionType": 0, "name": "京东运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130129, "positionType": 0, "name": "拼多多运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130117, "positionType": 0, "name": "国内电商运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "国内电商运营", "rank": 0, "value": "负责国内电子商务平台/店铺的整体运营与日常管理，包括货品上下架，店铺推广等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000016, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130130, "positionType": 0, "name": "亚马逊运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130131, "positionType": 0, "name": "速卖通运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130132, "positionType": 0, "name": "阿里国际站运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130133, "positionType": 0, "name": "跨境电商产品开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130124, "positionType": 0, "name": "跨境电商运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "跨境电商运营", "rank": 0, "value": "跨境电商运营涉及在全球范围内进行的电子商务运营活动，需要处理跨国电商的特殊问题，如关税、国际物流、外汇等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000020, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130101, "positionType": 0, "name": "用户运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130102, "positionType": 0, "name": "产品运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130103, "positionType": 0, "name": "数据/策略运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130106, "positionType": 0, "name": "商家运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130108, "positionType": 0, "name": "游戏运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130110, "positionType": 0, "name": "网站运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130112, "positionType": 0, "name": "社群运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130118, "positionType": 0, "name": "运营助理/专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130120, "positionType": 0, "name": "内容审核", "rank": 0, "mark": 0}], "positionType": 0, "name": "业务运营", "rank": 0, "value": "业务运营专门负责公司业务的日常运营管理，包括优化业务流程、提升业务效率、保障业务顺利进行等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000021, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 130107, "positionType": 0, "name": "品类运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "品类运营", "rank": 0, "value": "发现不同品类的用户、商品、商家痛点，从而解决问题、提升服务、促进细分市场发展的运营人员。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 12000022, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290314, "positionType": 0, "name": "商场运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130116, "positionType": 0, "name": "线下拓展运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130123, "positionType": 0, "name": "车辆运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "线下运营", "rank": 0, "value": "线下运营是一个涵盖多个任务和职责的角色，主要目标是通过在实体环境中执行各种策略和活动来推动业务增长。这通常需要深入了解产品或服务，以及目标市场和消费者。", "mark": 0}], "positionType": 0, "name": "运营/客服", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120101, "positionType": 0, "name": "视觉设计师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120105, "positionType": 0, "name": "UI设计师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120106, "positionType": 0, "name": "平面设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120116, "positionType": 0, "name": "CAD绘图员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120117, "positionType": 0, "name": "美工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120119, "positionType": 0, "name": "设计师助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120201, "positionType": 0, "name": "UX/交互设计师", "rank": 0, "mark": 0}], "positionType": 0, "name": "视觉/交互设计", "rank": 0, "value": "视觉/交互设计师负责产品的视觉效果和用户交互体验的设计，他们需要懂得如何通过视觉元素和交互设计来提升产品的用户体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100212, "positionType": 0, "name": "技术美术", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120305, "positionType": 0, "name": "系统策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120306, "positionType": 0, "name": "游戏主美术", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120111, "positionType": 0, "name": "游戏特效", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120112, "positionType": 0, "name": "游戏UI设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120113, "positionType": 0, "name": "游戏场景", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120114, "positionType": 0, "name": "游戏角色", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120115, "positionType": 0, "name": "游戏动作", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120303, "positionType": 0, "name": "游戏数值策划", "rank": 0, "mark": 0}], "positionType": 0, "name": "游戏设计", "rank": 0, "value": "游戏设计师负责整个游戏的创新和设计，包括游戏规则、剧情、角色和用户体验，使游戏具有吸引力和趣味性。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120107, "positionType": 0, "name": "3D设计师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120110, "positionType": 0, "name": "原画师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120120, "positionType": 0, "name": "动画设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120121, "positionType": 0, "name": "插画师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120122, "positionType": 0, "name": "漫画师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120123, "positionType": 0, "name": "修图师", "rank": 0, "mark": 0}], "positionType": 0, "name": "美术/3D/动画", "rank": 0, "value": "美术/3D/动画设计师负责绘制美术图像、制作3D模型和动画效果，他们是视觉表达的艺术家，能把创意转化为视觉效果。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120118, "positionType": 0, "name": "包装设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120602, "positionType": 0, "name": "工业设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120604, "positionType": 0, "name": "家具设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120606, "positionType": 0, "name": "珠宝设计", "rank": 0, "mark": 0}], "positionType": 0, "name": "工业/家居设计", "rank": 0, "value": "工业/家居设计师专注于产品和空间设计，他们要考虑美观、实用和生产工艺，打造符合人们使用习惯和审美的产品或空间。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120302, "positionType": 0, "name": "用户研究", "rank": 0, "mark": 0}], "positionType": 0, "name": "用户研究", "rank": 0, "value": "用户研究员主要进行用户需求研究和用户行为分析，他们通过调研和分析，了解用户的真实需求，为产品或服务的设计提供依据。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300507, "positionType": 0, "name": "面料辅料开发", "rank": 0, "mark": 0}], "positionType": 0, "name": "服装设计", "rank": 0, "value": "服装设计师负责服装的创新和设计，他们要关注时尚趋势、理解消费者需求，设计出时尚、美观、舒适的服装。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 13000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 120611, "positionType": 0, "name": "展览/展示设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120612, "positionType": 0, "name": "照明设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120608, "positionType": 0, "name": "陈列设计", "rank": 0, "mark": 0}], "positionType": 0, "name": "展示/照明设计", "rank": 0, "value": "展示/照明设计师负责空间布局、展品陈列和照明效果设计，他们的设计能营造出吸引人的展览环境，提升观展体验。", "mark": 0}], "positionType": 0, "name": "设计", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170102, "positionType": 0, "name": "编辑", "rank": 0, "mark": 0}], "positionType": 0, "name": "新闻/编辑", "rank": 0, "value": "新闻编辑或者记者，是负责采集、编写和发布新闻的人员。他们要关注社会热点，以客观、准确、及时的方式向公众传达信息。对于新闻编辑，良好的文字功底，敏锐的新闻嗅觉，以及公正的新闻伦理是非常重要的。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170630, "positionType": 0, "name": "模特", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170611, "positionType": 0, "name": "演员/配音员", "rank": 0, "mark": 0}], "positionType": 0, "name": "演员/配音/模特", "rank": 0, "value": "这个领域的专业人员通过身体、声音或外貌表现来进行艺术创作。他们可能出演电影、电视、舞台剧，为动画或电影配音，或者为时尚、广告做模特。他们需要有出色的演技，对角色有深入的理解，并且对自己的形象有严格的管理。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170626, "positionType": 0, "name": "剧本杀主持人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170627, "positionType": 0, "name": "剧本杀编剧", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120120, "positionType": 0, "name": "动画设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 120123, "positionType": 0, "name": "修图师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130122, "positionType": 0, "name": "直播运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170617, "positionType": 0, "name": "艺人助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170621, "positionType": 0, "name": "中控/场控/助播", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170622, "positionType": 0, "name": "灯光师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170624, "positionType": 0, "name": "影视特效", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170601, "positionType": 0, "name": "导演/编导", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170602, "positionType": 0, "name": "摄影/摄像师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170603, "positionType": 0, "name": "视频剪辑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170604, "positionType": 0, "name": "音频编辑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170605, "positionType": 0, "name": "经纪人/星探", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170606, "positionType": 0, "name": "后期制作", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170608, "positionType": 0, "name": "影视发行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170609, "positionType": 0, "name": "影视策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170611, "positionType": 0, "name": "演员/配音员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170614, "positionType": 0, "name": "录音/音效", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170615, "positionType": 0, "name": "制片人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170616, "positionType": 0, "name": "编剧", "rank": 0, "mark": 0}], "positionType": 0, "name": "影视媒体", "rank": 0, "value": "影视媒体涵盖了电影、电视、网络视频等多种形式的内容创作和发布。影视媒体的工作职责和角色多种多样，包括制片人、导演、编剧、演员、摄影师、剪辑师、音效师、模特等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170109, "positionType": 0, "name": "印刷排版", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170101, "positionType": 0, "name": "记者/采编", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170102, "positionType": 0, "name": "编辑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170104, "positionType": 0, "name": "作者/撰稿人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170105, "positionType": 0, "name": "出版发行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170106, "positionType": 0, "name": "校对录入", "rank": 0, "mark": 0}], "positionType": 0, "name": "采编/写作/出版", "rank": 0, "value": "这个职位涵盖了内容创作和发布的所有环节，包括新闻采集、文章撰写、书籍编辑等，他们通过文字传播信息和知识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170613, "positionType": 0, "name": "放映员", "rank": 0, "mark": 0}], "positionType": 0, "name": "放映员", "rank": 0, "value": "放映员主要负责影院的电影放映工作，他们需要熟练操作放映设备，确保电影的顺利播放，为观众提供良好的观影体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170628, "positionType": 0, "name": "儿童引导师", "rank": 0, "mark": 0}], "positionType": 0, "name": "儿童引导师", "rank": 0, "value": "儿童引导师是一个专门角色，他们通常在儿童摄影环境中工作，帮助引导和安抚儿童，以便摄影师可以捕捉到最好的照片。他们的角色通常包括让儿童在拍摄过程中感到舒适，创造有趣的环境，引导儿童进行自然的表现，并协助摄影师实现拍摄目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 14000016, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 170625, "positionType": 0, "name": "带货主播", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170629, "positionType": 0, "name": "游戏主播", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170620, "positionType": 0, "name": "主持人/DJ", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 170610, "positionType": 0, "name": "主播", "rank": 0, "mark": 0}], "positionType": 0, "name": "主持人/主播/DJ", "rank": 0, "value": "主持人/主播/DJ是媒体和娱乐行业中的重要角色。他们主要负责引导和控制节目或直播的流程，包括电视、电台、活动和网络直播、通过直播的方式进行商品的推介和销售等。他们必须具备良好的语言表达和沟通能力，能迅速适应变化并处理突发事件。这类职业需要熟悉媒体生产和广播设备，并能在压力下工作。他们还需要有出色的情感智商，能理解和激发听众观众的情绪。", "mark": 0}], "positionType": 0, "name": "影视/传媒", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 16000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 16000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150111, "positionType": 0, "name": "企业文化", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150102, "positionType": 0, "name": "招聘", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150103, "positionType": 0, "name": "HRBP", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150104, "positionType": 0, "name": "人力资源专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150105, "positionType": 0, "name": "培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150106, "positionType": 0, "name": "薪酬绩效", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150403, "positionType": 0, "name": "人力资源经理/主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150109, "positionType": 0, "name": "员工关系", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150110, "positionType": 0, "name": "组织发展", "rank": 0, "mark": 0}], "positionType": 0, "name": "人力资源", "rank": 0, "value": "该职位主要负责人力资源部门的日常工作，如招聘、培训、薪资福利、员工关系等。他们需要熟悉人力资源管理的各种工作流程和法律法规，并具有良好的沟通、组织和分析能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 16000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150210, "positionType": 0, "name": "文员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140802, "positionType": 0, "name": "企业党建", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150201, "positionType": 0, "name": "行政专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150202, "positionType": 0, "name": "前台", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150205, "positionType": 0, "name": "经理助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150207, "positionType": 0, "name": "后勤", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150401, "positionType": 0, "name": "行政经理/主管", "rank": 0, "mark": 0}], "positionType": 0, "name": "行政", "rank": 0, "value": "行政人员主要负责公司的日常行政事务，如文件管理、会议安排、办公设施维护等。他们需要有良好的时间管理能力、组织能力和人际交往能力，并且需要熟悉公司的业务流程和政策。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 16000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150202, "positionType": 0, "name": "前台", "rank": 0, "mark": 0}], "positionType": 0, "name": "前台", "rank": 0, "value": "前台工作人员主要负责接待访客、接听电话、处理邮件等工作，他们是公司形象的重要代表。他们需要具备优秀的沟通技巧、专业的服务态度，以及良好的问题解决能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 16000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150203, "positionType": 0, "name": "法务专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150506, "positionType": 0, "name": "法务经理/主管", "rank": 0, "mark": 0}], "positionType": 0, "name": "法务", "rank": 0, "value": "法务人员主要是在公司或政府机构中处理法律事务的专业人员。他们的职责包括提供法律咨询、起草和审查合同、处理法律纠纷等。他们需要对法律有深入的理解，具备解决复杂法律问题的能力，并且需要具备良好的道德操守。", "mark": 0}], "positionType": 0, "name": "人力/行政/法务", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150312, "positionType": 0, "name": "建筑/工程会计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150313, "positionType": 0, "name": "税务外勤会计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150301, "positionType": 0, "name": "会计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150304, "positionType": 0, "name": "结算会计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150310, "positionType": 0, "name": "成本会计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150311, "positionType": 0, "name": "总账会计", "rank": 0, "mark": 0}], "positionType": 0, "name": "会计", "rank": 0, "value": "会计专业人士负责记录、分析、汇总企业的财务信息，并准备财务报表。他们需要确保所有的会计记录都准确无误，符合会计原则和法律规定。他们需要有良好的会计知识、注意细节，以及强大的数据处理能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150307, "positionType": 0, "name": "风控", "rank": 0, "mark": 0}], "positionType": 0, "name": "风控", "rank": 0, "value": "风险控制专业人士在金融行业中主要负责识别、评估和控制企业面临的风险，包括信用风险、市场风险、操作风险等。他们需要建立有效的风险管理策略和制度，以保护企业免受潜在风险的损害。他们需要对金融市场和风险管理有深入了解，以及强大的分析和决策能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180118, "positionType": 0, "name": "投资助理", "rank": 0, "mark": 0}], "positionType": 0, "name": "互联网金融", "rank": 0, "value": "互联网金融专业人士主要在金融科技公司工作，利用互联网技术提供金融服务，如在线贷款、网络支付、P2P借贷等。他们需要了解金融市场规则，熟悉互联网技术，并能在两者之间架起桥梁，为客户提供便捷、高效的金融服务。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150302, "positionType": 0, "name": "出纳", "rank": 0, "mark": 0}], "positionType": 0, "name": "出纳", "rank": 0, "value": "出纳员是一个关键的财务职位，他们的主要职责是管理和记录公司的现金交易。这包括收款、付款、现金流水账的记录等。他们还需要确保所有交易都符合公司的财务政策和法律法规。对细节的注意力、精确的记录能力和良好的道德素质是这个职位的重要特征。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150303, "positionType": 0, "name": "财务顾问", "rank": 0, "mark": 0}], "positionType": 0, "name": "财务顾问", "rank": 0, "value": "财务顾问是专业的财务管理专家，他们为企业或个人提供财务策划、投资咨询、税务规划等服务，帮助客户实现财务目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150305, "positionType": 0, "name": "税务", "rank": 0, "mark": 0}], "positionType": 0, "name": "税务", "rank": 0, "value": "专业处理税务事务，包括税务筹划、税收申报、税务咨询等，帮助企业合理避税，确保税收合规。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150306, "positionType": 0, "name": "审计", "rank": 0, "mark": 0}], "positionType": 0, "name": "审计", "rank": 0, "value": "审计人员主要负责企业财务报告的审查，评估企业的财务状况和内部控制系统，以防止财务欺诈。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150314, "positionType": 0, "name": "统计员", "rank": 0, "mark": 0}], "positionType": 0, "name": "统计员", "rank": 0, "value": "统计员主要负责收集、处理和分析数据，通过数据提供有助于决策的信息，是企业决策的重要支持。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 17000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150316, "positionType": 0, "name": "财务分析/财务BP", "rank": 0, "mark": 0}], "positionType": 0, "name": "财务分析/财务BP", "rank": 0, "value": "财务分析师/BP主要负责进行财务分析和预测，提供财务建议，帮助企业做出更好的商业决策。", "mark": 0}], "positionType": 0, "name": "财务/审计/税务", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150504, "positionType": 0, "name": "法律顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260203, "positionType": 0, "name": "知识产权律师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260204, "positionType": 0, "name": "律师助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260201, "positionType": 0, "name": "律师", "rank": 0, "mark": 0}], "positionType": 0, "name": "法律服务", "rank": 0, "value": "法律服务专业人士提供法律咨询、代理诉讼等服务，他们用专业知识和经验帮助客户解决法律问题。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150303, "positionType": 0, "name": "财务顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260111, "positionType": 0, "name": "知识产权/专利/商标代理人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260101, "positionType": 0, "name": "企业管理咨询", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260402, "positionType": 0, "name": "咨询经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260104, "positionType": 0, "name": "IT咨询顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260105, "positionType": 0, "name": "人力资源咨询顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260106, "positionType": 0, "name": "咨询项目管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260107, "positionType": 0, "name": "战略咨询", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260108, "positionType": 0, "name": "猎头顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260109, "positionType": 0, "name": "市场调研", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260110, "positionType": 0, "name": "其他咨询顾问", "rank": 0, "mark": 0}], "positionType": 0, "name": "咨询/调研", "rank": 0, "value": "咨询/调研专业人士主要提供专业建议和解决方案，以帮助客户解决各种商业、管理或政策问题。他们通过深入调研和数据分析，形成对问题的深刻理解，并据此提出策略或行动建议。他们需要具备良好的分析思维，研究技能和人际交往能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260301, "positionType": 0, "name": "英语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "英语翻译", "rank": 0, "value": "英语翻译专业人士负责将英语文本准确地翻译成其他语言，或将其他语言的文本翻译成英语。他们在保证翻译准确性的同时，也需要注意保留原文的语言风格和文化特征。他们需要具备高级的英语水平，良好的翻译技能和跨文化理解能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260302, "positionType": 0, "name": "日语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "日语翻译", "rank": 0, "value": "日语翻译专业人士负责将日语文本准确地翻译成其他语言，或将其他语言的文本翻译成日语。他们需要对日语和目标语言有深入的理解，能准确、生动地表达原文的含义，同时也需要对日本的文化和社会有一定了解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260303, "positionType": 0, "name": "韩语/朝鲜语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "韩语/朝鲜语翻译", "rank": 0, "value": "韩语/朝鲜语翻译专业人士负责将韩语/朝鲜语文本准确地翻译成其他语言，或将其他语言的文本翻译成韩语/朝鲜语。他们需要掌握高水平的韩语/朝鲜语技能，同时对目标语言也需要有足够的掌握程度，以便准确无误地进行翻译。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260304, "positionType": 0, "name": "法语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "法语翻译", "rank": 0, "value": "法语翻译专业人士负责将法语文本准确地翻译成其他语言，或将其他语言的文本翻译成法语。他们需要精通法语和目标语言，熟悉法国和法语区域的文化，以确保翻译的准确性和生动性。他们的工作可能涵盖各种文本类型，包括法律文档、商业报告、文学作品等。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260305, "positionType": 0, "name": "德语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "德语翻译", "rank": 0, "value": "德语翻译是专门将一种语言（如英语）翻译为德语，或者将德语翻译为其他语言的专业人员。他们的工作可能涉及各种内容，包括书籍、文章、电影、音频、公务文件等。他们需要精通德语和至少一种其他语言，并能准确、快速地进行翻译。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260306, "positionType": 0, "name": "俄语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "俄语翻译", "rank": 0, "value": "俄语翻译是负责将一种语言翻译成俄语，或者将俄语翻译成其他语言的专业人员。他们需要具备出色的俄语水平和至少一种其他语言的精通程度，并且能够确保翻译的准确性和流畅性。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260307, "positionType": 0, "name": "西班牙语翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "西班牙语翻译", "rank": 0, "value": "西班牙语翻译专门负责将一种语言（如英语）翻译成西班牙语，或者将西班牙语翻译成其他语言。他们的工作涵盖了各种领域，包括商务、教育、旅游、媒体等。他们需要精通西班牙语和至少一种其他语言，能够精确地传达原文的意思。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260308, "positionType": 0, "name": "其他语种翻译", "rank": 0, "mark": 0}], "positionType": 0, "name": "其他语种翻译", "rank": 0, "value": "这个领域涵盖了所有非主流语言的翻译工作，包括但不限于荷兰语、波兰语、土耳其语等。这些翻译需要对他们专门从事的语言有深入的理解，以确保他们能准确无误地翻译各种内容。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 18000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 260112, "positionType": 0, "name": "心理咨询师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 260113, "positionType": 0, "name": "婚恋咨询师", "rank": 0, "mark": 0}], "positionType": 0, "name": "心理/婚恋咨询师", "rank": 0, "value": "心理/婚恋咨询师提供专业的心理辅导与婚恋建议，他们通过倾听和指导，帮助个体和情侣理解自我，解决情感困扰，实现情感生活的和谐。", "mark": 0}], "positionType": 0, "name": "咨询/翻译/法律", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190245, "positionType": 0, "name": "地理教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190301, "positionType": 0, "name": "教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190314, "positionType": 0, "name": "日语教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190315, "positionType": 0, "name": "其他外语教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190316, "positionType": 0, "name": "语文教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190317, "positionType": 0, "name": "数学教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190318, "positionType": 0, "name": "物理教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190319, "positionType": 0, "name": "化学教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190320, "positionType": 0, "name": "生物教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190303, "positionType": 0, "name": "高中教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190304, "positionType": 0, "name": "初中教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190305, "positionType": 0, "name": "小学教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190307, "positionType": 0, "name": "理科教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190308, "positionType": 0, "name": "文科教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190309, "positionType": 0, "name": "英语教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190310, "positionType": 0, "name": "音乐教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190311, "positionType": 0, "name": "美术教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190312, "positionType": 0, "name": "体育/体能老师", "rank": 0, "mark": 0}], "positionType": 0, "name": "教师", "rank": 0, "value": "教师是教育机构中的核心角色，他们负责向学生传授知识，培养学生的技能，激发学生的兴趣。他们需要熟悉所教学科的知识，具备良好的教学方法，有热爱教育的热情和耐心。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190202, "positionType": 0, "name": "教务管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190203, "positionType": 0, "name": "教学管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190204, "positionType": 0, "name": "班主任/辅导员", "rank": 0, "mark": 0}], "positionType": 0, "name": "教育行政", "rank": 0, "value": "教育行政人员负责学校或教育机构的行政管理工作，他们制定并执行教育政策，协调教学资源，维护教学秩序，为教育事业的顺利发展提供支持。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190501, "positionType": 0, "name": "财会培训讲师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190504, "positionType": 0, "name": "拓展培训", "rank": 0, "mark": 0}], "positionType": 0, "name": "职业培训", "rank": 0, "value": "职业培训老师提供针对特定职业或技能的培训服务，帮助学员提升专业能力或获取新的技能。他们需要在相关领域有深厚的专业知识，熟悉培训方法和技巧。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190718, "positionType": 0, "name": "少儿编程老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190406, "positionType": 0, "name": "IT培训讲师", "rank": 0, "mark": 0}], "positionType": 0, "name": "IT培训", "rank": 0, "value": "IT培训老师负责教授计算机科学和信息技术相关的课程，如编程语言、网络技术、数据库管理等。他们需要精通IT领域的知识，有丰富的教学经验，能够将复杂的技术知识讲解得简单易懂。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190101, "positionType": 0, "name": "课程设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190102, "positionType": 0, "name": "课程编辑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190104, "positionType": 0, "name": "培训研究", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190107, "positionType": 0, "name": "培训策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190106, "positionType": 0, "name": "其他教育产品研发职位", "rank": 0, "mark": 0}], "positionType": 0, "name": "教育产品研发", "rank": 0, "value": "教育产品研发人员专注于教育产品或课程的设计与开发，他们深入研究教育需求和趋势，创新教育理念和方法，为提高教育质量做出贡献。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000016, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190302, "positionType": 0, "name": "助教", "rank": 0, "mark": 0}], "positionType": 0, "name": "助教", "rank": 0, "value": "助教在教育机构中，负责协助教师进行教学工作，如备课、批改作业、辅导学生等。他们需要了解教学大纲和课程内容，并具有良好的沟通和组织能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000017, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190324, "positionType": 0, "name": "感统训练教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190326, "positionType": 0, "name": "保育员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190322, "positionType": 0, "name": "托管老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190323, "positionType": 0, "name": "早教老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190306, "positionType": 0, "name": "幼教", "rank": 0, "mark": 0}], "positionType": 0, "name": "幼儿/少儿教师", "rank": 0, "value": "幼儿/少儿教师负责幼儿和少儿的教育与照顾，他们设计并进行教学活动，引导孩子探索和学习，促进孩子的全面发展。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000018, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190313, "positionType": 0, "name": "就业老师", "rank": 0, "mark": 0}], "positionType": 0, "name": "就业老师", "rank": 0, "value": "就业老师在职业学校或高校工作，他们提供就业指导和职业规划，帮助学生理解就业市场，提升就业技能，顺利实现从学校到职场的转变。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000019, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190321, "positionType": 0, "name": "家教", "rank": 0, "mark": 0}], "positionType": 0, "name": "家教", "rank": 0, "value": "家教通常是专门为学生提供一对一教育辅导的角色，他们需要根据学生的学习需求和情况，设计个性化的教学计划和方法，以帮助学生提升学习效果。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000020, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190718, "positionType": 0, "name": "少儿编程老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190767, "positionType": 0, "name": "架子鼓老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190768, "positionType": 0, "name": "围棋老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190710, "positionType": 0, "name": "表演教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190711, "positionType": 0, "name": "机器人教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190712, "positionType": 0, "name": "书法教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190713, "positionType": 0, "name": "钢琴教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190714, "positionType": 0, "name": "吉他教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190715, "positionType": 0, "name": "古筝教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190716, "positionType": 0, "name": "播音主持教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190717, "positionType": 0, "name": "乐高教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190701, "positionType": 0, "name": "舞蹈老师", "rank": 0, "mark": 0}], "positionType": 0, "name": "文化艺术培训", "rank": 0, "value": "文化艺术培训是指通过专业的培训课程，教授学生如何理解和创作文化艺术作品，如绘画、雕塑、音乐、戏剧等，以提升学生的艺术素养和创作能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 19000021, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190719, "positionType": 0, "name": "乒乓球教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190720, "positionType": 0, "name": "羽毛球教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190766, "positionType": 0, "name": "足球教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190769, "positionType": 0, "name": "拳击教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190708, "positionType": 0, "name": "武术教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190709, "positionType": 0, "name": "轮滑教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190705, "positionType": 0, "name": "健身教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190706, "positionType": 0, "name": "篮球教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190707, "positionType": 0, "name": "跆拳道教练", "rank": 0, "mark": 0}], "positionType": 0, "name": "运动培训", "rank": 0, "value": "运动培训通常是针对某一种或几种运动的专门训练，比如足球、篮球、游泳等，他们需要根据运动员的情况，设计和实施训练计划，以提升运动员的技能和体能。", "mark": 0}], "positionType": 0, "name": "教育培训", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140301, "positionType": 0, "name": "销售专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140304, "positionType": 0, "name": "大客户代表", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140305, "positionType": 0, "name": "BD经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140307, "positionType": 0, "name": "渠道销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140310, "positionType": 0, "name": "电话销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140314, "positionType": 0, "name": "网络销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140316, "positionType": 0, "name": "销售工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140317, "positionType": 0, "name": "客户经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "销售", "rank": 0, "value": "销售人员的工作主要是销售产品或服务，寻找并联系潜在客户，建立良好的客户关系。他们需要具备优秀的沟通和谈判技巧，良好的产品知识，以及高度的服务意识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 160401, "positionType": 0, "name": "置业顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 160403, "positionType": 0, "name": "地产中介", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220403, "positionType": 0, "name": "地产招商", "rank": 0, "mark": 0}], "positionType": 0, "name": "房地产销售/招商", "rank": 0, "value": "房地产销售/招商专业人员是房地产行业的重要角色，他们负责推销房地产项目，如住宅、商业物业等，向潜在购房者或商业合作伙伴进行推介。他们需要了解市场动态，掌握房地产信息，具备优秀的沟通和谈判技巧，以达成销售目标并满足客户需求。同时，他们也负责招募和管理商业租赁者，以实现物业的商业价值。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101201, "positionType": 0, "name": "售前技术支持", "rank": 0, "mark": 0}], "positionType": 0, "name": "软件销售", "rank": 0, "value": "软件销售专业人员负责销售公司的软件产品或服务。他们需要了解公司的软件产品和解决方案，识别并接触潜在客户，展示产品特点和优势，以达成销售目标。他们需要有良好的沟通和谈判技巧，以及对软件和技术市场的了解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180801, "positionType": 0, "name": "证券经纪人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180401, "positionType": 0, "name": "信用卡销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180701, "positionType": 0, "name": "保险顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180506, "positionType": 0, "name": "理财顾问", "rank": 0, "mark": 0}], "positionType": 0, "name": "金融销售", "rank": 0, "value": "金融销售人员是金融机构的重要角色，他们的主要任务是推销公司的金融产品和服务，包括储蓄账户、信用卡、贷款、保险和投资产品等。他们需要与客户建立良好的关系，了解客户的财务需求，并提供适合的金融解决方案。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 230201, "positionType": 0, "name": "汽车销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230202, "positionType": 0, "name": "汽车配件销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230207, "positionType": 0, "name": "二手车评估师", "rank": 0, "mark": 0}], "positionType": 0, "name": "汽车销售", "rank": 0, "value": "汽车销售专业人员负责销售各种汽车，包括新车、二手车、商务车等。他们需要熟悉汽车的特点和技术规格，能够解答客户的问题，推荐适合的汽车。他们需要有良好的销售技巧和服务态度，以及对汽车市场的了解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290307, "positionType": 0, "name": "理货/陈列员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290303, "positionType": 0, "name": "店员/营业员", "rank": 0, "mark": 0}], "positionType": 0, "name": "店员/营业员", "rank": 0, "value": "店员/营业员主要在零售店或服务店面提供销售和客户服务。他们需要熟悉店内的产品和服务，能够帮助客户找到他们需要的商品或服务，提供购物建议，处理付款等。他们需要有良好的客户服务技巧，能够快速、准确地处理销售和服务任务。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140313, "positionType": 0, "name": "广告销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140501, "positionType": 0, "name": "会议活动销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140504, "positionType": 0, "name": "会展活动销售", "rank": 0, "mark": 0}], "positionType": 0, "name": "广告/会展销售", "rank": 0, "value": "这个职位的人员主要负责销售广告或会展的空间和机会。他们的工作包括寻找潜在客户，了解客户的需求，提供合适的广告或会展解决方案，以及与客户建立长期的合作关系。他们需要有良好的沟通和谈判技巧，以及对广告和会展市场的深入了解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 160301, "positionType": 0, "name": "商务专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 160303, "positionType": 0, "name": "客户成功", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140309, "positionType": 0, "name": "销售助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130119, "positionType": 0, "name": "销售运营", "rank": 0, "mark": 0}], "positionType": 0, "name": "销售行政/商务", "rank": 0, "value": "销售行政/商务通常涉及到销售活动的后台支持，包括订单处理、合同管理、销售数据分析等，他们的工作为销售团队提供重要的商务支持。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140302, "positionType": 0, "name": "销售经理/主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 160102, "positionType": 0, "name": "城市经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "销售管理", "rank": 0, "value": "销售管理是指对销售团队进行组织、领导和控制的工作，他们需要制定销售策略，管理销售团队，监控销售活动，以实现销售目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 160501, "positionType": 0, "name": "服装导购", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210406, "positionType": 0, "name": "化妆品导购", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210414, "positionType": 0, "name": "美容顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210610, "positionType": 0, "name": "会籍顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290312, "positionType": 0, "name": "珠宝销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280103, "positionType": 0, "name": "旅游顾问", "rank": 0, "mark": 0}], "positionType": 0, "name": "服务业销售", "rank": 0, "value": "服务业销售人员主要负责向客户推销公司的服务产品，他们需要了解市场需求、熟悉公司产品，并具备良好的沟通和谈判技巧。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 190601, "positionType": 0, "name": "课程顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190603, "positionType": 0, "name": "留学顾问", "rank": 0, "mark": 0}], "positionType": 0, "name": "课程销售", "rank": 0, "value": "课程销售人员负责推广和销售教育课程，他们需要深入理解课程内容和教育市场动态，以有效地吸引和留住客户。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 20000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 250201, "positionType": 0, "name": "外贸经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250203, "positionType": 0, "name": "外贸业务员", "rank": 0, "mark": 0}], "positionType": 0, "name": "外贸销售", "rank": 0, "value": "外贸销售人员主要负责开拓国际市场、推广产品、维护客户关系等，他们需要掌握一定的外语能力和国际贸易知识。", "mark": 0}], "positionType": 0, "name": "销售", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 240201, "positionType": 0, "name": "仓库主管/经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240204, "positionType": 0, "name": "仓库管理员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240205, "positionType": 0, "name": "仓库文员", "rank": 0, "mark": 0}], "positionType": 0, "name": "仓储", "rank": 0, "value": "仓储工作人员负责管理和维护仓库，包括接收货物、整理货架、记录库存和准备发货。他们需要保持仓库的整洁和有序，确保货物的安全。他们需要具备良好的物理体力和组织能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 250204, "positionType": 0, "name": "贸易跟单", "rank": 0, "mark": 0}], "positionType": 0, "name": "外贸文员", "rank": 0, "value": "外贸文员负责处理外贸业务的相关文档工作，如报价、合同、发票等。他们需要了解国际贸易的流程和规则，具备良好的英语沟通和书写能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100311, "positionType": 0, "name": "无人机飞手", "rank": 0, "mark": 0}], "positionType": 0, "name": "无人机飞手", "rank": 0, "value": "无人机飞手主要负责无人机的操作和维护，他们需要熟悉无人机操作规程，具备高度的责任心和严谨的工作态度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 240308, "positionType": 0, "name": "客运司机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 150208, "positionType": 0, "name": "商务司机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240305, "positionType": 0, "name": "网约车司机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240306, "positionType": 0, "name": "代驾司机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240307, "positionType": 0, "name": "驾校教练", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240301, "positionType": 0, "name": "货运司机", "rank": 0, "mark": 0}], "positionType": 0, "name": "驾驶员", "rank": 0, "value": "驾驶员主要负责驾驶车辆，执行货物或人员的运输任务，他们需要有良好的驾驶技能和安全意识，确保运输任务的顺利完成。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300107, "positionType": 0, "name": "生产计划/物料管理(PMC)", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240101, "positionType": 0, "name": "供应链专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240102, "positionType": 0, "name": "供应链经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "供应链", "rank": 0, "value": "供应链工作涵盖了商品从原材料到最终用户的整个过程，包括采购、生产、仓储、运输等环节。供应链管理的目标是通过优化这些流程，降低成本，提高效率和客户满意度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 240302, "positionType": 0, "name": "集装箱管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240103, "positionType": 0, "name": "物流专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240104, "positionType": 0, "name": "物流经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240105, "positionType": 0, "name": "物流运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240106, "positionType": 0, "name": "物流跟单", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240108, "positionType": 0, "name": "调度员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240109, "positionType": 0, "name": "物流/仓储项目经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240110, "positionType": 0, "name": "运输经理/主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240111, "positionType": 0, "name": "货代/物流销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240113, "positionType": 0, "name": "水/空/陆运操作", "rank": 0, "mark": 0}], "positionType": 0, "name": "物流/运输", "rank": 0, "value": "物流/运输工作主要涉及货物的存储和运输，包括库存管理、包装、运输规划、货物跟踪等，他们确保货物能够安全、准时、有效地从一地运到另一地。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 240118, "positionType": 0, "name": "配送站长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240303, "positionType": 0, "name": "配送员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240304, "positionType": 0, "name": "快递员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240206, "positionType": 0, "name": "配/理/拣/发货", "rank": 0, "mark": 0}], "positionType": 0, "name": "配送理货", "rank": 0, "value": "配送理货人员负责处理和组织仓库中的商品，他们需要对货物进行分类、标记，并确保其正确无误地发送到正确的地址。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 240119, "positionType": 0, "name": "跟车员", "rank": 0, "mark": 0}], "positionType": 0, "name": "跟车员", "rank": 0, "value": "跟车员一般陪同驾驶员执行运输任务，他们可能需要帮助装卸货物，处理运输过程中的问题，以及完成相关的文书工作。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 21000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300630, "positionType": 0, "name": "搬运工/装卸工", "rank": 0, "mark": 0}], "positionType": 0, "name": "搬运工/装卸工", "rank": 0, "value": "搬运工/装卸工主要负责仓库中货物的装卸、搬运工作，他们需要有良好的身体条件，以安全、高效地完成工作。", "mark": 0}], "positionType": 0, "name": "供应链/物流", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220207, "positionType": 0, "name": "城乡规划设计", "rank": 0, "mark": 0}], "positionType": 0, "name": "城市规划设计", "rank": 0, "value": "城市规划设计人员负责对城市的发展和布局进行整体设计和规划。他们需要考虑众多因素，如环境保护、交通状况、人口密度等，以实现城市的可持续发展。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 160401, "positionType": 0, "name": "置业顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 160403, "positionType": 0, "name": "地产中介", "rank": 0, "mark": 0}], "positionType": 0, "name": "房地产销售", "rank": 0, "value": "房地产销售职位负责销售房地产项目，包括住宅、商业空间等，他们需要具备良好的沟通技巧和谈判能力，以达成销售目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220217, "positionType": 0, "name": "软装设计师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220222, "positionType": 0, "name": "装修项目经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "装饰装修", "rank": 0, "value": "装饰装修职位主要负责室内空间的设计和装饰工作，包括为客户提供设计方案、选择合适的材料和配件，以及监督装修工程的施工进度，确保装修效果符合客户的期望。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220101, "positionType": 0, "name": "房地产策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220102, "positionType": 0, "name": "房地产项目管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220103, "positionType": 0, "name": "工程招投标", "rank": 0, "mark": 0}], "positionType": 0, "name": "房地产规划开发", "rank": 0, "value": "房地产规划开发职位主要负责房地产项目的策划和开发工作，包括选址、设计、施工和销售等全过程，他们需要具备多元化的专业知识和综合能力", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220202, "positionType": 0, "name": "建筑工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220203, "positionType": 0, "name": "建筑设计师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220204, "positionType": 0, "name": "土木/土建/结构工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220213, "positionType": 0, "name": "弱电工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220214, "positionType": 0, "name": "给排水工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220215, "positionType": 0, "name": "暖通工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220216, "positionType": 0, "name": "幕墙工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220221, "positionType": 0, "name": "BIM工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220223, "positionType": 0, "name": "建筑机电工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220224, "positionType": 0, "name": "消防工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "建筑/规划设计", "rank": 0, "value": "建筑/规划设计职位涉及建筑设计、城市规划等工作，他们通过创新和科学的设计，打造出满足使用功能且具有艺术价值的建筑和城市空间。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220206, "positionType": 0, "name": "园林/景观设计", "rank": 0, "mark": 0}], "positionType": 0, "name": "园林/景观设计", "rank": 0, "value": "园林/景观设计职位主要负责公共空间、园林、景观等的设计工作，他们需要融合自然和人工元素，打造出优美、和谐、可持续的环境空间。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220208, "positionType": 0, "name": "工程监理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220209, "positionType": 0, "name": "工程造价", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220211, "positionType": 0, "name": "资料员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220212, "positionType": 0, "name": "建筑施工项目经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220218, "positionType": 0, "name": "施工员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220219, "positionType": 0, "name": "测绘/测量", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220220, "positionType": 0, "name": "材料员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220225, "positionType": 0, "name": "施工安全员", "rank": 0, "mark": 0}], "positionType": 0, "name": "工程管理", "rank": 0, "value": "工程管理人员主要负责建筑工程的日常管理和协调工作，包括项目计划、进度控制、成本控制、质量管理等，以确保工程的顺利进行。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220302, "positionType": 0, "name": "房地产估价师", "rank": 0, "mark": 0}], "positionType": 0, "name": "房产评估师", "rank": 0, "value": "房产评估师是专门进行房产价值评估的专业人员，他们通过分析房产的各种因素（如位置、房龄、市场行情等），为买卖双方提供房产的公正价值。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 220407, "positionType": 0, "name": "物业工程主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290105, "positionType": 0, "name": "保安", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290117, "positionType": 0, "name": "保安主管/队长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290121, "positionType": 0, "name": "消防中控员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300635, "positionType": 0, "name": "弱电工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220401, "positionType": 0, "name": "物业经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220403, "positionType": 0, "name": "地产招商", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220404, "positionType": 0, "name": "综合维修工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220405, "positionType": 0, "name": "绿化工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220406, "positionType": 0, "name": "物业管理员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300627, "positionType": 0, "name": "锅炉工", "rank": 0, "mark": 0}], "positionType": 0, "name": "物业管理", "rank": 0, "value": "物业管理人员主要负责对小区、大厦等物业进行管理，包括环境维护、设施管理、服务提供等，以提升居住或使用环境的品质。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 22000016, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300604, "positionType": 0, "name": "焊工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300606, "positionType": 0, "name": "电工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300608, "positionType": 0, "name": "木工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300609, "positionType": 0, "name": "油漆工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300625, "positionType": 0, "name": "空调工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300626, "positionType": 0, "name": "电梯工", "rank": 0, "mark": 0}], "positionType": 0, "name": "建筑/装修工人", "rank": 0, "value": "建筑/装修工人负责进行各种建筑或装修工程，如砌墙、粉刷、装配等。他们的工作对于建筑的质量和美观至关重要。", "mark": 0}], "positionType": 0, "name": "房地产/建筑", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210201, "positionType": 0, "name": "护士", "rank": 0, "mark": 0}], "positionType": 0, "name": "护士", "rank": 0, "value": "护士在医疗机构中负责提供病人护理服务，包括基础护理、药物管理、病情观察、病人教育等。他们需要具备专业的护理知识和技能，良好的沟通和应对压力的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210901, "positionType": 0, "name": "试剂研发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210121, "positionType": 0, "name": "医疗器械注册", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210122, "positionType": 0, "name": "医疗器械生产/质量管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210105, "positionType": 0, "name": "医疗器械研发", "rank": 0, "mark": 0}], "positionType": 0, "name": "医疗器械", "rank": 0, "value": "医疗器械专业人员负责研发、生产和销售各种医疗器械，如手术器具、诊断设备、假体等。他们需要具备专业的医疗和工程知识，熟悉相关产品的设计和生产流程，以及对医疗市场的理解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210507, "positionType": 0, "name": "口腔咨询师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210506, "positionType": 0, "name": "医疗器械销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210801, "positionType": 0, "name": "药店店长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210803, "positionType": 0, "name": "药店店员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210502, "positionType": 0, "name": "医药代表", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210504, "positionType": 0, "name": "健康顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210505, "positionType": 0, "name": "医美咨询", "rank": 0, "mark": 0}], "positionType": 0, "name": "医疗销售", "rank": 0, "value": "医疗销售人员负责销售医疗产品和服务，如药物、医疗器械、医疗检查服务等。他们需要熟悉产品特性和市场需求，具备良好的销售技巧和客户服务意识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210101, "positionType": 0, "name": "医学编辑", "rank": 0, "mark": 0}], "positionType": 0, "name": "医药编辑", "rank": 0, "value": "医药编辑主要负责对医学或医药相关的文章、书籍、研究报告等进行编辑和修订。他们需要具备医学和编辑的专业知识，能够理解并准确表达专业内容。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210103, "positionType": 0, "name": "其他医生职位", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210308, "positionType": 0, "name": "幼儿园保健医", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210309, "positionType": 0, "name": "外科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210310, "positionType": 0, "name": "儿科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210311, "positionType": 0, "name": "妇产科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210312, "positionType": 0, "name": "眼科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210313, "positionType": 0, "name": "皮肤科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210314, "positionType": 0, "name": "耳鼻喉科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210315, "positionType": 0, "name": "麻醉科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210316, "positionType": 0, "name": "病理科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210317, "positionType": 0, "name": "医务管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210112, "positionType": 0, "name": "医生助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210113, "positionType": 0, "name": "放射科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210114, "positionType": 0, "name": "超声科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210306, "positionType": 0, "name": "内科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210307, "positionType": 0, "name": "全科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210302, "positionType": 0, "name": "中医", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210402, "positionType": 0, "name": "整形医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210303, "positionType": 0, "name": "精神心理科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210104, "positionType": 0, "name": "药剂师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210304, "positionType": 0, "name": "口腔科医生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210109, "positionType": 0, "name": "验光师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210111, "positionType": 0, "name": "医学检验", "rank": 0, "mark": 0}], "positionType": 0, "name": "医生/医技", "rank": 0, "value": "医生/医技人员主要负责提供医疗服务，包括诊断疾病，治疗疾病，为患者提供专业的医疗咨询等。他们是医疗健康系统中的核心角色。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210124, "positionType": 0, "name": "细胞培养员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210125, "positionType": 0, "name": "药物分析", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210126, "positionType": 0, "name": "药物合成", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210127, "positionType": 0, "name": "医疗产品技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210128, "positionType": 0, "name": "生物信息工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210129, "positionType": 0, "name": "制剂研发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210115, "positionType": 0, "name": "生物学研究人员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210116, "positionType": 0, "name": "药品注册", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210123, "positionType": 0, "name": "医药项目经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210108, "positionType": 0, "name": "医药研发", "rank": 0, "mark": 0}], "positionType": 0, "name": "生物医药", "rank": 0, "value": "生物医药工作者研究和开发用于治疗和预防疾病的生物药品。这可能涉及基因工程、生物技术等先进技术，以创新和优化医疗产品。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210117, "positionType": 0, "name": "药品生产", "rank": 0, "mark": 0}], "positionType": 0, "name": "药品生产", "rank": 0, "value": "药品生产人员负责制造医疗药品，包括原料采购、制剂开发、质量控制等步骤。他们的工作保证了药品的安全性和有效性。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 211002, "positionType": 0, "name": "临床监查员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210118, "positionType": 0, "name": "临床医学经理/专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210119, "positionType": 0, "name": "临床协调员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210120, "positionType": 0, "name": "临床数据分析", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 211001, "positionType": 0, "name": "临床项目经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "临床试验", "rank": 0, "value": "临床试验工作者负责进行医疗产品或治疗方法的临床研究，以验证其安全性和有效性。这是将医疗研究成果应用到实际临床的重要环节。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290118, "positionType": 0, "name": "产后康复师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210401, "positionType": 0, "name": "营养师/健康管理师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210403, "positionType": 0, "name": "理疗师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210404, "positionType": 0, "name": "针灸推拿", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210305, "positionType": 0, "name": "康复治疗师", "rank": 0, "mark": 0}], "positionType": 0, "name": "保健理疗", "rank": 0, "value": "保健理疗人员提供各种非药物治疗，如按摩、针灸、康复训练等，以帮助人们恢复健康、缓解疼痛、提高生活质量。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210503, "positionType": 0, "name": "导医", "rank": 0, "mark": 0}], "positionType": 0, "name": "导医", "rank": 0, "value": "导医是医疗机构中的重要角色，主要负责指导患者就医流程，协助患者完成挂号、支付、指引诊室等工作，以提升患者就医体验和医疗服务效率。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210801, "positionType": 0, "name": "药店店长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210802, "positionType": 0, "name": "执业药师/驻店药师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210803, "positionType": 0, "name": "药店店员", "rank": 0, "mark": 0}], "positionType": 0, "name": "药店", "rank": 0, "value": "药店人员负责销售药品、医疗器械等医疗产品，同时提供药物使用指导和简单的健康咨询服务，他们需要有一定的医药知识和良好的服务态度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 23000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290111, "positionType": 0, "name": "护工", "rank": 0, "mark": 0}], "positionType": 0, "name": "护工", "rank": 0, "value": "护工主要负责照顾病人和老年人的日常生活，包括帮助患者进食、洗浴、活动等，他们需要有爱心和耐心，以及基本的护理技能。", "mark": 0}], "positionType": 0, "name": "医疗健康", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 280101, "positionType": 0, "name": "计调", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280102, "positionType": 0, "name": "签证专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280103, "positionType": 0, "name": "旅游顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280104, "positionType": 0, "name": "导游", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280105, "positionType": 0, "name": "票务员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 280106, "positionType": 0, "name": "讲解员", "rank": 0, "mark": 0}], "positionType": 0, "name": "旅游服务", "rank": 0, "value": "旅游服务人员负责为旅游者提供服务，如导游、行程规划、门票预订等。他们需要具备良好的沟通能力，对旅游目的地有深入了解，以及服务意识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290202, "positionType": 0, "name": "服务员", "rank": 0, "mark": 0}], "positionType": 0, "name": "服务员", "rank": 0, "value": "服务员在餐馆、酒店等地方工作，他们负责接待客人，提供点餐、送餐、清理等服务。他们需要具备良好的服务态度和沟通技巧。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290206, "positionType": 0, "name": "餐饮店长", "rank": 0, "mark": 0}], "positionType": 0, "name": "餐饮店长", "rank": 0, "value": "餐饮店长负责管理餐馆的日常运营，包括菜单制定、人员安排、财务管理等。他们需要具备管理和领导能力，了解餐饮业的运营模式和食品安全规定。此外，他们还需要有优秀的服务意识，以提高顾客满意度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290107, "positionType": 0, "name": "礼仪/迎宾/接待", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290102, "positionType": 0, "name": "酒店前台", "rank": 0, "mark": 0}], "positionType": 0, "name": "酒店前台/迎宾", "rank": 0, "value": "酒店前台/迎宾是酒店的门面，他们负责接待客人、办理入住退房手续，提供酒店信息咨询等服务，他们需要具备良好的服务态度和专业素养。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 280201, "positionType": 0, "name": "旅游产品经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "旅游产品开发/策划", "rank": 0, "value": "旅游产品开发/策划人员负责设计和开发旅游产品，包括旅游路线、旅游项目等，他们需要深入了解旅游市场，以创新和精准的产品满足旅客的需求。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290103, "positionType": 0, "name": "客房服务员", "rank": 0, "mark": 0}], "positionType": 0, "name": "客房服务员", "rank": 0, "value": "客房服务员在酒店行业中主要负责房间的清洁和整理工作，以及提供其他相关的客房服务，如布置房间、更换床单等，确保客人享有舒适整洁的住宿环境。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 24000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290158, "positionType": 0, "name": "民宿管家", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290115, "positionType": 0, "name": "酒店前厅经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290116, "positionType": 0, "name": "客房经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290104, "positionType": 0, "name": "酒店经理", "rank": 0, "mark": 0}], "positionType": 0, "name": "酒店经理", "rank": 0, "value": "酒店经理负责酒店的日常运营管理，包括人员管理、客房管理、餐饮服务等。他们需要确保提供优质的客户服务，提高客人满意度，以达成酒店的经营目标。", "mark": 0}], "positionType": 0, "name": "酒店/旅游", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 26000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 26000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300801, "positionType": 0, "name": "电池工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300802, "positionType": 0, "name": "电机工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300803, "positionType": 0, "name": "线束设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101308, "positionType": 0, "name": "自动驾驶系统工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230101, "positionType": 0, "name": "汽车设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230102, "positionType": 0, "name": "车身/造型设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230103, "positionType": 0, "name": "底盘工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230105, "positionType": 0, "name": "动力系统工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230106, "positionType": 0, "name": "汽车电子工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230107, "positionType": 0, "name": "汽车零部件设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230108, "positionType": 0, "name": "汽车项目管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230110, "positionType": 0, "name": "内外饰设计工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230210, "positionType": 0, "name": "总装工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230109, "positionType": 0, "name": "汽车质量工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "汽车研发/制造", "rank": 0, "value": "汽车研发/制造工作涉及新车型的设计、开发和制造过程。他们需要不断研究和应用新的技术，优化汽车性能，满足消费者和市场的需求。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 26000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 230213, "positionType": 0, "name": "洗车工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230214, "positionType": 0, "name": "加油员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230203, "positionType": 0, "name": "汽车服务顾问", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230204, "positionType": 0, "name": "汽车维修", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230205, "positionType": 0, "name": "汽车美容", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230206, "positionType": 0, "name": "汽车查勘定损", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230208, "positionType": 0, "name": "4S店店长/维修站长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230209, "positionType": 0, "name": "汽车改装", "rank": 0, "mark": 0}], "positionType": 0, "name": "汽车服务", "rank": 0, "value": "汽车服务包括汽车维修、保养、美容等服务，他们需要对汽车结构和功能有深入理解，能够解决各种车辆问题，提供优质的汽车后市场服务。", "mark": 0}], "positionType": 0, "name": "汽车", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300318, "positionType": 0, "name": "液压工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100813, "positionType": 0, "name": "热设计工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300301, "positionType": 0, "name": "机械工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300303, "positionType": 0, "name": "机械设备工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300304, "positionType": 0, "name": "设备维修保养工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300305, "positionType": 0, "name": "机械制图员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300306, "positionType": 0, "name": "机械结构工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300307, "positionType": 0, "name": "工业工程师(IE)", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300308, "positionType": 0, "name": "工艺工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300309, "positionType": 0, "name": "材料工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300310, "positionType": 0, "name": "机电工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300311, "positionType": 0, "name": "CNC数控操机/编程员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300312, "positionType": 0, "name": "冲压工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300313, "positionType": 0, "name": "夹具工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300314, "positionType": 0, "name": "模具工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300315, "positionType": 0, "name": "焊接工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300316, "positionType": 0, "name": "注塑工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300317, "positionType": 0, "name": "铸造/锻造工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "机械设计/制造", "rank": 0, "value": "机械设计/制造工程师主要负责机械产品的设计和生产过程，包括新产品的设计、制图、模拟、制造等，他们需要具备深厚的机械专业知识和实践能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 110111, "positionType": 0, "name": "化妆品产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300407, "positionType": 0, "name": "化工项目经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300401, "positionType": 0, "name": "化工工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300402, "positionType": 0, "name": "实验室技术员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300404, "positionType": 0, "name": "涂料研发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300405, "positionType": 0, "name": "化妆品研发", "rank": 0, "mark": 0}], "positionType": 0, "name": "化工", "rank": 0, "value": "化工行业中的工作主要涉及化学物质的生产和加工。这包括从原料选择、化学反应、产品制备，到最后的质量检测和环保处理等一系列过程，需要专业的化学知识和技能。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300509, "positionType": 0, "name": "打样/制版", "rank": 0, "mark": 0}], "positionType": 0, "name": "服装纺织设计", "rank": 0, "value": "服装纺织设计师负责设计衣服或纺织品的样式、图案和颜色，以满足消费者的审美需求。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300208, "positionType": 0, "name": "质检员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300209, "positionType": 0, "name": "计量工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210122, "positionType": 0, "name": "医疗器械生产/质量管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250108, "positionType": 0, "name": "供应商质量工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300201, "positionType": 0, "name": "质量管理/测试工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300202, "positionType": 0, "name": "可靠度工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300203, "positionType": 0, "name": "失效分析工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300204, "positionType": 0, "name": "产品认证工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300205, "positionType": 0, "name": "体系工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300206, "positionType": 0, "name": "体系审核员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300402, "positionType": 0, "name": "实验室技术员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 230109, "positionType": 0, "name": "汽车质量工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "质量管理", "rank": 0, "value": "质量管理专员负责确保产品或服务满足预定的质量标准。他们的工作可能包括设计和实施质量检查程序，识别和解决质量问题，以及制定质量改进策略。他们需要熟悉质量管理原则和工具，具备良好的分析和解决问题的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300210, "positionType": 0, "name": "安全评价师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300903, "positionType": 0, "name": "EHS工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220225, "positionType": 0, "name": "施工安全员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300207, "positionType": 0, "name": "生产安全员", "rank": 0, "mark": 0}], "positionType": 0, "name": "生产安全", "rank": 0, "value": "生产安全岗位的主要职责是保证生产过程的安全。他们需要定期检查生产设备的状态，制定并执行安全规程，预防和处理生产过程中的安全事故。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300110, "positionType": 0, "name": "厂务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300104, "positionType": 0, "name": "生产组长/拉长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300106, "positionType": 0, "name": "生产设备管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300107, "positionType": 0, "name": "生产计划/物料管理(PMC)", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300108, "positionType": 0, "name": "生产跟单/文员", "rank": 0, "mark": 0}], "positionType": 0, "name": "生产营运", "rank": 0, "value": "生产营运人员负责管理和优化生产过程，以提高效率和生产力。他们可能需要安排生产计划，管理物料和人力资源，以及监控生产设备的性能。他们需要熟悉生产操作，有良好的组织和领导能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300406, "positionType": 0, "name": "食品/饮料研发", "rank": 0, "mark": 0}], "positionType": 0, "name": "食品/饮料研发", "rank": 0, "value": "食品/饮料研发人员负责开发新的食品和饮料产品。他们需要对食品科学有深入的了解，通过不断试验和改良，创新出满足消费者需求的产品。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300511, "positionType": 0, "name": "量体师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300637, "positionType": 0, "name": "裁剪工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300507, "positionType": 0, "name": "面料辅料开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300510, "positionType": 0, "name": "服装/纺织/皮革跟单", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300629, "positionType": 0, "name": "缝纫工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300632, "positionType": 0, "name": "样衣工", "rank": 0, "mark": 0}], "positionType": 0, "name": "服装制作生产", "rank": 0, "value": "服装制作生产工作包括设计稿的打样、量身定制、裁剪布料、缝纫制衣等一系列步骤。他们需要有良好的手工技巧，对服装制作的各个环节都有深入了解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300601, "positionType": 0, "name": "普工/操作工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300623, "positionType": 0, "name": "组装工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300624, "positionType": 0, "name": "包装工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300628, "positionType": 0, "name": "学徒工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300630, "positionType": 0, "name": "搬运工/装卸工", "rank": 0, "mark": 0}], "positionType": 0, "name": "普工", "rank": 0, "value": "普工，也被称为普通工人或操作工，是工厂或生产线上的基础员工。他们的任务通常涵盖各种简单的劳动性工作，如包装、搬运、清理或基本的机器操作等。尽管工作内容可能不需要高级的技术或专业知识，但他们在生产过程中起着至关重要的作用，是实现工厂日常运营和生产目标的关键。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300634, "positionType": 0, "name": "挖掘机司机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300602, "positionType": 0, "name": "叉车工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300603, "positionType": 0, "name": "铲车司机", "rank": 0, "mark": 0}], "positionType": 0, "name": "挖掘机/铲车/叉车", "rank": 0, "value": "挖掘机/铲车/叉车操作员是负责操作重型机械设备以执行各种建设和生产任务的专业人员。他们可能在建筑工地、矿山、仓库、码头或其他工业环境中工作。他们需要拥有专业的驾驶和操作技能，以安全有效地使用这些设备，进行土石方工程、货物搬运等工作。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 27000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300638, "positionType": 0, "name": "水电工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300604, "positionType": 0, "name": "焊工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300605, "positionType": 0, "name": "氩弧焊工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300606, "positionType": 0, "name": "电工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300608, "positionType": 0, "name": "木工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300609, "positionType": 0, "name": "油漆工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300610, "positionType": 0, "name": "车工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300611, "positionType": 0, "name": "磨工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300612, "positionType": 0, "name": "铣工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300613, "positionType": 0, "name": "钳工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300614, "positionType": 0, "name": "钻工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300615, "positionType": 0, "name": "铆工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300616, "positionType": 0, "name": "钣金工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300617, "positionType": 0, "name": "抛光工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300618, "positionType": 0, "name": "机修工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300619, "positionType": 0, "name": "折弯工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300620, "positionType": 0, "name": "电镀工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300621, "positionType": 0, "name": "喷塑工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300622, "positionType": 0, "name": "注塑工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300625, "positionType": 0, "name": "空调工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300626, "positionType": 0, "name": "电梯工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300631, "positionType": 0, "name": "切割工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300633, "positionType": 0, "name": "模具工", "rank": 0, "mark": 0}], "positionType": 0, "name": "技工", "rank": 0, "value": "技工是工厂、车间等生产线上的主力军，他们操作各类机械设备，制造出我们日常生活中使用的各种产品，同时他们也负责设备的日常维护和故障排查。", "mark": 0}], "positionType": 0, "name": "生产制造", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100803, "positionType": 0, "name": "自动化工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101402, "positionType": 0, "name": "电气工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 220223, "positionType": 0, "name": "建筑机电工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300310, "positionType": 0, "name": "机电工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101404, "positionType": 0, "name": "电气设计工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "电气/自动化", "rank": 0, "value": "电气/自动化工程师负责设计、开发和维护自动化设备和系统。这可能包括机器人、生产线、工业过程等。他们需要具备电气工程和自动化技术的知识，以及分析和解决复杂技术问题的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100818, "positionType": 0, "name": "光学工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100801, "positionType": 0, "name": "硬件工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100802, "positionType": 0, "name": "嵌入式软件工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100804, "positionType": 0, "name": "单片机", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100805, "positionType": 0, "name": "电路设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100806, "positionType": 0, "name": "驱动开发工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100807, "positionType": 0, "name": "系统集成", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100808, "positionType": 0, "name": "FPGA开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100809, "positionType": 0, "name": "DSP开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100811, "positionType": 0, "name": "PCB工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100816, "positionType": 0, "name": "射频工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101401, "positionType": 0, "name": "电子工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "电子/硬件开发", "rank": 0, "value": "电子/硬件开发人员负责电子产品和系统的设计与开发工作，他们通过硬件设计，实现产品功能，满足市场和用户需求。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101001, "positionType": 0, "name": "通信技术工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101002, "positionType": 0, "name": "通信研发工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101003, "positionType": 0, "name": "数据通信工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101005, "positionType": 0, "name": "宽带装维", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101007, "positionType": 0, "name": "有线传输工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101008, "positionType": 0, "name": "无线/天线工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101010, "positionType": 0, "name": "通信标准化工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101011, "positionType": 0, "name": "通信项目专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101012, "positionType": 0, "name": "通信项目经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101013, "positionType": 0, "name": "核心网工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101018, "positionType": 0, "name": "光网络工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "通信", "rank": 0, "value": "通信工程师主要负责通信系统和设备的设计、安装、调试和维护工作，他们的工作保证了信息的顺畅传递，是现代社会信息化建设的重要支撑。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101407, "positionType": 0, "name": "模拟版图设计工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101405, "positionType": 0, "name": "集成电路IC设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101403, "positionType": 0, "name": "FAE", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101406, "positionType": 0, "name": "数字IC验证工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "半导体/芯片", "rank": 0, "value": "半导体/芯片工程师涉及到芯片设计、制造、测试等环节，他们的工作推动了半导体技术的发展，是电子设备、智能终端等产品的核心部分。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 28000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101408, "positionType": 0, "name": "电子维修技术员", "rank": 0, "mark": 0}], "positionType": 0, "name": "电子维修技术员", "rank": 0, "value": "电子维修技术员主要负责维修和保养电子设备和系统。他们需要熟悉电子设备的工作原理和结构，能够检查、测试、诊断并修复设备的故障，确保设备的正常运行。", "mark": 0}], "positionType": 0, "name": "电子/电气/通信", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 31000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 31000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 250109, "positionType": 0, "name": "招标专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250110, "positionType": 0, "name": "投标专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250111, "positionType": 0, "name": "商品专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140312, "positionType": 0, "name": "商品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250108, "positionType": 0, "name": "供应商质量工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250102, "positionType": 0, "name": "采购经理/主管", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250103, "positionType": 0, "name": "采购专员/助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 250105, "positionType": 0, "name": "采购工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "采购", "rank": 0, "value": "采购人员负责从供应商那里购买公司需要的商品和服务。他们需要评估供应商的价格、质量和可靠性，进行谈判，以达到最佳的采购条件。他们需要有良好的分析和谈判技巧，对市场趋势有敏感的洞察力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 31000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 250204, "positionType": 0, "name": "贸易跟单", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240114, "positionType": 0, "name": "报关/报检员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 240117, "positionType": 0, "name": "单证员", "rank": 0, "mark": 0}], "positionType": 0, "name": "进出口贸易", "rank": 0, "value": "进出口贸易职位主要负责公司的国际贸易业务，包括寻找国外供应商、谈判交易、处理进出口手续等。他们需要了解国际贸易规则和流程，具备良好的沟通和谈判技巧。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 31000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 250104, "positionType": 0, "name": "买手", "rank": 0, "mark": 0}], "positionType": 0, "name": "买手", "rank": 0, "value": "买手是负责为零售商采购商品的职位。他们需要根据市场趋势和消费者需求选择商品，谈判交易条件，以获取最佳的采购结果。", "mark": 0}], "positionType": 0, "name": "采购/贸易", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140601, "positionType": 0, "name": "广告创意策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140602, "positionType": 0, "name": "美术指导", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140604, "positionType": 0, "name": "策划经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140605, "positionType": 0, "name": "广告文案", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140608, "positionType": 0, "name": "媒介投放", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140609, "positionType": 0, "name": "媒介商务BD", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140611, "positionType": 0, "name": "广告审核", "rank": 0, "mark": 0}], "positionType": 0, "name": "广告", "rank": 0, "value": "广告工作涉及创造、设计和发布广告，以提升品牌知名度，吸引潜在客户，推动产品销售。他们需要有创新思维，良好的艺术感和市场敏感度。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140116, "positionType": 0, "name": "信息流优化师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 130109, "positionType": 0, "name": "网络推广", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140101, "positionType": 0, "name": "市场营销策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140104, "positionType": 0, "name": "市场推广/地推", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140105, "positionType": 0, "name": "SEO", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140106, "positionType": 0, "name": "SEM", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140107, "positionType": 0, "name": "商务经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140109, "positionType": 0, "name": "活动策划执行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140111, "positionType": 0, "name": "海外市场", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140114, "positionType": 0, "name": "选址开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140115, "positionType": 0, "name": "游戏推广", "rank": 0, "mark": 0}], "positionType": 0, "name": "市场/营销", "rank": 0, "value": "市场/营销专业人士负责制定并执行市场营销策略，以提高产品或服务的市场份额。他们可能会涉及市场研究、广告、公关、促销等各种活动。他们需要具备市场分析、战略规划、项目管理等多方面的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140108, "positionType": 0, "name": "商业数据分析", "rank": 0, "mark": 0}], "positionType": 0, "name": "商业数据分析", "rank": 0, "value": "商业数据分析职位主要负责收集、处理和分析商业数据，以揭示业务趋势，指导决策。他们需要有较强的数据敏感度和分析能力，以及熟练掌握数据分析工具。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140803, "positionType": 0, "name": "社工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140804, "positionType": 0, "name": "项目申报专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140112, "positionType": 0, "name": "政府关系", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140801, "positionType": 0, "name": "政策研究", "rank": 0, "mark": 0}], "positionType": 0, "name": "政府事务", "rank": 0, "value": "政府事务人员需要根据公司的发展战略与管理需求，建立公司与政府相关机构沟通渠道，与相关政府部门战略合作伙伴建立并维持良好的互动关系。此岗位需要具备较强的观察力和应变能力，优秀的人际交往和协调能力，极强的社会活动能力和出色的公关能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140202, "positionType": 0, "name": "广告客户执行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140203, "positionType": 0, "name": "品牌公关", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140204, "positionType": 0, "name": "媒介专员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140206, "positionType": 0, "name": "媒介经理/总监", "rank": 0, "mark": 0}], "positionType": 0, "name": "公关媒介", "rank": 0, "value": "公关媒介职业是在品牌与公众之间建立和维护良好关系的关键角色。他们通过各种媒体渠道传递信息，塑造品牌形象，协助处理危机公关，并推动品牌的商业目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140505, "positionType": 0, "name": "会务/会展策划", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 140506, "positionType": 0, "name": "会务/会展执行", "rank": 0, "mark": 0}], "positionType": 0, "name": "会务会展", "rank": 0, "value": "会务会展专业人员主要负责组织、规划和执行各种会议、展览和大型活动。他们通常需要协调多个部门，如场地管理、供应商联系、活动策划等，以保证活动的顺利进行和成功完成。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 32000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 140607, "positionType": 0, "name": "广告制作", "rank": 0, "mark": 0}], "positionType": 0, "name": "广告制作", "rank": 0, "value": "广告制作人员是市场营销的创意驱动者，他们负责制定广告策略、设计广告、编写广告文案、制作广告素材，确保广告信息准确、有趣且引人入胜，进而促进产品销售和品牌形象的塑造。", "mark": 0}], "positionType": 0, "name": "市场/公关/广告", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210613, "positionType": 0, "name": "救生员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190701, "positionType": 0, "name": "舞蹈老师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 190705, "positionType": 0, "name": "健身教练", "rank": 0, "mark": 0}], "positionType": 0, "name": "运动健身", "rank": 0, "value": "运动健身职业涵盖了一系列与健康和体育相关的工作，包括健身教练、瑜伽教练等。这些专业人士通常需要有良好的身体素质、热爱运动，并且能够指导他人达到身心健康的目标。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210109, "positionType": 0, "name": "验光师", "rank": 0, "mark": 0}], "positionType": 0, "name": "验光师", "rank": 0, "value": "验光师是眼部健康的专业人员，主要负责评估和改善患者的视力。他们进行视力检查，判断是否需要眼镜或隐形眼镜，以及其度数，也可发现患者潜在的眼部疾病，是保障公众视力健康的重要角色。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210411, "positionType": 0, "name": "足疗师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210412, "positionType": 0, "name": "按摩师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210415, "positionType": 0, "name": "采耳师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210403, "positionType": 0, "name": "理疗师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210404, "positionType": 0, "name": "针灸推拿", "rank": 0, "mark": 0}], "positionType": 0, "name": "理疗保健", "rank": 0, "value": "理疗保健专业人员通过各种物理治疗技术，如按摩、电疗、热疗等，帮助客户缓解身体疼痛，改善身体机能，提高生活质量。他们需要掌握专业的理疗知识，了解人体结构和生理机能。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210408, "positionType": 0, "name": "美体师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210410, "positionType": 0, "name": "美容店长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210405, "positionType": 0, "name": "美容师", "rank": 0, "mark": 0}], "positionType": 0, "name": "美容/美体", "rank": 0, "value": "美容/美体专业人员是帮助客户保持外在美丽的专家，他们提供皮肤护理、美容咨询、身体塑形等服务，帮助客户提高自我形象，增强自信。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210407, "positionType": 0, "name": "纹绣师", "rank": 0, "mark": 0}], "positionType": 0, "name": "纹绣师", "rank": 0, "value": "纹绣师是在皮肤上进行艺术创作的专业人员，包括眉部纹绣、唇部纹绣、眼线纹绣等。他们需要精湛的技艺和艺术创造力，以及对人体结构和皮肤科学的深入理解，以确保安全并提供满意的效果。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210409, "positionType": 0, "name": "美发助理/学徒", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 210607, "positionType": 0, "name": "发型师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290801, "positionType": 0, "name": "养发师", "rank": 0, "mark": 0}], "positionType": 0, "name": "美发", "rank": 0, "value": "美发师负责为客户设计和塑造发型，包括剪发、烫发、染发等服务。他们需要拥有专业的美发技巧，同时具备良好的审美观，能根据客户的脸型、气质和需求，提供满意的发型设计。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210608, "positionType": 0, "name": "美甲美睫师", "rank": 0, "mark": 0}], "positionType": 0, "name": "美甲/美睫", "rank": 0, "value": "美甲/美睫专业人员使用各种技术和产品，包括凝胶、亚克力和磨砂等，来美化和增强客户的手部和足部。他们还为客户提供假睫毛服务，增强眼部的美感。此类工作需要精细的操作技巧和对美的独特理解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 210609, "positionType": 0, "name": "化妆/造型/服装", "rank": 0, "mark": 0}], "positionType": 0, "name": "化妆/造型师", "rank": 0, "value": "化妆/造型师利用化妆品和造型工具，为客户创造适合其面部特征和皮肤类型的美观造型。他们可以在多种场合下工作，包括电影、电视、婚礼或者日常生活，他们的目标是提升客户的外观和自信。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290105, "positionType": 0, "name": "保安", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290112, "positionType": 0, "name": "安检员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290117, "positionType": 0, "name": "保安主管/队长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290120, "positionType": 0, "name": "押运员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290121, "positionType": 0, "name": "消防中控员", "rank": 0, "mark": 0}], "positionType": 0, "name": "安保服务", "rank": 0, "value": "安保服务人员负责保护人员和财产的安全，防止罪犯行为和其他潜在危险。他们的工作可能包括巡逻、监控安全摄像头、处理紧急情况，以及编写安全报告。这项工作需要强大的观察力、决策能力和应对压力的能力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000010, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290106, "positionType": 0, "name": "保洁", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290122, "positionType": 0, "name": "保洁主管", "rank": 0, "mark": 0}], "positionType": 0, "name": "保洁", "rank": 0, "value": "保洁人员负责清洁和维护商业或住宅环境的卫生，包括扫地、擦窗、清理浴室等。他们的目标是提供一个干净整洁的环境，让人们在其中感到舒适和愉快。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000011, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290108, "positionType": 0, "name": "保姆", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290109, "positionType": 0, "name": "月嫂", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290110, "positionType": 0, "name": "育婴师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290111, "positionType": 0, "name": "护工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290118, "positionType": 0, "name": "产后康复师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290169, "positionType": 0, "name": "收纳师", "rank": 0, "mark": 0}], "positionType": 0, "name": "家政", "rank": 0, "value": "家政人员提供家庭管理服务，包括清洁、烹饪、照顾孩子或老人等。他们的工作是确保家庭的日常运行顺利，让家庭成员可以专注于他们的工作或其他责任。这项工作需要有组织能力和责任心。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000012, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290113, "positionType": 0, "name": "手机维修", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290114, "positionType": 0, "name": "家电维修", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290166, "positionType": 0, "name": "电脑/打印机维修", "rank": 0, "mark": 0}], "positionType": 0, "name": "维修", "rank": 0, "value": "维修人员是为了保持设备、机器或建筑的正常运作和维护的专业工作者。他们可能专门从事电器、电子产品、汽车、空调或建筑等领域的维修工作。他们的工作通常需要有丰富的专业知识和实践技能，以便快速找到问题并进行有效的解决。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000013, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290314, "positionType": 0, "name": "商场运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290305, "positionType": 0, "name": "督导/巡店", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290307, "positionType": 0, "name": "理货/陈列员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290308, "positionType": 0, "name": "防损员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290309, "positionType": 0, "name": "卖场经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290311, "positionType": 0, "name": "促销员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290201, "positionType": 0, "name": "收银", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290302, "positionType": 0, "name": "导购", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290303, "positionType": 0, "name": "店员/营业员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290304, "positionType": 0, "name": "门店店长", "rank": 0, "mark": 0}], "positionType": 0, "name": "零售", "rank": 0, "value": "零售人员在商店或在线平台销售商品，包括食品、服装、家居用品等。他们的工作通常包括了解商品信息，提供客户服务，处理交易等。零售人员需要具备良好的沟通能力和服务意识，以满足客户需求，保证良好的购物体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000014, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290313, "positionType": 0, "name": "网吧网管", "rank": 0, "mark": 0}], "positionType": 0, "name": "网吧网管", "rank": 0, "value": "网吧网管负责维护网吧的计算机系统和网络设备，以保证客户可以顺利进行网络游戏或者互联网浏览。他们的工作可能包括设备维修、系统更新、网络安全管理等，要求他们具备一定的计算机和网络知识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000015, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290601, "positionType": 0, "name": "宠物美容", "rank": 0, "mark": 0}], "positionType": 0, "name": "宠物美容", "rank": 0, "value": "宠物美容师为宠物提供美容和清洁服务，包括洗澡、修剪毛发、剪指甲等。他们需要了解不同宠物的美容需求和照顾方法，以提供专业的服务并确保宠物的舒适和健康。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000016, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290602, "positionType": 0, "name": "宠物医生", "rank": 0, "mark": 0}], "positionType": 0, "name": "宠物医生", "rank": 0, "value": "宠物医生，或称为兽医，负责宠物的健康和医疗护理，包括疾病诊断、治疗、预防接种等。他们需要具备专业的兽医学知识，以保护和改善宠物的健康。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000017, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290701, "positionType": 0, "name": "花艺师", "rank": 0, "mark": 0}], "positionType": 0, "name": "花艺师", "rank": 0, "value": "花艺师是艺术和设计的专家，他们利用鲜花和其他自然材料创建美观的花艺作品。这可能包括制作花束、花篮，或者为婚礼、节日和其他特殊场合设计花艺装饰。花艺师需要具备良好的审美眼光、创新思维和熟练的手工技巧。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 33000018, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290702, "positionType": 0, "name": "婚礼策划", "rank": 0, "mark": 0}], "positionType": 0, "name": "婚礼策划", "rank": 0, "value": "婚礼策划师是帮助新人组织和实施婚礼的专业人员。他们负责从整体主题和预算规划，到详细的婚礼日程安排和供应商协调等所有细节，确保婚礼顺利进行，并让新人和嘉宾都能享受这一特殊的日子。", "mark": 0}], "positionType": 0, "name": "生活服务/零售", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290107, "positionType": 0, "name": "礼仪/迎宾/接待", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290216, "positionType": 0, "name": "传菜员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290201, "positionType": 0, "name": "收银", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290202, "positionType": 0, "name": "服务员", "rank": 0, "mark": 0}], "positionType": 0, "name": "前厅", "rank": 0, "value": "前厅工作人员是餐厅的面孔，他们与顾客进行最直接的交流。他们的职责可能包括接待顾客、领位、介绍菜单，以及处理顾客的问题和请求。前厅人员需要具备良好的服务态度和沟通能力，以提供优质的用餐体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290213, "positionType": 0, "name": "面点师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290218, "positionType": 0, "name": "凉菜厨师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290219, "positionType": 0, "name": "中餐厨师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290220, "positionType": 0, "name": "西餐厨师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290221, "positionType": 0, "name": "日料厨师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290222, "positionType": 0, "name": "烧烤师傅", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290203, "positionType": 0, "name": "厨师", "rank": 0, "mark": 0}], "positionType": 0, "name": "厨师", "rank": 0, "value": "厨师负责准备食材，制作菜品。他们需要熟练掌握各种烹饪技巧，了解食材的特性和搭配，严格遵守食品安全和卫生规定。创新和对美食的热爱是他们的重要素质。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290223, "positionType": 0, "name": "奶茶店店员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290227, "positionType": 0, "name": "调酒师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290210, "positionType": 0, "name": "茶艺师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290204, "positionType": 0, "name": "咖啡师", "rank": 0, "mark": 0}], "positionType": 0, "name": "饮品师", "rank": 0, "value": "饮品师是专门调制和制作各种饮品的专业人员，包括咖啡、茶、鸡尾酒等。他们需要熟知各种饮品的制作方法和风味特性，以满足不同顾客的口味需求。良好的手艺和对新饮品的探索精神是这个职业的重要素质。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290205, "positionType": 0, "name": "送餐员", "rank": 0, "mark": 0}], "positionType": 0, "name": "送餐员", "rank": 0, "value": "送餐员负责将餐厅的食品安全、准时地送达顾客手中。他们需要熟悉送餐区域的路线，并能在各种天气和交通条件下，保持良好的服务态度和专业素质，确保顾客满意。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290226, "positionType": 0, "name": "储备店长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290206, "positionType": 0, "name": "餐饮店长", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290207, "positionType": 0, "name": "餐饮前厅经理/领班", "rank": 0, "mark": 0}], "positionType": 0, "name": "餐饮管理", "rank": 0, "value": "餐饮管理人员负责餐厅的日常运营和管理，包括菜品选择、人员管理、预算控制等。他们必须拥有良好的领导能力、决策能力和人际交往能力，以确保餐厅的高效运营并提供优质的顾客体验。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290224, "positionType": 0, "name": "水台/水产员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290208, "positionType": 0, "name": "后厨", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290209, "positionType": 0, "name": "配菜打荷", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290217, "positionType": 0, "name": "洗碗工", "rank": 0, "mark": 0}], "positionType": 0, "name": "帮厨", "rank": 0, "value": "帮厨是厨房团队的重要组成部分，他们负责协助主厨完成各项烹饪任务，如切菜、准备食材、清洗厨具等。这个职业需要热爱烹饪，能够在厨房的快节奏环境中保持高效工作。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290225, "positionType": 0, "name": "面包/烘焙师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 290211, "positionType": 0, "name": "蛋糕/裱花师", "rank": 0, "mark": 0}], "positionType": 0, "name": "甜点/烘焙", "rank": 0, "value": "甜点/烘焙师专注于制作各种甜点和面包产品，他们的工作需要对烘焙原料、制作技术和食品安全有深入的理解。他们的创新和艺术天赋，使得甜点和面包既美味又具有吸引力。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 34000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 290212, "positionType": 0, "name": "餐饮学徒", "rank": 0, "mark": 0}], "positionType": 0, "name": "餐饮学徒", "rank": 0, "value": "餐饮学徒是正在接受训练，以学习和掌握餐饮业务知识和技能的人员。他们可能会在多个岗位轮流实习，以了解餐饮业的全貌并找到自己最感兴趣的领域。", "mark": 0}], "positionType": 0, "name": "餐饮", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 300901, "positionType": 0, "name": "环保工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300902, "positionType": 0, "name": "环评工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300903, "positionType": 0, "name": "EHS工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300904, "positionType": 0, "name": "碳排放管理师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 300905, "positionType": 0, "name": "环境采样/检测员", "rank": 0, "mark": 0}], "positionType": 0, "name": "环保", "rank": 0, "value": "环保专业人员致力于保护环境，减少污染，促进可持续发展。他们的工作可能涉及到研究环境问题，开发解决方案，宣传环保知识，监测和评估环保政策的效果等。他们为保护我们的地球做出重要贡献。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 301001, "positionType": 0, "name": "地质工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 301002, "positionType": 0, "name": "光伏系统工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 301003, "positionType": 0, "name": "风电/光伏运维工程师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 301004, "positionType": 0, "name": "水利工程师", "rank": 0, "mark": 0}], "positionType": 0, "name": "能源/地质", "rank": 0, "value": "能源/地质专业人员专注于地球的物理结构，地质活动以及矿物资源的开发利用。他们的工作涉及到能源生产，如石油、天然气和煤炭的勘探和开采，以及在环保方面的工作，如水资源管理和土壤保护。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 400101, "positionType": 0, "name": "农业/林业技术员", "rank": 0, "mark": 0}], "positionType": 0, "name": "农业/林业技术员", "rank": 0, "value": "农业/林业技术员为农业和林业生产提供技术支持。他们可能参与种植作物、管理林木、防治疫病害虫，以及研究和推广农林科技知识，他们的工作对于确保粮食安全和森林资源的可持续利用起着关键作用。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 400201, "positionType": 0, "name": "饲养员", "rank": 0, "mark": 0}], "positionType": 0, "name": "饲养员", "rank": 0, "value": "饲养员负责喂养和照顾农场或动物园中的动物。他们需要了解不同动物的饲养技术和需求，如饲料配比、饲养环境等，并进行定期检查，以确保动物的健康。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 400202, "positionType": 0, "name": "养殖技术员", "rank": 0, "mark": 0}], "positionType": 0, "name": "禽畜/水产养殖技术员", "rank": 0, "value": "禽畜/水产养殖技术员专门负责禽畜和水生生物的养殖工作，包括喂养、繁育、防疫等。他们的工作旨在提高养殖效率和产量，保障食品安全，并促进动物福利。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 35000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 400203, "positionType": 0, "name": "畜牧兽医", "rank": 0, "mark": 0}], "positionType": 0, "name": "畜牧兽医", "rank": 0, "value": "畜牧兽医负责维护和提高动物健康，防止动物疾病的发生和传播。他们的工作涵盖动物疾病的诊断、治疗、疫苗接种，以及对养殖环境的卫生管理等，是动物健康和公众食品安全的守护者。", "mark": 0}], "positionType": 0, "name": "能源/环保/农业", "rank": 0, "value": "", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000001, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 150307, "positionType": 0, "name": "风控", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180204, "positionType": 0, "name": "合规稽查", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180304, "positionType": 0, "name": "清算", "rank": 0, "mark": 0}], "positionType": 0, "name": "风控合规清算", "rank": 0, "value": "风控合规清算专业人员主要负责在金融机构中实施风险控制和法规合规政策，以及完成交易后的清算工作。他们需要对金融市场的风险进行有效的识别、评估和控制，同时要保证金融交易的顺利进行，遵守相关的法律法规。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000002, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180120, "positionType": 0, "name": "投资者关系/证券事务代表", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180101, "positionType": 0, "name": "投资经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180103, "positionType": 0, "name": "行业研究", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180115, "positionType": 0, "name": "融资", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180116, "positionType": 0, "name": "并购", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180117, "positionType": 0, "name": "投后管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180118, "positionType": 0, "name": "投资助理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180111, "positionType": 0, "name": "其他投融资职位", "rank": 0, "mark": 0}], "positionType": 0, "name": "投融资", "rank": 0, "value": "投融资专业人员负责为个人、企业或政府组织投资和筹集资金。他们的工作涉及研究和分析市场趋势，制定投资策略，为客户提供财务咨询，以及通过发行股票或债券等方式筹集资金。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000003, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180104, "positionType": 0, "name": "资产评估", "rank": 0, "mark": 0}], "positionType": 0, "name": "资产评估", "rank": 0, "value": "资产评估专业人员负责为企业或个人的资产进行价值评估。他们会根据市场条件、资产状况和未来收益等因素，提供准确和公正的资产估值报告，这对于资产买卖、抵押贷款和投资决策等都是非常重要的。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000004, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180803, "positionType": 0, "name": "买方分析师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180805, "positionType": 0, "name": "基金经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180806, "positionType": 0, "name": "投资银行业务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180807, "positionType": 0, "name": "量化研究员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180802, "positionType": 0, "name": "卖方分析师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180402, "positionType": 0, "name": "柜员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180106, "positionType": 0, "name": "证券交易员", "rank": 0, "mark": 0}], "positionType": 0, "name": "证券/基金/期货", "rank": 0, "value": "证券/基金/期货专业人员在金融市场中进行证券、基金和期货的交易和管理。他们需要分析经济条件、公司业绩和市场趋势等信息，为投资者提供投资建议和策略。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000005, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180203, "positionType": 0, "name": "资信评估", "rank": 0, "mark": 0}], "positionType": 0, "name": "资信评估", "rank": 0, "value": "资信评估专业人员负责评估个人或企业的信用状况和偿债能力。他们的评估结果影响到借款人能否获得贷款，以及贷款的利率和额度等，是金融领域的重要工作。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000006, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180402, "positionType": 0, "name": "柜员", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180403, "positionType": 0, "name": "客户经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180404, "positionType": 0, "name": "银行大堂经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 180406, "positionType": 0, "name": "信贷专员", "rank": 0, "mark": 0}], "positionType": 0, "name": "银行", "rank": 0, "value": "银行专业人士在金融服务行业中，提供一系列的服务，如储蓄、贷款、投资、保险等。他们可能在个人银行、公司银行、投资银行等多个部门工作。他们需要具备良好的客户服务能力，对金融市场有深入了解，以及风险管理的知识。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000007, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180503, "positionType": 0, "name": "催收员", "rank": 0, "mark": 0}], "positionType": 0, "name": "催收员", "rank": 0, "value": "催收员是金融机构的重要角色，他们的主要任务是追回逾期的债务。这项工作需要良好的沟通技巧和谈判技巧，以及对贷款和信用政策的深入理解。他们必须在保持专业和尊重客户的同时，有效地完成催收任务。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000008, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180702, "positionType": 0, "name": "保险精算师", "rank": 0, "mark": 0}], "positionType": 0, "name": "保险精算师", "rank": 0, "value": "保险精算师是使用数学和统计技术来预测保险业务中的未来事件的专业人员。他们分析风险和不确定性，为保险公司的产品定价、制定保险政策，并评估公司的长期财务安全。他们的工作需要强大的分析技巧和对保险市场的深入理解。", "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 36000009, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 180703, "positionType": 0, "name": "保险理赔", "rank": 0, "mark": 0}], "positionType": 0, "name": "保险理赔", "rank": 0, "value": "保险理赔人员处理保险公司的赔偿请求。他们评估保险单的覆盖范围，调查保险事故，决定是否应支付赔偿，以及赔偿的金额。这项工作需要良好的判断力、沟通能力，以及对保险政策和法规的全面理解。", "mark": 0}], "positionType": 0, "name": "金融", "rank": 0, "value": "", "mark": 0}]}