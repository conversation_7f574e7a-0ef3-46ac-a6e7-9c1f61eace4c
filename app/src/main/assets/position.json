{"positionVersion": 37.9, "cityCode": 101010100, "position": [{"cityType": 0, "capital": 0, "code": 100000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100125, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "区块链工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Java", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "C/C++", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "PHP", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "C#", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": ".NET", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Python", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Node.js", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Golang", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100121, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "语音/视频/图形开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100123, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "全栈工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100124, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "GIS工程师", "rank": 0, "mark": 11}], "name": "后端开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100212, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "技术美术", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100213, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "鸿蒙开发工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Android", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "iOS", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "JavaScript", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "U3D", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100210, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "Cocos", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100211, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "UE4", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100901, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "前端开发工程师", "rank": 0, "mark": 11}], "name": "前端/移动开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "测试工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "自动化测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "功能测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "性能测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "测试开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "硬件测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "软件测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "渗透测试", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100703, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "测试经理", "rank": 0, "mark": 11}], "name": "测试", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100410, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "技术文档工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运维工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运维开发工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网络工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "系统工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "IT技术支持", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "系统管理员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网络安全", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100408, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "系统安全", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100409, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "DBA", "rank": 0, "mark": 11}], "name": "运维/技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100514, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "爬虫工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100515, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据治理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100122, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据采集", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100506, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "ETL工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100507, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据仓库", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100508, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100511, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据分析师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100512, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据架构师", "rank": 0, "mark": 11}], "name": "数据", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "项目经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100605, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "实施顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100606, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "实施工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100817, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "硬件项目经理", "rank": 0, "mark": 11}], "name": "项目管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100818, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "光学工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100819, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电源工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101408, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电子维修技术员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电池工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "硬件工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "嵌入式软件工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100804, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "单片机", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100805, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电路设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100806, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "驱动开发工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100807, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "系统集成", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100808, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "FPGA开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100809, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "DSP开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100811, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "PCB工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100816, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "射频工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电子工程师", "rank": 0, "mark": 11}], "name": "电子/硬件开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 101000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101001, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "通信技术工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101002, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "通信研发工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101003, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据通信工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101005, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "宽带装维", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101007, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "有线传输工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101008, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "无线/天线工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101010, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "通信标准化工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101011, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "通信项目专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101012, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "通信项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101013, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "核心网工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101018, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "光网络工程师", "rank": 0, "mark": 11}], "name": "通信", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 101400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "模拟版图设计工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101409, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "芯片测试工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101410, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "DFT工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101411, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数字前端设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101412, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数字后端工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101413, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "模拟IC设计工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "集成电路IC设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "FAE", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数字IC验证工程师", "rank": 0, "mark": 11}], "name": "半导体/芯片", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 100700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "技术经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100702, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "技术总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100704, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "架构师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100705, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "CTO/CIO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100706, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运维总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100707, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "技术合伙人", "rank": 0, "mark": 11}], "name": "高端技术职位", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 101300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "大模型算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "规控算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "SLAM算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "高性能计算工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据挖掘", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "搜索算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "自然语言处理算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "推荐算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "算法工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "自动驾驶系统工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "风控算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机器学习", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "深度学习", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "语音算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "图像算法", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "算法研究员", "rank": 0, "mark": 11}], "name": "人工智能", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 101200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "售前技术支持", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "售后技术支持", "rank": 0, "mark": 11}], "name": "销售技术支持", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 101100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他技术职位", "rank": 0, "mark": 11}], "name": "其他技术职位", "rank": 0, "mark": 0}], "name": "技术", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 110000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 110200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "系统策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏制作人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏数值策划", "rank": 0, "mark": 11}], "name": "游戏策划/制作", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 110100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 110110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "AI产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化妆品产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100607, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "需求分析工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "移动产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电商产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "产品专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "硬件产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 110302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "高级产品管理岗", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "用户研究", "rank": 0, "mark": 11}], "name": "产品经理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 110400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 110401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他产品职位", "rank": 0, "mark": 11}], "name": "其他产品职位", "rank": 0, "mark": 0}], "name": "产品", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 120000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "视觉设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "UI设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "平面设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "3D设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "原画师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美工", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "包装设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120119, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "设计师助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "动画设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120121, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "插画师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120122, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "漫画师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120123, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "修图师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "UX/交互设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告设计", "rank": 0, "mark": 11}], "name": "视觉/交互设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 120200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏主美术", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏特效", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏UI设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏场景", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏角色", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏动作", "rank": 0, "mark": 11}], "name": "游戏设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 120400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "设计经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "设计总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "视觉设计总监", "rank": 0, "mark": 11}], "name": "高端设计职位", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 120600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120613, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家具拆单员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120614, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "舞美设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120615, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "鞋类设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "CAD绘图员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120611, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "展览/展示设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120612, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "照明设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "室内设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220217, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "软装设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "服装/纺织设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120602, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工业设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120604, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家具设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120606, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "珠宝设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 120608, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "陈列设计", "rank": 0, "mark": 11}], "name": "非视觉设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 120500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 120501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他设计职位", "rank": 0, "mark": 11}], "name": "其他设计职位", "rank": 0, "mark": 0}], "name": "设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 130000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130126, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "淘宝运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130127, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "天猫运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130128, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "京东运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130129, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "拼多多运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130130, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "亚马逊运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130131, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "速卖通运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130132, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "阿里国际站运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130133, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "跨境电商产品开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130134, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "外卖运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "用户运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "产品运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据/策略运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "内容运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商家运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "品类运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网站运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "新媒体运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "社群运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "微信运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "线下拓展运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "国内电商运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运营助理/专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "内容审核", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130121, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数据标注/AI训练师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130122, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "直播运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130123, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "车辆运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130124, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "跨境电商运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "视频运营", "rank": 0, "mark": 11}], "name": "运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 130300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "售前客服", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "售后客服", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网络客服", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客服经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客服专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客服主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电话客服", "rank": 0, "mark": 21}], "name": "客服", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 130400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运营总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "COO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客服总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运营经理/主管", "rank": 0, "mark": 11}], "name": "高端运营职位", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 130500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他运营职位", "rank": 0, "mark": 11}], "name": "其他运营职位", "rank": 0, "mark": 0}], "name": "客服/运营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140120, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商业数据分析", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "选址开发", "rank": 0, "mark": 11}], "name": "调研分析", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140803, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "社工", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140804, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "项目申报专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "政府关系", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "政策研究", "rank": 0, "mark": 11}], "name": "政府事务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "信息流优化师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网络推广", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "市场推广/地推", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "SEO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "SEM", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏推广", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "媒介专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "媒介经理/总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140608, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "媒介投放", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140609, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "媒介商务BD", "rank": 0, "mark": 11}], "name": "推广/投放", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "市场营销策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "活动策划执行", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "海外市场", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "市场总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "CMO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140505, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会务/会展策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140506, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会务/会展执行", "rank": 0, "mark": 11}], "name": "市场营销", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "品牌公关", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "公关总监", "rank": 0, "mark": 11}], "name": "公关", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告客户执行", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "创意总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170212, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告/会展项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告创意策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140602, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美术指导", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140604, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "策划经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140605, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告文案", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140607, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告制作", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 140611, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告审核", "rank": 0, "mark": 11}], "name": "广告", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他市场职位", "rank": 0, "mark": 11}], "name": "其他市场职位", "rank": 0, "mark": 0}], "name": "市场/公关/广告", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 150000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "企业文化", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "招聘", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "HRBP", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "人力资源专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "培训", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "薪酬绩效", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "人力资源经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "人力资源总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "猎头顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "员工关系", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "组织发展", "rank": 0, "mark": 11}], "name": "人力资源", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 150200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150210, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "文员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150211, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "档案管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "项目专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "企业党建", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "行政专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "前台", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "经理助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150207, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "后勤", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "行政经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "行政总监", "rank": 0, "mark": 11}], "name": "行政", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 150300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "建筑/工程会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "税务外勤会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "统计员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150316, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "财务分析/财务BP", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "出纳", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "财务顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "结算会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "税务", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "审计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "成本会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "总账会计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "财务经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "CFO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "财务总监/VP", "rank": 0, "mark": 11}], "name": "财务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 150500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "法务专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150504, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "法律顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150506, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "法务经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150507, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "法务总监", "rank": 0, "mark": 11}], "name": "法务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 150600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他职能职位", "rank": 0, "mark": 11}], "name": "其他职能职位", "rank": 0, "mark": 0}], "name": "人力/财务/行政", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 310000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "总裁/总经理/CEO", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150408, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "副总裁/副总经理/VP", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150409, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "分公司/代表处负责人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150410, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "区域负责人(辖多个分公司)", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150411, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "总助/CEO助理/董事长助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150413, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "联合创始人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 150414, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "董事会秘书", "rank": 0, "mark": 11}], "name": "高级管理职位", "rank": 0, "mark": 0}], "name": "高级管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 160300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 160301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商务专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客户成功", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "招商", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商务经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商务总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130119, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售运营", "rank": 0, "mark": 11}], "name": "销售行政/商务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 160401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "置业顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "地产中介", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "地产招商", "rank": 0, "mark": 11}], "name": "房地产销售/招商", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 160501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "服装导购", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 160502, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家装导购", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化妆品导购", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210610, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会籍顾问", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "珠宝销售", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 280103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "旅游顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 290302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "导购", "rank": 0, "mark": 21}], "name": "服务业销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 230201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车配件销售", "rank": 0, "mark": 11}], "name": "汽车销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "广告销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会议活动销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140504, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "会展活动销售", "rank": 0, "mark": 11}], "name": "广告/会展销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "证券经纪人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "信用卡销售", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 180701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保险顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180506, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "理财顾问", "rank": 0, "mark": 11}], "name": "金融销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 250205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "海外销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "外贸经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "外贸业务员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "货代/物流销售", "rank": 0, "mark": 11}], "name": "外贸销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 140300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "大客户代表", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "BD经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "渠道销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电话销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网络销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140316, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140317, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客户经理", "rank": 0, "mark": 11}], "name": "销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "课程顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "留学顾问", "rank": 0, "mark": 11}], "name": "教培销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210507, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "口腔咨询师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210506, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医疗器械销售", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210502, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医药代表", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210504, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "健康顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210505, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医美咨询", "rank": 0, "mark": 11}], "name": "医疗销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 140302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "区域总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "城市经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 160103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "销售VP", "rank": 0, "mark": 11}], "name": "销售管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 160200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 160201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他销售职位", "rank": 0, "mark": 11}], "name": "其他销售职位", "rank": 0, "mark": 0}], "name": "销售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 170000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 170100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 130201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "主编/副主编", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "文案编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 130204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网站编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "印刷排版", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "记者/采编", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "作者/撰稿人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "出版发行", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "校对录入", "rank": 0, "mark": 11}], "name": "采编/写作/出版", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 170600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 170625, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "带货主播", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170626, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "剧本杀主持人", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170627, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "剧本杀编剧", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170628, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "儿童引导师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170629, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏主播", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170630, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "模特", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170617, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "艺人助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170620, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "主持人/DJ", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170621, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "中控/场控/助播", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170622, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "灯光师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170624, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "影视特效", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "导演/编导", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170602, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "摄影/摄像师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "视频剪辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170604, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "音频编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170605, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "经纪人/星探", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170606, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "后期制作", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170608, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "影视发行", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170609, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "影视策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170610, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "主播", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170611, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "演员/配音员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170613, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "放映员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 170614, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "录音/音效", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170615, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "制片人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 170616, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "编剧", "rank": 0, "mark": 11}], "name": "影视媒体", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 170500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 170501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他传媒职位", "rank": 0, "mark": 11}], "name": "其他传媒职位", "rank": 0, "mark": 0}], "name": "直播/影视/传媒", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180803, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "买方分析师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180805, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "基金经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180806, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投资银行业务", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180807, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "量化研究员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "卖方分析师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "证券交易员", "rank": 0, "mark": 11}], "name": "证券/基金/期货", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投资者关系/证券事务代表", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投资经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "行业研究", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投资总监/VP", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "融资", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "并购", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投后管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投资助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他投融资职位", "rank": 0, "mark": 11}], "name": "投融资", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "柜员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客户经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "银行大堂经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "信贷专员", "rank": 0, "mark": 11}], "name": "银行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180702, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保险精算师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180703, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保险理赔", "rank": 0, "mark": 11}], "name": "保险", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 150307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "风控", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "金融产品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "资信评估", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180503, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "催收员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "资产评估", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "合规稽查", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 180304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "清算", "rank": 0, "mark": 11}], "name": "中后台", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 180600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 180601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他金融职位", "rank": 0, "mark": 11}], "name": "其他金融职位", "rank": 0, "mark": 0}], "name": "金融", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "课程设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "课程编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "培训研究", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "培训策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他教育产品研发职位", "rank": 0, "mark": 11}], "name": "教育产品研发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "园长/副园长", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "校长/副校长", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "教务管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "教学管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "班主任/辅导员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "就业老师", "rank": 0, "mark": 11}], "name": "教育行政", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190245, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "地理教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190324, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "感统训练教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190326, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保育员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 190314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "日语教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190315, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他外语教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190316, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "语文教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190317, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "数学教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190318, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物理教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190319, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化学教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190320, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生物教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190321, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家教", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190322, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "托管老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190323, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "早教老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "助教", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "高中教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "初中教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "小学教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "幼教", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "理科教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "文科教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "英语教师", "rank": 0, "mark": 11}], "name": "教师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "财会培训讲师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "培训师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "IT培训讲师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190504, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "拓展培训", "rank": 0, "mark": 11}], "name": "职业培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190718, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "少儿编程老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190719, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "乒乓球教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190720, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "羽毛球教练", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190766, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "足球教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190767, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "架子鼓老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190768, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "围棋老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190769, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "拳击教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190770, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "台球教练/助教", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210612, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "普拉提老师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190708, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "武术教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190709, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "轮滑教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190710, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "表演教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190711, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机器人教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190712, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "书法教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190713, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "钢琴教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190714, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "吉他教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190715, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "古筝教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190716, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "播音主持教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190717, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "乐高教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210613, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "救生员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "舞蹈老师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "瑜伽老师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游泳教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190705, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "健身教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190706, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "篮球教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190707, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "跆拳道教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 190310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "音乐教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美术教师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 190312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "体育/体能老师", "rank": 0, "mark": 11}], "name": "特长培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 190800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 190801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他教育培训职位", "rank": 0, "mark": 11}], "name": "其他教育培训职位", "rank": 0, "mark": 0}], "name": "教育培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 211000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 211002, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床监查员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床医学经理/专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210119, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床协调员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床数据分析", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 211001, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "临床医学总监", "rank": 0, "mark": 11}], "name": "临床试验", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他医生职位", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "幼儿园保健医", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "外科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "儿科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "妇产科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "眼科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "皮肤科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "耳鼻喉科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210315, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "麻醉科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210316, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "病理科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210317, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医务管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医生助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "放射科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "超声科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "内科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "全科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "中医", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "整形医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "精神心理科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药剂师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "口腔科医生", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "验光师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医学检验", "rank": 0, "mark": 11}], "name": "医生/医技", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "护士长", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "护士", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210503, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "导医", "rank": 0, "mark": 11}], "name": "护士/护理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210411, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "足疗师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210412, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "按摩师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210415, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "采耳师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "产后康复师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "营养师/健康管理师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210403, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "理疗师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "针灸推拿", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "康复治疗师", "rank": 0, "mark": 11}], "name": "理疗保健", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210124, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "细胞培养员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210125, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药物分析", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210126, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药物合成", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210127, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医疗产品技术支持", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210128, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生物信息工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210129, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "制剂研发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210130, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药理毒理研究员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生物学研究人员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药品注册", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药品生产", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210123, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医药项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医药研发", "rank": 0, "mark": 11}], "name": "生物医药", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210901, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "试剂研发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210121, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医疗器械注册", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210122, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医疗器械生产/质量管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医疗器械研发", "rank": 0, "mark": 11}], "name": "医疗器械", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药店店长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 210802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "执业药师/驻店药师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210803, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "药店店员", "rank": 0, "mark": 21}], "name": "药店", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 210700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "医学编辑", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 210701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他医疗健康职位", "rank": 0, "mark": 11}], "name": "其他医疗健康职位", "rank": 0, "mark": 0}], "name": "医疗健康", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 250000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 250100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 250109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "招标专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "投标专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商品专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 140312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商品经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "供应商质量工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "采购总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "采购经理/主管", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "采购专员/助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "买手", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 250105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "采购工程师", "rank": 0, "mark": 11}], "name": "采购", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 250200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 250204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "贸易跟单", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "报关/报检员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "单证员", "rank": 0, "mark": 11}], "name": "进出口贸易", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 250300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 250301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他采购/贸易类职位", "rank": 0, "mark": 11}], "name": "其他采购/贸易职位", "rank": 0, "mark": 0}], "name": "采购/贸易", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 240000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 240100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 240118, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "配送站长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 240119, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "跟车员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 240101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "供应链专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "供应链经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "集装箱管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流跟单", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "调度员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流/仓储项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "水/空/陆运操作", "rank": 0, "mark": 11}], "name": "物流", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 240200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 240201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "仓库主管/经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "仓库管理员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 240205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "仓库文员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 240206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "配/理/拣/发货", "rank": 0, "mark": 21}], "name": "仓储", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 240300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "无人机飞手", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客运司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 150208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商务司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网约车司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "代驾司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "驾校教练", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "货运司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 240303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "配送员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 240304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "快递员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "送餐员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 240110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "运输经理/主管", "rank": 0, "mark": 11}], "name": "交通/运输", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 240400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 240401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "供应链总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 240402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物流总监", "rank": 0, "mark": 11}], "name": "高端供应链职位", "rank": 0, "mark": 0}], "name": "供应链/物流", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "装修造价", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220702, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "装修监理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220222, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "装修项目经理", "rank": 0, "mark": 11}], "name": "装饰装修", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220226, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工程检测员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工程招投标", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工程监理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工程造价", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220211, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "资料员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220212, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "建筑施工项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220218, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "施工员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220219, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "测绘/测量", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220220, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "材料员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220225, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "施工安全员", "rank": 0, "mark": 11}], "name": "工程管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300638, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "水电工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300639, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "泥瓦工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300625, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "空调工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300626, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电梯工", "rank": 0, "mark": 22}], "name": "建筑/装修工人", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "房地产策划", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "房地产项目管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "房地产估价师", "rank": 0, "mark": 11}], "name": "房地产规划开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "建筑工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "建筑设计师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "土木/土建/结构工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "园林/景观设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220207, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "城乡规划设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220213, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "弱电工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220214, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "给排水工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220215, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "暖通工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220216, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "幕墙工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220221, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "BIM工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220223, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "建筑机电工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 220224, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "消防工程师", "rank": 0, "mark": 11}], "name": "建筑/规划设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物业工程主管", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 220401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物业经理", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 220404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "综合维修工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 220405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "绿化工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 220406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "物业管理员", "rank": 0, "mark": 22}], "name": "物业管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 220600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 220601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他房地产职位", "rank": 0, "mark": 11}], "name": "其他房地产职位", "rank": 0, "mark": 0}], "name": "房地产/建筑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 400000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 400100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 400101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "农业/林业技术员", "rank": 0, "mark": 11}], "name": "农业/林业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 400200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 400201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "饲养员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 400202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "养殖技术员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 400203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "畜牧兽医", "rank": 0, "mark": 11}], "name": "畜牧/渔业", "rank": 0, "mark": 0}], "name": "农/林/牧/渔", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 260000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 260100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 260114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工程咨询", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "知识产权/专利/商标代理人", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "心理咨询师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "婚恋咨询师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "企业管理咨询", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "咨询总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "咨询经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "IT咨询顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "人力资源咨询顾问", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "咨询项目管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "战略咨询", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "市场调研", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他咨询顾问", "rank": 0, "mark": 11}], "name": "咨询/调研", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 260200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 260203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "知识产权律师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "律师助理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "律师", "rank": 0, "mark": 11}], "name": "律师", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 260300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 260301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "英语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260302, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "日语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "韩语/朝鲜语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "法语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "德语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "俄语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "西班牙语翻译", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 260308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他语种翻译", "rank": 0, "mark": 11}], "name": "翻译", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 260500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 260501, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他咨询/翻译类职位", "rank": 0, "mark": 11}], "name": "其他咨询类职位", "rank": 0, "mark": 0}], "name": "咨询/翻译/法律", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 280000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 280100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 280101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "计调", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 280102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "签证专员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 280104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "导游", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 280105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "票务员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 280106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "讲解员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 280201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "旅游产品经理", "rank": 0, "mark": 11}], "name": "旅游服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 280300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 280301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他旅游职位", "rank": 0, "mark": 11}], "name": "其他旅游职位", "rank": 0, "mark": 0}], "name": "旅游", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保安", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290112, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "安检员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290117, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保安主管/队长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290120, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "押运员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290121, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "消防中控员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290123, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "消防维保员", "rank": 0, "mark": 22}], "name": "安保服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 291000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290113, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "手机维修", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290114, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家电维修", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290124, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电动车/摩托车维修", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290166, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电脑/打印机维修", "rank": 0, "mark": 22}], "name": "维修服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290223, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "奶茶店店员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290224, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "水台/水产员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290225, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "面包/烘焙师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290226, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "储备店长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290227, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "调酒师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290228, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "餐饮督导", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "后厨", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "配菜打荷", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290210, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "茶艺师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290211, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "蛋糕/裱花师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290212, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "餐饮学徒", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290213, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "面点师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290214, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "行政总厨", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290215, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "厨师长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290216, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "传菜员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290217, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "洗碗工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290218, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "凉菜厨师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290219, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "中餐厨师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290220, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "西餐厨师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290221, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "日料厨师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290222, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "烧烤师傅", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "收银", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "服务员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "厨师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "咖啡师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "餐饮店长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290207, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "餐饮前厅经理/领班", "rank": 0, "mark": 23}], "name": "餐饮", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290158, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "民宿管家", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "礼仪/迎宾/接待", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290115, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "酒店前厅经理", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290116, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客房经理", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "酒店前台", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "客房服务员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "酒店经理", "rank": 0, "mark": 23}], "name": "酒店", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "商场运营", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 290305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "督导/巡店", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "理货/陈列员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "防损员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "卖场经理", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "促销员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "店员/营业员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "门店店长", "rank": 0, "mark": 23}], "name": "零售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290800, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 210407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "纹绣师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210408, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美体师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210409, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美发助理/学徒", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210410, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美容店长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 210414, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美容顾问", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 210607, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "发型师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210608, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美甲美睫师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210609, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化妆/造型/服装", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290801, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "养发师", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美容导师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 210405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "美容师", "rank": 0, "mark": 21}], "name": "美容美发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 230200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 230213, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "洗车工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 230214, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "加油员", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 230203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车服务顾问", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车维修", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车美容", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车查勘定损", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230207, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "二手车评估师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "4S店店长/维修站长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 230209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车改装", "rank": 0, "mark": 22}], "name": "汽车服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "花艺师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290702, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "婚礼策划", "rank": 0, "mark": 11}], "name": "婚礼/花艺", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "宠物美容", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290602, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "宠物医生", "rank": 0, "mark": 11}], "name": "宠物服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保洁", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保姆", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "月嫂", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "育婴师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 290111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "护工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290122, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "保洁主管", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 290169, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "收纳师", "rank": 0, "mark": 22}], "name": "家政/保洁", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 290400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 290313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "网吧网管", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "游戏陪玩", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 290401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他服务业职位", "rank": 0, "mark": 11}], "name": "其他服务业职位", "rank": 0, "mark": 0}], "name": "服务业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 101500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 100803, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "自动化工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电气工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 101404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电气设计工程师", "rank": 0, "mark": 11}], "name": "电气/自动化", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300900, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300901, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "环保工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300902, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "环评工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300903, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "EHS工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300904, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "碳排放管理师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300905, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "环境采样/检测员", "rank": 0, "mark": 11}], "name": "环保", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 301000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 301001, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "地质工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 301002, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "光伏系统工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 301003, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "风电/光伏运维工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 301004, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "水利工程师", "rank": 0, "mark": 11}], "name": "能源/地质", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "厂务", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "厂长", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产总监", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "车间主任", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300104, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产组长/拉长", "rank": 0, "mark": 23}, {"cityType": 0, "capital": 0, "code": 300106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产设备管理", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产计划/物料管理(PMC)", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产跟单/文员", "rank": 0, "mark": 21}], "name": "生产营运", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300200, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300208, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "质检员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300209, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "计量工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300210, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "安全评价师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300201, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "质量管理/测试工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300202, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "可靠度工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300203, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "失效分析工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300204, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "产品认证工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300205, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "体系工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300206, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "体系审核员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300207, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "生产安全员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 230109, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车质量工程师", "rank": 0, "mark": 11}], "name": "质量安全", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 230100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 230111, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "总布置工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300803, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "线束设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230102, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "车身/造型设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230103, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "底盘工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230105, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "动力系统工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230106, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车电子工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230107, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车零部件设计", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230108, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "汽车项目管理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230110, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "内外饰设计工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 230210, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "总装工程师", "rank": 0, "mark": 11}], "name": "汽车研发/制造", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300300, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300318, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "液压工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300319, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "仿真工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300320, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "装配工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300321, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "家电/3C结构工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300802, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电机工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 100813, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "热设计工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300301, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机械工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300303, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机械设备工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300304, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "设备维修保养工程师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300305, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机械制图员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300306, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机械结构工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300307, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工业工程师(IE)", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300308, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "工艺工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300309, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "材料工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300310, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机电工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300312, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "冲压工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300313, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "夹具工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300314, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "模具工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300315, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "焊接工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300316, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "注塑工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300317, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "铸造/锻造工程师", "rank": 0, "mark": 11}], "name": "机械设计/制造", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300400, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300407, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化工项目经理", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300401, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化工工程师", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300402, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "实验室技术员", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300404, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "涂料研发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300405, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "化妆品研发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300406, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "食品/饮料研发", "rank": 0, "mark": 11}], "name": "化工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300500, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300511, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "量体师", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300507, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "面料辅料开发", "rank": 0, "mark": 11}, {"cityType": 0, "capital": 0, "code": 300509, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "打样/制版", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300510, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "服装/纺织/皮革跟单", "rank": 0, "mark": 11}], "name": "服装/纺织/皮革", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300600, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300601, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "普工/操作工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 300634, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "挖掘机司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300635, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "弱电工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300637, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "裁剪工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300311, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "CNC数控操机/编程员", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300602, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "叉车工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300603, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "铲车司机", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300604, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "焊工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300605, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "氩弧焊工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300606, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300608, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "木工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300609, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "油漆工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300610, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "车工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300611, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "磨工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300612, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "铣工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300613, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "钳工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300614, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "钻工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300615, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "铆工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300616, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "钣金工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300617, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "抛光工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300618, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "机修工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300619, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "折弯工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300620, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "电镀工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300621, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "喷塑工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300622, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "注塑工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300623, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "组装工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 300624, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "包装工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 300627, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "锅炉工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300628, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "学徒工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 300629, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "缝纫工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300630, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "搬运工/装卸工", "rank": 0, "mark": 21}, {"cityType": 0, "capital": 0, "code": 300631, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "切割工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300632, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "样衣工", "rank": 0, "mark": 22}, {"cityType": 0, "capital": 0, "code": 300633, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "模具工", "rank": 0, "mark": 22}], "name": "技工/普工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 300700, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 300701, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他生产制造职位", "rank": 0, "mark": 11}], "name": "其他生产制造职位", "rank": 0, "mark": 0}], "name": "生产制造", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "code": 200000, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 200100, "positionType": 0, "recruitmentType": "", "regionCode": 0, "subLevelModelList": [{"cityType": 0, "capital": 0, "code": 200101, "positionType": 3, "recruitmentType": "1,2,3", "regionCode": 0, "name": "其他职位", "rank": 0, "mark": 11}], "name": "其他职位类别", "rank": 0, "mark": 0}], "name": "其他", "rank": 0, "mark": 0}]}