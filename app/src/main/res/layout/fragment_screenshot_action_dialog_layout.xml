<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="40dp"
    android:paddingRight="40dp"
    tools:background="@color/color_FF000000">

    <zpui.lib.ui.shadow.layout.ZPUIConstraintLayout
        android:id="@+id/cl_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/color_FFFFFFFF_FF151517"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:zpui_radius="3dp">

        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="20dp"
            android:text="分享给微信好友"
            android:textColor="@color/color_FF141414_FFE6E6EB"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <zpui.lib.ui.shadow.layout.ZPUIConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:background="@color/color_FFFFFFFF_FF151517"
            android:paddingLeft="10dp"
            android:paddingTop="11dp"
            android:paddingRight="10dp"
            android:paddingBottom="4dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:zpui_radius="3dp"
            app:zpui_shadowAlpha="0.5"
            app:zpui_shadowElevation="10dp">

            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/tv_job_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:lines="1"
                android:textColor="@color/color_FF141414_FFE6E6EB"
                android:textSize="18dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="看准网有限科技公看准技公...·服务员" />

            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/tv_salary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_FF0D9EA3"
                android:textSize="18dp"
                app:custom_font_activate="true"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_job_name"
                tools:text="15-20K · 16薪16薪16薪16薪16薪16薪16薪16薪16薪16薪16薪" />

            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/tv_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_FF5E5E5E_FF9E9EA1"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_salary"
                tools:text="北京 · 经验不限 · 学历不限" />

            <com.hpbr.bosszhipin.module.resume.views.FlowLayout
                android:id="@+id/fbl"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_desc"
                app:maxLine="1" />

            <TextView
                android:id="@+id/tv_job_detail_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="职位详情"
                android:textColor="@color/text_c1"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/fbl" />

            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/tv_job_detail_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="end"
                android:lineSpacingExtra="8dp"
                android:maxLines="3"
                android:textColor="@color/text_c2"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_job_detail_title"
                tools:text="职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情情职位详情职位详情职位详情职位详情职位详情职位详情职位详情职位详情" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/bj_share_position_shadow"
                app:layout_constraintBottom_toBottomOf="@id/tv_job_detail_desc"
                app:layout_constraintLeft_toLeftOf="@id/tv_job_detail_desc"
                app:layout_constraintRight_toRightOf="@id/tv_job_detail_desc"
                app:layout_constraintTop_toTopOf="@+id/tv_job_detail_desc"
                tools:ignore="MissingConstraints" />


        </zpui.lib.ui.shadow.layout.ZPUIConstraintLayout>

        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/tv_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="24dp"
            android:text="发送"
            android:textColor="@color/color_FF0D9EA3"
            android:textSize="16dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_content" />

        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="24dp"
            android:text="取消"
            android:textColor="@color/color_FFB8B8B8_FF6D6D70"
            android:textSize="16dp"
            app:layout_constraintRight_toLeftOf="@+id/tv_send"
            app:layout_constraintTop_toBottomOf="@+id/cl_content" />

    </zpui.lib.ui.shadow.layout.ZPUIConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
