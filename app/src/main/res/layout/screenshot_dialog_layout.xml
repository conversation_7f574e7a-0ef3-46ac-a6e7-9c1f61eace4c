<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:paddingLeft="52dp"
    android:paddingRight="52dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/transparent"
            app:cardCornerRadius="3dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <androidx.core.widget.NestedScrollView
                    android:id="@+id/screenshot_view"
                    android:layout_width="match_parent"
                    android:layout_height="380dp"
                    android:scrollbars="vertical">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="false">

                        <ImageView
                            android:id="@+id/screenshot_img"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:adjustViewBounds="true"
                            android:background="@color/agree_toast_bg_color"
                            android:scaleType="centerCrop" />


                        <ImageView
                            android:id="@+id/screenshot_title"
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:scaleType="centerCrop"
                            android:src="@mipmap/screenshot_title" />

                    </FrameLayout>
                </androidx.core.widget.NestedScrollView>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:background="@color/color_FFFFFFFF_FF151517"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/screenshot_share"
                        android:layout_width="0dp"
                        android:layout_height="35dp"
                        android:layout_marginLeft="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/screenshot_dialog_button_share"
                        android:gravity="center">


                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginRight="3dp"
                            android:background="@mipmap/screenshot_share" />

                        <com.hpbr.bosszhipin.views.MTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="分享好友"
                            android:textColor="@color/color_FF0D9EA3"
                            android:textSize="13sp" />


                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/screenshot_save"
                        android:layout_width="0dp"
                        android:layout_height="35dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/screenshot_dialog_button_save"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginRight="3dp"
                            android:background="@mipmap/screenshot_save" />

                        <com.hpbr.bosszhipin.views.MTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="保存图片"
                            android:textColor="@color/color_FFFFFFFF"
                            android:textSize="13sp" />

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <ImageView
                android:id="@+id/screenshot_close"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30dp"
                android:src="@mipmap/screenshot_close" />

        </RelativeLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
