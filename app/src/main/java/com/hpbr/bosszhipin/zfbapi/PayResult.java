package com.hpbr.bosszhipin.zfbapi;

import android.text.TextUtils;

import java.util.Map;

public class PayResult {
	private String resultStatus;
	private String result;
	private String memo;

	// 1215 支付流程打点，记录支付宝SDK调用支付方法，返回的全部信息
	private Map<String ,String> resultParams;

	public PayResult(Map<String, String> rawResult) {
		if (rawResult == null) {
			return;
		}

		for (String key : rawResult.keySet()) {
			if (TextUtils.equals(key, "resultStatus")) {
				resultStatus = rawResult.get(key);
			} else if (TextUtils.equals(key, "result")) {
				result = rawResult.get(key);
			} else if (TextUtils.equals(key, "memo")) {
				memo = rawResult.get(key);
			}
		}
		resultParams = rawResult;
	}

	@Override
	public String toString() {
		return "resultStatus={" + resultStatus + "};memo={" + memo
				+ "};result={" + result + "}";
	}

	/**
	 * @return the resultStatus
	 */
	public String getResultStatus() {
		return resultStatus;
	}

	/**
	 * @return the memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * @return the result
	 */
	public String getResult() {
		return result;
	}

	/**
	 * 1215 支付流程打点，获取支付宝SDK支付方法返回的，支付结果信息
	 * @return resultParams
	 */
	public Map<String, String> getResultParams() {
		return resultParams;
	}
}
