package com.hpbr.bosszhipin.data.manager;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Looper;
import android.os.MessageQueue;
import android.text.TextUtils;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.WebStorage;

import com.bszp.kernel.DataKernel;
import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.user.UserHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.pub.entity.LOGIN_STATUS;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.get.export.GetRouter;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.manager.EnvLogReporter;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserInfoModel;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module.main.fragment.contacts.F2ContactMarkLabelManager;
import com.hpbr.bosszhipin.module.my.activity.boss.brand.bean.BrandInfoBean;
import com.hpbr.bosszhipin.receiver.LoginStatusManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.listview.BossBannerDataManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.process.ProcessUtil;

import net.bosszhipin.api.GetTicketRequest;
import net.bosszhipin.api.GetTicketRespone;
import net.bosszhipin.api.bean.user.GeekFeature;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import me.leolin.shortcutbadger.ShortcutBadger;
import message.handler.analysis.ChatAnalyzer;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;

/**
 * Created by monch on 15/4/7.
 * 登录用户管理类
 */
public class UserManager {

    private static final String TAG = "UserManager";

    private static final String TOKEN = "com.hpbr.bosszhipin.TOKEN";

    private static final String WEB_TOKEN = "com.hpbr.bosszhipin.WEB_TOKEN";

    private static final String SECRET_KEY = "com.hpbr.bosszhipin.SECRET_KEY";

    private static final String UID = "com.hpbr.bosszhipin.UID";

//    private static final String MI_PUSH_TOKEN = "com.hpbr.bosszhipin.MI_PUSH_TOKEN";
//    private static final String IXT_PUSH_TOKEN = "com.hpbr.bosszhipin.IXT_PUSH_TOKEN";
//    private static final String HW_PUSH_TOKEN = "com.hpbr.bosszhipin.HW_PUSH_TOKEN";

    private static final String TWL_PUSH_TOKEN = "com.hpbr.bosszhipin.TWL_PUSH_TOKEN";

    private static void printUserChange() {
        L.info(TAG, "==========用户改变：【LOGIN=%b，UID=%d，ROLE=%d】==========", isCurrentLoginStatus(), getUID(), getUserRole().get());
    }

    /**
     * 设置当前用户登录状态
     *
     * @param status
     * @return
     */
    public static int setUserLoginStatus(final int status) {
        // apm 上报登录成功的信息
        apmReportLoginSuccess(status);
        AccountHelper.setUserLoginStatus(status);
        printUserChange();
        return status;
    }

    /**
     * 获取当前用户登录状态
     *
     * @return
     */
    public static int getUserLoginStatus() {
        return AccountHelper.getUserLoginStatus();
    }

    /**
     * 当前登录状态为：已登录
     *
     * @return
     */
    public static boolean isCurrentLoginStatus() {
        return AccountHelper.isCurrentLoginStatus();
    }

    /**
     * 传入roleId，获取角色实例
     *
     * @param roleInt
     * @return
     */
    public static ROLE getUserRole(int roleInt) {
        ROLE role = ROLE.GEEK;
        if (roleInt == 0) {
            role = ROLE.GEEK;
        } else if (roleInt == 1) {
            role = ROLE.BOSS;
        }
        return role;
    }

    /**
     * 获取当前用户角色
     *
     * @return
     */
    public static ROLE getUserRole() {
        return getUserRole(AccountHelper.getIdentity());
    }

    /**
     * 当前用户是否是boss
     */
    public static boolean isBossRole() {
        return AccountHelper.isBoss();
    }

    public static boolean isGeekRole() {
        return AccountHelper.isGeek();
    }

    /**
     * 是否是中介代招人员
     *
     * @return
     */
    public static boolean isRecruiter() {
        boolean isRecruiter = false;
        if (isBossRole()) {
            UserBean user = getLoginUser();
            if (user != null && user.bossInfo != null) {
                isRecruiter = user.bossInfo.isRecruit;
            }
        }
        return isRecruiter;
    }

    /**
     * getToken()方法 优先取T2，取不到再取T 8.16版本token改造
     **/
    public static String getToken() {
        if (!TextUtils.isEmpty(AccountHelper.getToken2())) {
            return AccountHelper.getToken2();
        }
        return AccountHelper.getToken();
    }

    public static void setToken(String token) {
        SP.get().putString(TOKEN, token);
    }

    public static String getSecretKey() {
        return AccountHelper.getSecretKey();
    }

    public static void setSecretKey(String secretKey) {
        SP.get().putString(SECRET_KEY, secretKey);
    }

    public static long getUID() {
        return AccountHelper.getUid();
    }

    public static int getUIDLastNum() {
        return (int) (AccountHelper.getUid() % 100);
    }

    public static void setUID(long uid) {
        SP.get().putLong(UID, uid);
        printUserChange();
    }

    public static String getPushToken() {
        return SP.get().getString(TWL_PUSH_TOKEN);
    }

    public static void setPushToken(String token) {
        SP.get().putString(TWL_PUSH_TOKEN, token);
    }

    /**
     * 用户登录失效
     *
     * @param context
     */
    public static void setAccountInvalid(Context context, boolean isKick, String msg) {
        if (!ProcessUtil.isMainProcess(context)) {
            try {
                CrashReport.postCatchedException(new RuntimeException("Other process setAccountInvalid!!!"));
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return;
        }
        //发送 登出 广告
        Intent kickOutIntent = new Intent();
        kickOutIntent.setAction(Constants.ACTION_LOGIN_KICK_OUT);
        ReceiverUtils.sendBroadcast(App.get(), kickOutIntent);

        final boolean isLoginStatus = UserManager.isCurrentLoginStatus();
        if (isLoginStatus && TextUtils.isEmpty(UserManager.getPhone())) {
            ApmAnalyzer.create().action("userInfo", "phone")
                    .p2(String.valueOf(AccountHelper.getUid()))
                    .p3(String.valueOf(AccountHelper.getAccount()))
                    .p4(String.valueOf(AccountHelper.getLoginAppVersionCode()))
                    .report();
        }

        try {
            clearCookies(context);
            MSGManager.get().disconnect();
        } catch (Throwable e) {
            TLog.error(TAG, e, "setAccountInvalid");
        }
        //退出登录
        UserManager.setUserLoginStatus(LOGIN_STATUS.NONE);
        DataKernel.getInstance().logout(isKick);
        //聊天
        ContactManager.getInstance().clean();
        GroupManager.getInstance().clean();
        ContactManager.getInstance().obtainRefreshStrategy().initRefreshTime();
        ContactManager.getInstance().obtainRefreshStrategy().resetRefreshing();
        MessageDaoFactory.clearMaxMessageId();
        //聊天
        NotifyUtils.resetNotifyId();

        // 发送退出登录广播
        LoginStatusManager.changed(context, false);

        // 跳转到登录页
        App.get().finishAll();
        jumpToGetStarted(context);
        //退出登录切换数据
        F2ContactMarkLabelManager.getInstance().clear();
        //推出登录清空新招呼缓存数据
        BossBannerDataManager.getInstance().clear();
        // 退出后 清除草稿
        GetRouter.clearVideoDraft();
        //退出后，清除所有上传任务
        GetRouter.clearAllUploadVideoTask();
        if (isKick && isLoginStatus) {
            Utils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (TextUtils.isEmpty(msg)) {
                        ToastUtils.showText(R.string.account_other_login);
                    } else {
                        ToastUtils.showText(msg);
                    }
                }
            });
        }
        printUserChange();
        //刷新桌面聊天气泡数量
        ShortcutBadger.applyCount(context, 0);
    }

    private static void jumpToGetStarted(Context context) {
        LoginRouter.startLoginActivityClearTask(context);
    }

    private static void clearCookies(Context context) {
        WebStorage storage = WebStorage.getInstance();
        storage.deleteAllData();
        CookieManager c = CookieManager.getInstance();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            c.removeAllCookies(null);
        } else {
            CookieSyncManager.createInstance(context);
            c.removeAllCookie();
        }
    }

    public static void setAccountInvalid(Context context, boolean isKick) {
        setAccountInvalid(context, isKick, "");
    }

    public static void syncMessageMaxMid() {
        long uid = AccountHelper.getUid();
        int identity = AccountHelper.getIdentity();
        //单聊
        long maxMessageId = MessageDaoFactory.getMessageDao().getLastMessageId(uid, identity);
        long spMaxNewMsgId = MessageDaoFactory.getMaxNewMessageId();
        MessageDaoFactory.saveMaxMessageId(true, getMaxId(maxMessageId, spMaxNewMsgId));
        checkMaxException(maxMessageId, spMaxNewMsgId, true);

        //群聊
        long maxGroupMessageId = MessageDaoFactory.getMessageDao().getLastGroupMessageId(uid);
        long spMaxNewGroupMsgId = MessageDaoFactory.getMaxNewGroupMessageId();
        MessageDaoFactory.saveMaxGroupMessageId(true, maxGroupMessageId);
        checkMaxException(maxGroupMessageId, spMaxNewGroupMsgId, false);
        TLog.info(TAG, "syncMessageMaxMid uid = %d identity = %d , db#maxId:%d sp#maxId:%d  , group => db#maxId:%d sp#maxId:%d", uid, identity, maxMessageId, spMaxNewMsgId, maxGroupMessageId, spMaxNewGroupMsgId);

    }

    private static void checkMaxException(long messageId, long maxNewSpMessageId, boolean single) {
        if (maxNewSpMessageId < 0) return;
        if (maxNewSpMessageId == messageId) return;
        if (maxNewSpMessageId > messageId) { //sp#maxId > db#maxId 异常
            ChatAnalyzer.report(ChatAnalyzer.MAX_ID_EXCEPTION).p2(String.valueOf(maxNewSpMessageId)).p3(String.valueOf(messageId)).p4(single ? "single" : "group").debug().report();
        } else if (maxNewSpMessageId < messageId) { //sp#maxId < db#maxId 可能离线同步未完成， 收到试试消息
            ChatAnalyzer.report(ChatAnalyzer.MAX_ID).p2(String.valueOf(maxNewSpMessageId)).p3(String.valueOf(messageId)).p4(single ? "single" : "group").debug().report();
        }
    }

    private static long getMaxId(long dbMaxMid, long newSpMaxId) {
        long maxId;
        if (newSpMaxId == -1) { //修正逻辑
            maxId = dbMaxMid;
        } else {
            maxId = Math.min(dbMaxMid, newSpMaxId);
        }
        return maxId;
    }

    /**
     * 计算牛人信息是否完善
     *
     * @return True - 完善 | False -未完善
     */
    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public static boolean isInfoCompleteGeek(@Nullable UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        GeekInfoBean geekInfo = user.geekInfo;
        int completeStatus = 0;
        if (geekInfo != null) {
            completeStatus = geekInfo.completeStatus;
        }
        boolean isGeekInfoComplete;
        // 当completeStatus > 0 时，服务器认为用户信息完整，完善状态，1-完善，2-不完善
        if (completeStatus > 0) {
            isGeekInfoComplete = completeStatus == 1;
        }
        // 否则由本地判断
        else {
            isGeekInfoComplete = isBasicInfoCompleteGeek(user) && isMoreInfoCompleteGeek(user);
        }
        return isGeekInfoComplete;
    }

    static boolean isGeekInfoDirty = false;

    //获得用户头像
    public static String getAvatarUrl() {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser != null) {
            return loginUser.largeAvatar;
        }
        return "";
    }

    public static String getUserKey() {
        return UserManager.getUID() + ":" + UserManager.getUserRole().get();
    }
    /**
     * 计算牛人基本信息是否完整
     *
     * @param user
     * @return
     */
    public static boolean isBasicInfoCompleteGeek(UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        final String tag = "UserManager.isBasicInfoCompleteGeek";
        GeekInfoBean geekInfoBean = user.geekInfo;
        if (geekInfoBean == null) {
            L.i(tag, "牛人信息不完整");
            return false;
        }

        if (LText.empty(user.avatar)
                && (geekInfoBean.headDefaultImageIndex <= 0
                || geekInfoBean.headDefaultImageIndex > 16)) {
            L.i(tag, "头像信息不完整");
            return false;
        }

        if (LText.empty(user.name)) {
            L.i(tag, "用户名不完整");
            return false;
        }

        if (user.gender < 0 || user.gender > 2) {
            L.i(tag, "性别信息不完整");
            return false;
        }

        if (geekInfoBean.graduate == GeekInfoBean.FRESH_GRADUATE_FIND_JOB
                || geekInfoBean.graduate == GeekInfoBean.NONE_WORK_EXPERIENCE
                || geekInfoBean.graduate == GeekInfoBean.AT_SCHOOL_INTERN
                || geekInfoBean.graduate == GeekInfoBean.SOCIAL_RECRUITMENT
                || geekInfoBean.graduate == GeekInfoBean.STUDENT_RECORD) {
            // 【应界生】【实习生】【无工作经验】【职场人】 需要判断教育经历是否完善
            if (geekInfoBean.eduList == null || geekInfoBean.eduList.size() <= 0) {
                L.i(tag, "牛人教育经历不完整");
                return false;
            }
            // 【职场人】 需要判断 工作经历是否完善
            if (geekInfoBean.graduate == GeekInfoBean.SOCIAL_RECRUITMENT) {
                if (geekInfoBean.workList == null
                        || geekInfoBean.workList.size() <= 0) {
                    L.i(tag, "牛人工作经历不完整");
                    return false;
                }
            }
        } else {
            L.i(tag, "是否为应届生信息不正确");
            return false;
        }

        if (LText.empty(geekInfoBean.advantageTitle)) {
            L.i(tag, "牛人优势不完整");
            return false;
        }

        return true;
    }

    /**
     * 计算牛人求职意向信息是否完整
     * true完整false不完整
     *
     * @param user
     * @return
     */
    public static boolean isMoreInfoCompleteGeek(UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        if (user.geekInfo == null) {
            return false;
        }

        GeekInfoBean geekInfo = user.geekInfo;
        //【社招】||【应届生】
        if (geekInfo.isSocial() || geekInfo.isFreshGraduate()) {
            return !LList.isEmpty(user.geekInfo.jobIntentList);
        }

        //【实习生】||【无工作经验】||【学生档】
        if (geekInfo.isIntern() || geekInfo.isNoneWorkExp() || isStudentRecord()) {
            return !LList.isEmpty(user.geekInfo.internJobIntentList);
        }

        return false;
    }

    /**
     * 计算BOSS信息是否完善
     *
     * @return True - 完善 | False -未完善
     */
    public static boolean isInfoCompleteBoss(@Nullable UserBean user) {
        return isBasicInfoCompleteBoss(user) && isMoreInfoCompleteBoss(user);
    }

    /**
     * 计算BOSS基本信息是否完整
     *
     * @param user
     * @return
     */
    public static boolean isBasicInfoCompleteBoss(@Nullable UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        BossInfoBean bossInfoBean = user.bossInfo;
        if (bossInfoBean == null) {
            TLog.info(TAG, "Boss信息不完整");
            return false;
        }
        if (LText.empty(user.avatar)
                && (bossInfoBean.headDefaultImageIndex <= 0
                || bossInfoBean.headDefaultImageIndex > 16)) {
            TLog.info(TAG, "头像信息不完整");
            return false;
        }
        if (LText.empty(user.name)) {
            TLog.info(TAG, "用户名不完整");
            return false;
        }
        if (LText.empty(bossInfoBean.positionDesc)) {
            TLog.info(TAG, "我的职位不完整");
            return false;
        }
        if (LText.empty(bossInfoBean.companyFullName)) {
            TLog.info(TAG, "公司全称不完整");
            return false;
        }
        if (LList.isEmpty(bossInfoBean.brandList)) {
            TLog.info(TAG, "品牌不完整");
            return false;
        }
        return true;
    }

    /**
     * 计算BOSS职位信息是否完整
     *
     * @param user
     * @return
     */
    public static boolean isMoreInfoCompleteBoss(@Nullable UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        final String tag = "UserManager.isMoreInfoCompleteBoss";
        BossInfoBean bossInfoBean = user.bossInfo;
        if (bossInfoBean == null) {
            L.i(tag, "Boss信息不完整");
            return false;
        }
        if (LList.getCount(BossJobListProvider.getInstance().getCanUseJobListReadOnly()) <= 0 && getUnPaidJobList(user).size() <= 0) {
            L.i(tag, "Boss职位信息不完整");
            return false;
        }
        return true;
    }

    /**
     * 计算BOSS职位信息是否完整
     *
     * @param user
     * @return
     */
    public static boolean isMoreInfoCompleteBossRegardlessUnpaidJobs(@Nullable UserBean user) {
        if (!isCurrentLoginStatus() || user == null) return false;
        final String tag = "UserManager.isMoreInfoCompleteBoss";
        BossInfoBean bossInfoBean = user.bossInfo;
        if (bossInfoBean == null) {
            L.i(tag, "Boss信息不完整");
            return false;
        }
        if (LList.getCount(BossJobListProvider.getInstance().getCanUseJobListReadOnly()) <= 0) {
            L.i(tag, "Boss职位信息不完整");
            return false;
        }
        return true;
    }

    /**
     * 获取有效的职位列表
     *
     * @deprecated Use {@link BossJobListProvider#getCanUseJobListCopy()}
     */
    @Deprecated
    public static List<JobBean> getCanUseJobList(UserBean userBean) {
        return BossJobListProvider.getInstance().getCanUseJobListCopy();
    }

    /**
     * @deprecated Use {@link BossJobListProvider#findJobById(long)}
     */
    @Deprecated
    public static JobBean findJobById(long jobId) {
        return BossJobListProvider.getInstance().findJobById(jobId);
    }

    /**
     * 获取未付费职位
     *
     * @param user
     * @return
     */
    public static List<JobBean> getUnPaidJobList(UserBean user) {
        List<JobBean> jobList = new ArrayList<>();
        List<JobBean> jobListCopy = BossJobListProvider.getInstance().getJobListCopy();
        if (user != null && !LList.isEmpty(jobListCopy)) {
            List<String> jobIds = StringUtil.splitKeywords(user.unpaidList);
            if (!LList.isEmpty(jobIds)) {
                LongSparseArray<JobBean> array = new LongSparseArray<>();
                for (JobBean job : jobListCopy) {
                    if (job == null || job.id <= 0) continue;
                    array.put(job.id, job);
                }

                for (String s : jobIds) {
                    if (TextUtils.isEmpty(s)) continue;
                    long id = LText.getLong(s);
                    if (id <= 0) continue;
                    if (array.indexOfKey(id) < 0) continue;
                    JobBean job = array.get(id);
                    if (job != null && JobStatusChecker.isJobStatusWaitForOpening(job.status)) {
                        jobList.add(job);
                    }
                }
                array.clear();
            }
        }
        return jobList;
    }

    /**
     * 查看有效职位里面是否有存在没有工作地址职位
     *
     * @param jobList 工作列表
     * @return
     */
    public static boolean hasEmptyAddressJob(List<JobBean> jobList) {
        boolean hasEmpty = false;
        if (jobList == null) return hasEmpty;
        for (JobBean bean : jobList) {
            if (bean == null) continue;
            if (!JobStatusChecker.isJobStatusClosed(bean.status) && LText.empty(bean.workAddress)) {
                hasEmpty = true;
                break;
            }
        }
        return hasEmpty;
    }

    /**
     * 获得最近工作信息
     *
     * @param jobList
     * @return
     */
    public static JobBean getRecentJobInfo(List<JobBean> jobList) {
        if (jobList != null) {
            final int lastIndex = jobList.size() - 1;
            for (int i = lastIndex; i >= 0; i--) {
                JobBean jobBean = LList.getElement(jobList, i);
                if (jobBean == null) {
                    continue;
                }
                return jobBean;
            }
        }
        return null;
    }

    /**
     * @deprecated Use {@link BossJobListProvider#findJobById(long)}
     */
    @Nullable
    public static JobBean getAvailableJobBean(long positionId) {
        JobBean targetJob = BossJobListProvider.getInstance().findJobById(positionId);
        return JobStatusChecker.isPositionAvailable(targetJob) ? targetJob : null;
    }

    /**
     * 检查公司信息是否完整
     *
     * @param bossInfoBean
     * @return
     */
    public static boolean isCompanyComplete(BossInfoBean bossInfoBean) {
        if (bossInfoBean != null) {
            BrandInfoBean brandInfoBean = LList.getElement(bossInfoBean.brandList, 0);
            if (brandInfoBean != null && brandInfoBean.isDecorateComplete) {
                return true;
            }
        }
        return false;
    }


    /**
     * 获得指定职位的薪资
     *
     * @param jobId
     * @return
     */
    public static String getTargetJobSalary(long jobId) {
        JobBean targetJob = BossJobListProvider.getInstance().findJobById(jobId);
        return targetJob == null ? "" : targetJob.salaryDesc;
    }

    /**
     * 获取用户的数据信息
     *
     * @return
     */
    @Nullable
    public synchronized static UserBean getLoginUser() {
        return UserHelper.getLoginUser();
    }

    @Nullable
    public synchronized static GeekFeature getGeekFeature() {
        GeekFeature geekFeature = null;
        UserBean loginUser = getLoginUser();
        if (getUserRole() == ROLE.GEEK && loginUser != null) {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo != null) {
                geekFeature = geekInfo.geekFeature;
            }
        }
        return geekFeature;
    }

    @Nullable
    public synchronized static BossInfoBean getBossInfo() {
        UserBean loginUser = getLoginUser();
        BossInfoBean bossInfoBean = null;
        if (UserManager.isCurrentLoginStatus()
                && UserManager.getUserRole() == ROLE.BOSS
                && loginUser != null) {
            bossInfoBean = loginUser.bossInfo;
        }
        return bossInfoBean;
    }

    public static long getBrandId() {
        if (UserManager.isGeekRole()) return 0;
        UserBean userBean = UserManager.getLoginUser();
        if (userBean != null) {
            if (userBean.bossInfo != null) {
                BrandInfoBean brandInfoBean = LList.getElement(userBean.bossInfo.brandList, 0);
                if (brandInfoBean != null) {
                    return brandInfoBean.brandId;
                }
            }
        }
        return 0;
    }

    /**
     * 获取Boss的VipStatus
     *
     * @return -1为无效状态
     */
    public static int getBossVipStatus() {
        UserBean userInfo = getLoginUser();
        if (userInfo == null) {
            return -1;
        }
        if (UserManager.isGeekRole()) {
            return -1;
        }
        if (userInfo.bossInfo == null) {
            return -1;
        }

        return userInfo.bossInfo.vipStatus;
    }

    /**
     * 解决登出的情况下 查询手机号
     *
     * @return
     */
    public static String getPhone() {
        return UserHelper.getPhone();
    }

    /**
     * 解决登出的情况下 查询区域号码
     *
     * @return
     */
    public static String getRegionCode() {
        return UserHelper.getRegionCode();
    }

    public static String getSecurityUrl() {
        UserBean userInfo = getLoginUser();
        if (userInfo == null) {
            TLog.error(TAG, "getSecurityUrl = userInfo null");
            return null;
        }
        if (getUserRole() == ROLE.BOSS) {
            BossInfoBean bossInfoBean = userInfo.bossInfo;
            if (bossInfoBean != null) {
                return bossInfoBean.securityUrl;
            }
        } else {
            GeekInfoBean geekInfoBean = userInfo.geekInfo;
            if (geekInfoBean != null) {
                return geekInfoBean.securityUrl;
            }
        }
        TLog.error(TAG, "getSecurityUrl = info null");
        return null;
    }

    public static void setSecurityUrl(String security) {
        UserBean userInfo = getLoginUser();
        if (userInfo == null) {
            return;
        }

        if (getUserRole() == ROLE.BOSS) {
            BossInfoBean bossInfoBean = userInfo.bossInfo;
            if (bossInfoBean != null) {
                if (TextUtils.equals(bossInfoBean.securityUrl, security)) {
                    return;
                }
                bossInfoBean.securityUrl = security;
            }
        } else {
            GeekInfoBean geekInfoBean = userInfo.geekInfo;
            if (geekInfoBean != null) {
                if (TextUtils.equals(geekInfoBean.securityUrl, security)) {
                    return;
                }
                geekInfoBean.securityUrl = security;
            }
        }
        save(userInfo);
    }

    public synchronized static long save(UserBean userBean) {
        UserHelper.saveUserData(userBean);
        return UserManager.getUID();
    }

    public static void closeSafeWindow() {
        setSecurityUrl("");
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_COLOSE_SAFE_WINDOWS);
        App.get().getApplicationContext().sendBroadcast(intent);
    }

    public static ChatUserInfoModel obtainChatUserInfoModel(ChatUserBean chatUserBean, int friendSource) {//生成方式参见ChatBeanFactory
        ChatUserInfoModel userInfoModel = new ChatUserInfoModel();
        if (chatUserBean.id == UserManager.getUID()) { //自己
            UserBean loginUserBean = UserManager.getLoginUser();
            if (loginUserBean == null) return userInfoModel;
            userInfoModel.id = UserManager.getUID();
            userInfoModel.avatar = loginUserBean.avatar;
            userInfoModel.name = loginUserBean.name;
            if (UserManager.getUserRole() == ROLE.BOSS && loginUserBean.bossInfo != null) {
                userInfoModel.company = loginUserBean.bossInfo.company;
                userInfoModel.headDefaultImageIndex = loginUserBean.bossInfo.headDefaultImageIndex;
            } else if (UserManager.getUserRole() == ROLE.GEEK && loginUserBean.geekInfo != null) {
                userInfoModel.company = "";
                userInfoModel.headDefaultImageIndex = loginUserBean.geekInfo.headDefaultImageIndex;
            }
        } else {//好友
            userInfoModel.id = chatUserBean.id;
            userInfoModel.name = chatUserBean.name;
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(chatUserBean.id, UserManager.getUserRole().get(), friendSource);
            if (contactBean != null) {
                userInfoModel.avatar = contactBean.friendDefaultAvatar;
                userInfoModel.headDefaultImageIndex = contactBean.friendDefaultAvatarIndex;
            } else {
                if (chatUserBean.avatar != null) {
                    userInfoModel.avatar = chatUserBean.avatar;
                }
            }
        }
        return userInfoModel;
    }

    /**
     * 当前用户是否是默认头像
     */
    public static boolean isDefaultProfile() {
        final UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) {
            L.e("获取登录用户信息失败");
            return false;
        }

        final int userHeadImage;
        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) {
                L.e("获取BOSS信息失败");
                return false;
            }

            userHeadImage = bossInfo.headDefaultImageIndex;
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) {
                L.e("获取牛人信息失败");
                return false;
            }

            userHeadImage = geekInfo.headDefaultImageIndex;
        }
        return userHeadImage > 0 && userHeadImage < 17;
    }

    /**
     * @param weixin
     * @since 911 保存 加密的
     */
    public static String setWeiXin(String weixin) {
        UserBean loginUser = getLoginUser();
        if (loginUser == null) {
            return weixin;
        }
        if (UserManager.getUserRole() == ROLE.GEEK) {
            GeekInfoBean geekInfoBean = loginUser.geekInfo;
            if (geekInfoBean != null) {
                geekInfoBean.weixin = weixin;
            }
        } else {
            BossInfoBean bossInfoBean = loginUser.bossInfo;
            if (bossInfoBean != null) {
                bossInfoBean.weixin = weixin;
            }
        }
        UserManager.save(loginUser);
        return weixin;
    }

    /**
     * @since 911 返回 加密后的
     */
    public static String getWeiXin() {
        UserBean loginUser = getLoginUser();
        if (loginUser == null) {
            return null;
        }
        if (UserManager.getUserRole() == ROLE.GEEK) {
            GeekInfoBean geekInfoBean = loginUser.geekInfo;
            if (geekInfoBean != null) {
                return geekInfoBean.weixin;
            }
        } else {
            BossInfoBean bossInfoBean = loginUser.bossInfo;
            if (bossInfoBean != null) {
                return bossInfoBean.weixin;
            }
        }
        return null;
    }

    /**
     * 判断是否是实习生
     *
     * @return
     */
    @Deprecated
    public static boolean isGeekIntern() {
        UserBean user = UserManager.getLoginUser();
        if (UserManager.isGeekRole()
                && user != null
                && user.geekInfo != null) {
            return user.geekInfo.graduate == GeekInfoBean.AT_SCHOOL_INTERN;
        }
        return false;
    }

    /**
     * 是否是学生党3（包含实习生2、应届生1）
     */
    public static boolean isGeekStudent() {
        UserBean user = UserManager.getLoginUser();
        if (UserManager.isGeekRole()
                && user != null
                && user.geekInfo != null) {
            int graduate = user.geekInfo.graduate;
            return graduate == GeekInfoBean.AT_SCHOOL_INTERN
                    || graduate == GeekInfoBean.STUDENT_RECORD
                    || graduate == GeekInfoBean.FRESH_GRADUATE_FIND_JOB;
        }
        return false; // 职场人
    }

    public static boolean isNoneWorkExp() {
        UserBean user = UserManager.getLoginUser();
        if (UserManager.isGeekRole()
                && user != null
                && user.geekInfo != null) {
            return user.geekInfo.graduate == GeekInfoBean.NONE_WORK_EXPERIENCE;
        }
        return false;
    }


    /**
     * 学生档
     *
     * @return
     */
    public static boolean isStudentRecord() {
        UserBean user = UserManager.getLoginUser();
        if (UserManager.isGeekRole()
                && user != null
                && user.geekInfo != null) {
            return user.geekInfo.graduate == GeekInfoBean.STUDENT_RECORD;
        }
        return false;
    }


    /**
     * apm 上报登录成功的信息
     */
    private static void apmReportLoginSuccess(final int status) {
        if (status == LOGIN_STATUS.LOGIN) {
            App.get().getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                        @Override
                        public boolean queueIdle() {
                            // apm 埋点上报
                            boolean firstLogin = SpManager.get().user().getBoolean(Constants.SP_APM_HAS_USER_FIRST_LOGIN, true);
                            ApmAnalyzer.create()
                                    .action(ApmAnalyticsAction.ACTION_U_LOGIN_SUCCESS,
                                            firstLogin ? ApmAnalyticsAction.ACTION_LOGIN_FIRST
                                                    : ApmAnalyticsAction.ACTION_LOGIN_EVER).report();
                            if (firstLogin) {
                                SpManager.get().user().edit().putBoolean(Constants.SP_APM_HAS_USER_FIRST_LOGIN, false).apply();
                            }
                            return false;
                        }
                    });
                }
            });
            //登录后重置环境数据上传标记
            EnvLogReporter.resetLastReportTime();
        }
    }


    /**
     * 8.17增加，主要是用来处理获取T2
     * 8.212版本修改，只有非登录态下才不调用换token接口
     */
    public static void updateToken2() {
        if (!isCurrentLoginStatus()) {
            return;
        }

        GetTicketRequest request = new GetTicketRequest(new ApiRequestCallback<GetTicketRespone>() {
            @Override
            public void onSuccess(ApiData<GetTicketRespone> data) {
                if (data.resp == null || TextUtils.isEmpty(data.resp.t2)) {
                    return;
                }
                AccountHelper.setToken2(data.resp.t2, data.resp.zpAt);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }


    public static void setTouristUserRole(int role) {
        SpManager.get().global().edit().putInt(Constants.SP_TOURIST_USER_ROLE, role).apply();
    }

    public static int getTouristUserRole() {
        return SpManager.get().global().getInt(Constants.SP_TOURIST_USER_ROLE, ROLE.GEEK.get());
    }


    public static String getLastPhone() {
        String lastPhone = AccountHelper.getLastPhone();
        if (TextUtils.isEmpty(lastPhone)) {
            return UserHelper.getPhone();
        }
        return lastPhone;
    }

    public static String getLastRegionCode() {
        String lastPhone = AccountHelper.getLastRegionCode();
        if (TextUtils.isEmpty(lastPhone)) {
            return UserHelper.getRegionCode();
        }
        return lastPhone;
    }


    public static class BindInfo {

        private final boolean bind; // true表示 已绑定
        private final String openId; //  OpenId
        private final String nickname; //  昵称

        public BindInfo(boolean bind, String openId, String nickname) {
            this.bind = bind;
            this.openId = openId;
            this.nickname = nickname;
        }

        public boolean isBind() {
            return bind;
        }

        public String getOpenId() {
            return openId;
        }


        public String getNickname() {
            return nickname;
        }

    }

    public static void setWeChatBindInfo(boolean bindWeiXin, String thirdUserId, String wxNickname) {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return;

        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return;

            bossInfo.bindWeiXin = bindWeiXin;
            bossInfo.thirdUserId = thirdUserId;
            bossInfo.wxNickname = wxNickname;
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return;

            geekInfo.bindWeiXin = bindWeiXin;
            geekInfo.thirdUserId = thirdUserId;
            geekInfo.wxNickname = wxNickname;
        }
    }

    public static void setQQBindInfo(boolean bind, String openId, String nickname) {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return;

        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return;

            bossInfo.bindQQ = bind;
            bossInfo.qqOpenId = openId;
            bossInfo.qqNickname = nickname;
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return;

            geekInfo.bindQQ = bind;
            geekInfo.qqOpenId = openId;
            geekInfo.qqNickname = nickname;
        }
    }

    public static BindInfo queryWechatBindInfo() {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return null;
        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return null;

            return new BindInfo(bossInfo.bindWeiXin, bossInfo.thirdUserId, bossInfo.wxNickname);
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return null;
            return new BindInfo(geekInfo.bindWeiXin, geekInfo.thirdUserId, geekInfo.wxNickname);
        }
    }

    public static BindInfo queryQQBindInfo() {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return null;
        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return null;

            return new BindInfo(bossInfo.bindQQ, bossInfo.qqOpenId, bossInfo.qqNickname);
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return null;
            return new BindInfo(geekInfo.bindQQ, geekInfo.qqOpenId, geekInfo.qqNickname);
        }
    }

    public static long getGeekRegisterDate() {
        UserBean loginUser = getLoginUser();
        if (isBossRole() || loginUser == null || loginUser.geekInfo == null) {
            return 0L;
        }

        return loginUser.geekInfo.registerDate;
    }

}
