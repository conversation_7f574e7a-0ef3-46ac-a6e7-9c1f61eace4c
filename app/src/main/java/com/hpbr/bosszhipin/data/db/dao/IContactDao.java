package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;

import java.util.List;

public interface IContactDao {


    /**
     * 添加或更新数据
     *
     * @param bean
     * @return
     */
    @SyncDataBase
    long insertOrUpdateAllField(ContactBean bean);

    //  ContactDataKernel 不调用
//    @SyncDataBase
    void upsertContactServerField(ContactBean bean, int myRole);

    /**
     * 更新联系人列表,将服务端的字段更新到本地
     * isNeedComplete 为本地字段，但是如果从服务端获取过信息后，此字段就需要更新为false
     *
     * @param bean
     */
    @Deprecated
    @SyncDataBase
    void insertOrUpdateServerField(ContactBean bean, int myRole);


    /**
     * 获取所有联系人数据
     *
     * @return
     */
    List<ContactBean> getAllContactList(int role);


    ContactBean findContact(long friendId, int friendSource, int myRole);

    @SyncDataBase
    long fixSendingContact();
    /**
     * 更新数据的固定列：最后聊天记录
     *
     * @param bean
     */
    @SyncDataBase
    void updateLastText(ContactBean bean);

    @SyncDataBase
    void updateDirectCallStatus(ContactBean bean);

    @SyncDataBase
    void updatePhone(ContactBean bean);


    @SyncDataBase
    void updateJumpToChatField(ContactBean bean);

    @SyncDataBase
    void updateSecurityId(ContactBean bean);

    @SyncDataBase
    void updateWeChat(ContactBean bean);

    @SyncDataBase
    void updateWxRemind(ContactBean bean);


    @SyncDataBase
    void updateIsFiltered(ContactBean bean);

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @SyncDataBase
    void updateExchangePhoneTime(ContactBean bean);

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @SyncDataBase
    void updateExchangeWechatTime(ContactBean bean);

    /**
     * 更新附件简历时间
     *
     * @param bean
     */
    @SyncDataBase
    void updateExchangeAnnexResumeTime(ContactBean bean);

    @SyncDataBase
    void updateUpdateTime(ContactBean bean);

    /**
     * 更新邀请页面时间
     *
     * @param bean
     */
    @SyncDataBase
    void updateInviteInterviewTime(ContactBean bean);

    /**
     * 更新面试状态
     *
     * @param bean
     */
    @SyncDataBase
    void updateInterviewStatus(ContactBean bean);

    @SyncDataBase
    void updateVideoInterview(ContactBean bean);

    /**
     * 更新聊天未读数据
     *
     * @param bean
     */
    @SyncDataBase
    void updateUnreadCount(ContactBean bean);

    /**
     * 更新联系人最后消息的发送状态
     *
     * @param bean 联系人实例
     */
    @SyncDataBase
    void updateLastTextStatus(ContactBean bean, long lastChatClientMessageId);

    /**
     * 更新联系人姓名和头像
     *
     * @param bean 联系人实例
     */
    @SyncDataBase
    void updateNameAndHeadUrl(ContactBean bean);

    /**
     * 更新草稿
     *
     * @param bean
     */
    @SyncDataBase
    void updateDraft(ContactBean bean);

    @SyncDataBase
    void updateLabel(ContactBean bean);

    @SyncDataBase
    void updateNote(ContactBean bean);

    @SyncDataBase
    void updateWarningTip(ContactBean bean);

    @SyncDataBase
    void updateFriendCompanies(List<ContactBean> bean);

    @SyncDataBase
    void updateTop(ContactBean bean);


    @SyncDataBase
    void updateIsNoDisturb(ContactBean bean);

    @SyncDataBase
    void updateInterviewSchedule(ContactBean bean);

    @SyncDataBase
    void updateSyncGeekInfo(ContactBean contactBean);

    @SyncDataBase
    void updateFriendStage(ContactBean contactBean);


    long getContactCount(int role);

    /**
     * 删除联系人
     *
     * @param bean
     */
    @SyncDataBase
    void deleteContact(ContactBean bean);

    //--------------------------getBaseInfoList使用start--------------------------
    /**
     * 保存联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    int updateContacts(List<ContactBean> list);
    /**
     * 更新联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    int updateBaseInfoContacts(List<ContactBean> list);

    @SyncDataBase
    int deleteContacts(List<ContactBean> list);
    @SyncDataBase
    void repairLastMid();
    /**
     * 检查重复联系人
     * 只处理老db，新db空实现
     */
    List<ContactBean> checkSameContacts();

    /**
     * 查询重复数据并且剔除originId
     */
    List<ContactBean> querySameContactsForBaseInfo(long friendId, int myRole,int friendSource,long originId);

    /**
     * 获取sqlite版本
     */
    String getDBVersion();

    //--------------------------getBaseInfoList使用end--------------------------

}
