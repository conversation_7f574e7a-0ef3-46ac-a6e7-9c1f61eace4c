package com.hpbr.bosszhipin.data.manager.contact;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactMessageTimeRangeManager;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.RejectDrawContactManager;
import com.hpbr.bosszhipin.module.workorder.LogDiagnoseManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.ApiCallbackSafeCallback;
import net.bosszhipin.api.ContactBaseInfoListResponse;
import net.bosszhipin.api.ContactBaseInfoRequest;
import net.bosszhipin.api.ContactFriendListV3Request;
import net.bosszhipin.api.ContactFriendListV3Response;

import java.util.ArrayList;
import java.util.List;

/**
 * Contact刷新策略 V3 版本
 * 主要改动：
 * 1. 使用 ContactFriendListV3Request 替代 ContactFriendListRequest
 * 2. 支持 waterLevel 字段，实现增量更新逻辑
 * 3. 优先更新有变化的联系人（本地 waterLevel < 服务端 waterLevel）
 */
public class DefaultRefreshStrategyV3 extends IRefreshStrategy {
    public static final String TAG = "DefaultRefreshV3";
    public static final String REFRESH_CONTACT_TIME_KEY = "com.hpbr.bosszhipin.InitContactDataService.REFRESH_CONTACT_TIME_KEY";
    public static final String REFRESH_CONTACT_VERSION_KEY = "com.hpbr.bosszhipin.InitContactDataService.VERSION.REFRESH_CONTACT_TIME_KEY";

    private final ContactSyncHandler handler;
    // 重试时间
    public static final int[] RETRY_TIME = {1, 2, 3, 4, 8, 16, 32};
    static HandlerThread handlerThread;
    private int identity;
    private long uid;

    static HandlerThread getHandlerThread() {
        if (handlerThread == null) {
            handlerThread = new HandlerThread("DefaultRefreshStrategyV3");
            handlerThread.start();
        }
        return handlerThread;
    }

    public DefaultRefreshStrategyV3() {
        handler = new ContactSyncHandler(getHandlerThread().getLooper());
    }

    private void refreshCurrentInfo() {
        uid = AccountHelper.getUid();
        identity = AccountHelper.getIdentity();
    }

    @Override
    public boolean isNeedRefresh() {
        if (isRefresing.get()) {
            return false;
        }
        //判断系统版本
        final long version = SpManager.get().global().getLong(REFRESH_CONTACT_VERSION_KEY, 0);
        if (BuildInfoUtils.getVersionCode() > version) {
            return true;
        }
        //判断上传更新时间
        final long lastTime = SpManager.get().global().getLong(REFRESH_CONTACT_TIME_KEY, 0);
        return ContactHandleManager.isOverTodayHour8(lastTime);
    }

    boolean isDiffAccount() {
        return !isRefreshing() || uid != AccountHelper.getUid() || identity != AccountHelper.getIdentity();
    }

    @Override
    public void doRefresh() {
        refreshCurrentInfo();
        TLog.info(TAG, "=========doRefresh====== %d %d %d", uid, identity, hashCode());
        isRefresing.set(true);
        ContactFriendListV3Request request = new ContactFriendListV3Request(new ApiCallbackSafeCallback<ContactFriendListV3Response>() {
            final long startTime = System.currentTimeMillis();
            final MonitorData monitorData = new MonitorData();

            @Override
            public void handleInChildThread(ApiData<ContactFriendListV3Response> data) {
                long requestTime = System.currentTimeMillis() - startTime;
                List<ContactBean> allContact = ContactManager.getInstance().getAllContact();
                TLog.debug(TAG, "   request friendList allContact = %d time= %d", LList.getCount(allContact), requestTime);
                ContactPageData contactPageData = handleContactPageData(data.resp, allContact);
                long handleDataTime = System.currentTimeMillis() - startTime - requestTime;
                monitorData.setBaseData(contactPageData);
                monitorData.addPrePageTime(requestTime, handleDataTime);
                TLog.debug(TAG, "   handleContactPageData time= %d", handleDataTime);

                //异步拉取联系人
                startContactSync(contactPageData, 0);

                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                        .put("status", "init")
                        .put("zpFriendId", LList.getCount(data.resp.zpFriendIdList))
                        .put("dzFriendId", LList.getCount(data.resp.dzFriendIdList))
                        .put("diff-zpFriendId", monitorData.getZpFriendCount())
                        .put("diff-dpFriendId", monitorData.getDzFriendCount())
                        .put("zpAddCount", monitorData.getZpAddCount())
                        .put("dzAddCount", monitorData.getDzAddCount())
                        .put("delCount", monitorData.getDelCount())
                        .info();

            }

            @Override
            public void onFailed(ErrorReason reason) {
                resetRefreshing();
                if (reason.getErrCode() > 0) {
                    DefaultRefreshStrategyV2 defaultRefreshStrategyV2 = new DefaultRefreshStrategyV2();
                    defaultRefreshStrategyV2.doRefresh();
                }
            }

            /**
             * 处理联系人分页数据，V3版本增加水位线比较逻辑
             * @param response
             * @param allContacts
             * @return
             */
            private ContactPageData handleContactPageData(ContactFriendListV3Response response, List<ContactBean> allContacts) {
                int zpNewAddCount = 0;
                long startTime = System.currentTimeMillis();
                ContactPageData contactPageData = new ContactPageData(allContacts, monitorData);
                List<Long> zpFriendIdList = null;
                List<Long> dpFriendIdList = null;
                if (response.zpFriendIdList != null) {
                    zpFriendIdList = new ArrayList<>();
                    for (ContactFriendListV3Response.FriendIdWithWaterLevel friendInfo : response.zpFriendIdList) {
                        zpFriendIdList.add(friendInfo.friendId);
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendInfo.friendId, identity, ContactBean.FROM_BOSS);
                        if (contactBean != null) {
                            TLog.debug(TAG, "diff waterLevel zpFriendIdList %s:%d %d:%d", contactBean.friendName, contactBean.friendId, contactBean.waterLevel, friendInfo.waterLevel);
                            // 比较水位线，如果本地水位线小于服务端水位线，则优先更新
                            if (contactBean.waterLevel == 0 || contactBean.waterLevel < friendInfo.waterLevel) {
                                contactPageData.zpFriendIdList.add(friendInfo.friendId);
                            }
                        } else {
                            zpNewAddCount++;
                            contactPageData.zpFriendIdList.add(0, friendInfo.friendId); // 新增联系人优先处理
                        }
                    }
                }
                monitorData.setZpAddCount(zpNewAddCount);
                TLog.debug(TAG, "handleContactPageData zpFriendIdList time= %d", System.currentTimeMillis() - startTime);

                int dzNewAddCount = 0;
                if (response.dzFriendIdList != null) {
                    dpFriendIdList = new ArrayList<>();
                    for (ContactFriendListV3Response.FriendIdWithWaterLevel friendInfo : response.dzFriendIdList) {
                        dpFriendIdList.add(friendInfo.friendId);
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendInfo.friendId, identity, ContactBean.FROM_DIAN_ZHANG);
                        if (contactBean != null) {
                            // 比较水位线，如果本地水位线小于服务端水位线，则优先更新
                            if (contactBean.waterLevel == 0 || contactBean.waterLevel < friendInfo.waterLevel) {
                                contactPageData.dzFriendIdList.add(friendInfo.friendId);
                            }
                        } else {
                            dzNewAddCount++;
                            contactPageData.dzFriendIdList.add(0, friendInfo.friendId); // 新增联系人优先处理
                        }
                    }
                }
                monitorData.setDzAddCount(dzNewAddCount);
                TLog.debug(TAG, "handleContactPageData dzFriendIdList time= %d", System.currentTimeMillis() - startTime);

                contactPageData.computeDelFriend(zpFriendIdList, dpFriendIdList);
                contactPageData.startIndex = 0;
                contactPageData.pageSize = contactPageData.computePageSize();
                TLog.debug(TAG, "handleContactPageData computeDelFriend time= %d", System.currentTimeMillis() - startTime);
                return contactPageData;
            }
        });
        request.execute();
        GroupManager.getInstance().loadAllGroupInfoFromServer(null);
        // 1226.80【B/C】聊天场景无用信息收纳功能
        refreshRejectFriendData();
    }

    private static void updateHandlerTime() {
        long curTime = System.currentTimeMillis();
        SpManager.get().global().edit().putLong(REFRESH_CONTACT_TIME_KEY, curTime).putLong(REFRESH_CONTACT_VERSION_KEY, BuildInfoUtils.getVersionCode()).apply();
    }


    @Override
    public void initRefreshTime() {
        resetRefreshing();
        handler.removeCallbacksAndMessages(null);
        SpManager.get().global().edit().remove(REFRESH_CONTACT_TIME_KEY).apply();
    }

    private void startContactSync(@NonNull ContactPageData contactPageData, long delayMillis) {
        handler.removeMessages(TASK);
        if (isDiffAccount()) {
            TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d refreshing = %b", contactPageData, AccountHelper.getUid(), AccountHelper.getIdentity(), hashCode(), isRefreshing());
            return;
        }
        handler.startSyncing(contactPageData);
        handler.sendMessageDelayed(Message.obtain(handler, TASK, contactPageData), delayMillis);
    }

    public static final int TASK = 1000;

    class ContactSyncHandler extends Handler {

        public ContactSyncHandler(Looper looper) {
            super(looper);
        }

        public synchronized void startSyncing(@NonNull ContactPageData contactPageData) {
            if (contactPageData.startIndex == 0 && contactPageData.source == ContactBean.FROM_BOSS) {
                ContactManager.getInstance().monitorContactSaveData(1);
            }
        }

        public synchronized void stopSyncing(@NonNull ContactPageData contactPageData) {
            handleDelContact(contactPageData.delFriends);//删除联系人
            ContactManager.getInstance().monitorContactSaveData(2);
            MonitorData monitor = contactPageData.monitorData;
            if (monitor != null) {
                monitor.stop();
                String jsonDetail = GsonUtils.toJson(monitor);

                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                        .put("status", "result")
                        .put("success", contactPageData.retryCount == 0)
                        .put("count", monitor.getCount())
                        .put("msgTime", String.valueOf(monitor.getMsgTime()))
                        .put("totalTime", String.valueOf(monitor.getTotalTime()))
                        .put("detail", jsonDetail).info();

                if (contactPageData.retryCount > 0 || monitor.getTotalTime() > 5000) {
                    ApmAnalyzer.create().action("action_contact", "base_info_list")
                            .p2(String.valueOf(contactPageData.retryCount == 0))
                            .p3(String.valueOf(monitor.getCount()))
                            .p4(String.valueOf(monitor.getMsgTime()))
                            .p5(String.valueOf(monitor.getTotalTime()))
                            .p6("v3")
                            .p8(jsonDetail).debug()
                            .report();
                }
            }
            resetRefreshing();
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.obj instanceof ContactPageData) {
                ContactPageData pageData = (ContactPageData) msg.obj;

                if (isDiffAccount()) {
                    TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <1> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV3.this.hashCode(), isRefreshing());
                    return;
                }

                final List<Long> friendIds = pageData.getFriendIdList();
                if (LList.isEmpty(friendIds)) {
                    TLog.error(TAG, "friendIds is null");
                    stopSyncing(pageData);
                    updateHandlerTime();
                    return;
                }
                TLog.info(TAG, "pull start contactPage = %s hashCode = %d", pageData, DefaultRefreshStrategyV3.this.hashCode());
                ContactBaseInfoRequest request = new ContactBaseInfoRequest(new ApiCallbackSafeCallback<ContactBaseInfoListResponse>() {
                    final MonitorData monitor = pageData.monitorData;
                    final long httpStartTime = System.currentTimeMillis();

                    @Override
                    public void handleInChildThread(ApiData<ContactBaseInfoListResponse> data) {
                        super.handleInChildThread(data);
                        long requestTime = System.currentTimeMillis() - httpStartTime;
                        LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                                .put("status", "start")
                                .put("contactPage", pageData)
                                .put("baseInfoList", LList.getCount(data.resp.baseInfoList))
                                .put("time", requestTime)
                                .info();
                        if (monitor != null) {
                            monitor.success(requestTime);
                        }
                        pageData.retryCount = 0;

                        if (data.resp != null) {
                            if (isDiffAccount(data.resp)) {
                                TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <2> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV3.this.hashCode(), isRefreshing());
                                return;
                            }
                            //保存30天聊天记录
                            ContactMessageTimeRangeManager.getInstance().saveContactMessageTimeRangeRecord(data.resp.timeRange);
                            ContactMessageTimeRangeManager.getInstance().saveContactFoldText(data.resp.foldText);
                            if (data.resp.baseInfoList != null) {
                                long start = System.currentTimeMillis();
                                int count = data.resp.baseInfoList.size();
                                if (isDiffAccount(data.resp)) {
                                    TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <3> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV3.this.hashCode(), isRefreshing());
                                    return;
                                }
                                ContactHandleManager contactHandleManager = new ContactHandleManager(data.resp);
                                contactHandleManager.insertAndUpdateCacheV2();
                                contactHandleManager.batchInsertAndUpdateContactsDaoV2();

                                if (monitor != null) {
                                    monitor.addPageTime(new MonitorData.MonitorTime(System.currentTimeMillis() - start, count));
                                }
                            }
                            if (isDiffAccount(data.resp)) {
                                TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <5> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV3.this.hashCode(), isRefreshing());
                                return;
                            }
                            if (pageData.hasMore()) {
                                startContactSync(pageData, 50);
                            } else {
                                stopSyncing(pageData);
                                updateHandlerTime();
                                LogDiagnoseManager.getInstance().saveContactListSp();
                            }
                        }
                    }

                    private boolean isDiffAccount(ContactBaseInfoListResponse resp) {
                        return !isRefreshing() || resp.userId != uid || resp.identity != identity;
                    }

                    @Override
                    public void onSuccess(ApiData<ContactBaseInfoListResponse> data) {
                        if (isDiffAccount(data.resp)) {
                            return;
                        }
                        ContactManager.getInstance().refreshContacts();
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (monitor != null) {
                            monitor.retry();
                        }
                        TLog.error(TAG, "pull contact error pageData = %s reason = %s ", pageData, reason);
                        if (pageData.retryCount < RETRY_TIME.length) {
                            startContactSync(pageData, RETRY_TIME[pageData.retryCount] * 1000L);
                            pageData.retryCount++;
                        } else {
                            stopSyncing(pageData);
                            TLog.error(TAG, "stop retry pull contact pageData = %s", pageData);
                        }
                    }
                });
                if (pageData.source == ContactBean.FROM_BOSS) {
                    request.friendIds = StringUtil.connectTextWithChar(",", friendIds, (StringUtil.IValueFunction<Long>) String::valueOf);
                } else {
                    request.dzFriendIds = StringUtil.connectTextWithChar(",", friendIds, (StringUtil.IValueFunction<Long>) String::valueOf);
                }
                request.execute();
            }
        }
    }

    private void refreshRejectFriendData() {
        RejectDrawContactManager.getInstance().requestDrawerData();
    }

    private void handleDelContact(List<ContactBean> delFriends) {
        if (LList.isNotEmpty(delFriends)) {
            for (ContactBean contactBean : delFriends) {
                ContactManager.getInstance().deleteContact(contactBean);
            }
        }
    }
} 