package com.hpbr.bosszhipin.data.manager.contact;

import android.text.TextUtils;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Created by wang<PERSON> on 16/11/2.
 */

public class ContactCache {
    private static final String TAG = "ContactCache";
    private static final ContactCache mInstance = new ContactCache();
    private Map<String, ContactBean> dataMaps = new HashMap<>();
    private StringBuilder keyStringPool = new StringBuilder();
    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private boolean hasLoad = false;
    private Runnable cleanDirtyData = new Runnable() {
        @Override
        public void run() {
            try {
                lock.writeLock().lock();
                Iterator<Map.Entry<String, ContactBean>> it = dataMaps.entrySet().iterator();
                L.debug("cache", "=====before cleanDirtyData======dataMaps:%d:%s", dataMaps.size(), UserManager.getUserRole());
                while (it.hasNext()) {
                    Map.Entry<String, ContactBean> entry = it.next();
                    if (!entry.getKey().startsWith(UserManager.getUID() + "_" + UserManager.getUserRole().get())) {
                        it.remove();
                    }
                }
                L.debug("cache", "=====after cleanDirtyData======dataMaps:%d:%s", dataMaps.size(), UserManager.getUserRole());
            } catch (Exception e) {
                L.e(TAG, "cleanDirtyData 异常", e);
            } finally {
                lock.writeLock().unlock();
            }
        }
    };

    private ContactCache() {
    }

    public void switchRole() {
        lock.writeLock().lock();
        hasLoad = false;
        lock.writeLock().unlock();
        load();
        App.get().getMainHandler().removeCallbacks(cleanDirtyData);
        App.get().getMainHandler().postDelayed(cleanDirtyData, 30 * 1000);
    }

    public void clean() {
        try {
            L.debug("cache", "===========clean====:%s", UserManager.getUserRole());
            lock.writeLock().lock();
            hasLoad = false;
            dataMaps.clear();
        } catch (Exception e) {
            L.e(TAG, "clean 异常", e);
        } finally {
            App.get().getMainHandler().removeCallbacks(cleanDirtyData);
            lock.writeLock().unlock();
        }
    }


    public static ContactCache getInstance() {
        return mInstance;
    }

    public static final int DEFAULT_FRIEND_SOURCE = ContactBean.FROM_BOSS;

    private void checkInitLoad() {
        try {
            lock.readLock().lock();
            if (hasLoad) return;
        } finally {
            lock.readLock().unlock();
        }
        load();
    }

    private String cacheKey;
    public void load() {
        if (TextUtils.isEmpty(UserManager.getToken())) {
            return;
        }
        String cacheKey = UserManager.getUID() + ":" + UserManager.getUserRole().get();
        LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_INFO).put("status","load").put("friends",dataMaps.size()).put("role",UserManager.getUserRole()).info();
        if (hasLoad) {
            if (!TextUtils.equals(cacheKey, this.cacheKey)) {
                ApmAnalyzer.create().action("action_contact_doctor", "type_cache_contact_diff").p3(cacheKey).debug().report();
            }
            L.debug("cache", "===========has load=====%s", UserManager.getUserRole());
            return;
        }

        try {

            lock.writeLock().lock();
            if (hasLoad) {
                boolean fixContactCache = AndroidDataStarGray.getInstance().fixContactCache();
                ApmAnalyzer.create().action("action_temp", "contactLoad").p2(String.valueOf(fixContactCache)).report();
                TLog.info("cache", "===========has load 2=====%s", UserManager.getUserRole());
                if (fixContactCache) {
                    return;
                }
            }
            dataMaps.clear();
            FriendStageManager.getInstance().resetRunning();
            List<ContactBean> mDatas = ContactManager.getInstance().getAllContactList(UserManager.getUserRole().get());
            StringBuilder stringBuilder = new StringBuilder();
            if (mDatas != null) {
                int size = mDatas.size();
                for (int i = 0; i < size; i++) {
                    ContactBean contactBean = mDatas.get(i);
                    if (contactBean == null) continue;
                    String key = makeKey(contactBean.friendId, keyStringPool, contactBean.friendSource);
                    ContactBean bean = mDatas.get(i);
                    ContactBean isDuplicate = dataMaps.put(key, bean);
                    if (isDuplicate != null) {
                        stringBuilder.append(contactBean.friendId).append(",");
                    }
                }
                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_INFO).put("status","result").put("friends",mDatas.size()).put("role",UserManager.getUserRole()).put("dbSize",dataMaps.size()).info();
            } else {
                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_INFO).put("status","result").put("friends",0).put("role",UserManager.getUserRole()).info();
            }
            this.cacheKey = cacheKey;
            hasLoad = true;
            if (LList.getCount(mDatas) != dataMaps.size()) {
                ApmAnalyzer.create().action("action_contact", "duplicate").p2(String.valueOf(LList.getCount(mDatas))).p3(String.valueOf(dataMaps.size())).p4(stringBuilder.toString()).debug().report();
            }
        } catch (Exception e) {
            TLog.error(TAG, e,"load 异常");
        } finally {
            lock.writeLock().unlock();
            ContactManager.getInstance().refreshContacts();
            //更新联系人里面双聊字段
            FriendStageManager.getInstance().checkSetFriendStage(getContactList(),false);
        }
    }

    public ContactBean queryContactByFriendId(long friendId, int myRole, int friendSource) {
        checkInitLoad();
        ContactBean bean = null;
        try {
            lock.readLock().lock();
            String key = makeKey(friendId, keyStringPool, myRole, friendSource);
            bean = dataMaps.get(key);
            return bean;
        } catch (Exception e) {
            L.e(TAG, "queryContactByFriendId 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return bean;
    }

    public List<ContactBean> getContactList() {
        return getContactList(0);
    }

    public List<ContactBean> getContactList(int extCount) {
        checkInitLoad();
        try {
            lock.readLock().lock();
            Collection<ContactBean> vCollection = dataMaps.values();
            List<ContactBean> list = new ArrayList<>(vCollection.size() + extCount);
            list.addAll(vCollection);
            return list;
        } catch (Exception e) {
            L.e(TAG, "getContactListWithoutUnfit 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return new ArrayList<>(0);
    }


    public void deleteContact(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            dataMaps.remove(key);
        } catch (Exception e) {
            L.e(TAG, "deleteContact 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /***
     * 不保留就数据
     **/
    public long insertOrUpdateAllField(ContactBean bean, int myRole) {
        if (bean == null) {
            return -1;
        }
        checkInitLoad();
//        L.debug("cache", "===before=insertOrUpdateAllField=======:%s", UserManager.getUserRole());
//        printMap();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, myRole, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                long id = bean.id;
                cache.cloneBean(bean);
                cache.id = id;
            } else {
                dataMaps.put(key, bean);
            }
            ContactManager.getInstance().notifyContactChange(bean);
            return 1;
        } catch (Exception e) {
            L.e(TAG, "insertOrUpdateAllField 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
        return -1;
    }

    /***
     * 保留部分旧数据
     **/
    public void insertOrUpdateServerField(ContactBean bean, int myRole) {
        if (bean == null) {
            return;
        }
        checkInitLoad();
//        L.debug("cache", "===before=insertOrUpdateServerField=======:%s", UserManager.getUserRole());
//        printMap();
        try {
            lock.writeLock().lock();
            if (myRole != bean.myRole) {
                TLog.error(TAG, "contact = %s myRole = %d", bean, myRole);
            }
            String key = makeKey(bean.friendId, keyStringPool, myRole, bean.friendSource);
            ContactBean cache = dataMaps.get(key);

            if (cache != null) {
                cache.myId = bean.myId;
                cache.jobId = bean.jobId;
                cache.jobIntentId = bean.jobIntentId;
                cache.bossCompanyName = bean.bossCompanyName;
                cache.isBlack = bean.isBlack;
                cache.friendId = bean.friendId;
                cache.highGeek = bean.highGeek;
                cache.friendPhone = bean.friendPhone;
                cache.regionCode = bean.regionCode;
                cache.friendWxNumber = bean.friendWxNumber;
                cache.myRole = bean.myRole;
                cache.geekPositionName = bean.geekPositionName;
                cache.friendName = bean.friendName;
                cache.age = bean.age;
                cache.gender = bean.gender;
                cache.friendDefaultAvatarIndex = bean.friendDefaultAvatarIndex;
                cache.friendDefaultAvatar = bean.friendDefaultAvatar;
                cache.isTop = bean.isTop;
                cache.updateTime = bean.updateTime;
                cache.isReject = bean.isReject;
                cache.rejectReason = bean.rejectReason;
                cache.expectPositionName = bean.expectPositionName;
                cache.bossJobPosition = bean.bossJobPosition;
                cache.jobSource = bean.jobSource;
                cache.exchangeResumeStatus = bean.exchangeResumeStatus;
                cache.exchangeResumeUrl = bean.exchangeResumeUrl;
                cache.userFromTitle = bean.userFromTitle;
                cache.certification = bean.certification;
                cache.currentInterviewStatus = bean.currentInterviewStatus;
                cache.currentInterviewDesc = bean.currentInterviewDesc;
                cache.isTechGeekBlock = bean.isTechGeekBlock;
                cache.chatMsgBlockHeadBar = bean.chatMsgBlockHeadBar;
                cache.currentInterviewProtocol = bean.currentInterviewProtocol;
                cache.friendCompanies = bean.friendCompanies;
                cache.isFiltered = bean.isFiltered;
                cache.filterReasonList = bean.filterReasonList;
                cache.friendSource = bean.friendSource;
                cache.itemSource = bean.itemSource;
                cache.isStar = bean.isStar;
                cache.starTypes = bean.starTypes;
                cache.blackDesc = bean.blackDesc;
                cache.workplace = bean.workplace;
                cache.positionName = bean.positionName;
                cache.salaryDesc = bean.salaryDesc;
                cache.lowSalary = bean.lowSalary;
                cache.highSalary = bean.highSalary;
                cache.isAgentRecruit = bean.isAgentRecruit;
                cache.goldGeekStatus = bean.goldGeekStatus;

                cache.securityId = bean.securityId;
                cache.itemType = bean.itemType;
                cache.messageExchangeIcon = bean.messageExchangeIcon;
                cache.greetingText = bean.greetingText;
                cache.noDisturb = bean.noDisturb;
                cache.isHunter = bean.isHunter;
                cache.isAgency = bean.isAgency;

                cache.addTime = bean.addTime;
                cache.delTime = bean.delTime;

                cache.isFreeze = bean.isFreeze;
                cache.isPreFreeze = bean.isPreFreeze;
                cache.freezeInfo = bean.freezeInfo;
                cache.preFreezeInfo = bean.preFreezeInfo;

                cache.interviewTimeStr = bean.interviewTimeStr;
                cache.interviewDateStr = bean.interviewDateStr;

                cache.invalidJob = bean.invalidJob;
                cache.workYear = bean.workYear;
                cache.workDesc = bean.workDesc;
                cache.degree = bean.degree;
                cache.expectSalary = bean.expectSalary;
                cache.goldInterviewer = bean.goldInterviewer;
                cache.warningTips = bean.warningTips;
                cache.friendType=bean.friendType;
                cache.jobTypeDesc = bean.jobTypeDesc;
                cache.jobCity = bean.jobCity;
                cache.positionMarkType = bean.positionMarkType;
                cache.aiDirectChat = bean.aiDirectChat;
                cache.waterLevel = bean.waterLevel;

                //只有getFullInfo接口返回数据,防止数据被冲掉
                if (!LText.empty(bean.note)) {
                    cache.note = bean.note;
                }

                //只有getFullInfo接口返回数据,防止数据被冲掉
                if (!LText.empty(bean.labels)) {
                    cache.labels = bean.labels;
                }
            } else {
                dataMaps.put(key, bean);
            }
            ContactManager.getInstance().notifyContactChange(bean);
        } catch (Exception e) {
            L.e(TAG, "insertOrUpdateServerField 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void updateExchangePhoneTip(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.hasShowExchangePhoneTip = bean.hasShowExchangePhoneTip;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateWxRemind(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.isWxRemind = bean.isWxRemind;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateSecurityId(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.securityId = bean.securityId;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateSecurityId 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateJumpToChatField(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.hasJumpToChat = bean.hasJumpToChat;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateJumpToChatField 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateSyncGeekInfo(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.workYear = bean.workYear;
                cache.workDesc = bean.workDesc;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateSyncGeekInfo 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateWarningTip(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.warningTips = bean.warningTips;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void updatePhone(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.friendPhone = bean.friendPhone;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateLabel(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.labels = bean.labels;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateNote(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.note = bean.note;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateFriendStage(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.fridendStage = bean.fridendStage;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }







    public void updateDirectCallStatus(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.directCallStatus = bean.directCallStatus;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void updateWeChat(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.friendWxNumber = bean.friendWxNumber;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateLastText(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.lastChatText = bean.lastChatText;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void updateIsFiltered(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.isFiltered = bean.isFiltered;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastText 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateExchangePhoneTime(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.exchangePhoneTime = bean.exchangePhoneTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateExchangePhoneTime 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateFriendCompanies(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.friendCompanies = bean.friendCompanies;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateContactCompany 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateExchangeWechatTime(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.exchangeWxNumberTime = bean.exchangeWxNumberTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateExchangeWechatTime 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateUpdateTime(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.updateTime = bean.updateTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateUpdateTimeTime 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateExchangeAnnexResumeTime(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.exchangeAnnexResumeTime = bean.exchangeAnnexResumeTime;
                cache.exchangeResumeStatus = bean.exchangeResumeStatus;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateExchangeAnnexResumeTime 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateIsTop(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.isTop = bean.isTop;
                cache.updateTime = bean.updateTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateIsTop 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateNoDisturb(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.noDisturb = bean.noDisturb;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateNoDisturb 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void updateInviteInterviewTime(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.exchangeInterviewTime = bean.exchangeInterviewTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateInviteInterviewTime 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateDraft(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.RoughDraft = bean.RoughDraft;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateDraft 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateInterviewSchedule(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.interviewScheduleStatus = bean.interviewScheduleStatus;
                cache.interviewScheduleProtocol = bean.interviewScheduleProtocol;
                cache.interviewScheduleTime = bean.interviewScheduleTime;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateDraft 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateInterviewStatus(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.currentInterviewStatus = bean.currentInterviewStatus;
                cache.currentInterviewDesc = bean.currentInterviewDesc;
                cache.currentInterviewProtocol = bean.currentInterviewProtocol;
                cache.createInterviewText = bean.createInterviewText;
                cache.createInterviewProtocol = bean.createInterviewProtocol;
                cache.createInterviewTimeout = bean.createInterviewTimeout;
                cache.messageExchangeIcon = bean.messageExchangeIcon;
                cache.interviewDateStr = bean.interviewDateStr;
                cache.interviewTimeStr = bean.interviewTimeStr;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateInterviewStatus 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateVideoInterview(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.videoInterviewF2Text = bean.videoInterviewF2Text;
                cache.videoInterviewChatTopText = bean.videoInterviewChatTopText;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateVideoInterview 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void updateLastTextStatus(ContactBean bean, long lastChatClientMessageId) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                if (bean.lastChatStatus > cache.lastChatStatus) {
                    // 当状态值
                    cache.lastChatStatus = bean.lastChatStatus;//
                }
                cache.lastChatClientMessageId = lastChatClientMessageId;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateLastTextStatus 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public ContactBean updateNameAndHeadUrl(long friendId, String name, String headUrl, int friendSource) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(friendId, keyStringPool, friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.friendName = name;
                cache.friendDefaultAvatar = headUrl;
                ContactManager.getInstance().notifyContactChange(cache);
            }
            return cache;
        } catch (Exception e) {
            L.e(TAG, "updateLastTextStatus 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
        return null;
    }

    public void initContactData(ContactBean bean) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            String key = makeKey(bean.friendId, keyStringPool, bean.friendSource);
            ContactBean cache = dataMaps.get(key);
            if (cache != null) {
                cache.noneReadCount = bean.noneReadCount;
                cache.switch1 = bean.switch1;
                ContactManager.getInstance().notifyContactChange(cache);
            }
        } catch (Exception e) {
            L.e(TAG, "updateUnreadCount 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    private synchronized String makeKey(long friendId, StringBuilder sb, int friendSource) {
        return makeKey(friendId, sb, UserManager.getUserRole().get(), friendSource);
    }

    private synchronized String makeKey(long friendId, StringBuilder sb, int role, int friendSource) {
        sb.setLength(0);
        sb.append(UserManager.getUID()).
                append("_").
                append(role).
                append("_").
                append(friendId).
                append("_").
                append(friendSource)
        ;
        return sb.toString();
    }


    public boolean isHasLoad() {
        return hasLoad;
    }
}
