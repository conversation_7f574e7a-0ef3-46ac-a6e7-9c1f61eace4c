package com.hpbr.bosszhipin.data.db.async;

/**
 * Created by wa<PERSON><PERSON> on 16/9/18.
 * 命名规则： 1、前两位代表表名 2、第三位代表增删查改类型（增：1，删：2，改：3，查：4） 3、后两位代表操作类型
 */
public interface DbOpType {


    /**
     * =========================contact统计表====================
     ***/
    int OP_CONTACT_NOTIFY_UI = 1024;
    int OP_CONTACT_INSERT_OR_UPDATE = 11101;
    int OP_CONTACT_INSERT_OR_UPDATE_V2 = 11102; //  OP_CONTACT_INSERT_OR_UPDATE 优化方案

    int OP_CONTACT_DELETE = 11201; //删除指定联系人

    int OP_CONTACT_UPDATE_BASE_INFO = 11300; //更新联系人
    int OP_CONTACT_UPDATE = 11301; //删除指定联系人

    int OP_CONTACT_UPDATE_LAST_TEXT = 11302; //删除指定联系人

    int OP_CONTACT_UPDATE_EXCHANGE_PHONE_TIME = 11303; //删除指定联系人

    int OP_CONTACT_UPDATE_EXCHANGE_WECHAT_TIME = 11304; //删除指定联系人

    int OP_CONTACT_UPDATE_EXCHANGE_ANNEX_RESUME_TIME = 11305; //删除指定联系人

    int OP_CONTACT_UPDATE_EXCHANGE_INTERVIEW_TIME = 11306; //删除指定联系人

    int OP_CONTACT_UPDATE_LAST_TEXT_STATUS = 11307; //删除指定联系人

    int OP_CONTACT_UPDATE_UNREAD_COUNT = 11308; //删除指定联系人

    int OP_CONTACT_UPDATE_INTERVIEW_STATUS = 11309; // 更新面试状态

    int OP_CONTACT_UPDATE_NAME_AND_HEAD_URL = 11310; // 更新面试状态

    int OP_CONTACT_UPDATE_IS_FILTERD = 11311; // 更新牛人是否被筛选

    int OP_CONTACT_UPDATE_DRAFT = 11312; // 更新草稿

    int OP_CONTACT_UPDATE_PHONE = 11314;

    int OP_CONTACT_UPDATE_PHONE_AUTH = 11315;

    int OP_CONTACT_UPDATE_RESUME_AUTH = 11316;

    int OP_CONTACT_UPDATE_VIDEO_INTERVIEW = 11317;

    int OP_CONTACT_UPDATE_WX_REMIND = 11318;

    int OP_CONTACT_UPDATE_HAS_JUMP_CHAT = 11319;

    int OP_CONTACT_UPDATE_PART_JOB_STRATEGY = 11321;

    int OP_CONTACT_UPDATE_SECURITY_ID = 11322;

    int OP_CONTACT_UPDATE_DIRECT_CALL_STATUS = 11323;

    int OP_CONTACT_UPDATE_WECHAT = 11324;
    //用于记录事务是否执行完成
    int OP_TRANSACTION_START = 11325;
    //用于记录事务是否执行完成
    int OP_TRANSACTION_END = 11326;

    int OP_CONTACT_UPDATE_LABEL = 11327;

    int OP_CONTACT_UPDATE_NOTE = 11328;

    /*更新「是否置顶」*/
    int OP_CONTACT_UPDATE_IS_TOP = 11329;
    /*更新「是否开启免打扰」*/
    int OP_CONTACT_UPDATE_IS_OPEN_NO_DISTURB = 11330;
    /*更新「updateTime」*/
    int OP_CONTACT_UPDATE_UPDATE_TIME = 11331;

    int OP_CONTACT_UPDATE_INTERVIEW_SCHEDULE = 11332;
    /*更新 friendCompanies */
    int OP_CONTACT_UPDATE_FRIEND_COMPANIES = 11333;
    int OP_CONTACT_UPDATE_SYNC_GEEK_INFO = 11334;
    //批量插入联系人
    int OP_BATCH_CONTACT_INSERT_CONTACTS= 11335;
    //批量更新联系人
    int OP_BATCH_CONTACT_UPDATE_CONTACTS= 11336;
    //批量删除联系人
    int OP_BATCH_CONTACT_DELETE_CONTACTS= 11337;
    //检查重复联系人并且保留第一个
    int OP_CONTACT_CHECK_SAME_DELETE_NOT_CACHE= 11338;
    //用于记录事务是否执行完成
    int OP_BATCH_CONTACT_NEW_TRANSACTION_START = 11339;
    //用于记录事务是否执行完成
    int OP_BATCH_CONTACT_NEW_TRANSACTION_END = 11340;

    int OP_CONTACT_UPDATE_WARNING_TIPS = 11341;
    int OP_BATCH_CONTACT_NEW_TRANSACTION_END_V2 = 11342;
    int OP_CONTACT_CHECK_SAME_DELETE_NOT_CACHE_V2= 11343;

    int OP_TRANSACTION_MONITOR = 19999;
    int OP_MONITOR_MESSAGE_HANDLE = 29999;
}
