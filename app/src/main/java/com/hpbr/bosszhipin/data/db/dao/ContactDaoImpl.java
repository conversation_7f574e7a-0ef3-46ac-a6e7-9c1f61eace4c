package com.hpbr.bosszhipin.data.db.dao;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.monch.lbase.orm.db.model.ColumnsValue;
import com.monch.lbase.orm.db.model.ConflictAlgorithm;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.bean.WarningTipsBean;

import java.util.ArrayList;
import java.util.List;

import message.handler.analysis.ChatAnalyzer;

/**
 * Created by wangtian on 16/11/1.
 * 包内可见
 */
class ContactDaoImpl implements IContactDao {
    public static final String TAG = "ContactDaoImpl";

    @Override
    public ContactBean findContact(long friendId, int friendSource, int myRole) {
        long uid = UserManager.getUID();
        if (uid < 0) {
            return null;
        }
        QueryBuilder qb = new QueryBuilder(ContactBean.class);
        String[] args = {String.valueOf(friendId), String.valueOf(uid), String.valueOf(myRole)};
        qb.where("friendId=? AND myId=? AND myRole= ?", args);
        List<ContactBean> list = App.get().db().query(qb);
        if (LList.getCount(list) > 1) {
            for (ContactBean contactBean : list) {
                if (contactBean.friendId == friendId && contactBean.friendSource == friendSource) {
                    return contactBean;
                }
            }
        }
        if (LList.isEmpty(list)) {//兜底查询
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, myRole, friendSource);
            if (contactBean != null) {
                ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "ContactDao").p2(String.valueOf(friendId)).report();
//                return contactBean;
            }
        }
        return LList.getElement(list, 0);
    }

    /**
     * 根据好友ID获取联系人
     *
     * @param friendId
     * @return
     */
    synchronized long queryIdByFriendId(long friendId, int friendSource, int myRole) {
        long uid = UserManager.getUID();
        if (friendId <= 0 || uid < 0) {
            TLog.error(TAG, "queryIdByFriendId friendId = %d uid = %d", friendId, uid);
        }
        if (uid < 0) { //退出登录了
            return -2;
        }
        long id = -1;
        try {
            QueryBuilder qb = new QueryBuilder(ContactBean.class);
            qb.columns(new String[]{"id","friendSource"});
            String[] args = {String.valueOf(friendId), String.valueOf(uid), String.valueOf(myRole)};
            qb.where("friendId=? AND myId=? AND myRole= ?", args);
            List<ContactBean> list = App.get().db().query(qb);
            if (LList.isNotEmpty(list)) {
                ContactBean contactBean = null;
                for (ContactBean temp : list) {
                    //取第一个联系人，其他删除
                    if (temp != null && temp.friendSource == friendSource) {
                        if (contactBean == null) {
                            contactBean = temp;
                            id = contactBean.id;
                        } else {
                            TLog.error(TAG, "remove Contact friendId = [%d] id=[%d]", friendId, temp.id);
                            App.get().db().delete(temp);
                            ApmAnalyzer.create().action("action_contact", "delRepeatFriend").p2(String.valueOf(friendId)).debug().report();
                        }
                    }
                }
            }
        } catch (Throwable e) {
            ApmAnalyzer.create().action("action_contact", "exception")
                    .p2("queryIdByFriendId")
                    .p3(Log.getStackTraceString(e)).debug().report();
            TLog.error(TAG, e, "queryIdByFriendId friendId = %d myRole = %d", friendId, myRole);
            id = -2;
        }
        return id;
    }

    /**
     * 添加或更新数据
     *
     * @param bean
     * @return
     */
    @Override
    public long insertOrUpdateAllField(ContactBean bean) {
        if (bean == null) return -1;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, bean.myRole);
        if (id == -2) {  //-2 表示 被退出登录了或者异常了
            TLog.error(TAG, "insertOrUpdateAllField ignore bean = %s", bean);
            return 0;
        }

        if (id > 0) {
            bean.id = id;
        }
        if (bean.myId != AccountHelper.getUid() || bean.myRole != AccountHelper.getIdentity()) {
            TLog.error(TAG, "insertOrUpdateAllField diff friendId = %d userId = %d:%d role = %d:%d", bean.friendId, bean.myId, AccountHelper.getUid(), bean.myRole, AccountHelper.getIdentity());
        }
        return App.get().db().save(bean);
    }

    @Override
    public void upsertContactServerField(ContactBean bean, int myRole) {
        insertOrUpdateServerField(bean, myRole);
    }


    /**
     * 更新联系人列表,将服务端的字段更新到本地
     * isNeedComplete 为本地字段，但是如果从服务端获取过信息后，此字段就需要更新为false
     *
     * @param bean
     */
    @Override
    public void insertOrUpdateServerField(ContactBean bean, int myRole) {
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, myRole);
        if (id == -2) {  //-2 表示 被退出登录了或者异常了
            TLog.error(TAG, "insertOrUpdateServerField ignore bean = %s", bean);
            return;
        }
        if (id > 0) {
            if (bean.id > 0 && bean.id != id) {
                TLog.error(TAG, "insertOrUpdateServerField diff query#id = %d bean#id = %d", id, bean.id);
            }
            //注意 这个方法  必须 key和objects 需要 一一对应,入库时候需要校验 ,否则入库失败
            bean.id = id;
            //注意  这个位置 keys 和 objects必须 一一对应,否则 入库失败
            updateServerField(bean);
        } else {
            if (bean.myId != AccountHelper.getUid() || bean.myRole != AccountHelper.getIdentity()) {
                TLog.error(TAG, "insertOrUpdateServerField diff userId = %d:%d role = %d:%d", bean.myId, AccountHelper.getUid(), bean.myRole, AccountHelper.getIdentity());
            }
            App.get().db().save(bean);
        }
    }


    /**
     * 获取所有联系人数据
     *
     * @return
     */
    @Override
    public List<ContactBean> getAllContactList(int role) {
        QueryBuilder qb = new QueryBuilder(ContactBean.class);
        qb.where("myId=" + UserManager.getUID() + " AND myRole=" + role, null);
        return App.get().db().query(qb);
    }

    @Override
    public long fixSendingContact() {
        try {
            SQLiteDatabase database = App.get().db().getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put("lastChatStatus", "2");
            return database.update("Contact", values, " lastChatStatus =?", new String[]{"0"});
        } catch (Exception e) {
            ApmAnalyzer.create().action("action_contact", "exception")
                    .p2("fixSendingContact")
                    .p3(Log.getStackTraceString(e)).debug().report();
        }
        return 0;
    }

    /**
     * 更新数据的固定列：最后聊天记录
     *
     * @param bean
     */
    @Override
    public void updateLastText(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"lastChatText", "lastChatStatus"};
            Object[] objects = new Object[]{bean.lastChatText, bean.lastChatStatus};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateDirectCallStatus(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"directCallStatus"};
            Object[] objects = new Object[]{bean.directCallStatus};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }


    @Override
    public void updatePhone(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"friendPhone"};
            Object[] objects = new Object[]{bean.friendPhone};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }


    @Override
    public void updateJumpToChatField(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"hasJumpToChat"};
            Object[] objects = new Object[]{bean.hasJumpToChat};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateWxRemind(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"isWxRemind"};
            Object[] objects = new Object[]{bean.isWxRemind};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateSecurityId(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"securityId"};
            Object[] objects = new Object[]{bean.securityId};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }


    @Override
    public void updateWeChat(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id >= 0) {
            bean.id = id;
            String[] keys = new String[]{"friendWxNumber"};
            Object[] objects = new Object[]{bean.friendWxNumber};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }


    @Override
    public void updateIsFiltered(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"isFiltered"};
            Object[] objects = new Object[]{bean.isFiltered};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @Override
    public void updateExchangePhoneTime(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"exchangePhoneTime"};
            Object[] objects = new Object[]{bean.exchangePhoneTime};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @Override
    public void updateExchangeWechatTime(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"exchangeWxNumberTime"};
            Object[] objects = new Object[]{bean.exchangeWxNumberTime};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新附件简历时间
     *
     * @param bean
     */
    @Override
    public void updateExchangeAnnexResumeTime(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"exchangeAnnexResumeTime", "exchangeResumeStatus"};
            Object[] objects = new Object[]{bean.exchangeAnnexResumeTime, bean.exchangeResumeStatus};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新updateTime
     *
     * @param bean
     */
    @Override
    public void updateUpdateTime(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"updateTime"};
            Object[] objects = new Object[]{bean.updateTime};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新邀请页面时间
     *
     * @param bean
     */
    @Override
    public void updateInviteInterviewTime(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"exchangeInterviewTime"};
            Object[] objects = new Object[]{bean.exchangeInterviewTime};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新面试状态
     *
     * @param bean
     */
    @Override
    public void updateInterviewStatus(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"currentInterviewStatus",
                    "currentInterviewDesc", "currentInterviewProtocol",
                    "createInterviewText", "createInterviewProtocol",
                    "createInterviewTimeout", "messageExchangeIcon",
                    "interviewDateStr", "interviewTimeStr"};
            Object[] objects = new Object[]{bean.currentInterviewStatus,
                    bean.currentInterviewDesc, bean.currentInterviewProtocol,
                    bean.createInterviewText, bean.createInterviewProtocol,
                    bean.createInterviewTimeout, bean.messageExchangeIcon,
                    bean.interviewDateStr, bean.interviewTimeStr
            };
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateVideoInterview(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"videoInterviewChatTopText", "videoInterviewF2Text"};
            Object[] objects = new Object[]{bean.videoInterviewChatTopText,
                    bean.videoInterviewF2Text};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新聊天未读数据
     *
     * @param bean
     */
    @Override
    public void updateUnreadCount(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"noneReadCount", "switch1"};
            Object[] objects = new Object[]{bean.noneReadCount, bean.switch1};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新联系人最后消息的发送状态
     *
     * @param bean 联系人实例
     */
    @Override
    public void updateLastTextStatus(ContactBean bean, long lastChatClientMessageId) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"lastChatStatus", "lastChatClientMessageId"};
            Object[] objects = new Object[]{bean.lastChatStatus, lastChatClientMessageId};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新联系人姓名和头像
     *
     * @param bean 联系人实例
     */
    @Override
    public void updateNameAndHeadUrl(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"friendName", "friendDefaultAvatar"};
            Object[] objects = new Object[]{bean.friendName, bean.friendDefaultAvatar};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    /**
     * 更新草稿
     *
     * @param bean
     */
    @Override
    public void updateDraft(ContactBean bean) {
        String[] keys = new String[]{"RoughDraft"};
        Object[] objects = new Object[]{bean.RoughDraft};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        App.get().db().update(bean, cv, ConflictAlgorithm.None);
    }

    @Override
    public void updateLabel(ContactBean bean) {
        String[] keys = new String[]{"labels"};
        Object[] objects = new Object[]{bean.labels};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        App.get().db().update(bean, cv, ConflictAlgorithm.None);
    }

    @Override
    public void updateNote(ContactBean bean) {
        String[] keys = new String[]{"note"};
        Object[] objects = new Object[]{bean.note};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        App.get().db().update(bean, cv, ConflictAlgorithm.None);
    }

    @Override
    public void updateWarningTip(ContactBean bean) {
        //https://bugly.qq.com/v2/crash-reporting/errors/65d92ea8a7/25917536?pid=1
        List<WarningTipsBean> warningTips = bean.warningTips;
        if (warningTips != null) {
            String[] keys = new String[]{"warningTips"};
            Object[] objects = new Object[]{new ArrayList<>(warningTips)};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateFriendCompanies(List<ContactBean> contactBeans) {
        for (ContactBean contactBean : contactBeans) {
            String[] keys = new String[]{"friendCompanies"};
            Object[] objects = new Object[]{contactBean.friendCompanies};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(contactBean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateTop(ContactBean bean) {
        if (bean == null) return;
        String[] keys = new String[]{"isTop", "updateTime"};
        Object[] objects = new Object[]{bean.isTop, bean.updateTime};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        App.get().db().update(bean, cv, ConflictAlgorithm.None);
    }

    @Override
    public void updateIsNoDisturb(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"noDisturb"};
            Object[] objects = new Object[]{bean.noDisturb};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateInterviewSchedule(ContactBean bean) {
        if (bean == null) return;
        long id = queryIdByFriendId(bean.friendId, bean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            bean.id = id;
            String[] keys = new String[]{"interviewScheduleStatus",
                    "interviewScheduleProtocol",
                    "interviewScheduleTime"};
            Object[] objects = new Object[]{bean.interviewScheduleStatus,
                    bean.interviewScheduleProtocol,
                    bean.interviewScheduleTime};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(bean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateSyncGeekInfo(ContactBean contactBean) {
        if (contactBean == null) return;
        long id = queryIdByFriendId(contactBean.friendId, contactBean.friendSource, UserManager.getUserRole().get());
        if (id > 0) {
            contactBean.id = id;
            String[] keys = new String[]{"workYear", "workDesc"};
            Object[] objects = new Object[]{contactBean.workYear, contactBean.workDesc};
            ColumnsValue cv = new ColumnsValue(keys, objects);
            App.get().db().update(contactBean, cv, ConflictAlgorithm.None);
        }
    }

    @Override
    public void updateFriendStage(ContactBean contactBean) {
        String[] keys = new String[]{"fridendStage"};
        Object[] objects = new Object[]{contactBean.fridendStage};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        App.get().db().update(contactBean, cv, ConflictAlgorithm.None);
    }

    @Override
    public long getContactCount(int role) {
        QueryBuilder qb = new QueryBuilder(ContactBean.class);
        qb.where("myId=" + UserManager.getUID() + " AND myRole=" + role, null);
        return App.get().db().queryCount(qb);
    }


    /**
     * //和IOS统一,用户显示新招呼
     *
     * @param toUser
     * @param contact
     * @param body
     */
    private void receiveFriendGreetingMsg(ChatUserBean toUser, ContactBean contact, ChatMessageBodyBean body) {
        if (toUser.id == contact.myId
                && body != null
                && ((body.type == 1 && body.templateId == 1)
                || body.type == 2 || body.type == 3
                || body.type == 20 || body.type == 27 ||
                (body.type == 16
                        && body.templateId == 202))) {
            contact.receiveFriendGreetMsg = true;
            contact.isRejectExchange = false;
        }
    }

    /**
     * 删除联系人
     *
     * @param bean
     */
    @Override
    public void deleteContact(ContactBean bean) {
        App.get().db().delete(bean);
    }

    @Override
    public String getDBVersion() {
        final String SQL = "SELECT sqlite_version()";
        SQLiteDatabase database = App.get().db().getReadableDatabase();
        Cursor cursor = null;
        try {
            cursor = database.rawQuery(SQL, new String[]{});
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                return cursor.getString(0);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "getDBVersion error.");
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (Exception e) {
                    TLog.error(TAG, e, "getDBVersion finally error.");
                }
            }
        }
        return "";
    }

    /**
     * 更新联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    @Override
    public int updateBaseInfoContacts(List<ContactBean> list) {
        SQLiteDatabase database = null;
        try {
            database = App.get().db().getWritableDatabase();
            database.beginTransaction();
            int totalCount = 0;
            for (ContactBean bean : list) {
                int i = updateServerField(bean);
                if (i > 0) {
                    totalCount = totalCount + i;
                }
            }
            database.setTransactionSuccessful();
            return totalCount;
        } catch (Exception e) {
            ApmAnalyzer.create().action("action_contact", "exception")
                    .p2("updateBaseInfoContacts")
                    .p3(Log.getStackTraceString(e)).debug().report();
            ChatAnalyzer.reportNew(ChatAnalyzer.BASE_INFO_CRASH).p2("update_baseInfo_contacts").p3(e.getMessage()).report();
        } finally {
            if (null != database && database.inTransaction()) {
                //error1: cannot commit - no transaction is active 「https://blog.csdn.net/mynicedream/article/details/2284258」
                //error2: attempt to re-open an already-closed object: SQLiteDatabase 「https://www.xujun.org/note-13204.html」
                //error3: database or disk is full 「https://github.com/googlesamples/androidtv-sample-inputs/issues/32」
                database.endTransaction();
            }
        }
        return 0;
    }

    /**
     * 更新联系人列表,将服务端的字段更新到本地
     * isNeedComplete 为本地字段，但是如果从服务端获取过信息后，此字段就需要更新为false
     *
     * @param bean
     */

    private int updateServerField(ContactBean bean) {
        //注意  这个位置 keys 和 objects必须 一一对应,否则 入库失败
        String[] keys = new String[]{
                "myId", "jobId", "jobIntentId", "bossCompanyName",
                "isBlack", "friendId", "friendPhone", "regionCode", "friendWxNumber",
                "myRole", "geekPositionName", "friendName", "friendDefaultAvatarIndex", "age", "gender",
                "friendDefaultAvatar", "isTop", "updateTime", "isFreeze",
                "isReject", "rejectReason", "expectPositionName", "bossJobPosition",
                "userFromTitle", "currentInterviewStatus", "currentInterviewDesc", "currentInterviewProtocol",
                "securityId", "isNeedComplete", "lastRefreshTime", "friendCompanies",
                "friendSource", "isFiltered", "blackDesc", "positionName",
                "salaryDesc", "workplace", "isStar", "highGeek",
                "isWxRemind", "videoResumeUrl", "itemType",
                "isAgentRecruit", "goldGeekStatus", "greetingText", "jobSource", "exchangeResumeStatus", "exchangeResumeUrl",
                "starTypes", "note", "labels", "noDisturb",
                "isHunter", "isAgency", "addTime", "delTime",
                "freezeInfo", "preFreezeInfo", "isPreFreeze", "teamMemberSize", "itemSource", "lowSalary", "highSalary",
                "interviewTimeStr", "interviewDateStr", "invalidJob", "workYear", "degree", "expectSalary",
                "goldInterviewer", "warningTips", "friendType", "jobTypeDesc", "jobCity", "positionMarkType", "aiDirectChat", "waterLevel"};

        //注意 这个方法  必须 key和objects 需要 一一对应,入库时候需要校验 ,否则入库失败
        Object[] objects = new Object[]{
                bean.myId, bean.jobId, bean.jobIntentId, bean.bossCompanyName,
                bean.isBlack, bean.friendId, bean.friendPhone, bean.regionCode, bean.friendWxNumber,
                bean.myRole, bean.geekPositionName, bean.friendName, bean.friendDefaultAvatarIndex, bean.age, bean.gender,
                bean.friendDefaultAvatar, bean.isTop, bean.updateTime, bean.isFreeze,
                bean.isReject, bean.rejectReason, bean.expectPositionName, bean.bossJobPosition,
                bean.userFromTitle, bean.currentInterviewStatus, bean.currentInterviewDesc, bean.currentInterviewProtocol,
                bean.securityId, bean.isNeedComplete, bean.lastRefreshTime, bean.friendCompanies,
                bean.friendSource, bean.isFiltered, bean.blackDesc, bean.positionName,
                bean.salaryDesc, bean.workplace, bean.isStar, bean.highGeek,
                bean.isWxRemind, bean.videoResumeUrl, bean.itemType,
                bean.isAgentRecruit, bean.goldGeekStatus, bean.greetingText, bean.jobSource, bean.exchangeResumeStatus, bean.exchangeResumeUrl,
                bean.starTypes, bean.note, bean.labels, bean.noDisturb,
                bean.isHunter, bean.isAgency, bean.addTime, bean.delTime,
                bean.freezeInfo, bean.preFreezeInfo, bean.isPreFreeze, bean.teamMemberSize, bean.itemSource, bean.lowSalary, bean.highSalary,
                bean.interviewTimeStr, bean.interviewDateStr, bean.invalidJob, bean.workYear, bean.degree, bean.expectSalary,
                bean.goldInterviewer, bean.warningTips, bean.friendType, bean.jobTypeDesc, bean.jobCity, bean.positionMarkType, bean.aiDirectChat, bean.waterLevel};
        ColumnsValue cv = new ColumnsValue(keys, objects);
        return App.get().db().update(bean, cv, ConflictAlgorithm.None);
    }

    /**
     * 保存联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    @Override
    public int updateContacts(List<ContactBean> list) {
        return App.get().db().save(list);
    }

    @Override
    public int deleteContacts(List<ContactBean> list) {
        return App.get().db().delete(list);
    }

    @Override
    public void repairLastMid() {
        SQLiteDatabase database = null;
        try {
            database = App.get().db().getWritableDatabase();
            database.beginTransaction();
            database.execSQL("UPDATE Contact SET lastMsgId = 0");
            database.setTransactionSuccessful();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != database) {
                database.endTransaction();
            }
        }
    }

    /**
     * 检查重复联系人
     */
    @SuppressLint("Range")
    @Override
    public List<ContactBean> checkSameContacts() {
        List<ContactBean> arrayList = new ArrayList<>();
        Cursor cursor = null;
        try {
            SQLiteDatabase db = App.get().db().getReadableDatabase();
            cursor = db.rawQuery(

                    "select friendId, id, friendSource,myRole ,myId " +

                            "from Contact as c " + "where c.myId= ? " +

                            "group by c.myId, c.myRole,c.friendSource,c.friendId having count (*) > 1 ", new String[]{String.valueOf(UserManager.getUID())}

            );

            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                do {
                    long friendId = cursor.getLong(cursor.getColumnIndex("friendId"));
                    long id = cursor.getLong(cursor.getColumnIndex("id"));
                    int friendSource = cursor.getInt(cursor.getColumnIndex("friendSource"));

                    ContactBean contactBean = new ContactBean();
                    contactBean.id = id;
                    contactBean.friendId = friendId;
                    contactBean.friendSource = friendSource;
                    contactBean.myRole = cursor.getInt(cursor.getColumnIndex("myRole"));
                    contactBean.myId = cursor.getLong(cursor.getColumnIndex("myId"));

                    arrayList.add(contactBean);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
            ApmAnalyzer.create().action("action_contact", "exception")
                    .p2("checkSameContacts")
                    .p3(Log.getStackTraceString(e)).debug().report();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return arrayList;
    }

    /**
     * 查询重复数据并且剔除originId
     */
    @Override
    public List<ContactBean> querySameContactsForBaseInfo(long friendId, int myRole, int friendSource, long originId) {
        List<ContactBean> list = new ArrayList<>();
        try {
            QueryBuilder qb = new QueryBuilder(ContactBean.class);
            qb.columns(new String[]{"id", "friendId", "myId", "myRole", "friendSource"});
            String[] args = {String.valueOf(friendId), String.valueOf(UserManager.getUID()), String.valueOf(myRole), String.valueOf(friendSource), String.valueOf(originId)};
            qb.where("friendId=? AND myId=? AND myRole=? AND friendSource=? AND id<>?", args);
            list = App.get().db().query(qb);
        } catch (Throwable e) {
            L.d(TAG, "check same sql catch==" + e.getMessage());
            ApmAnalyzer.create().action("action_contact", "exception")
                    .p2("querySameContactsForBaseInfo")
                    .p3(Log.getStackTraceString(e)).debug().report();
        }
        return list;
    }
}
