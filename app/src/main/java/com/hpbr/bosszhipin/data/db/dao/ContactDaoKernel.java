package com.hpbr.bosszhipin.data.db.dao;


import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.chat.db.ChatDatabaseHelper;
import com.bszp.kernel.chat.db.RoomConverters;
import com.bszp.kernel.chat.db.entity.ContactCompanies;
import com.bszp.kernel.chat.db.entity.ContactData;
import com.bszp.kernel.chat.db.entity.ContactEntity;
import com.bszp.kernel.chat.db.entity.ContactInterview;
import com.bszp.kernel.chat.db.entity.ContactLocalEntity;
import com.bszp.kernel.chat.service.ChatHelper;
import com.bszp.kernel.chat.utils.CursorUtil;
import com.bszp.kernel.utils.WCDBCheckRunnable;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class ContactDaoKernel implements IContactDao {

    public static final String TAG = "ContactDaoKernel";

    com.bszp.kernel.chat.db.ContactDao getContactDao() {
        return ChatHelper.getContactDao();
    }

    com.bszp.kernel.chat.db.LocalDataDao getLocalDataDao() {
        return ChatHelper.getLocalDataDao();
    }

    /**
     * 检查身份异常
     *
     * @param contactBean
     * @return
     */
    private boolean checkContactValidity(ContactBean contactBean) {
        if (contactBean == null || contactBean.friendId < 0) return false;
        if (contactBean.myRole != AccountHelper.getIdentity()) {
            reportLog(-1, "insertOrUpdateAllField 身份异常 friendId = %d ,role = %d", contactBean.friendId, contactBean.myRole);
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "role")
                    .p2(String.valueOf(contactBean))
                    .report();
            return false;
        }
        return true;
    }

    @Override
    public ContactBean findContact(long friendId, int friendSource, int myRole) {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            return ContactTransformUtils.toContactBean(contactDao.queryContactDataByFriendId(friendId, friendSource));
//            return toContactBean(contactDao.queryByFriendId(friendId, friendSource));
        }
        return null;
    }

    @Override
    public long fixSendingContact() {
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            return localDataDao.fixSendingContact();
        }
        return 0;
    }

    /**
     * 添加或更新数据
     *
     * @param bean
     * @return
     */
    @Override
    public long insertOrUpdateAllField(ContactBean bean) {
        if (!checkContactValidity(bean)) return -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            long returnId = contactDao.upsert(ContactTransformUtils.toContactEntity(bean));

            long returnLocalId = -1;
            com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
            if (localDataDao != null) {
                returnLocalId = localDataDao.upsert(ContactTransformUtils.toContactLocalEntity(bean));
            }
            reportLog(returnId, "insertOrUpdateAllField friendId = %d ,returnLocalId = %d ", bean.friendId, returnLocalId);
            return returnId;
        }
        return -1;
    }


    public void upsertContactLocal(List<ContactLocalEntity> entityList) {
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            localDataDao.replace(entityList);
        }
    }
    @Override
    public void upsertContactServerField(ContactBean bean, int myRole) {
        //ignore
    }

    /**
     * 更新联系人列表,将服务端的字段更新到本地
     * isNeedComplete 为本地字段，但是如果从服务端获取过信息后，此字段就需要更新为false
     *
     * @param bean
     */
    @Override
    public void insertOrUpdateServerField(ContactBean bean, int myRole) {
        ChatDatabaseHelper.runInTransaction(new WCDBCheckRunnable("insertOrUpdateServerField"){
            @Override
            protected void execute() {
                if (!checkContactValidity(bean)) return;
                long returnId = -1;
                com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
                if (contactDao != null) {
                    returnId = contactDao.upsert(ContactTransformUtils.toContactEntity(bean));
                }
                // TODO feature 更新 lastRefreshTime 应该是没有必要的 ContactDaoImpl.insertOrUpdateServerField
                replaceContactLocalService(bean);
                reportLog(returnId, "insertOrUpdateServerField friendId = %d", bean.friendId);
            }
        });
    }

    //TODO 后续优化这块，不能影响网络数据插入
    private void replaceContactLocalService(ContactBean bean) {
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            ContactLocalEntity localEntity = localDataDao.query(bean.friendId, bean.friendSource);
            if (localEntity == null) {
                localEntity = new ContactLocalEntity(bean.friendId, bean.friendSource);
            }
            localEntity.friendCompanies = bean.friendCompanies;
            ContactLocalEntity.LocalField localField = localEntity.getLocalField();
            if (localField == null) {
                localField = new ContactLocalEntity.LocalField();
            }
            localField.lastRefreshTime = bean.lastRefreshTime;
            localDataDao.replace(localEntity.toDataBase());
        }
    }

    /**
     * 获取所有联系人数据
     *
     * @return
     */
    @Override
    public List<ContactBean> getAllContactList(int role) {
        long startTime = System.currentTimeMillis();
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            List<ContactData> contactDataList = contactDao.queryContactDataList();
            TLog.info(TAG, "getAllContactList count = %d time = %d", LList.getCount(contactDataList), System.currentTimeMillis() - startTime);
            List<ContactBean> entities = new ArrayList<>(contactDataList.size());
            for (ContactData contactData : contactDataList) {
                entities.add(ContactTransformUtils.toContactBean(contactData));
            }
            return entities;
//            return toContactBeans(contactDao.queryDataList());
        }
        return new ArrayList<>();
    }

    /**
     * 更新数据的固定列：最后聊天记录
     *
     * @param bean
     */
    @Override
    public void updateLastText(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateSummary(bean.friendId, bean.friendSource, bean.lastChatText);
        }
        reportLog(returnId, "updateLastText friendId = %d ,lastChatText = %s ", bean.friendId, bean.lastChatText);

    }

    @Override
    public void updateDirectCallStatus(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_DIRECT_CALL_STATUS, bean.directCallStatus);
        }
        reportLog(returnId, "updateDirectCallStatus friendId = %d ,directCallStatus = %d ", bean.friendId, bean.directCallStatus);

    }

    @Override
    public void updatePhone(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateFriendPhone(bean.friendId, bean.friendSource, bean.friendPhone);
        }
        reportLog(returnId, "updatePhone friendId = %d ,friendPhone = %s ", bean.friendId, bean.friendPhone);

    }

    @Override
    public void updateJumpToChatField(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_HAS_JUMP_TO_CHAT, bean.hasJumpToChat);
        }
        reportLog(returnId, "updateJumpToChatField  friendId = %d ,hasJumpToChat = %b ", bean.friendId, bean.hasJumpToChat);
    }

    @Override
    public void updateSecurityId(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateSecurityId(bean.friendId, bean.friendSource, bean.securityId);
        }
        reportLog(returnId, "updateSecurityId friendId = %d ,securityId = %s", bean.friendId, bean.securityId);
    }

    @Override
    public void updateWeChat(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateWeChat(bean.friendId, bean.friendSource, bean.friendWxNumber);
        }
        reportLog(returnId, "updateWeChat friendId = %d ,friendWxNumber = %s", bean.friendId, bean.friendWxNumber);
    }

    @Override
    public void updateWxRemind(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateWxRemind(bean.friendId, bean.friendSource, bean.isWxRemind);
        }
        reportLog(returnId, "updateWxRemind friendId = %d ,isWxRemind = %b ", bean.friendId, bean.isWxRemind);
    }


    private void reportLog(long returnId, String format, Object... args) {
        if (returnId < 1) {
            final String formatStr = String.format(format, args);
            TLog.error(TAG, "%s returnId = %d", formatStr, returnId);
        } else {
            final String formatStr = String.format(format, args);
            TLog.debug(TAG, "%s returnId = %d", formatStr, returnId);
        }
    }

    @Override
    public void updateIsFiltered(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateFiltered(bean.friendId, bean.friendSource, bean.isFiltered);
        }
        reportLog(returnId, "updateFiltered friendId = %d ,isFiltered = %b ", bean.friendId, bean.isFiltered);

    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @Override
    public void updateExchangePhoneTime(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_EXCHANGE_PHONE_TIME, bean.exchangePhoneTime);
        }
        reportLog(returnId, "updateExchangePhoneTim friendId = %d ,exchangePhoneTime = %d ", bean.friendId, bean.exchangePhoneTime);

    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    @Override
    public void updateExchangeWechatTime(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_EXCHANGE_WX_NUMBER_TIME, bean.exchangeWxNumberTime);
        }
        reportLog(returnId, "updateExchangeWechatTime friendId = %d ,exchangeWxNumberTime = %d ", bean.friendId, bean.exchangeWxNumberTime);

    }

    /**
     * 更新附件简历时间
     *
     * @param bean
     */
    @Override
    public void updateExchangeAnnexResumeTime(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_EXCHANGE_ANNEX_RESUME_TIME, bean.exchangeAnnexResumeTime);
        }
        reportLog(returnId, "updateExchangeAnnexResumeTime friendId = %d ,exchangeAnnexResumeTime = %d ", bean.friendId, bean.exchangeAnnexResumeTime);

    }

    @Override
    public void updateUpdateTime(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateDataTime(bean.friendId, bean.friendSource, bean.updateTime);
        }
        reportLog(returnId, "updateUpdateTime friendId = %d ,updateTime = %d ", bean.friendId, bean.updateTime);

    }

    /**
     * 更新邀请页面时间
     *
     * @param bean
     */
    @Override
    public void updateInviteInterviewTime(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_EXCHANGE_INTERVIEW_TIME, bean.exchangeInterviewTime);
        }
        reportLog(returnId, "updateInviteInterviewTime friendId = %d ,exchangeInterviewTime = %d ", bean.friendId, bean.exchangeInterviewTime);

    }

    /**
     * 更新面试状态
     *
     * @param bean
     */
    @Override
    public void updateInterviewStatus(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateInterviewStatus(new ContactInterview(bean.friendId, bean.friendSource, bean.currentInterviewStatus, bean.currentInterviewDesc, bean.currentInterviewProtocol, bean.interviewDateStr, bean.interviewTimeStr));
        }
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateExchangeType(bean.friendId, bean.friendSource, bean.messageExchangeIcon);
        }
        reportLog(returnId, "updateInterviewStatus friendId = %d ,currentInterviewStatus = %d ", bean.friendId, bean.currentInterviewStatus);

    }

    @Override
    public void updateVideoInterview(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        int returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            if (TextUtils.isEmpty(bean.videoInterviewF2Text)) {
                returnId = localDataDao.removeField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_SHORT_MSG);
            } else {
                returnId = localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_SHORT_MSG, bean.videoInterviewF2Text);
            }

            if (TextUtils.isEmpty(bean.videoInterviewChatTopText)) {
                localDataDao.removeField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_LONG_MSG);
            } else {
                localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_LONG_MSG, bean.videoInterviewChatTopText);
            }
            reportLog(returnId, "updateVideoInterview friendId = %d ,videoInterviewF2Text = %s ", bean.friendId, bean.videoInterviewF2Text);

        }
    }

    /**
     * 更新聊天未读数据
     *
     * @param bean
     */
    @Override
    public void updateUnreadCount(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateUnreadCount(bean.friendId, bean.friendSource, bean.noneReadCount);
            localDataDao.addField(bean.friendId, bean.friendSource, ContactLocalEntity.LocalField.CONTACT_SWITCH, bean.switch1);
        }
        reportLog(returnId, "updateUnreadCount friendId = %d ,noneReadCount = %d ", bean.friendId, bean.noneReadCount);

    }

    /**
     * 更新联系人最后消息的发送状态
     *
     * @param bean 联系人实例
     */
    @Override
    public void updateLastTextStatus(ContactBean bean, long lastChatClientMessageId) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateMsgStatus(bean.friendId, bean.friendSource, bean.lastChatStatus);
        }
        reportLog(returnId, "updateLastTextStatus friendId = %d ,lastChatStatus = %d ", bean.friendId, bean.lastChatStatus);

    }

    /**
     * 更新联系人姓名和头像
     *
     * @param bean 联系人实例
     */
    @Override
    public void updateNameAndHeadUrl(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateNameAndHeadUrl(bean.friendId, bean.friendSource, bean.friendName, bean.friendDefaultAvatar);
        }
        reportLog(returnId, "updateNameAndHeadUrl friendId = %d ,friendName = %s ", bean.friendId, bean.friendName);
    }

    /**
     * 更新草稿
     *
     * @param bean
     */
    @Override
    public void updateDraft(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateDraft(bean.friendId, bean.friendSource, bean.RoughDraft);
        }
        reportLog(returnId, "updateDraft friendId = %d ,RoughDraft = %s ", bean.friendId, bean.RoughDraft);

    }

    @Override
    public void updateFriendStage(ContactBean bean) {

        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            returnId = localDataDao.updateFriendStage(bean.friendId, bean.friendSource, bean.fridendStage);
        }
        reportLog(returnId, "updateFriendStage friendId = %d ,fridendStage = %s ", bean.friendId, bean.fridendStage);


    }


    @Override
    public void updateLabel(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateLabel(bean.friendId, bean.friendSource, bean.labels);
        }
        reportLog(returnId, "updateLabel friendId = %d ,labels = %s ", bean.friendId, bean.labels);

    }

    @Override
    public void updateNote(ContactBean bean) {
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateNote(bean.friendId, bean.friendSource, bean.note);
        }
        reportLog(returnId, "updateNote friendId = %d ,note = %s ", bean.friendId, bean.note);

    }

    @Override
    public void updateWarningTip(ContactBean bean) {
        long returnId = -1;

        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateFullInfoWarningTips(bean.warningTips,bean.friendId,bean.friendSource);
        }
        reportLog(returnId, "updateWarningTip friendId = %d ,note = %s ", bean.friendId, bean.note);
    }

    @Override
    public void updateTop(ContactBean bean) {
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateTop(bean.friendId, bean.friendSource, bean.isTop,bean.updateTime);
        }
        reportLog(returnId, "updateTop friendId = %d ,isTop = %s updateTime = %d", bean.friendId, bean.isTop,bean.updateTime);
    }

    @Override
    public void updateFriendCompanies(List<ContactBean> contactBeans) {
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            List<ContactCompanies> contactCompanies = new ArrayList<>();
            for (ContactBean bean : contactBeans) {
                contactCompanies.add(new ContactCompanies(bean.friendId, bean.friendSource, bean.friendCompanies));
            }
            localDataDao.upsertFriendCompanies(contactCompanies);
        }
    }


    @Override
    public void updateIsNoDisturb(ContactBean bean) {
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateNoDisturb(bean.friendId, bean.friendSource, bean.noDisturb);
        }
        reportLog(returnId, "updateIsTop friendId = %d ,noDisturb = %d ", bean.friendId, bean.noDisturb);
    }

    @Override
    public void updateInterviewSchedule(ContactBean bean) {
        if (!checkContactValidity(bean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_STATUS, bean.interviewScheduleStatus);
            objectMap.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_TIME, bean.interviewScheduleTime);
            objectMap.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_PROTOCOL, bean.interviewScheduleProtocol);
            returnId = localDataDao.addField(bean.friendId, bean.friendSource, objectMap);
        }
        reportLog(returnId, "updateInterviewSchedule friendId = %d ", bean.friendId);
    }

    @Override
    public void updateSyncGeekInfo(ContactBean contactBean) {
        if (!checkContactValidity(contactBean)) return;
        long returnId = -1;
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            returnId = contactDao.updateSyncGeekInfo(contactBean.friendId, contactBean.friendSource, contactBean.workYear, contactBean.workDesc, contactBean.degree, contactBean.expectSalary);
        }
        reportLog(returnId, "updateSyncGeekInfo friendId = %d ", contactBean.friendId);
    }



    @Override
    public long getContactCount(int role) {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            return contactDao.queryContactCount();
        }
        return 0;
    }

    public long getContactCountWithFriendId() {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            return contactDao.queryContactCountWithFriendId();
        }
        return 0;
    }

    public long queryDelContactCount() {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            return contactDao.queryDelContactCount();
        }
        return 0;
    }
    /**
     * 删除联系人
     *
     * @param bean
     */
    @Override
    public void deleteContact(ContactBean bean) {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            int returnId = contactDao.delete(bean.friendId, bean.friendSource);
            reportLog(returnId, "deleteContact friendId = %d friendSource = %d", bean.friendId, bean.friendSource);

        }
        com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            int returnId = localDataDao.delete(bean.friendId, bean.friendSource);
            reportLog(returnId, "localDataDao.deleteContact friendId = %d friendSource = %d", bean.friendId, bean.friendSource);
        }
        //TODO 兼容老版本DB 多个相同的friendId
        deleteContact(bean.friendId, bean.friendSource > 0 ? 0 : 1);
    }

    @Override
    public String getDBVersion() {
        final String SQL = "SELECT sqlite_version()";
        SQLiteDatabase database = App.get().db().getReadableDatabase();
        Cursor cursor = null;
        try {
            cursor = database.rawQuery(SQL, new String[]{});
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                return cursor.getString(0);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "getDBVersion error.");
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (Exception e) {
                    TLog.error(TAG, e, "getDBVersion finally error.");
                }
            }
        }
        return "";
    }

    /**
     * 保存联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    @Override
    public int updateContacts(List<ContactBean> list) {
        return 0;
    }
    /**
     * 更新联系人
     * 注意：
     * 1、getBaseInfoList接口返回字段使用，不是全量字段存储
     * 2、只处理老db，新db空实现
     */
    @Override
    public int updateBaseInfoContacts(List<ContactBean> list) {
        return 0;
    }

    @Override
    public int deleteContacts(List<ContactBean> list) {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if(null!=contactDao){
            List<ContactEntity> contactEntities = new ArrayList<>();
            for (ContactBean contactBean : list){
                contactEntities.add(ContactTransformUtils.toContactEntity(contactBean));
            }
            return contactDao.delete(contactEntities);
        }
        return 0;
    }

    @Override
    public void repairLastMid() {
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (null != contactDao) {
            contactDao.repairLastMid();
        }
    }

    @Override
    public List<ContactBean> checkSameContacts() {
        return null;
    }

    @Override
    public List<ContactBean> querySameContactsForBaseInfo(long friendId, int myRole, int friendSource, long originId) {
        return null;
    }

    public void deleteContact(long friendId,int friendSource) {
        //为了兼容老版本BUG 存在多个相同的friendId的情况
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            int returnId = contactDao.delete(friendId, friendSource);
            com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
            if (localDataDao != null) {
                localDataDao.delete(friendId, friendSource);
            }
            if (returnId > 0) {
                ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "deleteDzContact").p2(String.valueOf(friendId)).report();
            }
        }

    }


    public void deleteContact() {
        ChatDatabaseHelper.runInTransaction(new WCDBCheckRunnable("deleteContact"){
            @Override
            public void execute() {
                com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
                if (contactDao != null) {
                    contactDao.deleteAll();
                }
                com.bszp.kernel.chat.db.LocalDataDao localDataDao = getLocalDataDao();
                if (localDataDao != null) {
                    localDataDao.deleteAll();
                }
            }
        });
    }

    public void quickDelete(){
        com.bszp.kernel.chat.db.ContactDao contactDao = getContactDao();
        if (contactDao != null) {
            contactDao.quickDelete();
        }
    }
    private List<ContactBean> toContactBeans(final Cursor _cursor) {
        try {
            final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
            final int _cursorIndexOfWxNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "wxNumber");
            final int _cursorIndexOfIsBlack = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlack");
            final int _cursorIndexOfIsFreeze = CursorUtil.getColumnIndexOrThrow(_cursor, "isFreeze");
            final int _cursorIndexOfInterviewStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewStatus");
            final int _cursorIndexOfInterviewStatusDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewStatusDesc");
            final int _cursorIndexOfInterviewUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewUrl");
            final int _cursorIndexOfInterviewDateStr = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewDateStr");
            final int _cursorIndexOfInterviewTimeStr = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewTimeStr");
            final int _cursorIndexOfExchangeResumeStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "exchangeResumeStatus");
            final int _cursorIndexOfResumeUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "resumeUrl");
            final int _cursorIndexOfBlackDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "blackDesc");
            final int _cursorIndexOfIsWxRemind = CursorUtil.getColumnIndexOrThrow(_cursor, "isWxRemind");
            final int _cursorIndexOfVideoResumeUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoResumeUrl");
            final int _cursorIndexOfPartJobStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "partJobStrategy");
            final int _cursorIndexOfAddTime = CursorUtil.getColumnIndexOrThrow(_cursor, "addTime");
            final int _cursorIndexOfDelTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delTime");
            final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
            final int _cursorIndexOfLabels = CursorUtil.getColumnIndexOrThrow(_cursor, "labels");
            final int _cursorIndexOfStarTypes = CursorUtil.getColumnIndexOrThrow(_cursor, "starTypes");
            final int _cursorIndexOfIsPreFreeze = CursorUtil.getColumnIndexOrThrow(_cursor, "isPreFreeze");
            final int _cursorIndexOfFreezeInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "freezeInfo");
            final int _cursorIndexOfPreFreezeInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "preFreezeInfo");
            final int _cursorIndexOfFriendId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendId");
            final int _cursorIndexOfFriendSource = CursorUtil.getColumnIndexOrThrow(_cursor, "friendSource");
            final int _cursorIndexOfSecurityId = CursorUtil.getColumnIndexOrThrow(_cursor, "securityId");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfTinyUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "tinyUrl");
            final int _cursorIndexOfJobId = CursorUtil.getColumnIndexOrThrow(_cursor, "jobId");
            final int _cursorIndexOfJobSource = CursorUtil.getColumnIndexOrThrow(_cursor, "jobSource");
            final int _cursorIndexOfJobName = CursorUtil.getColumnIndexOrThrow(_cursor, "jobName");
            final int _cursorIndexOfExpectId = CursorUtil.getColumnIndexOrThrow(_cursor, "expectId");
            final int _cursorIndexOfSourceTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceTitle");
            final int _cursorIndexOfIsTop = CursorUtil.getColumnIndexOrThrow(_cursor, "isTop");
            final int _cursorIndexOfIsRejectUser = CursorUtil.getColumnIndexOrThrow(_cursor, "isRejectUser");
            final int _cursorIndexOfRejectDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "rejectDesc");
            final int _cursorIndexOfGoldGeekStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "goldGeekStatus");
            final int _cursorIndexOfItemType = CursorUtil.getColumnIndexOrThrow(_cursor, "itemType");
            final int _cursorIndexOfHeadline = CursorUtil.getColumnIndexOrThrow(_cursor, "headline");
            final int _cursorIndexOfIsStar = CursorUtil.getColumnIndexOrThrow(_cursor, "isStar");
            final int _cursorIndexOfNoDisturb = CursorUtil.getColumnIndexOrThrow(_cursor, "noDisturb");
            final int _cursorIndexOfIsFiltered = CursorUtil.getColumnIndexOrThrow(_cursor, "isFiltered");
            final int _cursorIndexOfFilterReasonList = CursorUtil.getColumnIndexOrThrow(_cursor, "filterReasonList");
            final int warningTips = CursorUtil.getColumnIndexOrThrow(_cursor, "warningTips");
            final int _cursorIndexOfDatetime = CursorUtil.getColumnIndexOrThrow(_cursor, "datetime");
            final int _cursorIndexOfExpectPositionName = CursorUtil.getColumnIndexOrThrow(_cursor, "expectPositionName");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfCertification = CursorUtil.getColumnIndexOrThrow(_cursor, "certification");
            final int _cursorIndexOfCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "company");
            final int _cursorIndexOfIsHeadhunter = CursorUtil.getColumnIndexOrThrow(_cursor, "isHeadhunter");
            final int _cursorIndexOfHighGeek = CursorUtil.getColumnIndexOrThrow(_cursor, "highGeek");
            final int _cursorIndexOfPositionName = CursorUtil.getColumnIndexOrThrow(_cursor, "positionName");
            final int _cursorIndexOfSalaryDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "salaryDesc");
            final int _cursorIndexOfWorkplace = CursorUtil.getColumnIndexOrThrow(_cursor, "workplace");
            final int _cursorIndexOfIsAgentRecruit = CursorUtil.getColumnIndexOrThrow(_cursor, "isAgentRecruit");
            final int _cursorIndexOfIsHunter = CursorUtil.getColumnIndexOrThrow(_cursor, "isHunter");
            final int _cursorIndexOfIsAgency = CursorUtil.getColumnIndexOrThrow(_cursor, "isAgency");
            final int _cursorIndexOfInvalidJob = CursorUtil.getColumnIndexOrThrow(_cursor, "invalidJob");
            final int _cursorIndexOfWorkYear = CursorUtil.getColumnIndexOrThrow(_cursor, "workYear");
            final int _cursorIndexOfDegree = CursorUtil.getColumnIndexOrThrow(_cursor, "degree");
            final int _cursorIndexOfExpectSalary = CursorUtil.getColumnIndexOrThrow(_cursor, "expectSalary");
            final int _cursorIndexOfWorkDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "workDesc");
            final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
            final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
//        final int _cursorIndexOfFriendId_1 = CursorUtil.getColumnIndexOrThrow(_cursor, "friendId");
//        final int _cursorIndexOfFriendSource_1 = CursorUtil.getColumnIndexOrThrow(_cursor, "friendSource");
            final int _cursorIndexOfAvatarIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "avatarIndex");
            final int _cursorIndexOfNoneReadCount = CursorUtil.getColumnIndexOrThrow(_cursor, "noneReadCount");
            final int _cursorIndexOfFriendStage = CursorUtil.getColumnIndexOrThrow(_cursor, "friendStage");
            final int _cursorIndexOfFriendCompanies = CursorUtil.getColumnIndexOrThrow(_cursor, "friendCompanies");
            final int _cursorIndexOfDraft = CursorUtil.getColumnIndexOrThrow(_cursor, "draft");
            final int _cursorIndexOfExchangeType = CursorUtil.getColumnIndexOrThrow(_cursor, "exchangeType");
            final int _cursorIndexOfStateType = CursorUtil.getColumnIndexOrThrow(_cursor, "stateType");
            final int _cursorIndexOfExtStr = CursorUtil.getColumnIndexOrThrow(_cursor, "extStr");
            final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
            final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
            final int _cursorIndexOfMid = CursorUtil.getColumnIndexOrThrow(_cursor, "mid");
            final int _cursorIndexOfCmid = CursorUtil.getColumnIndexOrThrow(_cursor, "cmid");
            final int _cursorIndexOfQuoteMid = CursorUtil.getColumnIndexOrThrow(_cursor, "quoteMid");
            final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
            final int _cursorIndexOfMsgState = CursorUtil.getColumnIndexOrThrow(_cursor, "msgState");
            final int _cursorIndexOfChatTime = CursorUtil.getColumnIndexOrThrow(_cursor, "chatTime");
            final List<ContactBean> _result = new ArrayList<ContactBean>(_cursor.getCount());
            while (_cursor.moveToNext()) {
                ContactBean _item = new ContactBean();
                _item.myId = AccountHelper.getUid();
                _item.myRole = AccountHelper.getIdentity();
                _item.friendId = _cursor.getLong(_cursorIndexOfFriendId);
                _item.friendName = _cursor.getString(_cursorIndexOfName);
                _item.friendSource = _cursor.getInt(_cursorIndexOfFriendSource);
                _item.securityId = _cursor.getString(_cursorIndexOfSecurityId);
                _item.jobId = _cursor.getLong(_cursorIndexOfJobId);
                _item.bossJobPosition = _cursor.getString(_cursorIndexOfJobName);
                _item.jobIntentId = _cursor.getLong(_cursorIndexOfExpectId);
                _item.jobSource = _cursor.getInt(_cursorIndexOfJobSource);
                _item.invalidJob = _cursor.getInt(_cursorIndexOfInvalidJob);

                //服务器没有下发这个字段
                //_item.chatMsgBlockHeadBar = contactEntity.getChatMsgBlockHeadBar();
                _item.friendDefaultAvatar = _cursor.getString(_cursorIndexOfTinyUrl);
                _item.geekPositionName = _cursor.getString(_cursorIndexOfTitle);
                _item.userFromTitle = _cursor.getString(_cursorIndexOfSourceTitle);
                _item.certification = _cursor.getInt(_cursorIndexOfCertification);
                _item.bossCompanyName = _cursor.getString(_cursorIndexOfCompany);
                _item.expectPositionName = _cursor.getString(_cursorIndexOfExpectPositionName);
                _item.isTop = _cursor.getInt(_cursorIndexOfIsTop) != 0;
                _item.goldGeekStatus = _cursor.getInt(_cursorIndexOfGoldGeekStatus);
                _item.isHunter = _cursor.getInt(_cursorIndexOfIsHunter) != 0;
                _item.isAgency = _cursor.getInt(_cursorIndexOfIsAgency) != 0;
                _item.isReject = _cursor.getInt(_cursorIndexOfIsRejectUser) != 0;
                _item.isStar = _cursor.getInt(_cursorIndexOfIsStar) != 0;
                _item.rejectReason = _cursor.getString(_cursorIndexOfRejectDesc);
                _item.isHeadhunter = _cursor.getInt(_cursorIndexOfIsHeadhunter) != 0;
                _item.greetingText = _cursor.getString(_cursorIndexOfHeadline);
                //服务器没有下发这个字段
                //_item.isTechGeekBlock = contactEntity.isTechGeekBlock();
                _item.updateTime = _cursor.getLong(_cursorIndexOfDatetime);
                _item.positionName = _cursor.getString(_cursorIndexOfPositionName);
                _item.salaryDesc = _cursor.getString(_cursorIndexOfSalaryDesc);
                _item.workplace = _cursor.getString(_cursorIndexOfWorkplace);
                _item.isAgentRecruit = _cursor.getInt(_cursorIndexOfIsAgentRecruit) != 0;
                _item.itemType = _cursor.getInt(_cursorIndexOfItemType);
                _item.noDisturb = _cursor.getInt(_cursorIndexOfNoDisturb);
                _item.highGeek = _cursor.getInt(_cursorIndexOfHighGeek) != 0;
                _item.isFiltered = _cursor.getInt(_cursorIndexOfIsFiltered) != 0;
                _item.filterReasonList = RoomConverters.stringToList(_cursor.getString(_cursorIndexOfFilterReasonList));
                _item.starTypes = transferStarTypes(RoomConverters.stringToList(_cursor.getString(_cursorIndexOfStarTypes)));

                /* 以下是 FullInfo 返回的*/
                _item.friendPhone = _cursor.getString(_cursorIndexOfPhoneNumber);
                _item.friendWxNumber = _cursor.getString(_cursorIndexOfWxNumber);
                _item.isBlack = _cursor.getInt(_cursorIndexOfIsBlack) != 0;
                _item.isFreeze = _cursor.getInt(_cursorIndexOfIsFreeze) != 0;
                _item.blackDesc = _cursor.getString(_cursorIndexOfBlackDesc);
                _item.isWxRemind = _cursor.getInt(_cursorIndexOfIsWxRemind) != 0;
                _item.videoResumeUrl = _cursor.getString(_cursorIndexOfVideoResumeUrl);
                _item.partJobStrategy = _cursor.getInt(_cursorIndexOfPartJobStrategy);
                _item.exchangeResumeStatus = _cursor.getLong(_cursorIndexOfExchangeResumeStatus);
                _item.currentInterviewStatus = _cursor.getInt(_cursorIndexOfInterviewStatus);
                _item.currentInterviewDesc = _cursor.getString(_cursorIndexOfInterviewStatusDesc);
                _item.currentInterviewProtocol = _cursor.getString(_cursorIndexOfInterviewUrl);
                _item.exchangeResumeUrl = _cursor.getString(_cursorIndexOfResumeUrl);
                _item.addTime = _cursor.getLong(_cursorIndexOfAddTime);
                _item.delTime = _cursor.getLong(_cursorIndexOfDelTime);
                _item.note = _cursor.getString(_cursorIndexOfNote);
                _item.labels = _cursor.getString(_cursorIndexOfLabels);
                _item.freezeInfo = _cursor.getString(_cursorIndexOfFreezeInfo);
                _item.preFreezeInfo = _cursor.getString(_cursorIndexOfPreFreezeInfo);
                _item.isPreFreeze = _cursor.getInt(_cursorIndexOfIsPreFreeze) != 0;
                _item.interviewDateStr = _cursor.getString(_cursorIndexOfInterviewDateStr);
                _item.interviewTimeStr = _cursor.getString(_cursorIndexOfInterviewTimeStr);
                _item.workYear = _cursor.getString(_cursorIndexOfWorkYear);
                _item.workDesc = _cursor.getString(_cursorIndexOfWorkDesc);
                _item.degree = _cursor.getString(_cursorIndexOfDegree);
                _item.expectSalary = _cursor.getString(_cursorIndexOfExpectSalary);
                _item.warningTips = RoomConverters.stringToWarningList(_cursor.getString(warningTips));
                /*ContactLocal*/
                _item.noneReadCount = _cursor.getInt(_cursorIndexOfNoneReadCount);
                _item.RoughDraft = _cursor.getString(_cursorIndexOfDraft);
                _item.friendDefaultAvatarIndex = _cursor.getInt(_cursorIndexOfAvatarIndex);
                _item.friendCompanies = _cursor.getString(_cursorIndexOfFriendCompanies);
                _item.fridendStage = _cursor.getInt(_cursorIndexOfFriendStage);
                //_item.switch1 =
                _item.lastChatText = _cursor.getString(_cursorIndexOfSummary);
                _item.lastChatTime = _cursor.getLong(_cursorIndexOfChatTime);
                _item.lastChatStatus = _cursor.getInt(_cursorIndexOfMsgState);
                _item.lastChatClientMessageId = _cursor.getLong(_cursorIndexOfCmid);
                _item.quoteMessageId = _cursor.getLong(_cursorIndexOfQuoteMid);

                _item.messageExchangeIcon = _cursor.getInt(_cursorIndexOfExchangeType);

                ContactLocalEntity localEntity = new ContactLocalEntity(_item.friendId, _item.friendSource);
                localEntity.stateType = _cursor.getInt(_cursorIndexOfStateType);
                _item.isPlatFromResearch = localEntity.isStateType(ContactLocalEntity.StateType.IS_PLAT_FROM_RESEARCH);
                _item.isContactHelper = localEntity.isStateType(ContactLocalEntity.StateType.IS_CONTACT_HELPER);
                _item.receiveFriendGreetMsg = localEntity.isStateType(ContactLocalEntity.StateType.IS_RECEIVE_FRIEND_GREET_MSG);
                _item.ats = localEntity.isStateType(ContactLocalEntity.StateType.IS_ATS);
                _item.improveMessageExposure = localEntity.isStateType(ContactLocalEntity.StateType.IS_IMPROVE_MESSAGE_EXPOSURE);

                ContactLocalEntity.LocalField localField = GsonUtils.fromJson(_cursor.getString(_cursorIndexOfExtStr), ContactLocalEntity.LocalField.class);
                if (localField != null) {
                    _item.lid = localField.lid;
                    _item.switch1 = localField.switch1;
                    _item.directCallStatus = localField.directCallStatus;
                    _item.hasJumpToChat = localField.hasJumpToChat;
                    _item.exchangePhoneTime = localField.exchangePhoneTime;
                    _item.exchangeAnnexResumeTime = localField.exchangeAnnexResumeTime;
                    _item.exchangeInterviewTime = localField.exchangeInterviewTime;
                    _item.exchangeWxNumberTime = localField.exchangeWxNumberTime;
                    _item.lastRefreshTime = localField.lastRefreshTime;
                    _item.videoInterviewF2Text = localField.videoInterviewShortMsg;
                    _item.videoInterviewChatTopText = localField.videoInterviewLongMsg;
                }
                _result.add(_item);
            }
            return _result;
        } catch (Exception e) {

        } finally {
            _cursor.close();
        }
        return null;
    }

    private ContactBean toContactBean(final Cursor _cursor) {
        ContactBean _item = null;
        try {
            final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
            final int _cursorIndexOfWxNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "wxNumber");
            final int _cursorIndexOfIsBlack = CursorUtil.getColumnIndexOrThrow(_cursor, "isBlack");
            final int _cursorIndexOfIsFreeze = CursorUtil.getColumnIndexOrThrow(_cursor, "isFreeze");
            final int _cursorIndexOfInterviewStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewStatus");
            final int _cursorIndexOfInterviewStatusDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewStatusDesc");
            final int _cursorIndexOfInterviewUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewUrl");
            final int _cursorIndexOfInterviewDateStr = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewDateStr");
            final int _cursorIndexOfInterviewTimeStr = CursorUtil.getColumnIndexOrThrow(_cursor, "interviewTimeStr");
            final int _cursorIndexOfExchangeResumeStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "exchangeResumeStatus");
            final int _cursorIndexOfResumeUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "resumeUrl");
            final int _cursorIndexOfPhoneAuthStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneAuthStatus");
            final int _cursorIndexOfWeiXinAuthStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "weiXinAuthStatus");
            final int _cursorIndexOfBPhoneAuthStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "bPhoneAuthStatus");
            final int _cursorIndexOfBWeiXinAuthStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "bWeiXinAuthStatus");
            final int _cursorIndexOfResumeAuthStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "resumeAuthStatus");
            final int _cursorIndexOfBlackDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "blackDesc");
            final int _cursorIndexOfIsWxRemind = CursorUtil.getColumnIndexOrThrow(_cursor, "isWxRemind");
            final int _cursorIndexOfVideoResumeUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoResumeUrl");
            final int _cursorIndexOfPartJobStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "partJobStrategy");
            final int _cursorIndexOfAddTime = CursorUtil.getColumnIndexOrThrow(_cursor, "addTime");
            final int _cursorIndexOfDelTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delTime");
            final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
            final int _cursorIndexOfLabels = CursorUtil.getColumnIndexOrThrow(_cursor, "labels");
            final int _cursorIndexOfStarTypes = CursorUtil.getColumnIndexOrThrow(_cursor, "starTypes");
            final int _cursorIndexOfIsPreFreeze = CursorUtil.getColumnIndexOrThrow(_cursor, "isPreFreeze");
            final int _cursorIndexOfFreezeInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "freezeInfo");
            final int _cursorIndexOfPreFreezeInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "preFreezeInfo");
            final int _cursorIndexOfFriendId = CursorUtil.getColumnIndexOrThrow(_cursor, "friendId");
            final int _cursorIndexOfFriendSource = CursorUtil.getColumnIndexOrThrow(_cursor, "friendSource");
            final int _cursorIndexOfSecurityId = CursorUtil.getColumnIndexOrThrow(_cursor, "securityId");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfTinyUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "tinyUrl");
            final int _cursorIndexOfJobId = CursorUtil.getColumnIndexOrThrow(_cursor, "jobId");
            final int _cursorIndexOfJobSource = CursorUtil.getColumnIndexOrThrow(_cursor, "jobSource");
            final int _cursorIndexOfJobName = CursorUtil.getColumnIndexOrThrow(_cursor, "jobName");
            final int _cursorIndexOfExpectId = CursorUtil.getColumnIndexOrThrow(_cursor, "expectId");
            final int _cursorIndexOfSourceTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceTitle");
            final int _cursorIndexOfIsTop = CursorUtil.getColumnIndexOrThrow(_cursor, "isTop");
            final int _cursorIndexOfIsRejectUser = CursorUtil.getColumnIndexOrThrow(_cursor, "isRejectUser");
            final int _cursorIndexOfRejectDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "rejectDesc");
            final int _cursorIndexOfGoldGeekStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "goldGeekStatus");
            final int _cursorIndexOfItemType = CursorUtil.getColumnIndexOrThrow(_cursor, "itemType");
            final int _cursorIndexOfHeadline = CursorUtil.getColumnIndexOrThrow(_cursor, "headline");
            final int _cursorIndexOfIsStar = CursorUtil.getColumnIndexOrThrow(_cursor, "isStar");
            final int _cursorIndexOfNoDisturb = CursorUtil.getColumnIndexOrThrow(_cursor, "noDisturb");
            final int _cursorIndexOfIsFiltered = CursorUtil.getColumnIndexOrThrow(_cursor, "isFiltered");
            final int _cursorIndexOfFilterReasonList = CursorUtil.getColumnIndexOrThrow(_cursor, "filterReasonList");
            final int warningTips = CursorUtil.getColumnIndexOrThrow(_cursor, "warningTips");
            final int _cursorIndexOfDatetime = CursorUtil.getColumnIndexOrThrow(_cursor, "datetime");
            final int _cursorIndexOfExpectPositionName = CursorUtil.getColumnIndexOrThrow(_cursor, "expectPositionName");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfCertification = CursorUtil.getColumnIndexOrThrow(_cursor, "certification");
            final int _cursorIndexOfCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "company");
            final int _cursorIndexOfIsHeadhunter = CursorUtil.getColumnIndexOrThrow(_cursor, "isHeadhunter");
            final int _cursorIndexOfHighGeek = CursorUtil.getColumnIndexOrThrow(_cursor, "highGeek");
            final int _cursorIndexOfPositionName = CursorUtil.getColumnIndexOrThrow(_cursor, "positionName");
            final int _cursorIndexOfSalaryDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "salaryDesc");
            final int _cursorIndexOfWorkplace = CursorUtil.getColumnIndexOrThrow(_cursor, "workplace");
            final int _cursorIndexOfIsAgentRecruit = CursorUtil.getColumnIndexOrThrow(_cursor, "isAgentRecruit");
            final int _cursorIndexOfIsHunter = CursorUtil.getColumnIndexOrThrow(_cursor, "isHunter");
            final int _cursorIndexOfIsAgency = CursorUtil.getColumnIndexOrThrow(_cursor, "isAgency");
            final int _cursorIndexOfInvalidJob = CursorUtil.getColumnIndexOrThrow(_cursor, "invalidJob");
            final int _cursorIndexOfWorkYear = CursorUtil.getColumnIndexOrThrow(_cursor, "workYear");
            final int _cursorIndexOfDegree = CursorUtil.getColumnIndexOrThrow(_cursor, "degree");
            final int _cursorIndexOfExpectSalary = CursorUtil.getColumnIndexOrThrow(_cursor, "expectSalary");
            final int _cursorIndexOfWorkDesc = CursorUtil.getColumnIndexOrThrow(_cursor, "workDesc");
            final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
            final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
//          final int _cursorIndexOfFriendId_1 = CursorUtil.getColumnIndexOrThrow(_cursor, "friendId");
//          final int _cursorIndexOfFriendSource_1 = CursorUtil.getColumnIndexOrThrow(_cursor, "friendSource");
            final int _cursorIndexOfAvatarIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "avatarIndex");
            final int _cursorIndexOfNoneReadCount = CursorUtil.getColumnIndexOrThrow(_cursor, "noneReadCount");
            final int _cursorIndexOfFriendStage = CursorUtil.getColumnIndexOrThrow(_cursor, "friendStage");
            final int _cursorIndexOfFriendCompanies = CursorUtil.getColumnIndexOrThrow(_cursor, "friendCompanies");
            final int _cursorIndexOfDraft = CursorUtil.getColumnIndexOrThrow(_cursor, "draft");
            final int _cursorIndexOfExchangeType = CursorUtil.getColumnIndexOrThrow(_cursor, "exchangeType");
            final int _cursorIndexOfStateType = CursorUtil.getColumnIndexOrThrow(_cursor, "stateType");
            final int _cursorIndexOfExtStr = CursorUtil.getColumnIndexOrThrow(_cursor, "extStr");
            final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
            final int _cursorIndexOfMessageId = CursorUtil.getColumnIndexOrThrow(_cursor, "messageId");
            final int _cursorIndexOfMid = CursorUtil.getColumnIndexOrThrow(_cursor, "mid");
            final int _cursorIndexOfCmid = CursorUtil.getColumnIndexOrThrow(_cursor, "cmid");
            final int _cursorIndexOfQuoteMid = CursorUtil.getColumnIndexOrThrow(_cursor, "quoteMid");
            final int _cursorIndexOfSummary = CursorUtil.getColumnIndexOrThrow(_cursor, "summary");
            final int _cursorIndexOfMsgState = CursorUtil.getColumnIndexOrThrow(_cursor, "msgState");
            final int _cursorIndexOfChatTime = CursorUtil.getColumnIndexOrThrow(_cursor, "chatTime");
            _item = null;
            if (_cursor.moveToFirst()) {
                _item = new ContactBean();
                _item.myId = AccountHelper.getUid();
                _item.myRole = AccountHelper.getIdentity();
                _item.friendId = _cursor.getLong(_cursorIndexOfFriendId);
                _item.friendName = _cursor.getString(_cursorIndexOfName);
                _item.friendSource = _cursor.getInt(_cursorIndexOfFriendSource);
                _item.securityId = _cursor.getString(_cursorIndexOfSecurityId);
                _item.jobId = _cursor.getLong(_cursorIndexOfJobId);
                _item.bossJobPosition = _cursor.getString(_cursorIndexOfJobName);
                _item.jobIntentId = _cursor.getLong(_cursorIndexOfExpectId);
                _item.jobSource = _cursor.getInt(_cursorIndexOfJobSource);
                _item.invalidJob = _cursor.getInt(_cursorIndexOfInvalidJob);

                //服务器没有下发这个字段
                //_item.chatMsgBlockHeadBar = contactEntity.getChatMsgBlockHeadBar();
                _item.friendDefaultAvatar = _cursor.getString(_cursorIndexOfTinyUrl);
                _item.geekPositionName = _cursor.getString(_cursorIndexOfTitle);
                _item.userFromTitle = _cursor.getString(_cursorIndexOfSourceTitle);
                _item.certification = _cursor.getInt(_cursorIndexOfCertification);
                _item.bossCompanyName = _cursor.getString(_cursorIndexOfCompany);
                _item.expectPositionName = _cursor.getString(_cursorIndexOfExpectPositionName);
                _item.isTop = _cursor.getInt(_cursorIndexOfIsTop) != 0;
                _item.goldGeekStatus = _cursor.getInt(_cursorIndexOfGoldGeekStatus);
                _item.isHunter = _cursor.getInt(_cursorIndexOfIsHunter) != 0;
                _item.isAgency = _cursor.getInt(_cursorIndexOfIsAgency) != 0;
                _item.isReject = _cursor.getInt(_cursorIndexOfIsRejectUser) != 0;
                _item.isStar = _cursor.getInt(_cursorIndexOfIsStar) != 0;
                _item.rejectReason = _cursor.getString(_cursorIndexOfRejectDesc);
                _item.isHeadhunter = _cursor.getInt(_cursorIndexOfIsHeadhunter) != 0;
                _item.greetingText = _cursor.getString(_cursorIndexOfHeadline);
                //服务器没有下发这个字段
                //_item.isTechGeekBlock = contactEntity.isTechGeekBlock();
                _item.updateTime = _cursor.getLong(_cursorIndexOfDatetime);
                _item.positionName = _cursor.getString(_cursorIndexOfPositionName);
                _item.salaryDesc = _cursor.getString(_cursorIndexOfSalaryDesc);
                _item.workplace = _cursor.getString(_cursorIndexOfWorkplace);
                _item.isAgentRecruit = _cursor.getInt(_cursorIndexOfIsAgentRecruit) != 0;
                _item.itemType = _cursor.getInt(_cursorIndexOfItemType);
                _item.noDisturb = _cursor.getInt(_cursorIndexOfNoDisturb);
                _item.highGeek = _cursor.getInt(_cursorIndexOfHighGeek) != 0;
                _item.isFiltered = _cursor.getInt(_cursorIndexOfIsFiltered) != 0;
                _item.filterReasonList = RoomConverters.stringToList(_cursor.getString(_cursorIndexOfFilterReasonList));
                _item.starTypes = transferStarTypes(RoomConverters.stringToList(_cursor.getString(_cursorIndexOfStarTypes)));

                /* 以下是 FullInfo 返回的*/
                _item.friendPhone = _cursor.getString(_cursorIndexOfPhoneNumber);
                _item.friendWxNumber = _cursor.getString(_cursorIndexOfWxNumber);
                _item.isBlack = _cursor.getInt(_cursorIndexOfIsBlack) != 0;
                _item.isFreeze = _cursor.getInt(_cursorIndexOfIsFreeze) != 0;
                _item.blackDesc = _cursor.getString(_cursorIndexOfBlackDesc);
                _item.isWxRemind = _cursor.getInt(_cursorIndexOfIsWxRemind) != 0;
                _item.videoResumeUrl = _cursor.getString(_cursorIndexOfVideoResumeUrl);
                _item.partJobStrategy = _cursor.getInt(_cursorIndexOfPartJobStrategy);
                _item.exchangeResumeStatus = _cursor.getLong(_cursorIndexOfExchangeResumeStatus);
                _item.currentInterviewStatus = _cursor.getInt(_cursorIndexOfInterviewStatus);
                _item.currentInterviewDesc = _cursor.getString(_cursorIndexOfInterviewStatusDesc);
                _item.currentInterviewProtocol = _cursor.getString(_cursorIndexOfInterviewUrl);
                _item.exchangeResumeUrl = _cursor.getString(_cursorIndexOfResumeUrl);
                _item.addTime = _cursor.getLong(_cursorIndexOfAddTime);
                _item.delTime = _cursor.getLong(_cursorIndexOfDelTime);
                _item.note = _cursor.getString(_cursorIndexOfNote);
                _item.labels = _cursor.getString(_cursorIndexOfLabels);
                _item.freezeInfo = _cursor.getString(_cursorIndexOfFreezeInfo);
                _item.preFreezeInfo = _cursor.getString(_cursorIndexOfPreFreezeInfo);
                _item.isPreFreeze = _cursor.getInt(_cursorIndexOfIsPreFreeze) != 0;
                _item.interviewDateStr = _cursor.getString(_cursorIndexOfInterviewDateStr);
                _item.interviewTimeStr = _cursor.getString(_cursorIndexOfInterviewTimeStr);
                _item.workYear = _cursor.getString(_cursorIndexOfWorkYear);
                _item.workDesc = _cursor.getString(_cursorIndexOfWorkDesc);
                _item.degree = _cursor.getString(_cursorIndexOfDegree);
                _item.expectSalary = _cursor.getString(_cursorIndexOfExpectSalary);
                _item.warningTips = RoomConverters.stringToWarningList(_cursor.getString(warningTips));
                /*ContactLocal*/
                _item.noneReadCount = _cursor.getInt(_cursorIndexOfNoneReadCount);
                _item.RoughDraft = _cursor.getString(_cursorIndexOfDraft);
                _item.friendDefaultAvatarIndex = _cursor.getInt(_cursorIndexOfAvatarIndex);
                _item.friendCompanies = _cursor.getString(_cursorIndexOfFriendCompanies);
                _item.fridendStage = _cursor.getInt(_cursorIndexOfFriendStage);
                //_item.switch1 =
                _item.lastChatText = _cursor.getString(_cursorIndexOfSummary);
                _item.lastChatTime = _cursor.getLong(_cursorIndexOfChatTime);
                _item.lastChatStatus = _cursor.getInt(_cursorIndexOfMsgState);
                _item.lastChatClientMessageId = _cursor.getLong(_cursorIndexOfCmid);
                _item.quoteMessageId = _cursor.getLong(_cursorIndexOfQuoteMid);

                _item.messageExchangeIcon = _cursor.getInt(_cursorIndexOfExchangeType);

                ContactLocalEntity localEntity = new ContactLocalEntity(_item.friendId, _item.friendSource);
                localEntity.stateType = _cursor.getInt(_cursorIndexOfStateType);
                _item.isPlatFromResearch = localEntity.isStateType(ContactLocalEntity.StateType.IS_PLAT_FROM_RESEARCH);
                _item.isContactHelper = localEntity.isStateType(ContactLocalEntity.StateType.IS_CONTACT_HELPER);
                _item.receiveFriendGreetMsg = localEntity.isStateType(ContactLocalEntity.StateType.IS_RECEIVE_FRIEND_GREET_MSG);
                _item.ats = localEntity.isStateType(ContactLocalEntity.StateType.IS_ATS);
                _item.improveMessageExposure = localEntity.isStateType(ContactLocalEntity.StateType.IS_IMPROVE_MESSAGE_EXPOSURE);

                ContactLocalEntity.LocalField localField = GsonUtils.fromJson(_cursor.getString(_cursorIndexOfExtStr), ContactLocalEntity.LocalField.class);
                if (localField != null) {
                    _item.lid = localField.lid;
                    _item.switch1 = localField.switch1;
                    _item.directCallStatus = localField.directCallStatus;
                    _item.hasJumpToChat = localField.hasJumpToChat;
                    _item.exchangePhoneTime = localField.exchangePhoneTime;
                    _item.exchangeAnnexResumeTime = localField.exchangeAnnexResumeTime;
                    _item.exchangeInterviewTime = localField.exchangeInterviewTime;
                    _item.exchangeWxNumberTime = localField.exchangeWxNumberTime;
                    _item.lastRefreshTime = localField.lastRefreshTime;
                    _item.videoInterviewF2Text = localField.videoInterviewShortMsg;
                    _item.videoInterviewChatTopText = localField.videoInterviewLongMsg;
                }
            }
        } catch (Exception e) {

        } finally {
            _cursor.close();
        }
        return _item;
    }


    private static String transferStarTypes(List<String> star) {
        StringBuilder stringBuilder = new StringBuilder();
        if (star != null) {
            for (String value : star) {
                if (LText.empty(value)) continue;
                stringBuilder.append(value);
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();
    }

    private static List<String> transferStarTypes(String starTypes) {
        if (starTypes != null) {
            final String[] split = starTypes.split(",");
            return Arrays.asList(split);
        }
        return null;
    }
}
