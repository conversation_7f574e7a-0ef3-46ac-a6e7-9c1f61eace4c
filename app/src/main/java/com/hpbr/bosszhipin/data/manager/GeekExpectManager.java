package com.hpbr.bosszhipin.data.manager;

import android.content.Context;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.my.entity.GeekF1SortItemBean;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module_geek_export.PostExpectType;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.api.bean.geek.GeekF1CareerExpectItemBean;
import net.bosszhipin.api.bean.geek.GeekF1CareerItemBean;
import net.bosszhipin.api.bean.geek.ServeGeekMixedExpectBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Author: zhouyou
 * Date: 2019/5/13
 * 求职期望管理
 */
public class GeekExpectManager {

    private static final  String TAG="GeekExpectManager";

    private static SpImpl sp;

    private static String KEY_CURRENT_SWITCH_MIXTYPE = Constants.PREFIX + ".KEY_CURRENT_SWITCH_MIXTYPE";

    private static final String CURRENT_SELECT_EXPECT_KEY = Constants.PREFIX + ".CURRENT_SELECT_EXPECT_KEY";
    public static final int MIX_TYPE_NATIVE_SUBCITY = 4; // 816本地县城混推职位
    public static final int MIX_TYPE_NATIVE_SMALL_CITY = 8; // 1106.61 8:小城市混推

    public static final int MIX_TYPE_CITY_BLUE = 10; // 1109.62【C】城市普蓝-混推列表-功能优化
    public static final int MIX_TYPE_CITY_TOWN = 11; //1110.63【C】县级市本地混推升级为本地分类推荐+周边补量

    public static final int MIX_TYPE_SWITCH_RCMD = 12; //1121.66【C】F1分类推荐列表新增切换模式功能

    public static final int GEEK_PRO_PARTIME_EXPECTID = -3;

    public static final int MAX_SOCIAL_INTENT_COUNT = 3; // 职场人全职期望最多可选数量
    public static final int MAX_PART_TIME_INTENT_COUNT = 5; // 职场人兼职期望最多可选数量
    public static final int MAX_STUDENT_INTENT_COUNT = 10; // 学生期望最多可选数量
    public static final int MAX_INTERN_INTENT_COUNT = 1;

    public static final String EXPECT_EXPLAIN = URLConfig.getWebHost() + "mpa/html/mix/position-expection"; // 求职期望解释说明H5页面

    static {
        Context context = App.get().getContext();
        sp = SpFactory.create(context, Constants.PREFIX + ".current_select_expect." + UserManager.getUID());
    }

    public static boolean hasThisExpectation(long expectId) {
        boolean hasThisExpect = false;

        List<JobIntentBean> geekExpectList = getGeekExpectList();
        if (geekExpectList != null) {
            for (JobIntentBean jobIntentBean : geekExpectList) {
                if (jobIntentBean.jobIntentId == expectId) {
                    hasThisExpect = true;
                    break;
                }
            }
        }

        return hasThisExpect;
    }

    /**
     * 获取有效的牛人求职意向
     */
    public static List<JobIntentBean> getGeekExpectList() {
        UserBean user = UserManager.getLoginUser();
        return getGeekExpectList(user);
    }


    // 这两个方法主要用于判断  学生/牛人 期望是否变更，要隔离开来x
    public static List<JobIntentBean> getGeekStuExpectList() {
        UserBean user = UserManager.getLoginUser();

        if (!UserManager.isCurrentLoginStatus()
                || UserManager.getUserRole() != ROLE.GEEK
                || user == null
                || user.geekInfo == null) {
            return null;
        }
        GeekInfoBean geekInfo = user.geekInfo;
        //如果是实习生，或者是学生党，则获取此列表
        return geekInfo.internJobIntentList;
    }

    public static List<JobIntentBean> getGeekProExpectList() {
        UserBean user = UserManager.getLoginUser();

        if (!UserManager.isCurrentLoginStatus()
                || UserManager.getUserRole() != ROLE.GEEK
                || user == null
                || user.geekInfo == null) {
            return null;
        }
        GeekInfoBean geekInfo = user.geekInfo;
        return geekInfo.jobIntentList;


    }

    @Nullable
    public static JobIntentBean getGeekExpectById(@Nullable String expectId) {
        JobIntentBean jobIntentBean = null;
        List<JobIntentBean> geekExpectList = getGeekExpectList();

        if (geekExpectList != null && geekExpectList.size() > 0) {
            for (JobIntentBean intentBean : geekExpectList) {
                if (expectId != null && expectId.equals(String.valueOf(intentBean.jobIntentId))) {
                    jobIntentBean = intentBean;
                    break;
                }
            }
        }

        return jobIntentBean;
    }


    public static boolean hasBlueCollarExpect() {
        boolean isBlueCollarExpect = false;
        List<JobIntentBean> geekExpectList = GeekExpectManager.getGeekExpectList();
        // 判断是否当前选择的期望是否是蓝领期望
        if (LList.getCount(geekExpectList) > 0) {
            for (JobIntentBean serverExpectBean : geekExpectList) {
                if (serverExpectBean.blueCollarPosition) {
                    isBlueCollarExpect = true;
                    break;
                }
            }
        }
        return isBlueCollarExpect;
    }

    // 801 虚拟期望
    public static JobIntentBean getGeekMixedExpect() {


        UserBean user = UserManager.getLoginUser();


        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {

            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {

                // 如果type == 12 ，则说明该推荐期望支持切换，则默认取第子列表第一个或 上一次切换选中的type
                if (mixedExpectBean.mixExpectType == MIX_TYPE_SWITCH_RCMD && !LList.isEmpty(mixedExpectBean.switchableList)) {

                    // 当前type
                    int childMixType = sp.getInt(KEY_CURRENT_SWITCH_MIXTYPE, 0);
                    ServeGeekMixedExpectBean defaultExpectBean = LList.getElement(mixedExpectBean.switchableList, 0);

                    if (childMixType == 0) { //首次

                        if (defaultExpectBean != null) {
                            defaultExpectBean.supportSwitch = true;

                            return converMixedToJobIntentBean(defaultExpectBean);
                        }
                    } else {


                        for (ServeGeekMixedExpectBean childMixExpectBean : mixedExpectBean.switchableList) {
                            if (childMixExpectBean.mixExpectType == childMixType) {
                                childMixExpectBean.supportSwitch = true;

                                return converMixedToJobIntentBean(childMixExpectBean);
                            }
                        }

                        // 异常情况，如果没有这个type的期望，则选取默认的
                        if (defaultExpectBean != null) {
                            defaultExpectBean.supportSwitch = true;
                            sp.putInt(KEY_CURRENT_SWITCH_MIXTYPE, 0);
                            return converMixedToJobIntentBean(defaultExpectBean);
                        }
                    }
                }


                return converMixedToJobIntentBean(mixedExpectBean);
            }
        }


        return null;
    }


    /***
     * 获取职场人兼职期望
     * */
    public static JobIntentBean getGeekProPartimeExpect() {

        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekPartTimeCombineExpect != null) {

            ServeGeekMixedExpectBean partimeExpect = user.geekInfo.geekPartTimeCombineExpect;
            if (partimeExpect != null) {

                JobIntentBean partimeJobIntent = converMixedToJobIntentBean(partimeExpect);
                partimeJobIntent.isGeekPartime = true;

                return partimeJobIntent;
            }

        }


        return null;
    }

    /**
     * 获取职场人虚拟兼职期望
     */
    @Nullable
    public static ServeGeekMixedExpectBean getGeekProVirtualPartTimeExpect() {

        ServeGeekMixedExpectBean virtualPartTimeExpect = null;
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null) {
            virtualPartTimeExpect = user.geekInfo.virtualPartTimeCombineExpect;
        }
        return virtualPartTimeExpect;
    }

    public static JobIntentBean getOverseasExpect() {

        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.overSeasExpect != null) {

            ServeGeekMixedExpectBean overSeasExpect = user.geekInfo.overSeasExpect;
            if (overSeasExpect != null) {

                JobIntentBean overseIntent = converMixedToJobIntentBean(overSeasExpect);
                overseIntent.isOverSeas = true;

                return overseIntent;
            }

        }


        return null;
    }



    public static JobIntentBean getOddJobExpect() {

        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null ) {
            List<GeekF1CareerItemBean> careerList = user.geekInfo.careerList;
            if (!LList.isEmpty(careerList)){
                for (GeekF1CareerItemBean geekF1CareerItemBean : careerList) {
                    if (geekF1CareerItemBean!=null && geekF1CareerItemBean.tabId == GeekF1CareerItemBean.TAB_ID_ODD_JOB){
                        return getOddJobTab(geekF1CareerItemBean);
                    }

                }
            }
        }

        return null;
    }

    private static JobIntentBean getOddJobTab(GeekF1CareerItemBean geekF1CareerItemBean){
        List<GeekF1CareerExpectItemBean> expects =geekF1CareerItemBean.expects;
        if (!LList.isEmpty(expects)){
            GeekF1CareerExpectItemBean geekF1CareerExpectItemBean =expects.get(0);
            JobIntentBean jobIntentBean =new JobIntentBean();
            jobIntentBean.f1CornerNewTag =geekF1CareerItemBean.f1CornerNewTag;
            jobIntentBean.addIndex =geekF1CareerItemBean.index;
            jobIntentBean.jobIntentId =geekF1CareerExpectItemBean.expectId;
            jobIntentBean.encryptExpectId =geekF1CareerExpectItemBean.encryptExpectId;
            jobIntentBean.locationIndex =geekF1CareerExpectItemBean.location;
            jobIntentBean.locationName = geekF1CareerExpectItemBean.locationName;
            jobIntentBean.positionClassName = geekF1CareerExpectItemBean.positionName;
            jobIntentBean.positionCategory =geekF1CareerExpectItemBean.positionName;
            jobIntentBean.blueCollarPosition = geekF1CareerExpectItemBean.blueCollarExpect;
            jobIntentBean.positionType =geekF1CareerExpectItemBean.positionType;
            jobIntentBean.isMixedPosition = false;
             List<GeekF1SortItemBean>  list =new ArrayList<>();
            List<GeekF1CareerExpectItemBean.SortItemBean> sortList =  geekF1CareerExpectItemBean.sortList;
            if (!LList.isEmpty(sortList)){
                for (GeekF1CareerExpectItemBean.SortItemBean sortItemBean : sortList) {
                    GeekF1SortItemBean f1SortItemBean=new GeekF1SortItemBean(sortItemBean.name,sortItemBean.sortType);
                    list.add(f1SortItemBean);
                }
            }
            jobIntentBean.geekF1SortList =list;
            jobIntentBean.positionClassIndex = geekF1CareerExpectItemBean.position;
            return jobIntentBean;
        }

        return null;
    }



    public static List<JobIntentBean> getGeekProPartimeList() {

        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekPartTimeCombineExpect != null) {

            return getGeekPartTimeExpectList(user);

        }

        return null;
    }

    public static boolean isPartimeExpect(long expectId) {

        List<JobIntentBean> partimeList = new ArrayList<>();
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekPartTimeCombineExpect != null) {

            partimeList = getGeekPartTimeExpectList(user);

            if (!LList.isEmpty(partimeList)) {
                for (JobIntentBean expect : partimeList) {
                    if (expect.jobIntentId == expectId) {
                        return true;
                    }
                }
            }


        }

        return false;
    }


    // 切换推荐tab的类型

    public static void switchMixedExpect(int currentType) {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {
            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {


                if (mixedExpectBean.mixExpectType == MIX_TYPE_SWITCH_RCMD && !LList.isEmpty(mixedExpectBean.switchableList)) {
                    for (ServeGeekMixedExpectBean childMixExpectBean : mixedExpectBean.switchableList) {
                        if (childMixExpectBean.mixExpectType != currentType) {

                            ServeGeekMixedExpectBean defaultMixed = LList.getElement(mixedExpectBean.switchableList, 0);

                            AnalyticsFactory.create().action("mixlist_recommendtab_switch_click")
                                    .param("p2", defaultMixed == null ? 1 : defaultMixed.mixExpectType)
                                    .param("p3", childMixExpectBean.mixExpectType)
                                    .build();

                            sp.putInt(KEY_CURRENT_SWITCH_MIXTYPE, childMixExpectBean.mixExpectType);
                            break;
                        }
                    }
                }
            }
        }
    }


    // 804 虚拟期望混合职位
    public static String getGeekMixedExpectCombinedPosition() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {
            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {
                return mixedExpectBean.combinePosition;
            }
        }
        return null;
    }

    // 815 虚拟期望混合职位
    public static String getGeekMixedExpectBannerTitle() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {
            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {
                return mixedExpectBean.f1BannerTitle;
            }
        }
        return null;
    }


    public static String getGeekMixedExpectBannerContent() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {
            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {
                return mixedExpectBean.f1BannerContent;
            }
        }
        return null;
    }

    // 816 混推类型，1:蓝白期望混推，2:店长期望混推，3:大龄牛人混推，4:本地县城混推 5.902生命之树混推
    public static int getGeekMixedExpectType() {

        JobIntentBean jobIntentBean = getGeekMixedExpect();
        if (jobIntentBean != null) {
            return jobIntentBean.mixExpectType;
        }

        return 0;
    }

    // 816 县城code
    public static long getGeekMixed816SubLocation() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && user.geekInfo.geekMixedExpect != null) {
            ServeGeekMixedExpectBean mixedExpectBean = user.geekInfo.geekMixedExpect;
            if (mixedExpectBean != null) {
                return mixedExpectBean.subLocation;
            }
        }
        return 0;
    }


    // 801 虚拟期望
    public static void saveGeekMixedExpect(JobIntentBean jobIntentBean) {
        UserBean user = UserManager.getLoginUser();
        if (user != null && user.geekInfo != null && jobIntentBean != null) {
            user.geekInfo.geekMixedExpect = convertJonIntentToMixed(jobIntentBean);
        }
    }

    // 801 转换虚拟职位类型
    private static JobIntentBean converMixedToJobIntentBean(ServeGeekMixedExpectBean mixedExpectBean) {
        JobIntentBean jobIntentBean = new JobIntentBean();
        jobIntentBean.jobIntentId = mixedExpectBean.expectId;
        jobIntentBean.encryptExpectId = mixedExpectBean.encryptExpectId;
        jobIntentBean.locationName = mixedExpectBean.locationName;
        jobIntentBean.positionClassName = mixedExpectBean.positionName;
        jobIntentBean.positionType = mixedExpectBean.positionType;
        jobIntentBean.locationIndex = mixedExpectBean.location;
        jobIntentBean.positionType = mixedExpectBean.positionType;
        jobIntentBean.isMixedPosition = true;
        jobIntentBean.supportSwitch = mixedExpectBean.supportSwitch;
        jobIntentBean.mixExpectType = mixedExpectBean.mixExpectType;
        jobIntentBean.locationNameDesc =mixedExpectBean.locationNameDesc;
        return jobIntentBean;
    }

    private static ServeGeekMixedExpectBean convertJonIntentToMixed(JobIntentBean jobIntentBean) {
        ServeGeekMixedExpectBean mixedExpectBean = new ServeGeekMixedExpectBean();
        mixedExpectBean.expectId = jobIntentBean.jobIntentId;
        mixedExpectBean.encryptExpectId = jobIntentBean.encryptExpectId;
        mixedExpectBean.locationName = jobIntentBean.locationName;
        mixedExpectBean.positionName = jobIntentBean.positionClassName;
        mixedExpectBean.positionType = jobIntentBean.positionType;
        mixedExpectBean.location = jobIntentBean.locationIndex;
        return mixedExpectBean;
    }

    public static int getGeekExpectCount() {
        return LList.getCount(getGeekExpectList());
    }

    /**
     * 获取有效的牛人求职意向
     *
     * @return
     */
    @Nullable
    public static List<JobIntentBean> getGeekExpectList(UserBean user) {
        if (!UserManager.isCurrentLoginStatus()
                || UserManager.getUserRole() != ROLE.GEEK
                || user == null
                || user.geekInfo == null) {
            return null;
        }
        GeekInfoBean geekInfo = user.geekInfo;
        //如果是实习生，或者是学生党，则获取此列表
        if (geekInfo.isIntern() || geekInfo.isStudent() || geekInfo.isFreshGraduate()) {
            return geekInfo.internJobIntentList;
        } else {
            return geekInfo.jobIntentList;
        }
    }

    /**
     * 获取有效的牛人全职求职期望
     *
     * @return
     */


    public static List<JobIntentBean> getGeekFullTimeExpectList() {
        UserBean user = UserManager.getLoginUser();
        return getGeekFullTimeExpectList(user);
    }


    public static List<JobIntentBean> getGeekFullTimeExpectList(UserBean user) {
        List<JobIntentBean> fullTimeExpectList = new ArrayList<>();
        List<JobIntentBean> source = getGeekExpectList(user);
        if (!LList.isEmpty(source)) {
            for (JobIntentBean item : source) {
                if (item == null) continue;
                if (item.positionType == 0) {
                    fullTimeExpectList.add(item);
                }
            }
        }
        return fullTimeExpectList;
    }

    /**
     * 获取有效的牛人兼职求职期望
     *
     * @param user
     * @return
     */
    public static List<JobIntentBean> getGeekPartTimeExpectList(UserBean user) {
        List<JobIntentBean> partTimeExpectList = new ArrayList<>();

        List<JobIntentBean> source = getGeekExpectList(user);
        if (!LList.isEmpty(source)) {
            for (JobIntentBean item : source) {
                if (item == null) continue;
                if (item.positionType == 1) {
                    partTimeExpectList.add(item);
                }
            }
        }
        return partTimeExpectList;
    }


    public static List<JobIntentBean> getStudentL2DataForF1() {
        UserBean user = UserManager.getLoginUser();
        return getStuExpectList(user);
    }

    // 1115.607  学生多期望 getStuCombineRcmdExpect 综合期望  + internJobIntentList
    public static List<JobIntentBean> getStuExpectList(UserBean userBean) {

        if (userBean == null || userBean.geekInfo == null) {
            return null;
        }

        GeekInfoBean geekInfo = userBean.geekInfo;
        List<JobIntentBean> stuExpectList = new ArrayList<>();
        if (geekInfo != null && (geekInfo.isIntern() || geekInfo.isStudent() || geekInfo.isFreshGraduate())) {
            if (getStuCombineRcmdExpect() != null) {
                stuExpectList.add(getStuCombineRcmdExpect());
            }
            stuExpectList.addAll(geekInfo.internJobIntentList);
        }

        return stuExpectList;
    }

    public static JobIntentBean getStuCombineRcmdExpect() {


        UserBean user = UserManager.getLoginUser();

        if (user != null && user.geekInfo != null && (user.geekInfo.isIntern() || user.geekInfo.isStudent() || user.geekInfo.isFreshGraduate()) && user.geekInfo.geekStuCombineExpect != null) {
            JobIntentBean stuMixed = converMixedToJobIntentBean(user.geekInfo.geekStuCombineExpect);
            if (stuMixed != null) {
                stuMixed.isStudentRcmd = true;
                return stuMixed;
            }
        }
        return null;


    }


    /**
     * 保存求职期望
     */
    public static void add(UserBean user, JobIntentBean bean) {
        if (user != null && user.geekInfo != null) {
            if (UserManager.isGeekIntern()) {
                if (user.geekInfo.internJobIntentList != null) {
                    user.geekInfo.internJobIntentList.add(bean);
                }
            } else {
                if (user.geekInfo.jobIntentList != null) {
                    user.geekInfo.jobIntentList.add(bean);
                }
            }
        }
    }

    /**
     * 删除求职期望
     *
     * @param user     用户信息
     * @param expectId 求职期望id
     */
    public static void remove(UserBean user, long expectId) {
        if (user != null && user.geekInfo != null) {
            List<JobIntentBean> list;
            if (UserManager.isGeekIntern()) {
                list = user.geekInfo.internJobIntentList;
            } else {
                list = user.geekInfo.jobIntentList;
            }
            if (!LList.isEmpty(list)) {
                int size = list.size();
                for (int i = 0; i < size; i++) {
                    JobIntentBean item = LList.getElement(list, i);
                    if (item != null && expectId == item.jobIntentId) {
                        if (UserManager.isGeekIntern()) {
                            user.geekInfo.internJobIntentList.remove(i);
                        } else {
                            user.geekInfo.jobIntentList.remove(i);
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * 保存当前在f1中选中的期望职位
     */
    public static void saveCurrSelectExpect(JobIntentBean currExpect) {
        if (currExpect == null) return;
        try {
            String string = GsonUtils.getGson().toJson(currExpect);
            if (!TextUtils.isEmpty(string)) {
                sp.putString(CURRENT_SELECT_EXPECT_KEY, string);
            }
        } catch (Exception e) {
            L.d("期望数据解析失败");
        }
    }

    public static JobIntentBean getCurrSelectExpect() {
        String string = sp.getString(CURRENT_SELECT_EXPECT_KEY);
        try {
            if (!TextUtils.isEmpty(string)) {
                return GsonUtils.getGson().fromJson(string, JobIntentBean.class);
            }
        } catch (Exception e) {
            L.d("期望数据解析失败");
        }
        return null;
    }

    public static long getCurrSelectExpectId() {
        long expectId = 0;
        JobIntentBean bean = getCurrSelectExpect();
        if (bean != null) {
            expectId = bean.jobIntentId;
        }
        return expectId;
    }

    /**
     * 获取期望类型的列表(全职，兼职，实习)
     *
     * @return
     */
    public static List<CodeNameFlagBean> getGeekExpectTypeList() {
        UserBean user = UserManager.getLoginUser();
        if (!UserManager.isCurrentLoginStatus()
                || UserManager.getUserRole() != ROLE.GEEK
                || user == null
                || user.geekInfo == null) {
            return null;
        }
        GeekInfoBean geekInfo = user.geekInfo;
        //如果是实习生，或者是学生党，则获取此列表
        if (geekInfo.isIntern() || geekInfo.isStudent() || geekInfo.isFreshGraduate()) {
            return geekInfo.f1TabsConfig;
        }
        return null;
    }

    private static long tempExpectId = 0;
    private static long tempOpenPartTimeId = 0;

    private static long temStuExpectId = 0;

    public static void saveTempExpectId(long expectId) {
        tempExpectId = expectId;
    }

    public static void saveTempStuExpectId(long expectId) {
        TLog.info(TAG,"saveTempStuExpectId expectId=%s",expectId);
        temStuExpectId = expectId;
    }
    public static void saveTempOpenPartTimeId(long openPartTimeId) {
        tempOpenPartTimeId = openPartTimeId;
    }

    /**
     * 获取F1 tab的定位下标
     *
     * @param expectList
     * @return
     */
    public static int getLocateInitIndexByExpectId(List<JobIntentBean> expectList) {
        int index = -1;

        if (tempExpectId > 0) {

            boolean isPartime = false;
            // 检查是否是兼职期望，若果是兼职期望，则此id为兼职二级tab的 id,则应该落到兼职tab下
            List<JobIntentBean> partimeList = GeekExpectManager.getGeekProPartimeList();
            if (partimeList != null && partimeList.size() > 0) {
                int size = partimeList.size();
                for (int i = 0; i < size; i++) {
                    JobIntentBean bean = partimeList.get(i);
                    if (bean.jobIntentId == tempExpectId) {
                        isPartime = true;
                        break;
                    }
                }
            }

            if (!LList.isEmpty(expectList)) {
                List<JobIntentBean> list = new ArrayList<>(expectList);
                int size = list.size();
                for (int i = 0; i < size; i++) {
                    JobIntentBean bean = list.get(i);
                    if (isPartime && bean.jobIntentId == GEEK_PRO_PARTIME_EXPECTID) {
                        index = i;
                    } else if (bean.jobIntentId == tempExpectId) {
                        index = i;
                        GeekExpectManager.clearTempExpectId();
                        break;
                    }
                }

            }
        }else if (tempOpenPartTimeId ==GEEK_PRO_PARTIME_EXPECTID||tempOpenPartTimeId ==GeekF1CareerItemBean.TAB_ID_ODD_JOB){
            if (!LList.isEmpty(expectList)) {
                List<JobIntentBean> list = new ArrayList<>(expectList);
                int size = list.size();
                for (int i = 0; i < size; i++) {
                    JobIntentBean bean = list.get(i);
                    if (bean.jobIntentId == tempOpenPartTimeId) {
                        index = i;
                        GeekExpectManager.clearTempExpectId();
                        tempOpenPartTimeId =0;
                        break;
                    }
                }
            }
        }

        return index;
    }

    public static int getLocateInitIndexByExpectIdPartime(List<JobIntentBean> expectList) {
        int index = -1;
        if (tempExpectId > 0 && !LList.isEmpty(expectList)) {
            List<JobIntentBean> list = new ArrayList<>(expectList);
            int size = list.size();
            for (int i = 0; i < size; i++) {
                JobIntentBean bean = list.get(i);
                if (bean.jobIntentId == tempExpectId) {
                    index = i;
                    break;
                }
            }
        }
        return index;
    }


    public static int getStuLocateInitIndexByExpectId(List<JobIntentBean> expectList) {
        int index = 0;
        if (temStuExpectId > 0 && !LList.isEmpty(expectList)) {
            List<JobIntentBean> list = new ArrayList<>(expectList);
            int size = list.size();
            for (int i = 0; i < size; i++) {
                JobIntentBean bean = list.get(i);
                if (bean.jobIntentId == temStuExpectId) {
                    index = i;
                    break;
                }
            }
        }
        TLog.info(TAG,"getStuLocateInitIndexByExpectId temStuExpectId=%s; index=%s",temStuExpectId,index);
        return index;
    }

    /**
     * 获取期望发布页面的发布类型
     * 0 - 无
     * 1 - 仅能发布全职
     * 2 - 仅能发布兼职
     * 3 - 自由发布
     *
     * @return
     */
    public static int getExpectPostCreateType() {
        UserBean user = UserManager.getLoginUser();
        List<JobIntentBean> geekExpectList = GeekExpectManager.getGeekExpectList(user);
        List<JobIntentBean> fullTimeExpectList = new ArrayList<>();
        List<JobIntentBean> partTimeExpectList = new ArrayList<>();

        if (!LList.isEmpty(geekExpectList)) {
            for (JobIntentBean item : geekExpectList) {
                if (item == null) continue;
                if (item.positionType == 0) {
                    fullTimeExpectList.add(item);
                } else {
                    partTimeExpectList.add(item);
                }
            }
        }
        int fullTimeExpectPostCount = fullTimeExpectList.size();
        int partTimeExpectPostCount = partTimeExpectList.size();

        // 将虚拟兼职期望作为单独期望进行处理
        ServeGeekMixedExpectBean virtualPartTimeExpect = GeekExpectManager.getGeekProVirtualPartTimeExpect();
        if (virtualPartTimeExpect != null && partTimeExpectPostCount == 0) {
            partTimeExpectPostCount = 1;
        }

        int createExpectType = 0;
        if (fullTimeExpectPostCount < MAX_SOCIAL_INTENT_COUNT || partTimeExpectPostCount == 0) {
            if (fullTimeExpectPostCount == 0 && partTimeExpectPostCount > 0) {
                createExpectType = PostExpectType.ONLY_FULL_TIME;
            } else if (fullTimeExpectPostCount >= MAX_SOCIAL_INTENT_COUNT) {
                createExpectType = PostExpectType.ONLY_PART_TIME;
            } else if (fullTimeExpectPostCount > 0 && partTimeExpectPostCount > 0 && partTimeExpectPostCount <= MAX_PART_TIME_INTENT_COUNT) {
                createExpectType = PostExpectType.ONLY_FULL_TIME;
            } else {
                createExpectType = PostExpectType.FREE_ALLOW;
            }
        }

        return createExpectType;
    }

    public static long getStuLastExpectId() {
        return temStuExpectId;
    }


    public static void clearTempExpectId() {
        tempExpectId = 0;
    }

}
