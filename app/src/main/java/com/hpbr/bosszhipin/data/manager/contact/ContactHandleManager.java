package com.hpbr.bosszhipin.data.manager.contact;

import android.annotation.SuppressLint;
import android.os.SystemClock;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.db.async.DbOpHandlerThread;
import com.hpbr.bosszhipin.data.db.async.DbOpParam;
import com.hpbr.bosszhipin.data.db.async.DbOpType;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.dao.DaoFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.workorder.LogDiagnoseManager;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.ContactBaseInfoListResponse;
import net.bosszhipin.api.bean.ServerAddFriendBean;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import message.handler.analysis.ChatAnalyzer;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDaoFactory;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2023/7/26 21:29
 *     desc   : 联系人操作辅助类
 *     version: 11.15
 * </pre>
 */
public class ContactHandleManager {

    public static final String TAG = "ContactHandleManager";
    public static final int KEY_SAVE_CONTACT_ERROR = 100086;

    private final List<ContactBean> insertList = new ArrayList<>();//待插入数据
    private final List<ContactBean> insertDZList = new ArrayList<>();//店长新增联系人数据
    private final List<ContactBean> updateList = new ArrayList<>();//待更新数据
    private final List<ContactBean> deleteList = new ArrayList<>();//待删除数据
    private final Set<String> remoteContacts = new HashSet<>();//联系人set，后面删除使用
    public  static final List<ContactBean> localCachetList = new ArrayList<>();//本地未重组的缓存数据
    public static final String key_delete_messages_error_sp = "key_delete_messages_error";
    public static final String key_delete_contact_error_sp = "key_delete_contact_error_sp";
    private final ContactBaseInfoListResponse baseInfo;
    public long responseStartTime = 0;

    public ContactHandleManager(ContactBaseInfoListResponse baseInfo) {
        this.baseInfo = baseInfo;
        //重装数组
        handleInsertOrUpdateListByDiffContacts();
    }

    //-----------------------------------------------start-----------------------------------------------


    public void insertAndUpdateCacheV2() {
        ContactManager contactManager = ContactManager.getInstance();
        //更新缓存
        contactManager.insertAndUpdateCache(insertList, updateList, baseInfo.identity);
        //更新完缓存，通知F2刷新
        ContactManager.getInstance().refreshContacts();
    }


    /**
     * 批量插入和更新联系人
     * 异步 数据库操作
     */
    public void batchInsertAndUpdateContactsDaoV2() {
        //start
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_CONTACT_NEW_TRANSACTION_START).sendToTarget();
        //批量插入联系人
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_CONTACT_INSERT_CONTACTS, new DbOpParam<List<ContactBean>, Void>(insertList, null)).sendToTarget();
        //批量更新数据库
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_CONTACT_UPDATE_CONTACTS, new DbOpParam<List<ContactBean>, Void>(updateList, null)).sendToTarget();
        //end
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_CONTACT_NEW_TRANSACTION_END_V2,LList.getCount(insertList),LList.getCount(updateList)).sendToTarget();
    }


    /**
     * 异步删除联系人和消息
     */
    public static void deleteContactsAndMessagesDaoV2(List<ContactBean> deleteList,int role) {
        if (LList.getCount(deleteList) > 0) {
            //删除联系人
            ContactManager.getInstance().batchDeleteContacts(deleteList, role);
            //异步删除联系人消息 「非常耗时」
            deleteMessagesByContacts(deleteList, role);
        }
    }

    /**
     * 删除联系人的消息
     * 数据库操作
     * --多线程问题：--
     * 1、一边来消息存储，一边删除消息，目前测试着不会失败
     */
    @SuppressLint("twl_thread")
    public static void deleteMessagesByContacts(List<ContactBean> deleteList,int role){
        if (AndroidDataStarGray.getInstance().isMessageDelV2()) return;
        if(deleteList.isEmpty()) return;
        //删除消息 「最耗时、单独线程再处理、不影响上面联系人的用户感知」
        //删除比较耗时，是否可以用个标志位，先更新状态，之后再慢慢去真实删除
        AppThreadFactory.createThread(new Runnable() {
            @Override
            public void run() {
                try {
                    MessageDao messageDao = MessageDaoFactory.getMessageDao();
                    long messageCount = messageDao.getMessageCount();
                    //消息是否删除成功
                    SpManager.get().user().edit().putLong(key_delete_messages_error_sp, messageCount).apply();
                    long startTimeDel = SystemClock.elapsedRealtime();

                    if (UserManager.getUserRole(role) != UserManager.getUserRole()) {
                        TLog.info(TAG, "user switch role when check delete message");
                        return;
                    }
                    int deleteCount = LList.getCount(deleteList);
                    TLog.info(ContactHandleManager.TAG, "start delete messages messageTotalCount== %d deleteCount = %d", messageCount,deleteCount);
                    //db操作 消息本来删除就慢，很多消息批量删除会造成一直失败删不调，因此一部分一部分删除
                    for (int i = 0; i < deleteCount; i=i+200) {
                        List<ContactBean> newDeleteList;
                        if(deleteList.size() >= i+200){
                            newDeleteList = deleteList.subList(i,i+200);
                        }else{
                            newDeleteList = deleteList.subList(i,deleteCount);
                        }
                        messageDao.batchDeleteMessageByContacts(newDeleteList);
                        TLog.info(TAG, "for delete msg  now i==%d", i);
                    }
                    long timeEnd = SystemClock.elapsedRealtime();
                    long messageCountEnd = messageDao.getMessageCount();
                    TLog.info(ContactHandleManager.TAG, "delete messages finish deleteList==%d, delete messages==%d,  time:%d", deleteList.size(), (messageCount - messageCountEnd), timeEnd - startTimeDel);
                    /*删除消息时间超过3s才上报*/
                    if ((messageCount - messageCountEnd )> 0 && (timeEnd - startTimeDel) > 3000) {
                        ChatAnalyzer.reportNew(ChatAnalyzer.BATCH_DELETE_MESSAGE_TIME).
                                p2("deleteList=="+LList.getCount(deleteList))
                                .p3("deleteMsg=="+String.valueOf(messageCount - messageCountEnd))
                                .p4("time=="+String.valueOf(timeEnd - startTimeDel))
                                .report();
                    }
                    SpManager.get().user().edit().remove(key_delete_messages_error_sp).apply();
                    ContactDoctorFactory.ContactDel.complete();
                } catch (Exception e) {
                    TLog.error(TAG,e,"base info delete message");
                    ChatAnalyzer.reportNew(ChatAnalyzer.BASE_INFO_CRASH).p2("base info delete message").p3(e.getMessage()).report();
                }
            }
        }).start();
    }

    //-----------------------------------------------end-----------------------------------------------

    /**
     * 处理新增联系人数组、新增店长联系人数组、更新联系人数组
     **/
    public void handleInsertOrUpdateListByDiffContacts() {
        int countBaseInfo = LList.getCount(baseInfo.baseInfoList);
        ContactManager contactManager = ContactManager.getInstance();
        long countLocal = DaoFactory.getContactDao().getContactCount(UserManager.getUserRole().get());//本地是否有联系人
        ArrayList<ContactBean> contactBeans = new ArrayList<>();
        int tempTopCount = 0;
        for (int i = 0; i < countBaseInfo; i++) {
            ServerAddFriendBean serverAddFriendBean = LList.getElement(baseInfo.baseInfoList, i);
            if (null == serverAddFriendBean) continue;
            ContactBean remote = new ContactBean().fromServerContactBaseInfoBean(serverAddFriendBean, baseInfo.userId, baseInfo.identity);
            ContactBean local = contactManager.queryContactByFriendId(remote.friendId, baseInfo.identity, remote.friendSource);

            /* 情况：BOSS数据重组一半，切到牛人（应该更新的会变成插入，缓存清了），数据重组完，切回来，错误的插入数据会被插入，造成重复。
            baseInfo.identity != UserManager.getUserRole().get()可以拦截*/
            if(baseInfo.identity == UserManager.getUserRole().get()){
                if (local != null) {//这几个字段是全量接口返回的，批量接口里面没有，但是都属于服务端字段
                    remote.id = local.id;
                    remote.addFullInfoData(local);
                    if(remote.friendId>0){
                        updateList.add(remote);
                    }
                } else {
                    if (remote.friendSource == ContactBean.FROM_DIAN_ZHANG) {
                        ///新增本地店长联系人 &  本地有联系人
                        if (countLocal > 0 && remote.friendId>0) {
                            insertDZList.add(remote);
                        }
                    }
                    if(remote.friendId>0){
                        insertList.add(remote);
                    }
                }
            }

            /*这里不管身份切不切都得加，因为要和本地做对比生成删除数组，少了的话会多删本地数据*/
            remoteContacts.add(makeKey(remote));
            contactBeans.add(remote);
            //置顶职位埋点
            if (remote.isTop) {
                tempTopCount++;
            }
        }
        //判断getBaseInfo接口同步到数据>60&可能是其它端设置的
        analyGetBaseInfoMoreThanSixtyTop(tempTopCount);
        ContactDoctorFactory.checkOverlappingFriend(contactBeans);
        LogDiagnoseManager.getInstance().handleContactList(contactBeans, localCachetList);
    }



    //创建好友key
    private String makeKey(ContactBean contactBean) {
        return contactBean.friendId + "_" + contactBean.friendSource;
    }

    //判断getBaseInfo接口同步到数据>60&可能是其它端设置的
    private void analyGetBaseInfoMoreThanSixtyTop(int tempTopCount) {
        if (tempTopCount > 60) {
            ApmAnalyzer.create()
                    .action("action_temp",
                            "topCountError")
                    .p2(String.valueOf(tempTopCount))
                    .p3("getBaseInfoList")
                    .report();
        }
    }


    /**
     * 1、每天8点以后更新
     * 2、时间跨度超过12个小时
     */
    public static boolean isOverTodayHour8(long lastTime) {
        long curTime = System.currentTimeMillis();
        // 每天8点以后更新
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 8);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        long hour8 = calendar.getTimeInMillis();
        //L.d(TAG, "lastTime==" + DateUtil.formatterDate(lastTime, "yyyy年MM月dd日 HH:mm") + ",curTime==" + DateUtil.formatterDate(curTime, "yyyy年MM月dd日 HH:mm") + ",hour8==" + DateUtil.formatterDate(hour8, "yyyy年MM月dd日 HH:mm") + ",diffTime==" + (curTime - lastTime) / (1000 * 60 * 60));
        //lastTime==1970年01月01日 08:00
        //curTime==2023年09月07日 13:45
        //hour8==2023年09月07日 08:00
        if (lastTime < hour8 && curTime >= hour8) {
            return true;
        }
        return curTime - lastTime >= 1000 * 60 * 60 * 12;
    }


    public void checkSameContactAndDeleteNotInCacheV2() {
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_CHECK_SAME_DELETE_NOT_CACHE_V2, baseInfo.identity,new DbOpParam<List<ContactBean>, Void>(insertList, null)).sendToTarget();
    }

}
