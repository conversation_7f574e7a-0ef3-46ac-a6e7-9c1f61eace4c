package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.monch.lbase.orm.db.model.ColumnsValue;

import java.util.List;

public interface IGroupInfoDao {

    @SyncDataBase
    void insert(GroupInfoBean bean);

    @SyncDataBase
    void insertAll(List<GroupInfoBean> list);

    List<GroupInfoBean> queryGroupList();

    GroupInfoBean queryGroupInfoByGroupId(long groupId);

    /**
     * 传入的参数已经是最新的
     **/
    @SyncDataBase
    long updateGroupInfoAllFiled(GroupInfoBean bean);

    /**
     * 传入的参数已经是最新的
     **/
    @SyncDataBase
    long updateGroupInfoServerFiled(GroupInfoBean bean);


    int updateGroupInfo(long groupId, ColumnsValue columnsValue);

    @SyncDataBase
    long updateGroupInfo(GroupInfoBean groupInfoBean);

    @SyncDataBase
    int delete(long groupId);

    @SyncDataBase
    int deleteAll();

    @SyncDataBase
    void repairLastMid();
}
