package com.hpbr.bosszhipin.data.db.dao;

import android.database.sqlite.SQLiteDatabase;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.monch.lbase.orm.db.model.ColumnsValue;
import com.monch.lbase.orm.db.model.ConflictAlgorithm;

import java.util.List;


/**
 * Created by wa<PERSON><PERSON> on 2018/4/23.
 */

class GroupInfoDaoImpl implements IGroupInfoDao {


    @Override
    public void insert(GroupInfoBean bean) {
        App.get().db().save(bean);
    }

    @Override
    public void insertAll(List<GroupInfoBean> list) {
        App.get().db().save(list);
    }

    @Override
    public List<GroupInfoBean> queryGroupList() {
        QueryBuilder qb = new QueryBuilder(GroupInfoBean.class);
        qb.where("myUid=" + UserManager.getUID(), null);
        List<GroupInfoBean> list = App.get().db().query(qb);
        return list;
    }

    @Override
    public GroupInfoBean queryGroupInfoByGroupId(long groupId) {
        QueryBuilder qb = new QueryBuilder(GroupInfoBean.class);
        qb.where("myUid=" + UserManager.getUID() + " and groupId = " + groupId, null);
        List<GroupInfoBean> list = App.get().db().query(qb);
        if (list == null || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 传入的参数已经是最新的
     **/
    @Override
    public long updateGroupInfoAllFiled(GroupInfoBean bean) {
        return App.get().db().update(bean);
    }

    /**
     * 传入的参数已经是最新的
     **/
    @Override
    public long updateGroupInfoServerFiled(GroupInfoBean bean) {
        return App.get().db().update(bean);
    }

    @Override
    public int updateGroupInfo(long groupId, ColumnsValue columnsValue) {
        GroupInfoBean bean = queryGroupInfoByGroupId(groupId);
        if (bean != null) {
            return App.get().db().update(bean, columnsValue, ConflictAlgorithm.Replace);
        }
        return 0;
    }

    @Override
    public long updateGroupInfo(GroupInfoBean groupInfoBean){
        return 1;
    }
    @Override
    public int delete(long groupId) {
        return App.get().db().delete(GroupInfoBean.class, "myUid=" + UserManager.getUID() + " and groupId = " + groupId);
    }

    @Override
    public int deleteAll() {
        return App.get().db().delete(GroupInfoBean.class, "myUid=" + UserManager.getUID());
    }

    @Override
    public void repairLastMid() {
        SQLiteDatabase database = null;
        try {
            database = App.get().db().getWritableDatabase();
            database.beginTransaction();
            database.execSQL("UPDATE GroupInfo SET lastMsgId = 0");
            database.setTransactionSuccessful();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != database) {
                database.endTransaction();
            }
        }
    }
}
