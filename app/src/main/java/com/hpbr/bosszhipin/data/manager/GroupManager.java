package com.hpbr.bosszhipin.data.manager;

import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.dao.DaoFactory;
import com.hpbr.bosszhipin.data.db.dao.IGroupInfoDao;
import com.hpbr.bosszhipin.data.db.dao.IGroupMemberDao;
import com.hpbr.bosszhipin.data.db.dao.QuitMemberDao;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;
import com.hpbr.bosszhipin.data.db.entry.GroupUserCardBean;
import com.hpbr.bosszhipin.data.db.entry.QuitMemberBean;
import com.hpbr.bosszhipin.data.manager.contact.GroupCache;
import com.hpbr.bosszhipin.data.manager.contact.GroupMemberCache;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.grivatation.GravitationFilter;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.grivatation.OutSourceFilter;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.model.ColumnsValue;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.EditGroupUserCardRequest;
import net.bosszhipin.api.GetGroupContactListResponse;
import net.bosszhipin.api.GetGroupMemberInfoRequest;
import net.bosszhipin.api.GetGroupMemberInfoResponse;
import net.bosszhipin.api.GetGroupMemberListRequest;
import net.bosszhipin.api.GetGroupMemberListResponse;
import net.bosszhipin.api.GetJoinedGroupContactListRequest;
import net.bosszhipin.api.GetUserGroupCardRequest;
import net.bosszhipin.api.GetUserGroupCardResponse;
import net.bosszhipin.api.GroupInfoRequest;
import net.bosszhipin.api.GroupInfoResponse;
import net.bosszhipin.api.GroupRemoveMemberRequest;
import net.bosszhipin.api.JoinGroupChatRequest;
import net.bosszhipin.api.JoinGroupChatResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.SyncGroupMembersRequest;
import net.bosszhipin.api.SyncGroupMembersResponse;
import net.bosszhipin.api.bean.ServerGroupInfoBean;
import net.bosszhipin.api.bean.ServerGroupMemberBean;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import message.handler.MessageUtils;
import message.handler.dao.MessageDaoFactory;
import message.handler.receiver.RGroupMessageHandler;

/**
 * Created by wangtian on 2018/4/23.
 */

public class GroupManager {

    public static final String SP_HAS_UPDATE_REPLAY = "SP_HAS_UPDATE_REPLAY";
    public static final String SP_GAVE_CRICLE_HAS_UPDATE_REPLAY = "SP_GAVE_CRICLE_HAS_UPDATE_REPLAY";
    private static GroupManager mInstance = new GroupManager();
    private GroupCache groupCache;
    private GroupMemberCache groupMemberCache;
    private IGroupInfoDao groupInfoDao;
    private IGroupMemberDao groupMemberDao;
    private QuitMemberDao quitMemberDao;

    private static final String GROUP_USER_CARD_KEY = Constants.PREFIX + ".GROUP_USER_CARD_KEY";

    public static final int GROUP_NOT_EXIST = 404;

    public static void sendGroupContactReceiver() {//F2发生变更
        ContactManager.getInstance().refreshContacts();
    }

    public static void sendGroupInfoRefreshReceiver() {//群信息&群组成员信息
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_REFRESH_GROUP_INFO);
        App.get().sendBroadcast(intent);
    }


    private GroupManager() {
        groupCache = GroupCache.getmInstance();
        groupMemberCache = GroupMemberCache.getmInstance();
        groupInfoDao = DaoFactory.getGroupInfoDao();
        groupMemberDao = DaoFactory.getGroupMemberDao();
        quitMemberDao = DaoFactory.getDao(QuitMemberDao.class);
    }

    public static GroupManager getInstance() {
        return mInstance;
    }


    /**
     * 包含退出的群
     **/
    public List<GroupInfoBean> getGroupInfoListContainQuit() {
        return groupCache.getGroupInfoList();
    }

    //判断是否不支持的类型
    private boolean isUnSupportType(GroupInfoBean groupInfoBean) {
        //校友群不支持在f2显示
        return groupInfoBean != null && (groupInfoBean.source == 6 || groupInfoBean.source == 7);
    }

    //业务层获得的群聊集合
    public List<GroupInfoBean> getGroupInfoList() {
        List<GroupInfoBean> list = groupCache.getGroupInfoList();
        List<GroupInfoBean> data = new ArrayList<>();
        if (list != null) {
            for (GroupInfoBean bean : list) {
                //校友群不显示在f2
                if (isUnSupportType(bean)) continue;
                if (!bean.isQuit) {
                    data.add(bean);
                }
            }
        }
        return data;
    }


    private final GravitationFilter gravitationFilter = new GravitationFilter();

    private final OutSourceFilter outSourceFilter = new OutSourceFilter();

    //获得默认群聊: 不包含 引力波，悟空计划等
    public List<GroupInfoBean> getOnlyDefaultGroup() {
        List<GroupInfoBean> list = groupCache.getGroupInfoList();
        List<GroupInfoBean> data = new ArrayList<>();
        for (GroupInfoBean groupInfoBean : list) {
            if (groupInfoBean == null) continue;
            //校友群不显示在f2
            if (isUnSupportType(groupInfoBean)) continue;
            //悟空计划的群聊
            if (outSourceFilter.isOutSourceGroup(groupInfoBean)) continue;
            //引力波不处理
            if (gravitationFilter.isGravitation(groupInfoBean)) continue;
            data.add(groupInfoBean);
        }
        return data;
    }


    //获得悟空计划的联系人
    public List<GroupInfoBean> getOutSourceGroup() {
        List<GroupInfoBean> list = groupCache.getGroupInfoList();
        final List<GroupInfoBean> data = new ArrayList<>();
        for (GroupInfoBean groupInfoBean : list) {
            if (groupInfoBean == null) continue;
            //校友群不显示在f2
            if (isUnSupportType(groupInfoBean)) continue;
            if (outSourceFilter.isOutSourceGroup(groupInfoBean)) {
                data.add(groupInfoBean);
            }
        }
        return data;
    }


    public GroupInfoBean getGroupInfo(long groupId) {
        return groupCache.getGroupInfoByGroupid(groupId);
    }

    public String getGid(long groupId) {
        GroupInfoBean groupInfoBean = getGroupInfo(groupId);
        if (groupInfoBean != null) {
            return groupInfoBean.gid;
        }
        return null;
    }


    private void updateGroupInfoDao(long groupId, ColumnsValue cv) {
        //处理GroupDao
        groupInfoDao.updateGroupInfo(groupCache.getGroupInfoByGroupid(groupId));
        groupInfoDao.updateGroupInfo(groupId, cv);
        GroupManager.sendGroupContactReceiver();
    }

    public String getMemberUid(long groupId, long userId) {
        GroupMemberBean groupMemberBean = GroupManager.getInstance().getGroupMember(groupId, userId);
        if (groupMemberBean != null) {
            return groupMemberBean.uid;
        }
        return null;
    }


    //更新草稿
    public void updateDraftWithTime(GroupInfoBean groupInfoBean) {
        if (groupInfoBean == null) return;
        String[] keys = new String[]{"draft",
                "lastChatTime",
                "quoteMsgId"};
        Object[] objects = new Object[]{groupInfoBean.draft,
                groupInfoBean.lastChatTime,
                groupInfoBean.quoteMsgId};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(() -> updateGroupInfoDao(groupInfoBean.groupId, cv));
    }

    public long updateGroupName(final long groupId, String value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.name = value;
        }
        String[] keys = new String[]{"name"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }

    public long updateGroupFriendReplay(final long groupId, boolean value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.friendHasReplay = value;
        }
        String[] keys = new String[]{"friendHasReplay"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }


    public long updateGroupNotice(final long groupId, String value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.notice = value;
        }
        String[] keys = new String[]{"notice"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }

    public long updateGroupNoticeTopStatus(final long groupId, int value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.noticeTopStatus = value;
        }
        return 1;
    }

    public long updateGroupIntroduction(final long groupId, String value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.introduction = value;
        }
        String[] keys = new String[]{"introduction"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }

    public long updateGroupWatch(final long groupId, int value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.watch = value;
        }
        String[] keys = new String[]{"watch"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }

    public long updateGroupUpdateTime(final long groupId, long updateTime) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.updateTime = updateTime;
        }
        String[] keys = new String[]{"updateTime"};
        Object[] objects = new Object[]{updateTime};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }

    public long updateGroupSilent(final long groupId, int value) {
        GroupInfoBean result = groupCache.getGroupInfoByGroupid(groupId);
        if (result != null) {
            result.silent = value;
        }
        String[] keys = new String[]{"silent"};
        Object[] objects = new Object[]{value};
        final ColumnsValue cv = new ColumnsValue(keys, objects);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                updateGroupInfoDao(groupId, cv);
            }
        });
        return 1;
    }


    public long insertOrUpdateAllFile(final GroupInfoBean bean) {
        return insertOrUpdateAllFileWithFresh(bean, false);
    }

    public long insertOrUpdateAllFileWithFresh(final GroupInfoBean bean) {
        return insertOrUpdateAllFileWithFresh(bean, true);
    }

    public void repairLastMid() {
        List<GroupInfoBean> groupInfoList = getGroupInfoList();
        for (GroupInfoBean groupInfoBean : groupInfoList) {
            groupInfoBean.lastMsgId = 0;
        }
        IGroupInfoDao groupInfoDao = DaoFactory.getGroupInfoDao();
        if (groupInfoDao != null) {
            groupInfoDao.repairLastMid();
        }
    }

    /**
     * 更新db后 自动刷新F2列表
     */
    private long insertOrUpdateAllFileWithFresh(final GroupInfoBean bean, boolean needFresh) {
        if (bean.groupId == 0) {
            ToastUtils.showText("插入非法群id");
            return 0;
        }
        long result = groupCache.insertOrUpdateAllFiled(bean);

        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                GroupInfoBean local = groupInfoDao.queryGroupInfoByGroupId(bean.groupId);
                if (local != null) {
                    bean.id = local.id;
                    groupInfoDao.updateGroupInfoAllFiled(bean);
                } else {
                    groupInfoDao.insert(bean);
                }
                if (needFresh) {
                    sendGroupContactReceiver();
                }
            }
        });
        return result;
    }

    public void load() {
        groupCache.load();
    }


    public void clean() {
        groupCache.clean();
        userCardBean = null;
    }

    public long insertOrUpdateServerField(final GroupInfoBean bean) {
        return insertOrUpdateServerFieldWithFresh(bean, false);
    }

    public long insertOrUpdateServerFieldWithFresh(final GroupInfoBean bean) {
        return insertOrUpdateServerFieldWithFresh(bean, true);
    }

    private long insertOrUpdateServerFieldWithFresh(final GroupInfoBean bean, boolean needFresh) {
        if (bean.groupId == 0) {
            T.ss("插入非法群id");
            return 0;
        }
        final GroupInfoBean result = groupCache.insertOrUpdateServerField(bean);

        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                GroupInfoBean local = groupInfoDao.queryGroupInfoByGroupId(result.groupId);
                if (local != null) {
                    result.id = local.id;
                    groupInfoDao.updateGroupInfoServerFiled(result);
                } else {
                    groupInfoDao.insert(result);
                }
                if (needFresh) {
                    sendGroupContactReceiver();
                }
            }
        });
        return 1;
    }


    /**
     * 获取群信息列表
     * 同联系人列表每天获取一次的策略
     **/
    public void loadAllGroupInfoFromServer(final IRefreshCallback<List<GroupInfoBean>> iRefreshCallback) {
        GetJoinedGroupContactListRequest request = new GetJoinedGroupContactListRequest(new ApiRequestCallback<GetGroupContactListResponse>() {
            @Override
            public void handleInChildThread(ApiData<GetGroupContactListResponse> data) {
                DaoFactory.upsertAllGroupList(data.resp);
                super.handleInChildThread(data);
            }

            @Override
            public void onSuccess(ApiData<GetGroupContactListResponse> data) {
                if (data.resp.groups == null) {
                    if (iRefreshCallback != null) {
                        iRefreshCallback.onRefreshSuccess(GroupManager.getInstance().getGroupInfoList());
                    }
                    return;
                }

                List<ServerGroupInfoBean> groupList = handleGroupChatList(data);


                Set<Long> groupIds = new HashSet<>();
                for (ServerGroupInfoBean bean : groupList) {
                    groupIds.add(bean.groupId);
                    insertOrUpdateServerField(GroupInfoBean.fromServerGroupInfoBean(bean));
                }

                //处理自己被踢出群的操作
                List<GroupInfoBean> cacheList = GroupManager.getInstance().getGroupInfoListContainQuit();
                if (cacheList != null) {
                    for (GroupInfoBean bean : cacheList) {
                        if (bean.myUid == UserManager.getUID() && !groupIds.contains(bean.groupId)) {
                            bean.isQuit = true;
                            deleteGroup(bean.groupId);
                        }
                    }
                }

                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshSuccess(GroupManager.getInstance().getGroupInfoList());
                }

                sendGroupContactReceiver();
                ContactDoctorFactory.markMigrationGroup();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshFail(reason);
                }
            }
        });
        HttpExecutor.execute(request);
    }

    @NonNull
    private static List<ServerGroupInfoBean> handleGroupChatList(ApiData<GetGroupContactListResponse> data) {
        List<ServerGroupInfoBean> groupList = new ArrayList<>();

        //群聊
        if (data.resp.groups != null) {
            groupList.addAll(data.resp.groups);
        }
        //引力波
        if (data.resp.gravityGroups != null) {
            groupList.addAll(data.resp.gravityGroups);
        }
        //校友群
        if (!LList.isEmpty(data.resp.alumnusGroups)) {
            groupList.addAll(data.resp.alumnusGroups);
        }
        //双叶草群
        if (!LList.isEmpty(data.resp.directGroups)) {
            groupList.addAll(data.resp.directGroups);
        }

        // 悟空项目外包
        if (!LList.isEmpty(data.resp.wukongGroups)) {
            groupList.addAll(data.resp.wukongGroups);
        }

        //「羚羊计划」
        if (!LList.isEmpty(data.resp.antelopeGroups)) {
            groupList.addAll(data.resp.antelopeGroups);
        }

        // 安心保群聊
        if (!LList.isEmpty(data.resp.anxinbaoGroups)) {
            groupList.addAll(data.resp.anxinbaoGroups);
        }

        if (!LList.isEmpty(data.resp.resumeTutoredGroups)) {
            groupList.addAll(data.resp.resumeTutoredGroups);
        }
        return groupList;
    }

    /**
     * 刷新群全部信息
     * 进聊天设置界面的时候调用
     **/
    public void loadGroupFullInfoFromServer(final long groupId, final IRefreshCallback<GroupInfoBean> loadGroupCallback) {
        GroupInfoRequest request = new GroupInfoRequest(new ApiRequestCallback<GroupInfoResponse>() {

            @Override
            public void onSuccess(ApiData<GroupInfoResponse> data) {
                if (data.resp.memberStatus == 0) {//处理退出群
                    GroupInfoBean infoBean = getGroupInfo(groupId);
                    if (infoBean != null) {
                        infoBean.isQuit = true;
                        deleteGroup(infoBean.groupId);
                    }
                } else {
                    ServerGroupInfoBean serverGroupInfoBean = data.resp.group;
                    if (serverGroupInfoBean != null) {
                        insertOrUpdateServerFieldWithFresh(GroupInfoBean.fromServerGroupInfoBean(serverGroupInfoBean));

                        List<ServerGroupMemberBean> serverGroupMemberBeanList = serverGroupInfoBean.members;
                        if (serverGroupMemberBeanList == null) {
                            serverGroupMemberBeanList = new ArrayList<>();
                        }

                        Set<Long> allIds = new HashSet<>();
                        final List<GroupMemberBean> quitGroupMemberList = new ArrayList<>();
                        final List<GroupMemberBean> groupMemberBeanList = new ArrayList<>();
                        final List<GroupMemberBean> localMemberBeanList = groupMemberCache.getGroupMembers(groupId);
                        if (groupMemberCache.getGroupMembers(groupId) != null) {
                            localMemberBeanList.addAll(groupMemberCache.getGroupMembers(groupId));
                        }

                        for (ServerGroupMemberBean serverGroupMemberBean : serverGroupMemberBeanList) {
                            allIds.add(serverGroupMemberBean.userId);
                            groupMemberBeanList.add(GroupMemberBean.fromServerBean(serverGroupMemberBean, groupId));
                        }

                        for (GroupMemberBean groupMemberBean : localMemberBeanList) {
                            if (!allIds.contains(groupMemberBean.userId)) {
                                groupMemberBean.quit = true;
                                quitGroupMemberList.add(groupMemberBean);
                            }
                        }
                        groupMemberCache.put(groupId, groupMemberBeanList);
                        App.getDbExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                groupMemberDao.delete(groupId);
                                groupMemberDao.insertAll(groupMemberBeanList);
                            }
                        });
                    }
                }


                if (loadGroupCallback != null) {
                    loadGroupCallback.onRefreshSuccess(groupCache.getGroupInfoByGroupid(groupId));
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (loadGroupCallback != null) {
                    loadGroupCallback.onRefreshFail(reason);
                }
            }
        });
        request.gid = GroupManager.getInstance().getGid(groupId);
        request.execute();
        sendGroupInfoRefreshReceiver();
    }

    public void SyncGroupMembers(final long groupId, final IRefreshCallback<List<GroupMemberBean>> iRefreshGroupCallback) {
        GroupInfoBean groupInfo = GroupManager.getInstance().getGroupInfo(groupId);
        if (groupInfo != null) {
            SyncGroupMembers(groupId, groupInfo.gid, iRefreshGroupCallback);
        } else {
            ToastUtils.showText("数据错误");
        }

    }

    public void SyncGroupMembers(final List<String> gids, final int index) {
        if (index >= LList.getCount(gids)) return;
        String gid = LList.getElement(gids, index);
        if (TextUtils.isEmpty(gid)) return;
        SyncGroupMembers(gid, new IRefreshCallback<List<GroupMemberBean>>() {
            final int nextIndex = index + 1;

            @Override
            public void onRefreshFail(ErrorReason errorMsg) {
                SyncGroupMembers(gids, nextIndex);
            }

            @Override
            public void onRefreshSuccess(List<GroupMemberBean> result) {
                SyncGroupMembers(gids, nextIndex);
            }
        });
    }
    /**
     * 刷新群成员列表
     * 进聊天界面的时候调用
     * group.syncMembers接口
     **/
    public void SyncGroupMembers(final String gid, final IRefreshCallback<List<GroupMemberBean>> iRefreshGroupCallback) {
        final SyncGroupMembersRequest request = new SyncGroupMembersRequest(new ApiRequestCallback<SyncGroupMembersResponse>() {

            @Override
            public void handleInChildThread(ApiData<SyncGroupMembersResponse> data) {
                super.handleInChildThread(data);
            }

            @Override
            public void onSuccess(ApiData<SyncGroupMembersResponse> data) {
                long groupId = data.resp.groupId;
                if (data.resp.status == 2) {
                    deleteGroup(groupId);
                    if (iRefreshGroupCallback != null) {
                        iRefreshGroupCallback.onRefreshFail(new ErrorReason(GROUP_NOT_EXIST, "该群已解散"));
                    }
                    return;
                }
                List<ServerGroupMemberBean> serverGroupMemberBeanList = data.resp.newMembers;
                final List<GroupMemberBean> groupMemberBeanList = new ArrayList<>();

                if (serverGroupMemberBeanList != null) {
                    for (ServerGroupMemberBean bean : serverGroupMemberBeanList) {
                        bean.groupId = groupId;
                        bean.gid = gid;
                        groupMemberBeanList.add(GroupMemberBean.fromServerBean(bean, groupId));
                    }
                }

                groupMemberCache.put(groupId, groupMemberBeanList);

                if (iRefreshGroupCallback != null) {
                    iRefreshGroupCallback.onRefreshSuccess(groupMemberCache.getGroupMembers(groupId));
                }

                sendGroupInfoRefreshReceiver();

                App.getDbExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        groupMemberDao.delete(groupId);
                        groupMemberDao.insertAll(groupMemberBeanList);
                    }
                });

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (iRefreshGroupCallback != null) {
                    iRefreshGroupCallback.onRefreshFail(reason);
                }
            }
        });
        request.gid = gid;
        request.execute();
    }

    /**
     * 刷新群成员列表
     * 进聊天界面的时候调用
     * group.syncMembers接口
     **/
    public void SyncGroupMembers(final long groupId, final String gid, final IRefreshCallback<List<GroupMemberBean>> iRefreshGroupCallback) {
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                List<GroupMemberBean> list;
                List<String> uids = new ArrayList<>();
                if (groupMemberCache.getGroupMembers(groupId) == null || groupMemberCache.getGroupMembers(groupId).size() == 0) {
                    list = groupMemberDao.queryGroupList(groupId);
                    groupMemberCache.put(groupId, list);
                } else {
                    list = groupMemberCache.getGroupMembers(groupId);
                }
                if (list != null) {
                    for (int i = 0; i < list.size(); i++) {
                        uids.add(list.get(i).uid);
                    }
                }

                final SyncGroupMembersRequest request = new SyncGroupMembersRequest(new ApiRequestCallback<SyncGroupMembersResponse>() {


                    @Override
                    public void onSuccess(ApiData<SyncGroupMembersResponse> data) {
                        if (data.resp.status == 2) {
                            deleteGroup(groupId);
                            if (iRefreshGroupCallback != null) {
                                iRefreshGroupCallback.onRefreshFail(new ErrorReason(GROUP_NOT_EXIST, "该群已解散"));
                            }
                            return;
                        }
                        List<ServerGroupMemberBean> serverGroupMemberBeanList = data.resp.newMembers;

                        List<GroupMemberBean> cacheList = groupMemberCache.getGroupMembers(groupId);
                        if (cacheList == null) {
                            cacheList = new ArrayList<>();
                        }

                        Set<Long> quitIdSet = new HashSet<>();
                        if (data.resp.quitUserIds != null) {
                            quitIdSet.addAll(data.resp.quitUserIds);
                        }
                        final List<GroupMemberBean> quitGroupMemberList = new ArrayList<>();//退群列表

                        final List<GroupMemberBean> groupMemberBeanList = new ArrayList<>();
                        for (int i = 0; i < cacheList.size(); i++) {
                            if (quitIdSet.contains(cacheList.get(i).userId)) {
                                cacheList.get(i).quit = true;
                                quitGroupMemberList.add(cacheList.get(i));
                            } else {
                                groupMemberBeanList.add(cacheList.get(i));
                            }
                        }

                        if (serverGroupMemberBeanList != null) {
                            for (ServerGroupMemberBean bean : serverGroupMemberBeanList) {
                                bean.groupId = groupId;
                                bean.gid = gid;
                                groupMemberBeanList.add(GroupMemberBean.fromServerBean(bean, groupId));
                            }
                        }

                        groupMemberCache.put(groupId, groupMemberBeanList);

                        if (iRefreshGroupCallback != null) {
                            iRefreshGroupCallback.onRefreshSuccess(groupMemberCache.getGroupMembers(groupId));
                        }

                        sendGroupInfoRefreshReceiver();

                        App.getDbExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                groupMemberDao.delete(groupId);
                                groupMemberDao.insertAll(groupMemberBeanList);
                                quitMemberDao.insertAll(groupId, QuitMemberBean.fromGroupMemberBeanList(quitGroupMemberList));
                            }
                        });

                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (iRefreshGroupCallback != null) {
                            iRefreshGroupCallback.onRefreshFail(reason);
                        }
                    }
                });
                request.gid = gid;
                request.uids = StringUtil.connectTextWithChar(",", uids);
                request.execute();
            }
        });
    }


    private final Set<String> interceptIdSet = new HashSet<>();


    /***
     *
     * 暂不实现，属于优化体验，联系人那边已经实现了
     *
     * **/
    public void syncGroupsBaseInfoFromServer(List<String> groupIds) {
        final Set<String> needRequestIds = new HashSet<>();
        synchronized (interceptIdSet) {
            //使用迭代器遍历Set集合
            Iterator<String> idIterator = groupIds.iterator();
            while (idIterator.hasNext()) {
                String gid = idIterator.next();
                if (interceptIdSet.contains(gid)) {
                    continue;
                } else {
                    interceptIdSet.add(gid);
                    needRequestIds.add(gid);
                }
            }
            if (needRequestIds.size() <= 0) {
                return;
            }
        }

        GetJoinedGroupContactListRequest request = new GetJoinedGroupContactListRequest(new ApiRequestCallback<GetGroupContactListResponse>() {

            @Override
            public void onSuccess(ApiData<GetGroupContactListResponse> data) {

                List<ServerGroupInfoBean> groupList = handleGroupChatList(data);

                //更新群聊信息到数据库
                for (ServerGroupInfoBean bean : groupList) {
                    insertOrUpdateServerField(GroupInfoBean.fromServerGroupInfoBean(bean));
                }

                Set<Long> quitGroupSet = new HashSet<>();
                if (data.resp.quitGroupIds != null && data.resp.quitGroupIds.size() > 0) {
                    quitGroupSet.addAll(data.resp.quitGroupIds);
                }

                //处理自己被踢出群的操作
                List<GroupInfoBean> cacheList = GroupManager.getInstance().getGroupInfoListContainQuit();
                if (cacheList != null) {
                    for (GroupInfoBean bean : cacheList) {
                        if (bean.myUid == UserManager.getUID() && quitGroupSet.contains(bean.groupId)) {
                            bean.isQuit = true;
                            deleteGroup(bean.groupId);
                        }
                    }
                }
                sendGroupContactReceiver();
                sendGroupInfoRefreshReceiver();
                notifyAlumniGroups(data.resp.alumnusGroups);
            }

            private void notifyAlumniGroups(List<ServerGroupInfoBean> alumnusGroups) {
                /* 通知刷新校友圈信息 */
                if (!LList.isEmpty(alumnusGroups)) {
                    for (ServerGroupInfoBean alumnusGroup : alumnusGroups) {
                        notifyGroupRefresh(alumnusGroup.groupId, null);
                    }
                }
            }

            @Override
            public void onComplete() {
                synchronized (interceptIdSet) {
                    Iterator<String> idIterator = needRequestIds.iterator();
                    while (idIterator.hasNext()) {
                        interceptIdSet.remove(idIterator.next());
                    }
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.gids = StringUtil.connectTextWithChar(",", new ArrayList<>(needRequestIds));
        request.execute();
    }

    public static void notifyGroupRefresh(long groupId, @Nullable ChatBean bean) {
        GroupInfoBean groupInfo = GroupManager.getInstance().getGroupInfo(groupId);
        if (groupInfo != null) {

            String action = "";
            if (groupInfo.source == 6) {
                action = Constants.NOTICE_ALUMNI_GROUP_REFRESH;
            } else if (groupInfo.source == 7) {
                action = Constants.NOTICE_ALUMNI_REFRESH;
            }

            if (!LText.empty(action)) {
                Intent intent = new Intent();
                intent.setAction(action);
                intent.putExtra(Constants.DATA_ENTITY, bean);
                ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
            }

        }
    }

    /**
     * 刷新个人群卡片
     **/
    public void syncGroupCard(final IRefreshCallback<GroupUserCardBean> iRefreshCallback) {
        GetUserGroupCardRequest request = new GetUserGroupCardRequest(new ApiRequestCallback<GetUserGroupCardResponse>() {
            @Override
            public void onSuccess(ApiData<GetUserGroupCardResponse> data) {
                GetUserGroupCardResponse resp = data.resp;
                if (resp != null) {
                    String json = GsonUtils.getGson().toJson(resp);
                    GroupUserCardBean userCardBean = GroupUserCardBean.fromJson(json);
                    if (userCardBean != null && userCardBean.isGroupCardComplete()) {
                        GroupManager.getInstance().saveUserCard(userCardBean);
                    }
                    sendGroupInfoRefreshReceiver();
                    if (iRefreshCallback != null) {
                        iRefreshCallback.onRefreshSuccess(userCardBean);
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshFail(reason);
                }
            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * 保存用户群信息
     *
     * @param paramsMap
     * @param callback
     */
    public static void saveGroupUserCard(Map<String, String> paramsMap, final IRefreshCallback<GroupUserCardBean> callback) {
        if (paramsMap == null || paramsMap.isEmpty()) {
            T.ss("数据错误");
            return;
        }
        EditGroupUserCardRequest request = new EditGroupUserCardRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                T.ss("创建成功");
                GroupUserCardBean bean = GroupManager.getInstance().getUserCard();
                if (bean == null) {
                    bean = new GroupUserCardBean();
                }
                if (callback != null) {
                    callback.onRefreshSuccess(bean);
                }

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
                if (callback != null) {
                    callback.onRefreshFail(reason);
                }
            }
        });
        request.extra_map.putAll(paramsMap);
        HttpExecutor.execute(request);
    }

    /**
     * 删除成员
     *
     * @param groupId
     * @param uid
     * @param callback
     */
    public void removeMember(long groupId, String uid, final IRefreshCallback<SuccessResponse> callback) {
        GroupRemoveMemberRequest request = new GroupRemoveMemberRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                callback.onRefreshSuccess(data.resp);
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onRefreshFail(reason);
            }

        });
        request.gid = GroupManager.getInstance().getGid(groupId);
        request.uid = uid;
        request.execute();
    }


    public void loadGroupMemberInfo(final long groupId, final long userId, final String uid, final IRefreshCallback<GetGroupMemberInfoResponse> iRefreshCallback) {
        GetGroupMemberInfoRequest request = new GetGroupMemberInfoRequest(new ApiRequestCallback<GetGroupMemberInfoResponse>() {


            @Override
            public void onSuccess(ApiData<GetGroupMemberInfoResponse> data) {

                ServerGroupMemberBean serverGroupMemberBean = data.resp.member;
                if (serverGroupMemberBean != null) {
                    final GroupMemberBean bean = GroupMemberBean.fromServerBean(serverGroupMemberBean, groupId);
                    groupMemberCache.deleMember(groupId, userId);
                    groupMemberCache.addMember(bean);
                    sendGroupInfoRefreshReceiver();
                    App.getDbExecutor().execute(new Runnable() {
                        @Override
                        public void run() {
                            groupMemberDao.delete(groupId, userId);
                            groupMemberDao.insert(bean);
                        }
                    });
                }
                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshSuccess(data.resp);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshFail(reason);
                }
            }
        });
        request.gid = getGid(groupId);
        request.uid = uid;
        request.execute();
    }

    /**
     * 获取群成员列表
     **/
    public void loadGroupMembersFromServer(final long groupId, int infoType, final IRefreshCallback<List<GroupMemberBean>> iRefreshCallback) {
        loadGroupMembersFromServer(groupId, GroupManager.getInstance().getGid(groupId), infoType, iRefreshCallback);
    }

    public void loadGroupMembersFromServer(final long groupId, String gid, int infoType, final IRefreshCallback<List<GroupMemberBean>> iRefreshCallback) {
        GetGroupMemberListRequest request = new GetGroupMemberListRequest(new ApiRequestCallback<GetGroupMemberListResponse>() {

            @Override
            public void onSuccess(ApiData<GetGroupMemberListResponse> data) {
                GetGroupMemberListResponse resp = data.resp;
                if (resp != null) {
                    List<GroupMemberBean> mData = GroupMemberBean.fromServerBeanList(resp.members, groupId);
                    if (iRefreshCallback != null) {
                        iRefreshCallback.onRefreshSuccess(mData);
                    }

                    List<GroupMemberBean> cacheList = groupMemberCache.getGroupMembers(groupId);
                    if (cacheList != null) {//存在缓存，则做处理
                        Set<Long> memberIdSet = new HashSet<>();
                        for (GroupMemberBean bean : cacheList) {
                            memberIdSet.add(bean.userId);
                        }

                        final List<GroupMemberBean> quitGroupMemberList = new ArrayList<>();//退群列表

                        final List<GroupMemberBean> groupMemberBeanList = new ArrayList<>();


                        for (int i = 0; i < cacheList.size(); i++) {
                            if (!memberIdSet.contains(cacheList.get(i).userId)) {
                                cacheList.get(i).quit = true;
                                quitGroupMemberList.add(cacheList.get(i));
                            }
                        }
                        if (mData != null) {
                            groupMemberBeanList.addAll(mData);
                        }

                        groupMemberCache.put(groupId, groupMemberBeanList);

                        App.getDbExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                groupMemberDao.delete(groupId);
                                groupMemberDao.insertAll(groupMemberBeanList);
                                quitMemberDao.insertAll(groupId, QuitMemberBean.fromGroupMemberBeanList(quitGroupMemberList));
                            }
                        });

                    }
                    sendGroupInfoRefreshReceiver();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (iRefreshCallback != null) {
                    iRefreshCallback.onRefreshFail(reason);
                }
            }
        });
        request.groupId = groupId;
        request.gid = gid;
        request.infoType = infoType;
        request.execute();


        sendGroupInfoRefreshReceiver();
    }


    public List<GroupInfoBean> getGroupInfoListFromDb() {
        return groupInfoDao.queryGroupList();
    }


    public void joinGroup(long groupId, String gid, String securityId, @NonNull ApiRequestCallback<JoinGroupChatResponse> callback) {
        JoinGroupChatRequest request = new JoinGroupChatRequest(new ApiRequestCallback<JoinGroupChatResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                callback.onStart();
            }

            @Override
            public void onSuccess(ApiData<JoinGroupChatResponse> data) {
                //加入群聊 同步一下群卡片
                GroupManager.getInstance().syncGroupCard(null);
                // 同步数据到数据库
                ServerGroupInfoBean group = data.resp.group;
                if (group == null || group.groupId <= 0) {
                    T.ss("数据异常");
                    return;
                }

                //更新数据库
                GroupInfoBean infoBean = GroupInfoBean.fromServerGroupInfoBean(group);
                GroupManager.getInstance().insertOrUpdateAllFileWithFresh(infoBean);

                callback.onSuccess(data);
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onFailed(reason);
            }
        });
        request.groupId = groupId;
        request.gid = gid;
        request.securityId = securityId;
        request.execute();
    }

    /**
     * 加载群成员列表
     * 进聊天界面的时候调用
     **/
    public void loadGroupMembers(final long groupId) {
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                List<GroupMemberBean> list = groupMemberDao.queryGroupList(groupId);
                groupMemberCache.put(groupId, list);
                sendGroupInfoRefreshReceiver();
            }
        });
    }

    /**
     * 获取群成员列表，需要过滤掉已经退群的
     **/
    public List<GroupMemberBean> getGroupMembers(long groupId) {
        return groupMemberCache.getGroupMembers(groupId);
    }

    /**
     * 获取群成员信息
     **/
    public GroupMemberBean getGroupMember(long groupId, long memberId) {
        GroupMemberBean result = groupMemberCache.getGroupMember(groupId, memberId);
        if (result == null) {
            result = groupMemberCache.getQuitGroupMemberBean(groupId, memberId);
            if (result == null) {
                QuitMemberBean quitMemberBean = quitMemberDao.query(groupId, memberId);
                if (quitMemberBean != null) {
                    result = quitMemberBean.toGroupMemberBean();
                    groupMemberCache.putQuitGroupMemberBean(result);
                }
            }
        }
        return result;
    }

    /**
     * 删除群信息
     **/
    public void deleteGroup(final long groupId) {
        groupCache.deleteGroup(groupId);
        groupMemberCache.deleMembers(groupId);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                groupInfoDao.delete(groupId);
                groupMemberDao.delete(groupId);
                MessageDaoFactory.getMessageDao().deleteGroupMessage(groupId);
            }
        });

        sendGroupContactReceiver();
    }

    /**
     * 删除群信息
     **/
    public void deleteGroupMember(final long groupId, final long userId) {
        final GroupMemberBean groupMemberBean = groupMemberCache.deleMember(groupId, userId);
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                groupMemberDao.delete(groupId, userId);
                if (groupMemberBean != null) {
                    List<GroupMemberBean> list = new ArrayList<>();
                    list.add(groupMemberBean);
                    quitMemberDao.insertAll(groupId, QuitMemberBean.fromGroupMemberBeanList(list));
                }

            }
        });
        sendGroupInfoRefreshReceiver();
    }


    public GroupInfoBean createGroupIfNotExist(ChatBean bean, long uid) {
        ChatMessageBean message = bean.message;
        ChatUserBean toUser = message.toUser;
        GroupInfoBean groupInfoBean = GroupManager.getInstance().getGroupInfo(toUser.id);
        if (groupInfoBean == null) {
            groupInfoBean = new GroupInfoBean();
            groupInfoBean.myUid = uid;
            groupInfoBean.groupId = toUser.id;
            groupInfoBean.name = toUser.name;
            bean.isContactNeedRefresh = true;
        }
        return groupInfoBean;
    }


    public GroupInfoBean initGroupinfo(ChatBean bean, long uid) {
        ChatMessageBean message = bean.message;
        ChatUserBean fromUser = message.fromUser;
        ChatUserBean toUser = message.toUser;
        GroupInfoBean groupInfoBean = GroupManager.getInstance().getGroupInfo(toUser.id);
        if (groupInfoBean == null) {
            groupInfoBean = new GroupInfoBean();
            groupInfoBean.myUid = uid;
            groupInfoBean.groupId = toUser.id;
            groupInfoBean.name = toUser.name;
            bean.isContactNeedRefresh = true;
        }

        if (!TextUtils.isEmpty(toUser.name)) {
            if (LText.empty(groupInfoBean.name)) {
                groupInfoBean.name = toUser.name;
            }
        }


        // 更新相关数据
        ChatMessageBodyBean body = message.messageBody;


        // 更新未读数据
        if (message.type != 6) {
            if (fromUser.id != uid) {
                // 我是接收方
                if (message.status != 2) {
                    if (message.unCount != 1) {//是否记录未读数

                        if (bean.status != 3) {//撤回消息不技术
                            //如果是羚羊计划群聊文件，status==2 成功才显示，1和3失败不显示
                            if (null != body.hyperLinkBean && body.type == 12 && body.hyperLinkBean.templateId == 10) {
                                if (RGroupMessageHandler.isShowLastTextForLYGroup(body.hyperLinkBean)) {
                                    groupInfoBean.noneReadCount++;
                                }
                            } else {
                                groupInfoBean.noneReadCount++;
                            }
                        }

                    }
                    if (body.atBean != null) {
                        groupInfoBean.atFromMessageId = bean.msgId;
                    }
                }
            }
            if ((message.time >= groupInfoBean.lastChatTime - 60000) && MessageUtils.isNeedUpdateContactLastMessage(bean)) {
                groupInfoBean.lastChatTime = message.time;
                groupInfoBean.updateTime = message.time;
                if (toUser.id == uid) {
                    // 我是发送方
                    final int chatType = body.type;
                    groupInfoBean.lastChatStatus = (chatType == 1 || chatType == 2 || chatType == 3) ?
                            bean.status : -1;
                    groupInfoBean.lastChatClientMessageId = bean.clientTempMessageId;
                } else {
                    groupInfoBean.lastChatStatus = -1;
                    groupInfoBean.lastChatClientMessageId = -1;
                }
            }
        }
        return groupInfoBean;
    }

    private GroupUserCardBean userCardBean;

    public void saveUserCard(GroupUserCardBean userCardBean) {
        if (userCardBean == null) return;
        this.userCardBean = userCardBean;
        String stringUserCard = GsonUtils.getGson().toJson(userCardBean);
        TLog.info("GroupUserCardBean", "saveUserCard %s", stringUserCard);
        SpManager.get().user().edit().putString(GROUP_USER_CARD_KEY, stringUserCard).apply();
    }

    public GroupUserCardBean getUserCard() {
        if (this.userCardBean != null) {
            return this.userCardBean;
        }
        String stringUserCard = SpManager.get().user().getString(GROUP_USER_CARD_KEY, "");
        try {
            GroupUserCardBean groupUserCardBean = GroupUserCardBean.fromJson(stringUserCard);
            if (groupUserCardBean != null) {
                this.userCardBean = groupUserCardBean;
                return groupUserCardBean;
            }
        } catch (Exception e) {
            L.d("GroupUserCardBean", "序列化GroupUserCardBean错误");
        }
        return null;
    }

    /**
     * 1107 清理群聊信息卡片缓存
     */
    public void clearUserCardCache() {
        userCardBean = null;
        SpManager.get().user().edit().remove(GROUP_USER_CARD_KEY).apply();
    }

    // 获取职位卡片中的拓展securityId
    public static String getExtendSecurityId(String extend) {
        String securityId = "";

        if (!TextUtils.isEmpty(extend)) {
            try {
                JSONObject jsonObject = new JSONObject(extend);
                securityId = jsonObject.optString("shareSecurityId");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return securityId;
    }
}
