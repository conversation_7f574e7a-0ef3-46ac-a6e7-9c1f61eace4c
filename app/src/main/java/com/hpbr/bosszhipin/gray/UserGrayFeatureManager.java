package com.hpbr.bosszhipin.gray;

import android.animation.ValueAnimator;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewTreeObserver;

import com.google.gson.reflect.TypeToken;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.AppMainThemeColorConfig;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.custom.core.AnimatedView;
import com.hpbr.bosszhipin.config.custom.core.Factory;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module_boss_export.entity.BossIntentionF2EntranceParams;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.L;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import net.bosszhipin.api.BossIsDisplayVIPResponse;
import net.bosszhipin.api.GetBossBlockVipStatusResponse;
import net.bosszhipin.api.bean.BossPreferredPubInfoV2Bean;
import net.bosszhipin.api.bean.ServerSettingBean;

import java.util.Collections;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: ZhouYou
 * Date: 2018/7/3.
 */
public class UserGrayFeatureManager {

    private SpImpl sp;
    private static final String USER_FEATURE = Constants.PREFIX + ".USER_FEATURE";
    private static final String RESUME_FEATURE_KEY = Constants.PREFIX + ".RESUME_FEATURE_KEY";

    private static final String BOSS_IS_DISPLAY_VIP_STATUS = Constants.PREFIX + ".BOSS_IS_DISPLAY_VIP";
    private static final String BOSS_VIP_STATUS = Constants.PREFIX + ".BOSS_VIP_STATUS";

    private static final String ADVANCED_SEARCH = Constants.PREFIX + ".ADVANCED_SEARCH";

    private static final String BOSS_GREETING_QUESTION_FEATURE = Constants.PREFIX + ".BOSS_GREETING_QUESTION_FEATURE";

    private static final String BOSS_VIP_MANAGER_CLICKED = Constants.PREFIX + ".BOSS_VIP_MANAGER_CLICKED";

    private static final String GEEK_DIRECT_CALL_SETTING_GRAY = Constants.PREFIX + ".GEEK_DIRECT_CALL_SETTING_GRAY";
    private static final String SP_SHOW_HOMETOWN_SWITCH = Constants.PREFIX + ".SP_SHOW_HOMETOWN_SWITCH";
    private static final String SP_SHOW_COMMON_DEVICE = Constants.PREFIX + ".SP_SHOW_COMMON_DEVICE";

    //BossF1强化搜索灰度新样式
    private static final String BOSS_F1_ADVANCE_SEARCH_ENTRY_DESC = Constants.PREFIX + ".BOSS_F1_ADVANCE_SEARCH_ENTRY_DESC";

    private static final String BOSS_CAN_USE_JOB_TEMPLATE = "canUseJobTemplate";
    private static final String BOSS_CAN_USE_JOB_PROJECT = "canUseJobProject";
    private static final String BOSS_CAN_USE_SELECTED_PROJECT = "canUseSelectedProject";
    private static final String BOSS_JOB_TEMPLATE_TEXT = "jobTemplateText";
    private static final String BOSS_JOB_TEMPLATE_PUB_INFO_V2 = "preferredPubInfoV2";
    private static final String BOSS_JOB_SELECT_PUB_INFO = "selectedPubInfo";
    private static final String BOSS_SHOW_JOB_NAME_GUIDE = "bossShowJobNameGuide";
    private static final String BOSS_RECRUIT_CONFIG = "boss_recruit_config";
    private static final String GEEK_ONLINE_REMIND = "geek_online_remind";

    private static final String GEEK_QUICK_REMIND_CHAT = "geek_quick_remind_chat";

    private static final String GEEK_COMPANY_EXP_TASTE_TOPIC_GRAY = Constants.PREFIX + ".GEEK_COMPANY_EXP_TASTE_TOPIC_GRAY";
    private static final String BOSS_SUBSCRIBE_TIP = "boss_subscribe_tip";

    private static final String RELATION_GEEK_GRAY = "relation_Geek_Gray";
    //908.41【商业】搜畅职位版尝试 0默认值, 1命中灰度
    private static final String ADVANCED_SEARCH_CARD_4_JOB_GRAY = "advanced_search_card4_job_gray";
    private static final String UPDATE_NAME_GRAY = "update_name_gray";
    private static final String BLOCK_ACTIVITY_SWITCH = "block_activity_switch";
    private static final String REFINED_SELECTION = "refined_selection";
    private static final String MIDDLE_PAGE_AD_SEARCH = "middle_page_ad_search";
    private static final String DEF_REFINED_OPTIMIZED_GRAY = "default_refined_optimized_gray";
    private static final String SEARCH_VIEWED_GRAY = "search_viewed_gray";
    /*1003.259【商业】精选列表高筛免费实验*/
    private static final String REFINED_GEEK_SENIOR_SCREEN_FREE = "refined_geek_senior_screen_free";

    //1009.203【商业】切换身份入口引导
    private static final String SWITCH_IDENTITY_ENTRANCE_GUIDE = "switch_identity_entrance_guide";

    private static final String CONTACT_STYLE_GRAY = "contact_style_gray";
    private static final String CHAT_ROOM_STYLE_GRAY = "chat_room_style_gray";
    private static final String CONTACT_LIMIT_USER = "contact_limit_read_user";
    private static final String CONTACT_LIMIT_UNREAD_USER = "contact_limit_unread_user";

    private static final String URL_FEED_BACK = "url_feed_back";

    private static final String URL_REPORT_CHAT = "url_report_chat";

    // 106.060 用户账号认证状态灰度，0否1是
    private static final String USER_ACCOUNT_AUTH_STATUS_GRAY = "user_account_auth_status_gray";
    // 1107.157
    private static final String COM_OPTIMIZATION_TYPE = "com_optimization_type";
    private static final String ADDRESS_USE_A_MAP = "address_use_a_map";
    private static final String ADDRESS_USE_COM_POI = "address_use_com_poi";
    private static final String JOB_DESC_GUID_LIST = "job_desc_guid_list";
    private static final String ITEM_MALL_NEW_STYLE_URL_1112 = Constants.PREFIX + ".ITEM_MALL_NEW_STYLE_URL_1112";

    // 1205.234【商业】风险提示
    private static final String ADS_FILTER_RISK_MSG = "ads_filter_risk_msg";

    // 1207.233【商业】搜畅新形态，添加随心聊灰度
    private static final String BOSS_HAS_EASY_CHAT = "boss_has_easy_chat";

    // 1122.110
    public static final String INTERNET_PHONE_GRAY = "internet_phone_gray";

    // 1219.500
    private static final String BOSS_INTENTION_F2_ENTRANCE = "boss_intention_f2_entrance";
    // 1225.251【商业】搜索结果页吸顶交互优化
    private static final String ADS_CEILING_INTERACTION_GRAY = "ads_ceiling_interaction_gray";

    // 职位关键词选择灰度，1-命中灰度
    private static final String POSITION_KEY_WORDS_SELECT_GRAY = "position_key_words_select_gray";

    private static UserGrayFeatureManager instance = new UserGrayFeatureManager();


    public static UserGrayFeatureManager getInstance() {
        return instance;
    }


    private String buildUniqueKey(@NonNull String prefix) {
        return prefix + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get();
    }

    private UserGrayFeatureManager() {
        sp = SpFactory.create(App.get().getApplicationContext(), USER_FEATURE);
    }

    public int getResumeSend() {
        return sp.getInt(RESUME_FEATURE_KEY + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
    }


    public boolean useNewAdvanceSearch() {
        boolean useNewAdvanceSearch = sp.getInt(keyOfAdvanceSearch(), 0) == 1;
        return useNewAdvanceSearch;
    }

    private String keyOfAdvanceSearch() {
        return ADVANCED_SEARCH + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get();
    }

    public void saveBossBlockVipStatus(GetBossBlockVipStatusResponse vipStatusResponse) {
        try {
            String json = GsonUtils.getGson().toJson(vipStatusResponse);
            sp.putString(BOSS_VIP_STATUS + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), json);
        } catch (Exception e) {
            L.d("数据解析失败");
        }
    }

    public void saveBossIsDisplayVipEntrance(BossIsDisplayVIPResponse isDisplayVIPResponse) {
        try {
            String json = GsonUtils.getGson().toJson(isDisplayVIPResponse);
            sp.putString(BOSS_IS_DISPLAY_VIP_STATUS + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), json);
        } catch (Exception e) {
            L.d("数据解析失败");
        }
    }

    public boolean getIsDisplayShowVIP() {
        BossIsDisplayVIPResponse bossIsDisplayVIPResponse;
        try {
            String json = sp.getString(BOSS_IS_DISPLAY_VIP_STATUS + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
            if (!TextUtils.isEmpty(json)) {
                bossIsDisplayVIPResponse = GsonUtils.getGson().fromJson(json, BossIsDisplayVIPResponse.class);
                if (bossIsDisplayVIPResponse != null) {
                    return bossIsDisplayVIPResponse.result;
                }
                return false;
            }
        } catch (Exception e) {
            L.d("数据解析失败");
        }
        return false;
    }

    public boolean isBossVipManagerClicked() {
        return sp.getBoolean(BOSS_VIP_MANAGER_CLICKED + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
    }

    public void setBossVipManagerClicked() {
        sp.putBoolean(BOSS_VIP_MANAGER_CLICKED + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), true);
    }


    /**
     * 获取VIP阻断数据
     *
     * @return
     */
    public GetBossBlockVipStatusResponse getBossBlockVipStatus() {
        try {
            String json = sp.getString(BOSS_VIP_STATUS + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
            if (!TextUtils.isEmpty(json)) {
                return GsonUtils.getGson().fromJson(json, GetBossBlockVipStatusResponse.class);
            }
        } catch (Exception e) {
            L.d("数据解析失败");
        }
        return null;
    }

    public boolean isBossVipFriendFilter() {
        GetBossBlockVipStatusResponse bossBlockVipStatus = UserGrayFeatureManager.getInstance().getBossBlockVipStatus();
        return bossBlockVipStatus != null && bossBlockVipStatus.vipFriendFilterState == 1;
    }

    public void saveBossGreetingQuestionSwitchFeature(ServerSettingBean bean) {
        try {
            String json = GsonUtils.getGson().toJson(bean);
            sp.putString(BOSS_GREETING_QUESTION_FEATURE + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), json);
        } catch (Exception e) {
            L.d("数据解析失败");
        }
    }

    public ServerSettingBean getBossGreetingQuestionSwitchFeature() {
        try {
            String json = sp.getString(BOSS_GREETING_QUESTION_FEATURE + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
            if (!TextUtils.isEmpty(json)) {
                return GsonUtils.getGson().fromJson(json, ServerSettingBean.class);
            }
        } catch (Exception e) {
            L.d("数据解析失败");
        }
        return null;
    }

    private static final String TAG = "UserGrayFeatureManager";


    public void saveGeekDirectCallSetting(int directCallSettingGray) {
        sp.putInt(GEEK_DIRECT_CALL_SETTING_GRAY + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), directCallSettingGray);
    }

    public void saveShowHometownTag(int showHometown) {
        sp.putInt(SP_SHOW_HOMETOWN_SWITCH + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), showHometown);
    }

    public void saveCommonDeviceTag(int showCommonDevice) {
        sp.putInt(SP_SHOW_COMMON_DEVICE + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), showCommonDevice);
    }

    public boolean isShowGeekDirectCallSetting() {
        return sp.getInt(GEEK_DIRECT_CALL_SETTING_GRAY + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get()) == 1;
    }

    //保存展示BossF1强化搜索灰度新样式的文案
    public void saveF1AdvanceSearchEntryDesc(String searchDesc) {
        sp.putString(BOSS_F1_ADVANCE_SEARCH_ENTRY_DESC + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), searchDesc);
    }

    public String getF1AdvanceSearchEntryDesc() {
        return sp.getString(BOSS_F1_ADVANCE_SEARCH_ENTRY_DESC + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get(), "");
    }

    public void saveUserProxyConfig(int recruit) {
        SpManager.get().user().edit().putInt(BOSS_RECRUIT_CONFIG, recruit).apply();
    }

    public int getUserProxyConfig() {
        return SpManager.get().user().getInt(BOSS_RECRUIT_CONFIG, 0);
    }

    public void saveCanUseJobProject(boolean support) {
        SpManager.get().user().edit().putBoolean(BOSS_CAN_USE_JOB_PROJECT, support).apply();
    }


    public boolean getCanUseJobProject() {
        return SpManager.get().user().getBoolean(BOSS_CAN_USE_JOB_PROJECT, false);
    }


    public void saveCanUseSelectProject(boolean support) {
        SpManager.get().user().edit().putBoolean(BOSS_CAN_USE_SELECTED_PROJECT, support).apply();
    }

    public boolean getCanUseSelectProject() {
        return SpManager.get().user().getBoolean(BOSS_CAN_USE_SELECTED_PROJECT, false);
    }


    public void saveSelectedPubInfo(BossPreferredPubInfoV2Bean selectedPubInfoBean) {
        SpManager.get().user().edit().putString(BOSS_JOB_SELECT_PUB_INFO, GsonUtils.toJson(selectedPubInfoBean)).apply();
    }

    public BossPreferredPubInfoV2Bean getSelectedPubInfoBean() {
        return GsonUtils.fromJson(SpManager.get().user().getString(BOSS_JOB_SELECT_PUB_INFO, ""), BossPreferredPubInfoV2Bean.class);
    }

    public void saveBossPreferredPubInfoV2(BossPreferredPubInfoV2Bean preferredPubInfo) {
        SpManager.get().user().edit().putString(BOSS_JOB_TEMPLATE_PUB_INFO_V2, GsonUtils.toJson(preferredPubInfo)).apply();
    }

    public BossPreferredPubInfoV2Bean getBossPreferredPubInfoV2() {
        return GsonUtils.fromJson(SpManager.get().user().getString(BOSS_JOB_TEMPLATE_PUB_INFO_V2, ""), BossPreferredPubInfoV2Bean.class);
    }

    public void saveBossShowJobNameGuide(boolean showJobNameGuide) {
        SpManager.get().user().edit().putBoolean(BOSS_SHOW_JOB_NAME_GUIDE, showJobNameGuide).apply();
    }

    public boolean getBossShowJobNameGuide() {
        return SpManager.get().user().getBoolean(BOSS_SHOW_JOB_NAME_GUIDE, false);
    }

    public void saveOnlineNotify(int onlineRemind) {
        SpManager.get().user().edit().putInt(GEEK_ONLINE_REMIND, onlineRemind).apply();
    }


    public int getShowOnlineNotify() {
        return SpManager.get().user().getInt(GEEK_ONLINE_REMIND, 0);
    }


    /**
     * 是否显示家乡字段
     *
     * @return
     */
    public boolean isShowHometown() {
        return sp.getInt(SP_SHOW_HOMETOWN_SWITCH + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get()) == 1;
    }

    /**
     * 是否显示常用设备管理入口
     *
     * @return
     */
    public boolean isShowCommonDevice() {
        return sp.getInt(SP_SHOW_COMMON_DEVICE + "_" + UserManager.getUID() + "_" + UserManager.getUserRole().get()) == 1;
    }

    /**
     * 公司工作体验话题
     */
    public void saveCompanyExpTasteGray(boolean isGray) {
        SpManager.get().user().edit().putBoolean(GEEK_COMPANY_EXP_TASTE_TOPIC_GRAY, isGray).apply();
    }

    /**
     * 公司工作体验话题, return true: 展示话题
     */
    public boolean getCompanyExpTasteGray() {
        return SpManager.get().user().getBoolean(GEEK_COMPANY_EXP_TASTE_TOPIC_GRAY, false);
    }

    public void saveBossSubscribedRedTip(int subscribeTip) {
        SpManager.get().user().edit().putInt(BOSS_SUBSCRIBE_TIP, subscribeTip).apply();
    }

    public boolean isShowSubscribedRedTip() {
        return SpManager.get().user().getInt(BOSS_SUBSCRIBE_TIP, 0) == 1;
    }

    public void saveSearchCard4JobGray(int searchCard4JobGray) {
        SpManager.get().user().edit().putInt(ADVANCED_SEARCH_CARD_4_JOB_GRAY, searchCard4JobGray).apply();
    }

    public boolean isSearchCard4JobGray() {
        return SpManager.get().user().getInt(ADVANCED_SEARCH_CARD_4_JOB_GRAY, 0) == 1;
    }

    public void saveUpdateNameGray(int updateNameGray) {
        SpManager.get().user().edit().putInt(UPDATE_NAME_GRAY, updateNameGray).apply();
    }

    public boolean isUpdateNameGray() {
        return SpManager.get().user().getInt(UPDATE_NAME_GRAY, 0) == 1;
    }

    public void saveBlockActivitySwitch(int enable) {
        SpManager.get().user().edit().putInt(BLOCK_ACTIVITY_SWITCH, enable).apply();
    }

    public boolean isBlockActivitySwitch() { // 0 - 对照组 | 1 - 实验组
        return SpManager.get().user().getInt(BLOCK_ACTIVITY_SWITCH, 0) == 1;
    }

    public void saveRefinedDefSelection(int refinedSelection) {
        SpManager.get().user().edit().putInt(REFINED_SELECTION, refinedSelection).apply();
    }

    public boolean isRefinedSelected() { // 0 - 对照组 | 1 - 实验组
        return SpManager.get().user().getInt(REFINED_SELECTION, 0) == 1;
    }

    public boolean isLastJobMemoryRecordGray() {
//        if (UserManager.getUID() == 1823 || UserManager.getUID() == 6460888) { // TODO
//            return true;
//        }
        return SpManager.get().user().getBoolean(Constants.SP_LAST_JOB_MEMORY_RECORD_GRAY, false);
    }

    public long getDefaultSelectHotJobId() {
        return SpManager.get().user().getLong(Constants.SP_HOT_DEFAULT_SELECT_JOB_ID, 0);
    }

    /*1003.259【商业】精选列表高筛免费实验*/
    public void saveRefinedGeekSeniorScreenFreeGray(boolean refinedGeekSeniorScreenFree) {
        SpManager.get().user().edit().putBoolean(REFINED_GEEK_SENIOR_SCREEN_FREE, refinedGeekSeniorScreenFree).apply();
    }

    /*1003.259【商业】精选列表高筛免费实验*/
    public boolean isRefinedGeekSeniorScreenFreeGray() {
        return SpManager.get().user().getBoolean(REFINED_GEEK_SENIOR_SCREEN_FREE, false);
    }

    /*1003.259【商业】精选列表高筛免费实验 -（F1精选灰度筛选项切换不同步判断: 满足精选灰度 && 未购买VIP）(不考虑命中高筛限免场景)*/
    public boolean isRefinedGeekSeniorScreenFreeSwitchF1FilterGray() {
        boolean refinedFilterReset = isRefinedGeekSeniorScreenFreeGray();
        if (refinedFilterReset) {
            GetBossBlockVipStatusResponse bossBlockVipStatus = getBossBlockVipStatus();
            /*有vip功能 + 阻断下有F1高筛特权的用户 + 已购买VIP*/
            if (null != bossBlockVipStatus && bossBlockVipStatus.isVIPFunctionBzb()) {
                refinedFilterReset = false;
            }
        }
        return refinedFilterReset;
    }

    //1009.202【商业-阻断】促直豆消耗
    public void saveSwitchIdentityEntrance(int showSwitchIdentityEntrance) {
        SpManager.get().user().edit().putInt(SWITCH_IDENTITY_ENTRANCE_GUIDE, showSwitchIdentityEntrance).apply();
    }

    //1009.202【商业-阻断】促直豆消耗 - 当用户在app端触发商品详情页没有进确认支付页直接离开，给予挽留弹框
    public boolean isShowSwitchIdentityEntrance() {
        return SpManager.get().user().getInt(SWITCH_IDENTITY_ENTRANCE_GUIDE, 0) == 1;
    }

    public void saveNewContextStyle(int newContextStyle, int newChatRoomStyle, int readUser, int unReadUser) {
        SpManager.get().user().edit()
                .putInt(CONTACT_STYLE_GRAY, newContextStyle)
                .putInt(CHAT_ROOM_STYLE_GRAY, newChatRoomStyle)
                .putInt(CONTACT_LIMIT_USER, readUser)
                .putInt(CONTACT_LIMIT_UNREAD_USER, unReadUser)
                .apply();
    }

    private static int newChatRoomStyle = -1; // 1104.60 新样式 0-否 1是

    public boolean isNewChatRoom() {
        if (newChatRoomStyle == -1) {
            newChatRoomStyle = SpManager.get().user().getInt(CHAT_ROOM_STYLE_GRAY, 1); //1220 全量后，客户端默认修改为全量
        }
        return newChatRoomStyle == 1;
    }

    public int getNewContactReadUser() {
        return SpManager.get().user()
                .getInt(CONTACT_LIMIT_USER, -1);
    }

    public int getNewContactUnReadUser() {
        return SpManager.get().user()
                .getInt(CONTACT_LIMIT_UNREAD_USER, -1);
    }

    public void saveFeedbackUrl(String feedbackUrl) {
        SpManager.get().user().edit().putString(URL_FEED_BACK, feedbackUrl).apply();
    }

    public String getUrlFeedBack() {
        return SpManager.get().user().getString(URL_FEED_BACK, "");
    }

    public void saveChatReportUrl(String chatReportUrl) {
        SpManager.get().user().edit().putString(URL_REPORT_CHAT, chatReportUrl).apply();
    }

    public String getUrlReportChat() {
        return SpManager.get().user().getString(URL_REPORT_CHAT, "");
    }


    /**
     * 1106.060 用户账号认证状态灰度，0否1是
     */
    public void saveAccountAuthStatusGray(int zdRechargeGray) {
        SpManager.get().user().edit().putInt(USER_ACCOUNT_AUTH_STATUS_GRAY, zdRechargeGray).apply();
    }

    /**
     * 1106.060 用户账号认证状态灰度，0否1是
     */
    public boolean isAccountAuthStatusGray() {
//        if (BuildInfoUtils.isDebug() && UserManager.getUID() == **********) {
//            return true;
//        }
        return SpManager.get().user().getInt(USER_ACCOUNT_AUTH_STATUS_GRAY, 0) == 1;
    }

    public void saveComOptimizationType(int optimizationType) {
        SpManager.get().user().edit().putInt(COM_OPTIMIZATION_TYPE, optimizationType).apply();
    }

    public int getComOptimizationType() {
        return SpManager.get().user().getInt(COM_OPTIMIZATION_TYPE, 0);
    }

    public void saveAddressUseAMap(boolean useAMap) {
        SpManager.get().user().edit().putBoolean(ADDRESS_USE_A_MAP, useAMap).apply();
    }

    public boolean getAddressUseAMap() {
        return SpManager.get().user().getBoolean(ADDRESS_USE_A_MAP, false);
    }

    public void saveAddressUseComPoi(boolean useComPoi) {
        SpManager.get().user().edit().putBoolean(ADDRESS_USE_COM_POI, useComPoi).apply();
    }

    public boolean getAddressUseComPoi() {
        return SpManager.get().user().getBoolean(ADDRESS_USE_COM_POI, false);
    }


    public void saveJobDescGuidList(List<String> jobDescGuidList) {
        SpManager.get().user().edit().putString(JOB_DESC_GUID_LIST, GsonUtils.toJson(jobDescGuidList)).apply();
    }

    public List<String> getJobDescGuidList() {
        try {
            String json = SpManager.get().user().getString(JOB_DESC_GUID_LIST, "");
            return GsonUtils.fromJson(json, new TypeToken<List<String>>() {
            }.getType());
        } catch (Exception e) {
            L.d("数据解析失败");
            return Collections.emptyList();
        }
    }

    public void saveBossIntentionF2EntranceParams(BossIntentionF2EntranceParams params) {
        SpManager.get().user().edit().putString(BOSS_INTENTION_F2_ENTRANCE, GsonUtils.toJson(params)).apply();
    }

    public BossIntentionF2EntranceParams getBossIntentionF2EntranceParams() {
        try {
            String json = SpManager.get().user().getString(BOSS_INTENTION_F2_ENTRANCE, "");
            if (!TextUtils.isEmpty(json)) {
                return GsonUtils.getGson().fromJson(json, BossIntentionF2EntranceParams.class);
            }
        } catch (Exception e) {
            L.d("数据解析失败");
        }
        return null;
    }

    public void saveInternetPhoneGray(int internetPhone) {
        SpManager.get().user().edit().putInt(INTERNET_PHONE_GRAY, internetPhone).apply();
    }

    public boolean isInternetPhoneGray() {
        return SpManager.get().user().getInt(INTERNET_PHONE_GRAY, 0) == 1;
    }

    public void saveRiskMessage(String riskMessage) {
        SpManager.get().user().edit().putString(ADS_FILTER_RISK_MSG, riskMessage).apply();
    }

    public String getRiskMessage() {
        return SpManager.get().user().getString(ADS_FILTER_RISK_MSG, "");
    }

    // 1207.233【商业】搜畅新形态，添加随心聊灰度
    public void setHasEasyChat(int searchEasyChatGray) {
        SpManager.get().user().edit().putInt(BOSS_HAS_EASY_CHAT, searchEasyChatGray).apply();
    }

    // 1207.233【商业】搜畅新形态，判断是否可使用随心聊道具
    public boolean hasEasyChat() {
        return SpManager.get().user().getInt(BOSS_HAS_EASY_CHAT, 0) == 1;
    }

    /**
     * 1312.243【商业】短信通知升级服务：将接口返回的角标提示信息，设置为本地数据
     *
     * @param smsLabelText 提示信息
     */
    public void setSmsLabelText(@Nullable String smsLabelText) {
        SpManager.get().user().edit().putString(Constants.SMS_LABEL_TEXT_VALUE, smsLabelText).apply();
    }

    /**
     * 1312.243【商业】短信通知升级服务：将接口返回的角标提示灰度值，设置为本地数据
     *
     * @param intentionOrderGray 灰度字段
     */
    public void setIntentionOrderGray(int intentionOrderGray) {
        SpManager.get().user().edit().putInt(Constants.SMS_LABEL_ORDER_GRAY, intentionOrderGray).apply();
    }

    public void fitHeadViewHeight(View container, int height) {
        if (container == null) return;
        View headerView = container.findViewById(R.id.iv_head_bg);
        if (headerView == null) return;
        if (height > 0) {
            headerView.getLayoutParams().height = height;
            headerView.requestLayout();
        }
    }

//    public void fitSubHeader(View subView, boolean isTop) {
//        if (UserGrayFeatureManager.getInstance().isNewUIStyle()) {
//            if (subView != null) {
//                if (isTop) {
//                    subView.setBackgroundResource(AccountHelper.isBoss() ? R.mipmap.ic_boss_title_filter_style_bg : R.mipmap.ic_geek_title_filter_style_bg);
//                } else {
//                    subView.setBackgroundColor(ContextCompat.getColor(subView.getContext(), R.color.color_FFFFFFFF_FF151517));
//                }
//            }
//        }
//    }

    public void setHeaderView(View container, View heightView) {
        setHeaderView(container, heightView, AppMainThemeColorConfig.get().defColorRes(container.getContext()));
    }

    public void setHeaderView(View container, View heightView, AppMainThemeColorConfig.F1Tab f1Tab) {
        AnimatedView headerView = container.findViewById(R.id.iv_head_bg);
        if (headerView == null) return;
        headerView.setRepeatCount(ValueAnimator.INFINITE);
        headerView.setAnimation(Factory.defaultImpl(), f1Tab.getTopThemeBackground());
//        ViewCompat.setFitsSystemWindows(headerView, true);
        setHeaderHeightView(container, heightView, 0);
        //方案一：
//            ViewCompat.setFitsSystemWindows(container,true);
//            ViewCompat.requestApplyInsets(container);
        //方案二：
        //com.hpbr.bosszhipin.views.titlebar.MainToolBar.applyWindowInsetsByView()
    }

    public void setHeaderHeightView(View container, View heightView, int top) {
        if (heightView != null) {
            heightView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    if (heightView.getHeight() > 0) {
                        fitHeadViewHeight(container, heightView.getHeight() + top);
                        heightView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    }
                }
            });
        }
    }


    /**
     * 1112.256【商业】B端道具商城改版
     */
    public void saveItemMallNewStyleGray(String itemMallNewStylePageUrl) {
        if (UserManager.isGeekRole()) {
            ApmAnalyzer.create().action("item_mall_action", "wrong_identity")
                    .p3(itemMallNewStylePageUrl)
                    .report();
        }
        SpManager.get().user().edit().putString(ITEM_MALL_NEW_STYLE_URL_1112, itemMallNewStylePageUrl).apply();
    }

    public String getItemMallNewStylePageUrl() {
        return SpManager.get().user().getString(ITEM_MALL_NEW_STYLE_URL_1112, "");
    }

    public void destroy() {
        newChatRoomStyle = -1;
    }

    /**
     * 保存职位关键词选择灰度状态
     *
     * @param positionKeyWordsSelectGray 灰度状态值，1-命中灰度
     */
    public void savePositionKeyWordsSelectGray(int positionKeyWordsSelectGray) {
        SpManager.get().user().edit().putInt(POSITION_KEY_WORDS_SELECT_GRAY, positionKeyWordsSelectGray).apply();
    }

    /**
     * 判断是否命中职位关键词选择灰度
     *
     * @return true-命中灰度
     */
    public boolean isPositionKeyWordsSelectGray() {
        return SpManager.get().user().getInt(POSITION_KEY_WORDS_SELECT_GRAY, 0) == 1;
    }
}
