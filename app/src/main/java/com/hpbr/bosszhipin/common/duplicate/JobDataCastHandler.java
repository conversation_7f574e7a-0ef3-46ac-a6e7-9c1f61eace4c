package com.hpbr.bosszhipin.common.duplicate;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.GeekBaseCardBean;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerExperimentInfoBean;
import net.bosszhipin.api.bean.ServerJobCardBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/4/10.
 */
public class JobDataCastHandler extends AbsDuplicateHandler<ServerJobCardBean> {
    @Override
    public List<ServerJobCardBean> getList(List<ServerJobCardBean> list) {
        return handler(new ArrayList<ServerJobCardBean>(), list);
    }

    @Override
    public List<Object> getGenericList(List<ServerJobCardBean> list) {
        return handler(new ArrayList<>(), list);
    }

    @Override
    public List<Object> getNormalList(List<ServerJobCardBean> list) {
        return transfer(new ArrayList<>(), list);
    }


    public List<GeekBaseCardBean> handleJobListViaCardType(List<ServerJobCardBean> list, int cardStyle, boolean isMixedRecommend, ServerExperimentInfoBean experimentInfo) {

        if (LList.isEmpty(list)) {
            return null;
        }
        List<GeekBaseCardBean> dataList = new ArrayList<>();
        for (ServerJobCardBean bean : list) {
            if (containsKey(bean.jobId)) continue;
            if (!bean.isRecommendListAdBean){
                bean.itemType = getItemTypeByCardStyle(bean, cardStyle, isMixedRecommend);
                bean.experimentInfo =experimentInfo;
                put(bean.jobId);
            }
            dataList.add(bean);

        }
        return dataList;
    }

    public List<GeekBaseCardBean> handleJobListViaCardTypeCityFill(List<ServerJobCardBean> list, int cardStyle, boolean isMixedRecommend) {

        if (LList.isEmpty(list)) {
            return null;
        }
        List<GeekBaseCardBean> dataList = new ArrayList<>();
        for (ServerJobCardBean bean : list) {
            if (containsKey(bean.jobId)) continue;
            bean.itemType = getItemTypeByCardStyle(bean, cardStyle, isMixedRecommend);
            bean.fromBiz = GeekBaseCardBean.FROM_BIZ_1109_SMALLCITY;
            put(bean.jobId);
            dataList.add(bean);

        }
        return dataList;
    }


    public int getItemTypeByCardStyle(ServerJobCardBean cardBean, int cardStyle, boolean isMixedRecommend) {

        if (cardBean.cardType == 0) {
            if (isMixedRecommend) {
                if (cardStyle == 37) {
                    return GeekBaseCardBean.TYPE_F1_JOBCARD_MIXED_GRAY;
                } else {
                    return GeekBaseCardBean.TYPE_F1_JOBCARD_MIXED;
                }
            } else {
                if (cardStyle == 1) {
                    return GeekBaseCardBean.TYPE_F1_JOBCARD_DEFAULT;
                } else if (cardStyle == 2) {
                    return GeekBaseCardBean.TYPE_F1_JOBCARD_KEYWORD;
                } else if (cardStyle == 5) {
                    return GeekBaseCardBean.TYPE_F1_JOBCARD_WORKER;
                }
            }
            return GeekBaseCardBean.TYPE_F1_JOBCARD_DEFAULT;

        } else if (cardBean.cardType == 1) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_LIVE;
        } else if (cardBean.cardType == 2) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_TITAN;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 0) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_ANXINBAO;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 1) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_ANXINBAO_GROUPA;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 2) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_ANXINBAO_GROUPB;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 3) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_ANXINBAO_GROUPC;
        } else if (cardBean.cardType == 4) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_RECOMMEND;
        } else if (cardBean.cardType == 5 && cardBean.cusAddrSugAd != null) {// 1105.601 设置地址卡片
            return GeekBaseCardBean.TYPE_1105_SETTING_HOME_ADDRESS;
        } else if (cardBean.cardType == 6 && cardBean.schoolJobBrandAd != null) {// 1106.55 校招名企
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_SCHOOL_COMPANY_2;
        } else if (cardBean.cardType == 7 && cardBean.traitTip != null) {/*1107.6  特征标签新样式卡片*/
            return GeekBaseCardBean.TYPE_F1_CARD_CHARACTER_LABEL_ENTRY_NEW_STYLE_IN_JOB_LIST;
        } else if (cardBean.cardType == 8 && cardBean.adCard != null) {/*1115.70  */
            return GeekBaseCardBean.TYPE_1116_COMPANY_ENTRANCE;
        } else if (cardBean.cardType == 10 && cardBean.adCard != null) {
            return GeekBaseCardBean.TYPE_GEEK_F1_AI_ROBOT;
        }else if (cardBean.cardType ==11){
            return GeekBaseCardBean.TYPE_GEEK_F1_TOP_POSITION;
        }

        ApmAnalyzer.create().action("GeekF1NewException", "nosuchCardType")
                .param("p2", "cardType")
                .param("p3", String.valueOf(cardBean.cardType))
                .report();
        return GeekBaseCardBean.TYPE_F1_ERRO;
    }

    /**
     * 学生端根据数据 处理卡片类型
     *
     * @param list
     * @param cardStyle
     * @return
     */
    public List<ServerJobCardBean> stuHandleJobListViaCardType(List<ServerJobCardBean> list, int cardStyle) {

        if (LList.isEmpty(list)) {
            return null;
        }
        List<ServerJobCardBean> dataList = new ArrayList<>();
        for (ServerJobCardBean bean : list) {
            if (containsKey(bean.jobId)) continue;
            bean.itemType = getItemTypeByCardStyleStu(bean, cardStyle);
            put(bean.jobId);
            dataList.add(bean);

        }
        return dataList;
    }

    /**
     * 学生端处理卡片的逻辑
     *
     * @param cardBean
     * @param cardStyle
     * @return
     */
    private int getItemTypeByCardStyleStu(ServerJobCardBean cardBean, int cardStyle) {

        if (cardBean.cardType == 0) {

            if (cardStyle == 1) {
                return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_CARD_DEFAULT;
            } else if (cardStyle == 2) {
                return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_CARD_KEY_WORD;
            } else if (cardStyle == 5) {
                return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_CARD_WORKER;
            }

            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_CARD_DEFAULT;

        } else if (cardBean.cardType == 1) {
            return GeekBaseCardBean.TYPE_F1_JOBCARD_LIVE;//和职场人一样
        } else if (cardBean.cardType == 2) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_TITAN;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 0) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_ANXINBAO;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 1) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_ANXINBAO_A;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 2) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_ANXINBAO_B;
        } else if (cardBean.cardType == 3 && cardBean.factoryAd != null && cardBean.factoryAd.group == 3) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_ANXINBAO_C;
        } else if (cardBean.cardType == 4) {
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_RECOMMEND;
        } else if (cardBean.cardType == 5 && cardBean.cusAddrSugAd != null) {// 1105.601 设置地址卡片
            return GeekBaseCardBean.TYPE_1105_SETTING_HOME_ADDRESS;
        } else if (cardBean.cardType == 6 && cardBean.schoolJobBrandAd != null) {// 1106.55 校招名企
            return GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_SCHOOL_COMPANY_2;
        }else if (cardBean.cardType == 11) {
            return GeekBaseCardBean.TYPE_GEEK_F1_TOP_POSITION;
        }

        ApmAnalyzer.create().action("GeekStuF1NewException", "nosuchCardType_stu")
                .param("p2", "cardType")
                .param("p3", String.valueOf(cardBean.cardType))
                .report();
        return GeekBaseCardBean.TYPE_F1_ERRO;
    }

    /**
     * 去重复, 并转换成object集合
     *
     * @param result
     * @param list
     * @param <T>
     * @return
     */
    private <T> List<T> handler(List<T> result, List<ServerJobCardBean> list) {
        if (list == null) return result;
        for (ServerJobCardBean bean : list) {
            if (containsKey(bean.jobId)) continue;
            put(bean.jobId);
            result.add((T) bean);
        }
        return result;
    }

    /**
     * 不去重复, 并转换成object集合
     *
     * @param result
     * @param list
     * @param <T>
     * @return
     */
    private <T> List<T> transfer(List<T> result, List<ServerJobCardBean> list) {
        if (list == null) return result;
        for (ServerJobCardBean bean : list) {
            result.add((T) bean);
        }
        return result;
    }
}
