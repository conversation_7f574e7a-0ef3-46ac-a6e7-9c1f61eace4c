package com.hpbr.bosszhipin.common.report;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.ReportDialog;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.module.contacts.activity.ReportEvidenceActivity;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ZPUserReportProcessIdResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

/**
 * B/C举报管理类
 */
public class Report {

    private Builder builder;

    private Report(Builder builder) {
        this.builder = builder;
    }

    public void show() {
        if (UserManager.isGeekRole()) {
            geekReport(builder);
        } else {
            bossReport2(builder);
        }
        requestAndBgAction();
    }

    /**
     * 牛人端举报
     */
    private void geekReport(Builder builder) {
        GeekReport geekReport = new GeekReport();
        geekReport.show(builder.context, builder.reportedId, builder.jobId, builder.source, builder.securityId, builder.expectId, builder.imgUrl);
    }

    private void bossReport2(Builder builder) {
        StringBuilder urlBuilder = new StringBuilder();
        String urlReportChat = UserGrayFeatureManager.getInstance().getUrlReportChat();
        if (LText.empty(urlReportChat)) {
            urlReportChat = URLConfig.WEB_URL_BOSS_REPORT_GEEK;
        }

        urlBuilder.append(urlReportChat);
        urlBuilder.append("?");
        if (!TextUtils.isEmpty(builder.securityId)) {
            urlBuilder.append("securityId=").append(builder.securityId).append("&");
        }
        urlBuilder.append("targetId=").append(builder.expectId).append("&");
        urlBuilder.append("source=").append(builder.source).append("&");
        urlBuilder.append("identity=").append(UserManager.getUserRole().get()).append("&");
        if (urlBuilder.length() > 0) {
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        String urlString = urlBuilder.toString();
        TLog.print("ReportUrl", "ReportUrl = %s", urlString);
        Intent intent = new Intent(builder.context, WebViewActivity.class);
        intent.putExtra(Constants.DATA_URL, urlString);
        AppUtil.startActivity(builder.context, intent);
    }

    /**
     * 将本地页面跳转的来源转换为埋点所需要的source
     * <p>
     * 埋点detail-report-click：举报来源 source 0:旧版本未知 1:聊天 2:简历 3:职位详情 4:客服（714）5:面试详情页，6:极速处理页
     **/
    private int covertDetailReportClickSource(int source) {
        switch (source) {
            case ReportEvidenceActivity.SOURCE_CHAT:
                return 1;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME:
                return 2;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL:
                return 3;
            case ReportEvidenceActivity.SOURCE_CUSTOMER_SERVICE:
                return 4;
            case ReportEvidenceActivity.SOURCE_GEEK_INTERVIEW_FEEDBACK:
                return 5;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME_QUICK_HANDLER:
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_QUICK_HANDLER:
                return 6;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_SAFETIP:
                return 7;
            case ReportEvidenceActivity.SOURCE_GEEK_CHAT_SCREEN:
                return 9;
            case ReportEvidenceActivity.SOURCE_INTERVIEW_SECURITY_DIALOG:
                return 10;
            default:
                return 0;
        }
    }

    /**
     * 举报埋点L: 先调接口获取生成的流程id，然后埋点
     */
    public void requestAndBgAction() {
        SimpleApiRequest.POST(LoginURLConfig.URL_ZPUSER_USER_REPORT_PROCESS_ID)
                .setRequestCallback(new ApiRequestCallback<ZPUserReportProcessIdResponse>() {
                    @Override
                    public void onSuccess(ApiData<ZPUserReportProcessIdResponse> data) {
                        reportBgAction(null != data && null != data.resp ? data.resp.processId : "-1");
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        reportBgAction("-1");
                    }
                })
                .execute();
    }

    private void reportBgAction(String processId) {
        if (null != builder) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_DETAIL_REPORT_CLICK)
                    .param("p", String.valueOf(builder.reportedId))
                    .param("p2", String.valueOf(builder.jobId))
                    .param("p3", String.valueOf(builder.expectId))
                    .param("p4", processId)  //生成的流程id
                    .param("p6", String.valueOf(covertDetailReportClickSource(builder.source)))
                    .debug()
                    .build();
        }
    }

    public static class Builder {

        private Context context;
        //举报的userid
        private long reportedId;
        //职位Id
        private long jobId;
        //来源
        private int source;

        private String securityId;
        //期望
        private long expectId;

        protected String imgUrl;
        //Boss端举报Item点击回调
        protected ReportDialog.OnReportListener listener;
        //Boss端举报Item额外原因数据
        protected ReportDialog.ExtarReson extarReson;
        //举报内容Code
        protected long filterCode;
        //举报内容
        protected long filterName;


        protected int detailSourceFrom; //在职位详情页举报入口：1：右上角感叹号 2：温馨提示中的举报

        public void setFilterCode(long filteCode) {
            this.filterCode = filteCode;
        }

        public void setFilteName(long filteName) {
            this.filterName = filteName;
        }

        public Builder setContext(Context context) {
            this.context = context;
            return this;
        }

        public Builder setReportedId(long reportedId) {
            this.reportedId = reportedId;
            return this;
        }

        public Builder setJobId(long jobId) {
            this.jobId = jobId;
            return this;
        }

        public Builder setSource(int source) {
            this.source = source;
            return this;
        }

        public Builder setSecurityId(String securityId) {
            this.securityId = securityId;
            return this;
        }

        public Builder setImages(String imgUrl) {
            this.imgUrl = imgUrl;
            return this;
        }

        public Builder setExpectId(long expectId) {
            this.expectId = expectId;
            return this;
        }

        public Builder setListener(ReportDialog.OnReportListener listener) {
            this.listener = listener;
            return this;
        }

        public Builder setExtarReson(ReportDialog.ExtarReson extarReson) {
            this.extarReson = extarReson;
            return this;
        }

        public Builder setDetailSourceFrom(int detailSourceFrom) {
            this.detailSourceFrom = detailSourceFrom;
            return this;
        }

        public Report build(Context context) {

            if (null == context) {
                throw new IllegalStateException("context is not null !!!");
            }

            setContext(context);

            return new Report(this);
        }

    }
}
