package com.hpbr.bosszhipin.common;

import android.content.Context;
import android.content.res.ColorStateList;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.custom.ThemeConfigManager;
import com.hpbr.bosszhipin.config.custom.core.Spec;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.NightUtil;

/**
 * Author: zhouyou
 * Date: 2022/11/30
 */
public class AppMainThemeColorConfig {

    private static class SingletonHolder {
        private static final AppMainThemeColorConfig CONFIG = new AppMainThemeColorConfig();
    }

    public static AppMainThemeColorConfig get() {
        return SingletonHolder.CONFIG;
    }

    public F1Tab f1ColorRes(Context context) {
        return new F1Tab(context, ThemeConfigManager.getInstance().getF1ColorType());
    }

    public F1Tab defColorRes(Context context) {
        return new F1Tab(context, ColorType.THEME_ORIGIN);
    }

    public F4Tab f4ColorRes(Context context) {
        return new F4Tab(context, ThemeConfigManager.getInstance().getF4colorType());
    }

    public MainNavi mainNaviRes(Context context) {
        return new MainNavi(context, ThemeConfigManager.getInstance().getBottomColorType());
    }

    public MainTab mainTabRes(Context context) {
        return new MainTab(context, ThemeConfigManager.getInstance().getMainTabColorType());
    }

    public static class F1Tab {

        private final Context context;
        private final int colorType;

        public F1Tab(Context context, int colorType) {
            this.context = context;
            this.colorType = colorType;
        }

        /**
         * F1头部指示器文案
         * 0 - 选中文案
         * 1 - 未选中文案
         *
         * @return
         */
        public int[] getIndicatorTextSelectionColor() {
            int selectedColor;
            int normalColor;
            if (colorType == ColorType.THEME_DARK) {
                selectedColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
                normalColor = ContextCompat.getColor(context, R.color.color_99FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                selectedColor = ContextCompat.getColor(context, R.color.color_FF141414_E5FFFFFF);
                normalColor = ContextCompat.getColor(context, R.color.color_99292929_99FFFFFF);
            } else {
                normalColor = ContextCompat.getColor(context, R.color.color_FF858585);
                selectedColor = ContextCompat.getColor(context, R.color.color_FF141414_FFE6E6EB);
            }
            return new int[]{selectedColor, normalColor};
        }

        /**
         * F1头部指示器tab标签
         * 0 - 标签背景
         * 1 - 标签文案
         *
         * @return
         */
        public int[] getIndicatorTabBadgeColor() {
            int badgeColor;
            int badgeTextColor;
            if (colorType == ColorType.THEME_DARK) {
                badgeColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
                badgeTextColor = ContextCompat.getColor(context, R.color.color_FF5E5E5E);
            } else if (colorType == ColorType.THEME_LIGHT) {
                badgeColor = ContextCompat.getColor(context, R.color.color_FF15B3B3);
                badgeTextColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF);
            } else {
                badgeColor = ContextCompat.getColor(context, R.color.color_FF15B3B3);
                badgeTextColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF);
            }
            return new int[]{badgeColor, badgeTextColor};
        }

        /**
         * F1头部右侧图标颜色
         *
         * @return
         */
        public int getTopIconColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF292929_E6FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF292929_FFD2D2D6);
            }
            return color;
        }

        /**
         * F1头部右侧学生版切换按钮颜色
         *
         * @return
         */
        public int[] getTopStudentVersionButtonColor() {
            int borderColor;
            int textColor;
            if (colorType == ColorType.THEME_DARK) {
                textColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
                borderColor = R.drawable.bg_student_tag_dark;
            } else if (colorType == ColorType.THEME_LIGHT) {
                textColor = ContextCompat.getColor(context, R.color.color_FF292929_E6FFFFFF);
                borderColor = R.drawable.bg_student_tag_light;
            } else {
                textColor = ContextCompat.getColor(context, R.color.color_FF292929_E6FFFFFF);
                borderColor = R.drawable.bg_student_tag_light;
            }
            return new int[]{textColor, borderColor};
        }

        /**
         * 获取F1头部主题背景
         *
         * @return
         */
        public Source getTopThemeBackground() {
            int defBgRes;
            if (UserManager.isBossRole()) {
                defBgRes = R.mipmap.ic_boss_title_style_bg;
            } else {
                defBgRes = R.mipmap.ic_geek_title_style_bg;
            }

            String fileName = "";
            if (colorType > 0) {
                int stringRes;
                if (UserManager.isBossRole()) {
                    stringRes = NightUtil.isDarkMode(context) ? R.string.f1_b_dark : R.string.f1_b;
                } else {
                    stringRes = NightUtil.isDarkMode(context) ? R.string.f1_c_dark : R.string.f1_c;
                }
                String fileExt = context.getString(R.string.file_ext_webp);
                fileName = context.getString(stringRes, fileExt);
            }
            return ThemeConfigManager.getInstance().getF1TopPageSource(Spec.obj().setDefaultIdRes(defBgRes).setName(fileName).setAutoPlay(true).setRepeatCount(Spec.REPEAT_COUNT_INFINITE));
        }


        /**
         * F1头部标题文案【用于Boss端未完善状态】
         *
         * @return
         */
        public int getTopTitleTextColor() {
            int textColor;
            if (colorType == ColorType.THEME_DARK) {
                textColor = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E5FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                textColor = ContextCompat.getColor(context, R.color.color_FF141414_E5FFFFFF);
            } else {
                textColor = ContextCompat.getColor(context, R.color.color_FF141414_FFE6E6EB);
            }
            return textColor;
        }

        public int getColorType() {
            return colorType;
        }
    }

    public static class F4Tab {

        private final Context context;
        private final int colorType;

        public F4Tab(Context context, int colorType) {
            this.context = context;
            this.colorType = colorType;
        }

        public int getUserTitleColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF141414_E5FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF141414_FFE6E6EB);
            }
            return color;
        }

        public int getUserDescColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_CCFFFFFF_99FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_99FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_FF9E9EA1);
            }
            return color;
        }

        public int getUserDescSideIconColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_CCFFFFFF_99FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_99FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_FF9E9EA1);
            }
            return color;
        }


        public int getUserDescArrowIconColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_CCFFFFFF_99FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF858585_FF818185);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF858585_FF818185);
            }
            return color;
        }

        /**
         * 简历预览-文字颜色
         */
        public int getResumePreviewButtonTextColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF0D9EA3_FFFFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF0D9EA3_FFFFFFFF);
            }
            return color;
        }

        /**
         * 简历预览-边框颜色
         */
        public int getResumePreviewButtonBorderColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF15B3B3_FFFFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF15B3B3_FFFFFFFF);
            }
            return color;
        }


        public int getTopRightIconColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF292929_E6FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF292929_FFD2D2D6);
            }
            return color;
        }

        public int getUserTabNumColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_FFFFFFFF_E6FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF141414_E5FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF141414_99FFFFFF);
            }
            return color;
        }

        public int getUserTabDescColor() {
            int color;
            if (colorType == ColorType.THEME_DARK) {
                color = ContextCompat.getColor(context, R.color.color_CCFFFFFF_99FFFFFF);
            } else if (colorType == ColorType.THEME_LIGHT) {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_99FFFFFF);
            } else {
                color = ContextCompat.getColor(context, R.color.color_FF5E5E5E_FFFFFFFF);
            }
            return color;
        }

        /**
         * 获取F4主题背景【展开态】
         *
         * @return
         */
        public Source getExpandThemeBackground() {
            int defBgRes;
            if (UserManager.isBossRole()) {
                defBgRes = R.drawable.ic_boss_header_bg;
            } else {
                defBgRes = R.drawable.ic_geek_header_bg;
            }

            int stringRes;
            if (UserManager.isBossRole()) {
                if (NightUtil.isDarkMode(context)) {
                    stringRes = R.string.f4_b_dark;
                } else {
                    stringRes = R.string.f4_b;
                }
            } else {
                if (NightUtil.isDarkMode(context)) {
                    stringRes = R.string.f4_c_dark;
                } else {
                    stringRes = R.string.f4_c;
                }
            }

            String fileExt = context.getString(R.string.file_ext_webp);
            String fileName = context.getString(stringRes, fileExt);
            return ThemeConfigManager.getInstance().getF4TopPageSource(Spec.obj().setDefaultIdRes(defBgRes).setName(fileName).setAutoPlay(true).setRepeatCount(Spec.REPEAT_COUNT_INFINITE));
        }

        /**
         * 获取F4主题背景【收起态】
         *
         * @return
         */
        public Source getCollapseThemeBackground() {
            int defBgRes;
            if (UserManager.isBossRole()) {
                defBgRes = R.drawable.ic_b_f3_header_bg;
            } else {
                defBgRes = R.drawable.ic_c_f3_header_bg;
            }

            int stringRes;
            if (UserManager.isBossRole()) {
                if (NightUtil.isDarkMode(context)) {
                    stringRes = R.string.f4_b_up_dark;
                } else {
                    stringRes = R.string.f4_b_up;
                }
            } else {
                if (NightUtil.isDarkMode(context)) {
                    stringRes = R.string.f4_c_up_dark;
                } else {
                    stringRes = R.string.f4_c_up;
                }
            }

            String fileExt = context.getString(R.string.file_ext_webp);
            String fileName = context.getString(stringRes, fileExt);
            return ThemeConfigManager.getInstance().getF4TopPageSource(Spec.obj().setDefaultIdRes(defBgRes).setName(fileName).setAutoPlay(true).setRepeatCount(Spec.REPEAT_COUNT_INFINITE));
        }
    }

    public static class MainNavi {

        private final Context context;
        private final int colorType;

        private MainNavi(Context context, int colorType) {
            this.context = context;
            this.colorType = colorType;
        }

        /**
         * 获取主页导航栏主题背景
         *
         * @return
         */
        public Source getMainNaviBottomThemeBackground() {
            int stringRes;
            if (UserManager.isBossRole()) {
                stringRes = NightUtil.isDarkMode(context) ? R.string.bottom_navi_b_dark : R.string.bottom_navi_b;
            } else {
                stringRes = NightUtil.isDarkMode(context) ? R.string.bottom_navi_c_dark : R.string.bottom_navi_c;
            }
            String fileExt = context.getString(R.string.file_ext_webp);
            String fileName = context.getString(stringRes, fileExt);
            return ThemeConfigManager.getInstance().getBottomPageSource(Spec.obj().setName(fileName).setAutoPlay(true).setRepeatCount(Spec.REPEAT_COUNT_INFINITE));
        }

        /**
         * 获取主页导航栏tab的文字颜色
         *
         * @return
         */
        public boolean isMainNaviStatusBarThemeDark() {
            boolean isStatusBarDark;
            if (colorType == ColorType.THEME_DARK) {
                isStatusBarDark = false;
            } else if (colorType == ColorType.THEME_LIGHT) {
                isStatusBarDark = !NightUtil.isDarkMode(context);
            } else {
                isStatusBarDark = true;
            }
            return isStatusBarDark;
        }
    }

    public static class MainTab {
        private final Context context;
        private final int colorType;

        private MainTab(Context context, int colorType) {
            this.context = context;
            this.colorType = colorType;
        }

        /**
         * 获取主页导航栏tab的文字颜色
         */
        public ColorStateList getMainNaviTabTextColorSelector() {
            int tabColorSelector;
            if (colorType == ColorType.THEME_DARK) {
                tabColorSelector = R.drawable.tab_text_selector_theme_dark;
            } else if (colorType == ColorType.THEME_LIGHT) {
                tabColorSelector = R.drawable.tab_text_selector_theme_light;
            } else {
                tabColorSelector = R.drawable.tab_text_selector_green;
            }
            return ContextCompat.getColorStateList(context, tabColorSelector);
        }

    }

    public interface ColorType {
        int THEME_DARK = 1; // ui定制主题深色
        int THEME_LIGHT = 2; // ui定制主题浅色
        int THEME_ORIGIN = 0; // 主题原始
    }
}
