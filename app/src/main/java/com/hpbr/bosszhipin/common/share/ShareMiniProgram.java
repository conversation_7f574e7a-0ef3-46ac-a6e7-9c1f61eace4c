package com.hpbr.bosszhipin.common.share;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.business_export.report.ZPPayApmActionExport;
import com.hpbr.bosszhipin.common.DownloadPhotoCommon;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.share.ShareMiniInfo.SoureType;
import com.hpbr.bosszhipin.config.OtherConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.share.linteners.ShareType;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.activity.LActivity;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.AbsApiRequest;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.ComShareRequest;
import net.bosszhipin.api.ComShareResponse;
import net.bosszhipin.api.GeekSharePositionRequest;
import net.bosszhipin.api.GeekSharePositionResponse;
import net.bosszhipin.api.GetHomePageShareRequest;
import net.bosszhipin.api.GetHomePageShareResponse;
import net.bosszhipin.api.GetShareAppMessageRequest;
import net.bosszhipin.api.GetShareAppMessageResponse;
import net.bosszhipin.api.GetShareGeekRequest;
import net.bosszhipin.api.GetShareGroupRequest;
import net.bosszhipin.api.GetShareGroupResponse;
import net.bosszhipin.api.GetShareInterviewRequest;
import net.bosszhipin.api.GetShareTimelineRequest;
import net.bosszhipin.api.GetShareTimelineResponse;
import net.bosszhipin.api.PostShareUnionRequest;
import net.bosszhipin.api.ShareCallbackRequest;
import net.bosszhipin.api.ShareCallbackResponse;
import net.bosszhipin.api.bean.WxGetMiniTokenResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.UiThread;

/**
 * Created by zhangxiangdong on 2018/5/15 10:53.
 * <p>
 * 处理分享微信小程序
 */
public class ShareMiniProgram implements IShare {

    private static final String LOG_TAG = "ShareMiniProgram";
    private static final int THUMB_SIZE = 64; // px
    private static final long SIZE_128K = 128 * 1024L;
    private static final long SIZE_64K = 64 * 1024L;

    @NonNull
    private final WeakReference<Activity> host;
    private final IWXAPI wxapi;
    private final ShareMiniInfoProvider shareMiniInfoProvider;

    private static ShareMiniProgram mStaticShareInstance;
    public static ShareType mShareType;

    //region 兼容请求小程序信息失败，分享wap页面
    private final String shareTitle;
    private final String shareDesc;
    //endregion
    // 兼容低版本的网页链接
    private final String webpageUrl;
    // 分享短息信息
    private final String smsTitle;
    // 回调
    @NonNull
    private final WeakReference<Callback> callbackWeakRef;

    private ShareMiniProgram(Builder builder) {
        host = builder.host;
        shareMiniInfoProvider = builder.shareMiniInfoProvider;
        shareTitle = builder.shareTitle;
        shareDesc = builder.shareDesc;
        webpageUrl = builder.webpageUrl;
        smsTitle = builder.smsTitle;
        this.callbackWeakRef = new WeakReference<>(builder.callback);
        wxapi = WXAPIFactory.createWXAPI(App.getAppContext().getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        wxapi.registerApp(OtherConfig.WE_CHAT_APP_ID);

        ShareMiniProgram.mStaticShareInstance = this;
    }

    public static Builder newBuilder(Activity context) {
        return new Builder(context);
    }

    public static void sendShareCompleteBroadcast(ShareType type, boolean success, String desc) {
        if (mStaticShareInstance != null) {
            mStaticShareInstance.complete(type, success, desc);
        }
        mShareType = null;
        mStaticShareInstance = null;
    }

    public static ShareMiniInfoProvider getShareMiniInfoProvider(ShareMiniInfo info) {
        return new ShareMiniInfoProvider() {

            @Override
            public String getInterviewId() {
                return checkNonNull(info.getInterviewId());
            }

            @Override
            public String getEncryptInterviewId() {
                return checkNonNull(info.getEncryptInterviewId());
            }

            @Override
            public String getJobId() {
                return checkNonNull(info.getJobId());
            }

            @Override
            public String getGeekId() {
                return checkNonNull(info.getGeekId());
            }

            @Override
            public String getExpectId() {
                return checkNonNull(info.getExpectId());
            }

            @Override
            public String getSourceType() {
                return checkNonNull(info.getSourceType());
            }

            @Override
            public String getSecurityId() {
                return checkNonNull(info.getSecurityId());
            }

            @Override
            public String getParam() {
                return checkNonNull(info.getParam());
            }

            @Nullable
            @Override
            public Object getExtra() {
                return info.getExtra();
            }

            @Override
            public Bitmap getBitmap() {
                return info.getBitmap();
            }

            @NonNull
            private String checkNonNull(@Nullable String raw) {
                return raw == null ? "" : raw;
            }

        };
    }

    @NonNull
    private GeekSharePositionRequest newGeekSharePositionRequest() {
        GeekSharePositionRequest request = new GeekSharePositionRequest(new ApiRequestCallback<GeekSharePositionResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

//            @Override
//            public void handleInChildThread(ApiData<GeekSharePositionResponse> data) {
//                super.handleInChildThread(data);
//                getAvatarBitmapFromUrl();
//            }
//
//            @WorkerThread
//            private void getAvatarBitmapFromUrl() {
//                Object extra = shareMiniInfoProvider.getExtra();
//                if (extra instanceof GeekSharePositionCardView.CardData) {
//                    GeekSharePositionCardView.CardData cardData = (GeekSharePositionCardView.CardData) extra;
//                    cardData.bitmapAvatar(ShareBitmapUtils.createBitmapFromUrl(cardData.getAvatar()));
//                }
//            }

            @Override
            public void onSuccess(ApiData<GeekSharePositionResponse> data) {
                GeekSharePositionResponse resp = data.resp;
                handleShare(resp);
            }

            private void handleShare(@NonNull GeekSharePositionResponse resp) {
                if (host.get() == null) {
                    return;
                }

                final String miniAppId = resp.miniAppId;
                if (TextUtils.isEmpty(miniAppId)) {
                    /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                    shareWebpage(SendMessageToWX.Req.WXSceneSession);
                    return;
                }

                Bitmap bitmap = null;
                try {
                    Object extra = shareMiniInfoProvider.getExtra();
                    if (extra instanceof GeekSharePositionCardView.CardData) {
                        int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                        int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                        GeekSharePositionCardView cardView = new GeekSharePositionCardView(host.get());
                        GeekSharePositionCardView.CardData cardData = (GeekSharePositionCardView.CardData) extra;
                        cardData.bossUpLabels = resp.shareInfo != null ? resp.shareInfo.bossUpJobLabels : null;
                        cardData.titleIcons = resp.shareInfo != null ? resp.shareInfo.titleIcons : null;
                        cardView.setData(cardData);

                        cardView.measure(w, h);
                        cardView.layout(cardView.getLeft(), cardView.getTop(), cardView.getRight(), cardView.getBottom());
                        Bitmap b = Bitmap.createBitmap(cardView.getMeasuredWidth(), cardView.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
                        Canvas c = new Canvas(b);
                        cardView.draw(c);

                        bitmap = b;
                    } else {
                        /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                        shareWebpage(SendMessageToWX.Req.WXSceneSession);
                        return;
                    }
                } catch (Exception ignored) {
                    ignored.printStackTrace();
                }

                if (bitmap != null) {
                    // 分享小程序
                    shareMini(ShareMiniInfoWrapper.obj()
                        .miniAppId(miniAppId)
                        .link(resp.link)
                        .title(resp.title)
                        .bitmap(bitmap)
                        .webpageUrl(webpageUrl)
                    );
                } else {
                    ToastUtils.showText("分享失败，请重试");
                }
            }

            @Override
            public void onComplete() {
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = shareMiniInfoProvider.getSecurityId();
        return request;
    }

    /**
     * 根据sourceType获取拆分后的请求
     *
     * @param sourceType 各种后台定义的请求类型。1: 分享职位。2：BOSS分享牛人。4：分享面试
     * @return 请求实例
     */
    @SuppressWarnings("rawtypes")
    @Nullable
    private AbsApiRequest newRequestBySourceType(@Nullable String sourceType) {
        if (SoureType.TYPE_BOSS_SHARE_GEEK.equals(sourceType)) { // boss分享牛人
            return newShareGeekRequest();
        } else if (SoureType.TYPE_SHARE_INTERVIEW.equals(sourceType)) { // 面试分享
            return newShareInterviewRequest();
        } else if (SoureType.TYPE_GEEK_SHARE_GROUP.equals(sourceType)) { // C-JD-group组团求职
            return newShareJDGroupRequest();
        } else if (SoureType.TYPE_BOSS_UNION_SHARE.equals(sourceType)) { // boss联盟分享
            return newShareUnionRequest();
        } else if (SoureType.TYPE_GEEK_SHARE_POSITION.equals(sourceType)) { // 牛人分享职位
            return newGeekSharePositionRequest();
        } else if (SoureType.TYPE_HOME_PAGE_SHARE.equals(sourceType)) { // 牛人 boss主页 分享
            return getHomepageRequest();
        } else if (SoureType.TYPE_COMPANY_PAGE_SHARE.equals(sourceType)) { //  公司主页 分享公司
            if (UserManager.isGeekRole()) {
                return getCompanyShare();
            } else {
                Object extra = shareMiniInfoProvider.getExtra();
                if (extra instanceof ComShareResponse) {
                    ComShareResponse response = (ComShareResponse) extra;
                    // boss的话 在外面请求好数据了
                    shareMini(ShareMiniInfoWrapper.obj()
                        .miniAppId(response.miniAppId)
                        .link(response.link)
                        .title(response.title)
                        .bitmap(shareMiniInfoProvider.getBitmap())
                        .webpageUrl(webpageUrl));
                }

                return null;
            }

        } else if (SoureType.TYPE_INDUSTRY_PAGE_SHARE.equals(sourceType)) {
            return getIndustryRequest();
        } else {
            return newGetShareAppMessageRequest();
        }
    }

    private AbsApiRequest getIndustryRequest() {
        PostShareUnionRequest request = new PostShareUnionRequest(new SimpleApiRequestCallback<GetShareAppMessageResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GetShareAppMessageResponse> data) {
                super.onSuccess(data);
                GetShareAppMessageResponse resp = data.resp;
                shareMini(ShareMiniInfoWrapper.obj()
                    .miniAppId(resp.miniAppId)
                    .link(resp.link)
                    .title(shareMiniInfoProvider.getParam())
                    .bitmap(shareMiniInfoProvider.getBitmap())
                    .webpageUrl(webpageUrl));
            }

            @Override
            public void onComplete() {
                super.onComplete();
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                ToastUtils.showText(reason.getErrReason());
            }


        });

        request.bizType = "9";
        request.securityId = shareMiniInfoProvider.getSecurityId();

        return request;
    }

    @NonNull
    private GetShareAppMessageRequest newGetShareAppMessageRequest() {
        GetShareAppMessageRequest request = new GetShareAppMessageRequest(getShareMiniCallback(false));

        String encyptInterviewId = shareMiniInfoProvider.getEncryptInterviewId();

        if (!TextUtils.isEmpty(encyptInterviewId)) {
            request.encryptInterviewId = encyptInterviewId;
        } else {
            request.interviewId = shareMiniInfoProvider.getInterviewId();
        }
        request.encryptInterviewId = shareMiniInfoProvider.getEncryptInterviewId();
        request.jobId = shareMiniInfoProvider.getJobId();
        request.geekId = shareMiniInfoProvider.getGeekId();
        request.expectId = shareMiniInfoProvider.getExpectId();
        request.sourceType = shareMiniInfoProvider.getSourceType();
        request.securityId = shareMiniInfoProvider.getSecurityId();
        request.param = shareMiniInfoProvider.getParam();
        return request;
    }

    /* boss分享牛人 */
    @NonNull
    private GetShareGeekRequest newShareGeekRequest() {
        GetShareGeekRequest request = new GetShareGeekRequest(getShareMiniCallback(true));
        request.geekId = shareMiniInfoProvider.getGeekId();
        request.expectId = shareMiniInfoProvider.getExpectId();
        request.jobId = shareMiniInfoProvider.getJobId();
        request.securityId = shareMiniInfoProvider.getSecurityId();
        return request;
    }

    /* 组团求职 */
    @NonNull
    private GetShareGroupRequest newShareJDGroupRequest() {
        GetShareGroupRequest request = new GetShareGroupRequest(getShareGroupCallback());
        return request;
    }

    @NonNull
    private GetShareInterviewRequest newShareInterviewRequest() {
        GetShareInterviewRequest request = new GetShareInterviewRequest(getInterviewMiniProgramCallback());

        String encyptInterviewId = shareMiniInfoProvider.getEncryptInterviewId();
        if (!TextUtils.isEmpty(encyptInterviewId)) {
            request.encryptInterviewId = encyptInterviewId;
        } else {
            request.interviewId = shareMiniInfoProvider.getInterviewId();
        }

        return request;
    }

    /* boss联盟分享小程序 */
    @Nullable
    private PostShareUnionRequest newShareUnionRequest() {
        PostShareUnionRequest request = new PostShareUnionRequest(getShareMiniCallback(false));
        String param = shareMiniInfoProvider.getParam();
        if (param == null) {
            L.e(LOG_TAG, "BOSS联盟分享参数为空");
            return null;
        }

        try {
            param = URLDecoder.decode(param, "UTF-8");
            JSONObject jsonObject = new JSONObject(param);
//            String forwardId = String.valueOf(jsonObject.optInt("forwardId"));
//            String encryptedForwardId = jsonObject.optString("encryptedForwardId");
//            String wxShareImg = jsonObject.optString("wxShareImg");

            Map<String, String> params = new HashMap<>();
            try {
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    String value = jsonObject.optString(key);
                    params.put(key, value);
                }
            } catch (Exception e) {
                L.e(LOG_TAG, e.getMessage());
                return null;
            }

            request.extra_map = params;
            return request;
        } catch (JSONException e) {
            L.e(LOG_TAG, "BOSS联盟，JSON解析出错：" + param);
            return null;
        } catch (UnsupportedEncodingException e) {
            L.e(LOG_TAG, "BOSS联盟，解码出错：" + param);
            return null;
        }
    }

    /**
     * @param canShareH5 是否可以分享H5链接
     */
    @NonNull
    private ApiRequestCallback<GetShareAppMessageResponse> getShareMiniCallback(boolean canShareH5) {
        return new ApiRequestCallback<GetShareAppMessageResponse>() {
            @Override
            public void onSuccess(ApiData<GetShareAppMessageResponse> data) {
                final GetShareAppMessageResponse resp = data.resp;

                /* 先下载封面图 */
                final String imgUrl = resp.imgUrl;
                final String miniAppId = resp.miniAppId;
                if (canShareH5) {
                    // 分享H5链接
                    shareH5(resp);
                    return;
                }
                if (TextUtils.isEmpty(imgUrl) || TextUtils.isEmpty(miniAppId)) {
                    /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                    shareWebpage(SendMessageToWX.Req.WXSceneSession);
                    dismissProgress();
                    return;
                }

                DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
                downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
                    @Override
                    public void onDownloadComplete(Bitmap bitmap) {
                        dismissProgress();
                        if (bitmap == null) {
                            showShareFailed();
                            return;
                        }

                        shareMini(ShareMiniInfoWrapper.obj()
                            .miniAppId(miniAppId)
                            .link(resp.link)
                            .title(resp.title)
                            .desc(resp.desc)
                            .bitmap(bitmap)
                            .webpageUrl(webpageUrl)
                        );
                    }

                    @Override
                    public void onDownloadFailed() {
                        showShareFailed();
                        dismissProgress();
                    }

                });
                downloadPhotoCommon.onNewDownloadTask(imgUrl);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.e(LOG_TAG, reason.getErrReason());
                ToastUtils.showText("分享失败");
                dismissProgress();
            }
        };
    }

    @NonNull
    private ApiRequestCallback<GetShareGroupResponse> getShareGroupCallback() {
        return new ApiRequestCallback<GetShareGroupResponse>() {
            @Override
            public void onSuccess(ApiData<GetShareGroupResponse> data) {
                final GetShareGroupResponse resp = data.resp;

                /* 先下载封面图 */
                final String imgUrl = resp.image;
                final String miniAppId = resp.miniAppId;
                if (TextUtils.isEmpty(imgUrl) || TextUtils.isEmpty(miniAppId)) {
                    /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                    shareWebpage(SendMessageToWX.Req.WXSceneSession);
                    dismissProgress();
                    return;
                }

                DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
                downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
                    @Override
                    public void onDownloadComplete(Bitmap bitmap) {
                        dismissProgress();
                        if (bitmap == null) {
                            showShareFailed();
                            return;
                        }

                        shareMini(ShareMiniInfoWrapper.obj()
                            .miniAppId(miniAppId)
                            .link(resp.link)
                            .title(resp.title)
                            .bitmap(bitmap)
                            .webpageUrl(webpageUrl)
                        );
                    }

                    @Override
                    public void onDownloadFailed() {
                        showShareFailed();
                        dismissProgress();
                    }

                });
                downloadPhotoCommon.onNewDownloadTask(imgUrl);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.e(LOG_TAG, reason.getErrReason());
                ToastUtils.showText("分享失败");
                dismissProgress();
            }
        };
    }


    // 905面试小程序分享 - 图片改为客户端绘制，不再使用接口返回url
    @NonNull
    private ApiRequestCallback<GetShareAppMessageResponse> getInterviewMiniProgramCallback() {
        return new ApiRequestCallback<GetShareAppMessageResponse>() {
            @Override
            public void onSuccess(ApiData<GetShareAppMessageResponse> data) {
                final GetShareAppMessageResponse resp = data.resp;

                if (resp != null && !LText.empty(resp.h5Link)) {
                    shareH5(resp);
                    return;
                }

                final String miniAppId = resp.miniAppId;
                if (TextUtils.isEmpty(miniAppId)) {
                    /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                    shareWebpage(SendMessageToWX.Req.WXSceneSession);
                    dismissProgress();
                    return;
                }

                shareMini(ShareMiniInfoWrapper.obj()
                    .miniAppId(miniAppId)
                    .link(resp.link)
                    .title(resp.title)
                    .desc(resp.desc)
                    .bitmap(shareMiniInfoProvider.getBitmap())
                    .webpageUrl(webpageUrl));
            }

            @Override
            public void onComplete() {
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.e(LOG_TAG, reason.getErrReason());
                ToastUtils.showText("分享失败");
                dismissProgress();
            }
        };
    }

    /**
     * 分享 牛人 boss 主页
     *
     * @return
     */
    private GetHomePageShareRequest getHomepageRequest() {
        GetHomePageShareRequest request = new GetHomePageShareRequest(new ApiRequestCallback<GetHomePageShareResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GetHomePageShareResponse> data) {
                GetHomePageShareResponse resp = data.resp;
                shareMini(ShareMiniInfoWrapper.obj()
                        .miniAppId(resp.orginId)
                        .link(resp.miniAppUrl)
//                        .title("我的个人主页")
                        .title(TextUtils.isEmpty(resp.shareTitle) ? "我的个人主页" : resp.shareTitle)
                        .bitmap(shareMiniInfoProvider.getBitmap())
                        .webpageUrl(webpageUrl)
                );
            }

            @Override
            public void onComplete() {
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
//        request.securityId = shareMiniInfoProvider.getSecurityId();
        return request;
    }

    private ComShareRequest getCompanyShare() {
        ComShareRequest request = new ComShareRequest(new ApiRequestCallback<ComShareResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<ComShareResponse> data) {
                ComShareResponse resp = data.resp;
                shareMini(ShareMiniInfoWrapper.obj()
                    .miniAppId(resp.miniAppId)
                    .link(resp.link)
                    .title(resp.title)
                    .bitmap(shareMiniInfoProvider.getBitmap())
                    .webpageUrl(webpageUrl)
                );
            }

            @Override
            public void onComplete() {
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = shareMiniInfoProvider.getSecurityId();
        return request;
    }

    private void shareWebpage(int scene) {
        WXWebpageObject object = new WXWebpageObject();
        object.webpageUrl = webpageUrl;
        WXMediaMessage message = new WXMediaMessage(object);
        message.title = shareTitle;
        message.description = shareDesc;
        // message.thumbData = avatar;
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("web");
        req.scene = scene;
        req.message = message;
        wxapi.sendReq(req);
    }

    private void shareH5(@Nullable GetShareAppMessageResponse resp) {
        if (resp == null) {
            ToastUtils.showText(R.string.string_share_failed);
            return;
        }
        DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
        downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
            @Override
            public void onDownloadComplete(Bitmap bitmap) {
                dismissProgress();
                if (bitmap == null) {
                    showShareFailed();
                    return;
                }
                WXWebpageObject object = new WXWebpageObject();
                object.webpageUrl = !LText.empty(resp.h5Link) ? resp.h5Link : resp.link;
                WXMediaMessage message = new WXMediaMessage(object);
                message.title = resp.title;
                message.description = resp.desc;
                message.thumbData = compressBitmap(bitmap, SIZE_64K);
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction = buildTransaction("web");
                req.scene = SendMessageToWX.Req.WXSceneSession;
                req.message = message;
                try {
                    boolean shareSuccess = wxapi.sendReq(req);
                    if (!shareSuccess) {
                        showShareFailed();
                        TLog.error(LOG_TAG, "share failed, webpageUrl=%s, title=%s, description=%s, thumbData.length=%d", resp.link, resp.title, resp.desc, message.thumbData.length);
                    }
                } catch (Exception e) {
                    TLog.error(LOG_TAG, e.getMessage());
                }
            }

            @Override
            public void onDownloadFailed() {
                showShareFailed();
                dismissProgress();
            }

        });
        downloadPhotoCommon.onNewDownloadTask(resp.imgUrl);
    }

    private void showShareFailed() {
        App.get().getMainHandler().post(() -> ToastUtils.showText(R.string.string_share_failed));
    }

    @Override
    public void shareWechat() {
        if (wechatNotInstalled()) {
            ToastUtils.showText("未检测到您的微信客户端");
            complete(mShareType, false, "未检测到您的微信客户端");
            return;
        }

        mShareType = ShareType.WECHAT;

        shareStart(mShareType);

        showProgress();
        //noinspection rawtypes
        AbsApiRequest req = newRequestBySourceType(shareMiniInfoProvider.getSourceType());
        if (req != null) {
            //noinspection unchecked
            HttpExecutor.execute(req);
        } else {
            // Invalid request
            dismissProgress();
            if (!SoureType.TYPE_COMPANY_PAGE_SHARE.equals(shareMiniInfoProvider.getSourceType())) {
                ToastUtils.showText("分享失败，请稍候重试");
            }
        }
    }

    /**
     * 分享到微信好友
     */
    @Override
    public void shareWechatFriend(@NonNull GeekSharePositionResponse resp) {
        if (wechatNotInstalled()) {
            ToastUtils.showText("未检测到您的微信客户端");
            complete(mShareType, false, "未检测到您的微信客户端");
            return;
        }

        mShareType = ShareType.WECHAT;

        shareStart(mShareType);

        if (host.get() == null) {
            return;
        }

        final String miniAppId = resp.miniAppId;
        if (TextUtils.isEmpty(miniAppId)) {
            /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
            shareWebpage(SendMessageToWX.Req.WXSceneSession);
            return;
        }

        Bitmap bitmap = null;
        try {
            Object extra = shareMiniInfoProvider.getExtra();
            if (extra instanceof GeekSharePositionCardView.CardData) {
                int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                GeekSharePositionCardView cardView = new GeekSharePositionCardView(host.get());
                cardView.setData((GeekSharePositionCardView.CardData) extra);

                cardView.measure(w, h);
                cardView.layout(cardView.getLeft(), cardView.getTop(), cardView.getRight(), cardView.getBottom());
                Bitmap b = Bitmap.createBitmap(cardView.getMeasuredWidth(), cardView.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
                Canvas c = new Canvas(b);
                cardView.draw(c);

                bitmap = b;
            } else {
                /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                shareWebpage(SendMessageToWX.Req.WXSceneSession);
                return;
            }
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

        if (bitmap != null) {
            // 分享小程序
            shareMini(ShareMiniInfoWrapper.obj()
                .miniAppId(miniAppId)
                .link(resp.link)
                .title(resp.title)
                .bitmap(bitmap)
                .webpageUrl(webpageUrl)
            );
        } else {
            ToastUtils.showText("分享失败，请重试");
        }
    }

    @Override
    public void shareMoment() {
        if (wechatNotInstalled()) {
            ToastUtils.showText("未检测到您的微信客户端");
            complete(mShareType, false, "未检测到您的微信客户端");
            return;
        }

        mShareType = ShareType.WEMOMENT;

        showProgress();
        // 1、请求getShareTimeline接口获取图片地址
        GetShareTimelineRequest request = new GetShareTimelineRequest(getShareTimelineCallback());
        request.jobId = shareMiniInfoProvider.getJobId();
        request.geekId = shareMiniInfoProvider.getGeekId();
        request.expectId = shareMiniInfoProvider.getExpectId();
        request.sourceType = shareMiniInfoProvider.getSourceType();
        request.securityId = shareMiniInfoProvider.getSecurityId();
        //noinspection unchecked
        HttpExecutor.execute(request);
    }

    private void dismissProgress() {
        Activity host = this.host.get();
        if (host instanceof BaseActivity) {
            ((BaseActivity) host).dismissProgressDialog();
        } else if (host instanceof LActivity) {
            ((LActivity) host).dismissProgressDialog();
        }
    }

    private void showProgress() {
        Activity host = this.host.get();
        if (host instanceof BaseActivity) {
            ((BaseActivity) host).showProgressDialog("准备分享中……");
        } else if (host instanceof LActivity) {
            ((LActivity) host).showProgressDialog("准备分享中……");
        }
    }

    private static String buildTransaction(String s) {
        return s;
    }

    private boolean wechatNotInstalled() {
        return !wxapi.isWXAppInstalled();
    }

    @Override
    public void shareSms() {
        Uri uri = Uri.parse("smsto:");
        Intent intent = new Intent(Intent.ACTION_SENDTO, uri);
        intent.putExtra("sms_body", smsTitle);
        try {
            Activity host = this.host.get();
            host.startActivity(intent);
        } catch (Exception e) {
            ToastUtils.showText("没有找到可用的短信");
        }
    }

    @Override
    public void shareQQ() {

    }

    @SuppressWarnings("unused")
    public static final class Builder {
        private final WeakReference<Activity> host;
        private ShareMiniInfoProvider shareMiniInfoProvider;
        private String shareTitle;
        private String shareDesc;
        private String webpageUrl;
        private String smsTitle;
        private Callback callback;

        private Builder(Activity host) {
            this.host = new WeakReference<>(host);
        }

        public Builder shareMini(ShareMiniInfoProvider shareMiniInfoProvider) {
            this.shareMiniInfoProvider = shareMiniInfoProvider;
            return this;
        }

        public Builder setShareTitle(String shareTitle) {
            this.shareTitle = shareTitle;
            return this;
        }

        public Builder setShareDesc(String shareDesc) {
            this.shareDesc = shareDesc;
            return this;
        }

        public Builder setWebpageUrl(String webpageUrl) {
            this.webpageUrl = webpageUrl;
            return this;
        }

        public Builder setSmsTitle(String smsTitle) {
            this.smsTitle = smsTitle;
            return this;
        }

        public Builder setCallback(Callback callback) {
            this.callback = callback;
            return this;
        }

        public ShareMiniProgram build() {
            return new ShareMiniProgram(this);
        }
    }

    @NonNull
    private ApiRequestCallback<GetShareTimelineResponse> getShareTimelineCallback() {
        return new ApiRequestCallback<GetShareTimelineResponse>() {

            @Override
            public void onSuccess(ApiData<GetShareTimelineResponse> data) {
                final GetShareTimelineResponse response = data.resp;
                final String imgUrl = response.imgUrl;
                if (TextUtils.isEmpty(imgUrl)) {
                    /* 获取小程序分享信息失败，使用原分享wap页面方式分享 */
                    shareWebpage(SendMessageToWX.Req.WXSceneTimeline);
                    dismissProgress();
                    return;
                }

                // 2、下载图片
                DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
                downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
                    @Override
                    public void onDownloadComplete(final Bitmap bitmap) {
                        // 3、分享下载回来的图片
                        if (bitmap != null) {
                            shareImage(bitmap);
                        } else {
                            showShareFailed();
                            complete(mShareType, false, "分享失败");
                        }
                        dismissProgress();
                    }

                    private void shareImage(Bitmap bmp) {
                        WXImageObject imgObj = new WXImageObject(bmp);
                        WXMediaMessage msg = new WXMediaMessage();
                        msg.mediaObject = imgObj;
                        Bitmap thumb = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
                        msg.thumbData = bitmapToByte(thumb);
                        thumb.recycle();
                        SendMessageToWX.Req req = new SendMessageToWX.Req();
                        req.transaction = buildTransaction("img");
                        req.message = msg;
                        req.scene = SendMessageToWX.Req.WXSceneTimeline;

                        wxapi.sendReq(req);

                        complete(mShareType, true, "分享成功");
                    }

                    private byte[] bitmapToByte(Bitmap b) {
                        ByteArrayOutputStream o = null;
                        try {
                            o = new ByteArrayOutputStream();
                            b.compress(Bitmap.CompressFormat.PNG, 80, o);
                            return o.toByteArray();
                        } finally {
                            if (o != null) {
                                try {
                                    o.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }

                    @Override
                    public void onDownloadFailed() {
                        // 下载失败，提示“分享失败”
                        showShareFailed();
                        complete(mShareType, false, "分享失败");
                        dismissProgress();
                    }
                });
                downloadPhotoCommon.onNewDownloadTask(imgUrl);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.e(LOG_TAG, reason.getErrReason());
                ToastUtils.showText("分享失败");
                dismissProgress();
            }
        };
    }

    @SuppressWarnings("unused")
    public static void executeShareJobCallback(String jobId) {
        ShareCallbackRequest request = new ShareCallbackRequest(new ApiRequestCallback<ShareCallbackResponse>() {
            @Override
            public void onSuccess(ApiData<ShareCallbackResponse> data) {
                L.d(LOG_TAG, data.resp.msg);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.e(LOG_TAG, reason.getErrReason());
            }
        });
        request.jobId = jobId;
        //noinspection unchecked
        HttpExecutor.execute(request);
    }

    public void shareMini(@NonNull ShareMiniInfoWrapper wrapper) {
        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();

        miniProgramObj.webpageUrl = TextUtils.isEmpty(wrapper.getWebpageUrl()) ? "https://m.zhipin.com/" : wrapper.getWebpageUrl();
        miniProgramObj.miniprogramType = OtherConfig.WE_CHAT_MINIPROGRAM_TYPE;
        miniProgramObj.userName = wrapper.getMiniAppId(); // 小程序原始id
        miniProgramObj.path = wrapper.getLink(); // 小程序路径
        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        msg.title = wrapper.getTitle();
        msg.description = wrapper.getDesc();
        if (wrapper.getBitmap() != null) {
            msg.thumbData = compressBitmap(wrapper.getBitmap(), SIZE_128K); // 小程序消息封面图片，小于128k
        }
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("miniprogram");
        req.message = msg;
        req.scene = SendMessageToWX.Req.WXSceneSession;

        wxapi.sendReq(req);

        complete(mShareType, true, "分享成功");
    }

    //region 打开微信小程序

    /**
     * 弹起确认前往微信的弹窗，确认后打开微信
     *
     * @param userName         小程序原始id
     * @param path             小程序路径
     * @param shouldDecodePath 是否需要解码路径
     */

    public static void openMiniProgram(@NonNull Activity activity, @NonNull String userName, @NonNull String path, boolean shouldDecodePath, boolean showAlertDialog) {
        if (showAlertDialog) {
            new DialogUtils.Builder(activity)
                    .setDoubleButton()
                    .setTitle(LText.getString(R.string.string_geek_open_wechat_alert))
                    .setNegativeAction(R.string.string_cancel)
                    .setPositiveAction(R.string.string_continue, new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            openMiniProgram(activity, userName, path, shouldDecodePath);
                        }
                    })
                    .build()
                    .show();

        } else {
            openMiniProgram(activity, userName, path, shouldDecodePath);
        }
    }

    /**
     * 直接打开微信小程序
     *
     * @param userName         小程序原始id
     * @param path             小程序路径
     * @param shouldDecodePath 是否需要解码路径
     */
    public static void openMiniProgram(@NonNull Context context, @NonNull String userName, @NonNull String path, boolean shouldDecodePath) {
        openMiniProgram(context, userName, path, shouldDecodePath, "您未安装微信，请先下载安装");
    }

    private static void openMiniProgram(@NonNull Context context, @NonNull String userName, @NonNull String path, boolean shouldDecodePath, @NonNull String noWechatToast) {
        // 先请求接口获取 miniToken
        SimpleApiRequest.GET(LoginURLConfig.URL_ZPPASSPORT_WX_GET_MINI_TOKEN)
            .setRequestCallback(new SimpleApiRequestCallback<WxGetMiniTokenResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    if (context instanceof BaseActivity) {
                        ((BaseActivity) context).showProgressDialog();
                    }
                }

                @Override
                public void onSuccess(ApiData<WxGetMiniTokenResponse> data) {
                    super.onSuccess(data);
                    // 拼接 miniToken参数
                    realOpenMiniProgram(context, userName, path, data.resp.miniToken, shouldDecodePath, noWechatToast);
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    super.onFailed(reason);
                    realOpenMiniProgram(context, userName, path, null, shouldDecodePath, noWechatToast);
                }

                @Override
                public void onComplete() {
                    super.onComplete();
                    if (context instanceof BaseActivity) {
                        ((BaseActivity) context).dismissProgressDialog();
                    }
                }
            })
            .execute();
    }

    @NonNull
    public static String shouldAppendTokenOnPath(@NonNull String originalPath, @NonNull String miniToken) {
        if (!LText.empty(miniToken) && !LText.empty(originalPath)) {
            if (originalPath.contains("?")) {
                // pages/service/service?key=value
                originalPath += "&miniToken=" + miniToken;
            } else {
                // pages/service/service
                originalPath += "?miniToken=" + miniToken;
            }
        }
        return originalPath;
    }

    private static void realOpenMiniProgram(@NonNull Context context,
                                            @NonNull String userName,
                                            @NonNull String path,
                                            @Nullable String miniToken,
                                            boolean shouldDecodePath,
                                            String noWechatToast) {
        IWXAPI api = WXAPIFactory.createWXAPI(context, OtherConfig.WE_CHAT_APP_ID);

        boolean wxAppNotInstalled = !api.isWXAppInstalled();
        if (wxAppNotInstalled) {
            ToastUtils.showText(noWechatToast);
            // 1215 支付流程打点，添加小程序拉起流程失败打点
            ZPPayApmActionExport.onLaunchMiniProgramFailure(userName, path, miniToken, 1, noWechatToast);
            return;
        }

        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = userName; // 填小程序原始id

        if (shouldDecodePath) {
            try {
                String decodedPath = URLDecoder.decode(path, "UTF-8");
                if (miniToken == null) {
                    req.path = decodedPath; // 拉起小程序页面的可带参路径，不填默认拉起小程序首页
                } else {
                    req.path = shouldAppendTokenOnPath(decodedPath, miniToken);
                }
            } catch (Throwable e) {
                L.e(LOG_TAG, e.getMessage());
                ToastUtils.showText(context, "开启失败，请稍候重试");
                return;
            }
        } else {
            if (miniToken == null) {
                req.path = path;
            } else {
                req.path = shouldAppendTokenOnPath(path, miniToken);
            }
        }

        req.miniprogramType = OtherConfig.WE_CHAT_LAUNCH_MINIPROGRAM_TYPE; // 可选打开 开发版，体验版和正式版
        try {
            boolean ret = api.sendReq(req);
            if (ret) {
                // 1215 支付流程打点，添加小程序拉起成功打点
                ZPPayApmActionExport.onLaunchMiniProgram(userName, path, miniToken);
            } else {
                ToastUtils.showText(context, "开启失败，微信内部错误");
                // 1215 支付流程打点，小程序拉起失败上报
                ZPPayApmActionExport.onLaunchMiniProgramFailure(userName, path, miniToken, 2, "微信内部错误");
            }
        } catch (Exception e) {
            TLog.error(LOG_TAG, e.getMessage());
            // 1215 支付流程打点，添加小程序拉起失败打点
            ZPPayApmActionExport.onLaunchMiniProgramFailure(userName, path, miniToken, 3, e.getMessage());

            // https://developers.weixin.qq.com/community/develop/doc/000a4467c34060096900259726bc00
            ToastUtils.showText(context, "检查你的微信是否是最新版本。如果不是最新版本，建议更新到最新版本，因为旧版本的微信可能不支持新版本的小程序");
        }
    }

    /**
     * 7.02新增，通过打开小程序来关注Boss公众号
     */
    public static void openMiniProgramForOfficialAccount(@NonNull Context context, @InvokeMiniFrom int from) {
        try {
            String path = OtherConfig.MINI_PATH_FOR_OFFICIAL_ACCOUNT;
            UserBean userBean = UserManager.getLoginUser();
            if (userBean != null && !LText.empty(userBean.weiXinSecurityUid)) {
                path += "?uid=" + userBean.weiXinSecurityUid + "&from=" + from; // 追加用户id和调起来源参数
            } else {
                ToastUtils.showText(context, "开启失败，请稍候重试");
                return;
            }

            L.d(LOG_TAG, "mini Path: " + path);

            openMiniProgram(context, OtherConfig.MINI_ID_FOR_OFFICIAL_ACCOUNT, path, false);
        } catch (Exception e) {
            L.e(LOG_TAG, e.getMessage());
            ToastUtils.showText(context, "开启失败，请稍候重试");
        }
    }

    /**
     * 7.08新增，打开直猎邦小程序
     */
    public static void openZhiLieBangMiniProgram(@NonNull Context context, @NonNull String originID, @NonNull String miniPath) {
        openMiniProgram(context, originID, miniPath, false, "您当前没有安装微信，可自行查找直猎邦登陆。");
    }
    //endregion

    @UiThread
    public static void shareMiniProgram(@NonNull ShareMiniInfoWrapper wrapper) {
        IWXAPI wxapi = WXAPIFactory.createWXAPI(App.getAppContext().getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        wxapi.registerApp(OtherConfig.WE_CHAT_APP_ID);
        if (!wxapi.isWXAppInstalled()) {
            App.get().getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showText("未检测到您的微信客户端");
                }
            });
            return;
        }

        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();

        miniProgramObj.webpageUrl = TextUtils.isEmpty(wrapper.getWebpageUrl()) ? "https://m.zhipin.com/" : wrapper.getWebpageUrl();
        miniProgramObj.miniprogramType = OtherConfig.WE_CHAT_MINIPROGRAM_TYPE;
        miniProgramObj.userName = wrapper.getMiniAppId(); // 小程序原始id
        miniProgramObj.path = wrapper.getLink(); // 小程序路径
        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        msg.title = wrapper.getTitle();
        msg.description = wrapper.getDesc();

        msg.thumbData = compressBitmap(wrapper.getBitmap(), SIZE_128K); // 小程序消息封面图片，小于128k

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("miniprogram");
        req.message = msg;
        req.scene = SendMessageToWX.Req.WXSceneSession;

        wxapi.sendReq(req);
    }

    private static byte[] compressBitmap(@NonNull Bitmap bitmap, long maxStreamLength) {
        // 检测图片尺寸，需要小于128K
        long streamLength = maxStreamLength;
        int compressQuality = 105;
        ByteArrayOutputStream bmpStream = new ByteArrayOutputStream();
        while (streamLength >= maxStreamLength && compressQuality > 5) {
            try {
                bmpStream.flush();
                bmpStream.reset();
            } catch (IOException e) {
                e.printStackTrace();
            }
            compressQuality -= 5;
            bitmap.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpStream);
            byte[] bmpPicByteArray = bmpStream.toByteArray();
            streamLength = bmpPicByteArray.length;
        }
        return bmpStream.toByteArray();
    }

    private void shareStart(ShareType type) {
        shareStart(type, 0, "");
    }

    private void shareStart(ShareType type, @SuppressWarnings("SameParameterValue") int subType, @SuppressWarnings("SameParameterValue") String desc) {
        if (callbackWeakRef.get() != null) {
            callbackWeakRef.get().onStart(type, subType, desc);
        }
    }

    private void complete(ShareType type, boolean success, String desc) {
        complete(type, success, 0, desc);
    }

    private void complete(ShareType type, boolean success, @SuppressWarnings("SameParameterValue") int subType, String desc) {
        if (callbackWeakRef.get() != null) {
            callbackWeakRef.get().onComplete(type, success, subType, desc);
        }
    }

    @IntDef({InvokeMiniFrom.FROM_F1, InvokeMiniFrom.FROM_F2, InvokeMiniFrom.FROM_F3})
    @Retention(RetentionPolicy.SOURCE)
    public @interface InvokeMiniFrom {
        int FROM_F1 = 1; // F1弹窗引导
        int FROM_F2 = 2; // F2悬浮入口
        int FROM_F3 = 3; // F3设置菜单
        int FROM_CHAT_DIALOG = 7; //  聊天页面强提醒弹窗
        int FROM_CHAT_SETTING = 8; //  聊天设置页面开启强提醒
    }

}
