package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.GeekBaseCardBean;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.contacts.exchange.RejectHttpManager;
import com.hpbr.bosszhipin.module.main.fragment.geek.dialog.GeekCommonImproperReasonDialog;
import com.hpbr.bosszhipin.module.position.view.MaxHeightRecyclerView;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.F1FeedBackCardResponse;
import net.bosszhipin.api.GeekFeedBackStyleResponse;
import net.bosszhipin.api.PostImproperReasonRequest;
import net.bosszhipin.api.PostImproperReasonResponse;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.F1FeedBackCardBean;
import net.bosszhipin.api.bean.ServerF1CardFeedBackBean;
import net.bosszhipin.api.bean.ServerFeedBackResultBean;
import net.bosszhipin.api.bean.ServerFeedBackTextBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * create by guofeng
 * date on 2023/4/17
 */

public class GeekFeedBackStyleDialog {


    public static abstract class IBaseFeedBackStyle {

        public abstract void loadData(String securityId);

        public abstract void postFeedBack(String text, int code, int type);

        private ProgressDialog progressDialog;

        private Activity topActivity;

        protected void showProgressDialog() {
            topActivity = ForegroundUtils.get().getTopActivity();
            if (ActivityUtils.isValid(topActivity)) {
                if (progressDialog == null) {
                    progressDialog = new ProgressDialog(topActivity);
                }
                try {
                    progressDialog.show();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        protected void dismissProgressDialog() {
            try {
                if (progressDialog != null && progressDialog.isShowing()) {
                    if (ActivityUtils.isValid(topActivity)) {
                        if (progressDialog != null) {
                            progressDialog.dismiss();
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        protected List<List<ServerFeedBackResultBean>> sortResultBean(@Nullable List<ServerFeedBackResultBean> result) {
            List<List<ServerFeedBackResultBean>> feedBackParamsBeans = new ArrayList<>();
            if (result != null) {
                for (ServerFeedBackResultBean serverFeedBackResultBean : result) {
                    int type = serverFeedBackResultBean.type;


                    List<ServerFeedBackResultBean> groupList = getGroupList(feedBackParamsBeans, type);
                    if (groupList == null) {
                        groupList = new ArrayList<>();
                        feedBackParamsBeans.add(groupList);
                    }

                    groupList.add(serverFeedBackResultBean);

                }
            }
            return feedBackParamsBeans;
        }

        private List<ServerFeedBackResultBean> getGroupList(@NonNull List<List<ServerFeedBackResultBean>> feedBackParamsBeans, int type) {
            for (List<ServerFeedBackResultBean> feedBackParamsBean : feedBackParamsBeans) {
                ServerFeedBackResultBean firstItem = LList.getElement(feedBackParamsBean, 0);
                if (firstItem != null) {
                    if (firstItem.type == type) {
                        return feedBackParamsBean;
                    }
                }
            }
            return null;
        }
    }

    public static class GeekFeedBackStyleA extends IBaseFeedBackStyle {

        private final int pageType;
        private final ContactBean contactBean;
        private final RejectHttpManager.OnLockUnLockCallBack onLockUnLockCallBack;

        public GeekFeedBackStyleA(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack onLockUnLockCallBack) {
            this.pageType = pageType;
            this.contactBean = contactBean;
            this.onLockUnLockCallBack = onLockUnLockCallBack;
        }

        private FeedBackCommonDialog geekFeedBackStyleDialog;

        @Override
        public void loadData(String securityId) {
            SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_USER_MARK_GET_JOB_REJECT_REASON);
            get.setRequestCallback(new ApiRequestCallback<GeekFeedBackStyleResponse>() {

                @Override
                public void onStart() {
                    super.onStart();
                    showProgressDialog();
                }

                @Override
                public void onSuccess(ApiData<GeekFeedBackStyleResponse> data) {
                    GeekFeedBackStyleResponse resp = data.resp;
                    List<ServerFeedBackResultBean> result = resp.result;
                    List<List<ServerFeedBackResultBean>> sortListBean = sortResultBean(result);

                    Activity topActivity = ForegroundUtils.get().getTopActivity();
                    if (ActivityUtils.isValid(topActivity)) {
                        geekFeedBackStyleDialog = new FeedBackCommonDialog(topActivity);
                        geekFeedBackStyleDialog.showDialog(sortListBean, true, GeekFeedBackStyleA.this);
                    }
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            get.addParam("securityId", securityId);
            get.addParam("source", pageType);
            HttpExecutor.execute(get);
        }


        @Override
        public void postFeedBack(String text, int code, int type) {

            RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
            params.reasonText = text;
            params.markId = contactBean.friendId;
            params.pageType = pageType;
            params.jobId = contactBean.jobId;
            params.expectId = contactBean.jobIntentId;
            params.markReason = code;
            params.markType = 1;
            params.securityId = contactBean.securityId;
            params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;

            setUnInterestListener(contactBean, params, onLockUnLockCallBack);
        }

        //设置不感兴趣
        private void setUnInterestListener(ContactBean contactBean, RejectHttpManager.HttpParams params, RejectHttpManager.OnLockUnLockCallBack callBack) {
            RejectHttpManager.getInstance().setLock(contactBean, params, new RejectHttpManager.OnLockUnLockCallBack() {

                @Override
                public void onSuccess(ContactBean contactBean) {
                    callBack.onSuccess(contactBean);
                    ToastUtils.showText("已设置不感兴趣");

                }

                @Override
                public void onFailed(ContactBean contactBean) {
                    callBack.onFailed(contactBean);
                }
            });
        }
    }

    public static class FeedBackCommonDialog {

        private final Activity activity;

        public FeedBackCommonDialog(Activity activity) {
            this.activity = activity;
        }

        private BottomView bottomView;

        private void showDialog(@Nullable List<List<ServerFeedBackResultBean>> result, boolean hasFriendRelatationShip, IBaseFeedBackStyle iBaseFeedBackStyle) {
            if (result == null) return;
            View view = LayoutInflater.from(activity).inflate(R.layout.view_geek_feedback_style, null);
            View mCloseView = view.findViewById(R.id.mCloseView);
            MTextView mSubTitleView = view.findViewById(R.id.mSubTitleView);

            view.findViewById(R.id.mCoverView).setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    dismissBottomView();
                }
            });

            MaxHeightRecyclerView mRecycleView = view.findViewById(R.id.mRecycleView);
            mRecycleView.setLayoutManager(new LinearLayoutManager(activity));
            mRecycleView.setMaxHeight(App.get().getDisplayHeight() / 3 * 2);

            if (hasFriendRelatationShip) {
                mSubTitleView.setVisibility(View.VISIBLE);
            } else {
                mSubTitleView.setVisibility(View.GONE);
            }

            GeekFeedBackRecycleAdapter adapter = new GeekFeedBackRecycleAdapter(result);
            adapter.setiBaseFeedBackStyle(iBaseFeedBackStyle);
            mRecycleView.setAdapter(adapter);
            adapter.setOnDismissDialogListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    dismissBottomView();
                }
            });

            mCloseView.setOnClickListener(v -> {
                dismissBottomView();
            });

            bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
            bottomView.setAnimation(R.style.BottomToTopAnim);
            try {
                bottomView.showBottomView7(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private void dismissBottomView() {
            if (ActivityUtils.isValid(activity)) {
                if (bottomView != null) {
                    bottomView.dismissBottomView();
                }
            }
        }


        private static class GeekFeedBackRecycleAdapter extends BaseRvAdapter<List<ServerFeedBackResultBean>, BaseViewHolder> {


            private IBaseFeedBackStyle iBaseFeedBackStyle;

            public void setiBaseFeedBackStyle(IBaseFeedBackStyle iBaseFeedBackStyle) {
                this.iBaseFeedBackStyle = iBaseFeedBackStyle;
            }

            private View.OnClickListener onDismissDialogListener;

            public void setOnDismissDialogListener(View.OnClickListener onDismissDialogListener) {
                this.onDismissDialogListener = onDismissDialogListener;
            }

            public GeekFeedBackRecycleAdapter(@Nullable List<List<ServerFeedBackResultBean>> data) {
                super(R.layout.view_geek_feedback_item, data);
            }

            @Override
            protected void convert(@NonNull BaseViewHolder helper, List<ServerFeedBackResultBean> item) {

                View mEditView = helper.getView(R.id.mEditView);
                mEditView.setVisibility(View.GONE);

                FlexboxLayout mFlexboxLayout = helper.getView(R.id.mFlexboxLayout);
                mFlexboxLayout.removeAllViews();


                mFlexboxLayout.post(() -> {

                    int measuredWidth = mFlexboxLayout.getMeasuredWidth();

                    ServerFeedBackResultBean itemBean = LList.getElement(item, 0);
                    if (itemBean != null) {
                        String title = itemBean.title;
                        MTextView mItemTitle = helper.getView(R.id.mItemTitle);
                        mItemTitle.setText(title);

                        if (itemBean.type == 3) {
                            mEditView.setVisibility(View.VISIBLE);
                            mEditView.setOnClickListener(new OnClickNoFastListener() {
                                @Override
                                public void onNoFastClick(View v) {
                                    if (onDismissDialogListener != null) {
                                        onDismissDialogListener.onClick(v);
                                    }
                                    showOtherInputDialog(itemBean.code);
                                }
                            });
                        } else {

                            int margin = ZPUIDisplayHelper.dp2px(mContext, 15);
                            for (int i = 0; i < item.size(); i++) {
                                ServerFeedBackResultBean serverFeedBackResultBean = LList.getElement(item, i);
                                if (serverFeedBackResultBean == null) continue;


                                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                                params.width = (measuredWidth - margin) / 2;
                                params.bottomMargin = margin;

                                if (i % 2 == 0) {
                                    params.rightMargin = margin;
                                }


                                ServerFeedBackTextBean text = serverFeedBackResultBean.text;
                                if (text == null) continue;
                                String content = text.content;
                                int code = serverFeedBackResultBean.code;
                                mFlexboxLayout.addView(createTagView(content, code, serverFeedBackResultBean.type), params);
                            }

                        }


                    }
                });


            }

            private void showOtherInputDialog(int code) {

                DialogUtils d = new DialogUtils.InputBuilder((Activity) mContext)
                        .setTitle("输入原因")
                        .setMaxInput(50)
                        .setInputText("")
                        .setInputHint("请输入")
                        .setNegativeAction("取消")
                        .setPositiveAction("确定", input -> {

                            postFeedBackListener(input, code, 0);

                        })
                        .build();
                d.showInput();

            }

            private void postFeedBackListener(String input, int code, int type) {

                if (iBaseFeedBackStyle != null) {
                    iBaseFeedBackStyle.postFeedBack(input, code, type);
                }

            }


            private View createTagView(@NonNull String text, int code, int type) {
                return createTagView(text, code, com.twl.ui.R.drawable.bg_position_card_4_corner, ContextCompat.getColor(mContext, com.twl.ui.R.color.text_c6_light), type);
            }

            private View createTagView(@NonNull String text, int code, @DrawableRes int bgResource,
                                       @ColorInt int textColor, int type) {
                int paddingHorizontal = ZPUIDisplayHelper.dp2px(mContext, 15);
                int paddingVertical = ZPUIDisplayHelper.dp2px(mContext, 8);
                MTextView textView = new MTextView(mContext);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE);
                textView.setGravity(Gravity.CENTER);
                textView.setBackgroundResource(bgResource);
                textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
                textView.setTextColor(textColor);
                textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
                textView.setText(text);
                textView.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        textView.setBackgroundResource(R.drawable.bg_feedback_press);
                        textView.setTextColor(ContextCompat.getColor(mContext, com.twl.ui.R.color.app_green_dark));


                        App.get().getMainHandler().postDelayed(() -> {
                            if (onDismissDialogListener != null) {
                                onDismissDialogListener.onClick(null);
                            }
                        }, 200);


                        postFeedBackListener(text, code, type);
                    }
                });
                return textView;
            }
        }

    }

    public interface IRemoveCallBack {
        void onRemoveListener(F1FeedBackCardBean bean);
    }

    public static class GeekFeedBackStyleB extends IBaseFeedBackStyle {


        public boolean hasChat(long friendId, int friendSource) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), friendSource);
            return contactBean != null;
        }

        private GeekFeedBackStyleBParams geekFeedBackStyleBParams;

        public void setGeekFeedBackStyleBParams(GeekFeedBackStyleBParams geekFeedBackStyleBParams) {
            this.geekFeedBackStyleBParams = geekFeedBackStyleBParams;
        }


        private FeedBackCommonDialog geekFeedBackStyleDialog;

        @Override
        public void loadData(String securityId) {

            SimpleApiRequest get = SimpleApiRequest.GET(GeekUrlConfig.URL_APP_NAGATIVE_FEED_BACK_REASON_V2);
            get.setRequestCallback(new ApiRequestCallback<GeekFeedBackStyleResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    showProgressDialog();
                }

                @Override
                public void onSuccess(ApiData<GeekFeedBackStyleResponse> data) {
                    GeekFeedBackStyleResponse resp = data.resp;
                    List<ServerFeedBackResultBean> result = resp.result;
                    List<List<ServerFeedBackResultBean>> sortListBean = sortResultBean(result);

                    Activity topActivity = ForegroundUtils.get().getTopActivity();
                    if (ActivityUtils.isValid(topActivity)) {
                        geekFeedBackStyleDialog = new FeedBackCommonDialog(topActivity);
                        geekFeedBackStyleDialog.showDialog(sortListBean, geekFeedBackStyleBParams.hasChat, GeekFeedBackStyleB.this);
                    }
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            get.addParam("scene", geekFeedBackStyleBParams.scene);
            get.addParam("jobType", geekFeedBackStyleBParams.jobType);
            get.addParam("securityId", securityId);
            get.addParam("city", geekFeedBackStyleBParams.city);
            get.addParam("lid", geekFeedBackStyleBParams.lid);
            get.addParam("query", geekFeedBackStyleBParams.query);
            get.addParam("cardType", geekFeedBackStyleBParams.cardType);
            get.addParam("adId", geekFeedBackStyleBParams.adId);
            HttpExecutor.execute(get);

        }

        @Override
        public void postFeedBack(String text, int code, int type) {
            PostImproperReasonRequest postImproperReasonRequest = new PostImproperReasonRequest(new ApiRequestCallback<PostImproperReasonResponse>() {

                @Override
                public void onStart() {
                    super.onStart();
                    showProgressDialog();
                }

                @Override
                public void onSuccess(ApiData<PostImproperReasonResponse> data) {
                    if (geekFeedBackStyleBParams != null) {

                        if (geekFeedBackStyleBParams.scene == GeekCommonImproperReasonDialog.SCENE_F1 || geekFeedBackStyleBParams.from == ParamBean.FROM_F1_LIST) {

                            requestFeedBackCard(code, new SimpleApiRequestCallback<F1FeedBackCardResponse>() {
                                @Override
                                public void onSuccess(ApiData<F1FeedBackCardResponse> feedData) {
                                    super.onSuccess(feedData);
                                    if (feedData.resp.banner != null && feedData.resp.banner.bannerType == 2) {
                                        feedData.resp.banner.itemType = GeekBaseCardBean.TYPE_1107_SALARY_FEED_BACK;
                                    }
                                    IRemoveCallBack iRemoveCallBack = geekFeedBackStyleBParams.iRemoveCallBack;
                                    if (iRemoveCallBack != null) {
                                        iRemoveCallBack.onRemoveListener(feedData.resp.banner);
                                    }

                                }

                                @Override
                                public void onFailed(ErrorReason reason) {
                                    super.onFailed(reason);
                                    IRemoveCallBack iRemoveCallBack = geekFeedBackStyleBParams.iRemoveCallBack;
                                    if (iRemoveCallBack != null) {
                                        iRemoveCallBack.onRemoveListener(null);
                                    }
                                }

                                @Override
                                public void onComplete() {
                                    if (data != null && data.resp != null && !TextUtils.isEmpty(data.resp.msg)) {
                                        ToastUtils.showText(data.resp.msg);
                                    } else {
                                        ToastUtils.showText("已反馈");
                                    }
                                    if (type == 6 && geekFeedBackStyleBParams.mListRefreshListener != null) {
                                        geekFeedBackStyleBParams.mListRefreshListener.refresh();
                                    }
                                }
                            });

                        } else {
                            if (geekFeedBackStyleBParams.iRemoveCallBack != null) {
                                geekFeedBackStyleBParams.iRemoveCallBack.onRemoveListener(null);
                            }

                            if (data != null && data.resp != null && !TextUtils.isEmpty(data.resp.msg)) {
                                ToastUtils.showText(data.resp.msg);
                            } else {
                                ToastUtils.showText("已反馈");
                            }
                            if (type == 6 && geekFeedBackStyleBParams.mListRefreshListener != null) {
                                geekFeedBackStyleBParams.mListRefreshListener.refresh();
                            }
                        }
                    }


                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });

            postImproperReasonRequest.code = String.valueOf(code);
            //职位securityId
            postImproperReasonRequest.securityId = geekFeedBackStyleBParams.securityId;
            //职位lid
            postImproperReasonRequest.lid = geekFeedBackStyleBParams.lid;
            //F1职位卡片推荐理由
            postImproperReasonRequest.rcdReason = geekFeedBackStyleBParams.rcdReason;
            //F1排序
            postImproperReasonRequest.sortType = geekFeedBackStyleBParams.sortType;
            //学生tab,  0:全职，1:兼职，2:实习，3:推荐
            postImproperReasonRequest.jobType = geekFeedBackStyleBParams.jobType;
            //F1场景下传加密期望id参数，职场人混推不用传
            postImproperReasonRequest.expectId = geekFeedBackStyleBParams.expectId;
            //用户输入反馈理由
            postImproperReasonRequest.feedbackReason = text;
            //新增请求参数mixExpectType, 混合推荐参数-废弃请求参数 mixExpectType
            postImproperReasonRequest.mixExpectType = geekFeedBackStyleBParams.mixExpectType;
            //职位列表场景，0:F1, 1:新职位, 2:搜索职位,3:收藏职位_新职位, 4:职位详情
            postImproperReasonRequest.scene = geekFeedBackStyleBParams.scene;
            postImproperReasonRequest.adId =geekFeedBackStyleBParams.adId;
            postImproperReasonRequest.cardType =geekFeedBackStyleBParams.cardType;
            if (geekFeedBackStyleBParams.outFeedbackBean!=null){
                postImproperReasonRequest.entry = geekFeedBackStyleBParams.outFeedbackBean.getCode();
                postImproperReasonRequest.strategyId = geekFeedBackStyleBParams.outFeedbackBean.strategyId;
            }
            HttpExecutor.execute(postImproperReasonRequest);

        }

        /**
         * 1105 601  负反馈后 可能有卡片返回  需要插入。 新增此接口
         *
         * @param code
         */
        private void requestFeedBackCard(int code, SimpleApiRequestCallback<F1FeedBackCardResponse> callback) {
            SimpleApiRequest.GET(GeekUrlConfig.URL_GET_F1_FEED_BACK_CARD)
                    .addParam("code", code)
                    .addParam("securityId", geekFeedBackStyleBParams.securityId)
                    .addParam("expectId", geekFeedBackStyleBParams.expectId)
                    .addParam("jobType", geekFeedBackStyleBParams.jobType) //F1列表类型 学生身份下：0-全职，1-兼职，2-实习，3-学生混合推荐列表
                    .addParam("sortType", geekFeedBackStyleBParams.sortType)//F1排序
                    .addParam("filterParams", geekFeedBackStyleBParams.filterParams)
                    .addParam("lid", geekFeedBackStyleBParams.lid)
                    .setRequestCallback(callback)
                    .execute();
        }
    }

    public static class GeekFeedBackStyleBParams extends BaseServerBean {

        private static final long serialVersionUID = 7614146712923435196L;

        //city的值
        public long city;

        // 1125.609
        // 搜索词
        public String query;


        //新增请求参数mixExpectType, 混合推荐参数
        public int mixExpectType;
        //职位列表场景，0:F1, 1:新职位, 2:搜索职位,3:收藏职位_新职位, 4:职位详情
        public int scene;
        //F1排序
        public int sortType;
        //学生tab,  0:全职，1:兼职，2:实习，3:推荐
        public long jobType;
        //职位lid
        public String lid;
        //F1职位卡片推荐理由
        public String rcdReason;
        //F1场景下传加密期望id参数，职场人混推不用传
        public String expectId;
        //职位securityId
        public String securityId;

        public boolean hasChat;
        public int from; //jd 使用，标志是从F1 还是其他地方进入

        public int cardType; //新加卡片类型
        public String adId; //新加卡片id


        //数据提交后回掉,各自业务层用来发送广播
        public IRemoveCallBack iRemoveCallBack;
        public String filterParams;
        public ListRefreshListener mListRefreshListener;

        public ServerF1CardFeedBackBean outFeedbackBean;

    }

    public interface ListRefreshListener {
        void refresh();
    }
}