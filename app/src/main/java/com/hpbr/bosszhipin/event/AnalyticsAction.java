package com.hpbr.bosszhipin.event;


public interface AnalyticsAction {

    /**
     * 【发现-推荐页】引导访问内容推荐卡片（引导条下的第一个卡片）】点击
     */
    String ACTION_EXTENSION_GET_EXPLOREGUIDE_CARDCLICK = "extension-get-exploreguide-cardclick";
    /**
     * 【发现-推荐页】引导访问内容推荐卡片（引导条下的第一个卡片）】曝光
     */
    String ACTION_EXTENSION_GET_EXPLOREGUIDE_CARDEXPOSE = "extension-get-exploreguide-cardexpose";
    /**
     * 用户点击F4tab 816版本发现tab全量
     */
    String ACTION_GEEK_F4_TAB = "f4-tab";

    String ACTION_BIZ_ITEM_GEEKVIP_RESUMETEMPLATE = "biz-item-geekvip-resumetemplate";
    String ACTION_USERINFO_MICRORESUME_EXPECT_SWITCHTYPE = "userinfo-microresume-expect-switchtype";

    String ACTION_DETAIL_SCREENSHOT_CLICK = "action-detail-screenshot-report-click";
    String ACTION_DETAIL_SCREENSHOT_EXPO = "action-detail-screenshot-report-expo";

    String ACTION_JOB_LIST_F1_ORDER_SHOW = "Boss-job-list-f1-order-show";

    /**
     * 8.17 【发现页】-搜索推荐词-确认搜索（只上报后台配置搜索词）
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=21714
     */
    String ACTION_EXTENSION_GET_RECSEARCH_CONFIRM = "extension-get-recsearch-confirm";

    String ACTION_USERINFO_MICRORESUME_WORK_CLICKJOBTITLE = "userinfo-microresume-work-clickjobtitle";
    String ACTION_USERINFO_MICRORESUME_WORK_ADDJOBTITLE = "userinfo-microresume-work-addjobtitle";
    String ACTION_USERINFO_JOB_EDIT_QUICKJDCHANGE = "userinfo-job-edit-quickJDchange";

    String ACTION_ACTION_DETAIL_EXPECT_EXTRADDSHOW = "action-detail-expect-extraddshow";
    String ACTION_EXTENSION_GET_VIDEO_DURATION = "extension-get-video-duration";
    String ACTION_LIFECYCLE_RESUME_VIDEO_EXAMPLEVIEW = "lifecycle-resume-video-exampleview";
    String ACTION_USERINFO_MICRORESUME_WORK_SKILLINTO = "userinfo-microresume-work-skillinto";
    String ACTION_USERINFO_MICRORESUME_WORK_SKILLOVERLIMIT = "userinfo-microresume-work-skilloverlimit";
    String ACTION_USERINFO_MICRORESUME_WORK_SKILLPRESET = "userinfo-microresume-work-skillpreset";

    String ACTION_LIFECYCLE_RESUME_RENAME_CLICK = "lifecycle-resume-rename-click";
    String ACTION_LIFECYCLE_RESUME_UPLOAD_CLICK = "lifecycle-resume-upload-click";
    String ACTION_LIFECYCLE_RESUME_UPLOAD_SHOW = "lifecycle-resume-upload-show";
    String ACTION_LIFECYCLE_RESUME_UPLOAD_VIDEO = "lifecycle-resume-upload-video";
    String ACTION_LIFECYCLE_RESUME_VIDEO_LENGTHLIMIT = "lifecycle-resume-video-lengthlimit";

    String ACTION_LIFECYCLE_RESUME_VIDEO_UPBTS = "lifecycle-resume-video-upbts";
    String ACTION_LIFECYCLE_RESUME_VIDEO_UPSHOW = "lifecycle-resume-video-upshow";

    String ACTION_LIFECYCLE_RESUME_VIDEO_COVERCLICK = "lifecycle-resume-video-coverclick";
    String ACTION_LIFECYCLE_RESUME_VIDEO_COVERDONE = "lifecycle-resume-video-coverdone";
    String ACTION_LIFECYCLE_RESUME_VIDEO_DRAFTEDIT = "lifecycle-resume-video-draftedit";
    String ACTION_LIFECYCLE_RESUME_VIDEO_EXAMPLE = "lifecycle-resume-video-example";
    String ACTION_LIFECYCLE_RESUME_VIDEO_FUNCTION = "lifecycle-resume-video-function";
    String ACTION_LIFECYCLE_RESUME_VIDEO_NEXT = "lifecycle-resume-video-next";
    String ACTION_LIFECYCLE_RESUME_VIDEO_QUESTION = "lifecycle-resume-video-question";
    String ACTION_LIFECYCLE_RESUME_VIDEO_RECORD = "lifecycle-resume-video-record";
    String ACTION_LIFECYCLE_RESUME_VIDEO_SECONDSUGGEST = "lifecycle-resume-video-secondsuggest";
    String ACTION_LIFECYCLE_RESUME_VIDEO_SECSUGCLICK = "lifecycle-resume-video-secsugclick";
    String ACTION_LIFECYCLE_RESUME_VIDEO_TOAST = "lifecycle-resume-video-toast";
    String ACTION_LIFECYCLE_RESUME_VIDEO_UPLOADCLICK = "lifecycle-resume-video-uploadclick";
    String ACTION_LIFECYCLE_RESUME_VIDEOHI_FUNCTION = "lifecycle-resume-videohi-function";

    String ACTION_LIFECYCLE_RESUME_VIDEOHI_NOTESEXAMPLE = "lifecycle-resume-videohi-notesexample";
    String ACTION_LIFECYCLE_RESUME_VIDEOHI_NOTESDONE = "lifecycle-resume-videohi-notesdone";

    String ACTION_USERINFO_MICRORESUME_CONTENT_EXCHANGE = "userinfo-microresume-content-exchange";
    String ACTION_USERINFO_MICRORESUME_CONTENT_CLICK = "userinfo-microresume-content-click";

    String ACTION_ACTION_SEARCH_EXPECT_CITY = "action-search-expect-city";
    String ACTION_SYSTEM_FEEDBACK_UPLOAD_VIDEO = "system-feedback-upload-video";

    String ACTION_USERINFO_POSITION_SALARYGUIDE_TIPSCLICK = "userinfo-position-salaryguide-tipsclick";
    String ACTION_USERINFO_POSITION_SALARYGUIDE_TIPSHOW = "userinfo-position-salaryguide-tipshow";
    String ACTION_USERINFO_POSITION_SALARYMODULE_CLICK = "userinfo-position-salarymodule-click";
    String ACTION_USERINFO_POSITION_SALARYMODULE_SHOW = "userinfo-position-salarymodule-show";
    String ACTION_USERINFO_POSITION_SALARYPOP_CLICK = "userinfo-position-salarypop-click";

    String ACTION_ACTION_LIST_F_1_ACCSHOW = "action-list-f1-accshow"; // 牛人进到F1 促开聊卡片弹窗 展示
    String ACTION_ACTION_LIST_F_1_HELLOGIFTSHOW = "action-list-f1-hellogiftshow"; // 新增职位初到F1列表送礼物弹窗展示（808蓝精灵）
    String ACTION_ACTION_LIST_F_1_ACCCLICK = "action-list-f1-accclick";

    String ACTION_LIFECYCLE_RESUME_VIDEO_UPLOAD = "lifecycle-resume-video-upload";
    String ACTION_LIFECYCLE_RESUME_VIDEO_SETUP = "lifecycle-resume-video-setup";
    String ACTION_LIFECYCLE_RESUME_VIDEO_UNPASSEXPO = "lifecycle-resume-video-unpassexpo";

    String ACTION_DATAIL_BOSS_ASK_ANSWER = "datail-boss-ask-answer"; // 牛人点击bossJD职位问答模块
    String ACTION_EXPECT_SUGGEST_CLICK = "expect-suggest-click"; // 引导期望卡片-点击添加（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=60245614）

    // F2页面「新职位-猜你想看」“发现您在店长想看xx工作”的弹窗-点击「马上添加」
    String ACTION_ACTION_LIST_F_2_NEW_GUESS_ADD = "action-list-f2NewGuess-add";
    // F2页面「新职位-猜你想看」“发现您在店长想看xx工作”的弹窗-点击「X」
    String ACTION_ACTION_LIST_F_2_NEW_GUESS_CANCEL = "action-list-f2NewGuess-cancel";
    // F2页面「新职位-猜你想看」“发现您在店长想看xx工作”的弹窗
    String ACTION_ACTION_LIST_F_2_NEW_GUESS_POP = "action-list-f2NewGuess-pop";

    String ACTION_F_3_PERSONAL_INFO_SET = "f3-personal-info-set";

    String ACTION_JOB_KEYWORD_SAVE = "job-keyword-save";

    String ACTION_WELFARE_UNFINISH_PUSH_CLICK = "welfare-unfinish-push-click";
    String ACTION_F_3_INFLUENCE_CLICK = "f3-influence-click";

    //region 7.15 Get
    String ACTION_GET_MY_CLICK = "get-my-click";
    @Deprecated //801下线
    String ACTION_GET_TOPIC_CLICK = "get-topic-click";
    String ACTION_GET_MY_MORE_CLICK = "get-my-more-click";
    String ACTION_GET_OPERATION_CLICK = "get-operation-click";
    String ACTION_RENEW_FILTER_CONFIRM = "renew-filter-confirm";
    //endregion

    //region 7.14 Get
    String ACTION_GET_PUBLISH_CLICK = "get-publish-click"; // 点击进入发布页面     https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=50596483
    String ACTION_GET_NOTICES_SHOW = "get-notices-show"; // 通知卡片曝光      https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=50596483
    //endregion

    //region 7.16 Get

    //region 7.18 Get
    String ACTION_GET_TOP_EXPOSURE = "get-top-exposure"; // Top卡片曝光
    String ACTION_GET_EXPLORE_ICON_EXPOSE = "get-explore-icon-expose"; // 发现页面-【icon】曝光
    String ACTION_GET_EXPLORE_NOTICE = "get-explore-notice"; // 发现页面-点击消息通知
    String ACTION_GET_PAGE_DURATION = "get-page-duration"; // 页面停留时长（统计进入页面到离开页面的停留时长）
    String ACTION_GET_QUESITION_DURATION = "get-question-duration"; // 记录问题详情页的停留时长，区分最新/最热tab
    String ACTION_GET_PAGE_HOME = "get-page-home"; // 搭建页面-显示（进入搭建页面即上报埋点）
    String ACTION_GET_PAGE_LIST = "get-page-list"; // 搭建页面曝光
    String ACTION_GET_ASSOCIATE_JOB_SHOW = "get-associate-job-show"; // 关联职位弹窗-显示
    String ACTION_GET_ASSOCIATE_JOB_CONFIRM = "get-associate-job-confrim"; // 关联职位弹窗-点击确认关联
    String ACTION_GET_ASSOCIATE_JOB_EXPOSE = "get-associate-job-expose"; // 关联职位曝光
    String ACTION_GET_BANNER_GROUP_SHOW = "get-banner-group-show"; // 多图片运营位曝光
    String ACTION_GET_BANNER_GROUP_CLICK = "get-banner-group-click"; // 多图片运营位点击
    String ACTION_GET_FEED_QUESTION = "get-feed-question"; // question列表卡片曝光
    String ACTION_GET_FEED_LIST = "get-feed-list"; // get-项目内容页刷列表
    String ACTION_GET_BANNER_SHOW = "get-banner-show"; // get feed banner图片运营位曝光
    String ACTION_ASSOCIATE_JOB_CLICK = "get-associate-job-click"; // 关联职位点击


    //802 发现
    String ACTION_GET_TOPICK_CLICK = "get-action-topic-click"; // 点击卡片上的话题标签，跳转到话题详情页
    String ACTION_GET_QUESTION_CLICK = "get-action-question-click"; // 点击卡片上的问题标签，进入问题详情
    //805 发现
    String ACTION_GET_SHARE_CLICK = "extension-get-share-click"; // 社区内容分享-点击分享按钮
    String ACTION_GET_SHARE_WAY = "extension-get-share-way"; // 社区内容分享-选择分享渠道
    String ACTION_GET_HEAD_CLICK = "extension-get-head-click"; //点击个人头像

    //region Get
    String ACTION_GET_CONTENT_PLAY = "get-content-play"; // 播放或者暂停播放音频/视频
    String ACTION_GET_FEED_RETURN = "get-feed-return"; // 从get列表返回或者关闭get列表
    String ACTION_GET_NOTICE = "get-notice";
    String ACTION_GET_FEEDBACK_SHOW = "get-feedback-show";
    String ACTION_GET_NOTICE_CHANGE = "get-notice-change";
    String ACTION_GET_CHOOSE_DONE = "get-choose-done";

    String ACTION_GET_INVITR_POST = "get-invite-answer";
    String ACTION_GET_WAIT4YOU_CLICK = "get-invite-answer-click";
    String ACTION_GET_EXCELLENT_CLICK = "get-excellent-click";//优质内容点击
    String ACTION_GET_HOT_TOPIC_EXPOSE = "get-hot-topic-expose";//热门话题曝光
    String ACTION_GET_SEARCH_CONTENT_KNOW_EXPOSE = "get-search-result-content-expose";//搜索结果页-知识点曝光
    String ACTION_GET_SEARCH_TOPIC_EXPOSE = "get-search-result-topic-expose";//搜索结果页-话题曝光

    String ACTION_GET_DYNAMIC_CLICK = "get-dynamic-feed-click";//公司圈/个人主页 动态点击

    String ACTION_GET_INDUSTRY_HP_LIST_EXPOSE = "get-industryhomepage-joblist-show";//行业主页  TAB列表曝光
    String ACTION_GET_HOMEPAGE_TAB_LIST_EXPOSE = "extension-get-myhome-tabexpose";//个人主页 TAB列表曝光
    String ACTION_HOMEPAGE_PERSONAL_HOBBY_EXPOSE = "get-feed-interest";//个人主页 爱好
    String ACTION_GET_HOMEPAGE_MODULE_CLICK = "extension-get-myhome-click";//个人主页 模块点击

    String ACTION_PERSONAL_HOMEPAGE_CLICK = "personal-homepage-click";//个人主页 模块点击

    String ACTION_GET_BE_INCLUDE_LIST_EXPOSE = "get-myhome-collection-page-expose";//被收录页 曝光

    String ACTION_GET_DYNAMIC_JOB_CLICK = "get-dynamic-job";//公司圈 动态关联的职位 卡片点击

    String ACTION_BRAND_JOBBANNER_CLICK = "brand-jobbanner-click";

    String ACTION_BRAND_GALLARYPHOTO_SHOW = "brand-gallaryphoto-show";
    // 人脉直联列表-卡片曝光
    String ACTION_EXTENSION_GET_DIRECT_CARDEXPOSE = "extension-get-direct-cardexpose";
    // 发现页-发布按钮点击
    String ACTION_EXTENSION_GET_EXPLORE_PUBLISH = "extension-get-explore-publish";

    String ACTION_JOB_LIST_MANAGE_PLUS = "job-list-manage-plus";
    String ACTION_JOB_LIST_MANAGE_PLUS_EXPO = "job-list-manage-plus-expo";

    String ACTION_JOB_LIST_DETAIL = "job-list-detail";

    /**
     * 支付渠道点击 920.204【商业非版本】支付中台化
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
     */
    String ACTION_BIZ_PAY_PAGE_CLICK = "biz-pay-page-click";

    String ACTION_GEEK_MASK_COMPANY = "geek-mask-company";
    String ACTION_LIST_MASK = "list-mask";
    String ACTION_ITEM_MINE = "item-mine";

    // 首次完善工作经历时选择职位类型-推荐店长工作经历点击
    String ACTION_LIFECYCLE_COMPLETE_RECO_DZ_WORK_CLICK = "lifecycle-complete-RecoDzWork-click";

    String ACTION_EXPECT_POSITION_MANAGER = "exp-list-manage";
    String ACTION_EXPECT_POSITION_ITEM = "exp-list-detail";
    String ACTION_NLP_SHOW = "nlp-remind-active-show";
    String ACTION_NLP_CLICK = "chat-nlp-remind-click";
    String ACTION_QUICK_AWSWER_APPEND = "quick-answer-append";

    String ACTION_SUPER_SEARCH = "super-search";
    String ACTION_F_1_AD_SEARCH = "f1-ad-search"; // boss在F1点击高搜引导卡片

    String ACTION_CLICK_RECOMMEND_KEYWORD = "click-recommend-keyword";//高搜入口新增推荐搜索模块 点击 https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2623
    String ACTION_CLICK_CHANGE_KEYWORD = "biz-item-search-RecommenderChange";// 点击换一换
    String ACTION_NEW_WORD_SHOW = "biz-item-search-NewWordsExpose";// 新词曝光


    String ACTION_SEARCH_SCREEN = "search-screen"; // 记录各个筛选项（职位、学历、工作经验、更多）的点击 https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=1075

    String ACTION_PUSH_CLOSED = "push-closed"; // 用户关闭通知（首次启动时授权设置）

    String ACTION_RESEND_CHAT_CLICK = "resend-chat-click";

    String ACTION_F1_TAB_CLICK = "f1-tab";

    String ACTION_F2_INTEREST_REMIND = "f2-interest-remind";

    String ACTION_REPEAT_REMOVE = "f1-repeat-remove"; // F1列表去除重复

    String ACTION_F3_TAB_CLICK = "f3-tab";

    String ACTION_CHAT_FRAMEWORK_DIALOG_POP_UP = "addf-limit-popup-c"; // 【开聊框架】开聊提醒弹窗弹出

    String ACTION_ADDFRIEND_LIMIT_CLOSE = "action-addfriend-limit-close";

    String ACTION_CHAT_FRAMEWORK_DIALOG_CHAT = "addf-limit-popup-connect"; // 【开聊框架】开聊提醒弹窗-点击继续开聊

    String ACTION_RESUME_UPLOAD_APP = "resume-upload-app";

    String ACTION_RESUME_UPLOAD_WEB = "resume-upload-web";

    String ACTION_RESUME_UPLOAD_EMAIL = "resume-upload-email";

    String ACTION_RESUME_UPLOAD_EMAIL_NEXT = "resume-upload-email-next";

    String ACTION_RESUME_UPLOAD_EMAIL_REPLY = "resume-upload-email-reply";

    String ACTION_SHOP_ITEM_CLICK = "tools-preview"; // 道具商城item的点击事件

    String ACTION_GEEK_F2_TAB = "gf2-tab";

    String ACTION_SYSTEM_CHAT_WEIXIN_OPEN = "system-newguide-chat-weixinopen";

    String ACTION_GEEK_F2_DETAIL_DIALOG = "gf2-detail-dialog";

    String ACTION_NO_DISTURB_NEW_POUP = "no-disturb-news-popup";

    String ACTION_NLP_CHAT_CLICK = "nlp-chat-click";


    String ACTION_SAVE_AVATAR = "detail-headimg-save"; // 保存头像

    String ACTION_F2_SEARCH_CLICK = "f2-search-click";//f2消息页面点击搜索

    String ACTION_F2_SEARCH_CLICK_DETAIL = "f2-search-click-detail";//F2消息页面点击搜索框输入内容后点击一个联系人

    String ACTION_F2_SEARCH_PULL = "f2-search-pull";//F2消息页面出现搜索框搜索框

    //901猜你想问已经下架，埋点自动也下架
    String ACTION_GUESS_LIKE = "guess-like";
    String ACTION_RESUME_SEND_LEAD = "resume-send-lead";
    String ACTION_TIP_CLICK = "change-tips-click";
    String ACTION_MESSAGE_SHOW = "change-message-show";

    String ACTION_BRAND_SEARCH = "brand-search"; // 牛人在"公司"点击搜索按钮（点击事件）

    String ACTION_BRAND_INFO = "detail-brand-info"; // 牛人在公司主页点击查看高管详情/产品介绍/全部地址/公司介绍/工商信息/（6.02）

    String ACTION_BRAND_RELATED_FEEDBACK = "brand-related-feedback"; // 牛人在公司主页，相关内容中点击反馈（6.02）

    String ACTION_BRAND_SLIDE_INFO = "brand-slide-info"; // 牛人在公司主页滑动查看图片（6.02）

    String ACTION_F2_SWITCH = "f2-switch";

    String ACTION_DETAIL_BRAND_SWITCH = "detail-brand-switch"; // 牛人在公司主页切换热招职位和公司概况

    String ACTION_DETAIL_DESCRIPTION_EXPAND = "detail-boss-all"; // 展开职位描述

    String ACTION_DYNAMIC_AD_CLICK = "detail-dynamic-ad"; // 点击F1列表中的广告

    String ACTION_PROTLCOL_RETURN = "protocol-return";

    String ACTION_CHAT_PLUS = "chat-plus"; // 聊天加号

    String ACTION_CHAT_PIC_SEND_CLICK = "chat-pic-send-click"; // 聊天+号中 点击图片

    String ACTION_CHAT_NOTIFY_CLICK = "chat-notify-click"; // 聊天+号中 B/C端 点击优先提醒

    String ACTION_VIDEO_VOICE_CLICK = "chat-videovoice-click"; // 聊天+号中 B端点语音/视频通话

    String ACTION_SMS_NOTIFY_CLICK = "boss-sms-notify-click"; // 聊天+号中 B端点击短信通知


    String ACTION_CHAT_JOBCHANGE_CLICK = "chat-jobchange_click"; // 聊天+号中 B端点击更换职位
    String ACTION_CHAT_BACKGROUND_CLICK = "chat-background-click"; // 聊天+号中 B端点击背景调查
    String ACTION_CHAT_ASSESSMENT_CLICK = "chat-assessment-click"; // 聊天+号中 B端点击人才测评

    String ACTION_SAVE_EDU_SCHOOL = "edu-save-school"; // 增加教育经历填写学校时，保存
    String ACTION_SLIDE_PICTURE = "silde-picture"; // 聊天页面图片向前滑动到第一张划不动了或者向后滑动到最后一张划不动了（多次向前或向后划划不动时只打一次）

    String ACTION_START_SEARCH_GEEK = "start-search-geek"; //点击开始招募牛人

    String ACTION_ADD_JOB_CLICK = "addjob-click"; //职位发布成功页面点击

    String ACTION_ADD_JOB_MORE_CLICK = "addjob-more-click"; //职位发布成功页面点击


    String ACTION_GROUP_GROUND = "group-ground";
    String ACTION_GROUP_PRIVACY_GROUND = "group-privacy-popup";
    String ACTION_GROUP_CARD = "group-card";
    String ACTION_GROUP_ADD = "group-add";
    String ACTION_GROUP_SETTING = "group-set";
    String ACTION_GROUP_USER_CARD = "group-user-card";
    String ACTION_GROUP_MODIFY_CARD = "group-modify-card";
    String ACTION_GROUP_PLUS = "group-ground-plus";
    String ACTION_GROUP_CREATE = "group-ground-create";
    String ACTION_GROUP_INVITE = "group-invite";

    String ACTION_FRESH_CHANGE_WORKTIME = "fresh-change-worktime-client"; // 引导”应届生“中的非应届生更改工作时间客户端弹窗
    String ACTION_FRESH_CHANGE_WORKTIME_CONFIRM = "fresh-change-worktime-sure"; // 引导”应届生“中的非应届生更改工作时间修改工作时间(点击确定)

    String ACTION_PREVIEW_RESUME_OTHER_APP = "preview-resume-other-app"; // 附件简历用其他应用打开

    String ACTION_CHAT_QUICK_REPLAY = "push-chat-quick-reply";
    String ACTION_LIFECYCLE_RESUME_SEND_UPLOAD = "lifecycle-resume-send-upload";
    String ACTION_LIFECYCLE_RESUME_SEND_SHOW = "lifecycle-resume-send-show";
    String ACTION_QUCIK_REPLAY_CLIENT = "quick-reply-client";
    String ACTION_PRESS_CHAT_MSG = "press-chat-msg";
    String ACTION_PRESS_CHAT_MSG_COPY = "press-chat-msg-copy";
    String ACTION_ADD_SELF_GREETING = "add-self-greeting";
    String ACTION_MANAGE_RESUME_RENAME = "manage-resume-rename";
    String ACTION_MANAGE_RESUME_EMAIL = "manage-resume-email";
    String ACTION_CHOOSE_RESUME_SEND = "choose-resume-send";
    String ACTION_CHOOSE_RESUME_CANCEL = "choose-resume-cancel";
    String ACTION_LIST_IMPROPER_GEEK = "list-improper-geek";
    String ACTION_DETAIL_IMPROPER_GEEK = "detail-improper-geek";
    String ACTION_IMPROPER_SETTING = "improper-setting";
    String ACTION_UPDATE_REPLAY_CONTENT = "improper-update-reply-content";
    String ACTION_UPDATE_REPLAY_TYPE = "improper-update-reply-type";
    String ACTION_CHAT_CHANGE = "tips-chat-change";
    String ACTION_EXP_ADD_CLICK = "exp-add-click";
    String ACTION_RESUME_SEND_CLICK = "resume-send-click";
    String ACTION_CHANGE_CANCEL_CLICK = "change-cancel-click";
    String ACTION_MESSAGE_ACCEPT = "change-message-accept";
    String ACTION_TIPS_DONE = "change-tips-done";
    String ACTION_CANCEL_DONE = "change-cancel-done";


    String ACTION_CHAT_STICKER = "chat-sticker";
    String ACTION_CHAT_RESUME_SEND_CLICK = "chat-resume-send-click";
    String ACTION_BUBBLE_EXCHANGE_TEL = "bubble-exchange-tel";

    String ACTION_CHAT_VOICE_CALL_CLICK = "chat-voice-call-click";
    String ACTION_CHAT_CALL_IN = "chat-call-in";
    String ACTION_TIP_VIDEO_CALL = "chat-tips-video-call";

    String ACTION_VICINITY_CLICK = "vicinity-click"; // 牛人在F1筛选点击“附近”
    String ACTION_VICINITY_FILTER = "vicinity-filter"; // 牛人进入“附近”筛选页面
    String ACTION_VICINITY_FILTER_EXPAND = "vicinity-filter-expand"; // 牛人使用附近筛选时-扩大筛选范围点击“查看”


    String ACTION_JOB_DRAFT_POP_UP = "job-draft-popup"; // 职位草稿弹窗
    String ACTION_JOB_DRAFT_POP_UP_BUTTON_CLICK = "job-draft-button"; // 职位草稿按钮点击

    String ACTION_CHOOSE_JOB_POSITION = "choose-job-position"; //boss进入选择职位类型页面


    String ACTION_CHOOSE_JOB_TITLE = "choose-job-title"; //boss进入选择职位名称页面
    String ACTION_DETAIL_USER_NOTE = "detail-user-note"; // 牛人/boss 在聊天设置中点击查看（设置）备注和标签(608)

    String ACTION_RESUME_DETAIL_TIME = "detail-time"; // 查看详情离开时记录每个模块的停留时间

    String ACTION_SEARCH_GEEK_CONNECT = "search-geek-connect"; // Boss在匿名牛人简历详情页点击立即联系牛人

    String ACTION_SEARCH_GEEK_RESUME_CONNECT = "search-geek-resume-connect"; // Boss在匿名牛人简历详情页点击立即联系牛人|使用搜索畅聊卡

    String ACTION_SEARCH_GEEK_CHATTING_CARD = "search-geek-chating-card"; // Boss在匿名牛人简历详情页点击使用畅聊卡

    String ACTION_SEARCH_GEEK_COMPARE_BUTTON = "search-geek-compare-button"; // Boss在牛电/月卡对比页按钮点击

    String ACTION_BOSS_REG_INFO = "boss-reg-info"; // Boss个人信息页提交结果成功
    String ACTION_CHANGE_CONNECT_TAB = "change-connect-tab";
    String ACTION_BOSS_ADD_JOB = "boss-add-job"; // Boss发布职位页提交结果成功
    String ACTION_BOSS_ADD_START = "userinfo-job-edit-start"; // Boss发布职位页提交结果成功

    String ACTION_GEEK_REG_INFO = "geek-reg-info"; // 牛人个人信息页提交结果成功(609)
    String ACTION_GEEK_REG_WJD = "geek-reg-wjd"; // 牛人微简历页面提交结果成功(609)
    String ACTION_GEEK_REG_EDU = "geek-reg-edu";
    String ACTION_GEEK_REG_DESC = "geek-reg-desc";
    String ACTION_GEEK_REG_EXP = "geek-reg-exp";
    String ACTION_STUDENT_LIST_CHANGE = "student-list-change";
    String ACTION_STUDENT_LIST_SORT = "list-f1-student-sort";
    String ACTION_STUDENT_LIST_SORT_COMPLETE = "list-f1-student-sortcomplete";
    String ACTION_EXPECT_QUESTION_MARK = "expect-question-mark";
    String ACTION_QUIT_UNSOLVE_TYPE = "zhs-quit-unsolve-type";
    String ACTION_STUDENGT_LIST_MANAGER = "student-list-manage";

    String ACTION_ADD_HUNTER_BLACK_LIST = "add_hunter_black_list";
    String ACTION_ADD_HUNTER_BLACK_LIST_CLICK = "add_hunter_black_list_click";

    String ACTION_SHARE_GEEK = "share-geek"; // boss点击分享牛人给同事
    String ACTION_SHARE_GEEK_TO = "share-geek-to"; // 选择三种转发对象

    String ACTION_PRIVACY_SETTING_CLICK = "privacy-setting-click"; // 牛人在F3页面【设置】页面点击【隐私设置】选项，详情：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38015917
    String ACTION_JOB_ASSISTANT_AREA_CLICK = "job-assistant-area-click"; // 牛人在F3页面【求职助手】区域点击选项，详情：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38015917
    String ACTION_MY_PERSONAL_RESUME_CLICK = "my-personal-resume-click"; // 牛人在F3页面个人信息区，点击【我的在线简历】    详情：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38015917
    String ACTION_SHARE_JD_CLICK = "share-jd-click";// 	boss在F3页面【招聘职位管理】子页面点击【分享微JD】，详情：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40012009
    String ACTION_PERSONAL_INFO_AREA_CLICK = "personal-info-area-click"; // boss/牛人在F3点击A区【头像区域】进入个人信息页面，详情：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40012009

    String ACTION_REG_BG = "reg-bg"; // 用户注册时选择身份(609)
    String ACTION_SIGN_PHONE_CLICK = "sign-phone-click"; // 用户输入手机号页面点击(609)
    String ACTION_SIGN_PGRAPH_CODE_CLICK = "sign-graph-code-click"; // 用户输入图形验证码页面点击(609)
    String ACTION_SIGN_SMS_CODE_CLICK = "sign-sms-code-click"; // 用户输入短信验证码页面点击(609)
    String ACTION_SIGN_NEW_PASSWORD_SET = "sign-new-password-set"; // 用户在忘记密码页面设置新密码后点击下一步(609)
    String ACTION_SIGN_WEIXIN_BIND_PHONE = "sign-weixin-bind-phone"; // 用户点击微信登录后进入绑定手机号页面(609)


    String ACTION_MY_RESUME_PREVIEW = "resume-preview"; // 牛人预览个人微简历
    String ACTION_CHAT_WINDOW_MORE = "chat-window-more";

    String ACTION_QUICK_REPLAY = "quick-reply-click";

    String ACTION_QUICK_REPLAY_MANAGE = "quick-reply-manage";

    String ACTION_RENEW_FILTER = "renew-filter";

    String ACTION_GROUP_CHAT_UNREAD_MESSAGES = "group-chat-unread-messages";


    String ACTION_QUICK_PROCESS_READ_ALL = "quick-process-read-all"; //f2快速处理页面点击全部已读
    String ACTION_GEEK_NICKNAME_SET = "geek-nickname-set"; // 更改公开昵称成功，点击下一步

    String ACTION_ADD_FRIEND_CONTINUE = "detail-geek-addfriend-continue";

    String C = "userinfo-job-material-showclick";

    String ACTION_PUSH_ARRIVE = "msg_arrive";//push到达上报

    String ACTION_PUSH_SHOW = "msg_show";//push弹通知上报

    String ACTION_PUSH_CLICK = "msg_click";//push点击通知上报
    String ACTION_CLICK_REMARK_GEEK = "user-note-edit"; //boss 点击牛人备注

    String ACTION_EDIT_JOB_LOCATION = "edit-job-location";
    String ACTION_EDIT_JOB_LOCATION_GPS = "edit-job-location-gps";
    String ACTION_CHOOSE_JOB_LOCATION_MAP = "choose-job-location-map";
    String ACTION_CHOOSE_JOB_LOCATION_SEARCH = "choose-job-location-search";
    String ACTION_CHOOSE_JOB_LOCATION_CITY = "choose-job-location-city";

    String ACTION_GUIDE_CONNECT_WECHAT = "guide-connect-wechat"; // 用户收到开启公众号消息提示引导
    String ACTION_CLOSE_GUIDE = "close-guide"; // 用户点击关闭公众号消息提示引导
    String ACTION_CONNECT_WECHAT_CLICK = "connect-wechat-click"; // 用户在公众号消息提示引导点击“去开启”
    String ACTION_F_3_SET_CLICK = "f3-set-click"; // boss/geek在f3页面右上角点击设置
    String ACTION_CLICK_BOSS_ITEM = "click-boss-item"; // F3页面道具点击
    String ACTION_WECHAT_NOTICE = "wechat-notice"; // boss/geek在设置菜单点击微信通知
    String ACTION_CHANGE_WECHAT_NOTICE = "change-wechat-notice"; // boss/geek在设置-微信通知内点击去开启/关闭
    String ACTION_CLOSE_WECHAT_SAVE = "close-wechat-save"; // boss/geek在确认关闭微信通知弹窗点击“确认关闭”

    String ACTION_REQUIRE_RECEIPT = "require-receipt"; // boss在购买记录页点击“开发票”
    String ACTION_GOTO_ITEMMALL = "goto-itemmall"; // boss在购买记录为空时点击“去道具商城”

    String ACTION_BOSS_REG_ADD_JOB = "boss-reg-addjob"; //boss注册完善阶段完成个人信息后点击发布职位

    String ACTION_QUICK_PROCESS_CLICK = "quick-process-click"; // boss/geek在f2消息页点击进入极速处理
    String ACTION_QUICK_PROCESS_CHAT_READ = "quick-process-chat-read"; // boss/geek在极速处理页面点击未读气泡进入聊天页面

    String ACTION_POSITION_SEARCH = "exp-position-search";
    String ACTION_POSITION_SEARCH_NULL = "exp-position-search-null";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15393
     * 触发用户满意度调研弹窗 https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=44993224，10分制更新： https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=194986238
     */
    String ACTION_USER_QUEST_ALERT = "user-quest-alert";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15394
     * 用户在满意度调研弹窗的点击分数的行为（709增加了关闭按钮，actionp打0）https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=48269891，V1013更新，https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=194986238
     */
    String ACTION_USER_QUEST_CLICK = "user-quest-click";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=59954
     * 回流用户求职意向收集
     */
    String ACTION_OTHER_BACK_USER_EXPECT_GUID = "other-back-user-expect-guid";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=59977
     * F1 补充完善半弹层曝光
     */
    String ACTION_LIFECYCLE_COMPLETE_F1_POPSHOWD = "lifecycle-complete-F1-popshow";


    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=59978
     * F1 补充完善半弹层关闭
     */
    String ACTION_LIFECYCLE_COMPLETE_F1_POPCLOSE = "lifecycle-complete-F1-popclose";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=59979
     * F1 补充完善半弹层点击确认
     */
    String ACTION_LIFECYCLE_COMPLETE_F1_POPUPLOAD = "lifecycle-complete-F1-popupload";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=59980
     * F2 补充完善半弹层板块完善提交
     */
    String ACTION_LIFECYCLE_COMPLETE_F1_POPSAVE = "lifecycle-complete-F1-popsave";


    String ACTION_ITEM_MALL_CLICK = "item-mall-click";
    String ACTION_BEAN_NAKUP = "bean-nakup";// 用户充值页面点击立即充值
    String ACTION_CLICK_CHAT_POSITION = "click-chat-position";
    String ACTION_VOIVE_CALL_HIT = "voice-call-hit";
    String ACTION_CALL_HIT_CLICK = "voice-call-hit-click";

    String ACTION_EDETAIL_TRANSFER_CV = "detail-transfer-cv";
    String ACTION_F2_CHAT_FILTER_TOAST = "f2-chat-filter-toast";
    String ACTION_F2_CHAT_FILTER_CLICK = "f2-chat-filter-click";
    String ACTION_F2_CHAT_FILTER_SELECT = "f2-chat-filter-choose";
    String ACTION_F2_CHAT_CLICK_DETRAIL = "f2-filter-click-detail";
    String ACTION_DETAIL_CHAT_READ_POSITION = "detail-chat-readposition";
    String ACTION_AUDIO_ACTION = "audio-action";

    String ACTION_LIFE_CYCLE_RESUME_VIDEO_HI_CLICK = "lifecycle-resume-videohi-popupclick";
    String ACTION_LIFE_CYCLE_RESUME_VIDEO_HI_SHOW = "lifecycle-resume-videohi-popupshow";

    String ACTION_EDIT_COMPANY_BENEFITS = "edit-company-benefits"; // boss进入到公司福利编辑页面
    String ACTION_MESSAGE_REEDIT = "message-reedit"; //用户撤回消息后点击“重新编辑”

    String ACTION_CLICK_JOB_COMPETIVITY = "click-job-competitive";

    String ACTION_CLICK_EMOTION = "click-emoji";
    String ACTION_JOB_KEYWORD_INTO = "job-keyword-into";
    String ACTION_F1_OPEN_JOB = "preview-f1-open-job"; // vip阻断boss在F1预发布页点击灰条“打开职位”进入职位详情页

    String ACTION_DETAIL_MATE_RECORD = "detail-mate-record";

    //曝光阻断提醒（简历助手中同事沟通进度图标（806））
    String ACTION_BIZ_BLOCK_REMIND_EXPOSE = "biz-block-remind-expose";

    String ACTION_FRIEND_DELETE = "friend_delete";

    String ACTION_CLICK_DIALOG_MARK = "click-dialog-mark";

    String ACTION_EMERGENCY_TOP_CLICK = "emergency-top-card-banner-click";
    String ACTION_EMERGENCY_TOP_SHOW_BANNER = "emergency-top-card-show-banner";

    String ACTION_ANDROID_CRASH = "android-crash";
    String ACTION_QUICK_PROCESS = "quick-process"; // 快速处理操作
    String QUICK_PROCESS_LID = "quick-process-name-card-list";

    String ACTION_VIP_F3_GUIDE = "vip-f3-guide";
    String ACTION_GROUP_INVITE_SEARCH_CLICK = "group-invite-search-click";
    String ACTION_BLOCK_HUNTER = "block-hunter"; // 牛人在隐私设置页面点击【禁止猎头联系我】
    String ACTION_BLOCK_HUNTER_POSITION = "block-hunter-position"; // 牛人在隐私设置页面点击【不看猎头职位】
    String ACTION_DETAIL_CONNECT = "detail-connect"; // 牛人在聊天窗口点击被开聊卡片跳转职位详情或个人微简历或boss主页 boss
    String ACTION_DETAIL_PROFILE_SWITCH = "detail-profile-switch"; // 牛人在boss个人主页切换tab标签
    String ACTION_GEEK_EXP_PAGE = "geek-exp-page"; // 牛人进入三级职类选择页面

    String ACTION_HIDDEN_RESUME_REASON_TO_HUNTER = "hidden-resume-reason-to-hunter"; // 牛人隐藏简历的原因选择确认

    String ACTION_CHANGE_REALME_PICTURE = "change-realme-picture"; // 聊天页面或者个人信息页面，提示更新真人头像页面出现
    String ACTION_CHANGE_REALME_PICTURE_CLICK = "change-realme-picture-click"; // 聊天页面或者个人信息页面，提示更新真人头像页面出现点击立即上传
    String ACTION_CHANGE_UPLOAD_REALME_PICTURE = "change-upload-realme-picture"; // 更新上传头像页面出现
    String ACTION_CHANGE_UPLOAD_REALME_PICTURE_CLICK = "change-upload-realme-picture-click"; // 更新上传头像页点击更新头像
    String ACTION_CHANGE_BOSS_AVATAR_INFO_CLICK = "userinfo-profile-photo-changeinfo"; // boss在个人信息页更换头像页面，点击更换头像/更换挂件
    String ACTION_MINI_BLUE_INFO_CLICK = "mini-blue-info-click"; // 不接收小程序快速完善牛人的信息
    String ACTION_MINI_BLUE_INFO_CLOSE = "mini-blue-info-close"; // 不接收小程序快速完善牛人的信息,弹窗确定and取消按钮


    String ACTION_SYNC_VJD_BUTTON = "sync-vjd-button"; // 牛人在同步微简历流程中点击不同同步类型按钮
    String ACTION_SYNC_VJD_REPLACE = "sync-vjd-replace"; // 牛人在微简历同步流程点击【确定替换】
    String ACTION_SYNC_VJD_CORRECT = "sync-vjd-correct"; // 牛人在微简历同步流程个人优势模块点击【修改】
    /*牛人在微简历同步流程各模块选择同步操作类型*/
    String ACTION_SYNC_VJD_CHOOSE = "sync-vjd-choose";
    /*NLP附件简历解析点击删除垃圾简历*/
    String ACTION_USERINFO_MICRORESUME_NLPRESUME_GARBAGEDELETE = "userinfo-microresume-nlpresume-garbagedelete";
    /*NLP附件简历解析垃圾简历黄条曝光*/
    String ACTION_USERINFO_MICRORESUME_NLPRESUME_GARBAGEEXPOSE = "userinfo-microresume-nlpresume-garbageexpose";
    /*NLP附件简历解析点击编辑重复经历*/
    String ACTION_USERINFO_MICRORESUME_NLPRESUME_REPEATEDIT = "userinfo-microresume-nlpresume-repeatedit";
    /*NLP附件简历解析重复经历曝光*/
    String ACTION_USERINFO_MICRORESUME_NLPRESUME_REPEATEXPOSE = "userinfo-microresume-nlpresume-repeatexpose";

    String ACTION_SYNC_VJD_PAGE = "sync-vjd-page"; // 牛人进入到微简历同步模块

    String ACTION_LIFECYCLE_RESUME_VIDEOHI_REMINDERCLICK = "lifecycle-resume-videohi-reminderclick";
    String ACTION_LIFECYCLE_RESUME_VIDEOHI_REMINDEREXPO = "lifecycle-resume-videohi-reminderexpo";

    String ACTION_MAP_TRANSPORTATION = "detail-map-transportation";
    String ACTION_BIZ_BZB_TOPUPTEXT2_EXPOSURE = "biz-bzb-topuptext2-exposure";

    String ACTION_GO_CLICK = "go-click";
    String ACTION_GUIDE_CLICK = "guide-click";
    String ACTION_TRANSPORTATION_SWITCH = "transportation-switch";
    String ACTION_MAP_APP_SELECT = "action-detail-job-guide";
    String ACTION_GEEK_LOCATION_UPDATE = "geek-location-update";
    String ACTION_DETAIL_MAP_ADDRESS = "detail-map-address";

    String ACTION_SMS_NOTIFY_CONFIRM = "sms-notify-comfirm";
    String ACTION_EXP_LIST_ADD_EXPECT = "exp-list-add-expect";

    String ACTION_GEEK_COMP_INFO_PAGE = "geek-comp-info-page"; // 牛人首善流程进入到个人信息页面（如果是注册前选了牛人身份的话，这就是进来的第一个页面）

    String ACTION_JOB_POSITION_NULL_PAGE = "job-position-null-page"; // 发布职位选择公司地址时进入无搜索结果空页面 https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=36733373

    //boss点击场景化道具入口
    String ACTION_ITEM_SCENE_CLICK = "item-scene-click";

    String ACTION_PRIVACY_AGREEMENT = "privacy_agreement"; // 用户首次注册进入用户协议和隐私政策选择页面（uid为空，数据上用deviceID区分）
    String ACTION_PRIVACY_AGREEMENT_CLICK = "privacy_agreement_click"; // 用户在隐私协议政策窗口点击选择按钮（uid为空，数据上用deviceID区分）


    String ACTION_BLUE_F1_MORE = "blue-f1-more"; // 蓝领牛人在F1列表8个标签中点击最后一个“更多”

    String ACTION_VIP_FILTER_DIALOG_SHOW = "open-vip-filter-block";// vip筛选功能阻断-出现购买vip弹窗

    String ACTION_CV_JOB_COMPETITIVE = "cv-job-competitive"; // boss在牛人简历中点击竞争力分析

    String ACTION_F1_KEYWORD_SEARCH = "f1-keyword-search"; // f1关键词筛选页面点击搜索框
    String ACTION_F1_KEYWORD_SUGGEST = "f1-keyword-suggest"; // f1关键词筛选功能搜索页面点击推荐关键词
    String ACTION_F1_KEYWORD_CONFIRM = "f1-keyword-confirm"; // f1关键词筛选页面点击确认
    String ACTION_F1_KEYWORD_NULL_CLICK = "f1-keyword-null-click"; // f1关键词搜索空结果页面点击按钮
    String ACTION_F1_KEYWORD_CLICK = "f1-keyword-click"; // f1列表点击关键词筛选按钮

    /**
     * 1106.603 F1筛选模块一级/二级筛选的点击/取消
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54355
     */
    String ACTION_F1_FILTER_WORD_CLICK = "f1-filter-word-click";

    /**
     * 1106.603 F1筛选项点击“确认”按钮提交筛选
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54356
     */
    String ACTION_F1_FILTER_MODULE_SUBMIT = "f1-filter-moudle-submit";


    String ACTION_FILTER = "f2-newgeek-filter";
    String ACTION_CHAT_LIMIT_WEIXIN = "action-chat-limit-weixin";

    String ACTION_VJD_PERSONAL_INFO_PAGE = "vjd-personal-info-page"; // 微简历页面点击进入个人信息页面 https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38016396
    String ACTION_VJD_PAGE_CLICK = "vjd-page-click"; // 牛人在f3我的微简历页面内的点击按钮https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38016396

    String ACTION_BRAND_BRIEF_SAVE = "brand-brief-save"; // 公司简称页面保存（公司简称页面右上角处点击保存     https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40010820）
    String ACTION_FULL_NAME_GUESS = "full-name-guess"; // 点击全称猜想（在公司简称页处  点击根据公司全称猜测   https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40010820）
    String ACTION_BRAND_BRIEF = "brand-brief"; // 进入公司简称页面（从新建公司页面点击公司简称处的请填写      https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40010820）
    String ACTION_BRAND_EDIT_NEWS = "brand-edit-news"; // 点击进入公司编辑页中的公司资讯（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=38015930）
    String ACTION_BRAND_NEW_BRAND = "build-new-brand"; // 点击新建公司（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40010820）

    String ACTION_F1_SCREEN_CLICK_FILTER = "f1-screen"; // 牛人在F1页面点击筛选按钮
    String ACTION_SEARCH_SCREEN_CLICK_FILTER = "search-screen"; // 牛人在F1页面点击筛选按钮


    /**
     * 816
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5616
     * 【个人主页-基本信息页】显示
     */
    String ACTION_EXTENSION_GET_MYHOME_BASICSHOW = "extension-get-myhome-basicshow";

    /**
     * 816
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5617
     * 个人主页-基本信息页】完成点击
     */
    String ACTION_EXTENSION_GET_MYHOME_BASICFINISHCLICK = "extension-get-myhome-basicfinishclick";

    /**
     * 816
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5618
     * 【个人主页】停留时长
     */
    String ACTION_EXTENSION_GET_MYHOME_DURATION = "extension-get-myhome-duration";

    /**
     * 820
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=22640
     * C端搜索行业专区点击话题
     */
    String ACTION_SEARCH_SPECIAL_CLICKTOPIC = "action-search-special-clicktopic";

    /**
     * 914
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=27912
     * 点击职位详情页面搜索框
     */
    String ACTION_SEARCH_JOB_SUPER = "action-search-job-super";

    /**
     * 915
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=28178
     * 搜索屏蔽公司，点击详情，解除屏蔽公司
     */
    String ACTION_SEARCH_COMPANY_BLOCKREMOVE = "action-search-company-blockremove";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30068
     * VR 提示条点击立刻添加
     */
    String ACTION_VR_ADD_PHOTOE_CLICK = "vr-add-photoe-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30071
     * vr拍摄公司环境，点击完成按钮
     */
    String ACTION_VR_PHOTO_SAVE_SUCCESS_CLICK = "vr-photo-save-success-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30072
     * vr拍摄公司环境，点击退出按钮
     */
    String ACTION_VR_PHOTO_QUIT_CLICK = "vr-photo-quit-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30073
     * vr模式下，点击图片
     */
    String ACTION_VR_PHOTO_CLICK = "vr-photo-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30074
     * vr拍摄页面，点击修改信息按钮
     */
    String ACTION_VR_CHANGE_INFO_CLICK = "vr-change-info-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30075
     * vr拍摄修改页面，点击删除按钮
     */
    String ACTION_VR_DELETED_CLICK = "vr-deleted-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30076
     * vr添加照片信息页面，点击确定
     */
    String ACTION_VR_PHOTO_INFO_ADD_SAVE = "vr-photo-info-add-save";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30104
     * vr预览界面，点击右上方陀螺仪按钮
     */
    String ACTION_VR_TUOLUO_BUTTOM_CLICK = "vr-tuoluo-buttom-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30105
     * vr预览界面，点击屏幕
     */
    String ACTION_VR_SCREEN_CLICK = "vr-screen-click";

    /**
     * 920
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30106
     * vr预览页面，滑动下方信息栏
     */
    String ACTION_VR_SLIDE_INFO_SHOW = "vr-slide-info-show";

    /**
     * 921
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30225
     * 集团&品牌聚合页“说明文案和问题反馈”入口曝光
     */
    String ACTION_SEARCH_BRAND_FEEDBACK = "action-search-brand-feedback";

    /**
     * 921
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30226
     * 集团&品牌聚合页“说明文案和问题反馈”点击反馈
     */
    String ACTION_SEARCH_BRAND_FEEDBACKCLICK = "action-search-brand-feedbackclick";

    /**
     * 921
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30227
     * 集团&品牌聚合页“说明文案和问题反馈”点击提交
     */
    String ACTION_SEARCH_BRAND_FEEDBACKSUBMIT = "action-search-brand-feedbacksubmit";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30441
     * 搜索时【搜索发现】中安心保入口词的曝光
     */
    String ACTION_REASSURANCE_ENTRANCE_SEARCH_SHOW = "reassurance-entrance-search-show";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30534
     * 添加产品介绍页曝光
     */
    String ACTION_PRO_INTRO_IMAGE_SHOW = "pro-intropage-show";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30536
     * 添加产品介绍页按钮点击
     */
    String ACTION_PRO_INTROPAGE_BUTTONCLICK = "pro-intropage-buttonclick";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30537
     * 产品介绍编辑页曝光
     */
    String ACTION_PRO_EDITPAGE_SHOW = "pro-editpage-show";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30538
     * 产品介绍编辑页保存按钮点击
     */
    String ACTION_PRO_EDITPAGE_SAVE_CLICK = "pro-editpage-save-click";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30540
     * 产品介绍编辑页 添加产品logo按钮点击
     */
    String ACTION_PRO_EDITPAGE_ADD_CLICK = "pro-editpage-add-click";

    /**
     * 1001
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=30843
     * 编辑页面按钮点击
     */
    String ACTION_BRAND_EDITPAGE_BUTTONCLICK = "brand-editpage-buttonclick";

    /**
     * 1004
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=31547
     * 内容搜索-tab切换
     */
    String ACTION_GET_SEARCH_TAB_CHANGE = "get-search-tab-change";

    /**
     * 1004
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=31548
     * 内容搜索-强插卡片曝光
     */
    String ACTION_GET_SEARCH_CARD_EXPOSE = "get-search-card-expose";

    /**
     * 1004
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=31549
     * 内容搜索-强插卡片点击
     */
    String ACTION_GET_SEARCH_CARD_CLICK = "get-search-card-click";

    /**
     * 1005
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=31870
     * 点击安心保广告卡片右上角关闭【×号】
     */
    String REASSURANCE_CARD_CLOSE_CLICK = "reassurance-card-close-click";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50093
     * 1017.81 app B浏览vr照片列表页面
     */
    String VR_BOSS_PHOTOLIST_POP = "vr-boss-photolist-pop";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50095
     * 1017.81 app B浏览vr照片列表页面时，照片 实际 曝光
     */
    String VR_BOSS_PHOTOLIST_PHOTOPOP = "vr-boss-photolist-photopop";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50097
     * 1017.81 app B浏览vr照片列表页面时，点击 去拍摄/重新拍摄
     */
    String VR_BOSS_PHOTOLIST_SHOOT = "vr-boss-photolist-shoot";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50098
     * 1017.81 app B浏览vr照片列表页面时，点击 图片区域预览/编辑/删除
     */
    String VR_BOSS_PHOTOLIST_EDIT = "vr-boss-photolist-edit";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50099
     * 1017.81 app B浏览vr照片列表页面时，点击 保存（仅记录有效保存）
     */
    String VR_BOSS_PHOTOLIST_SAVE = "vr-boss-photolist-save";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50100
     * 1017.81 app B在vr照片列表页保存后，同步职位弹窗曝光
     */
    String VR_BOSS_PHOTOLIST_UPDATETOAST_POP = "vr-boss-photolist-updatetoast-pop";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50101
     * 1017.81 app B在vr照片列表页保存后，同步职位弹窗点击确认
     */
    String VR_BOSS_PHOTOLIST_UPDATETOAST_CK = "vr-boss-photolist-updatetoast-ck";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50102
     * 1017.81 app B在vr准备拍摄页面，点击「拍摄」
     */
    String USERINFO_BRAND_HOMEPAGE_VEDIOSHOOTCK = "userinfo-brand-homepage-vedioshootck";

    /**
     * 1017
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=23418
     * 用户进入VR全景照片拍摄页面
     */
    String USERINFO_BRAND_HOMEPAGE_VEDIOSHOOT = "userinfo-brand-homepage-vedioshoot";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52287
     * 地址核验-进入地址核验页面（开始进入拍摄流程）
     */
    String LIFECYCLE_CERTIFY_ENVVR_VERIFYINFO = "lifecycle_certify_envvr_verifyinfo";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52293
     * 地址核验-用户点击进行拍摄（单个模块）「包含‘拍摄’按钮和‘我知道了开始拍摄」
     */
    String LIFECYCLE_CERTIFY_ENVVR_STARTFILM = "lifecycle_certify_envvr_startfilm";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52294
     * 地址核验-拍摄内容上传结果（单个模块）
     */
    String LIFECYCLE_CERTIFY_ENVVR_UPLOADRESULT = "lifecycle_certify_envvr_uploadresult";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52296
     * 地址核验-用户点击「提交」
     */
    String LIFECYCLE_CERTIFY_ENVVR_SUBMITFILM = "lifecycle_certify_envvr_submitfilm";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52298
     * 地址核验-vr内容上传-拍摄版本记录
     */
    String LIFECYCLE_CERTIFY_ENVVR_VRVERSION = "lifecycle_certify_envvr_vrversion";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52300
     * 地址核验-用户点击返回按钮
     */
    String LIFECYCLE_CERTIFY_ENVVR_CLICKBACK = "lifecycle_certify_envvr_clickback";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52301
     * 地址核验-用户点击确定退出页面-退出界面选择原因
     */
    String LIFECYCLE_CERTIFY_ENVVR_QUITREASON = "lifecycle_certify_envvr_quitreason";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52302
     * 地址核验-用户点击再试试
     */
    String LIFECYCLE_CERTIFY_ENVVR_TRYAGAIN = "lifecycle_certify_envvr_tryagain";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52303
     * 地址核验-用户点击进行视频环境认证
     */
    String LIFECYCLE_CERTIFY_ENVVR_ENVVIDEO = "lifecycle_certify_envvr_envvideo";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52304
     * 地址核验-用户VR拍摄引导页面曝光/点击情况
     */
    String LIFECYCLE_CERTIFY_ENVVR_VRGUIDE = "lifecycle_certify_envvr_vrguide";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52310
     * 地址核验-vr内容上传-用户点击重新拍摄
     */
    String LIFECYCLE_CERTIFY_ENVVR_UPLOADAGAIN = "lifecycle_certify_envvr_uploadagain";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52312
     * 地址核验-用户点击去录制视频
     */
    String LIFECYCLE_CERTIFY_ENVVR_RECORDVIDEO = "lifecycle_certify_envvr_recordvideo";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52313
     * 地址核验-算法返回拍摄不清晰
     */
    String LIFECYCLE_CERTIFY_ENVVR_IAUNCLEARFEEDBACK = "lifecycle_certify_envvr_iaunclearfeedback";

    /**
     * 1102
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52321
     * 地址核验-用户拍摄过程app失活
     */
    String LIFECYCLE_CERTIFY_ENVVR_QUITAPP = "lifecycle_certify_envvr_quitapp";

    /**
     * 1111
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52321
     * 地址核验，地址核验VR用户点击进行场景拍摄
     */
    String LIFECYCLE_CERTIFY_ADDRESSVR_ENTERSCENE = "lifecycle-certify-addressvr-enterscene";

    /**
     * 912
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=27481
     * 首页点击搜索框
     */
    String TOURIST_SEARCH_LIST_CLICK = "tourist-search-list-click";

    /**
     * 1009
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=44835
     * vr拍摄“我已了解拍摄规则“点击
     */
    String VR_RULE_READY_CLICK = "vr-rule-ready-click";

    /**
     * 1009
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=44834
     * vr拍摄“我已找到合适的位置”点击”点击
     */
    String VR_AREA_READY_CLICK = "vr-area-ready-click";

    /**
     * 1014
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48496
     * 抽屉入口设置点击
     */
    String DRAW_ENTRY_SETTING_CLICK = "draw-entry-setting-click";

    /**
     * 1014
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48501
     * 点击左上角「返回时」，记录设置页面的抽屉设置状态
     */
    String DRAW_SETTINGS_STATUS = "draw-settings-status";

    /**
     * 1014
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48504
     * 抽屉设置中点击删除
     */
    String DRAW_SETTINGS_DELETE = "draw-settings-delete";

    /**
     * 1101.520
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=51891
     * 老用户欢迎页猎头屏蔽开关点击【1101.520】
     */
    String BIZ_ITEM_ACCURATESEARCH_WELCOMPAGECLICK = "biz-item-AccurateSearch-welcompageclick";


    String ACTION_FILTER_GEEK_NOTICE = "filter-geek-notice"; // 出现筛选牛人提示（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40637218）
    String ACTION_FILTER_GEEK_NOTICE_CLICK = "filter-geek-notice-click"; // 点击筛选牛人功能提示（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=40637218）

    String ACTION_CHAT_QUESTION_F3 = "chat-question-f3";// boss在f3点击“新招呼问题回复”
    String ACTION_ADD_CHAT_QUESTION_START = "add-chat-question-start"; // 进入新招呼问题设置页面
    String ACTION_CLICK_QUESTION_TAG = "click-question-tag"; // boss点击新招呼自动回复按钮
    String ACTION_SYS_QUIT_APPRAISE_CLICK = "zhs-system-quit-appraise-click";
    String ACTION_LOGO_CHAT = "ligo-chat";
    String ACTION_ZHS_ART_SERVICE_CLICK = "zhs-artificial-service-appraise-click";
    String ACTION_ROBOT_ANSWER_CLICK = "zhs-robot-answer-appraise-click";
    String ACTION_LOW_GUIDE_CLICK = "zhs-right-low-guide-click";
    String ACTION_CHAT_INPUT_ALBUM_CLICK = "zhs-chat-input-album-click";
    String ACTION_CHAT_INPUT_PLUS_CLICK = "zhs-chat-input-plus-click";
    String ACTION_GUESS_QUESTION_CLICK = "zhs-guess-question-click";
    String ACTION_ZHS_RIGHT_UP_CLICK = "zhs-right-up-click";
    String ACTION_LIGO_PROFILE_DETAIL = "ligo-zpboss-profile-detail";
    String ACTION_LIGO_CHAT_READ = "ligo-chat-read";
    String ACTION_ZHS_KNOWLEDGE_CARD_CLICK = "zhs-knowledge-card-click";
    String ACTION_PUSH_CALL_EXCHANGE = "push-call-exchange";
    String ACTION_PUSH_CALL_EXCHANGE_AGREE = "push-call-exchange-agree";

    String ACTION_SOCIAL_CONTACT_CLICK = "social-contact-click"; // 点击牛人社交主页
    String ACTION_CLICK_ORDER = "click-order"; // 点击"去预约"

    String ACTION_LEAVE_CHANGE = "leave-change"; // 在 "我的公司" 页面点击 "离开公司"
    String ACTION_DETAIL_JOB_INFO = "detail-job-info"; // 在职位详情页点击查看内容

    String ACTION_SHGO_CHANGE_PIC = "shgo-change-pic"; // 分享图片是点击换一张（）https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43680330
    String ACTION_SHARE_JOBBOSS = "share_jobboss";

    String ACTION_NO_DISTURB_TIME = "no-disturb-time"; // 牛人/boss夜间免打扰时段提交（ https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681477 ）
    String ACTION_NO_DISTURB_OPEN = "no-disturb-open"; // 牛人/boss点击夜间免打扰按钮（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681477）
    String ACTION_NOTICE_OPEN_CLICK = "notice-open-click"; // 点击开启消息通知弹窗按钮 （ https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681477 ）

    String ACTION_WORK_UPDATE_INTO = "work-update-into";// 进入工作经历修改页

    String ACTION_DETAIL_WORK_TASTE = "detail-work-taste";// 查看工作体验详情页面（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_LIST_WORK_TASTE = "list-work-taste";// 牛人刷工作体验列表（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_BRAND_WORK_TASTE = "brand-work-taste";// 填写工作体验后确认岗位信息页面点击完成（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_TRANSFER_MATE_TASTE_CLICK = "transfer-mate-taste-click";// 在填写工作体验页面点击邀请同事（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_BRAND_TASTE_TAG = "brand-work-taste-tag";// 填写工作体验时点击标签（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_BRAND_WORK_TASTE_OVER = "brand-work-taste-over";// 填写工作体验后确认岗位信息页面点击完成（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_BRAND_TASTE_SAVE = "brand-work-taste-save";// 在填写工作体验页面点击保存按钮（ https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231 ）
    String ACTION_BRAND_TASTE_ADD = "brand-work-taste-add";// 在填写工作体验页面点击添加工作经验按钮（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=43681231）
    String ACTION_BRAND_INFORMATION_PIC_HOW = "brand-information-pic-how";// 在添加公司照片页面点击查看别人怎么拍 （ https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=42436606 ）
    String ACTION_LOOK_OTHER_WRITE = "lifecycle-complete-homepage-tastsample";//工作体验  看看别人怎么写
    String ACTION_WORK_EXP_HAVE_EDIT = "lifecycle-complete-homepage-expwrite";//退出时 是否编辑过

    String ACTION_DETAIL_LOWEST = "detail-lowest"; // 详情页滑到最底部

    String ACTION_DETAIL_REPORT_DONE = "detail-report-done";//点击举报提交成功
    String ACTION_DETAIL_REPORT_CLICK = "detail-report-click";//点击举报
    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15378
     * <p>
     * 用户在最终在NPS弹窗上点击提交按钮 https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=44993224， V1013修改：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=194986238
     */
    String ACTION_USER_QUEST_SUBMIT = "user-quest-submit"; // 用户在展开文本输入框的NPS弹窗上点击按钮

    String ACTION_DETAIL_GEEK_ADD_FRIEND_BLOCK = "detail-geek-addfriend-block"; // boss 开聊牛人受阻断 (点击开聊时受阻断，剩余次数为 0 时触发)
    String ACTION_NOTICE_OPEN_POPUP = "notice-open-popup";
    String ACTION_SHARE_EXCHANGE_PICTURE = "exchange_picture"; // 708弹窗点击换一张
    String ACTION_SHARE_POST_SHARE_CLICK = "poster_share_click"; // 708弹窗点击分享这一刻按钮
    String ACTION_ADD_SHAE_POSTER = "add_share_poster"; // 708 弹窗

    String ACTION_MAKE_CALL = "make-call";
    String ACTION_CHAT_WEIXIN = "chat-weixin";
    String ACTION_CHAT_MOBILE = "chat-mobile";
    String ACTION_RESUME_CHOOSE = "resume-choose";
    String ACTION_CHAT_NLP_REMIND_CLOSE = "chat-nlp-remind-close";

    String ACTION_IMPORTANT_NOTIFY_CLICK = "important-notify-click"; //boss点击重要消息提醒（ https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=45585092）
    String ACTION_IMPORTANT_NOTIFY = "important-notify"; //boss收到重要消息提醒 ( https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=45585092 )

    String ACTION_EXPECT_KEY_SUGGEST = "expect-keyword-suggest";//触发nlp职类预测
    String ACTION_EXPECT_CANCEL = "expect-cancel";//添加求职期望页面点击取消a
    String ACTION_UNREAD_MESSAGE = "choose-unread-message";
    String ACTION_ADD_INTEREST_SHOW = "new-addinterest-show";
    String ACTION_ADD_INTEREST_CLICK = "new-addinterest-click";

    /**
     * 点击资格证书模块
     */
    String ACTION_ADD_CERTIFICATE_CLICK = "add-certificate-click";
    String ACTION_ITEM_MALL_OTHERS = "item-mall-others";

    String ACTION_MORE_BOSS_TOUCH = "more-bosstouch"; // 牛人端F3看过我页面增加让更多boss看到我按钮，跳转后打点
    String ACTION_ZHU_USERENTRY_SUCCESS = "zhs-userentry-success"; // 智慧石项目，用户成功进入我的客服页面
    String ACTION_ZHU_USER_QUIT = "zhs-user-quit"; // 智慧石，用户在客服界面点击页面左上角退出
    String ACTION_RESUME_SIZE_CHOOSE_FAIL = "resume-size-choose-fail"; // 上传附件失败
    String ACTION_DESC_KEYWORD_CLICK = "desc-keyword-click";
    String ACTION_CHAT_LOCATION_SEND = "chat-location-send";
    String ACTION_CARD_CLICK = "location-card-click";
    String ACTION_LOCATION_REFUSE = "chat-location-refuse";
    String ACTION_LOCATION_AGREE = "chat-location-agree";

    String ACTION_CONFIRM_BACK_POP_UP = "confirm-back-pop-up"; //道具确认购买页返回时触发弹窗
    String ACTION_CONFIRM_BACK_CLICK = "confirm-back-back"; //道具确认购买页返回时弹窗操作结果
    String ACTION_PASSIVE_CALL_EDIT = "help-passive-call-edit";//请销售顾问联系我-修改
    String ACTION_PASSIVE_CALL_BACK = "help-passive-call-back";//请销售顾问联系我-返回默认号码

    //接受或放弃确认支付页挽留优惠(912.34)
    String ACTION_BIZ_BLOCK_CLICK_GIVEUPORACCEPTDISCONT = "biz-block-click-GiveUpOrAcceptDiscont";
    //912.34随机优惠弹窗曝光埋点
    String ACTION_BIZ_BLOCK_EXPOSURE_RANDOMDISCOUNT = "biz-block-exposure-RandomDiscount";

    String ACTION_PUSH_WORK_TASTE = "push-work-taste";//boss收到工作体验push
    String ACTION_PUSH_WORK_TASTE_CLICK = "push_work_taste_click";//boss点击工作体验push
    String ACTION_DETAIL_HOME_PAGE = "detail-home-page";//boss进入公司主页

    String ACTION_ENTRANCE_EXPOSURE = "entrance-exposure";

    String ACTION_ADD_NEW_JOB_CLICK = "add-newjob-click"; //点击  发布新职位  按钮

    String ACTION_CLOSE_ANOMALY_TIMELINE_MAKESURE = "close-anomaly-timeline-makesure"; // VIP简历助手-确认-异常时间线点击关闭

    String ACTION_COMPANY_MORE = "company-more"; // VIP简历助手-简历详情点击牛人简历中的公司名称

    String ACTION_CHAT_ANOMALY_TIMELINE = "chat-anomaly-timeline";// VIP简历助手-异常时间线点击开聊

    String ACTION_CLOSE_ANOMALY_TIMELINE = "close-anomaly-timeline"; // VIP简历助手-异常时间线点击关闭

    String ACTION_CLOSE_ANOMALY_COMFIRM = "close-anomaly-comfirm"; // VIP简历助手-牛人详情页，简历异常【去询问】确认

    String ACTION_CLOSE_ANOMALY_CLICK = "close-anomaly-click"; // VIP简历助手-牛人详情页，简历异常【去询问】点击

    String ACTION_ACCELERATOR_ASK_ADDFRIEND_CONTINUE = "accelerator-ask-addfriend-continue"; // 蓝领牛人选择一个提问问题继续聊

    String ACTION_ACCELERATOR_ASK_ADDFRIEND = "accelerator-ask-addfriend"; // 蓝领牛人选择一个提问问题继续聊

    String ACTION_CLOSE_COMPANY_MORE = "close-company-more"; // VIP简历助手-关闭-简历详情点击牛人简历中的公司名称

    String ACTION_EMERGENCY_TOP_CARD = "emergency_top_card";//弹出急聘置顶卡导购弹窗

    String ACTION_PIC_UPLOAD_SAVE = "pic-upload-save"; // 牛人点击保存退出（触发时机：点击保存按钮）
    String ACTION_PIC_UPLOAD = "pic-upload"; // 牛人点击添加作品
    String ACTION_PIC_OPEN_CLICK = "pic-open-click"; // 牛人点击放大展示的作品
    String ACTION_PIC_DELETE = "pic-delete"; //牛人点击删除作品
    String ACTION_ADD_PIC_PAGE = "add-pic-page"; // 作品展示点击
    String ACTION_GEEK_PIC_CLICK = "geek-pic-click"; // Boss点击放大牛人作品
    String ACTION_PIC_UPLOAD_UNSAVE = "pic-upload-unsave"; // 牛人点击不保存退出

    String ACTION_CLICK_ENHANCE_EXPOSURE = "click-enhannce-exposure";//点击我的在线简历和附件简历页面的涨曝光
    String ACTION_CLICK_REMOVE_RECOMMENDATION = "click-remove-recommendation"; //点击X去掉置顶职位幅条

    String ACTION_SETTING_GPS_POPUP = "setting-gps-popup"; // boss发布职位时强制索要GPS定位弹框
    String ACTION_ATTENTION_BRAND_ALL = "attention-brand-all"; // 牛人点击“查看全部”关注公司
    String ACTION_GEEK_RESUME_PREVIEW_FAIL = "geek-resume-preview-fail"; // 牛人加载附件失败
    String ACTION_BOSS_RESUME_PREVIEW_FAIL = "boss-resume-preview-fail"; // 牛人加载附件失败

    String ACTION_CHANGE_CONNECT_JOB = "change-connect-job"; // 在确认条中切换职位
    String ACTION_DETAIL_CONNECT_JOB = "detail-connect-job"; // 拥有搜索畅聊卡时，点击沟通职位上方的职位确认条

    String ACTION_ZHS_AUTOQUEST_PUSH = "zhs-autoquest-push";
    String ACTION_ZHS_AUTOQUEST_CLICK = "zhs-autoquest-click";

    String ACTION_CLICK_GEEK_CONNECTION = "click-geek-connection"; //在牛人简历页点击同事协作功能
    String ACTION_JOB_UPDATE_CLICK = "job-update-click"; // 在职位详情页，点击修改职位按钮
    String ACTION_PHONE_AUTHORIZATION_POP_UP = "zhipin-phone-authorization-pop-up"; // 允许通过boss直聘打电话弹窗
    String ACTION_DIRECT_AUTHORIZE_FIRST_PAGE_EXPOSURE = "directcall-authorize-firstpage-popup-exposure"; //牛人打开app触发电话直拨授权弹窗的曝光（1108.1）
    String ACTION_TAP_ALLOW_TO_CONNECT = "tap-allow-to-connect"; // 虚拟电话授权页面打开允许boss直接电话联系我
    String ACTION_MAKE_A_CALL = "make-a-call"; // boss牛人详情页点击打电话
    String ACTION_JOB_DETAIL_ADDFRIEND = "userinfo-job-detail-addfriend"; // boss牛人详情页点击打电话

    String ACTION_GARBAGE_ADVANCE_IN = "my-advantage-click"; // 716 垃圾简历的 编辑

    String ACTION_NLP_FEEDBACK_SHOW = "sync-vjd-comment"; //716 简历解析弹窗
    String ACTION_NLP_FEEDBACK_RESULT = "sync-vjd-comment-result"; //716 简历解析反馈结果
    //在公司主页右上角点击“...”对该公司进行更多操作
    String ACTION_BRAND_OPERATION = "brand-operation";
    String ACTION_BRAND_WORK_TASTE_POPUP = "brand-work-taste-popup";
    String ACTION_WORK_TASTE_WILLDEL = "brand-work-taste-willdel";
    String ACTION_EXP_ADDRESS_MAP = "exp-address-map";
    String ACTION_EXP_ADDRESS_MAP_HOME = "exp-address-map-home";
    String ACTION_EXP_ADDRESS_MAP_SEARCH = "exp-address-map-search";
    //牛人添加屏蔽公司/解除屏蔽公司
    String ACTION_MASK_BRAND_CONFIRM = "mask-brand-confirm";
    //在公司主页内点击“屏蔽公司”
    String ACTION_MASK_BRAND = "mask-brand";
    String ACTION_EXP_ADDRESS_MAP_OUT = "exp-address-map-out";//在地图选点页面弹出超出期望城市的弹窗
    String ACTION_EXP_ADDRESS_MAP_SAVE = "exp-address-map-save";//牛人在地图选点页面点击完成

    //7.17 牛人在职位详情页点击同城招聘模块
    String ACTION_DETAIL_BOSS_OTHER_JOBS = "detail-boss-other-jobs";
    String ACTION_DETAIL_CERTIFICATE_CLICK = "action-detail-certificate-click"; //809.23 证书引导
    String ACTION_DETAIL_ACCEPT_FORIGNPOSITION = "userinfo-job-nonlocal-accept"; //809.23 证书引导
    String ACTION_DETAIL_ACCEPT_OTHERCIRTY_POSITION = "action-detail-popclick-generalworker"; //912 城市普工
    String SYSTEM_NEWGUIDE_CERTI_SHOW = "system-newguide-certificate-show"; //809.23 证书引导
    String SYSTEM_NEWGUIDE_ADD_FREIEND_BUBBLE = "system-newguide-addfriend-bubble";

    String ACTION_JOB_DETAIL_BUBBLE_SHOW = "job_detail_bubble_show"; //809.23 证书引导
    String ACTION_JOB_DETAIL_BUBBLE_CLICK = "job_detail_bubble_click";
    String DETAIL_BOSS_CARD_CLICK = "detail-boss-card-click";

    //7.17 首善发布职位页面填写描述时，使用联想功能
    String ACTION_JOB_DESC_ASSOCIATE = "job-desc-associate";

    String ACTION_ZHS_NOJOB_MODIFY = "zhs-nojob-modify";
    String ACTION_ZHS_CONFIRM_MODIFY = "zhs-confirm-modify";

    //7.18 工作经历填写页面触发行业推荐提示
    String ACTION_INDUSTRY_SUGGEST_WORK = "industry-suggest-work";
    //7.18 期望填写页面触发行业推荐提示
    String ACTION_INDUSTRY_SUGGEST_EXPECT = "industry-suggest-expect";
    /**
     * 718招聘反馈闭环埋点
     */
    //boss在F2点击进度列表标签
    String ACTION_BOSS_CLICK_SCHEDULE = "boss-click-schedule";
    //boss点击“近七天招聘进度”
    String ACTION_BOSS_CLICK_SCHEDULE_BAR = "boss-click-schedule-bar";
    //boss点击牛人卡片标记进度
    String ACTION_BOSS_MARK_SCHEDULE = "boss-mark-schedule";
    //boss进度列表引导弹窗-关闭
    String ACTION_BOSS_SCHEDULE_POP_SHUT = "boss-schedule-pop-shut";
    //boss进度卡片上点击查看牛人详情
    String ACTION_SCHEDULE_DETAIL_GEEK = "schedule-detail-geek";

    /**
     * 718求职反馈闭环埋点
     */
    //牛人在F2点击进度列表标签
    String ACTION_GEEK_CLICK_SCHEDULE = "geek-click-schedule";
    //牛人点击职位卡片标记进度
    String ACTION_GEEK_MARK_SCHEDULE = "geek-mark-schedule";
    //牛人进度列表引导弹窗-点击以上暂无进展
    String ACTION_GEEK_SCHEDULE_POP_BUTTON = "geek-schedule-pop-button";
    //牛人进度列表引导弹窗-关闭
    String ACTION_GEEK_SCHEDULE_POP_SHUT = "geek-schedule-pop-shut";
    //牛人进度卡片上点击查看职位详情
    String ACTION_SCHEDULE_DETAIL_BOSS = "schedule-detail-boss";

    /**
     * 718公司优化
     */
    //牛人在F4推荐行业公司列表点击筛选
    String ACTION_COMPANY_SCREEN_CLICK = "company-screen-click";

    //718 截图分享埋点
    String ACTION_SCREEN_SHOT_SHARE = "screenshot-option-click";
    String ACTION_SCREEN_SHOT_ACTION = "screenshot-option";

    String ACTION_ADD_JOB_CLICK_POSITION = "add-job-click-position";
    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15975
     * 牛人触发欢迎页面
     */
    String ACTION_WELCOME_GEEK = "welcome-geek";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15976
     * 牛人在欢迎页面点击下一步 /有无经历（1103）
     */
    String ACTION_WELCOME_GEEK_CLICK = "welcome-geek-click";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=15977
     * 牛人在欢迎页面点击跳过
     */
    String ACTION_WELCOME_GEEK_CLOSE = "welcome-geek-close";

    /**
     * 1017.061
     * 牛人在欢迎页面【接下来，可更新你的求职状态】点击下一步 （1103改为 「想找什么工作？」）
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=49834
     */
    String ACTION_WELCOME_GEEK_APPLY_CLICK = "welcome-geek-apply-click";

    /**
     * 11.03.14
     * 牛人在欢迎页面【添加新的工作经历-详情页面】的按钮点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52632
     */
    String ACTION_WELCOME_GEEK_ADD_WORK_CLICK = "welcome-geek-add-work-click";

    /**
     * 11.03.14
     * 牛人在欢迎页面【添加新的工作经历-选择页面】的按钮点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52633
     */
    String ACTION_WELCOME_GEEK_ADD_WORK_DETAIL_CLICK = "welcome-geek-add-work-detail-click";

    /**
     * 11.03.14
     * 牛人在欢迎页面【添加新的工作经历】曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=52665
     */
    String ACTION_WELCOME_GEEK_ADD_WORK_EXPO = "welcome-geek-add-work-expo";

    //719 降低驳回 https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2506
    String ACTION_JOB_REJECT_REPUBLISH = "job-reject-republish";
    //点击修改工作体验入职时间
    String ACTION_BRAND_WORK_TASTE_WILLUP = "brand-work-taste-willup";
    // 点击“去筛选”或“关闭”
    String ACTION_VIP4_F1_SEARCH_CLICK = "VIP4-F1-search-click";
    String ACTION_DETAIL_WORK_TASTE_PICUTER = "detail-work-taste-picuter";

    // 补充详细的期望工作方向F1动态引导出现
    String ACTION_EXPECT_DIRECTION_SUGGEST = "expect-direction-suggest";
    String ACTION_MASK_COMPANY_MANAGER = "mask-company-manage";

    String ACTION_MOBILE_EXCHANGE_GEEK_CALL = "mobile-exchange-geek-call";

    String ACTION_MOBILE_EXCHANGE_GEEK_EXCH = "mobile-exchange-geek-exch";

    String ACTION_MOBILE_EXCHANGE_OPEN = "mobile-exchange-open";

    String ACTION_MOBILE_CALL_OK = "mobile-call-ok";

    String ACTION_ZHS_ALTERNATE_QUESTION_LIST = "zhs-alternate-question-list";
    String ACTION_ZHS_HIGHFREQUENCY_QUESTION = "zhs-highfrequency-question";
    String ACTION_ZHS_CUSTOMER_SERVICE_CLICK = "zhs-customer-service-click";
    String ACTION_ZHS_JOB_RECOMMENDATION_CLICK = "zhs-job-recommendation-click";
    String ACTION_ZHS_QUEUE_END_PUSH = "zhs-queue-end-push";
    String ACTION_ZHS_SERVICE_ENTRANCE = "zhs-service-entrance";
    String ACTION_ZHS_CUSTOMER_SERVICE_PUSH = "zhs-customer-service-push";

    // 7.20 选择工作地址再精确
    String ACTION_SELECT_WORK_REPORT_JOB_ADDRESS_MAP = "job-address-map";

    String ACTION_F3_SETTING_CHECK = "f3-set-check"; // B端F3设置页面内点击各二级入口
    String ACTION_FULL_SCREEN_DISPLAY = "full-screen-display"; // 在视频半弹窗页面点击全屏播放
    String ACTION_TAP_ITEM_VIDEO = "tap-item-video"; // 点击道具详情页视频介绍

    //填写教育经历页面展示
    String ACTION_BOSS_PROFILE_EDU_SHOW = "boss-profile-edu-show";
    //填写工作经历页面展示
    String ACTION_BOSS_PROFILE_JOB_SHOW = "boss-profile-job-show";
    //职位详情页点击编辑职位进入编辑职位页面 (5.47)
    String ACTION_EDIT_JOB_INFO = "edit-job-info";
    //boss成功上传/修改个人主页教育经历
    String ACTION_BOSS_PROFILE_EDU = "boss-profile-edu";
    String ACTION_BOSS_PROFILE_job = "boss-profile-job";

    String ACTION_ASSESS_HUNTER_JOB = "assess-hunter-job";
    String ACTION_ASSESS_HUNTER_JOB_COMFIRM = "assess-hunter-job-comfirm";
    String ACTION_ASSESS_HUNTER_EVALUATION_CLICKEVALUATIONBUTTON = "extension-hunter-evaluation-clickevaluationbutton";

    //点击“更多价位”
    String ACTION_CLICK_MORE_PRICE = "click-more-price";
    // 引导牛人查看搜畅消息点击
    String ACTION_SEARCH_CHAT_CLICK = "search-chat-click";
    // 账号安全提醒弹窗的曝光
    // 账号安全提醒弹窗的点击
    // 账号管理-修改手机号的点击

    // 直播相关
    /**
     * 牛人在APP端F4页面点击校招直播入口
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2957&wikiCount=1&wikiLink=https%3A%2F%2Fwiki.kanzhun-inc.com%2Fpages%2Fviewpage.action%3FpageId%3D62817325
     */
    String ACTION_EXTENSION_CAMPUSLIVE_CF4_ENTER = "extension-campuslive-cf4-enter";

    String ACTION_F1_LIST_FILTER_CLICK = "action-list-f1-filter-click"; // 点击f1筛选中的“筛选”


    String ACTION_USERINFO_JOB_DESC_LOW_QUALITY_CITY = "userinfo-job-desc-lowqualityclick"; // 职位描述待完善-点击【任性提交】或【继续完善】
    String ACTION_USERINFO_JOB_COMMON_DESCTOAST = "userinfo-job-common-desctoast"; // 职位描述填写完毕点击保存，由于内容不合格出现诊断弹窗
    String ACTION_USERINFO_JOB_COMMON_DESCNUMSHOW = "userinfo-job-common-descnumshow"; // 在因字数不足而出现的弹窗内选择“返回编辑”

    String ACTION_JOB_ADD_NEW_COM_BRAND = "hunter-job-add-new-brand";//职位管理-选择企业-选择企业品牌
    String ACTION_JOB_BELONG_COM = "hunter-job-belong-com";//发布职位—选择当前代招企业—搜索的企业
    String ACTION_JOB_ADD_NEW_COM = "hunter-job-add-new-com";//职位管理-点击新增企业
    String ACTION_JOB_ADD_NEW_COM_NEXT = "hunter-job-add-new-com-next";//职位管理-新增企业下一步
    String ACTION_DETAIL_BOSS_QUESTION_MORE = "detail-boss-question-answer-more";


    String ACTION_EXTENSION_ZHS_TASKID_CLICK = "extension-zhs-taskid-click";

    String ACTION_LIFECYCLE_COMPLETE_EXPECT_ADD = "lifecycle-complete-expect-add"; // 牛人求职意向点击下一步（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62821656）
    String ACTION_LIFECYCLE_COMPLETE_EXPECT_STUDENT_ADD = "lifecycle-complete-expect-student-add"; // 学生牛人求职意向点击下一步（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62821656）
    String ACTION_LIST_GEEK_F1_CLOSE_FEEDBACK = "action-list-f1-nagreportclick";
    String ACTION_F1_DIALOG_ELDER_BLUE_RECOMMEND = "action-list-f1-olderpopshow";
    String ACTION_F1_DIALOG_ELDER_BLUE_CLICK = "action-list-f1-olderpopclick";
    String ACTION_F1_DIALOG_ELDER_BLUE_ADDED = "action-list-f1-oldertoastshow";

    String ACTION_LIST_F1_POPSCREEN = "action-list-f1-popcsreen";
    String ACTION_SYSTEM_SET_PRIVACY_CLICKADD = "system-set-privacy-clickadd";


    String ACTION_LIFECYCLE_COMPLETE_EXPECT_SUGUPDATE = "lifecycle-complete-expect-sugupdate"; // 职场人求职意向预填点击修改（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62821656）
    String ACTION_LIFECYCLE_COMPLETE_PHOTO_ADD = "lifecycle-complete-photo-add"; // 牛人头像点击下一步（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62821656）

    String LIFECYCLE_COMPLETE_DESC_ADD = "lifecycle-complete-desc-add"; //牛人个人优势点击（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62821656）
    String LIFECYCLE_COMPLETE_EDU_ADD = "lifecycle-complete-edu-add"; // 牛人教育经历点击
    String LIFECYCLE_COMPLETE_BASIC_INFO_ADD = "lifecycle-complete-info-add"; // 牛人基础信息点击
    String LIFECYCLE_COMPLETE_PROCESS_CLICK = "lifecycle-complete-process-click"; // 牛人注册完善过渡页面点击下一步
    String LIFECYCLE_COMPLETE_IDENTITY_ADD = "lifecycle-complete-identity-add"; // 牛人求职状态点击下一步
    String LIFECYCLE_COMPLETE_WORK_ADD = "lifecycle-complete-work-add"; // 牛人工作经历点击

    String SIGN_SELECTION = "sign-selection"; // 输入验证码后身份选择界面，选择牛人和Boss身份，或点击退出登录
    String ACTION_CHAT_DOUBLE_CHAT_HI = "action-chat-doublechat-hi";
    String ACTION_CHAT_DOUBLE_CHAT_CLICK = "action-chat-doublechat-click";
    String SUPPLY_CHAT_GUIDE_CLICK = "supply-chat-guide-click";
    String ACTION_BIZ_BLOCK_HOT_EXPOSURE = "biz-block-hot-exposureOpenJob";
    String ACTION_BIZ_BLOCK_HOT_JOB_CLICK = "biz-block-hot-exposureOpenJobClick";

    String ACTION_JOB_ASK_ANSWER_F1_ENTER = "userinfo-job-askanswer-f1-enter"; // 简问易答 boss在f1入口点击

    String ACTION_USER_INFO_MICRO_RESUME_PROJECT_UPDATE_INTO = "userinfo-microresume-project-updateinto"; // 牛人进入项目经历修改页面
    String ACTION_GET_GROUP_CHAT_SHOW = "get-group-chat-show"; // 课程训练营 群聊

    String ACTION_ANOMALY_WEB = "anomaly_web";
    /**
     * 804 职类预测埋点
     */
    String ACTION_USERINFO_JOB_COMMON_POSITIONCODE = "userinfo-job-common-positioncode";


    String ACTION_DETAIL_NOTE_MANAGE = "action-detail-note-manage";

    String ACTION_LIFECYCLE_COMPLETE_EXPECT_EDIT_JOB = "lifecycle-complete-expect-editjob"; // 从编辑期望页点击期望职位，进入选择职位的编辑页面
    String ACTION_LIFECYCLE_COMPLETE_EXPECT_CLICK_TAG = "lifecycle-complete-expect-clicktag"; // 修改/添加职位类型时点击推荐职位标签

    //发现feed 底部bottomTips
    String ACTION_GET_CARD_BOTTOM_TIPS_CLICK = "extension-get-feedmore-click";
    String ACTION_GET_CARD_BOTTOM_TIPS_EXPOSE = "extension-get-feedmore-expose";

    //    https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=3115&wikiCount=0&wikiLink=  话题详情
    String ACTION_GET_TOPIC_DETAIL_FOCUSCARD_CLICK = "extension-get-guidecard-click";
    String ACTION_GET_TOPIC_DETAIL_FOCUSCARD_EXPOSE = "extension-get-guidecard-expose";

    //F2消息列表抽屉点击
    String ACTION_MSG_DRAW_CLICK = "msg-draw-click";
    String ACTION_BOSS_MSG_DRAW_CLICK = "boss-msg-draw-click";
    String ACTION_MSG_DRAW_EXPOSE = "msg-draw-expose";
    String ACTION_BOSS_MSG_DRAW_EXPOSE = "boss-msg-draw-expose";

    //    https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=3117&wikiCount=0&wikiLink=
    String ACTION_GET_HOMEPAGE_SHOW = "extension-get-myhome-show";

    String ACTION_MAP_SWITCH_ADDRESS = "action-detail-profile-mapstartclick";

    //点击“添加高管”按钮“开始添加高管
    String ACTION_LIFECYCLE_COMPLETE_HOMEPAGE_BOSSADD = "lifecycle-complete-homepage-bossadd";
    //完成添加高管介绍，点击“保存”
    String ACTION_LIFECYCLE_COMPLETE_HOMEPAGE_BOSSSAVE = "lifecycle-complete-homepage-bosssave";
    //产品介绍、高管介绍页面点击“排序”按钮
    String ACTION_LIFECYCLE_COMPLETE_HOMEPAGE_ORDER = "lifecycle-complete-homepage-order";

    String ACTION_ACCEPT_MOBILE_CLICK = "action-accept-mobile-click";

    String ACTION_CHAT_POP_CLICK_MESSAGE = "action-chat-pop-clickmessagenotice";

    String ACTION_CHAT_POP_CLICK_WX_OPEN = "action-chat-pop-clickweixinopen";

    String ACTION_SYSTEM_NEW_GUIDE_CHAT = "system-newguide-chat-firstmessagenotice";

    String ACTION_SYSTEM_NEW_GUIDE_CHAT_OPEN = "system-newguide-chat-messagenoticeopened";
    String ACTION_NEW_GUIDE_NOTICE_SHOW = "system-newguide-chat-noticeshow";

    String ACTION_LIFECYCLE_COMPLETE_EXPECT_EDITPREFERENCE = "lifecycle-complete-expect-editpreference"; // 牛人进入偏好标签编辑页面

    String ACTION_ACTION_CHAT_DOUBLECHAT_ASSIST = "action-chat-doublechat-assistchatclick";

    String ACTION_GEEK_ADD_EDIT_CLICK = "geek-add-edit-click";

    String ACTION_ACTION_CHAT_DOUBLECHAT_PUSH = "action-chat-doublechat-assistchatpush";

    String ACTION_ACTION_BOSS_CLICK_OFFER = "boss-click-chat-offer";


    String ACTION_BIZ_BLOCK_HOT_CHOOSE_JOB = "biz-block-hot-ChoseJob"; // 806.40【商业】已关闭职位的开聊优化(PC 同步) 职位选择弹框点击关闭按钮
    String ACTION_BIZ_BLOCK_HOT_CHOOSE_JOB_CANCEL = "biz-block-hot-ChoseJobCancel";
    String ACTION_OPERATION_SECRETARY_PC_SCAN = "operation-secretary-pc-scan";
    String ACTION_LIFECYCLE_COMPLETE_INFO_GOTOCOMCLICK = "lifecycle-complete-info-gotocomclick"; // 简化流程牛人点击去完善
    String ACTION_LIFECYCLE_RESUME_VIDEO_DELETE = "lifecycle-resume-video-delete"; // 预览状态视频简历删除确认弹窗-点击删除
    String ACTION_LIFECYCLE_RESUME_VIDEO_EDITDONE = "lifecycle-resume-video-editdone"; // 视频简历剪辑完成
    String ACTION_LIFECYCLE_RESUME_VIDEO_PREVIEW = "lifecycle-resume-video-preview"; // 视频简历录制预览页-点击
    String ACTION_LIFECYCLE_RESUME_VIDEO_NOTEDONE = "lifecycle-resume-video-notesdone"; // 视频简历-编辑文稿页-点击“开始录制”
    String ACTION_LIFECYCLE_RESUME_VIDEO_BOSSDETAIL = "lifecycle-resume-video-bossdetail"; //boss查看视频简历

    String ACTION_CLIENT_EXPOSURE_ENTRANCE = "entrance-exposure";
    String ACTION_LIFECYCLE_COMPLETE_JOB_EDIT_PREFERENCE = "lifecycle-complete-job-editpreference"; // boss进入偏好标签编辑页面
    String ACTION_TOPIC_ITEM_CLICK = "action-list-worktaste-topicfeed"; //点击话题 进体验列表
    String ACTION_ADVANTAGE_SHOW_AI_ENTRANCE = "system-newguide-diagnosis-assistant"; //8.07进入我的优势触发智能助手
    String ACTION_ADVANTAGE_CLICK_AI_ADVANTAGE = "system-newguide-diagnosis-assistantclick"; //8.07我的优势中点击智能助手
    String ACTION_AI_ADVANTAGE_GENERATE_TEXT = "system-newguide-diagnosis-assistantcreate"; //8.07我的优势中使用智能助手点击生成文案
    String ACTION_EDIT_RESUME_SHOW_DIAGNOSE = "system-newguide-diagnosis-resumeshow"; //8.07微简历中简历诊断卡片曝光

    String ACTION_USERINFO_MICRORESUME_MAJOR_CLICK = "userinfo-microresume-major-click";
    String ACTION_USERINFO_MICRORESUME_MAJOR_ADD = "userinfo-microresume-major-add";

    String ACTION_BIZ_BLOCK_VIP_RECENTFILTER = "biz-block-vip-recentfilter"; //8.08提示VIP用户是否还原上次筛选的条件，顶端粉条呈现
    String ACTION_BIZ_BLOCK_VIP_RECENTFILTERCLICK = "biz-block-vip-recentfilterclick"; //8.08记录用户是否点击了顶端粉条。

    String ACTION_BIZ_BLOCK_REMIND_CLICK = "biz-block-remind-click";

    String ACTION_SEARCH_BRAND_CLICK = "action-search-brand-click"; //808 公司主页  搜索点击。
    String ACTION_WORK_EXP_PRACTICE_CARD_CLICK = "system-newguide-diagnosis-internshipclick";//808点击工作经历页疑似实习弹窗
    String ACTION_WORK_EXP_SHOW_SWITCH = "system-newguide-diagnosis-internshipnormal";//编辑/新增工作经历触发疑似实习常规入口
    String ACTION_WORK_EXP_SHOW_CARD = "system-newguide-diagnosis-internshipshow";//进入工作经历页弹出疑似实习弹窗

    /**
     * 809 f3页面职务审核未通过气泡展示
     */
    String ACTION_NOT_PASS_TYPE = "userinfo-position-cert-unpassbubbleshow";

    //810 关键词高亮需求埋点:
    String ACTION_USERINFO_JOB_KEYWORD_SUGGEST = "userinfo-job-keyword-suggest";//BOSS阅读牛人微简历，光标选择触发【高亮显示】引导条
    String ACTION_USERINFO_JOB_KEYWORD_WORDCLICK = "userinfo-job-keyword-wordclick";//BOSS阅读牛人微简历，点击高亮词
    String ACTION_USERINFO_JOB_KEYWORD_VIDEOSUGGEST = "userinfo-job-keyword-videosuggest";//用户首次使用「取消高亮」后出现引导视频弹窗
    String ACTION_USERINFO_JOB_KEYWORD_SELECT = "userinfo-job-keyword-select";//BOSS阅读牛人微简历，长按出现选择光标选择器

    String ACTION_DETAIL_PHONEASSISTANT_POPUPCLICK = "action-detail-phoneassistant-popupclick";


    String SYSTEM_BLOCK_VIP_CHAT_HELPER_CARD_CLICK = "biz-block-vip-ChatHelperCardClick";
    String ACTION_SYSTEM_NEW_GUIDE_DETAIL_PHONE_ASSIGN_PROMPT = "system-newguide-detail-phoneassistantprompt";

    // 810 get
    String GET_NOTICE_EXPOSE810 = "extension-get-inform-expose";
    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=4374
     * F1搜索-筛选项点击情况（810）
     */
    String ACTION_SEARCH_FILTER_CLICK = "action-search-filter-click";

    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=4524
     * 【工作环境】B端添加工作环境或视频【入口】展示810
     */
    String ACTION_SYSTEM_NEWGUIDE_JOBPUBLISH_PICVIDEOSHOW = "system-newguide-jobpublish-picvideoshow";


    String ACTION_USERINFO_JOB_SALARY_SALARYLIMITS = "userinfo-job-salary-salarylimits";
    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=4522
     * 【B薪资结构化】添加薪资范围详细内容（每填完一段内容后埋一次点）
     */
    String ACTION_USERINFO_USERINFO_JOB_SALARY_DETAILS = "userinfo-job-salary-details";

    String ACTION_USERINFO_JOB_BASIC_SALARY_TOAST = "userinfo-job-basicsalary-toast";

    String ACTION_JOB_ADD_JOB = "job-add-job";

    String ACTION_DISABILITY_JOBPUBLIC_CLICK = "disability-jobpublic-click";

    String ACTION_DISABILITY_JOBPUBLIC_SHOW = "disability-jobpublic-show";

    /**
     * 814
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5033
     * 奖金补贴标签展现的埋点
     */
    String ACTION_USERINFO_JOB_SALARYDETAIL_SUBSIDYLABLESHOW = "userinfo-job-salarydetail-subsidylableshow";

    /**
     * 814
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5030
     * 提成方式标签展现的埋点
     */
    String ACTION_USERINFO_JOB_SALARYDETAIL_PCTMODESHOW = "userinfo-job-salarydetail-pctmodeshow";

    /**
     * 814
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5034
     * 奖金补贴标签提交
     */
    String ACTION_USERINFO_JOB_SALARYDETAIL_SUBSIDYLABLECLICK = "userinfo-job-salarydetail-subsidylableclick";

    /**
     * 814
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5031
     * 提成方式标签提交
     */
    String ACTION_USERINFO_JOB_SALARYDETAIL_PCTMODECLICK = "userinfo-job-salarydetail-pctmodeclick";

    String ACTION_BIZ_ITEM_GEEK_VIP_CARD = "biz-item-geekvip-card"; // 订阅boss上线提醒后，记录提醒牛人设置夜间免打扰卡片曝光
    String ACTION_BIZ_ITEM_GEEK_VIP_CARD_CLICK = "biz-item-geekvip-cardclick"; // 订阅boss上线提醒后，记录提醒牛人设置夜间免打扰卡片点击行为
    String ACTION_SYSTEM_NEW_GUIDE_DETAIL_PIC_VIDEO_CLICK = "system-newguide-detail-picvideoclick"; // 点击环境照片/视频
    String ACTION_SYSTEM_NEW_GUIDE_DETAIL_PIC_VIDEO_SAVE = "system-newguide-detail-picvideosave"; // 牛人在职位详情页 ”长按保存“ 工作环境照片810
    String ACTION_EXCHANGE_CONTACT_CLICK = "action-exchange-contact-click";
    String ACTION_BIZ_ITEM_GEEK_VIP_CLICK = "biz-item-geekvip-click";
    //810 品牌信息编辑页面进入各类信息编辑页面
    String ACTION_USERINFO_BRAND_EDIT_ENTER = "userinfo-brand-edit-enter";
    //810 品牌信息编辑页面点击“预览”按钮
    String ACTION_USERINFO_BRAND_EDIT_HOMEPAGESCAN = "userinfo-brand-edit-homepagescan";

    String ACTION_USERINFO_WORKENVIROMENT_SHOW = "userinfo-job-workenvironment-show";

    String ACTION_COMPETIVE_JD_EXPOSURE = "biz-competitiveanalysis-exposure-SaleEntrance";

    String ACTION_MESSAGE810_CLICK = "extension-get-inform-click";

    String LIFECYCLE_COMPLETE_INFO_GOTOCOMSHOW = "lifecycle-complete-info-gotocomshow";


    String ACTION_EXCHANGE_CONTACT_CHOOSE = "action-exchange-contact-choose";

    String ACTION_ADDFRIEND_WELFARE_CHANGEORSEND = "action-addfriend-welfare-ChangeOrSend";

    String ACTION_ADDFRIEND_WELFARE_TOASTEXPOSE = "action-addfriend-welfare-ToastExpose";

    String ACTION_SYSTEM_SAFELY_RESUME_F1_CLICK = "system-safely-resume-f1click";
    String ACTION_SYSTEM_NEW_GUIDE_EDU_CLICK = "system-newguide-f1edu-click";
    String ACTION_SYSTEM_SAFELY_RESUME_F1 = "system-safely-resume-f1";
    String ACTION_SYSTEM_NEW_GUIDE_EDU = "system-newguide-f1edu-show";
    String ACTION_CHAT_NOSECPOUP_CLICK = "action-chat-nosecpoup-click";

    String ACTION_EXTENSION_ZHS_TASKID_ANSWERSUBMIT = "extension-zhs-taskid-answersubmit";

    /**
     * 【全部回复】-页面显示
     */
    String ACTION_GET_COMMENT_POP_SHOW = "extension-get-comment-popshow";

    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=4721
     * 职位发布成功页上（有相似职位tips的才上报）按钮点击
     */
    String ACTION_USERINFO_JOB_PUBSUC_CLICK = "userinfo-job-pubsuc-click";

    /**
     * 811: https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=4760
     * 公司列表tag切换: 切换后的列表：0:推荐公司 1:全部公司
     */
    String ACTION_USERINFO_BRAND_BRANDLIST_TAGSWITCH = "userinfo-brand-brandlist-tagswitch";

    String ACTION_OPERATION_NPS_QUESTION_SCREEN_CLICK = "operation-nps-question-screenclick";

    String ACTION_OPERATION_NPS_QUESTION_SCREEN_SHOW = "operation-nps-question-screenshow";

    String ACTION_OPERATION_NPS_QUESTION_SCREEN_SUBMIT = "operation-nps-question-screensubmit";

    /**
     * 811:点击F2本地职位抽屉
     */
    String ACTION_SYSTEM_MESSAGE_NOTICE_LOCALCLICK = "system-message-notice-localclick";
    String ACTION_BIZ_ITEM_SEARCH_SUGGEST = "biz-item-search-sug"; // 高搜联想词
    String ACTION_GET_CARD_CLICK_COMMENT = "extension-get-comment-click";
    String ACTION_ITEM_BATCH_DISTANCE = "biz-item-batch-distance";

    String ACTION_CHAT_VIRTUAL_PHONE_CALL = "action-chat-virtualphone-call";
    String ACTION_CHAT_VIRTUAL_PHONE_OPEN = "action-chat-virtualphone-open";
    String ACTION_DETAIL_REMIND_BUTTON_CLICK = "action-detail-RemindButton-click";// 牛人详情页增加动态信息引导&牛人详情页增加职位优惠提醒
    String ACTION_DETAIL_BUBBLE_CLICK = "action-detail-detailbubble-click"; // 增加F1预览入口跳转
    String ACTION_OPERATION_PUSH_SAFECARD_CLICK = "operation-push-safecard-click";

    /**
     * 8.13【我的在线简历】页面-【岗位经验】小红点展现
     */
    String ACTION_SHOW_EDIT_RESUME_POST_NEW_TAG = "lifecycle-resume-onliresume-lrbs";
    /**
     * 8.13【我的在线简历】页面-【岗位经验【+】】按钮点击
     */
    String ACTION_CLICK_EDIT_RESUME_POST_ADD = "lifecycle-resume-onliresume-postexpadd";
    /**
     * 8.13 简历预览页面曝光
     */
    String ACTION_SHOW_RESUME_PREVIEW_FRAGMENT = "lifecycle-resume-preview-expo";

    /**
     * 8.13 简历预览页面退出
     */
    String ACTION_RESUME_PREVIEW_STAY_TIME = "lifecycle-resume-preview-quit";
    /**
     * 8.13 C端F4【我的在线简历】小红点展现
     */
    String ACTION_GEEK_F4_SHOW_POST_RED_DOT = "operation-dynamicbar-f4-lrbs";
    /**
     * 8.14 【企业版公司主页】灰度更多点击
     */
    String ACTION_DETAIL_COM_GRAY_CLICK_MORE = "action-detail-com-grayclickmore";
    /**
     * 8.14 【企业版公司主页】灰度显示
     */
    String ACTION_DETAIL_COM_GRAY_SHOW = "action-detail-com-grayshow";
    /**
     * 8.14 用户会公司视频进行新增、修改、删除等操作
     */
    String ACTION_USERINFO_BRAND_EDIT_VIDEO = "userinfo-brand-edit-video";


    /**
     * 814 boss在微简历页面点击播放视频简历
     */
    String ACTION_LIFECYCLE_RESUME_VIDEO_DETAILTYPE = "lifecycle-resume-video-detailtype";
    String ACTION_BIZ_ITEM_TOP_CARD_EFFECT_CLICK = "biz-item-topcard-effectclick"; // 职位详情页-置顶状态条-查看效果点击
    String ACTION_USER_INFO_RESUME_CONTENTS_DURATION = "userinfo-microresume-contents-duration";
    String ACTION_BIZ_ITEM_JOB_DESC_RECRUIT_QUESTION_CLICK = "biz-item-jobDescription-recruitData-question-click";


    /**
     * 814 个人主页tab 匿名评价 按钮打点
     */
    String ACTION_BOSS_HOMEPAGE_EVALUATE_CLICK = "extension-hunter-evaluation-anonyassessclick";
    /**
     * 814 个人主页tab 匿名评价 显示
     */
    String ACTION_BOSS_HOMEPAGE_EVALUATE_SHOW = "extension-hunter-evaluation-anonyassessshow";
    /**
     * 职位详情页  工作体验点击
     */
    String ACTION_JOB_DETAIL_WORK_EXP_CLICK = "userinfo-brand-worktaste-viewmore";

    String ACTION_USERINFO_JOB_SALARY_DETAIL_SRC_CLICK = "userinfo-job-salarydetail-srcclick";
    String ACTION_USERINFO_JOB_EXTRAJOBCLASS_REPORTNEWCLASS = "userinfo-job-extrajobclass-reportnewclass";
    String ACTION_USERINFO_JOB_SALARYDETAIL_GUIDETIPCLICK = "userinfo-job-salarydetail-guidetipclick";
    String ACTION_USERINFO_JOB_SALARYDETAIL_GUIDETIPSHOW = "userinfo-job-salarydetail-guidetipshow";

    //双叶草埋点
    /**
     * 8.14 工作内容序号编辑按钮点击
     */
    String ACTION_WORK_CONTENT_ORDER_CLICK = "userinfo-microresume-work-orderclick";

    /**
     * 8.14 进入微简历的牛人姓名编辑页面
     */
    String ACTION_GEEK_EDIT_NAME_ENTER = "userinfo-microresume-personinfo-name";

    /**
     * 8.14 工作经历编辑页面内容引导黄条曝光
     */
    String ACTION_WORK_EXP_PAGE_WORK_CONTENT_YELLOW_BAR_SHOW = "userinfo-microresume-work-contentguide";

    /**
     * 8.14 工作经历页面点击工作内容
     */
    String ACTION_WORK_EXP_PAGE_WORK_CONTENT_CLICK = "userinfo-microresume-work-clickcontent";

    String ACTION_BIZ_ITEM_SUBSCRIBE_CLICK = "biz-item-subscribe-click";

    String ACTION_BIZ_ITEM_POPUP_SUBSCRIBE = "biz-item-popup-subscribe"; // 订阅牛人增强相关toast展示

    /**
     * 816 工作体验
     */
    String ACTION_WORK_EXP_SWITCH_CLICK = "userinfo-brand-worktaste-listswitch"; // 用户在工作体验详情列表切换列表
    String ACTION_BIZ_BLOCK_CLICK_TAB = "biz-block-click-tab"; // 增加了tab点击埋点和文案提示的曝光埋点

    /**
     * 817.9【用户产品】特征标签体系优化
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=87310327
     */
    String ACTION_LIFECYCLE_COMPLETE_EXPECT_PREFERCLICK = "lifecycle-complete-expect-preferclick"; // 特征标签F1卡片点击
    String ACTION_LIFECYCLE_COMPLETE_EXPECT_PREFERCNOSOW = "lifecycle-complete-expect-prefernoshow"; // 个性推荐设置关闭弹窗-点击最近不再展示
    String ACTION_LIFECYCLE_COMPLETE_EXPECT_PREFERQUESTIONCLICK = "lifecycle-complete-expect-preferquestionclick"; // 特征标签问题点击

    String ACTION_LIST_F1_REJECTMARKPOINT = "action-list-f1-rejectmarkpoint"; // 蓝领标签问题点击

    String ACTION_LIST_F1_REJECT = "action-list-f1-reject"; // 蓝领标签问题点击

    //816 在非匿名牛人详情页，沟通职位的浮层，记录选择哪个职位沟通
    String ACTION_EXTENSION_HUNTER_DETAIL_CHOOSE = "extension-hunter-detail-choose";

    //817 “周边城市招聘”商品被勾选（在触发页上，选择多价格的同时呈现时）
    String ACTION_BIZ_BLOCK_HOT_MULTICITIESPITCH = "biz-block-hot-MultiCitiesPitch";
    //817 “周边城市招聘”查看详情点击，点击后呈现出所有周边城市
    String ACTION_BIZ_BLOCK_HOT_MULTICITIESDETAILS = "biz-block-hot-MultiCitiesDetails";

    //817 用户点击B端F1底部的电话交换助手弹层
    String ACTION_SYSTEM_NEWGUIDE_F1_PHONEASSISTANTCLICK = "system-newguide-f1-phoneassistantclick";

    String ACTION_BIZ_ITEM_COUPON = "biz-item-action-coupon"; //优惠券弹窗点击
    String ACTION_BIZ_CLICK_CHAT_PHONE_EXCHANGE_GREY_BAR = "biz-block-click-ChatPhoneExchangeGreyBar"; //boss在聊天界面点击系统灰条上的按钮

    String ACTION_LIST_F3MESSAGE_EXPOSE = "action-list-f3message-expose";
    String ACTION_LIST_F1_SHORTLINESHOW = "action-list-f1-shortlineshow";
    String ACTION_BIZ_BLOCK_VIP4_MSG_CLICK = "biz-block-VIP4-MsgClick"; // 短信通知详情页上，点击“购买并使用”，用户每点一次就打一个点
    String ACTION_BIZ_BLOCK_VIP4_MSG_SELECT = "biz-block-VIP4-MsgSelect"; // 短信通知中选中VIP，用户每主动选中一次，就打一个点

    String ACTION_BIZ_BLOCK_VIP4_FILTER_CLICK = "biz-block-VIP4-FilterClick"; // 引导VIP4付费账号用户使用F1筛选的卡片被点击，用户每点一次就打一个点
    String ACTION_LIST_F1_SUGGEST_LIST = "action-list-f1-suggestlist"; // 点击CF推荐卡片进入列表

    String ACTION_VIDEOHI_QUICK_REPLY = "lifecycle-resume-videohi-quickreply";

    String ACTION_VIDEOHI_RECORDENTER = "lifecycle-resume-videohi-recordenter";

    String ACTION_VIDEOHI_RECORD_EXPO = "lifecycle-resume-videohi-recordexpo";

    //819 boss查看多视频时滑动切换视频
    String ACTION_LIFECYCLE_RESUME_VIDEO_SWITCH = "lifecycle-resume-video-switch";
    String ACTION_LIST_F1_ORDER = "action-list-f1-order";
    String ACTION_BIZ_ITEM_SEARCH_NOT_INTEREST = "biz-item-search-notinterest";


    //F1相似匿名牛人开聊场景引导弹窗点击（实验B组：开聊场景下触发，点击去看看）
    String ACTION_BIZ_ITEM_SEARCH_F1ANONYMOUSGEEK = "biz-item-search-F1AnonymousGeek";

    String ACTION_BIZ_RESUME_VIDEOHI_GUIDE_SHOW = "lifecycle-resume-videohi-guideshow";

    //818.59【商业】搜畅小版本搭售:用户进入阻断页面后，选择/取消选择搭售商品，记录该埋点。
    String ACTION_BIZ_BLOCK_CLICK_TIELN = "biz-block-click-Tieln";

    String ACTION_USERINFO_JOB_DETAIL_EDIT_STATUS_CARD = "userinfo-job-detailedit-statuscard";
    String ACTION_BIZ_BLOCK_PAY_FOR_ANOTHER_DATA_SYNC = "biz-block-ForAnother-DataSync"; // 职位详情页，数据同步开关点击后续
    String ACTION_BIZ_BLOCK_PAY_FOR_ANOTHER_CONFIRM_PAYMENT = "biz-block-PayForAnother-ConfirmPayment";// 确认支付页面，各支付方式点击转化
    String ACTION_BIZ_ITEM_TOPCARD_QUICKSEND = "biz-item-topcard-quicksend";
    String ACTION_LIST_F1_SUGGESTSLIDE = "action-list-f1-suggestslide";

    String ACTION_LIFECYCLE_RESUME_VIDEO_HI = "lifecycle-resume-videohi-myvideo";

    String ACTION_LIFECYCLE_RESUME_VIDEO_CREATE_MY_VIDEO = "lifecycle-resume-videohi-createmyvideo";

    //VIP阻断页面中，所有包含两个商品的时候（两个tab，切换展示商品）
    String ACTION_BIZ_BLOCK_CLICK_BLOCKTAB = "biz-block-click-BlockTab";
    //在VIP4权益提示弹层点击关闭或者知道了
    String ACTION_BIZ_BLOCK_CLICK_VIP4CONFIRM = "biz-block-click-VIP4Confirm";

    String ACTION_CHAT_DOUBLE_CHAT_FEEDBACK = "action-chat-doublechat-feedbackclick";

    String ACTION_CHAT_DOUBLE_CHAT_PUSH = "action-chat-doublechat-feedbackpush";

    //搜畅开聊后批量消耗引导弹窗——一键沟通按钮点击
    String ACTION_BIZ_ITEM_SEARCH_BATCH_CHAT = "biz-item-search-batchChat";

    String ACTION_SEARCH_CARD_OVERDUE = "biz-item-search-overdueBatchClick";//905用户开启/关闭搜畅临期批量消耗
    String ACTION_SETTING_VIP_PROP_MANAGE = "biz-item-manage-click";//905设置->道具功能管理点击

    String ACTION_BIZ_BLOCK_HOT_CROSSCITYDETAILCLICK = "biz-block-hot-CrossCityDetailClick";//906.43 阻断页面跨城市招聘条中“查看详情”点击
    String ACTION_BIZ_BLOCK_HOT_CROSSCITYBANNERCLICK = "biz-block-hot-CrossCityBannerClick";//906.43 阻断页面“跨城市招聘”商品条的勾选

    String ACTION_LIST_TO_OPEN_EXPO = "action-list-toopen-expo";

    String ACTION_BIZ_ITEM_SEARCH_CLICK = "biz-item-search-searchClick";

    String ACTION_QRCODE_PHOTO = "active-active-qrcode-photo";//扫码截图

    String ACTION_ADMIN_USER_AGREEMENT_SHOW = "admin-user-agreement-show";
    String ACTION_BIZ_BLOCK_EXPOSURE_OPENPAGEOPTIMIZE = "biz-block-exposure-OpenPageOptimize";//906.34付费职位发布成功页 招聘小助手 曝光记录

    String ACTION_NO_LIKE_SHOW = "chat-nonlike-show";

    String ACTION_NO_LIKE_CHANGE_CLICK = "chat-nonlike-change-click";
    String ACTION_TOURIST_EXPECT_INFO_SHOW = "tourist-expect-info-show"; // 牛人期望页面展示
    String ACTION_TOURIST_EXPECT_ADD = "tourist-expect-add"; //牛人点击确定，增加期望意向
    String ACTION_TOURIST_REG_EXP = "tourist-geek-rep-exp";
    String ACTION_TOURIST_SIGNIN_CLICK = "tourist-signin-click";

    String ACTION_BIZ_BLOCK_VIP_LOW_DEMAND_DISCOUNT_F1 = "biz-block-vip-lowDemandDiscountF1";
    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24483
     * 「本地服务」页面曝光
     */
    String ACTION_MINI_LOCAL_TAB = "mini-local-tab";

    /**
     * F3消息页通知bot详情曝光
     */
    String ACTION_F3_MESSAGE_NOTIFICATION_EXPO = "action-list-f3message-notificationdetailexpo";


    /**
     * 「本地服务」-「查看全部职位」按钮点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24502
     * tab：0（热门工作）；1（附近区县）
     */
    String ACTION_GET_LOCATION_SEE_MORE_JOB = "local-morejob-click";

    String ACTION_BIZ_ITEM_SEARCH_JOB_SELECT = "biz-item-search-jobSelect"; //908.41 搜畅职位版职位选择页面曝光

    String ACTION_LOCAL_COUNTY_CLICK = "local-county-click";
    String ACTION_LOCAL_HOTJOB_L2_CLICK = "local-hotjob-l2-click";
    String ACTION_LOCAL_DETAIL_BOSS = "local-detail-boss";
    String ACTION_SYSTEM_SAFELY_SECURITYPOP_CLICK = "system-safely-securitypop-click";
    String ACTION_SYSTEM_EDU_SECURITYPOP_CLICK = "system-edu-securitypop-click";

    //910.48 VIP触发的阻断页面中 “更多权益”按钮的点击
    String ACTION_BIZ_BLOCK_CLICK_VIP_PAGE_MORE_RIGHTS = "biz-block-click-VipPageMoreRights";
    //910.48 触发VIP账号阻断的 阻断页面中“更多权益”按钮的曝光
    String ACTION_BIZ_BLOCK_EXPOSURE_BLOCK_PAGE_MORE_RIGHTS = "biz-block-exposure-BlockPageMoreRights";


    String ACTION_ZP_TINKER_REPORT = "zp_tinker_report";
    String ACTION_ZP_TINKER_REPORT_INFO = "info";
    String ACTION_ZP_TINKER_REPORT_RECEIVE = "receive";
    String ACTION_ZP_TINKER_REPORT_CLEAN = "clean";
    String ACTION_ZP_TINKER_REPORT_ACTIVE_INFO = "active";
    String ACTION_ZP_TINKER_REPORT_TEMP = "temp";
    String ACTION_ZP_TINKER_REPORT_RETRY = "retry";
    String ACTION_ZP_TINKER_REPORT_CRASH_PROTECT = "protect";

    String ACTION_BIZ_ITEM_PURSUE_BOX_CLICK = "biz-item-pursue-boxClick";

    //记录boss在阻断页面/确认支付页的停留时长（单位：毫秒ms）
    String ACTION_BIZ_BLOCK_CLICK_TIME = "biz-block-click-time";
    //记录boss在直豆页/充值确认页的停留时长（单位：毫秒ms）
    String ACTION_BIZ_BLOCK_PAY_BEAN_STAY_TIME = "biz-block-bzb-beanStayTime";

    //910.99【商业】VIP2职位商品售卖调整
    String ACTION_BIZ_BLOCK_CLICK_MORE_BUSINESS_TYPE = "biz-block-click-moreBusinessType";

    String ACTION_WEB_ViEW_FIX = "web_view_fix";
    String TYPE_DELETE = "delete";
    String TYPE_OPEN = "open";

    String ACTION_BIZ_REUND_DRAW_EXPOSURE = "biz-refund-draw-exposure";
    String ACTION_BIZ_REUND_DRAW_DETAIL = "biz-refund-draw-detail";
    String ACTION_BIZ_REUND_DRAW_WAY = "biz-refund-draw-way";
    String ACTION_BIZ_REUND_DRAW_NOW = "biz-refund-draw-now";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=27076
     * APP‘我的’页面，banner曝光
     */
    String ACTION_EXTENSITON_CAMPUSLIVE_BANNERB_CREATESHOW = "extension-campuslive-bannerb-createshow";
    String ACTION_GEEK_GSS_F3_BANNER_EXPOSE = "geek-gss-f3banner-expose";

    //优惠说明中，解释说明icon的点击埋点
    String ACTION_BIZ_BLOCK_CLICK_EXPLAINDISCOUNT = "biz-block-click-explainDiscount";

    String ACTION_CHAT_COPY_WEIXIN = "action-chat-copy-weixin";

    //商品阻断详情页优惠活动过多时，“展示”按钮的曝光。
    String ACTION_BIZ_BLOCK_EXPOSURE_PAGESHOWMOREDISCOUNT = "biz-block-exposure-pageshowmorediscount";
    //当阻断详情页内优惠活动达到一定条件时候，记录用户是否点击“展开”按钮
    String ACTION_BIZ_BLOCK_CLICK_PAGESHOWMOREDISCOUNT = "biz-block-click-pageshowmorediscount";

    String ACTION_BIZ_REUND_CLICK_BEANREFRUND = "biz-refund-click-BeanRefund";


    String ACTION_RESUME_ATTACH_SHARE = "lifecycle-resume-attach-share";

    String ACTION_BIZ_REUND_EXPOSURE_DETAIL_PAGE = "biz-refund-exposure-DetailPage";
    String ACTION_GEEK_INTO_SHARE_POP = "action-geek-into-sharepop";

    /**
     * 914 点击更多选项，进入代付曝光
     * 老曝光点："biz-block-exposure-moreforAnother"
     */
    String ACTION_BIZ_PAY_PAGE_EXPOSURE_FORANOTHER = "biz-pay-page-exposure-forAnother";

    /**
     * 914 在确认支付页找人代付的曝光。
     * 老曝光点： "biz-block-exposure-forAnother"
     */
    String ACTION_BIZ_PAY_PAGE_EXPOSURE_CLIENT = "biz-pay-page-exposure-client";

    String ACTION_BIZ_ITEM_CLICK_COMPANY_SEARCH = "biz-item-click-companysearch";
    String ACTION_BIZ_ITEM_CLICK_COMPANY_MORE = "biz-item-click-companymore";

    String ACTION_BIZ_BLOCK_VIP4_PAY_PAGE_CLICK = "biz-block-VIP4-FFPageClick";
    String ACTION_CHAT_REJECT_WINDOWS_CLICK = "action-chat-reject-windowsclick";

    String ACTION_LIFECYCLE_FACE_CLICK_LAW = "lifecycle-certify-face-clicklaw";

    String ACTION_MATCHING_CLICK = "exclusive_matching_click";

    String ACTION_BIZ_ITEM_EXPOSURE_CARD_CLICK = "biz-item-exposurecard-exposureclick";

    String ACTION_BIZ_ITEM_SEARCH_MID_PAGE = "biz-item-search-midpage";
    String ACTION_DIRECT_CALL_FIRSTIME_POP_EXPOSURE = "direct-call-firstime-pop-exposure";

    //达成赠送列表开聊——开聊按钮点击（917.232）
    String ACTION_BIZ_ITEM_SEARCH_FREECHAT = "biz-item-search-freeChat";


    String ACTION_UPDATE_LOW_MEDIAN_EXPO = "update-low-median-windows-expo";

    String ACTION_CHAT_LIST_PUSH_OPEN_CLICK = "chat-list-pushopen-click";

    String ACTION_CHAT_LIST_PUSH_OPEN_EXPO = "chat-list-pushopen-expo";

    String ACTION_CHAT_DETAIL_PUSH_OPEN_CLICK = "chat-detail-pushopen-click";

    String ACTION_CHAT_DETAIL_PUSH_OPEN_EXPOSE = "chat-detail-pushopen-expo";


    String ACTION_ITEM_DIRECT_CALL_AUTHORIZE_POPUP = "biz-item-directcall-authorizepopup";

    String ACTION_ITEM_DIRECT_CALL_POUP_CLICK = "biz-item-directcall-popupclick";

    String ACTION_ITEM_DIRECT_CALL_AUTHORIZATION = "biz-item-directcall-authorization";

    String ACTION_BIZ_RECHARGE_BEAN_EXPOSURE = "biz-recharge-bean-exposure";

    String ACTION_BIZ_ITEM_DIRECT_CALL_NIGHT_POP = "biz-item-directcall-nightpopup";

    String ACTION_BIZ_ITEM_SEARCH_LISTCHAT = "biz-item-search-listChat";

    String ACTION_BIZ_BLOCK_VIP4_TRIGUSEGUIDE = "biz-block-VIP4-TrigUseguide";

    String ACTION_BIZ_ITEM_CHAT_CARD_CLICK = "biz-item-chatcard-click";

    String ACTION_BIZ_BLOCK_PAYFORANOTHER_SENDTOCOLLEAGE = "biz-block-PayForAnother-SendToColleage";

    String ACTION_BIZ_BLOCK_PAYFORANOTHER_SCANPAYMENTSUCCESS = "biz-block-PayForAnother-ScanPaymentSuccess";

    String ACTION_INSTRUCTON_IKNOW_ITEM_CLICK = "instruction-iknow-item-click";


    String ACTION_BLOCK_AND_FILTER_ITEM_CLICK = "block-and-filter-items-click";
    String ACTION_TOOLS_PRIVACY_POLICY_CLICK = "action-tools-privacy-policy-click";

    String ACTION_PERSONALIZED_RECOMMEND_ITEM_CLICK = "personalized-recommend-items-click";

    String ACTION_BLOCK_TYPE_OPEN_CLOSE_CLICK = "block-type-open-close-click";

    String ACTION_BOSS_OPEN_CLOSE_CLICK = "detail-boss-open-close-click";


    String ACTION_CHAT_DOUBLE_CHAT_LABEL_PUSH = "action-chat-doublechat-labelpush";

    String ACTION_CHAT_DOUBLE_CHAT_LABEL_CLICK = "action-chat-doublechat-lableclick";

    String ACTION_BIZ_ITEM_SEARCH_PRESET_WORD = "biz-item-search-PresetWord";
    String ACTION_BIZ_ITEM_SEARCH_RESULT_PAGE_RECOMMENDATION = "biz-item-search-ResultPageRecommendation";


    String ACTION_F2_SEARCH_HISTORY_CLICK = "f2-search-historyclick";

    String ACTION_MICRORESUME_DESC_DETAILALL_CLICK = "microresume-desc-detailall-click";
    String ACTION_MICRORESUME_DESC_DETAILALL_EXPO = "microresume-desc-detailall-expo";

    String ACTION_BIZ_ITEM_SUBSCRIBE_MSG_RMD_CLICK = "biz-item-subscribe-msgRmdClick";
    String ACTION_JOB_KEYWORD_CHECK_ALL = "job-keyword-check-all";


    String ACTION_BIZ_ITEM_SEARCH_INTENDJOBCLICK = "biz-item-search-intendJobClick";

    String ACTION_VIP4_F1_SEARCH_UPGRADE = "VIP4-F1-search-upgrade";
    String ACTION_BIZ_BLOCK_VIP4_FILTERTRIG = "biz-block-VIP4-FilterTrig";

    String ACTION_DISABLED_HELP_DETAIL_CLICK = "disabled-help-detail-click";

    String ACTION_DISABILITY_QUESTION_CLICK = "disability-question-click";

    /*记录商业道具曝光（客户端）*/
    String ACTION_BIZ_ITEM_EXPOSURE_CLIENT = "biz-item-exposure-client";

    String ACTION_BIZ_ITEM_SEARCH_DESIGN_CLICK = "biz-item-searchDesignWorks-click";


    String ACTION_EMOTION_COLLECTION_ADD_CLICK = "emoji-collect-add-click";


    String ACTION_EMOTION_TAB_CHANGE_CLICK = "emoji-tab-change-click";

    String ACTION_EMOTION_MENU_CLICK = "emoji-menu-click";

    String ACTION_BLOCK_GEEK_LIST_CLICK = "block-geek-list-click";

    String ACTION_BLOCK_BOSS_CLICK = "block-boss-click";

    String ACTION_BLOCK_BOSS_EXPO = "block-boss-expo";

    String ACTION_USER_NOTE_EDIT_SAVE = "user-note-edit-save";

    //下线
    String ACTION_USER_NOTE_EDIT_EXPO = "user-note-edit-expo";
    //
    String ACTION_BLOCK_CANCEL_CLICK = "block-boss-cancel-click";


    // 批量追聊页面-曝光
    String ACTION_GEEK_DOUBLECHAT_DETAIL_EXPO = "geek-doublechat-detail-expo";
    // 新招呼待处理页面-曝光
    String ACTION_GEEK_GREETINGS_DETAILS_EXPO = "geek-greetings-details-expo";

    String ACTION_BIZ_ITEM_SEARCH_SELECT_BY_DEFAULT = "biz-item-search-SelectedByDefault";

    //人才发展模块曝光
    String ACTION_BRAND_TALENT_DEV_SHOW = "brand-talent-dev-show";
    //人才发展编辑页面曝光
    String ACTION_BRAND_TALENT_DEV_PAGESHOW = "brand-talent-dev-pageshow";
    //人才发展编辑页标签点击
    String ACTION_BRAND_TALENT_DEV_BUTTON_CLICK = "brand-talent-dev-button-click";
    //品牌主页人才发展模块点击
    String ACTION_BRAND_TALENT_DEV_MODULE_CLICK = "brand-talent-dev-module-click";

    //1003.216【商业】阻断页面样式差异化 - 点击标题问号的icon
    String ACTION_BIZ_BLOCK_CLICK_TRIGGERWH = "biz-block-click-triggerwh";

    //优惠券弹窗点击
    String ACTION_BIZ_ITEM_ACTION_COUPON = "biz-item-action-coupon";
    String ACTION_UI = "action_ui";
    String ACTION_TOAST = "toast";
    //优惠券挽留弹窗app端曝光（1003.241）
    String ACTION_BIZ_ITEM_COUPON_DETENTION_APP_EXPOSURE = "biz-item-coupon-detention-app-exposure";

    String ACTION_DISABILITY_BOSS_CONSULT_DISAGREE = "disability-boss-consult-disagree";
    String ACTION_DISABILITY_BOSS_CONSULT_AGREE = "disability-boss-consult-agree";
    String ACTION_DISABILITY_BOSS_CONSULT_SHOW = "disability-boss-consult-show";

    String ACTION_JOB_MODIFY_RECOMMEND_CLICK = "job-modify-recommend-click";
    String ACTION_DISABLE_GEEK_CONSULT_AGREE = "disability-geek-consult-agree";

    String ACTION_DISABLE_GEEK_CONSULT_DISAGREE = "disability-geek-consult-disagree";

    String ACTION_LIST_GEEK_SEARCH_GALLERY_ITEM_CLICK = "list-geek-search-click";
    String ACTION_LIST_GEEK_SEARCH_GALLERY_ITEM_NEXT_PIC = "list-geek-search-nextpic";

    /*扫码相关埋点*/
    String ACTION_ZP_SCANNER = "action_zp_scanner";
    /*扫码成功*/
    String ACTION_ZP_SCANNER_SUCCESS = "scan_success";
    /*扫码结束*/
    String ACTION_ZP_SCANNER_FINISH = "scan_finish";

    /*搜索畅聊卡使用SecurityId为空排查*/
    String ACTION_ZP_LOCAL_USESEARCHCHATCARD = "ACTION_ZP_LOCAL_USESEARCHCHATCARD";

    String ACTION_RECENT_CHAT_HISTORY_HAVE_OR_NOT = "recent-month-chat-history-have-or-not";

    String ACTION_RECENT_CHAT_HISTORY_ENTER_CLICK = "recent-month-chat-history-entry-click";

    String ACTION_BLOCK_BOSS_MESSAGE_NOTIFY_DOUBLE_CHECK = "block-boss-message-notify-double-check";

    String ACTION_BLOCK_GEEK_MESSAGE_NOTIFY_DOUBLE_CHECK = "block-geek-message-notify-double-check";

    String ACTION_DISABILITY_INFO_CHANGE_CLICK = "disability-info-change-click";
    String ACTION_DISABILITY_JOB_DETAIL_CLICK = "disability-job-detail-click";

    String ACTION_BIZ_BLOCK_JOBLIST_EXPOSURE = "biz-block-joblist-exposure";

    //1004.216【商业】畅聊版2个月商品+基础版6个月商品 商品切换
    String ACTION_BIZ_BLOCK_HOT_CHANGEPRODUCT = "biz-block-hot-changeproduct";


    String ACTION_TEMPORARY_SHOW_MESSAGE_CARD_EXPO = "temporary-show-message-card-expo";

    String ACTION_BLOCK_BOSS_MSG_NOTIFY_EXPO = "block-boss-message-notify-expo";
    String ACTION_BLOCK_BOSS_MSG_NOTIFY_EXPO_CANCEL_OR_NOT = "block-boss-message-notify-cancel-or-not";
    String ACTION_BIZ_ITEM_SEARCH_MIDDLE_PAGE_JOB_SELECT = "biz-item-search-MiddlePageJobSelect";

    String ACTION_BIZ_ITEM_SEARCH_ACTIVATION = "biz-item-search-activation";

    String ACTION_BIZ_ITEM_SEARCH_SELECTED_POSITION = "biz-item-search-SelectedPosition";

    String ACTION_LOG_IN_CALENDAR_ACCESS_OR_NOT = "log-in-calendar-access-or-not";

    //畅聊版开聊(沟通)达到上限的场景下，售卖道具页面点击情况
    String ACTION_USER_BLOCK_BIZ_ITEM_CLICK = "user-block-biz-item-click";

    //1005.267【商业】服务业畅聊职位-开聊权益消耗优化
    String ACTION_BIZ_BLOCK_EXPOSURE_BATCHCHAT = "biz-block-exposure-batchchat";

    String ACTION_BIZ_ITEM_MY_BEAN_EXPOSURE = "biz-item-mybean-exposure";
    String ACTION_BIZ_ITEM_MY_BEAN_CLICK = "biz-item-mybean-click";

    String ACTION_LIFECYCLE_CERTIFY_UNCHANGE_POPUP_CLICK = "lifecycle-certify-unchange-popup-click";

    String ACTION_LIFECYCLE_CERTIFY_UNCHANGE_POPUP_SHOW = "lifecycle-certify-unchange-popup-show";

    String ACTION_LIFECYCLE_CERTIFY_FAIL_POPUP_CLICK = "lifecycle-certify-fail-popup-click";

    String ACTION_LIFECYCLE_CERTIFY_FAIL_POPUP_SHOW = "lifecycle-certify-fail-popup-show";

    String ACTION_ZHS_GUESS_QUESTION_SWITCH = "zhs-guess-question-switch";


    String ACTION_ZHS_GET_MORE_MAKE_CALL = "zhs-phone-make-call";


    String ACTION_NA_ZHS_ROBOT_ANSWER_GUIDE_JUMP_WXCLICK = "na-zhs-robot-answer-guide-jump-wxclk";

    String ACTION_ZHS_SERVICE_UNAPPRAISE = "zhs-service-unappraise";

    String ACTION_F2_MESSAGE_FILTER_ACTION = "f2-message-filter-action";

    String ACTION_QUICK_REPLY_MANAGE_ACTION = "quick-reply-manage-action";

    String ACTION_QUICK_REPLY_EDIT_PAGE_ACTION = "quick-reply-editpageaction";

    String ACTION_QUICK_REPLY_PAGE_ACTION = "quick-reply-page-action";

    /**
     * 1006.070 相似企业板块落地页曝光
     */
    String ACTION_SIMILAR_INDUSTRY_COMPANY_EXPOSE = "similar-industry-company-expose";

    /**
     * 1006.070 相似企业主页 公司/岗位查看
     */
    String ACTION_SIMILAR_INDUSTRY_DETAIL_LIST = "similar-industry-detail-list";

    /**
     * 1006.070 相似企业切换【岗位】，落地页默认【公司】
     */
    String ACTION_SIMILAR_INDUSTRY_JOB_SWITCH = "similar-industry-job-switch";

    String ACTION_QUICK_REPLY = "quick-reply";
    String ACTION_BIZ_BLOCK_F1_FILTER_CLICK = "biz-block-f1filter-click";
    String ACTION_BIZ_BLOCK_F1_FILTER_EXPOSURE = "biz-block-f1filter-exposure";

    //黄条节约成本的曝光，1006.280【商业】尝试「账号到职位」用户往「账号到账号」拉伸
    String ACTION_BIZ_BLOCK_COSTSAVE_EXPOSURE = "biz-block-costsave-exposure";

    //点击【联系我】-弹窗曝光
    String ACTION_BOSS_PASSIVE_CALL_EXPOSURE = "boss-passive-call-exposure";
    //点击【联系我】-弹窗内点击"x"关闭弹窗按钮
    String ACTION_BOSS_PASSIVE_CALL_CANCEL = "boss-passive-call-cancel";

    String ACTION_BRAND_INTRO_PAGE_SHOW = "brand-intro-page-show";

    String ACTION_BRAND_INTRO_PAGE_MODULE_CLICK = "brand-intro-page-module-click";

    String ACTION_BRAND_INTRO_PAGE_MODULE_SAVE = "brand-intro-page-module-save";

    String ACTION_BRAND_INTRO_PAGE_MODULE_BACK = "brand-intro-page-module-back";

    String ACTION_DIRECT_PHONE_BACKUP_EXP = "direct_phone_backup_exp";
    String ACTION_DIRECT_PHONE_BACKUP_CLICK = "direct_phone_backup_click";
    String ACTION_RESUME_SEND_MANAGER_CLICK = "resume-send-manage-click";

    String ACTION_RESUME_SEND_PREVIEW_CLICK = "resume-send-preview-click";

    String ACTION_MSG_DRAW_DELETE = "msg-draw-delete";

    String ACTION_AXB_EVALUATE_CARD_CLOSE = "axb-evalue-card-close";

    //卡片曝光 1007.275【商业】背调增加在线简历触发入口
    String ACTION_BIZ_ITEM_BACKGROUND_ONLINERESUME = "biz-item-background-onlineresume";

    String ACTION_BIZ_ITEM_SEARCH_EXPOSE_LIFT = "biz-item-search-exposeleft";

    //牛人详情页，设置无障碍职位弹窗曝光
    String ACTION_DISABILITY_DETAILGEEK_POP_EXPOSE = "disability-detailgeek-pop-expose";
    //牛人详情页，设置无障碍职位弹窗点击
    String ACTION_DISABILITY_DETAILGEEK_POP_CLICK = "disability-detailgeek-pop-click";

    String ACTION_BIZ_ITEM_IMPORTANT_NOTIFY_CLOSE = "biz-item-importantnotify-closed";

    String ACTION_USER_INFO_PROFILE_DEVICE_CANCEL_DELETE = "userinfo-profile-device-cancelDelete";

    String ACTION_USER_INFO_PROFILE_DEVICE_CONFIRM_DELETE = "userinfo-profile-device-comfirmDelete";

    String ACTION_USER_INFO_PROFILE_DEVICE_DELETE_CLICK = "userinfo-profile-device-deleteClick";

    String ACTION_BRAND_MODIFY_CERTIFY_EXPOSURE = "brand-modify-certify-exposure";
    String ACTION_BRAND_MODIFY_CERTIFY_CLICK = "brand-modify-certify-click";

    String ACTION_BIZ_ITEM_SEARCH_LEFT_CLICK = "biz-item-search-leftclick";

    String ACTION_BOSS_REQUIRE_CONSULT_DETAIL = "boss-require-consult-detail";
    String ACTION_CLOCK_VIP4_BATCHCHATBAREXPO = "biz-block-VIP4-BatchChatBarRCExpo";

    String ACTION_CHAT_RESUME_CHOOSE = "chat-resume-choose";

    String ACTION_BRAND_PHRASE_SHOW = "brand-phrase-show";

    //1001.802 点击卡片进入牛人在线详情后，点击「继续沟通」
    String ACTION_LIVE_CHAT_CONTINUE = "live-chat-continue";

    String ACTION_CHOOSE_CHAT_INTRODUCE_TEMPLETE_CLICK = "chase_chat_introduce_templete_click";

    String ACTION_CHOOSE_CHAT_INTRODUCE_LABEL_CLICK = "chase_chat_introduce_label_click";

    String ACTION_CHOOSE_CHAT_INTRODUCE_CARD_CLICK = "chase_chat_introduce_card_click";

    String ACTION_CHASE_CHATCARD_CLICK = "chase_chatcard_click";

    String ACTION_CHASE_CARD_EXPOSE = "chase_chatcard_expose";


    String ACTION_GET_LABEL_HP_LIST_EXPOSE = "industryhomepage-lbiz-block-bzb-beanBzbClickabelpage-expose";//标签主页  TAB列表曝光


    String ACTION_DISABILITY_GEEK_SPECIAL_CLOSE_REASON = "disability-geek-special-closereason";

    String ACTION_DISABILITY_GEEK_JOBCOMP_CLICK = "disability-geek-jobcomp-click";
    String ACTION_DISABILITY_GEEK_JOBCOMP_SHOW = "disability-geek-jobcomp-show";

    //1009.301  发布按钮气泡曝光
    String ACTION_GET_EXPLORE_PUBLISH_BUBBLE = "get-explore-publish-bubble";

    String ACTION_GEEK_CERTIFICATION_COMPLETE_CLOSE = "geek-certification-complete-close";

    String ACTION_F2_MESSAGE_FILTER_PAGE_SHOW = "f2-message-filter-page-show";

    String ACTION_CHAT_SET_CHAT_CLICK_SET_CHAT_CLICK = "action-chat-setchat-clicksetchat-click";

    String ACTION_CHAT_SET_CHAT_CLICK_SET_CHAT_EXPO = "action-chat-setchat-clicksetchat-expo";
    String ACTION_CHAT_DETAIL_CLICK = "dialog-detail-click";


    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54292
     * Boss在聊天详情页点击收藏/取消收藏
     */
    String ACTION_CHAT_CLICK_INTEREST = "chat-click-interest";

    /*----------------------面试相关埋点 start------------------------*/

    String ACTION_CHAT_INTERVIEW_CLICK = "chat-interview-click"; // 聊天点击面试TA
    String ACTION_INTERVIEW_AREA_CLICK = "list-geek-interview"; // 718 B端F3一级页面点击「面试」，详情：https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2440
    String ACTION_DETAIL_TRANSFER_INTERVIEW = "detail-transfer-interview";
    String ACTION_VIDEO_INTERVIEW_APPLY_OK = "video-interview-apply-ok";
    String ACTION_INTERVIEW_APPLY = "video-interview-apply";
    String ACTION_DETAIL_MORE_INTERVIEW = "detail-more-interview";//F1面试卡片牛人点击查看更多
    String ACTION_VOICE_INTERVIEW_GUIDE_START = "voice-interview-guide-start"; // 【语音面试】引导面板上点击发起呼叫按钮 （https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=41486063）
    String ACTION_VIDEO_INTERVIEW_GUIDE_START = "video-interview-guide-start"; // 【视频面试】引导面板上点击发起呼叫按钮 （https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=41486063）
    String ACTION_VOICE_INTERVIEW_GUIDE = "voice-interview-guide"; // 主叫方第一次使用【语音面试】，发起呼叫时进入引导面板（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=41486063）
    String ACTION_VIDEO_INTERVIEW_GUIDE = "video-interview-guide"; // 主叫方第一次使用【视频面试】，发起呼叫时进入引导面板 （https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=41486063）
    String ACTION_CHAT_INTERVIEW_CARD_SHOW = "chat-interview-card-show"; //房卡请求展示
    String ACTION_CHAT_INTERVIEW_CARD = "chat-interview-card"; //点击房卡
    String ACTION_INTERVIEW_NEAR_DONE = "interview-near-done"; // 牛人提交面试附近地址
    String ACTION_INTERVIEW_TYPE_CHOOSE = "interview-type-choose";
    //F1列表面试爽约小黄条出现
    String ACTION_SYSTEM_NEWGUIDE_F1_INTERVIEW = "system-newguide-f1-interview";
    //F1列表面试爽约小黄条点击查看
    String ACTION_SYSTEM_NEWGUIDE_F1_INTERVIEWDETAIL = "system-newguide-f1-interviewdetail";
    /**
     * 820
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=22320
     * boss点击复制视频面试间链接
     */
    String ACTION_INTERVIEW_ROOM_COPYLINK = "action-interview-room-copylink";

    String ACTION_INTERVIEW_SET_CLICK = "action-interview-set-click";//906 进行面试设置
    String ACTION_INTERVIEW_SET_TYPE = "action-interview-set-type";
    /**
     * 1005.46 曝光引导弹窗
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=34090
     */
    String ACTION_INTERVIEW_EXPERIENCE_GUIDE_EXPO = "action-interview-experience-guide-expo";
    /**
     * 1005.46 引导弹窗的点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=34092
     */
    String ACTION_INTERVIEW_EXPERIENCE_GUIDE_CLICK = "action-interview-experience-guide-click";
    String ACTION_CHAT_INTERVIEW_BAR = "chat-interview-bar";
    /**
     * 1019.40 手动添加面试详情页曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=50913
     */
    String ACTION_INTERVIEW_GEEK_SELF_ADD_DETAIL_EXPO = "action-interview-add-manual-expo";

    /**
     * 1105.41 牛人F3点击面试
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54442
     */
    String ACTION_INTERVIEW_F3_CLICK = "action-interview-f3-click";

    /*----------------------面试相关埋点 end------------------------*/

    /**
     * 1107.052 安心保职位的头像点击埋点
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=14335
     */
    String ACTION_JD_HEAD_CLICK = "detail-headimg";

    /**
     * 1106.3 职位详情页JD引导跳转资格证书页保存
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54795
     */
    String ACTION_JD_CERTI_SAVE = "JD-certificate-suggest-listsave";

    /**
     * 1106.3 职位详情页JD引导跳转资格证书页曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54794
     */
    String ACTION_JD_CERT_EXPO = "JD-certificate-suggest-list";

    /*-------------群聊埋点 start----------------*/

    /**
     * 1109.51 群聊内所有职位按钮点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60700
     */
    String ACTION_GROUP_CHAT_ALL_JOB_CLICK = "group_all_job_click";

    /**
     * 1109.51 群聊内所有职位按钮曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60699
     */
    String ACTION_GROUP_CHAT_ALL_JOB_SHOW = "group_all_job_show";

    /**
     * 1109.51 F2群聊点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60698
     */
    String ACTION_GROUP_CHAT_F2_CLICK = "f2_group_click";

    /**
     * 1109.51 群聊职位置顶取消
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60696
     */
    String ACTION_GROUP_CHAT_JOB_TOP_CANCEL = "group_job_top_concel";

    /**
     * 1109.51 群置顶职位点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60695
     */
    String ACTION_GROUP_CHAT_JOB_TOP_CLICK = "group_top_job_click";
    /**
     * 1109.51 群置顶职位曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60694
     */
    String ACTION_GROUP_CHAT_JOB_TOP_SHOW = "group_top_job_show";
    /**
     * 1109.51 将群职位置顶
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60693
     */
    String ACTION_GROUP_CHAT_JOB_TOP = "group_job_top";

    /**
     * 1109.51 群公告条点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60692
     */
    String ACTION_GROUP_CHAT_PROCLAIM_STRIP_CLICK = "group_proclaim_strip_click";

    /**
     * 1109.51 群公告条曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60691
     */
    String ACTION_GROUP_CHAT_PROCLAIM_STRIP_SHOW = "group_proclaim_strip_show";

    /**
     * 1109.51 app群内发布公告时点击【确定】
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=60690
     */
    String ACTION_GROUP_CHAT_ISSUE_PROCLAIM_CLICK = "group_issue_proclaim_click";

    /*-------------群聊埋点 end----------------*/

    /**
     * 1115.607 期望管理页展示「关闭“隐藏简历“」按钮
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=30748
     */
    String ACTION_EXP_LIST_RESUME = "exp-list-resume";

    //点击“更多”。1010.260【商业】
    String ACTION_BIZ_ITEM_KEY_CLICK = "biz-item-key-click";
    String ACTION_BIZ_BLOCK_VIP_CLICKMORE = "biz-block-vip-clickmore";
    String ACTION_BIZ_BLOCK_IDENTITY_EXPOSURE = "biz-block-identity-exposure";
    String ACTION_BIZ_BLOCK_IDENTITY_BUTTON_CLICK = "biz-block-identity-buttonClick";


    String ACTION_ADMIN_USER_SAFETY_EXCHANGE_POPUPCLICK = "admin-user-safety-exchangePopUpClick";

    String ACTION_ADMIN_USER_SAFETY_EXCHANGE_EXCHANGEPOPUP = "admin-user-safety-exchangePopUp";

    String ACTION_ADMIN_USER_SAFETY_POPUPCLICK = "admin-user-safety-popUpClick";

    String ACTION_ADMIN_USER_SAFETY_POPUP = "admin-user-safety-popUp";

    String ACTION_BIZ_BLOCK_ONLINE_EXPOSURE = "biz-block-online-exposure";


    String ACTION_QUICK_PROCESS_JOB_CHOOSE = "quick-process-job_choose";

    String ACTION_QUICK_PROCESS_JOB_CLICK = "quick-process-job-click";

    String ACTION_QUICK_PROCESS_BATCH_CLICK = "quick-process-batch-click";

    String ACTION_QUICK_PROCESS_BATCH_ENTER = "quick-process-batch-enter";

    String ACTION_F3_BOTTOM_LAYER_EXPO = "action_titan_f3bottom_layerexpo";

    String ACTION_BIZ_BLOCK_BZB_ACCOUNT_LEFT_EXPOSURE = "biz-block-bzb-accountLeftExposure";

    String ACTION_ADMIN_USER_SAFETY_WARNING_CLICK = "admin-user-safety-warningClick";
    String ACTION_ADMIN_USER_SAFETY_WARNING_POPUP = "admin-user-safety-warningPopUp";

    String ACTION_ADMIN_USER_SAFETY_REMINDER_CLICK = "admin-user-safety-reminderClick";
    String ACTION_ADMIN_USER_SAFETY_REMINDER_POPUP = "admin-user-safety-reminderPopUp";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47320
     */
    String ACTION_JOB_HUNTING_STATUS_EXPO = "job-hunting-status-expo";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47321
     */
    String ACTION_JOB_HUNTING_STATUS_UPDATE = "job-hunting-status-update";

    String ACTION_BOSS_RECRUITMENT_PROGRESS = "boss-recruitment-progress";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47922
     */
    String ACTION_INDUSTRY_DISCUSS_PAGE_CLICK = "industry-discuss-page-click";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47920
     */
    String ACTION_INDUSTRY_DISCUSS_PAGE_EXPOSE = "indusry-discuss-page-expose";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47918
     */
    String ACTION_INDUSTRY_VOTE_PUBLISH_CLICK = "industry-vote-publish-click";

    /**
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=47910
     */
    String ACTION_INDUSTRY_DISCUSS_VOTE_CLICK = "industry-discuss-vote-click";

    String ACTION_EXTENSION_HUNTER_ONLINE_NO_GEEK = "extension-hunter-onlinebd-nogeek";
    String ACTION_EXTENSION_HUNTER_ONLINE_FIND_HUNTER = "extension-hunter-onlinebd-findhunter";


    String ACTION_CHASE_CHAT_CLOSE_CARD_CLICK = "chase_chat_close_card_click";
    String ACTION_CHASE_CHAT_CLOSE_CARD_EXPOSE = "chase_chat_close_card_expose";

    String ACTION_BRAND_EDITOR_CLICK_BRAND_INDEPENDENCE = "brand-editor-click-brand-independence";

    String ACTION_BIZ_ITEM_GEEKCOMPETIVE_POPCLICK = "biz-item-geekcompetitive-UesingPopClick";

    String ACTION_BIZ_ITEM_EXPOSURE_USEENTRANCE = "biz-item-exposure-UseEntrance";


    String ACTION_TITAN_STUDY_SERVICE_DROP_DOWN = "action_titan_studyservice_dropdownclick";

    String ACTION_BRAND_ADD_NEW_COM_ABBREVIATION = "brand-add-new-company-abbreviation";

    String ACTION_INTERVIEW_CARD_F1_CARD_CLICK = "action-interview-card-f1card-click";
    String ACTION_BIZ_BLOCK_F1_CARD_CHAT_CLICK = "biz-block-click-f1ChatCard";

    String ACTION_FEEDBACK_DETAIL_HIDE_CANCEL = "detail-hide-cancel";
    String ACTION_FEEDBACK_DETAIL_HIDE_CLICK = "detail-hide-click";
    String ACTION_FEEDBACK_DETAIL_HIDE_SHOW = "detail-hide-show";

    String ACTION_EXTENSION_HUNTER_LIGO_REPLY = "extension-hunter-ligo-reply";

    String ACTION_EXTENSION_HUNTER_LIGO_ENTER = "extension-hunter-ligo-enter";

    String ACTION_CHAT_PIN_CLICK = "chat-pin-click";

    String ACTION_LIST_GEEK_CONN_CLICK = "listgeek-conn-click";
    String ACTION_LIST_GEEK_CONN_JOB_DATA_CLICK = "listgeek-conn-jobdata-click";
    String ACTION_LIST_GEEK_CONN_FILTER_CLICK = "listgeek-conn-filter-click";
    String ACTION_LIST_GEEK_CONN_SEARCH_BOX_RECOMMEND_CLICK = "listgeek-conn-search-box-recommend-click";

    String ACTION_CHAT_VOICE_SEND_SUCCESS = "chat-voice-send-success";

    String ACTION_CHAT_VOICE_MODIFY_ACTION = "chat-voice-modify-action";

    String ACTION_VOICE_SEND_CLICK = "chat-voice-send-click";

    /**
     * 1020.89 发送语音 -选择取消
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=51631
     */
    String ACTION_CHAT_VOICE_CANCEL = "chat-voice-cancel";

    /**
     * 1017.88 b&c 在F2点击联系人，分筛选栏
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=49470
     */
    String ACTION_CHAT_CONTACT_CLICK = "f2-filter-detail-dialog";

    String ACTION_SYSTEM_MESSAGE_SAFE_YELLOW_STRIP_FREEZE = "system-message-safety-YellowStripFreeze";

    String ACTION_BIZ_BLOCK_URGENT_REFUND = "biz-block-urgent-refund";

    String ACTION_VIP4_F1_SEARCH_CARD_EXPO = "VIP4-F1-search-card_expo";

    String ACTION_BRAND_JOBBANNER_SHOW = "brand-jobbanner-show";

    /**
     * 1015.812 app b在直播中，“涨流量”购买弹层曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48876
     */
    String EXTENSION_CAMPUSLIVE_ADDFLOWTOASTPOP = "extension-campuslive-addflowtoastpop";

    /**
     * 1015.812 app b在直播中，流量购买弹层内，点击「立即支付」
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48870
     */
    String EXTENSION_CAMPUSLIVE_ADDFLOW_TOASTCK = "extension-campuslive-addflow-toastck";

    String ACTION_CHAT_QUESTION_ANSWER = "action-chat-chatQuestion-questionAnswer";

    String ACTION_STICKER_PRESS_PREVIEW_EXPOSE = "sticker-press-preview-expose";

    String ACTION_STICKER_PRESS_PREVIEW_DELETE_CLICK = "sticker-preview-delete-click";

    String ACTION_STICKER_PRESS_PREVIEW_DELETE_DOUBLE_CHECK = "sticker-preview-delete-doublecheck";

    String ACTION_STICKER_PRESS_TO_PREVIEW = "sticker-press-to-preview";

    String ACTION_PRESS_ADD = "sticker-press-add";

    String ACTION_CHAT_QUESTION_ANSWEER_DETAIL = "action-chat-chatQuestion-answerDetail";

    String ACTION_CHAT_QUESTION_ANSWEER_QUESTION = "action-chat-chatQuestion-answerQuestion";

    String ACTION_CHAT_QUESTION_QUESTION_MESSAGE_AUTO_SET_QUESTION_CLICK = "action-chat-chatQuestion-messageAutoSetQuestionClick";

    String ACTION_CHAT_CHAT_QUESTION_ANSWER_ME = "action-chat-chatQuestion-answerMe";

    String ACTION_CHAT_CHAT_QUESTION_HIS_ANSWER = "action-chat-chatQuestion-hisAnswer";

    String ACTION_CHAT_CHAT_QUESTION_PUBLISH = "action-chat-chatQuestion-questionAnswerPublish";

    String ACTION_CHAT_CHAT_QUESTION_CARD_COMFIRM = "action-chat-chatQuestion-questionCardConfirm";

    String ACTION_CHAT_CHAT_QUESTION_MY_ANSWER_ENTER = "action-chat-chatQuestion-myAnswerEnter";

    String ACTION_CHAT_CHAT_QUESTION_CARD_EXPOSE = "action-chat-chatQuestion-questionCardExpose";

    String ACTION_CHAT_CHAT_QUESTION_ANSWER_IMMEDIATELY = "action-chat-chatQuestion-answerImmediately";

    String ACTION_EXTENSION_GET_INTERVIEW_EXPERIENCE_TEXT = "extension-get-interview-experience-text";

    String ACTION_EXTENSION_GET_INTERVIEW_EXPERIENCE_PUBLISH_CLICK = "extension-get-interview-experience-publish-click";

    String ACTION_EXTENSION_GET_INTERVIEW_EXPERIENCE_PUBLISH_SUC = "extension-get-interview-experience-publish-suc";

    String ACTION_CHAT_WINDOW_EMOJIBAR = "action-chat-window-emojiBar";

    String ACTION_CHAT_WINDOW_YELLOW_BAR_CLOSE = "action-chat-window-yellowBarClose";

    String ACTION_CHAT_WINDOW_YELLOW_BAR_EXPOSE = "action-chat-window-yellowBarExpose";

    String ACTION_CHAT_WINDOW_YELLOW_EMOTION_CLICK = "action-chat-window-emojiClick";

    String ACTION_CHAT_WINDOW_YELLOW_EMOTION_BAR_CLOSE = "action-chat-window-emojiBarClose";


    /**
     * 简历页视频切片点击
     */
    String ACTION_BIZ_ITEM_CLICK_JDVIDEOSLICE = "biz-item-click-jdvideoslice";

    /**
     * 简历页视频切片曝光
     */
    String ACTION_BIZ_ITEM_EXPOSURE_JDVIDEOSLICE = "biz-item-exposure-jdvideoslice";


    String ACTION_C_CHAT_CARD_SHOW = "c-chat-card-show";

    String ACTION_C_CHAT_CARD_CLICK = "c-chat-card-click";

    String ACTION_CHAT_BRANDCARD_SEND_CK = "chat-brandcard-send-ck";

    String ACTION_CHAT_BRANDCARD_ICON_CK = "chat-brandcard-icon-ck";

    String ACTION_CHAT_BRANDCARD_ICON_POP = "chat-brandcard-icon-pop";


    String ACTION_B_CHAT_CARD_SHOW = "b-chat-card-show";

    String ACTION_B_CHAT_CARD_CLICK = "b-chat-card-click";

    /*注：1103.2【C】简历作品优化 - F1卡片图片点击直接跳转简历页，移除此埋点，已与产品@丁英豪沟通确认*/
    String ACTION_DETAIL_GEEK_PIC_CLICK = "detail-geek-pic-click";

    String ACTION_GEEK_LIKE_SEARCH_DETAIL_GEEK = "geeklike-search-detailgeek";
    String ACTION_GEEK_LIKE_GUIDE_EXPO = "geeklike-guide-expo";
    String ACTION_GEEK_LIKE_GUIDE_CLICK = "geeklike-guide-click";
    String ACTION_GEEK_LIKE_FILTER_CLICK = "listgeek-like-filter-click";

    String ACTION_BIZ_REFUND_OPERATION_WITHDRAW = "biz-refund-operation-withdraw";
    String ACTION_BIZ_REFUND_OPERATION_WITHDRAW_CLICK = "biz-refund-operation-withdrawClick";

    String ACTION_BIZ_PAY_CHARGE_AMOUNT = "biz-pay-charge-amount";

    String ACTION_BIZ_LEVEL_BEAN_CLICK = "biz-bzb-bean-click";

    String ACTION_USERINFO_MICRORESUME_HORNOR_SHOW = "userinfo-microresume-hornor-show";

    String ACTION_AXB_WX_EXCHANGE_CLICK = "axb-wxexchange-click";

    String ACTION_USERINFO_MICRORESUME_HORNOR_SEARCH = "userinfo-microresume-hornor-search";

    String ACTION_USERINFO_MICRORESUME_HORNOR_SAVE = "userinfo-microresume-hornor-save";

    String ACTION_USERINFO_MICRORESUME_WORK_JOBLEVEL_ADD = "userinfo-microresume-work-joblevel-add";

    String ACTION_USERINFO_MICRORESUME_WORK_JOBLEVEL_SHOW = "userinfo-microresume-work-joblevel-show";

    String ACTION_USERINFO_MICRORESUME_WORK_JOBLEVEL_CLICK = "userinfo-microresume-work-joblevel-click";

    String ACTION_USERINFO_MICRORESUME_WORK_JOBLEVEL_SAVE = "userinfo-microresume-work-joblevel-save";

    String ACTION_FEED_BACK_CLOSE_POUP = "pg-feedback-close-popup";


    String ACTION_BIZ_BLOCK_REJECT_MODIFY_CLICK = "biz-block-rejectmodify-click";
    String ACTION_BIZ_BLOCK_REJECT_MODIFY_CLOSE = "biz-block-rejectmodify-close";

    String ACTION_CHAT_PAGE_EXPOSE = "chat-page-epxose";

    /**
     * 1104.034 app boss在工作环境视频上传方式弹窗内，点击 取消/拍摄/从相册中选择
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=197833407
     */
    String ACTION_USERINFO_PROFILE_WORKENVIRONMENT_VIDEOUPWAY = "userinfo-profile-workenvironment-videoupway";

    /**
     * 1104.034 boss在工作环境视频拍摄停止后，点击 返回/完成
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=197833407
     */
    String ACTION_USERINFO_PROFILE_WORKENVIRONMENT_VIDEOSHOOTDONE = "userinfo-profile-workenvironment-videoshootdone";

    String ACTION_PREFERRED_RECRUITMENT_LIST_CLICK = "hailuo-preferred-recruitment-list-click";

    String ACTION_JOB_MANAGE_BRAND_TASTE_POP = "jobmanage-brandtaste-pop";


    String ACTION_CHAT_NEW_CHAT_RECENT_REPLY = "action-chat-newChat-recentReply";

    String ACTION_CHAT_NEW_CHAT_QUICK_REPLY = "action-chat-newChat-quickReply";

    String ACTION_EXCHANGE_WECHAT_CLICK = "action-exchange-wechat-click";

    String ACTION_EXCHANGE_MOBILE_CLICK = "action-exchange-mobile-click";

    String ACTION_F2_DETAIL_LAYER_CLICK = "f2-detail-layer-click";

    String ACTION_F2_DETAIL_LAYER_EXPOSE = "f2-detail-layer-expose";

    String ACTION_SUPER_SEARCH_MIDDLE_EXPOSURE = "super-search-middle-exposure";

    String ACTION_B_CHAT_GEEK_CARD_EXPO = "b-chat-geek-card-expo";

    String ACTION_GREETING_SET_PAGE_ACTION = "greeting-set-page-action";

    String ACTION_LIST_GEEK_LIKE_NON_RELATE_GEEK_EXPO = "listgeek-like-non-relatedgeek-expo";

    String ACTION_F1_SALARY_DETAIL_SHOW_CLICK = "f1_salary_detail_show_click";
    String ACTION_F1_SALARY_DETAIL_SHOW = "f1_salary_detail_show";

    String ACTION_F2_SEARCH_GUESS_CLICK = "f2-search-guess-click";

    String ACTION_F2_SEARCH_GUESS_EXPOSE = "f2-search-guess-expose";

    String ACTION_BIZ_ITEM_F3_NEW_ENTRANCE_EXPOSE_OUT = "biz-item-f3-new-entrance-exposure-out";

    String ACTION_USER_INFO_JOB_POPUP_STOP_EXPO = "userinfo-job-popup-StopExpo";
    String ACTION_USER_INFO_JOB_POPUP_STOP_CLICK = "userinfo-job-popup-StopClick";

    String ACTION_BLOCK_MSG_EXPOSURE = "biz-block-msg-exposure";

    String ACTION_HUNTER_APP_CHAT_EXPOSE = "extension-hunter-cvcustomer-appchatexpo";
    String ACTION_F1_QUICK_ADD_JOB_CLICK = "quick-add-job-click";
    String ACTION_F1_QUICK_ADD_JOB_EXPOSE = "quick-add-job-expose";

    String ACTION_CHAT_STARASH = "chat-starash";
    String ACTION_CHAT_NEW_THEME_CLICK = "starash_newtheme_click";
    String ACTION_CHAT_NLP_CLICK = "starash_replaymaterial_click";
    String ACTION_CHAT_MATERIAL_CLICK = "starash_chatmaterial_click";
    String ACTION_REPLY_EVALUATE = "starash_replay_evaluate";
    String ACTION_REPLY_VOICE = "starash_play_voice";
    String ACTION_WINDOW_EXPOSE = "heterotype-window-expose";

    String ACTION_GET_RECORDVIDEO_RETURN = "get-recordvideo-return";// 有了社区-完成视频录制页面-返回按钮点击
    String ACTION_APP_PAGE_DWELLTIME = "app-page-dwelltime";// APP B/C主要页面停留时长

    String ACTION_GREETING_EDIT_COMPLETE_CLICK = "greeting-edit-complete-click";

    String ACTION_GREETING_EDIT_CLICK = "greeting-edit-click";

    //有了社区-创作引导弹窗点击】
    String ACTION_GUIDEWINDOWS_CLICK = "get-guidewindows-click";

    //【有了社区-创作引导弹窗曝光】
    String ACTION_GUIDEWINDOWS_EXPOSE = "get-guidewindows-expose";


    String ACTION_RESUME_RECALL_CLICK = "resume-send-recall-click";

    String ACTION_RESUME_SEND_RECALL_CLICK = "resume-send-request-recall-click";

    String ACTION_RESUME_SEND_RECALL_SHOW = "resume-send-request-recall-show";

    String ACTION_EXTENSION_HUNTER_JOB_MANAGEMENT_SAME_JOB = "extension-hunter-jobmanagement-samejob";

    String ACTION_BIZ_BLOCK_LQJD_GPTINVOLVED_CARD_CLICK = "biz-block-lqjd-gptinvolved-card-click";
    String ACTION_BIZ_BLOCK_LQJD_GPTINVOLVED_CARD_CLOSE = "biz-block-lqjd-gptinvolved-card-close";
    String ACTION_BIZ_BLOCK_LQJD_GPTINVOLVED_CARD_EXPOSURE = "biz-block-lqjd-gptinvolved-card-exposure";
    String ACTION_EXTENSION_CAMPUSLIVE_WINDOW_PLAY = "extension-campuslive-windowplay";

    /**
     * 1114.63 app 关闭小窗时，关闭小窗播放弹窗设置弹窗曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?version=11.14&stakeholder=299&showdetail=61782
     */
    String EXTENSION_CAMPUSLIVE_PRESET_SILENT = "extension-campuslive-presetsilent";

    String ACTION_USERINFO_JOB_PART_TIME_DEFAULT_TIME = "userinfo-job-parttime-DefaultTime";

    String ACTION_CHAT_CHAT_QUESTION_ANSWER = "action-chat-chatQuestion-answerCover";

    /**
     * 1115.603 app 牛人查看职位详情页，点击「同公司推荐职位」模块
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=61879
     */
    String ACTION_JD_SIMJOBS_CK = "jd-simjobs-ck";

    /**
     * 1115.603 app 牛人查看职位详情页，有「同公司职位推荐」模块（按接口请求记录，非真实曝光）
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=61877
     */
    String ACTION_JD_SIMJOBS_POP = "jd-simjobs-pop";

    /**
     * 1115.266【商业】服务业畅聊 - 职位页/聊天页引导牛人打电话- 曝光埋点
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=61950
     */
    String ACTION_JD_BIZ_BLOCK_CALL_GEEK_EXPO = "biz-block-call-geek-expo";

    /**
     * 1115.266【商业】服务业畅聊 - 职位页/聊天页引导牛人打电话- 点击埋点
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=208199620
     */
    String ACTION_JD_BIZ_BLOCK_CALL_GEEK_CLICK = "biz-block-call-geek-click";

    String ACTION_BIZ_BLOCK_FUN_GUIDE_EXPO = "biz-block-fun-guide-expo";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_SCRIPT_CHANGE_SCRIP = "batchappendchat-choosescript-changescript";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_GEEK_GOCHAT = "batchappendchat-choosegeek-gochat";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_SEND_CLICK = "batchappendchat-choosescript-sendclick";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_CLOSE_JOB = "batchappendchat-choosegeek-closejob";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_CLICK_JOB = "batchappendchat-choosegeek-clickjob";

    String ACTION_BATCH_APPEND_CHAT_CHOOSE_FILT_JOB_CLICK = "batchappendchat-choosegeek-filtjob-click";


    String ACTION_BIZ_BLOCK_BEAN_INTRODUCTION_INFO_CLICK = "biz-block-bean-introductionInfoClick";

    String ACTION_CHAT_QUESTION_NEW_MODIFY_QUESTION = "action-chat-chatQuestion-newModifyQuestion";

    String ACTION_CHAT_QUESTION_NEW_REPLY = "action-chat-chatQuestion-newReply";

    String ACTION_CHAT_QUESTION_AUTO_POPUP_CLICK = "action-chat-chatQuestion-autoPopUpClick";

    String ACTION_CHAT_QUESTION_AUTO_POPUP = "action-chat-chatQuestion-autoPopUp";

    String ACTION_CHAT_QUESTION_EDIT_QUESTION = "action-chat-chatQuestion-editQuestion";

    String ACTION_CHAT_QUESTION_EDIT_MODIFY_QUESTION = "action-chat-chatQuestion-modifyQuestion";

    String ACTION_BATCH_QUICK_LIST_SHOW = "newchat-batch-quicklist-show";

    String ACTION_BATCH_QUICK_LIST_SEND = "newchat-quickreply-send";

    String ACTION_BATCH_QUICK_HIDE_CLICK = "newchat-quickhide-click";

    String ACTION_BATCH_QUICK_READ_READ_MARK = "newchat-quickread-readmark";

    String ACTION_QUICK_PROCESS_SLIDE_GEEK = "quick-process-slide-geek";

    String ACTION_F2_EXPOSE_RED_DOT = "F2-expose-red-dot";

    String ACTION_BIZ_BLOCK_BEAN_PAGE_RECORD = "biz-block-bean-page-record";
    String ACTION_BIZ_BLOCK_BEAN_GUESS_WANT_CLICK = "biz-block-bean-guess-want-click";

    String ACTION_BIZ_QUESTION_CHOOSE_JOB_POP_UP = "action-chat-Question-chooseJobPopUp";

    String ACTION_BIZ_QUESTION_CHOOSE_JOB_POP_UP_CLICK = "action-chat-Question-chooseJobPopUpClick";

    String ACTION_BIZ_QUESTION_QUESTION_SAVE = "action-chat-Question-questionSave";

    String ACTION_BIZ_QUESTION_QUESTION_OPOUP = "action-chat-Question-chooseQuestionPopUp";

    String ACTION_BIZ_QUESTION_QUESTION_MODIFY_QUESTION_POPUP = "action-chat-Question-modifyQuestionPopUp";

    String ACTION_BIZ_QUESTION_QUESTION_MODIFY_QUESTION_POPUP_CLICK = "action-chat-Question-chooseQuestionPopUpClick";

    String ACTION_BIZ_QUESTION_QUESTION_MODIFY_QUESTION = "action-chat-Question-modifyQuestion";

    String ACTION_BIZ_QUESTION_QUESTION_MODIFY_NEW_QUESTION = "action-chat-Question-newQuestion";

    String ACTION_BIZ_QUESTION_QUESTION_SET_PAGE = "action-chat-Question-setPage";

    String ACTION_BIZ_QUESTION_QUESTION_POP_UP = "action-chat-Question-PopUp";

    String ACTION_CHAT_RESUME_GRAY_BAR_CLICK = "chat-resume-greybar-click";

    String ACTION_BIZ_BZB_BEAN_CHANGE_CHANNEL_CLICK = "biz-bzb-beanChangeChannel-click";

    String ACTION_BIZ_BZB_BEAN_CHANGE_CHANNEL_EXPOSURE = "biz-bzb-beanChangeChannel-exposure";

    String ACTION_CHAT_ANSWER_LIST_CLICK = "action-chat-Answer-ListClick";

    String ACTION_CHAT_ANSWER_LIST_SHOW = "action-chat-Answer-ListShow";

    String ACTION_CHAT_ANSWER_SYNC_BOTTOM_SHOW = "action-chat-Answer-SyncBottomShow";

    String ACTION_CHAT_APPEND_SWITCH_CLICK = "batchappendchat-switch-click";

    String ACTION_CHAT_APPEND_CLOSE_WINDOW_CLICK = "batchappendchat-closewindow-click";

    String ACTION_CHAT_APPEND_CLOSE_WINDOW_SHOW = "batchappendchat-closewindow-show";

    String BS_CER_COM_OCR_TYPE = "bs_cer_com_ocr_type";

    String ACTION_HUNTER_GEEK_CONFIRM_CLICK = "extension-hunter-cvcustomer-geekconfirmclick";

    String ACTION_HUNTER_CVCUSTOMER_SIMI_GEEK_EXPO = "extension-hunter-cvcustomer-simigeekexpo";


    String ACTION_USER_INFO_PART_TIME_REAL_TIME_CARD_EXPO = "userinfo-job-parttime-RealtimeCardExpo";
    String ACTION_USER_INFO_PART_TIME_REAL_TIME_CARD_CLICK = "userinfo-job-parttime-RealtimeCardClick";

    String ACTION_DETAIL_MATE_RECORD_EXPOSURE = "detail-mate-record-exposure";

    String ACTION_ITEM__CHAT_ACCURATE_POP_EXPOSE = "biz-item-AccurateSearch-chat-PopExposure";

    String ACTION_ITEM__CHAT_ACCURATE_POP_CLICK = "biz-item-AccurateSearch-chat-PopClick";

    String ACTION_ITEM__CHAT_ACCURATE_CONTACT_CLICK = "biz-item-AccurateSearch-contact-Click";

    String ACTION_ITEM__CHAT_ACCURATE_CHAT_PAYMENT_EXPOSE = "biz-item-AccurateSearch-chat-bzbExposure";

    String ACTION_ITEM__CHAT_ACCURATE_CHAT_BLOCK_EXPOSURE = "biz-item-AccurateSearch-chat-BlockExposure";

    /**
     * 通用满意度采集功能的调整 - NPS选项的提交
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=95320
     */
    String ACTION_LIST_NPS_WINDOW_SUBMIT = "action-list-nps-window-submit";

    /**
     * 通用满意度采集功能的调整 - NPS弹层的曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=95320
     */
    String ACTION_LIST_NPS_WINDOW_EXPO = "action-list-nps-window-expo";

    /**
     * 通用满意度采集功能的调整 - NPS选项的点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=95307
     */
    String ACTION_LIST_NPS_WINDOW_CLICK = "action-list-nps-window-click";
    /**
     * 通用满意度采集功能的调整 - 气泡的曝光
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=95276
     */
    String ACTION_LIST_NPS_EMOJI_EXPO = "action-list-nps-emoji-expo";
    /**
     * 通用满意度采集功能的调整 - 气泡的点击
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=95280
     */
    String ACTION_LIST_NPS_EMOJI_CLICK = "action-list-nps-emoji-click";
    /**
     * 【有了社区-动态图片点击】
     */
    String ACTION_PICTURE_DETAIL_DETAIL = "get-picturedetail-show";

    String ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_EXPO = "biz-item-directcall-callmuchexpo";
    String ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_CLICK = "biz-item-directcall-callmuchclick";

    String ACTION_BOSS_PART_TIME_JOB_WORK_TIME_PAGE_SHOW = "boss_partjob_worktime_page_show";

    String ACTION_GAO_DE = "gaode";


    String ACTION_BIZ_ITEM_PUSH_FAIL = "biz-item-push-fail";

    String ACTION_BIZ_ITEM_ACCURATE_SEARCH_GEEK_CALL_NOW_CLICK = "biz-item-AccurateSearch-GeekCallNowClick";

    String ACTION_BIZ_ITEM_ACCURATE_SEARCH_GEEK_CALL_BACK_CLICK = "biz-item-AccurateSearch-GeekCallBackClick";

    String ACTION_BIZ_ITEM_ACCURATE_SEARCH_GEEK_NOT_CONNECT_EXP = "biz-item-AccurateSearch-GeekNotConnectExp";

    String ACTION_GEEK_EXPECT_SUG_EXP = "geek-expect-sug-exp";

    String ACTION_BOSS_F1_BLANK = "boss-f1-blank";

    String ACTION_EXP_ADD_CITY_EXPOSE = "exp-add-city-expose";
    String ACTION_STU_EXP_DIALOG_ENTRY_EXPOSE = "stu_exp_dialog_entry_expose";

    String ACTION_F1_OVERSEA_TAB_SHOW = "f1-oversea-tab-show";
    String ACTION_F1_OVERSEA_FLITER_CLICK = "f1-oversea-fliter-click";
    String ACTION_STU_EXP_DIALOG_ENTRY_CLICK = "stu_exp_dialog_entry_click";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_CHAT = "extension-aiagent-newMessage-chat";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_ERROR = "extension-aiagent-newMessage-error";

    String ACTION_EXTENSION_GET_VIDEO_JDASSIST_LIST_ACT = "get-videojdassist-list-act";

    String ACTION_EXTENSION_DOUBLE_CHAT_CHANGE_SCRIPT = "doublechat-change-script";


    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_EXIT = "extension-aiagent-newMessage-exit";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_CLEAR = "extension-aiagent-newMessage-clear";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_DEAL_REJECT = "extension-aiagent-newMessage-dealReject";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_DEAL_REPLY = "extension-aiagent-newMessage-dealReply";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_DEAL = "extension-aiagent-newMessage-deal";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_LIST_RECORD = "extension-aiagent-newMessage-listRecord";

    String ACTION_EXTENSION_AIAGENT_NEW_MESSAGE_ENTER = "extension-aiagent-newMessage-enter";

    String ACTION_GET_VIDEO_ASSIST_SETTING_CLICK = "get-videojdassist-detailsettings-click";

    String ACTION_GET_VIDEO_ASSIST_TAB_CLICK = "get-videojdassist-detailtab-click";

    String ACTION_EXTENSION_NEW_MESSAGE_MORE_GEEK = "extension-aiagent-newMessage-moreGeek";

    String ACTION_AIAGENT_NEW_MESSAGE_GEEKLIST = "extension-aiagent-newMessage-geekList";

    String ACTION_EXPECT_DELETE_BLOCK_POP_UP_CLICK = "expect_delete_block_popup_click";
    String ACTION_EXPECT_DELETE_BLOCK_POP_UP_SHOW = "expect_delete_block_popup_show";

    String ACTION_PART_TIME_GEEK_EXPECT_CLICK = "part-time-geek-expect-click";

    String ACTION_AI_CHAT_REPORT = "ai-chat-report";

    String ACTION_AI_CHAT_BLOCK = "ai-chat-block";

    String ACTION_EXCELLENT_BUSINESS_PUB_JOB_PROMPT = "excellent_business_publish_job_prompt_app";


    String ACTION_EXCELLENT_NEW_MESSAGE_FILTER_EDIT = "extension-aiagent-newMessage-filterEdit";


    String ACTION_EXCELLENT_NEW_MESSAGE_ERROR_EDIT = "extension-aiagent-newMessage-errorEdit";

    String ACTION_B_F1_BANNER_EXPOSRURE = "boss-gss-f1banner-expose";

    String ACTION_BIZ_BLOCK_VERIFY_POP_UP_CLICK = "biz-block-verify-popup-click";
    String ACTION_BIZ_BLOCK_VERIFY_POP_UP_EXPOSURE = "biz-block-verify-popup-exposure";
    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_APP_LIST_EXPO = "extension-hunter-cvcustomer-approbotlist";
    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_MY_ORDER_LIST_EXPO = "extension-hunter-cvcustomer-myorderlist";
    String ACTION_CHAT_DOUBLE_CHAT_POP_SHOW = "action-chat-doublechat-PopShow";
    String ACTION_CHAT_DOUBLE_CHAT_POP_CLICK = "action-chat-doublechat-PopClick";

    String ACTION_CHAT_TASK_ID_EXPLORE = "extension-zhs-taskid-exposure";

    //用户取消支付行为，1204.210【商业】待支付订单兼容二合一场景
    String ACTION_BIZ_BLOCK_TOPAY_CANCEL = "biz-block-topay-cancel";
    String ACTION_HUNTER_GUIDE_BAR_SHOW = "extension-hunter-AbilityShow-GuideBar";

    String ACTION_EXTENSION_AI_AGENT_SEARCH_EXPOSE = "extension-aiagent-search-expose";
    String ACTION_EXTENSION_AI_AGENT_SEARCH_ENTER = "extension-aiagent-search-enter";


    String ACTION_EXTENSION_AI_AGENT_SEARCH_ERROR = "extension-aiagent-search-error";

    String ACTION_EXTENSION_AI_AGENT_SEARCH_SUBMIT = "extension-aiagent-search-submit";

    String ACTION_EXTENSION_AI_AGENT_SEARCH_CHAT = "extension-aiagent-search-chat";

    String ACTION_EXTENSION_AI_AGENT_SEARCH_REPLY = "extension-aiagent-search-reply";

    String ACTION_EXTENSION_AI_AGENT_SEARCH_DELETE = "extension-aiagent-search-delete";

    String ACTION_OPER_SHARE_DETAIL_SUCCESS = "operation-share-detail-sharesuccess";

    String ACTION_GEEK_GSS_AUTHOR_SWITCH = "geek-gss-author-switch";

    String ACTION_STU_DIALOG_QUESTION_EXPOSE = "stu_dialog_question_expose";

    String ACTION_STU_DIALOG_QUESTION_CLICK = "stu_dialog_question_click";

    String ACTION_STU_DIALOG_PROLOGUE_EXPOSE = "stu_dialog_prologue_expose";

    String ACTION_CHAT_CLICK_AI_JOB = "action-chat-click-AIjob";

    String ACTION_CHAT_POP_AI_SECURITY = "action-chat-pop-AIsecretary";

    String ACTION_CHAT_CLICK_AI_SECURITY = "action-chat-click-AIsecretary";

    String ACTION_CHAT_HELLO_CARD_QUICK_RESONSED_CLICK = "action-chat-HelloCard-QuickRespondClick";

    String ACTION_CHAT_HELLO_QUICK_RESPONSED_SHOW = "action-chat-HelloCard-QuickRespondShow";

    String ACTION_CHAT_HELLO_CARD_CLICK = "action-chat-HelloCard-click";
    String ACTION_OUTPOST_WORK_ENVIRONMENT_INVITE_CLICK = "outpost-work-environment-invite-click";
    String ACTION_OUTPOST_WORK_ENVIRONMENT_UPLOAD = "outpost-work-environment-upload";
    String ACTION_OUTPOST_WORK_ENVIRONMENT_CLICK = "outpost-work-environment-click";
    String ACTION_OUTPOST_WORK_ENVIRONMENT_CONFIRM_CLICK = "outpost-work-environment-confirm-click";
    String ACTION_OUTPOST_WORK_PIC_SHOW = "detail-outpostjob-pic-show";
    String ACTION_JOB_TOP_CARD_CLICK = "job-top-card-click";

    String ACTION_BOSS_REG_CLICK_NEW_COM = "boss_reg_click_new_com";

    String ACTION_BOSS_REG_CLICK_MY_COMPANY = "boss_reg_click_my_company";

    String ACTION_BOSS_REG_CLICK_MY_COM = "boss_reg_click_my_com";

    String ACTION_BOSS_REG_CLICK_SHOW_MY_COM = "boss_reg_click_show_my_com";

    String ACTION_COMPANY_INDUSTRY_SAVE = "company_industry_save";

    String ACTION_FINISH_RECRUITMENT_PAGE_SAVE_INFO = "finish_recruitment_page_save_info";

    String ACTION_GEEK_EXPECT_PREFERENCE_EXPO = "action-geek-expect-expo";

    String ACTION_VIDEO_REVIEW_CLICK = "video-voice-review-click";

    String ACTION_VIDEO_CALL_SUCCESS = "video-voice-call-success";

    String ACTION_VIDEO_CALL_DEAL_CLICK = "video-voice-call-deal-click";

    String ACTION_VIDEO_REVIEW_SHOW = "video-voice-review-show";

    String ACTION_BOSS_CERTIFY_BOSS_REG_BASIC_POST_EXPAND_CLICK = "Boss-certify-boss_reg_basic_post_expand-click";

    String ACTION_BOSS_CERTIFY_BOSS_REG_SCALE_PAGE_EXPO_SHOW = "Boss-certify-boss_reg_scale_page_expo-show";

    String ACTION_BOSS_CERTIFY_BOSS_REG_SCALE_SAVE_CLICK = "Boss-certify-boss_reg_scale_save-click";

    String ACTION_AI_SEARCH_RESULT_PAGE_CLICK = "ai-search-resultpage-cilck";

    String ACTION_AI_SEARCH_INPUT_RESULT = "ai-search-input-result";

    String ACTION_AI_SEARCH_PAGE_EXPOSE = "ai-search-resultpage-expose";

    String ACTION_AI_SEARCH_EXIT_CLICK = "ai-search-exit-click";

    String ACTION_GEEK_CONNECT_SHOU_CANG_CLICK = "geek-connect-shoucang-click";

    String ACTION_BOSS_COM_UPLOAD_CLICK = "Boss-com-upload-click"; // B-公司-点击上传-点击

    String ACTION_BOSS_COM_AUTHORIZATION_POP_UP_SHOW = "Boss-com-Authorization-pop-up-show"; //点击公司简称页面【保存】按钮，满足条件时展现品牌授权书上传弹窗

    String ACTION_BOSS_COM_AUTHORIZATION_POP_UP_CLICK = "Boss-com-Authorization-pop-up-button-click"; //B-公司-品牌授权书上传弹窗按钮点击-点击


    String ACTION_GEEK_RCMD_CITYPOPUP_SHOW = "geek-rcmd-citypopup-show";

    String ACTION_GEEK_RCMD_CITYPOPUP_CLICK = "geek-rcmd-citypopup-click";

    String ACTION_BOSS_RCMD_GEEK_HIDE_CLICK = "Boss-rcmd-geek-hide-click";

    String ACTION_EXTENSION_AI_AGENT_FILTER_EXPOSE = "extension-aiagent-filter-iconexpose";
    String ACTION_EXTENSION_AI_AGENT_FILTER_CLICK = "extension-aiagent-filter-iconclick";

    String ACTION_EXTENSION_AI_AGENT_NEW_LIST = "extension-aiagent-search-newlist";

    String ACTION_EXTENSION_AI_SEARCH_GEEK_LIST = "extension-aiagent-search-geekList";

    String ACTION_EXTENSION_AI_SEARCH_DETAIL_LIST = "extension-aiagent-search-detailList";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=2785499">...</a>
     */
    String ACTION_GEEK_ACCEPT_GUIDE_DIALOG_SHOW = "geek-connect-yindaofuceng-show";

    String ACTION_STU_DIALOG_SUGQQUESTION_EXPOSE = "stu_dialog_sugqquestion_expose";

    String ACTION_STU_DIALOG_SUGQQUESTION_CLICK = "stu_dialog_sugqquestion_click";

    String ACTION_STU_DIALOG_CARD_EXPOSE = "stu_dialog_card_expose";

    String ACTION_STU_DIALOG_CARD_MORE_CLICK = "stu_dialog_card_more_click";

    String ACTION_STU_DIALOG_REQUEST_CLICK = "stu_dialog_request_click";

    String ACTION_AI_ASSISTANT_FEEDBACK_REASON_EXPOSE = "ai-assistant-feedback-reason-expose";

    String ACTION_AI_ASSISTANT_FEEDBACK_NEGTYPE_POP_CLICK = "ai-assistant-feedback-negtype-pop-click";

    String ACTION_STU_EXP_DIALOG_CHAT_GPT_FAIL_EXPOSE = "stu_exp_dialog_chat_gpt_fail_expose";

    String ACTION_STU_EXP_DIALOG_CHAT_GPT_FAIL_CLICK = "stu_exp_dialog_chat_gpt_fail_click";

    String ACTION_STU_DIALOG_JOB_CONTRAST_BEGIN_CLICK = "stu_dialog_jobcontrast_begin_click";

    String ACTION_STU_DIALOG_JOB_CLICK = "stu_dialog_job_click";

    String ACTION_STU_DIALOG_JOB_CARD_ADD_FRIEND = "stu_dialog_jobcard_addfriend";

    String ACTION_STU_DIALOG_RESUME_CARD_CLICK = "stu_dialog_resume_card__click";

    /**
     * https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=2848541
     */
    String ACTION_BOSS_CERTIFY_HELP_INVITE = "Boss-certify-bs_cer_com_help_invite_expose-show";

    /**
     * https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=2848544
     */
    String ACTION_BOSS_CERTIFY_HELP_INVITE_CLICK = "Boss-certify-bs_cer_com_help_invite_expose_click-click";

    String ACTION_EXTENSION_AI_AGENT_NEW_CHAT = "extension-aiagent-search-newchat";

    String ACTION_GEEK_FEEDBACK_MORE_QUESTIONS_POP_SHOW = "geek-feedback-morequestions_pop-show";

    String ACTION_GEEK_FEEDBACK_STANDARD_QUESTION_CLICK = "geek-feedback-Standard_question-click";

    String ACTION_GEEK_FEEDBACK_GUESS_SHOW = "geek-feedback-guess-show";

    String ACTION_MYCUSTOMER_SELFSERVICE_CLICK = "mycustomer_selfservice_click";

    /**
     * 搜索行为-搜索职位详情页-搜索框曝光
     * https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=2875708&appId=100
     */
    String ACTION_SEARCH_JOBDETAIL_EXPOSE = "action-search-jobdetail-expose";

    String ACTION_VVIP_RESPONSE_AGENT_THUMBS = "vvip-response-agent-thumbs";

    String ACTION_VVIP_RESPONSE_AGENT_REGENERATE = "vvip-response-agent-response-regenerate";

    String ACTION_VVIP_RESPONSE_AGENT_REDIRECT_PAGE = "vvip-response-agent-redirect-page";

    String ACTION_BIZ_ITEM_SEARCH_MODULE_CLICK = "biz-item-searchModule-click";
    String ACTION_BIZ_ITEM_SEARCH_MODULE_SHOW = "biz-item-searchModule-show";
    String ACTION_BIZ_ITEM_UPBUTTOM_CLICK = "biz-item-upButton-click";
    String ACTION_BIZ_ITEM_UPBUTTOM_SHOW = "biz-item-upButton-show";
    String ACTION_CER_COM_MATERIAL_COMPRESS = "bs_cer_com_material_compress";

    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_F1_GUIDE_CARD_CLOSE = "extension-hunter-cvcustomer-f1guidecardclose";
    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_F1_GUIDE_CARD_VIEW_MORE = "extension-hunter-cvcustomer-f1guidecardmoreexpo";
    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_F1_GUIDE_CARD_CLICK = "extension-hunter-cvcustomer-f1guidecardclick";

    String ACTION_F1_NEGATIVE_FEEDBACK_OTHER_REASONS_CANCELLED = "f1-negative-feedback-other-reasons-cancelled";
    /**
     * 1224.170 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=16022">...</a>
     */
    String ACTION_INTERVIEW_GUIDE_CLICK = "interview-guide-click";

    /**
     * 校验地址是否通过 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=3021695">...</a>
     */
    String ACTION_CERTIFY_ADDRESS_VERIFY= "lifecycle-certify-address-verify";


    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_APP_AI_AGENT_ICON_CLICK = "extension-hunter-cvcustomer-app-aiagent-iconclick";
    String ACTION_EXTENSION_HUNTER_CV_CUSTOM_APP_AI_AGENT_ICON_EXPOSE = "extension-hunter-cvcustomer-app-aiagent-iconexpose";

    String ACTION_GEEK_CHANGE_CITY_MULTI_SELECTION_SUBMISSION_CLICK = "geek-change_city-multi-city-selection-submission-click";
    String ACTION_BOSS_JOB_FIRST_TITLE_SELECTION_CLICK = "Boss-job-first-title-selection-click";

    String ACTION_GEEK_EXP_ADD_FULLTIME_SHOW = "geek-expect-add-fulltime-show";
    String ACTION_GEEK_EXP_ADD_FULLTIME_CLICK = "geek-expect-add-full-time-click";
    String ACTION_GEEK_EXP_MODIFY_POPUP_CLICK = "geek-expect-modify-popup-click";

    String ACTION_GEEK_IMAGE_PHOTO_UPLOAD = "geek-image-photo-upload";
    String ACTION_GEEK_RESUME_MORE_CLICK = "geek-resume-more-click";
    String ACTION_GEEK_RESUME_IMPORT_CLICK = "geek-resume-import-click";
    String ACTION_GEEK_RESUME_EXPORT_CLICK = "geek-resume-export-click";
    String ACTION_GEEK_RESUME_PROTECT_CLICK = "geek-resume-protect-click";
    String ACTION_CERTIFY_MAP_BEHAVIOR = "lifecycle-certify-envcertify-map-behavior";
    String ACTION_CERTIFY_MAP_SELECT_ADDRESS = "lifecycle-certify-envcertify-map-select-address";

    String ACTION_BOSS_AI_QUICK_RECRUIT_SHOW = "Boss-AI-quickrecruitment-show";
    String ACTION_BOSS_AI_QUICK_RECRUIT_CLICK = "Boss-AI-quickrecruitment-click";
    String ACTION_BOSS_AI_QUICK_RECRUIT_MATCH_SHOW = "Boss-AI-quickrecruitment-match-show";

    String ACTION_BOSS_JOB_MANAGEMENT_PAGE_REPLICATE_BUBBLE_SHOW= "Boss-job-job-management-page-replicate-bubble-show";

    String ACTION_BOSS_JOB_F1_BLANK_GUESS_BOSS_WANT_JOB_CARD_SHOW = "Boss-job-f1-blank-guess-boss-want-job-card-show";

    String ACTION_BIZ_ITEM_F2_CARD_INSERT_OTHER = "biz-item-F2-card-insert-other";

    String ACTION_BS_CER_COM_FR_NAME_EXPOSE_CLICK = "bs_cer_com_fr_name_expose_click";
    String ACTION_BS_CER_COM_FR_NAME_EXPOSE = "bs_cer_com_fr_name_expose";

    String ACTION_GEEK_RESUME_SELECT_SHOW = "geek-resume-select-show";

    String ACTION_LIST_EYEBROW_DISTRIBUTE_CLICK = "list-eyebrow-distribute-click";
}