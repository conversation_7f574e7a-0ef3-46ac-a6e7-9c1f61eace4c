package com.hpbr.bosszhipin.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.amap.api.services.geocoder.RegeocodeAddress;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.datasource.DataSources;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.image.CloseableBitmap;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.GeekBaseCardBean;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.common.dialog.manager.GeekF1DialogRequestParams;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.net.request.GeekJDPreferenceCollectRequest;
import com.hpbr.bosszhipin.net.response.GeekJDPreferenceCollectResponse;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.setting_export.SettingConstants;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.tip.TipBar;
import com.hpbr.bosszhipin.views.tip.TipManager;
import com.hpbr.bosszhipin.weiget.CenterImageSpan;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.LBase;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.BitmapUtil;

import net.bosszhipin.api.CheckResumeCompleteResponse;
import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.GeekCloseComplainTipResponse;
import net.bosszhipin.api.GeekClosePartimeGuideTipRequest;
import net.bosszhipin.api.GetCharactLabelCloseRequest;
import net.bosszhipin.api.GetF1BannerCloseRequest;
import net.bosszhipin.api.GetF1BannerCloseResponse;
import net.bosszhipin.api.GetGeekBusinessDistrictResponse;
import net.bosszhipin.api.HunterAdvGeekJobAssistantResponse;
import net.bosszhipin.api.PoiMatchCityRequest;
import net.bosszhipin.api.PoiMatchCityResponse;
import net.bosszhipin.api.RecommendListAdResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UserNotifyUpdateRequest;
import net.bosszhipin.api.bean.CityBean;
import net.bosszhipin.api.bean.GetF1CloseAdCardRequest;
import net.bosszhipin.api.bean.ServerAfterNameIconBean;
import net.bosszhipin.api.bean.ServerBlueCheckTips;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.api.bean.ServerJobDividerInfo;
import net.bosszhipin.api.bean.ServerResumeRestrictBean;
import net.bosszhipin.api.bean.ServerTopicBannerBean;
import net.bosszhipin.api.bean.UserNotifySetting;
import net.bosszhipin.api.bean.geek.RecommendListAdBean;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.api.bean.user.GeekFeature;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName: GeekF1Util
 * @Description:
 * @Author: yanglinjie
 * @Date: 2024/5/28 16:28
 */
public class GeekF1Util {
    public static final String TAG = "GeekF1Util";
    public static int TYPE_STU_TOPIC_JOB_LIST = 1;
    public static int TYPE_F1_JOB_LIST = 1;

    /**
     * 构造参数集合，用于关联 职位详情页列表
     */
    public static List<ParamBean> getParamList(List<GeekBaseCardBean> jobList, String localPageTag, int type) {
        if (LList.isEmpty(jobList)) return null;
        List<ParamBean> list = new ArrayList<>();
        for (GeekBaseCardBean item : jobList) {
            if (item instanceof ServerJobCardBean) {
                ServerJobCardBean cardBean = (ServerJobCardBean) item;
                if (isJob(cardBean)) {
                    if (TYPE_STU_TOPIC_JOB_LIST == type) {
                        list.add(getStuTopicJobListParamBean(cardBean, localPageTag));
                    }
                }
            }
        }
        return list;
    }


    private List<ParamBean> getStuParamList(List<ServerJobCardBean> jobList, JobIntentBean expectJob, int sortType, long expectType, String keywordParamString, String filterJsonString) {
        if (LList.isEmpty(jobList)) return null;
        List<ParamBean> list = new ArrayList<>();
        for (ServerJobCardBean item : jobList) {
            if (item == null || item.cardType != 0) continue;
            list.add(getStuParamBean(item, expectJob, sortType, expectType, keywordParamString, filterJsonString));
        }
        return list;
    }


    private ParamBean getStuParamBean(ServerJobCardBean item, JobIntentBean expectJob, int sortType, long expectType, String keywordParamString, String filterJsonString) {
        ParamBean bean = new ParamBean();
        bean.securityId = item.securityId;
        bean.userId = item.bossId;
        bean.jobId = item.jobId;
        bean.expectId = expectJob != null ? expectJob.jobIntentId : 0;
        bean.geekF1EncrptyExpectId = expectJob != null ? expectJob.encryptExpectId : "";
        bean.f1ExpectId = expectJob != null ? expectJob.jobIntentId : 0L;
        bean.geekF1SortType = sortType;
        bean.lid = item.lid;
        bean.jobType = expectType;
        bean.jobName = item.jobName;
        bean.degreeName = item.jobDegree;
        bean.experienceName = item.jobExperience;
        bean.hasJobClosed = item.isJobInvalid();
        bean.city = item.cityName;
        bean.salaryDesc = item.salaryDesc;
        bean.businessDistrict = item.businessDistrict;
        bean.from = ParamBean.FROM_F1_LIST;
        if (!TextUtils.isEmpty(item.bottomRightURL)) {
            bean.sourceType = ParamBean.SOURCE_TYPE_F1;
        }
        bean.jobDetailKeywords = keywordParamString;
        if (TextUtils.isEmpty(item.securityId)) {
            TLog.error("MissingSecurityId", "SecurityId missing from %s", getClass().getSimpleName());
        }
        bean.localPageTag = "GListStudentPresenter";
        bean.online = item.online;
        bean.experimentConfigs = item.experimentConfigs;
        bean.filterParams = filterJsonString;
        return bean;
    }

    /**
     * 判断是真正的职位
     * 抽取处理 方便管理
     *
     * @param cardBean
     * @return
     */
    public static boolean isJob(ServerJobCardBean cardBean) {

        return cardBean != null && (cardBean.cardType == 0 || cardBean.cardType == 11);
    }


    private static ParamBean getStuTopicJobListParamBean(ServerJobCardBean bean, String localPageTag) {
        ParamBean paramBean = new ParamBean();
        paramBean.userId = bean.bossId;
        paramBean.jobId = bean.jobId;
        paramBean.securityId = bean.securityId;
        paramBean.expectId = bean.expectId;
        paramBean.lid = bean.lid;
        paramBean.jobName = bean.jobName;
        paramBean.degreeName = bean.jobDegree;
        paramBean.city = bean.cityName;
        if (TextUtils.isEmpty(bean.securityId)) {
            TLog.error("MissingSecurityId", "SecurityId missing from %s", localPageTag);
        }
        paramBean.localPageTag = localPageTag;
        return paramBean;
    }


    public static void closeCommonF1NearByViewRequest() {
        GeekClosePartimeGuideTipRequest request = new GeekClosePartimeGuideTipRequest(new SimpleApiRequestCallback<GeekCloseComplainTipResponse>() {
            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.type = 17;
        HttpExecutor.execute(request);

    }

    public static void handleListAdResponse(RecommendListAdResponse recommendListAdResponse, @NonNull List<GeekBaseCardBean> jobList) {
        if (recommendListAdResponse != null) {
            List<RecommendListAdBean> listAds = recommendListAdResponse.listAds;
            if (!LList.isEmpty(listAds) && !LList.isEmpty(jobList)) {
                for (RecommendListAdBean listAd : listAds) {
                    if (listAd.index < jobList.size()) {
                        if (listAd.cardType == 12) { // 广告类型，12:自然语言搜索
                            if (!LList.isEmpty(listAd.searchWords)) {
                                switch (listAd.style) {
                                    case 1:
                                        listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_EXPERIMENT_ONE;
                                        break;
                                    case 2:
                                        listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_EXPERIMENT_TWO;

                                        break;
                                    case 3:
                                        listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_EXPERIMENT_THREE;
                                        break;
                                    case 4:
                                        listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_EXPERIMENT_FOUR;
                                        break;
                                }
                                jobList.add(listAd.index, listAd);
                            }
                        } else if (listAd.cardType == 13) { //卡片类型 13:犀牛面对面看机会卡片（1211.500）
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RHINOCEROS_FACE_TO_FACE_LOOK_CHANCE;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == 14) { // 卡片类型：F1做期望变更引导卡片（1215.82）
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_OLD_USER_UPDATE_EXPECT;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == GeekF1Constant.SERVER_F1_ADD_ADD_INDUSTRY) {
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_ADD_INDUSTRY;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == 16) {
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_STUDENT_INTERNSHIP_TEXT;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == GeekF1Constant.SERVER_F1_ADD_SKILL) {
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_ADD_SKILL;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == GeekF1Constant.SERVER_F1_GUIDE_ADD_MORE_EXPECT) {
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_RECOMMEND_AD_GUIDE_ADD_MORE_EXPECT;
                            jobList.add(listAd.index, listAd);
                        } else if (listAd.cardType == GeekF1Constant.SERVER_F1_FEEDBACK) {
                            listAd.itemType = GeekBaseCardBean.TYPE_GEEK_F1_FEEDBACK;
                            jobList.add(listAd.index, listAd);
                        }
                    }
                }
            }
        }

    }


    public static final int OPT_TYPE_EXPOSURE = 0;
    public static final int OPT_TYPE_CLICK_CLOSE = 1;
    public static final int OPT_TYPE_CLICK_OTHER = 2;

    /**
     * @param bean    广告实体
     * @param optType 0-曝光，1-点X，2-点按钮
     */
    public static void doF1CloseRequest(RecommendListAdBean bean, int optType) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_RECOMMEND_LISTAD_CLOSE)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("adId", bean.adId)
                .addParam("cardType", bean.cardType)
                .addParam("optType", optType)
                .execute();
    }


    public static void removeCard(int removePosition, BaseRvAdapter adapter) {
        if (adapter == null) {
            ToastUtils.showText("当前不支持移除");
            return;
        }
        int size = adapter.getData().size();
        if (removePosition >= 0 && removePosition < size) {
            adapter.remove(removePosition);
        } else {
            TLog.info(TAG,"ArrayIndexOutOfBoundsException removePosition=%s ;size =%s",removePosition,size);
        }
    }


    public static ServerCommonButtonBean getButtonViaType(List<ServerCommonButtonBean> buttonList, int actionType) {

        if (!LList.isEmpty(buttonList)) {
            for (ServerCommonButtonBean option : buttonList) {
                if (option != null && option.actionType == actionType) {
                    return option;
                }
            }
        }
        return null;
    }

    public static void updateSettingState(Context context, int notifyType, boolean open, SimpleApiRequestCallback<SuccessResponse> callback) {
        updateSettingState(context, notifyType, 0, open, callback);
    }

    public static void updateSettingState(Context context, int notifyType, int scene, boolean open, SimpleApiRequestCallback<SuccessResponse> callback) {
        if (notifyType == 0) {
            ToastUtils.showText("开关类型错误");
            return;
        }
        UserNotifyUpdateRequest request = new UserNotifyUpdateRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void onStart() {
                if (context instanceof BaseActivity) {
                    ((BaseActivity) context).showProgressDialog();
                }
                super.onStart();
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                callback.onSuccess(data);
            }

            @Override
            public void onComplete() {
                if (context instanceof BaseActivity) {
                    ((BaseActivity) context).dismissProgressDialog();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.notifyType = notifyType;
        request.settingType = open ? UserNotifySetting.FUNCTION_ENABLE : UserNotifySetting.FUNCTION_DISABLE;
        request.scene = scene;
        HttpExecutor.execute(request);
    }

    public static void requestCheckShowHKCollectWindow(SimpleApiRequestCallback<GeekJDPreferenceCollectResponse> callback) {
        //香港职位信息偏好设置
        GeekJDPreferenceCollectRequest preferenceCollectRequest = new GeekJDPreferenceCollectRequest(new ApiRequestCallback<GeekJDPreferenceCollectResponse>() {
            @Override
            public void onSuccess(ApiData<GeekJDPreferenceCollectResponse> data) {
                callback.onSuccess(data);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        preferenceCollectRequest.source = GeekJDPreferenceCollectRequest.SOURCE_F1;
        HttpExecutor.execute(preferenceCollectRequest);
    }


    /**
     * @param optType 0-曝光，1-点X，2-点按钮
     */
    public static void doF1MiddleTipCloseERequest(int type, int optType) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZP_GEEK_APP_F1_MIDDLE_TIP_CLOSE)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("type", type)
                .addParam("optType", optType)
                .execute();
    }

    public static void exposureCommonDialogSubmit(int templateType, long id ,String bizId) {
        exposureCommonDialogSubmit(templateType,id,bizId,null);
    }
   public static void exposureCommonDialogSubmit(int templateType, long id,String bizId,String actionpJson ) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPAPPTIPS_APP_TIP_EXPOSURE)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("templateType", templateType)
                .addParam("id", id)
                .addParam("bizId", bizId)
                .addParam("actionpJson",actionpJson)
                .execute();
    }

    /**
     * F2端弹窗曝光
     * @param templateType 模板类型
     * @param id 弹窗ID
     * @param bizId 业务ID
     * @param actionpJson 操作参数JSON
     */
    public static void exposureF2CommonDialogSubmit(int templateType, long id, String bizId, String actionpJson) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPAPPTIPS_APP_TIP_EXPOSURE_F2)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("templateType", templateType)
                .addParam("id", id)
                .addParam("bizId", bizId)
                .addParam("actionpJson", actionpJson)
                .execute();
    }

    public static void clickCommonDialogSubmit(int templateType, long id,int actionType,String bizId ) {
        clickCommonDialogSubmit(templateType,id,actionType,bizId,null);
    }
    public static void clickCommonDialogSubmit(int templateType, long id,int actionType,String bizId,String actionpJson ) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPAPPTIPS_APP_TIP_CLICK)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("templateType", templateType)
                .addParam("id", id)
                .addParam("bizId",bizId)
                .addParam("actionType",actionType)
                .addParam("actionpJson",actionpJson)
                .execute();
    }

    public static void clickF2CommonDialogSubmit(int templateType, long id, int actionType, String bizId) {
        clickF2CommonDialogSubmit(templateType, id, actionType, bizId, null);
    }

    /**
     * F2端弹窗点击
     * @param templateType 模板类型
     * @param id 弹窗ID
     * @param actionType 操作类型
     * @param bizId 业务ID
     * @param actionpJson 操作参数JSON
     */
    public static void clickF2CommonDialogSubmit(int templateType, long id, int actionType, String bizId, String actionpJson) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPAPPTIPS_APP_TIP_CLICK_F2)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("templateType", templateType)
                .addParam("id", id)
                .addParam("bizId", bizId)
                .addParam("actionType", actionType)
                .addParam("actionpJson", actionpJson)
                .execute();
    }


    public static final int CLOSE_GET_CHARACTER_LABEL_EXPOSE = 0;

    /**
     * 特征卡片曝光上报
     *
     * @param encryptExpectId 期望id
     * @param optType         0:曝光，1:关闭，默认为1:关闭
     * @param adId            卡片曝光参数，optType=0时上传，用于曝光计数
     */
    public static void closeGetCharacterLabel(String encryptExpectId, int optType, String adId) {
        GetCharactLabelCloseRequest closeRequest = new GetCharactLabelCloseRequest(new ApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });

        closeRequest.expectId = encryptExpectId;
        closeRequest.optType = optType;
        closeRequest.adId = adId;
        HttpExecutor.execute(closeRequest);

    }

    public static void closeF1GuideTip(int tipType,int opType) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_GEEK_F1_GUIDE_TIP_CLOSE)
                .addParam("tipType", tipType)
                .addParam("opType",opType)
                .setRequestCallback(
                        new ApiRequestCallback<EmptyResponse>() {
                            @Override
                            public void onStart() {
                                super.onStart();
                            }

                            @Override
                            public void onSuccess(ApiData<EmptyResponse> data) {
                            }

                            @Override
                            public void onComplete() {
                            }

                            @Override
                            public void onFailed(ErrorReason reason) {
                            }
                        }).execute();

        AnalyticsFactory.create().action("geek-mask-openresume-click")
                .param("p", 3)
                .param("p2", 1)
                .debug()
                .build();
    }

    public static void closeF1GuideTip(int tipType) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_GEEK_F1_GUIDE_TIP_CLOSE)
                .addParam("tipType", tipType)
                .setRequestCallback(
                        new ApiRequestCallback<EmptyResponse>() {
                            @Override
                            public void onStart() {
                                super.onStart();
                            }

                            @Override
                            public void onSuccess(ApiData<EmptyResponse> data) {
                            }

                            @Override
                            public void onComplete() {
                            }

                            @Override
                            public void onFailed(ErrorReason reason) {
                            }
                        }).execute();

        AnalyticsFactory.create().action("geek-mask-openresume-click")
                .param("p", 3)
                .param("p2", 1)
                .debug()
                .build();
    }


    /**
     * 是否是对boss 隐藏简历
     */
    public static boolean isHideResumeToBoss() {
        UserBean user = UserManager.getLoginUser();
        return user != null && user.geekInfo != null && user.geekInfo.resumeStatus == 1;
    }


    public static void closeF1Banner(@NonNull ServerBlueCheckTips checkTips, int optionCode) {
        GetF1BannerCloseRequest request = new GetF1BannerCloseRequest(new ApiRequestCallback<GetF1BannerCloseResponse>() {
            @Override
            public void onSuccess(ApiData<GetF1BannerCloseResponse> data) {

                if (data != null && data.resp != null && !TextUtils.isEmpty(data.resp.msg)) {
                    ToastUtils.showText(data.resp.msg);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.tipType = checkTips.tipType;
        request.code = optionCode;
        HttpExecutor.execute(request);
    }

    /**
     * 回流老用户引导更新期望卡片点击事件
     *
     * @param bean    点击的item
     * @param optType optType=0（曝光）、optType=1（忽略/关闭）、optType=2（修改）
     */
    public static void closeF1AdCardRequest(RecommendListAdBean bean, int optType) {
        if (bean != null) {
            GetF1CloseAdCardRequest request = new GetF1CloseAdCardRequest(new ApiRequestCallback<GetF1BannerCloseResponse>() {
                @Override
                public void onSuccess(ApiData<GetF1BannerCloseResponse> data) {

                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.adId = bean.adId;
            request.cardType = bean.cardType;
            request.optType = optType;
            HttpExecutor.execute(request);
        }
    }



    public static void closeF1AdCardRequest(String adId,int cardType, int optType) {
            GetF1CloseAdCardRequest request = new GetF1CloseAdCardRequest(new ApiRequestCallback<GetF1BannerCloseResponse>() {
                @Override
                public void onSuccess(ApiData<GetF1BannerCloseResponse> data) {

                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.adId = adId;
            request.cardType = cardType;
            request.optType = optType;
            HttpExecutor.execute(request);
    }

    public static void showPersonalityRecommendTip(CheckResumeCompleteResponse resumeQuality, TipBar tipBar) {
        if (tipBar == null) {
            TLog.error(TAG, "tipBar is null");
            return;
        }
        if (resumeQuality != null) {
            ServerResumeRestrictBean resumeRestrictBean = resumeQuality.getPersonalityRecommend();
            if (resumeRestrictBean != null) {
                TipManager.Tip tip = new TipManager.Tip();
                tip.content = resumeRestrictBean.tipText;
                ServerButtonBean button = resumeRestrictBean.button;
                if (button != null) {
                    tip.actionText = button.text;
                    tip.actionListener = v -> {
                        SettingRouter.updateNotifySettingSwitch(true, SettingConstants.NOTIFY_TYPE_POSITION_RECOMMENDATION);
                        AnalyticsFactory.bgAction(button.ba);
                    };
                }
                tip.closeOnLeft = true;
                tip.closeListener = v -> {
                    tipBar.remove(GeekF1Constant.F1_TIP_PERSONALITY_RECOMMEND);
                    tipBar.show(GeekF1Constant.F1_TIP_PERSONALITY_RECOMMEND);
                    GeekF1Util.closeF1GuideTip(resumeRestrictBean.tipType);
                    AnalyticsFactory.bgAction(resumeRestrictBean.closeBa);
                };
                tipBar.put(GeekF1Constant.F1_TIP_PERSONALITY_RECOMMEND, tip);
                AnalyticsFactory.bgAction(resumeRestrictBean.exposureBa);
            } else {
                tipBar.remove(GeekF1Constant.F1_TIP_PERSONALITY_RECOMMEND);
            }

        } else {
            tipBar.remove(GeekF1Constant.F1_TIP_PERSONALITY_RECOMMEND);

        }

    }

    public static void showGuideUpdateResumeTip(CheckResumeCompleteResponse resumeQuality, TipBar tipBar,Context context) {
        if (tipBar == null || ActivityUtils.isInvalid(context)) {
            TLog.error(TAG, "tipBar is null");
            return;
        }
        if (resumeQuality != null) {
            ServerResumeRestrictBean resumeRestrictBean = resumeQuality.getGuideUpdateResumeTip();
            if (resumeRestrictBean != null) {
                TipManager.Tip tip = new TipManager.Tip();
                tip.content = resumeRestrictBean.tipText;
                ServerButtonBean button = resumeRestrictBean.button;
                if (button != null) {
                    tip.actionText = button.text;
                    tip.actionListener = v -> {
                        closeF1GuideTip(resumeRestrictBean.tipType,2);
                        new ZPManager(context,button.url).handler();
                        AnalyticsFactory.bgAction(button.ba);
                        tipBar.remove(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);
                        tipBar.show(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);
                    };
                }
                tip.closeOnLeft = true;
                tip.closeListener = v -> {
                    tipBar.remove(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);
                    tipBar.show(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);
                    closeF1GuideTip(resumeRestrictBean.tipType,0);
                    AnalyticsFactory.bgAction(resumeRestrictBean.closeBa);
                };
                tip.style = TipManager.Tip.STYLE_GREEN;
                tipBar.put(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME, tip);
                closeF1GuideTip(resumeRestrictBean.tipType,1);
                AnalyticsFactory.bgAction(resumeRestrictBean.exposureBa);
            } else {
                tipBar.remove(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);
            }

        } else {
            tipBar.remove(GeekF1Constant.TIP_GUIDE_UPDATE_RESUME);

        }

    }

    public static void canAppBarScroll(boolean isScroll, AppBarLayout appBarLayout) {
        if (appBarLayout == null) {
            return;
        }
        View appBarChildAt = appBarLayout.getChildAt(0);
        if (appBarChildAt != null) {
            ViewGroup.LayoutParams layoutParams = appBarChildAt.getLayoutParams();
            if (layoutParams instanceof AppBarLayout.LayoutParams) {
                AppBarLayout.LayoutParams appBarParams = (AppBarLayout.LayoutParams) layoutParams;
                if (isScroll) {
                    appBarParams.setScrollFlags(AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL | AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED);
                    appBarChildAt.setLayoutParams(appBarParams);
                } else {
                    appBarParams.setScrollFlags(0);
                }
            }

        }
    }


    public static String getTopicContentType(List<ServerTopicBannerBean> list) {
        String para = "";
        if (!LList.isEmpty(list)) {
            for (ServerTopicBannerBean bean : list) {
                para = StringUtil.connectTextWithChar(",", para, String.valueOf(bean.type));
            }
        }
        return para;
    }


    /**
     * @param type      弹窗类型
     * @param closeType 动作 0:关闭，1:点击，2:曝光
     */
    public static void submitF1TipCloseFrequency(int type, int closeType) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_GEEK_CLOSE_PARTTIME_TIP).addParam("type", type).addParam("closeType", closeType).execute();
    }


    public static void setJobNameWithImageTag(ServerJobCardBean job, MTextView tvTitle, Context context) {
        if (context==null || job ==null || tvTitle==null){
            TLog.error(TAG,"job or tvTitle or context is null");
            return;
        }
        List<Bitmap> beforeTags = new ArrayList<>();
        List<Bitmap> afterTags = new ArrayList<>();
        tvTitle.setMaxLines(Math.max(job.jobNameLineNumber, 1)); //如果服务端没有给，最少保留一行，给了按服务端的走
        tvTitle.setEllipsize(TextUtils.TruncateAt.END);
        if (!LList.isEmpty(job.beforeNameIcons) || !LList.isEmpty(job.afterNameIcons)) {
            AppThreadFactory.POOL.execute(() -> {
                requestImgTag(job.beforeNameIcons, beforeTags, context);
                requestImgTag(job.afterNameIcons, afterTags, context);
                Utils.runOnUiThread(() -> {
                    if (ActivityUtils.isInvalid(context)){
                        return;
                    }

                    if ((!isBitmapUseless(beforeTags) || !isBitmapUseless(afterTags)) && !LText.isEmptyOrNull(job.jobName)) {
                        handleTextIcons(tvTitle, job.jobName, afterTags, beforeTags,context);
                    } else {
                        tvTitle.setText(job.jobName, View.GONE);
                    }
                });
            });
        } else {
            tvTitle.setText(job.jobName, View.GONE);
        }
    }

    // 复制自 JobCardView  handleTextIcons  范少写的很高端
    private static void handleTextIcons(MTextView textview, String defaultText, List<Bitmap> afterNameIconBitmaps, List<Bitmap> beforeNameIcons, Context context) {

        SpannableStringBuilder stringBuilder = new SpannableStringBuilder();

        // 前面加标签
        final String beforeRegex = "@%";
        if (!LList.isEmpty(beforeNameIcons)) {
            for (int i = 0; i < beforeNameIcons.size(); i++) {
                stringBuilder.append(" " + beforeRegex + " ");
            }
        }

        stringBuilder.append(defaultText);


        // 后面加标签
        final String placeRegex = "&";
        if (!LList.isEmpty(afterNameIconBitmaps)) {
            for (int i = 0; i < afterNameIconBitmaps.size(); i++) {
                stringBuilder.append(" " + placeRegex + " ");
            }
        }

        Pattern patternBefore = Pattern.compile(beforeRegex);
        Matcher matcherBefore = patternBefore.matcher(stringBuilder);
        int index = 0;
        while (matcherBefore.find() && index < beforeNameIcons.size()) {
            Bitmap bitmap = LList.getElement(beforeNameIcons, index);
            if (bitmap != null && !bitmap.isRecycled()) {
                CenterImageSpan imageSpan = new CenterImageSpan(context, beforeNameIcons.get(index), 2);
                stringBuilder.setSpan(imageSpan, matcherBefore.start(), matcherBefore
                        .end(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

                index++;
            }

        }


        Pattern pattern = Pattern.compile(placeRegex);
        Matcher matcher = pattern.matcher(stringBuilder);
        index = 0;
        while (matcher.find() && index < afterNameIconBitmaps.size()) {
            Bitmap bitmap = LList.getElement(afterNameIconBitmaps, index);
            if (bitmap != null && !bitmap.isRecycled()) {
                CenterImageSpan imageSpan = new CenterImageSpan(context, afterNameIconBitmaps.get(index), 2);
                stringBuilder.setSpan(imageSpan, matcher.start(), matcher
                        .end(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

                index++;
            }

        }
        textview.setText(stringBuilder, View.GONE);

    }


    private static boolean isBitmapUseless(List<Bitmap> bitmapList) {
        if (LList.isEmpty(bitmapList)) {
            return true;
        }
        for (Bitmap targetBitmap : bitmapList) {
            if (targetBitmap.isRecycled()) {
                return true;
            }
        }
        return false;

    }

    private static void requestImgTag(List<ServerAfterNameIconBean> iconUrls, List<Bitmap> tags, Context context) {
        SoftReference<Context> contextReference = new SoftReference<Context>(context);
        if (LList.isEmpty(iconUrls)) {
            return;
        }
        for (ServerAfterNameIconBean tagBean : iconUrls) {

            // 接口url可能返回空，Uri.parse(null)的时候会throw exception
            if (tagBean != null && !TextUtils.isEmpty(tagBean.url)) {
                ImageRequest imageRequest = ImageRequestBuilder
                        .newBuilderWithSource(Uri.parse(tagBean.url))
                        .build();

                ImagePipeline imagePipeline = Fresco.getImagePipeline();

                DataSource<CloseableReference<CloseableImage>> dataSource =
                        imagePipeline.fetchDecodedImage(imageRequest, null);
                try {
                    CloseableReference<CloseableImage> result = DataSources.waitForFinalResult(dataSource);
                    if (result != null) {
                        CloseableImage closeableImage = result.get();
                        if (closeableImage instanceof CloseableBitmap && contextReference.get() != null) {
                            Bitmap bitmap = ((CloseableBitmap) closeableImage).getUnderlyingBitmap();
                            bitmap.setDensity(ZPUIDisplayHelper.getDisplayMetrics(contextReference.get()).densityDpi);
                            Bitmap handledBitmap = BitmapUtil.changeBitmapSize(bitmap, ZPUIDisplayHelper.dp2px(contextReference.get(), 15));
                            tags.add(handledBitmap);
                        }
                    }
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                } finally {
                    dataSource.close();
                }
            }
        }
    }

    private static int activeInfoPixel =-1;

    public static int getActiveInfoPixel() {
        if (activeInfoPixel==-1){
            UserBean loginUser = UserManager.getLoginUser();
            if (loginUser != null) {
                GeekInfoBean geekInfo = loginUser.geekInfo;
                if (geekInfo != null) {
                    GeekFeature geekFeature = geekInfo.geekFeature;
                    if (geekFeature != null) {
                        activeInfoPixel = geekFeature.activeInfoPixel;
                    }
                }
            }
        }
        return   activeInfoPixel;
    }

    private static int f1PreferSortType =-1;

    public static int getF1PreferSortType() {
        if (f1PreferSortType==-1){
            UserBean loginUser = UserManager.getLoginUser();
            if (loginUser != null) {
                GeekInfoBean geekInfo = loginUser.geekInfo;
                if (geekInfo != null) {
                    GeekFeature geekFeature = geekInfo.geekFeature;
                    if (geekFeature != null) {
                        f1PreferSortType = geekFeature.f1PreferSortType;
                    }
                }
            }
        }
        return   f1PreferSortType;
    }

    /**
     * 获取猎头职位助手响应数据
     * 
     * @return HunterAdvGeekJobAssistantResponse 猎头职位助手响应数据，如果不存在则返回null
     */
    public static HunterAdvGeekJobAssistantResponse getHunterAdvGeekJobAssistantResponse() {
//mock的数据
//        HunterAdvGeekJobAssistantResponse hunterAdvGeekJobAssistantResponse=new HunterAdvGeekJobAssistantResponse();
//        hunterAdvGeekJobAssistantResponse.showBubble=1;
//        hunterAdvGeekJobAssistantResponse.bubbleText ="Offer小伴有新推荐";
//        hunterAdvGeekJobAssistantResponse.url ="https://www.zhipin.com/hunter/adv/geek/jobassistant/";
//        hunterAdvGeekJobAssistantResponse.icon ="https://img.bosszhipin.com/beijin/hunter/20250423/b04b41490bfc0ab204b55b5bf17063c84aed889a23227fafd34184f2e980c1514cf4d8ab3e99c0f3.png.webp";
//        hunterAdvGeekJobAssistantResponse.showEntrance=1;
//
//        return hunterAdvGeekJobAssistantResponse;

        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser != null) {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo != null) {
                return geekInfo.hunterAdvGeekJobAssistantResponse;
            }
        }
        return null;
    }

    private static int f1MultiCity = -1;

    private static int getF1MultiCity() {
        if (f1MultiCity == -1) {
            UserBean loginUser = UserManager.getLoginUser();
            if (loginUser != null) {
                GeekInfoBean geekInfo = loginUser.geekInfo;
                if (geekInfo != null) {
                    GeekFeature geekFeature = geekInfo.geekFeature;
                    if (geekFeature != null) {
                        f1MultiCity = geekFeature.f1MultiCity;
                    }
                }
            }
        }
        return f1MultiCity;
    }

    public static boolean isSupportF1MultiCity(){
        return  getF1MultiCity() ==1;
    }


    final static List<String> specialCitys = Arrays.asList("吉林", "三沙", "西安", "中山");

    public static String getCityWithSuffix(String city) {
        if (!AndroidDataStarGray.getInstance().isCityWithSuffix()){
            return city;
        }

        if (TextUtils.isEmpty(city)) {
            return "";
        }
        if (specialCitys.contains(city)) {
            return String.format("%s市", city);
        } else {
            return city;
        }
    }


    public static CityBean getCityBean(JobIntentBean currExpectJob ,CityBean cityBean){
        if (cityBean!=null){
            return cityBean;
        }else {
            if (currExpectJob!=null){
                return new CityBean(currExpectJob.locationIndex,currExpectJob.locationName);
            }
        }
        return null;

    }

    /**
     * 判断是否有商圈
     */
    public static void  judgeHasBusiness(long cityCode,String position,String encryptExpectId,String scene,ApiRequestCallback<GetGeekBusinessDistrictResponse> callback){
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPGEEK_APP_BUSINESSDISTRICT)
                .addParam("city", cityCode)
                .addParam("position", position)//从搜索过来的不传 position和 encryptExpectId
                .addParam("encryptExpectId",  encryptExpectId)
                .addParam("scene", scene)/*@since 1002   调用场景，0:默认， 1:F1，默认值为0*/
                .addParam("ignoreAction",1)
                .setRequestCallback(new SimpleApiRequestCallback<GetGeekBusinessDistrictResponse>() {
                    @Override
                    public void onSuccess(ApiData<GetGeekBusinessDistrictResponse> data) {
                        super.onSuccess(data);
                        if (callback!=null){
                            callback.onSuccess(data);
                        }
                    }
                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        if (callback!=null){
                            callback.onFailed(reason);
                        }
                    }
                })
                .execute();
    }


    public static int getSysNotifySwitch(Context context){
        if (ActivityUtils.isInvalid(context)){
            return GeekF1DialogRequestParams.SYS_NOTIFY_SWITCH_CLOSE;
        }

       return NotificationCheckUtils.areNotificationsEnabled(context)?GeekF1DialogRequestParams.SYS_NOTIFY_SWITCH_OPEN:GeekF1DialogRequestParams.SYS_NOTIFY_SWITCH_CLOSE;
    }



    public static void updatePositionNoLimitSwitch(int status,SimpleApiRequestCallback<EmptyResponse> simpleApiRequestCallback) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_ZPGEEK_APP_F1_POSITION_SWITCH_UPDATE)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (simpleApiRequestCallback!=null){
                            simpleApiRequestCallback.onSuccess(data);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("status", status)
                .execute();
    }



    public static long getCityCodeByRegeocodeAddress(RegeocodeAddress regeocodeAddress) {
        if (regeocodeAddress == null) {
            return 0L;
        }
        String localCityCode;
        //兜底处理，高德SDK 关于省直辖县级市的问题 ：https://zhishu.zhipin.com/docs/Hfigka6SC2f
        if (AndroidDataStarGray.getInstance().getMapConfig().is_fix_city_province) {
            localCityCode = LBase.getCityCode(regeocodeAddress.getCity(), regeocodeAddress.getDistrict());
        } else {
            localCityCode = LBase.getCityCode(regeocodeAddress.getCity());
        }
        return LText.getLong(localCityCode);
    }


    public static long getCityCodeByRegeocodeAddress(com.hpbr.bosszhipin.map.search.geocode.RegeocodeAddress regeocodeAddress) {
        if (regeocodeAddress == null) {
            return 0L;
        }
        String localCityCode;
        //兜底处理，高德SDK 关于省直辖县级市的问题 ：https://zhishu.zhipin.com/docs/Hfigka6SC2f
        if (AndroidDataStarGray.getInstance().getMapConfig().is_fix_city_province) {
            localCityCode = LBase.getCityCode(regeocodeAddress.getCity(), regeocodeAddress.getDistrict());
        } else {
            localCityCode = LBase.getCityCode(regeocodeAddress.getCity());
        }
        return LText.getLong(localCityCode);
    }


    public static void checkPoiMatchCity(String areaName, String cityName,String provinceName,  String matchCityCode,  ApiRequestCallback<PoiMatchCityResponse> callback) {
        PoiMatchCityRequest request = new PoiMatchCityRequest(new ApiRequestCallback<PoiMatchCityResponse>() {

            @Override
            public void onSuccess(ApiData<PoiMatchCityResponse> data) {
                if (callback!=null){
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback!=null){
                    callback.onFailed(reason);
                }
            }
        });

        request.areaName = areaName;
        request.cityName = cityName;
        request.provinceName = provinceName;
        request.matchCityCode = matchCityCode;
        HttpExecutor.execute(request);
    }

    /**
     * 弹窗信息频次控制
     * 对应接口：https://api.weizhipin.com/project/30/interface/api/774259
     *
     * @param type 弹窗类型，从/api/zpapptips/app/tip/get/popup/info中返回的type
     * @param optType 操作类型：0-曝光记录（默认）、1-确认相关操作记录、2-关闭相关操作记录
     * @param actionpJson 操作相关的JSON数据
     * @param cityCode 城市编码（可选）
     */
    public static void closePopupInfoSubmit(int type, int optType, String actionpJson, String cityCode) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_ZPAPPTIPS_APP_TIP_GET_POPUP_INFO_CLOSE)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                })
                .addParam("type", type)
                .addParam("optType", optType)
                .addParam("actionpJson", actionpJson)
                .addParam("cityCode", cityCode)
                .execute();
    }



    public static int getDividerInfoIndex(List<GeekBaseCardBean> adapterList){
        int  index =-1;
        if (!LList.isEmpty(adapterList)){
            for (int i = 0; i < adapterList.size(); i++) {
                GeekBaseCardBean baseCardBean =adapterList.get(i);
                if (baseCardBean instanceof ServerJobCardBean){
                    ServerJobCardBean serverJobCardBean = (ServerJobCardBean) baseCardBean;
                    ServerJobDividerInfo dividerInfo = serverJobCardBean.dividerInfo;
                    if(dividerInfo!=null){
                        TLog.info(TAG,"getDividerInfoIndex dividerInfo =%s; index =%s",dividerInfo.toString(),i);
                        if (dividerInfo.side==1){
                            index =i;
                            break;
                        }else if (dividerInfo.side==2){
                            index =i+1;

                            if (index<adapterList.size()){
                                GeekBaseCardBean nextBaseCardBean =adapterList.get(index);
                                if (nextBaseCardBean instanceof ServerJobCardBean){
                                    ServerJobCardBean nextServerJobCardBean = (ServerJobCardBean) nextBaseCardBean;
                                    ServerJobDividerInfo nextDividerInfo =new ServerJobDividerInfo();
                                    nextDividerInfo.side =1;
                                    nextDividerInfo.type =dividerInfo.type;
                                    nextDividerInfo.memo =dividerInfo.memo;
                                    serverJobCardBean.dividerInfo =null;
                                    nextServerJobCardBean.dividerInfo =nextDividerInfo;
                                    break;
                                }else {
                                    index=-1;
                                }
                            }
                        }
                    }
                }
            }
        }

        return index;
    }


    public static void postPositionFeedBack(int type,int action,String securityId,String uuid,String lid,String strategyId ) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_GEEK_CHAT_RCMD_JOBLIST_CLOSE)
                .addParam("type", type)
                .addParam("action", action)
                .addParam("securityId", securityId)
                .addParam("uuid", uuid)
                .addParam("lid", lid)
                .addParam("strategyId", strategyId)
                .execute();

    }

    public static String getHideHeadhunterTipString() {
        String str = "";
        boolean hideHead = !NotifyUtils.isForHeadHunterHide(); // 对猎头顾问隐藏简历
        boolean hideIntermediary = !NotifyUtils.isIntermediary(); // 对中介等经纪人隐藏简历
        if (hideHead && hideIntermediary) {
            str = "您已对猎头及人才经纪人隐藏简历";
        } else if (hideHead) {
            str = "您已对猎头隐藏简历";
        } else if (hideIntermediary) {
            str = "您已对人才经纪人隐藏简历";
        }
        return str;
    }

    public  static boolean isUseAMap(){
        return AndroidDataStarGray.getInstance().isUseNewGeekAddAddressType();
    }

}
