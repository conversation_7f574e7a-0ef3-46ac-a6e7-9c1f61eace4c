package com.hpbr.bosszhipin.utils;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.WebSiteConfig;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.main.entity.ItemBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.report.AutoReport;
import com.hpbr.bosszhipin.weiget.CenterImageSpan;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.tencent.bugly.crashreport.CrashReport;

import net.bosszhipin.api.bean.HighlightItem;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by monch on 15/4/1.
 */
public class StringUtil {

    /**
     * 把关键词的List集合转换成String
     */
    public static final String SPLIT_CHAR_KEYWORD = "#&#"; // 分隔符"#\u0026#"
    public static final String COMMON_SEPERATOR = " · "; // 常用分隔符
    public static final String COMMON_SEPERATOR2 = "·"; // 常用分隔符
    public static final String COMMON_BIG_DOT = " • "; // 大点
    public static final String SPLIT_CHAR_COMMA = ","; // 英文的逗号分隔符
    public static final String SPLIT_CHAR_COMMA2 = "、"; // 中文顿号分隔符
    public static final String SPLIT_CHINESE_CHAR_COMMA = "，"; // 中文逗号分隔符
    public static final String LEFT_QUOT = "「";
    public static final String RIGHT_QUOT = "」";
    public static final String VERTICAL_LINE = " | ";
    public static final String SPLIT_CHAR_COLON = ":";
    public static final String SPLIT_CHINESE_CHAR_COLON = "：";
    public static final String SPLIT_CHAR_DASH = "-";
    public static final String SPLIT_CHAR_UNDERLINE = "_";
    public static final String SEMICOLON = ";";
    public static final String SPACE = " ";
    public static final String DOT = ".";
    public static final String NEW_LINE = "\r\n";
    public static final String SPLIT_UNDER_LINE = "_";
    public static final String SPLIT_CHAR_TILDE = "~";
    public static final String SPLIT_CHAR_AND = "&";
    public static final String SPLIT_CHAR_QUESTION = "?";

    // 计算有几个unicode字
    private static final Pattern NORMAL_CODE_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]"); // [^\x00-\xff]
    private static final Pattern WIDTH_CODE_PATTERN = Pattern.compile("[^\\x00-\\xff]"); // [^\x00-\xff]


    /**
     * 这个方法有兼容深色模式的问题， 请注意使用
     *
     * @param resId
     * @return
     */
    @Deprecated
    public static Uri getResouceUri(int resId) {
        return Uri.parse("res://" + App.get().getPackageName() + "/" + resId);
    }

    public static Uri getFileUri(File file) {
        if (file == null || !file.exists()) return null;
        return Uri.parse("file://" + file.getAbsolutePath());
    }

    public static Uri getFileUri(String filePath) {
        return Uri.parse("file://" + filePath);
    }

    public static Uri getNetworkUri(String url) {
        Uri uri = null;
        if (!LText.empty(url)) {
            uri = Uri.parse(url);
        }
        return uri;
    }

    public static String parsePhoneNumberOld(String text) {
        if (LText.empty(text)) return null;
        if (text.contains(":") || text.contains("：")) {
            String[] strs1 = text.split(":");
            if (strs1.length == 2) {
                if (LText.isMobile(strs1[1])) {
                    return strs1[1];
                }
            }
            String[] strs2 = text.split("：");
            if (strs2.length == 2) {
                if (LText.isMobile(strs2[1])) {
                    return strs2[1];
                }
            }
        }
        return null;
    }


    public static String createCombineRegionCodeText(@Nullable ChatBean chatBean, boolean nextLine) {

        if (chatBean == null) return "";

        final String reginCode = StringUtil.parsePhoneReginCode(chatBean);
        boolean isAdd86Number = LText.equal(reginCode, "+86");

        ChatMessageBodyBean messageBodyBean = chatBean.message.messageBody;
        String text = messageBodyBean.text;

        String splitText = "：";

        /*如果是境外号码 需要手动补充境外 区域*/
        if (!LText.empty(reginCode) && !isAdd86Number) {
            String[] split = text.split(splitText);
            if (split.length == 2) {

                text = split[0] + splitText + reginCode + " " + split[1];

                if (nextLine) {
                    text = text.replace(splitText, "：\r\n");
                }

            }
        }

        return text
                .replace("<phone>", "")
                .replace("</phone>", "");
    }


    public static @Nullable String parsePhoneReginCode(@Nullable ChatBean chatBean) {
        if (chatBean == null) return null;
        if (chatBean.message == null) return null;
        if (chatBean.message.messageBody == null) return null;
        if (chatBean.message.messageBody.extend == null) return null;

        String extend = chatBean.message.messageBody.extend;

        if (LText.empty(extend)) return null;

        try {
            JSONObject jsonObject = new JSONObject(extend);
            return jsonObject.optString("regionCode");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String parsePhoneNumber(String text) {
        if (LText.empty(text)) return null;
        String phoneTag1 = "<phone>";
        String phoneTag2 = "</phone>";
        if (!text.contains(phoneTag1) || !text.contains(phoneTag2)) {
            return null;
        }
        int startIndex = text.indexOf(phoneTag1) + phoneTag1.length();
        int endIndex = text.indexOf(phoneTag2);
        if (startIndex < endIndex && startIndex < text.length() && endIndex <= text.length()) {
            return text.substring(startIndex, endIndex);
        }
        return null;
    }

    public static String parseWechatNumber(String text) {
        return parserTagContent(text, "<copy>", "</copy>");
    }

    public static String parserPhoneNumber(String text) {
        return parserTagContent(text, "<phone>", "</phone>");
    }

    public static String parserTagContent(String text, String startTag, String endTag) {
        if (LText.empty(text) || LText.empty(startTag) || LText.empty(endTag)) {
            return "";
        }

        if (text.contains(startTag) && text.contains(endTag)) {
            int start = text.indexOf(startTag) + startTag.length();
            int end = text.indexOf(endTag);
            if (start < end && start < text.length() && end <= text.length()) {
                return text.substring(start, end);
            }

        }

        return "";
    }

    /**
     * InputStream转换为String
     *
     * @param inputstream
     * @return
     * @throws IOException
     */
    public static String inputStreamToString(InputStream inputstream) {
        return readFully(new InputStreamReader(inputstream, Charset.forName("UTF-8")));
    }

    private static String readFully(Reader reader) {
        StringWriter sw = new StringWriter();
        try {
            char ac[] = new char[1024];
            do {
                int i = reader.read(ac);
                if (i == -1)
                    break;
                sw.write(ac, 0, i);
            } while (true);

            return sw.toString();
        } catch (Exception e) {
            MException.printError(e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    MException.printError(e);
                }
            }
            try {
                sw.close();
            } catch (Exception e) {
                MException.printError(e);
            }
        }
        return "";
    }

    /**
     * 算unicode字节,区分中英文
     *
     * @param str
     * @return
     */
    public static int getChineseCount(CharSequence str) {
        int count = 0;
        if (!TextUtils.isEmpty(str)) {
            int charCode;
            for (int i = 0; i < str.length(); i++) {
                charCode = Character.codePointAt(str, i);
                if (charCode > 255) {// 大于255的包括中文字、全角符号、特殊字
                    count += 1;
                }
            }
            count += str.length();
        }
        return count;
    }

    /**
     * 获取字符串长度，汉字2个字符，英文1个字符
     */
    public static int getLength(String str) {
        if (TextUtils.isEmpty(str)) {// 字符串为空
            return 0;
        }

        int totalLength = 0;

        char[] array = str.toCharArray();
        for (char c : array) {
            if ((c & 0xffff) <= 0xff) {
                totalLength = totalLength + 1;
            } else {
                totalLength = totalLength + 2;
            }
        }

        return totalLength;
    }

    public static String cutContent(String content, int length) {
        if (TextUtils.isEmpty(content)) {
            return "";
        }

        if (content.length() > length) {
            return content.substring(0, length).concat("...");
        }

        return content;
    }


    /**
     * 截取字符串
     *
     * @param origin 原始字符串
     * @param len    截取长度，汉字2个字符，英文1个字符
     */
    public static String subString(String origin, int len) {
        try {
            if (TextUtils.isEmpty(origin) || (len < 1)) {// 字符串为空
                return "";
            }

            if (len > getLength(origin)) {// 截取长度大于字符串长度
                return origin;
            }

            StringBuffer buffer = new StringBuffer();
            char[] array = origin.toCharArray();
            double currentLength = 0;
            for (char c : array) {
                if ((c & 0xffff) <= 0xff) {
                    currentLength = currentLength + 1;
                } else {
                    currentLength = currentLength + 2;
                }

                if (currentLength <= len) {
                    buffer.append(c);
                } else {
                    break;
                }
            }

            return buffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static String joinKeywords(List<String> list) {
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i));
                } else {
                    sb.append(list.get(i)).append(SPLIT_CHAR_KEYWORD);
                }
            }
            return new String(sb);
        } else {
            return "";
        }
    }

    /**
     * 把String类型的集合元素，按splitCharType符合进行拼接
     *
     * @param list
     * @param splitCharType
     * @return
     */
    public static String joinKeywords(List<String> list, String splitCharType) {
        if (TextUtils.isEmpty(splitCharType)) {
            return joinKeywords(list);
        }
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i));
                } else {
                    sb.append(list.get(i)).append(splitCharType);
                }
            }
            return new String(sb);
        } else {
            return "";
        }
    }

    /**
     * 把String关键词转换成为List集合,
     * 该方法不建议使用，建议使用{@link #splitRemoveDuplicateKeywords(String)}替换，
     * 测试中发现部分keyWords有重复
     *
     * @param string
     * @return
     */
    public static List<String> splitKeywords(String string) {
        if (!TextUtils.isEmpty(string)) {
            List<String> list = new ArrayList<>();
            String[] array = string.split(SPLIT_CHAR_KEYWORD);
            for (int i = 0; i < array.length; i++) {
                list.add(array[i]);
            }
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 把String关键词转换成为List集合,
     *
     * @param string
     * @return
     */
    @NonNull
    public static List<String> splitKeywords(String string, String splitCharType) {
        if (!TextUtils.isEmpty(string)) {
            List<String> list = new ArrayList<>();
            if (splitCharType == null) return list;
            String[] array = string.split(splitCharType);
            if (array == null || array.length == 0) return list;
            for (int i = 0; i < array.length; i++) {
                list.add(array[i]);
            }
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 把String关键词转换成为Set集合,保证,去重，有序
     *
     * @param string
     * @return
     */
    public static Set<String> splitRemoveDuplicateKeywords(String string) {
        Set<String> data = new LinkedHashSet<>();
        if (!TextUtils.isEmpty(string)) {
            String[] array = string.split(SPLIT_CHAR_KEYWORD);
            for (String str : array) {
                if (LText.empty(str)) continue;
                data.add(str);
            }
        }
        return data;
    }

    /**
     * 从字符串里面提取数字
     *
     * @param str
     * @return
     */
    public static String getNumberFromString(String str) {
        Pattern pattern = Pattern.compile("[^0-9]");
        Matcher Matcher = pattern.matcher(str);
        return Matcher.replaceAll("").trim();
    }

    /**
     * @return
     */
    public static String getFormattedCount(long count) {
        String numStr;
        if (count <= 0) {
            numStr = "0";
        } else {
            numStr = count + "";
            if (count > 999 && count <= 99000) {
                if (count % 1000 == 0) {
                    numStr = count / 1000 + "k";
                } else {
                    float fNum = count;
                    if (fNum >= 9999) {
                        numStr = String.format("%.0fk", fNum / 1000.0);
                    } else {
                        numStr = String.format("%.1fk", fNum / 1000.0);
                    }
                }
            } else if (count > 99000) {
                numStr = "99k";
            }
        }
        return numStr;
    }

    /**
     * 把地点字段格式化，超过四个字的都格式化成四个字
     *
     * @param location
     * @return
     */
    public static String setSubstringLocation(String location) {
        if (LText.empty(location)) return "";

        if (location.length() > 4) {
            location = location.substring(0, 4);
        }
        return location;
    }

    /**
     * 文字换行
     *
     * @param textView
     * @param mWidth
     * @return
     */
    public static String getNewLineText(TextView textView, List<String> list, int mWidth) {
//        final float textViewWidth = 1080 - 40 * 2;    // TextView的宽度，需要自己计算
        if (LList.getCount(list) <= 0) return "";
        final String separate = "丨";
        StringBuilder sb = new StringBuilder();
        // 循环字符数组
        for (String text : list) {
            if (TextUtils.isEmpty(text)) continue;
            // 获取最后一个换行符的位置
            int beforeEnterLocation = sb.toString().lastIndexOf("\n");
            // 如果最后一个换行符的位置小于零，证明未换过行，将变量置为0
            if (beforeEnterLocation < 0) beforeEnterLocation = 0;
            // 获取当前行需要计算行宽的字符串
            String computeText = sb.substring(beforeEnterLocation, sb.length()) + text;
            // 计算当前行字符的宽度
            float computeWidth = getTextViewLength(textView, computeText);
            // 当前行字符宽度是否大于控件的宽度
            if (computeWidth >= mWidth) {
                // 去掉前一段的分隔符
                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                // 添加换行
                sb.append("\n");
            }
            // 添加
            sb.append(text).append(separate);
        }
        // 删除最后的分隔符
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private static float getTextViewLength(TextView textView, String text) {
        TextPaint paint = textView.getPaint();
        // 得到使用该paint写上text的时候,像素为多少
        return paint.measureText(text);
    }

    /**
     * 去掉字符串中的空格（针对网址）
     *
     * @param str
     * @return
     */
    public static String removeAllSpaces(String str) {
        if (TextUtils.isEmpty(str)) return "";
        return str.replace(" ", "");
    }

    /**
     * 检测URL是否是合法，并且返回对应的图片资源ID
     *
     * @param url 要检测的路径
     * @return Object数组，第0位为布尔值，true为合法，false为不合法，第1位为图片资源ID，整型
     */
    public static int checkUrlAndIcon(String url) {
        return WebSiteConfig.getIconResId(url);
    }

    public static WebSiteConfig.TempEntity getUrlAndIcon(String url) {
        return WebSiteConfig.getIconTempEntity(url);
    }

    public static int getRMB(int value) {
        if (value < 0) return 0;
        return value / 100;
    }

    /**
     * 获取人民币（元.角分）
     *
     * @param value 已单位为分传入的值
     * @return
     */
    public static String getStringRMB(int value) {
        return getStringRMB(value, false);
    }

    public static String getStringRMB(int value, boolean withZeroFormat) {
        if (value <= 0) {
            return withZeroFormat ? "0.00" : "0";
        } else {
            double d = LText.getDouble((double) value / 100 + "");
            BigDecimal b = new BigDecimal(d);
            double money = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            DecimalFormat df = new DecimalFormat("#####0.00");
            return df.format(money);
        }
    }

    /**
     * 金额为整数元时，显示整数金额，否则展示以为小数金额
     *
     * @param value
     * @param withZeroFormat
     * @return
     */
    public static String getStringRMBWithOneOrNone(int value, boolean withZeroFormat) {
        if (value <= 0) {
            return withZeroFormat ? "0.0" : "0";
        } else {
            if (value % 100 == 0) {
                return value / 100 + "";
            } else {
                double d = LText.getDouble((double) value / 100 + "");
                BigDecimal b = new BigDecimal(d);
                double money = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                DecimalFormat df = new DecimalFormat("#####0.0");
                return df.format(money);
            }
        }
    }

    /**
     * 获取人民币（元.角分）
     *
     * @param value 已单位为分传入的值
     * @return
     */
    public static double getDoubleRMB(int value) {
        if (value <= 0) return 0;
        double d = LText.getDouble((double) value / 100 + "");
        BigDecimal b = new BigDecimal(d);
        return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static SpannableStringBuilder formatCountString(@IntRange(from = 0) int count) {
        SpannableStringBuilder builder;
        if (count > 99999) {
            count = 10;
            String suffix = "w+";
            String totalString = count + suffix;
            builder = new SpannableStringBuilder(totalString);
            builder.setSpan(new RelativeSizeSpan(0.65f), suffix.length(), totalString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        } else {
            if (count < 0) {
                count = 0;
            }
            builder = new SpannableStringBuilder(String.valueOf(count));
        }
        return builder;
    }

    /**
     * 人数≥十万显示10w+，＜10w展示具体数值，每3位数有逗号分隔
     */
    public static String formatCnt(int count) {
        if (count >= 100000) {
            return "10w+";
        } else {
            String strCount = String.valueOf(count);

            if (count >= 1000) {
                return (count / 1000) + "," + strCount.substring(strCount.length() - 3);
            }
        }
        return String.valueOf(count);
    }

    /**
     * 点赞数（需求：最多为99W+，1W到99W之间，展示1.1W、10.1W）
     */
    public static String formatAudienceApplaud(int count) {
        int W = 10000;//一万

        if (count < 1 * W) {//如果小于1万，就展示实际的数量
            return String.valueOf(count);
        } else if (count > 99 * W) {//如果大于99万，就展示99w+
            return "99w+";
        } else {//如果介于 1w—99w之间
            // 整数
            int integer = count / W;
            // 第一位小数
            int firstDecimal = (count - integer * W) / 1000;

            if (firstDecimal > 0) {
                return String.format(Locale.getDefault(), "%d.%d%s", integer, firstDecimal, "w");
            } else {
                return String.format(Locale.getDefault(), "%d%s", integer, "w");
            }
        }
    }

    public static String getTipCountText(int count) {
        return count > 99 ? "99+" : String.valueOf(count);
    }


    public static String getNoneReadText(int count) {
        if (count <= 0) {
            return "";
        }
        return count > 99 ? "..." : String.valueOf(count);
    }


    /**
     * 道具描述
     *
     * @param item
     * @return
     */
    public static String getItemExpireDesc(ItemBean item) {
        String sCount = item.itemCount > 99 ? "99+" : item.itemCount + "";
        String strDesc = "已有" + sCount + "件";
        //显示有效期
        if (item.showExpireDate != 0) {
            strDesc += " 有效期" + item.expireDesc;
        }
        return strDesc;
    }

    /**
     * 根据最大字符返回文字
     *
     * @param text
     * @param maxChar
     * @return
     */
    public static String getMaxCharText(String text, int maxChar) {
        return getMaxCharText(text, maxChar, true);
    }


    /**
     * 根据最大字符返回文字
     *
     * @param text
     * @param maxChar
     * @param distinguish 是否区分中英文
     * @return
     */
    public static String getMaxCharText(String text, int maxChar, boolean distinguish) {
        int index = 0;
        if (!LText.empty(text)) {
            final int length = text.length();
            int count = 0;
            for (int i = 0; i < length; i++) {
                char c = text.charAt(i);
                if (distinguish && isChinese(c)) {
                    count += 2;
                } else {
                    count += 1;
                }
                if (count >= maxChar) {
                    index = i;
                    break;
                }
            }
            if (index != 0) {
                boolean appendEnd = ++index == length;
                String substring = text.substring(0, index);
                if (!appendEnd) substring += "...";
                return substring;
            }
        }
        return text;
    }

    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }

    public static boolean isAlpha(@NonNull String name) {
        return name.matches("[a-zA-Z]+");
    }

    /**
     * F1,F2筛选参数用,隔开
     *
     * @param filterBean
     * @return
     */
    public static String getFilterCode(FilterBean filterBean) {
        return getFilterCode(filterBean, false);
    }

    /**
     * F1,F2筛选参数用,隔开
     *
     * @param filterBean
     * @return
     */
    public static String getFilterCode(FilterBean filterBean, boolean isStrCode) {
        StringBuilder sb;
        if (filterBean != null) {
            List<FilterBean> subFilterList = filterBean.subFilterConfigModel;
            int count = LList.getCount(subFilterList);
            if (count > 0) {
                sb = new StringBuilder();
                for (int i = 0; i < count; i++) {
                    FilterBean bean = LList.getElement(subFilterList, i);
                    if (bean == null || bean.isSearchFilter) continue;
                    if (isStrCode) {
                        if (!LText.isEmptyOrNull(bean.strCode)) {
                            sb.append(bean.strCode);
                        } else {
                            sb.append(bean.code);
                        }
                    } else {
                        sb.append(bean.code);
                    }
                    sb.append(",");
                }
                int length = sb.length();
                if (length > 0) {
                    sb.deleteCharAt(length - 1);
                }
                return sb.toString();
            }
        }
        return "";
    }

    /**
     * 1111.4 薪资增加自定义
     * zpgeek/app/geek/recommend/joblist参数filterParams需要传
     */
    public static String getFilterLowSalary(FilterBean filterBean) {
        if (filterBean != null) {
            List<FilterBean> subFilterList = filterBean.subFilterConfigModel;
            int count = LList.getCount(subFilterList);
            if (count > 0) {
                for (int i = 0; i < count; i++) {
                    FilterBean bean = LList.getElement(subFilterList, i);
                    if (bean == null) continue;
                    if (bean.lowSalary > 0) {
                        return bean.lowSalary + "";
                    }
                }
            }
        }
        return "";
    }

    /**
     * 1111.4 薪资增加自定义
     * zpgeek/app/geek/recommend/joblist参数filterParams需要传
     */
    public static String getFilterHighSalary(FilterBean filterBean) {
        if (filterBean != null) {
            List<FilterBean> subFilterList = filterBean.subFilterConfigModel;
            int count = LList.getCount(subFilterList);
            if (count > 0) {
                for (int i = 0; i < count; i++) {
                    FilterBean bean = LList.getElement(subFilterList, i);
                    if (bean == null) continue;
                    if (bean.highSalary > 0) {
                        return bean.highSalary + "";
                    }
                }
            }
        }
        return "";
    }

    public static String getFilterRecommendJson(FilterBean filterBean) {
        String json = "";
        if (filterBean != null && !LList.isEmpty(filterBean.subFilterConfigModel)) {
            try {
                JSONArray ja = new JSONArray();
                for (FilterBean item : filterBean.subFilterConfigModel) {
                    if (item == null) continue;
                    JSONObject joItem = new JSONObject();
                    joItem.put("code", item.code);
                    joItem.put("type", item.type);
                    joItem.put("expression", TextUtils.isEmpty(item.expression) ? "" : item.expression);
                    ja.put(joItem);
                }
                json = ja.toString();
            } catch (JSONException e) {
                e.printStackTrace();
                json = "";
            }
        }
        return json;
    }

    /**
     * 追加字符串
     *
     * @param stringBuilder
     * @param content
     * @return
     */
    public static StringBuilder appendText(StringBuilder stringBuilder, String content) {
        if (stringBuilder == null) {
            throw new NullPointerException("stringBuilder can not be null");
        }
        if (content != null) {
            stringBuilder.append(content);
        }
        return stringBuilder;
    }

    /**
     * 拨打电话
     *
     * @param context
     */
    public static void dialCompanyService(Context context) {
        String tel = VersionAndDatasCommon.getInstance().getCompanyServiceTel();
        if (TextUtils.isEmpty(tel)) {
            tel = "4000655799";
        }
        dial(context, tel);
        new AutoReport(AutoReport.CALL_COMPANY_SERVICE).report();
    }

    /**
     * 拨打电话
     *
     * @param context
     * @param number
     */
    public static void dial(Context context, String number) {
        Intent intent = new Intent();
        Uri mobileUri = Uri.parse("tel:" + number);
        intent.setAction(Intent.ACTION_DIAL);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(mobileUri);
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        try {
            AppUtil.startActivity(context, intent);
        } catch (Exception ignored) {
        }
    }

    /**
     * 复制文本
     *
     * @param context
     * @param text
     */
    public static boolean copyText(Context context, String text) {
        if (context == null) return false;
        if (LText.empty(text)) return false;
        try {
            ClipboardManager clip = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            clip.setPrimaryClip(ClipData.newPlainText("copy", text));
        } catch (Throwable e) {
            CrashReport.postCatchedException(e);
            return false;
        }
        return true;
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @param linkedChar
     * @param list
     * @return
     */
    @NonNull
    public static String connectTextWithChar(String linkedChar, List<String> list) {
        return connectTextWithChar(linkedChar, list, true);
    }

    public static String connectTextWithChar(String linkedChar, List<String> list, boolean skipEmptyItem) {
        StringBuilder sb = new StringBuilder();
        if (!LList.isEmpty(list)) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                String item = list.get(i);
                if (skipEmptyItem && TextUtils.isEmpty(item)) continue;

                if (i == size - 1) {
                    sb.append(item);
                } else {
                    sb.append(item).append(linkedChar);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @param linkedChar
     * @param texts
     * @return
     */
    @NonNull
    public static String connectTextWithChar(String linkedChar, String... texts) {
        StringBuilder sb = new StringBuilder();
        if (texts != null) {
            List<String> items = new ArrayList<>();
            //去掉空数据
            for (String text : texts) {
                if (LText.empty(text)) continue;
                items.add(text);
            }
            final int size = items.size();
            if (size == 1) return items.get(0);
            //遍历数据源,字符之间用传入进来的linkedChar链接
            for (int i = 0; i < size; i++) {
                String value = items.get(i);
                if (i > 0) {
                    sb.append(linkedChar);
                }
                sb.append(value);
            }
        }
        return sb.toString();
    }


    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @deprecated replaced by {@link #connectTextWithCharNew(String, List, IStringFunction)}
     *
     */
    @Deprecated
    public static <T> String connectTextWithChar(String linkedChar, List<T> list, ValueProvider<T> provider) {
        if (provider == null || LList.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();

        int size = list.size();
        for (int i = 0; i < size; i++) {
            T bean = list.get(i);
            if (bean == null) continue;

            String item = provider.getValue(bean);
            if (TextUtils.isEmpty(item)) continue;

            if (i == 0) {
                sb.append(item);
            } else {
                sb.append(linkedChar).append(item);
            }
        }

        return sb.toString();
    }

    public static <T> List<String> mapToStringList(List<T> list, ValueProvider<T> provider) {
        if (provider == null || LList.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        for (T bean : list) {
            String item = provider.getValue(bean);
            if (TextUtils.isEmpty(item)) continue;
            result.add(item);
        }

        return result;
    }

    public interface ValueProvider<T> {
        String getValue(@NonNull T bean);
    }

//
//    /**
//     * 姓名是否有非法
//     */
//    public static boolean isUserNameInvalid(String inputText) {
//
//        /*
//             (?!\\·)      不能以“·”开头
//             (?!.*?\\·$)  不能以“·”结尾
//         */
//
//        // 首尾不能为“·”，支持中英文空格“·”
//        final String regex = "^(?!·)(?!.*?·$)[a-zA-Z\u4e00-\u9fa5\\s·]+$";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher m = pattern.matcher(inputText);
//        return !m.matches();
//    }

    /**
     * 将一个字符串中的所有不需要的字去掉
     *
     * @param rawString 输入字符串
     * @return 剔除后的字符串
     */
    @Nullable
    public static String trimUnwanted(@Nullable String rawString) {
        if (TextUtils.isEmpty(rawString)) {
            return rawString;
        }
        /* 要过滤的字 */

        List<String> sUnwantedChars = Arrays.asList("省", "市", "特别行政区", "区", "县", "旗", "自治区", "壮族自治区", "回族自治区", "维吾尔自治区");// Arrays.asList("省", "市", "特别行政区", "区", "县", "旗");
        String finalString = rawString;
        for (String uChar : sUnwantedChars) {
            finalString = finalString.replace(uChar, "");
        }
        return finalString;
    }

    @Nullable
    public static String trim(@Nullable String content) {
        if (content == null) {
            return null;
        }
        return content.trim();
    }

    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0 || "null".equals(str);
    }

    public static String listToString(List<String> list, String split) {
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i));
                } else {
                    sb.append(list.get(i)).append(split);
                }
            }
            return new String(sb);
        } else {
            return "";
        }
    }

    /**
     * 半角转全角
     **/
    public static String ToDBC(String input) {
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == 12288) {
                c[i] = (char) 32;
                continue;
            }
            if (c[i] > 65280 && c[i] < 65375)
                c[i] = (char) (c[i] - 65248);
        }
        return new String(c);
    }

    @NonNull
    public static String checkNonNull(@Nullable String nullableString) {
        return nullableString == null ? "" : nullableString;
    }

    /**
     * 将字符串中的特定 字符高亮
     *
     * @param text       需要处理的 整段文字
     * @param regular    匹配正则
     * @param colorResId 高亮颜色
     * @return
     */
    public static CharSequence highLightString(String text, String regular, int colorResId) {

        try {
            SpannableStringBuilder builder = new SpannableStringBuilder(text);

            Pattern p = Pattern.compile(regular);
            Matcher matcher = p.matcher(text);
            int start = 0;
            int end = 0;
            while (matcher.find()) {
                if (matcher.start() == end) {
                    end = matcher.end();
                } else {
                    if (start != end) {
                        ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                        builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                    start = matcher.start();
                    end = matcher.end();
                }
            }
            if (start != end) {
                ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            return builder;
        } catch (Exception e) {
            return text;
        }
    }

    @Nullable
    public static SpannableString highLightString2(@NonNull List<HighlightItem> highlightList, String content) {
        if (!LText.empty(content)) {
            SpannableString stringDesc = new SpannableString(content);
            int length = content.length();
            if (LList.isEmpty(highlightList)) return stringDesc;

            for (HighlightItem highLight : highlightList) {
                final int start = highLight.startIndex;
                final int end = highLight.endIndex;
                if (end <= length && start < end) {
                    stringDesc.setSpan(new ForegroundColorSpan(Color.parseColor("#0D9EA3")), start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
            return stringDesc;
        }
        return null;
    }

    @Nullable
    public static SpannableString highLightString(@NonNull List<HighlightItem> highlightList, String content) {
        if (!LText.empty(content)) {
            SpannableString stringDesc = new SpannableString(content);
            ForegroundColorSpan highColor = new ForegroundColorSpan(Color.parseColor("#0D9EA3"));
            int length = content.length();
            if (LList.isEmpty(highlightList)) return stringDesc;

            for (HighlightItem highLight : highlightList) {
                final int start = highLight.startIndex;
                final int end = highLight.endIndex;
                if (end <= length && start < end) {
                    // TODO: 2022/5/25 注意！该方法只高亮最后一个 HighlightItem
                    stringDesc.setSpan(highColor, start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
            return stringDesc;
        }
        return null;
    }

    @Nullable
    public static SpannableString highLightString(@NonNull List<HighlightItem> highlightList, String content, String colorString) {
        if (!LText.empty(content)) {
            SpannableString stringDesc = new SpannableString(content);
            ForegroundColorSpan highColor = new ForegroundColorSpan(Color.parseColor(colorString));
            int length = content.length();
            if (LList.isEmpty(highlightList)) return stringDesc;

            for (HighlightItem highLight : highlightList) {
                final int start = highLight.startIndex;
                final int end = highLight.endIndex;
                if (end <= length && start < end) {
                    stringDesc.setSpan(highColor, start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
            return stringDesc;
        }
        return null;
    }

    @Nullable
    public static SpannableString highLightString(@NonNull List<HighlightItem> highlightList, String content, int color) {
        if (!LText.empty(content)) {
            SpannableString stringDesc = new SpannableString(content);
            ForegroundColorSpan highColor = new ForegroundColorSpan(color);
            int length = content.length();
            if (LList.isEmpty(highlightList)) return stringDesc;

            for (HighlightItem highLight : highlightList) {
                final int start = highLight.startIndex;
                final int end = highLight.endIndex;
                if (end <= length && start < end) {
                    stringDesc.setSpan(highColor, start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
            return stringDesc;
        }
        return null;
    }

    public static String format(@StringRes int resId, Object... args) {
        return String.format(Utils.getApp().getString(resId), args);
    }


    public static String findUrlByStr(String data) {
        Pattern pattern = Pattern.compile("https?://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]");
        Matcher matcher = pattern.matcher(data);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }

    public static String getArrayTextForLevelBean(List<LevelBean> list, String split, String emptyText) {
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i));
                } else {
                    sb.append(list.get(i)).append(split);
                }
            }
            return new String(sb);
        } else {
            return emptyText;
        }
    }

    public static String getArrayTextWhithName(List<LevelBean> list, String split, String emptyText) {
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i).name);
                } else {
                    sb.append(list.get(i).name).append(split);
                }
            }
            return new String(sb);
        } else {
            return emptyText;
        }
    }

    public static String getArrayTextWithCode(List<LevelBean> list, String split, String emptyText) {
        StringBuffer sb = new StringBuffer();
        if (LList.getCount(list) > 0) {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                if (i == (size - 1)) {
                    sb.append(list.get(i).code);
                } else {
                    sb.append(list.get(i).code).append(split);
                }
            }
            return new String(sb);
        } else {
            return emptyText;
        }
    }

    public static List<LevelBean> getListWithCodeArrayText(String arrayText, String split) {
        if (LText.empty(arrayText)) {
            return null;
        }
        String[] args = arrayText.split(split);
        if (LList.isEmpty(args)) {
            return null;
        }
        List<LevelBean> list = new ArrayList<>();
        for (String item : args) {
            int anInt = LText.getInt(item);
            if (anInt > 0) {
                list.add(new LevelBean(anInt, ""));
            }
        }

        return list;
    }

    /**
     * drawable路径转换成 url
     */
    public static String imageTranslateUri(int resId) {

        Resources r = App.getAppContext().getResources();
        Uri uri = Uri.parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://"
                + r.getResourcePackageName(resId) + "/"
                + r.getResourceTypeName(resId) + "/"
                + r.getResourceEntryName(resId));

        return uri.toString();
    }

    public static String getNotEmptyString(String defString, String... strings) {
        String result = LText.isEmptyOrNull(defString) ? "" : defString;
        if (null != strings) {
            for (String str : strings) {
                if (!LText.isEmptyOrNull(str)) {
                    result = str;
                    break;
                }
            }
        }

        return result;
    }

    public static String getUrlEncodeString(String text) {
        if (!LText.isEmptyOrNull(text)) {
            try {
                return URLEncoder.encode(text, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return text;
    }

    public static String decodeString(String encodeTxt) {
        String result = "";
        if (!LText.isEmptyOrNull(encodeTxt)) {
            try {
                result = URLDecoder.decode(encodeTxt, "UTF-8");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 将字符串中的特定 字符高亮
     * patternFlags：例如Pattern.CASE_INSENSITIVE
     */
    public static CharSequence lightString(String text, int patternFlags, String regular, int colorResId) {
        if (LText.empty(text) || LText.empty(regular)) return text;
        try {
            SpannableStringBuilder builder = new SpannableStringBuilder(text);

            Pattern p = Pattern.compile(regular, patternFlags);
            Matcher matcher = p.matcher(text);
            int start = 0;
            int end = 0;
            while (matcher.find()) {
                if (matcher.start() == end) {
                    end = matcher.end();
                } else {
                    if (start != end) {
                        ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                        builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                    start = matcher.start();
                    end = matcher.end();
                }
            }
            if (start != end) {
                ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            return builder;
        } catch (Exception e) {
            return text;
        }
    }

    public static void addForegroundColorSpan(SpannableStringBuilder builder, int patternFlags, String regular, int colorResId) {
        if (builder == null || builder.length() <= 0 || LText.empty(regular)) return;
        try {
            Pattern p = Pattern.compile(regular, patternFlags);
            Matcher matcher = p.matcher(builder);
            int start = 0;
            int end = 0;
            while (matcher.find()) {
                if (matcher.start() == end) {
                    end = matcher.end();
                } else {
                    if (start != end) {
                        ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                        builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                    start = matcher.start();
                    end = matcher.end();
                }
            }
            if (start != end) {
                ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        } catch (Exception e) {
        }
    }

    public static void addBoldColorSpan(SpannableStringBuilder builder, List<HighlightItem> highlightItems, @ColorInt int color) {
        if (builder == null || builder.length() <= 0 || LList.isEmpty(highlightItems)) return;
        try {
            for (HighlightItem highlightItem : highlightItems) {
                int end = highlightItem.endIndex;
                int start = highlightItem.startIndex;
                int length = builder.length();
                if (end <= length && start < end && start >= 0) {
                    builder.setSpan(new ForegroundColorSpan(color), highlightItem.startIndex, highlightItem.endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    builder.setSpan(new StyleSpan(Typeface.BOLD), highlightItem.startIndex, highlightItem.endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        } catch (Exception e) {
        }
    }

    public static void addCenterImageSpan(Context context, SpannableStringBuilder stringBuilder, List<Bitmap> afterNameIconBitmaps) {
        if (stringBuilder == null || LList.isEmpty(afterNameIconBitmaps)) return;
        try {
            final String placeRegex = "#&#";
            if (!LList.isEmpty(afterNameIconBitmaps)) {
                for (int i = 0; i < afterNameIconBitmaps.size(); i++) {
                    stringBuilder.append(" ").append(placeRegex).append(" ");
                }
            }
            Pattern pattern = Pattern.compile(placeRegex);
            Matcher matcher = pattern.matcher(stringBuilder);
            int index = 0;
            while (matcher.find() && index < afterNameIconBitmaps.size()) {
                Bitmap bitmap = LList.getElement(afterNameIconBitmaps, index);
                if (bitmap != null && !bitmap.isRecycled()) {
                    CenterImageSpan imageSpan = new CenterImageSpan(context, afterNameIconBitmaps.get(index), 2);
                    stringBuilder.setSpan(imageSpan, matcher.start(), matcher
                            .end(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

                    index++;
                }

            }
        } catch (Exception e) {
        }

    }



    public static void setTextWithBookTitleMark(TextView textView, int textViewMaxWidth, String text) {
        setTextWithBookTitleMark(textView, textViewMaxWidth, "《", text);
    }

    public static void setTextWithBookTitleMark(TextView textView, int textViewMaxWidth, String prefix, String text) {
        if (textView == null || TextUtils.isEmpty(text)) return;

        String suffix = "》";
        String ellipsis = "…";
        String content = TextUtils.concat(prefix, text, suffix).toString();

        TextPaint paint = textView.getPaint();
        paint.measureText(text);
        int maxWidth = (int) paint.measureText(content);
        String concatText = "";
        if (maxWidth > textViewMaxWidth) {
            for (int i = text.length() - 1; i >= 0; i--) {
                String substring = text.substring(0, i);
                String tempContent = TextUtils.concat(prefix, substring, ellipsis, suffix).toString();
                int tempWidth = (int) paint.measureText(tempContent);
                if (tempWidth < textViewMaxWidth) {
                    concatText = tempContent;
                    break;
                }
            }
            textView.setText(concatText);
        } else {
            textView.setText(content);
        }
    }

    public static String dealStringLength(String str, int length) {
        if (TextUtils.isEmpty(str) || str.length() <= length) return str;

        return str.substring(0, length) + "...";
    }

    public static String connectTextWithChar(String linkedChar, char[] charArray) {
        StringBuilder sb = new StringBuilder();
        if (charArray != null && charArray.length > 0) {
            int lenght = charArray.length;
            for (int i = 0; i < lenght; i++) {
                char item = charArray[i];
                if (i == lenght - 1) {
                    sb.append(item);
                } else {
                    sb.append(item).append(linkedChar);
                }
            }
        }
        return sb.toString();
    }

    public static boolean isNumeric(String str) {
        if (TextUtils.isEmpty(str)) return false;
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public interface IValueFunction<T> {
        String create(@NonNull T t);
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @deprecated replaced by {@link #connectTextWithCharNew(String, List, IStringFunction, boolean)}
     *
     * @param linkedChar
     * @param dataList
     * @return
     */
    @Deprecated
    @NonNull
    public static <E> String connectTextWithChar(String linkedChar, List<E> dataList, @NonNull IValueFunction<E> valueFunction) {
        return connectTextWithChar(linkedChar, dataList, valueFunction, true);
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @deprecated replaced by {@link #connectTextWithCharNew(String, List, IStringFunction, boolean)}
     *
     * @param linkedChar
     * @param dataList
     * @return
     */
    @Deprecated
    @NonNull
    public static <E> String connectTextWithChar(String linkedChar, List<E> dataList, @NonNull IValueFunction<E> valueFunction, boolean skipEmptyItem) {
        if (LList.getCount(dataList) == 0) return "";
        List<String> list = new ArrayList<>();
        for (E e : dataList) {
            if (e == null) continue;
            String value = valueFunction.create(e);
            if (skipEmptyItem && TextUtils.isEmpty(value)) continue;
            list.add(value);
        }
        return connectTextWithChar(linkedChar, list, skipEmptyItem);
    }

    @FunctionalInterface
    public interface IStringFunction<T> {
        String get(@NonNull T t);
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @param linkedChar
     * @param dataList
     * @return
     */
    @NonNull
    public static <E> String connectTextWithCharNew(String linkedChar, List<E> dataList, @NonNull IStringFunction<E> valueFunction) {
        return connectTextWithCharNew(linkedChar, dataList, valueFunction, true);
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     *
     * @param linkedChar
     * @param dataList
     * @return
     */
    @NonNull
    public static <E> String connectTextWithCharNew(String linkedChar, List<E> dataList, @NonNull IStringFunction<E> valueFunction, boolean skipEmptyItem) {
        if (LList.getCount(dataList) == 0) return "";
        List<String> list = new ArrayList<>();
        for (E e : dataList) {
            if (e == null) continue;
            String value = valueFunction.get(e);
            if (skipEmptyItem && TextUtils.isEmpty(value)) continue;
            list.add(value);
        }
        return connectTextWithChar(linkedChar, list, skipEmptyItem);
    }


    public static List<String> split(String splitChar, String content) {
        if (content == null || content.trim().length() == 0) {
            return new ArrayList<>();
        }
        String[] split = content.trim().split(splitChar);
        if (split.length > 0) {
            return Arrays.asList(split);
        } else {
            return new ArrayList<>();
        }
    }

}
