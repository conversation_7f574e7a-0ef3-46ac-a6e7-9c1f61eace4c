package com.hpbr.bosszhipin.module.mapcompat.view;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.map.search.poi.OnGetPoiSearchResultListener;
import com.hpbr.bosszhipin.map.search.poi.PoiItem;
import com.hpbr.bosszhipin.map.search.poi.PoiSearchCompat;
import com.hpbr.bosszhipin.map.search.route.RoutePath;
import com.hpbr.bosszhipin.module.map.activity.GeekRoute1004NewActivity;
import com.hpbr.bosszhipin.module.map.view.GeekRouteBusItemView;
import com.hpbr.bosszhipin.module.mapcompat.GeekRouteHelper;
import com.hpbr.bosszhipin.module.mapcompat.adapter.BottomAddressCompatAdapter;
import com.hpbr.bosszhipin.module.mapcompat.bean.GeekPathItemCompatData;
import com.hpbr.bosszhipin.module.mapcompat.listener.OnBottomSheetClickListener;
import com.hpbr.bosszhipin.module.mapcompat.listener.OnGeekHandleBottomViewListener;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.BottomButtonView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.behavior.ViewPagerBottomSheetBehavior;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.DisplayHelper;
import com.twl.utils.frequency.DebounceExecutor;

import net.bosszhipin.api.bean.HighlightItem;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * created by yhy
 * date 2022/4/11
 * desc: copy from com.hpbr.bosszhipin.module.map.view.GeekRouteBottomView
 */
public class GeekRouteBottomSheetView extends LinearLayout implements View.OnClickListener, OnGeekHandleBottomViewListener {
    private MTextView mEndTv;
    private MapStartPositionEditText mStartEt;
    private LinearLayout mSwitchBarLl;
    private LinearLayout mFlBus;
    private TextView mTvBusMode;
    private LinearLayout mFlRide;
    private TextView mTvRideMode;
    private LinearLayout mFlDrive;
    private TextView mTvDriveMode;
    private LinearLayout mFlWalk;
    private TextView mTvWalkMode;
    private ZPUIFrameLayout mBottomFl;
    private LinearLayout mLyContainer;
    private FrameLayout mTvFailedLl;
    private ImageView mImgLoading;
    private MTextView mTvFailed;
    private BottomButtonView mBottomButtonView;
    private RecyclerView mGrayAddressRv;
    private ZPUIRoundButton mPositionTag;

    private ImageView mEditIv, mClearIv;
    private int mSelectType;
    private BottomAddressCompatAdapter mBottomAddressAdapter;
    private String mCurrentLocationAddress;
    private String mHomeAddress;
    private MTextView mCancelTv;
    private String mStartAddress;
    /**
     * 用于搜索的限定城市
     */
    private String mCity;
    private String endCity;

    /**
     * 起点位置标签
     */
    private String mStartPositionTag;  // 「我的位置」、「我的住址」
    private final DebounceExecutor inputExecutor = new DebounceExecutor(Constants.MAP_INPUT_DEBOUNCE_TIME_C);


    public GeekRouteBottomSheetView(Context context) {
        super(context);
        init();
    }

    public GeekRouteBottomSheetView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GeekRouteBottomSheetView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.view_geek_route_bottom, this);
        initView();
    }

    private void initView() {
        mStartEt = findViewById(R.id.start_et);
        mGrayAddressRv = findViewById(R.id.gray_rv);
        ConstraintLayout.LayoutParams rvParams = (ConstraintLayout.LayoutParams) mGrayAddressRv.getLayoutParams();
        rvParams.height = DisplayHelper.getScreenHeight(getContext()) - DisplayHelper.dp2px(getContext(), 80) - DisplayHelper.dp2px(getContext(), 210);
        mGrayAddressRv.setLayoutParams(rvParams);

        mCancelTv = findViewById(R.id.cancel_tv);
        mEditIv = findViewById(R.id.start_edit_iv);
        mClearIv = findViewById(R.id.clear_iv);
        mClearIv.setOnClickListener(this);

        mEditIv.setOnClickListener(onStartSelectClickListener);

        mEndTv = findViewById(R.id.tv_target);
        mSwitchBarLl = findViewById(R.id.switch_bar_ll);

        mFlBus = findViewById(R.id.fl_bus);
        mFlBus.setOnClickListener(this);
        mTvBusMode = findViewById(R.id.tv_bus_mode);

        mFlRide = findViewById(R.id.fl_bike);
        mFlRide.setOnClickListener(this);
        mTvRideMode = findViewById(R.id.tv_bike_mode);

        mFlDrive = findViewById(R.id.fl_drive);
        mFlDrive.setOnClickListener(this);
        mTvDriveMode = findViewById(R.id.tv_drive_mode);

        mFlWalk = findViewById(R.id.fl_walk);
        mFlWalk.setOnClickListener(this);
        mTvWalkMode = findViewById(R.id.tv_walk_mode);

        mBottomFl = findViewById(R.id.bottom_fl);
        mLyContainer = findViewById(R.id.ly_container);
        mTvFailedLl = findViewById(R.id.tv_location_failed_ll);
        mImgLoading = findViewById(R.id.img_loading);
        mTvFailed = findViewById(R.id.tv_location_failed);
        mBottomButtonView = findViewById(R.id.bottom_button_view);
        mPositionTag = findViewById(R.id.position_tag);

        mBottomAddressAdapter = new BottomAddressCompatAdapter(getContext());
        mGrayAddressRv.setAdapter(mBottomAddressAdapter);
        mBottomAddressAdapter.setOnItemClickListener(new BottomAddressCompatAdapter.onItemClickListener() {
            @Override
            public void locationClick() {
                setStartPositionTag(mStartPositionTag);   // 先恢复当前的起始位置标签
                mOnBottomViewClickListener.onBottomLocationClick();
            }

            @Override
            public void openLocationClick() {
                setStartPositionTag(mStartPositionTag);   // 先恢复当前的起始位置标签
                mOnBottomViewClickListener.onBottomOpenLocationClick();
            }

            @Override
            public void editAddress() {
                mOnBottomViewClickListener.onBottomHomeAddressEditClick();
            }

            @Override
            public void homeAddressClick() {
                if (LText.notEmpty(mHomeAddress)) {
                    mStartEt.setText(mHomeAddress);
                    setStartPositionTag(LText.getString(R.string.string_my_home_position));
                } else {
                    setStartPositionTag(mStartPositionTag);
                }
                //我的地址和当前起点不一致了，刷新下地图，修改通过输入框改起点，然后再点击我的地址，地图不刷新的历史问题，为了不更改其他逻辑，在这里兼容
                boolean isForceRefresh = !TextUtils.isEmpty(mHomeAddress) && !TextUtils.isEmpty(mStartAddress) && !TextUtils.equals(mHomeAddress, mStartAddress);
                mOnBottomViewClickListener.onBottomHomeAddressClick(isForceRefresh);
            }

            @Override
            public void onItemClick(PoiItem poiItem) {
                setStartText(poiItem.getTitle());
                setStartPositionTag(null);
                mOnBottomViewClickListener.onBottomPoiClick(poiItem);
            }
        });

        mStartEt.setVisibility(VISIBLE);
        mStartEt.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                mPositionTag.setVisibility(GONE);           // 编辑状态不展示起点位置标签
                mSwitchBarLl.setVisibility(GONE);
                if (!TextUtils.isEmpty(mStartEt.getText())) {
                    mClearIv.setVisibility(VISIBLE);
                }
                mStartEt.setCursorToEnd();
                mEditIv.setVisibility(GONE);
                AnalyticsFactory.create().action("stast-location-click")
                        .debug().build();
                AnalyticsFactory.create().action("start-location-expose")
                        .param("p", TextUtils.isEmpty(mHomeAddress) ? 1 : 0)
                        .debug().build();
                mOnBottomViewClickListener.onStartClick();
                mGrayAddressRv.setVisibility(VISIBLE);
                mCancelTv.setVisibility(VISIBLE);
                App.get().getMainHandler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        List<Object> list = new ArrayList<>();
                        BottomAddressCompatAdapter.TopBean bean = new BottomAddressCompatAdapter.TopBean();
                        bean.homeDesc = mHomeAddress;
                        bean.locationDesc = mCurrentLocationAddress;
                        list.add(0, bean);
                        mBottomAddressAdapter.setData(list);
                    }
                }, 100);
                mBottomFl.setVisibility(INVISIBLE);
            } else {
                mClearIv.setVisibility(GONE);
                mEditIv.setVisibility(VISIBLE);
            }
        });
        mStartEt.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                mOnBottomViewClickListener.onStartClick();
            }
        });

        initSearch();
        mCancelTv.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                mOnBottomViewClickListener.setBehaviorState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);

                // 恢复之前的文字
                mStartEt.removeTextChangedListener(mTextWatcher);
                // 恢复之前的起点位置标签
                setStartPositionTag(mStartPositionTag);
                mStartEt.setText(mStartAddress);

                mStartEt.addTextChangedListener(mTextWatcher);

                recoverUI();
            }
        });
    }

    private void initSearch() {
        mStartEt.addTextChangedListener(mTextWatcher);
    }

    private TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (!TextUtils.isEmpty(s.toString())) {
                mClearIv.setVisibility(VISIBLE);
            } else {
                mClearIv.setVisibility(GONE);
            }
            inputExecutor.submit(() -> poiSearch(s.toString()));
        }
    };

    private void poiSearch(String address) {
        if(TextUtils.isEmpty(address)){
            TLog.info(GeekRouteHelper.TAG, "poiSearch address empty");
            return;
        }
        com.hpbr.bosszhipin.map.search.poi.PoiSearch poiSearch = new com.hpbr.bosszhipin.map.search.poi.PoiSearch();
        poiSearch.setCity(getCityWithDefault());
        poiSearch.setKeyWord(address);
        poiSearch.setPageSize(50);
        int mapType = AndroidDataStarGray.getInstance().isUserBaiduMapAndSearch() ? MapViewCompat.A_MAP_TYPE : MapViewCompat.DEFAULT_TYPE;
        TLog.info(GeekRouteHelper.TAG, "isUserBaiduMapAndSearch %s, mapType %s", AndroidDataStarGray.getInstance().isUserBaiduMapAndSearch(), mapType);
        PoiSearchCompat poiSearchCompat = PoiSearchCompat.newInstance(getContext(), mapType);
        poiSearchCompat.searchPoi(poiSearch, new OnGetPoiSearchResultListener() {
            @Override
            public void onGetPoiResult(com.hpbr.bosszhipin.map.search.poi.PoiResult poiResult) {
                List<Object> list = new ArrayList<>();
                BottomAddressCompatAdapter.TopBean bean = new BottomAddressCompatAdapter.TopBean();
                bean.homeDesc = mHomeAddress;
                bean.locationDesc = mCurrentLocationAddress;
                list.add(0, bean);

                if (poiResult != null && !LList.isEmpty(poiResult.getPoiItems()) && address != null) {
                    list.addAll(dealSort(poiResult.getPoiItems(), address));
                }

                mBottomAddressAdapter.setData(list);
                poiSearchCompat.destroy();
            }
        });
    }

    private List<BottomAddressCompatAdapter.CommonBean> dealSort(List<PoiItem> list, String keyStr) {
        List<BottomAddressCompatAdapter.CommonBean> resultList = new ArrayList<>();
        int needTopIndex = -1;
        for (int i = 0; i < LList.getCount(list); i++) {
            String title = list.get(i).getTitle();
            BottomAddressCompatAdapter.CommonBean commonBean = new BottomAddressCompatAdapter.CommonBean();
            commonBean.poiItem = list.get(i);

            if (!TextUtils.isEmpty(title) && title.contains(keyStr)) {
                HighlightItem item = new HighlightItem();
                item.startIndex = title.indexOf(keyStr);
                item.endIndex = item.startIndex + keyStr.length();
                commonBean.highlightItems.add(item);
            }
            resultList.add(commonBean);
            if (keyStr.equals(title)) {
                needTopIndex = i;

            }
        }
        if (needTopIndex > 0) {
            BottomAddressCompatAdapter.CommonBean topCommonBean = resultList.remove(needTopIndex);
            resultList.add(0, topCommonBean);
        }
        return resultList;
    }

    private OnClickNoFastListener onStartSelectClickListener = new OnClickNoFastListener() {
        @Override
        public void onNoFastClick(View v) {
            showSoftInput();
        }
    };

    @Override
    public void onClick(View v) {
        if (mOnBottomViewClickListener == null) return;
        if (v.equals(mFlBus)) {

            mTvFailedLl.setVisibility(GONE);
            mTvBusMode.setSelected(true);
            mTvDriveMode.setSelected(false);
            mTvWalkMode.setSelected(false);
            mTvRideMode.setSelected(false);
            buttonClickStyle(0);
            mSelectType = NAV_MODE.MODE_BUS;
            //null 则表示失败， 空数组是没有路线
            if (LList.isEmpty(mBusPathList)) {
                showLoading();
            }
            mOnBottomViewClickListener.onBusTabClick(LList.isEmpty(mBusPathList));
            showBusRoute();

        } else if (v.equals(mFlRide)) {

            mTvBusMode.setSelected(false);
            mTvDriveMode.setSelected(false);
            mTvWalkMode.setSelected(false);
            mTvRideMode.setSelected(true);
            buttonClickStyle(1);
            mSelectType = NAV_MODE.MODE_RIDE;
            //null 则表示失败， 空数组是没有路线
            if (LList.isEmpty(mRidePathList)) {
                showLoading();
            }
            mOnBottomViewClickListener.onRideTabClick(LList.isEmpty(mRidePathList));
            showRideRoute();

        } else if (v.equals(mFlWalk)) {

            mTvBusMode.setSelected(false);
            mTvDriveMode.setSelected(false);
            mTvWalkMode.setSelected(true);
            mTvRideMode.setSelected(false);
            buttonClickStyle(2);
            mSelectType = NAV_MODE.MODE_WALK;
            //null 则表示失败， 空数组是没有路线
            if (LList.isEmpty(mWalkPathList)) {
                showLoading();
            }
            mOnBottomViewClickListener.onWalkTabClick(LList.isEmpty(mWalkPathList));
            showWalkRoute();

        } else if (v.equals(mFlDrive)) {

            mTvBusMode.setSelected(false);
            mTvDriveMode.setSelected(true);
            mTvWalkMode.setSelected(false);
            mTvRideMode.setSelected(false);
            buttonClickStyle(3);
            mSelectType = NAV_MODE.MODE_DRIVE;
            //null 则表示失败， 空数组是没有路线
            if (LList.isEmpty(mDrivePathList)) {
                showLoading();
            }
            mOnBottomViewClickListener.onDriveTabClick(LList.isEmpty(mDrivePathList));
            showDriveRoute();

        } else if (v.equals(mClearIv)) {
            mStartEt.setText("");
        }
        SpManager.get().user().edit().putInt(GeekRoute1004NewActivity.KEY_LAST_SELECT_TRAFFIC_TYPE,mSelectType).apply();
    }

    private List<Integer> markList = Arrays.asList(GeekRouteBusItemView.RouteWay.ROUTE_WAY_FASTEST, GeekRouteBusItemView.RouteWay.ROUTE_WAY_LEAST_WALK, GeekRouteBusItemView.RouteWay.ROUTE_WAY_LEAST_CHANGE);

    /**
     * 渲染 公交 列表
     */
    @Override
    public void showBusRoute() {
        mCurrentBusPathIndex = 0;
        List<GeekPathItemCompatData> itemData = new ArrayList<>(3);
        for (int i = 0; i < LList.getCount(mBusPathList); i++) {
            RoutePath busPath = LList.getElement(mBusPathList, i);
            itemData.add(new GeekPathItemCompatData(busPath, LList.getElement(markList, i)));
        }

        mLyContainer.removeAllViews();
        if (!LList.isEmpty(itemData)) {
            for (int i = 0; i < LList.getCount(itemData); i++) {
                int index = i;
                GeekPathItemCompatData geekPathItemData = LList.getElement(itemData, i);
                GeekRouteBusItemCompatView itemView = new GeekRouteBusItemCompatView(getContext());
                itemView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mCurrentBusPathIndex == index) return;
                        clickBusPath(index, true);
                    }
                });

                itemView.setSelect(i == mCurrentBusPathIndex);

                itemView.setData(geekPathItemData, geekPathItemData.markStyle);
                LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                params.topMargin = DisplayHelper.dp2px(getContext(), 8);
                itemView.getNavBtn().setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        mOnBottomViewClickListener.onNavClick();
                    }
                });
                mLyContainer.addView(itemView, params);
            }
            mLyContainer.post(new Runnable() {
                @Override
                public void run() {
                    clickBusPath(0, false);
                }
            });

        } else {
            mTvFailedLl.setVisibility(VISIBLE);
        }

    }

    public int mCurrentBusPathIndex;

    private void clickBusPath(int index, boolean isUserClick) {
        mCurrentBusPathIndex = index;
        if (mLyContainer.getChildCount() > 1) {
            for (int i = 0; i < mLyContainer.getChildCount(); i++) {
                GeekRouteBusItemCompatView view = (GeekRouteBusItemCompatView) mLyContainer.getChildAt(i);
                view.setSelect(i == mCurrentBusPathIndex);
            }

        }
        if (mOnBottomViewClickListener != null) {
            mOnBottomViewClickListener.onBusItemPathClick(LList.getElement(mBusPathList, index), isUserClick);
        }
    }

    /**
     * 渲染 骑行列表
     */
    @Override
    public void showRideRoute() {
        mLyContainer.removeAllViews();
        mTvFailedLl.setVisibility(GONE);

        if (!LList.isEmpty(mRidePathList)) {
            GeekPathItemCompatData walkPathData = new GeekPathItemCompatData(mRidePathList);

            GeekRouteOtherItemCompatView itemView = new GeekRouteOtherItemCompatView(getContext());
            itemView.setData(walkPathData);
            itemView.setOnItemClickListener(new GeekRouteOtherItemCompatView.OnItemClickListener() {
                @Override
                public void onItemClick(View v, int index) {
                    mOnBottomViewClickListener.onRideItemPathClick(LList.getElement(mRidePathList, index));
                }

                @Override
                public void onNaviClick() {
                    mOnBottomViewClickListener.onNavClick();
                }
            });
            mLyContainer.addView(itemView);
            itemView.selectDefaultRoute();

        } else {
            mTvFailedLl.setVisibility(VISIBLE);
        }

    }

    /**
     * 渲染 步行列表
     */
    @Override
    public void showWalkRoute() {
        mLyContainer.removeAllViews();
        mTvFailedLl.setVisibility(GONE);

        if (!LList.isEmpty(mWalkPathList)) {
            GeekPathItemCompatData walkPathData = new GeekPathItemCompatData(mWalkPathList);

            GeekRouteOtherItemCompatView itemView = new GeekRouteOtherItemCompatView(getContext());
            itemView.setData(walkPathData);
            itemView.setOnItemClickListener(new GeekRouteOtherItemCompatView.OnItemClickListener() {
                @Override
                public void onItemClick(View v, int index) {
                    mOnBottomViewClickListener.onWalkItemPathClick(LList.getElement(mWalkPathList, index));
                }

                @Override
                public void onNaviClick() {
                    mOnBottomViewClickListener.onNavClick();
                }
            });
            mLyContainer.addView(itemView);
            itemView.selectDefaultRoute();
        } else {
            mTvFailedLl.setVisibility(VISIBLE);
        }

    }

    /**
     * 渲染 驾车列表
     */
    @Override
    public void showDriveRoute() {
        mLyContainer.removeAllViews();
        mTvFailedLl.setVisibility(GONE);

        if (!LList.isEmpty(mDrivePathList)) {
            GeekPathItemCompatData walkPathData = new GeekPathItemCompatData(mDrivePathList);

            GeekRouteOtherItemCompatView itemView = new GeekRouteOtherItemCompatView(getContext());
            itemView.setData(walkPathData);
            itemView.setOnItemClickListener(new GeekRouteOtherItemCompatView.OnItemClickListener() {
                @Override
                public void onItemClick(View v, int index) {
                    mOnBottomViewClickListener.onDriveItemPathClick(LList.getElement(mDrivePathList, index));
                }

                @Override
                public void onNaviClick() {
                    mOnBottomViewClickListener.onNavClick();
                }
            });
            mLyContainer.addView(itemView);
            mLyContainer.post(new Runnable() {
                @Override
                public void run() {
                    itemView.selectDefaultRoute();
                }
            });

        } else {
            mTvFailedLl.setVisibility(VISIBLE);
        }

    }

    private void buttonClickStyle(int position) {

        mTvBusMode.setTextColor(ContextCompat.getColor(getContext(), position == 0 ? R.color.color_FFFFFFFF : R.color.text_c2));
        mTvRideMode.setTextColor(ContextCompat.getColor(getContext(), position == 1 ? R.color.color_FFFFFFFF : R.color.text_c2));
        mTvWalkMode.setTextColor(ContextCompat.getColor(getContext(), position == 2 ? R.color.color_FFFFFFFF : R.color.text_c2));
        mTvDriveMode.setTextColor(ContextCompat.getColor(getContext(), position == 3 ? R.color.color_FFFFFFFF : R.color.text_c2));

        mTvBusMode.setCompoundDrawablesWithIntrinsicBounds(position == 0 ? R.mipmap.ic_geek_route_bus : 0, 0, 0, 0);
        mTvRideMode.setCompoundDrawablesWithIntrinsicBounds(position == 1 ? R.mipmap.ic_geek_route_ride : 0, 0, 0, 0);
        mTvWalkMode.setCompoundDrawablesWithIntrinsicBounds(position == 2 ? R.mipmap.ic_geek_route_walk : 0, 0, 0, 0);
        mTvDriveMode.setCompoundDrawablesWithIntrinsicBounds(position == 3 ? R.mipmap.ic_geek_route_drive : 0, 0, 0, 0);
    }

    @IntDef({NAV_MODE.MODE_BUS, NAV_MODE.MODE_RIDE, NAV_MODE.MODE_DRIVE, NAV_MODE.MODE_WALK})
    public @interface NAV_MODE {
        int MODE_BUS = 1;
        int MODE_RIDE = 2;
        int MODE_DRIVE = 3;
        int MODE_WALK = 4;
    }

    @Override
    public void modeClick(@NAV_MODE int mode) {
        switch (mode) {
            case NAV_MODE.MODE_BUS:
                mFlBus.performClick();
                break;
            case NAV_MODE.MODE_RIDE:
                mFlRide.performClick();
                break;
            case NAV_MODE.MODE_DRIVE:
                mFlDrive.performClick();
                break;
            case NAV_MODE.MODE_WALK:
                mFlWalk.performClick();
                break;
        }
    }

    private List<RoutePath> mBusPathList;
    private List<RoutePath> mRidePathList;
    private List<RoutePath> mWalkPathList;
    private List<RoutePath> mDrivePathList;

    @Override
    public void resetRoute() {
        mBusPathList = null;
        mWalkPathList = null;
        mRidePathList = null;
        mDrivePathList = null;
        TLog.info(GeekRouteHelper.TAG, "resetRoute bottom sheet");
    }

    @Override
    public void setBusPathList(List<RoutePath> busPathList) {
        mBusPathList = busPathList;
    }

    @Override
    public void setRidePathList(List<RoutePath> ridePathList) {
        mRidePathList = ridePathList;
    }

    @Override
    public void setWalkPathList(List<RoutePath> walkPathList) {
        mWalkPathList = walkPathList;
    }

    @Override
    public void setDrivePathList(List<RoutePath> drivePathList) {
        mDrivePathList = drivePathList;
    }

    @Override
    public int getSelectType() {
        return mSelectType;
    }

    @Override
    public void showLocationView() {
        mBottomButtonView.setVisibility(View.GONE);
    }

    @Override
    public BottomButtonView getBottomButtonView() {
        return mBottomButtonView;
    }

    @Override
    public void showLoading() {
        mTvFailedLl.setVisibility(View.VISIBLE);
        mTvFailed.setVisibility(View.GONE);
        mImgLoading.setVisibility(View.VISIBLE);
        mBottomButtonView.setVisibility(GONE);
        ((AnimationDrawable) mImgLoading.getDrawable()).start();
    }

    @Override
    public void hideLoading() {
        mImgLoading.setVisibility(View.GONE);
        ((AnimationDrawable) mImgLoading.getDrawable()).stop();
    }


    @Override
    public void setFailedText(String text) {
        mTvFailed.setText(text);
    }

    @Override
    public void setFailedTvVisible(int visible) {
        mTvFailed.setVisibility(visible);
    }

    @Override
    public void setFailedLlVisible(int visible) {
        mTvFailedLl.setVisibility(visible);
    }

    @Override
    public void pathRemoveAllView() {
        mLyContainer.removeAllViews();
    }

    private OnBottomSheetClickListener mOnBottomViewClickListener;

    public void setOnBottomViewClickListener(OnBottomSheetClickListener onBottomViewClickListener) {
        mOnBottomViewClickListener = onBottomViewClickListener;
    }

    @Override
    public void setStartText(String startText) {
        mStartAddress = startText;
        if (mStartEt != null) {
            mStartEt.removeTextChangedListener(mTextWatcher);
            if(null!=startText){
                mStartEt.setText(startText);
            }

            mStartEt.addTextChangedListener(mTextWatcher);
        }
    }

    @Override
    public void setEndText(String endText) {
        if (mEndTv != null) {
            mEndTv.setText(endText);
        }
    }

    @Override
    public void setCurrentLocAddress(String locationText) {
        mCurrentLocationAddress = locationText;
    }

    @Override
    public void setHomeAddress(String homeAddress) {
        mHomeAddress = homeAddress;
    }

    @Override
    public void recoverUI() {
        if (mBottomFl != null) {
            mStartEt.clearFocus();
            AppUtil.hideSoftInput(getContext(), mStartEt);
            mBottomFl.setVisibility(VISIBLE);
            mGrayAddressRv.setVisibility(GONE);
            mCancelTv.setVisibility(GONE);
            mSwitchBarLl.setVisibility(VISIBLE);
            mEditIv.setVisibility(VISIBLE);
            mClearIv.setVisibility(GONE);
            mOnBottomViewClickListener.showBack();
        }
    }

    @Override
    public void enterInputState() {
        showSoftInput();
    }

    private void  showSoftInput(){
        if (mStartEt!=null){
            InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null) {
                mStartEt.requestFocus();
                inputMethodManager.showSoftInput(mStartEt, 0);
            }
        }
    }

    public void requestEtFocusAndShowSoftInput() {
        App.get().getMainHandler().postDelayed(() -> {
            if (mStartEt != null && mStartEt.hasFocus()) {
              showSoftInput();
            }
        }, 100);
    }

    @Override
    public void setCity(String city) {
        mCity = city;
        if (TextUtils.isEmpty(mCity) && LocationService.location != null && !TextUtils.isEmpty(LocationService.location.city)) {
            mCity = LocationService.location.city;
        }
    }

    public void setEndCity(String endCity){
        this.endCity = endCity;
    }

    private String getCityWithDefault(){
        if (TextUtils.isEmpty(mCity) && LocationService.location != null && !TextUtils.isEmpty(LocationService.location.city)) {
            mCity = LocationService.location.city;
        }
        if(TextUtils.isEmpty(mCity)){
            mCity = endCity;
        }
        return mCity;
    }

    public void setStartPositionTag(@Nullable String tag) {
        if (LText.empty(tag) || LText.equal(mStartAddress, LText.getString(R.string.string_my_position)) || LText.equal(mStartAddress, LText.getString(R.string.string_my_home_position))) {
            mStartPositionTag = "";
            mPositionTag.setVisibility(GONE);
        } else {
            mStartPositionTag = tag;
            mPositionTag.setVisibility(VISIBLE);
            mPositionTag.setText(tag);
        }
    }

    public void onDestroy() {
        if (null != inputExecutor) {
            inputExecutor.cancel();
        }
    }
}