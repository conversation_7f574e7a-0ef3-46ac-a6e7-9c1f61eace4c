package com.hpbr.bosszhipin.module.unfit;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Build;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.AppActivityLifecycleCallbacks;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.entity.manager.ChatSettingManager;
import com.hpbr.bosszhipin.module.contacts.entity.manager.IChatSettingCardListener;
import com.hpbr.bosszhipin.module.contacts.exchange.BossUnfitChangeManager;
import com.hpbr.bosszhipin.module.contacts.exchange.RejectHttpManager;
import com.hpbr.bosszhipin.module.contacts.exchange.UserRejectManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.BossUnfitReplyBannerView;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.HireProgressGrayLayout;
import com.hpbr.bosszhipin.views.MButton;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.ui.decorator.AppDividerDecorator;
import com.twl.utils.ActivityUtils;
import com.twl.utils.DisplayHelper;
import com.twl.utils.GsonUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.AddLabelRequest;
import net.bosszhipin.api.GetAllLabelsAndNoteResponse;
import net.bosszhipin.api.GetBossMarkGeekLabelsResponse;
import net.bosszhipin.api.HideEditButtonResponse;
import net.bosszhipin.api.ResponseReplayRequest;
import net.bosszhipin.api.ResponseReplayResponse;
import net.bosszhipin.api.ResultStringResponse;
import net.bosszhipin.api.SaveNoteAndLabelsRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.UnfitSwitchRequest;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.ServerResponseReplayBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * 1019.41 面试进程标记标签整合灰度弹窗
 *
 * <AUTHOR>
 * @since 2022-12-16
 */
public class BossMarkGeekGrayDialog {
    private static final int TYPE_MARK = 0; //  dialog展示样式 默认类型 收藏, 备注，不感兴趣操作列表类型
    private static final int TYPE_LABEL = 1; // dialog展示样式 设置标签/备注类型
    private static final int TYPE_REASON = 2; // dialog展示样式 选择不合适原因

    private final Activity mActivity;
    private final LayoutInflater mInflater;
    private BottomView mBottomView;
    private FrameLayout mContainerView; // 弹窗展示内容布局
    private ImageView mBackView;
    private MTextView mTitleView;
    private ReasonViewHolder mReasonViewHolder; // 二层弹窗 - 选择不合适原因布局
    private LabelViewHolder mLabelViewHolder; // 二层弹窗 - 标签布局
    private MarkViewHolder mMarkViewHolder; // 一层弹窗 - 标记布局
    private final ContactBean mContactBean;
    private SpannableString mLabelTitle;
    private final List<GetAllLabelsAndNoteResponse.LabelsBean> mLabelsBeans = new ArrayList<>(); // 标签数据
    private final List<String> mSelectedLabels = new ArrayList<>(); // 已选标签
    private String mNote; // 备注信息
    private final List<ServerResponseReplayBean> mReasonsList = new ArrayList<>(); // 不合适原因

    private ResponseReplayResponse mUnfitResponse; // 不合适response


    // 当前弹窗展示样式类型
    private int mCurrentLayoutType = -1;

    public BossMarkGeekGrayDialog(Activity activity, ContactBean contactBean) {
        mActivity = activity;
        mContactBean = contactBean;
        mInflater = LayoutInflater.from(mActivity);
        initView();
    }

    private void initView() {
        View view = mInflater.inflate(R.layout.contact_dialog_boss_mark_geek, null);
        View appTitleFakeSpace = view.findViewById(R.id.app_fake_space);

        mBackView = view.findViewById(R.id.iv_back);
        mTitleView = view.findViewById(R.id.tv_title);
        ImageView closeView = view.findViewById(R.id.mClose);
        mContainerView = view.findViewById(R.id.fl_container);


        mBackView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                getSelectedLabels(() -> {
                    if (mCurrentLayoutType == TYPE_REASON) {
                        doMarkAction("3");
                    }
                    changeLayoutStatus(TYPE_MARK);
                });
            }
        });
        closeView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (mCurrentLayoutType == TYPE_LABEL) {
                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_NOTE_EDIT_SAVE)
                            .param("p", mContactBean.friendId)//被备注的人uid
                            .param("p2", 2)
                            .build();
                } else if (mCurrentLayoutType == TYPE_MARK || mCurrentLayoutType == TYPE_REASON) {
                    doMarkAction("3");
                }
                dismissDialog();
            }
        });

        appTitleFakeSpace.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissDialog();
            }
        });

        mBottomView = new BottomView(mActivity, R.style.BottomViewTheme_Transparent, view);
        mBottomView.setAnimation(R.style.BottomToTopAnim);
        mBottomView.setOnCancelListener(dialog -> {
            if (mCurrentLayoutType == TYPE_LABEL) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_NOTE_EDIT_SAVE)
                        .param("p", mContactBean.friendId)//被备注的人uid
                        .param("p2", 0)
                        .build();
            }
        });

    }

    public void showChatMarkDialog() {
        if (!ActivityUtils.isValid(mActivity)) return;
        getLabels(() -> {
            if (!ActivityUtils.isValid(mActivity)) {
                return;
            }
            showDialog();
            changeLayoutStatus(TYPE_MARK);
        });
    }

    private boolean hideArrow;

    private int pageType = UserRejectManager.PAGE_TYPE_CHAT_PAGE;

    private RejectHttpManager.OnLockUnLockCallBack callBack;

    public void showUnfitDialog(int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        this.callBack = callBack;
        this.pageType = pageType;
        hideArrow = true;
        /*接口请求到数据才显示弹窗*/
        getReasonsList(() -> {
            if (!ActivityUtils.isValid(mActivity)) {
                return;
            }
            showDialog();
            changeLayoutStatus(TYPE_REASON);
        });
    }


    private void showDialog() {
        if (mBottomView != null && ActivityUtils.isValid(mActivity)) {
            mBottomView.showBottomView(true);
        }
    }

    private void dismissDialog() {
        if (mBottomView != null && mBottomView.isShowing() && ActivityUtils.isValid(mActivity)) {
            mBottomView.dismissBottomView();
        }
    }


    // 获取标签列表
    private void getLabels(Runnable runnable) {
        ChatSettingManager.getInstance().GetAllLabelsAndNote(mContactBean, new IChatSettingCardListener<GetAllLabelsAndNoteResponse>() {

            @Override
            public void onSuccess(GetAllLabelsAndNoteResponse data) {
                if (data != null) {
                    mLabelsBeans.clear();
                    mSelectedLabels.clear();
                    mNote = data.getNote();
                    List<GetAllLabelsAndNoteResponse.LabelsBean> labels = data.getLabels();
                    if (labels != null) {
                        mLabelsBeans.addAll(labels);
                    }
                }
            }

            @Override
            public void onFailed(int errCode, String errMessage) {
                ToastUtils.showText(errMessage);
            }

            @Override
            public void onComplete() {
                getSelectedLabels(() -> {
                    if (runnable != null) {
                        runnable.run();
                    }
                });

            }
        });
    }

    private void getSelectedLabels(Runnable runnable) {
        SimpleApiRequest.GET(ChatUrlConfig.URL_GET_PREVIEW_LABELS)
                .addParam("securityId", mContactBean.securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GetBossMarkGeekLabelsResponse>() {
                    @Override
                    public void onSuccess(ApiData<GetBossMarkGeekLabelsResponse> data) {
                        super.onSuccess(data);
                        if (LList.isEmpty(data.resp.labels)) {
                            return;
                        }
                        mSelectedLabels.clear();
                        mSelectedLabels.addAll(data.resp.labels);
                    }

                    @Override
                    public void onComplete() {
                        if (runnable != null) {
                            runnable.run();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                    }
                }).execute();
    }


    private boolean isNewUnfitStyle() {
        return mUnfitResponse != null && mUnfitResponse.style == 1;
    }

    private boolean showOpenReplyDialog() {
        return mUnfitResponse != null && mUnfitResponse.rejectInfo != null && mUnfitResponse.rejectInfo.canShowTip;
    }

    private boolean isStyle2ShowEditButton() {
        return mUnfitResponse != null && mUnfitResponse.rejectInfo != null && mUnfitResponse.rejectInfo.canEdit;
    }

    private void changeLayoutStatus(int type) {
        mCurrentLayoutType = type;
        switch (type) {
            case TYPE_MARK: // 展示标记布局, 收藏， 标签， 标记不合适
                initMarkLayout();
                break;
            case TYPE_LABEL: // 展示添加标签布局
                initLabelLayout();
                break;
            case TYPE_REASON: // 展示选择不合适原因布局
                //需要调用接口判断是否需要“隐藏编辑”按钮
                SimpleApiRequest post = SimpleApiRequest.POST(BusinessUrlConfig.URL_ZPCHAT_CHAT_UNSUITABLE_STATUS);
                post.setRequestCallback(new ApiRequestCallback<HideEditButtonResponse>() {
                    @Override
                    public void onSuccess(ApiData<HideEditButtonResponse> data) {
                        HideEditButtonResponse resp = data.resp;
                        boolean hideEditBtn = resp.hideEditBtn;
                        initReasonStyleLayout(isNewUnfitStyle(), hideEditBtn);
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
                post.addParam("jobId", mContactBean.jobId);
                post.addParam("geekId", mContactBean.friendId);
                post.addParam("geekSource", mContactBean.friendSource);
                post.addParam("securityId", mContactBean.securityId);
                HttpExecutor.execute(post);

            default:
                break;
        }
    }


    private interface OnLoadReasonCallback {
        void onLoadSuccess();
    }

    /**
     * 获取不合适原因列表数据
     */
    private void getReasonsList(@Nullable OnLoadReasonCallback callback) {

        ResponseReplayRequest request = new ResponseReplayRequest(new SimpleCommonApiRequestCallback<ResponseReplayResponse>() {

            @Override
            public void onSuccess(ApiData<ResponseReplayResponse> data) {
                mUnfitResponse = data.resp;
                List<ServerResponseReplayBean> reasonReplyList = data.resp.reasonReplyList;
                if (LList.isEmpty(reasonReplyList)) return;
                mReasonsList.clear();
                mReasonsList.addAll(reasonReplyList);
                if (callback != null) {
                    callback.onLoadSuccess();
                } else {
                    if (!ActivityUtils.isValid(mActivity)) {
                        return;
                    }
                    changeLayoutStatus(TYPE_REASON);
                }
            }

        });
        HttpExecutor.execute(request);
    }


    @SuppressLint("InflateParams")
    private void initReasonStyleLayout(boolean isNewStyle, boolean hideEditBtn) {
        mTitleView.setText("选择原因，为您优化推荐");
        if (hideArrow) {
            mBackView.setVisibility(View.GONE);
        } else {
            mBackView.setVisibility(View.VISIBLE);
        }

        if (mReasonViewHolder == null) {
            mReasonViewHolder = new ReasonViewHolder(mInflater.inflate(R.layout.contact_layout_unfit_reason_list, null));
        }
        mReasonViewHolder.initReasonView(isNewStyle, hideEditBtn);
    }


    /**
     * 加载选择标签布局
     */
    @SuppressLint("InflateParams")
    private void initLabelLayout() {
        getLabels(() -> {
            mTitleView.setText(getLabelDialogTitle());
            mBackView.setVisibility(View.VISIBLE);
            if (mLabelViewHolder == null) {
                mLabelViewHolder = new LabelViewHolder(mInflater.inflate(R.layout.contact_layout_boss_mark_label, null));
            }
            mLabelViewHolder.initView();
        });
    }

    /**
     * 获取标签弹窗标题
     */
    private SpannableString getLabelDialogTitle() {
        if (mLabelTitle == null) {
            mLabelTitle = new SpannableString("设置标签/备注 （牛人不可见）");
            mLabelTitle.setSpan(new RelativeSizeSpan(0.78F), 8, mLabelTitle.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            mLabelTitle.setSpan(new ForegroundColorSpan(ContextCompat.getColor(mActivity, R.color.text_c2)), 8, mLabelTitle.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return mLabelTitle;
    }

    /**
     * 加载添加标记布局
     */
    @SuppressLint("InflateParams")
    private void initMarkLayout() {
        mTitleView.setText("标记");
        mBackView.setVisibility(View.GONE);
        if (mMarkViewHolder == null) {
            mMarkViewHolder = new MarkViewHolder(mInflater.inflate(R.layout.contact_layout_boss_select_mark, null));
        }
        mMarkViewHolder.initView();
    }

    /**
     * 不合适原因列表布局
     */
    class ReasonViewHolder {
        View parentView;

        BossUnfitReplyBannerView mReplyBannerView;
        RecyclerView recyclerView;
        UnfitReasonAdapter adapter;

        public ReasonViewHolder(View parentView) {
            this.parentView = parentView;
            recyclerView = parentView.findViewById(R.id.rv_reasons);
            mReplyBannerView = parentView.findViewById(R.id.mReplyBannerView);
            adapter = new UnfitReasonAdapter();
            AppDividerDecorator dividerDecorator = new AppDividerDecorator();
            dividerDecorator.setDividerColor(ContextCompat.getColor(mActivity, R.color.app_divider));
            dividerDecorator.setDividerHeight(ZPUIDisplayHelper.dp2px(mActivity, 0.5f));
            recyclerView.addItemDecoration(dividerDecorator);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemClickListener((adapter, view, position) -> {

                ServerResponseReplayBean bean = (ServerResponseReplayBean) adapter.getItem(position);
                if (bean == null) {
                    return;
                }
                doMarkAction("1");


                //提示开启自动回复
                if (isNewUnfitStyle()
                        && showOpenReplyDialog()
                        && !mUnfitResponse.openAutoReply
                        && AutoReplyInfo.canShowUnfitReply()) {


                    new DialogUtils.Builder(mActivity)
                            .setTitle("开启自动发送回复？")
                            .setDesc("开启后，移入不合适时将自动发送礼貌通知：\"" + mUnfitResponse.uniformContent + "\" ")
                            .setDoubleButton()
                            .setPositiveAction("开启", v -> {
                                AnalyticsFactory.create()
                                        .action(AnalyticsAction.ACTION_CHAT_QUESTION_AUTO_POPUP_CLICK)
                                        .param("p", "1")
                                        .build();
                                openReplyListener(bean);
                            })
                            .setNegativeAction("不开启", v -> {
                                AnalyticsFactory.create()
                                        .action(AnalyticsAction.ACTION_CHAT_QUESTION_AUTO_POPUP_CLICK)
                                        .param("p", "0")
                                        .build();
                                setUserUnfit(bean);
                                /*保存记录点击不开启*/
                                AutoReplyInfo.saveUnfitReplyInfo();
                            }).build().show();

                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_CHAT_QUESTION_AUTO_POPUP)
                            .build();

                    return;
                }

                setUserUnfit(bean);

            });
        }

        /*点击自动回复  服务记录开关点击过*/
        private void openReplyListener(@NonNull ServerResponseReplayBean bean) {
            SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_ZPCHAT_IMPROPER_SHOW_REJECT_TIP);
            get.addParam("status", "1");
            get.setRequestCallback(new ApiRequestCallback<SuccessBooleanResponse>() {
                @Override
                public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                    /*开启自动回复*/
                    openAutoReply(bean);
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            HttpExecutor.execute(get);
        }

        /*开启自动回复*/
        private void openAutoReply(@NonNull ServerResponseReplayBean bean) {
            UnfitSwitchRequest request = new UnfitSwitchRequest(new ApiRequestCallback<ResultStringResponse>() {
                @Override
                public void onSuccess(ApiData<ResultStringResponse> data) {
                    /*设置牛人不合适*/
                    setUserUnfit(bean);
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            /*0关闭,1打开*/
            request.open = 1;
            HttpExecutor.execute(request);
        }


        private void setUserUnfit(ServerResponseReplayBean bean) {
            BossUnfitChangeManager.getInstance().onBossUnfitListener(mContactBean,
                    pageType,
                    (int) bean.reasonType, new RejectHttpManager.OnLockUnLockCallBack() {

                        @Override
                        public void onSuccess(ContactBean contactBean) {

                            dismissDialog();

                            if (callBack != null) {
                                callBack.onSuccess(contactBean);
                            } else {
                                UserRejectManager.getInstance().chatPageUnfitListenerJump(mActivity, "FROM_FAST_HANDLE_CHAT", contactBean);
                            }
                        }

                        @Override
                        public void onFailed(ContactBean contactBean) {
                            if (callBack != null) {
                                callBack.onFailed(contactBean);
                            }
                        }
                    });
        }


        private void initReasonView(boolean isNewStyle, boolean hideEditBtn) {

            if (LList.isEmpty(mReasonsList) || mUnfitResponse == null) {
                ToastUtils.showText("数据异常");
                changeLayoutStatus(TYPE_MARK);
                return;
            }

            adapter.setNewData(mReasonsList);

            if (isNewStyle) {
                /*是否选中统一回复*/
                boolean isUnitReply = mUnfitResponse.autoReplyType == ResponseReplayResponse.UNITY_REPLY;
                /*是否开启自动回复*/
                boolean openAutoReply = mUnfitResponse.openAutoReply;
                /*开启的时候取的是uniformContent，没开启的时候应该取的是reasonReplyList里面的第一个的content*/
                String desc = isUnitReply ? mUnfitResponse.uniformContent : (LList.getElement(mUnfitResponse.reasonReplyList, 0) != null ? LList.getElement(mUnfitResponse.reasonReplyList, 0).content : "");
                mReplyBannerView.refreshStyle2(hideEditBtn, openAutoReply, isStyle2ShowEditButton(), desc, () -> dismissDialog());
            } else {
                mReplyBannerView.refreshStyle1(mUnfitResponse.openAutoReply ? "向对方发送消息" : "不向对方发送消息，仅自己可见", hideEditBtn, () -> dismissDialog());
            }

            mContainerView.removeAllViews();
            mContainerView.addView(parentView);
        }

    }

    private static class AutoReplyInfo extends BaseServerBean {

        private static final long serialVersionUID = 3883933257832370011L;

        public static final String SP_REPLY = "SP_SUTO_REPLY_COUNT";
        private int count;

        private long date;

        private static AutoReplyInfo getOpenUnfitAutoReplyCount() {
            String info = SpManager.get().user().getString(SP_REPLY, "");
            if (LText.empty(info)) return null;
            return GsonUtils.fromJson(info, AutoReplyInfo.class);
        }

        private static boolean canShowUnfitReply() {
            AutoReplyInfo openUnfitAutoReplyCount = getOpenUnfitAutoReplyCount();
            if (openUnfitAutoReplyCount == null) return true;
            if (openUnfitAutoReplyCount.count >= 3) return false;
            long durarion = System.currentTimeMillis() - openUnfitAutoReplyCount.date;
            long day = durarion / 1000 / 60 / 60 / 24;
            if (day < 14) return false;
            return true;
        }

        private static void saveUnfitReplyInfo() {
            AutoReplyInfo openUnfitAutoReplyCount = getOpenUnfitAutoReplyCount();
            if (openUnfitAutoReplyCount == null) {
                openUnfitAutoReplyCount = new AutoReplyInfo();
            }
            openUnfitAutoReplyCount.count += 1;
            openUnfitAutoReplyCount.date = System.currentTimeMillis();
            SpManager.get().user().edit().putString(SP_REPLY, GsonUtils.toJson(openUnfitAutoReplyCount)).apply();
        }

    }

    /**
     * 标签布局
     */
    class LabelViewHolder {
        private static final int MAX_CHECK_COUNT = 12;

        private static final int MAX_CHILD_COUNT = 100;
        FlexboxLayout labelsView;
        LinearLayout manageLabelLayout;

        MButton addLabelBtn;
        ScrollView scrollView;

        MEditText addLabelEditView;
        MEditText etInput;
        LinearLayout addLabelLayout;
        InputUtils labelInputUtils;
        MButton saveBtn;
        View parentView;
        MTextView inputCount;
        LinearLayout mManageLabelsView;
        InputUtils inputUtils;

        private MTextView mNewAddLabel;

        LabelViewHolder(View parentView) {
            this.parentView = parentView;
            labelsView = parentView.findViewById(R.id.labelsLayout);
            manageLabelLayout = parentView.findViewById(R.id.ll_manage_parent);
            addLabelLayout = parentView.findViewById(R.id.add_lable_ll);
            addLabelEditView = parentView.findViewById(R.id.add_et);
            addLabelBtn = parentView.findViewById(R.id.add_btn);
            scrollView = parentView.findViewById(R.id.mScrollView);
            etInput = parentView.findViewById(R.id.et_input);
            saveBtn = parentView.findViewById(R.id.btn_confirm);
            inputCount = parentView.findViewById(R.id.tv_input_count);
            mManageLabelsView = parentView.findViewById(R.id.ll_manage_label);
            addLabelBtn.setOnClickListener(v -> addNewLabel());
            addLabelEditView.addTextChangedListener(getLabelTextWatcher());
            mManageLabelsView.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    jumpToRemark();
                }
            });

            // 备注
            if (!TextUtils.isEmpty(mNote)) {
                etInput.setText(mNote);
                etInput.setSelection(mNote.length());
            }

            saveBtn.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    if (mContactBean != null) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_NOTE_EDIT_SAVE)
                                .param("p", mContactBean.friendId)//被备注的人uid
                                .param("p2", 1)
                                .build();
                    }

                    saveRemarkText();
                }
            });

            etInput.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    if (s == null) return;
                    String str = s.toString().trim();
                    inputUtils.checkInputCount(inputCount, str);
                }
            });
            labelInputUtils = new InputUtils(mActivity, 0, 8);
            inputUtils = new InputUtils(mActivity, 0, 50);
        }

        private void initView() {
            labelsView.removeAllViews();
            for (GetAllLabelsAndNoteResponse.LabelsBean labelsBean : mLabelsBeans) {
                final CheckBox labelView = (CheckBox) mInflater.inflate(R.layout.contact_item_boss_mark_label, labelsView, false);
                labelView.setText(labelsBean.getLabel());
                labelView.setChecked(labelsBean.getMarked() == 1);
                labelView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                        //非收藏标签
                        if (isChecked && getCheckCount() >= MAX_CHECK_COUNT) {
                            ToastUtils.showText("最多选中12个标签");
                            labelView.setChecked(false);
                            return;
                        }

                        refreshSelectLabel(isChecked, labelsBean.getLabel());
                        postSaveSelectLabels();
                    }
                });
                labelsView.addView(labelView);
            }

            // 添加 "添加按钮"
            appendNewAddLabel();

            mContainerView.removeAllViews();
            // android 8发现标签过多时弹窗展示不出来（原因是弹窗在屏幕外了），这里兼容处理下指定布局尺寸
            if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O) {
                parentView.post(()->{
                    // 如果弹窗高度超过屏幕的80%，就指定布局尺寸避免展示不了问题。
                    if(parentView.getHeight() > DisplayHelper.getScreenHeight(mActivity) * 0.8) {
                        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.getScreenHeight(mActivity) - DisplayHelper.dp2px(mActivity, 80));
                        parentView.setLayoutParams(layoutParams);
                    }
                });
            }
            mContainerView.addView(parentView);
        }


        // 监控标签的文案
        private TextWatcher getLabelTextWatcher() {
            return new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    addLabelBtn.setEnabled(!TextUtils.isEmpty(s));
                }
            };
        }

        // 获得选中标签的数量
        private int getCheckCount() {
            return LList.getCount(mSelectedLabels);
        }

        // 刷新标签
        private void refreshSelectLabel(boolean isChecked, String label) {
            if (mSelectedLabels.contains(label)) {
                mSelectedLabels.remove(label);
            }
            if (isChecked) {//选择标签
                mSelectedLabels.add(label);
            }
        }

        // 跳转到标签管理界面
        private void jumpToRemark() {
            App.get().registerActivityLifecycleCallbacks(new AppActivityLifecycleCallbacks(App.get()) {
                @Override
                public void onActivityDestroyed(Activity activity) {
                    super.onActivityDestroyed(activity);


                    Class<?> reMarkLabelClass = SingleRouter.getReMarkLabelClass();
                    if (reMarkLabelClass != null) {
                        if (reMarkLabelClass.isAssignableFrom(activity.getClass())) {
                            App.get().unregisterActivityLifecycleCallbacks(this);
                            //重新刷新dialogView,有可能在标签管理界面已经删除了标签
                            getLabels(() -> initView());
                        }
                    }

                }
            });
            SingleRouter.startRemarkLabelChat(mActivity, false);
        }

        // 手动添加一个新的标签
        private void addNewLabelView(String label) {
            final CheckBox view = (CheckBox) LayoutInflater.from(mActivity).inflate(R.layout.contact_item_boss_mark_label, labelsView, false);
            view.setText(label);
            view.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked && getCheckCount() >= MAX_CHECK_COUNT) {
                    ToastUtils.showText("最多选中12个标签");
                    view.setChecked(false);
                    return;
                }
                refreshSelectLabel(isChecked, label);
                postSaveSelectLabels();
            });
            // 小于12默认选中
            if (getCheckCount() < MAX_CHECK_COUNT) {
                view.setChecked(true);
                refreshSelectLabel(true, label);
            }
            labelsView.addView(view);
        }


        // 保存备注信息
        private void saveRemarkText() {
            if (inputUtils.isInputLargerThanMaxLength(etInput.getTextContent())) {
                ToastUtils.showText("超出字数限制");
                return;
            }

            AppUtil.hideSoftInput(mActivity);

            if (UserManager.isGeekRole()) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_NOTE_EDIT_SAVE)
                        .param("p", mContactBean.friendId)
                        .param("p2", "1")
                        .secId(mContactBean.securityId)
                        .build();
            }

            SaveNoteAndLabelsRequest request = new SaveNoteAndLabelsRequest(new ApiRequestCallback<HttpResponse>() {

                @Override
                public void handleInChildThread(ApiData<HttpResponse> data) {
                    super.handleInChildThread(data);

                    // if (UserManager.isGeekRole()) {
                    mContactBean.note = etInput.getText().toString();
                    ContactManager.getInstance().updateNote(mContactBean);
                    //  }
                }

                @Override
                public void onSuccess(ApiData<HttpResponse> data) {
                    changeLayoutStatus(TYPE_MARK);
                }

                @Override
                public void onComplete() {
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            request.source = "0";
            request.friendId = String.valueOf(mContactBean.friendId);
            request.note = etInput.getText().toString();
            request.friendSource = mContactBean.friendSource;
            request.securityId = mContactBean.securityId;

            HttpExecutor.execute(request);
        }

        // 新加标签
        private void addNewLabel() {
            if (labelInputUtils.isInputLargerThanMaxLength(addLabelEditView.getText().toString())) {
                T.ss("输入应小于8个汉字");
                return;
            }
            if (!TextUtils.isEmpty(addLabelEditView.getText())) {
                AddLabelRequest request = new AddLabelRequest(new ApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        addNewLabelView(addLabelEditView.getText().toString());
                        appendNewAddLabel();
                    }

                    @Override
                    public void onComplete() {
                        AppUtil.hideSoftInput(mActivity, addLabelEditView);

                        //选中同步服务器
                        if (getCheckCount() <= MAX_CHECK_COUNT) {
                            postSaveSelectLabels();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        T.ss(reason.getErrReason());
                    }
                });
                request.label = addLabelEditView.getText().toString();
                HttpExecutor.execute(request);
            }
        }


        // 实时选中|取消标签
        private void postSaveSelectLabels() {
            String labels = mContactBean.getLabelsFormatter(mSelectedLabels);
            SaveNoteAndLabelsRequest request = new SaveNoteAndLabelsRequest();
            request.setCallback(new SimpleCommonApiRequestCallback<SuccessBooleanResponse>() {

                @Override
                public void handleInChildThread(ApiData<SuccessBooleanResponse> data) {
                    super.handleInChildThread(data);
                    mContactBean.labels = labels;
                    ContactManager.getInstance().updateLabels(mContactBean);
                }
            });
            request.friendId = String.valueOf(mContactBean.friendId);
            request.friendSource = mContactBean.friendSource;
            request.securityId = mContactBean.securityId;
            request.labels = labels;
            request.note = mNote;
            HttpExecutor.execute(request);
        }

        private MTextView getmAddLabel() {
            if (mNewAddLabel == null) {
                mNewAddLabel = new MTextView(mActivity);
                mNewAddLabel.setText("+ 添加");
                mNewAddLabel.setGravity(Gravity.CENTER);
                mNewAddLabel.setTextColor(ContextCompat.getColor(mActivity, R.color.app_green));
                mNewAddLabel.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
                int leftPadding = Scale.dip2px(mActivity, 12);
                int topPadding = Scale.dip2px(mActivity, 6);
                mNewAddLabel.setPadding(leftPadding, topPadding, leftPadding, topPadding);
                mNewAddLabel.setBackgroundResource(R.drawable.bg_contact_tag_unselect);
                mNewAddLabel.setOnClickListener(v -> showAddInput());
            }

            return mNewAddLabel;
        }

        //显示添加标签按钮
        private void showAddInput() {
            addLabelLayout.setVisibility(View.VISIBLE);
            labelsView.removeView(getmAddLabel());
            AppUtil.showSoftInput(mActivity, addLabelEditView);
            scrollToBottom();
        }

        //滑动到底部
        private void scrollToBottom() {
            scrollView.post(() -> {
                scrollView.fullScroll(View.FOCUS_DOWN);
                addLabelRequestFocus();
            });
        }


        //添加标签获得焦点
        private void addLabelRequestFocus() {
            addLabelEditView.requestFocus();
            addLabelEditView.setFocusable(true);
            addLabelEditView.setFocusableInTouchMode(true);
        }


        //添加 "添加按钮"
        private void appendNewAddLabel() {

            int childCount = labelsView.getChildCount();
            if (childCount > 0 && childCount < MAX_CHILD_COUNT) {
                MTextView addLabel = getmAddLabel();
                // 如果已经有了父容器,先删除
                ViewGroup parent = (ViewGroup) addLabel.getParent();
                if (parent != null) {
                    parent.removeView(addLabel);
                }

                ViewGroup.MarginLayoutParams params = new ViewGroup.MarginLayoutParams(ViewGroup.MarginLayoutParams.WRAP_CONTENT, ViewGroup.MarginLayoutParams.WRAP_CONTENT);
                params.bottomMargin = ZPUIDisplayHelper.dp2px(mActivity, 10F);
                labelsView.addView(addLabel, params);
            }

            // 展示 添加按钮后 移除添加备注布局
            addLabelLayout.setVisibility(View.GONE);
            addLabelEditView.setText("");
        }
    }

    /**
     * 选择标记布局
     */
    class MarkViewHolder {
        RelativeLayout starLayout;
        ImageView starImageView;
        MTextView starTitleView;
        RelativeLayout labelLayout;
        MTextView labelDescView;
        FlexboxLayout labelsPreviewView;
        HireProgressGrayLayout progressLayout;
        RelativeLayout unfitLayout;
        ImageView labelImageView;
        View starDivider;
        View parentView;


        private MarkViewHolder(View parentView) {
            this.parentView = parentView;
            starLayout = parentView.findViewById(R.id.star_rl);
            starImageView = parentView.findViewById(R.id.star_iv);
            starTitleView = parentView.findViewById(R.id.star_title);
            labelLayout = parentView.findViewById(R.id.rl_label);
            labelDescView = parentView.findViewById(R.id.label_desc);
            labelImageView = parentView.findViewById(R.id.iv_label);
            labelsPreviewView = parentView.findViewById(R.id.labelsLayout);
            progressLayout = parentView.findViewById(R.id.mHireProgresslayout);
            starDivider = parentView.findViewById(R.id.divider_star);
            unfitLayout = parentView.findViewById(R.id.unfit_rl);

            labelLayout.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_CLICK_REMARK_GEEK).secId(mContactBean.securityId)
                            .param("p", String.valueOf(mContactBean.friendId))
                            .param("p2", 0)
                            .build();
                    changeLayoutStatus(TYPE_LABEL);
                }
            });

            unfitLayout.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {

                    // 已经设置了不合适
                    if (mContactBean.isReject) {
                        doMarkAction("3");
                        ContactKeyManager.getInstance().onCheckListener(mContactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                                params.markType = 1;
                                params.markId = mContactBean.friendId;
//                                params.pageType = pageType;
                                params.jobId = mContactBean.jobId;
                                params.expectId = mContactBean.jobIntentId;
                                params.lid = TextUtils.isEmpty(mContactBean.lid) ? "" : mContactBean.lid;
                                params.securityId = mContactBean.securityId;
                                RejectHttpManager.getInstance().cancelLock(mContactBean, params, true, null);
                            }
                        }, ContactKeyManager.BgSource.UNFIT_SOURCE);
                        return;
                    }
                    getReasonsList(null);
                }
            });

            if (mContactBean.friendSource == ContactBean.FROM_DIAN_ZHANG) {
                starLayout.setVisibility(View.GONE);
                starDivider.setVisibility(View.GONE);
            } else {
                starLayout.setVisibility(View.VISIBLE);
                starDivider.setVisibility(View.VISIBLE);
                starLayout.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        doMarkAction(mContactBean.isStar ? "5" : "4");

                        BossUnfitChangeManager.getInstance().onStartChangeListener(UserRejectManager.PAGE_TYPE_CHAT_PAGE, mContactBean, new RejectHttpManager.OnLockUnLockCallBack() {

                            @Override
                            public void onSuccess(ContactBean contactBean) {
                                changeStarView(starImageView, starTitleView);
                                dismissDialog();
                            }

                            @Override
                            public void onFailed(ContactBean contactBean) {

                            }
                        });
                    }
                });
            }


            progressLayout.setSecurityId(mContactBean.securityId);
        }

        private void initView() {
            boolean hasChecked = !LList.isEmpty(mSelectedLabels);
            if (hasChecked) {
                labelsPreviewView.setVisibility(View.VISIBLE);
                labelImageView.setImageResource(R.mipmap.icon_label_select);
                labelDescView.setVisibility(View.GONE);
                labelsPreviewView.removeAllViews();
                for (String label : mSelectedLabels) {
                    final CheckBox labelView = (CheckBox) LayoutInflater.from(mActivity).inflate(R.layout.contact_item_boss_preview_label, labelsPreviewView, false);
                    labelView.setText(label);
                    labelsPreviewView.addView(labelView);
                }
            } else {
                labelsPreviewView.setVisibility(View.GONE);
                labelImageView.setImageResource(R.mipmap.icon_label_unselect);
                labelDescView.setVisibility(View.VISIBLE);
            }

            changeStarView(starImageView, starTitleView);
            progressLayout.load();

            mContainerView.removeAllViews();
            mContainerView.addView(parentView);
        }

        private void changeStarView(ImageView starImageView, MTextView starTitleView) {
            starImageView.setImageResource(mContactBean.isStar ? R.mipmap.icon_star_select : R.mipmap.icon_star_unselect);
            starTitleView.setText(mContactBean.isStar ? "取消收藏" : "收藏");
        }
    }

    /**
     * 收藏 取消收藏， 标记不合适，取消不合适埋点
     * <p>
     * 1. 点击不合适 3. 取消 4.点击星标 5.取消星标
     */
    private void doMarkAction(String action) {

        AnalyticsFactory analyticsFactory = AnalyticsFactory.create().action("boss-hide");
        analyticsFactory.param("p", String.valueOf(mContactBean.friendId));
        analyticsFactory.param("p2", String.valueOf(mContactBean.jobId));
        analyticsFactory.param("p3", String.valueOf(mContactBean.jobIntentId));
        analyticsFactory.param("p5", action);
        analyticsFactory.param("p8", "2");
        analyticsFactory.build();
    }
}
