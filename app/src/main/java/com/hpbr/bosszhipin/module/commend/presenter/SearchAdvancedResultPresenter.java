package com.hpbr.bosszhipin.module.commend.presenter;

import android.Manifest;
import android.app.Activity;
import android.text.TextUtils;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.hpbr.bosszhipin.advancedsearch.export.AdsSearchFilterType;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.module.commend.activity.advanced.common.BossFilterConditionHelper;
import com.hpbr.bosszhipin.module.commend.entity.AdvancedSearchBean;
import com.hpbr.bosszhipin.module.commend.entity.AdvancedSearchBean.PQuery;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.commend.entity.RegionBean;
import com.hpbr.bosszhipin.module.commend.entity.manager.FilterBeanManager;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.position.utils.SalaryDescShowBaseHelper;
import com.hpbr.bosszhipin.module.search.AdvancedSearchFormatter;
import com.hpbr.bosszhipin.module.search.FilterConstants;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.hpbr.bosszhipin.views.wheelview.AdvanceSearchAgeWheelView.LevelAge;
import com.hpbr.bosszhipin.views.wheelview.eduexp.EducateExpUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Author: ZhouYou
 * Date: 2017/9/14.
 */
public class SearchAdvancedResultPresenter {

    protected AdvancedSearchBean advancedSearchBean;

    protected ISearchAdvancedResultListener listener;

    protected boolean hasConditionSelected;

    protected ArrayList<FilterBean> mSelectedCondition = new ArrayList<>();

    public void setAdvancedSearchBean(AdvancedSearchBean advancedSearchBean) {
        this.advancedSearchBean = advancedSearchBean;
    }

    public SearchAdvancedResultPresenter(Activity activity, AdvancedSearchBean advancedSearchBean, ISearchAdvancedResultListener listener) {
        this.advancedSearchBean = advancedSearchBean;
        this.listener = listener;
        startLocation(activity);
    }

    private void startLocation(Activity activity) {
        String[] PERMISSIONS = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION};
        boolean lostLocationInfo = (LocationService.location == null);
        boolean hasPermission = PermissionManager.checkAllSelfPermissions(activity, PERMISSIONS);
        if (lostLocationInfo && hasPermission) {
            LocationService mLocationService = new LocationService(activity);
            mLocationService.setOnLocationCallback(new LocationService.OnLocationCallback() {
                @Override
                public void onLocationCallback(boolean success, LocationService.LocationBean locationBean) {
                    mLocationService.stop();
                }
            });
            mLocationService.start();
        }
    }

    public AdvancedSearchBean getAdvancedSearchBean() {
        return advancedSearchBean;
    }

    public void setup() {
        if (advancedSearchBean == null) return;
        mSelectedCondition.clear();
        PQuery pQuery = LList
                .getElement(advancedSearchBean.companyNames, 0);
        if (pQuery != null) {
            listener.initSearchText(pQuery.query);
        }
        initCity();
        initSearchResultArrange();
        initPosition();
        initDegree();
        initExp();
        initOther();
        initGeekJobRequirements();
        initSwitchFreq();
        initGender();
        initExchangeResume();
        initRecentNotView();
        initManageExperience();
        initWithDesignImgs();
        initOverSeaWorkExp();
        initOverSeaWorkLang();
        initCondition();
    }

    private void initCondition() {
        listener.initCondition(mSelectedCondition);
    }

    /**
     * 筛选项第一个可选项调整为城市
     * 若用户选择的为不限职位，则默认选中用户的定位城市
     * 若用户选择的为在线职位，则默认选中该在线职位对应的城市；若用户切换在线职位，则城市的值进行更新，默认选中新职位对应的城市，若切换为不限职位，则城市的值不更新
     * 点击后进入「选择城市」页面，可进行城市切换
     * 筛选项有值即高亮展示
     */
    public void initCity() {
        String cityString = "";

        //819.47【商业】搜索支持区域筛选功能的入口文案:
        if (advancedSearchBean.region != null && LText.isEmptyOrNull(cityString)) {
            cityString = RegionBean.obtainRegionTitle(advancedSearchBean.region);
        }

        // 如果没有定位成功，则依然走原始逻辑
        if (TextUtils.isEmpty(cityString)) {
            LevelBean city = LList.getElement(advancedSearchBean.cityList, 0);
            if (city == null || TextUtils.isEmpty(city.name) || city.code == 0) {
                cityString = "全国";
            } else {
                cityString = LText.empty(city.name) ? "全国" : city.name;
                if (cityString.length() > 4) {
                    cityString = cityString.substring(0, 4);
                }
            }
        }

        listener.initCity(cityString);
    }

    private String findLevelBeanName(LevelBean levelBean) {
        return null != levelBean && levelBean.code > 0 && !LText.isEmptyOrNull(levelBean.name) ? levelBean.name : "";
    }

    public void initSearchResultArrange() {
        listener.initSearchResultArrange(advancedSearchBean.sortType);
    }

    private void initPosition() {
        listener.initPosition(advancedSearchBean.currJobName);
    }

    public long getJobId() {
        return advancedSearchBean.currJobId;
    }

    public String getCurrJobName() {
        return advancedSearchBean.currJobName;
    }

    public JobBean getCurrJob() {
        return BossJobListProvider.getInstance().findJobById(getJobId());
    }


//    @Nullable
//    public LevelBean getPosition() {
//        return LList.getElement(advancedSearchBean.positionList, 0);
//    }

    private void initDegree() {
        FilterBean degree = new FilterBean(AdsSearchFilterType.CODE_DEGREE, "学历", "degree");
        long lowDegreeCode = advancedSearchBean.lowDegree.code;
        long highDegreeCode = advancedSearchBean.highDegree.code;
        /* 同时选中最低和最高学历认为选择不限，即没有作选择（默认不限） */
        if ((lowDegreeCode != EducateExpUtil.EduDegreeCode.JUNIOR && lowDegreeCode != 201) // 209为最低学历：初中及以下。201表示不限
                || (highDegreeCode != EducateExpUtil.EduDegreeCode.Doctor && highDegreeCode != 201)) { // 205为最高学历：博士。201表示不限
            degree.subFilterConfigModel.add(new FilterBean(lowDegreeCode, advancedSearchBean.lowDegree.name));
            degree.subFilterConfigModel.add(new FilterBean(highDegreeCode, advancedSearchBean.highDegree.name));
            mSelectedCondition.add(degree);
        }

        FilterBean school = new FilterBean(AdsSearchFilterType.CODE_SCHOOL, "院校要求", "school");
        if (LList.getCount(advancedSearchBean.schoolRequiresEx) > 0) {
            school.subFilterConfigModel.addAll(advancedSearchBean.schoolRequiresEx);
            mSelectedCondition.add(school);
        }
    }

    public ArrayList<FilterBean> getSelectedCondition() {
        return mSelectedCondition;
    }

    /**
     * 这里的数组需要跟 {@link AdsSearchFilterType initWorkYear} 一一对应
     */
    private final SparseArray<String> workYearMap = new SparseArray<>();

    {
        workYearMap.put(-1, "不限");
        workYearMap.put(-3, "在校/应届生");
//        workYearMap.put(0, "应届生");
        workYearMap.put(1, "1年");
        workYearMap.put(2, "2年");
        workYearMap.put(3, "3年");
        workYearMap.put(4, "4年");
        workYearMap.put(5, "5年");
        workYearMap.put(6, "6年");
        workYearMap.put(7, "7年");
        workYearMap.put(8, "8年");
        workYearMap.put(9, "9年");
        workYearMap.put(10, "10年");
        workYearMap.put(11, "10年以上");
    }

    private void initExp() {

        int lowerYear = advancedSearchBean.lowerYear;
        int higherYear = advancedSearchBean.higherYear;
        if (lowerYear != -1 && lowerYear != 0 && !((lowerYear == -2 && higherYear == 11) /* 不限，不添加 */)) {
            FilterBean workYearBean = new FilterBean(AdsSearchFilterType.CODE_WORK_YEAR, "工作经验", "workYear");
            workYearBean.subFilterConfigModel.add(new FilterBean(lowerYear, workYearMap.get(lowerYear)));
            workYearBean.subFilterConfigModel.add(new FilterBean(higherYear, workYearMap.get(higherYear)));
            mSelectedCondition.add(workYearBean);
        }

        //region 期望薪资
        int lowSalary = advancedSearchBean.lowSalary;
        int highSalary = advancedSearchBean.highSalary;
        if ((lowSalary >= FilterConstants.SALARY_LOW && lowSalary <= FilterConstants.SALARY_HIGH)
                && (highSalary >= FilterConstants.SALARY_LOW && highSalary <= FilterConstants.SALARY_HIGH)
                && (highSalary >= lowSalary)) {
            String salarySuffix = SalaryDescShowBaseHelper.getSalarySuffix();
            FilterBean bean = new FilterBean(AdsSearchFilterType.CODE_WORK_SALARY, "期望薪资", "workSalary");
            bean.subFilterConfigModel.add(new FilterBean(lowSalary, lowSalary + salarySuffix));
            bean.subFilterConfigModel.add(new FilterBean(highSalary, highSalary + salarySuffix));
            mSelectedCondition.add(bean);
        }
        //endregion

        int lowAge = fixAge(advancedSearchBean.lowAge);
        int highAge = fixAge(advancedSearchBean.highAge);
        if (lowAge >= LevelAge.LEVEL_LOW.start /* 16 */ && highAge <= LevelAge.LEVEL_LOW.end /* 46 */) {
            if (!(lowAge == LevelAge.LEVEL_LOW.start && highAge == LevelAge.LEVEL_LOW.end /* 16-46: 不限 */)) {
                FilterBean ageBean = new FilterBean(AdsSearchFilterType.CODE_WORK_AGE, "年龄", "workAge");
                ageBean.subFilterConfigModel.add(new FilterBean((long) lowAge, (lowAge == LevelAge.LEVEL_LOW.end ? (FilterConstants.AGE_DEFAULT_HIGH + "岁+") : (lowAge + "岁"))));
                ageBean.subFilterConfigModel.add(new FilterBean((long) highAge, (highAge == LevelAge.LEVEL_LOW.end ? (FilterConstants.AGE_DEFAULT_HIGH + "岁+") : (highAge + "岁"))));
                mSelectedCondition.add(ageBean);
            }
        }

    }

    private int fixAge(int age){
        if(age <= 0){
            return age;
        } else if(age > LevelAge.LEVEL_LOW.end){
            return LevelAge.LEVEL_LOW.end;
        } else if (age < LevelAge.LEVEL_LOW.start){
            return LevelAge.LEVEL_LOW.start;
        }
        return age;
    }

    private void initOther() {
        if (LList.getCount(advancedSearchBean.positionStatusEx) > 0) { // 回填求职状态
            FilterBean status = FilterBeanManager.getInstance().getFilterBossParam4();
            if (status != null) {
                status = new FilterBean(AdsSearchFilterType.CODE_STATUS, status.name, status.paramName);
                status.subFilterConfigModel.addAll(advancedSearchBean.positionStatusEx);
                mSelectedCondition.add(status);
            }
        }
    }

    private void initGeekJobRequirements() {
        if (LList.getCount(advancedSearchBean.geekJobRequirementEx) > 0) { // 回填求职状态
            String geekJobRequirement = advancedSearchBean.geekJobRequirement;
            if (!FilterConstants.DEFAULT_GEEK_JOB_REQUIREMENT.equals(geekJobRequirement)) {
                FilterBean requirement = new FilterBean(AdsSearchFilterType.CODE_JOB_REQUIREMENT, "牛人职位要求", "geekJobRequirements");
                requirement.subFilterConfigModel.addAll(advancedSearchBean.geekJobRequirementEx);
                mSelectedCondition.add(requirement);
            }
        }
    }

    private void initSwitchFreq() {
        if (LList.getCount(advancedSearchBean.switchFreqEx) > 0) { // 回填求职状态
            String switchFreqstr = advancedSearchBean.switchFreq;
            if (!FilterConstants.DEFAULT_SWITCH_FREQ.equals(switchFreqstr)) {
                FilterBean switchFreq = new FilterBean(AdsSearchFilterType.CODE_SWITCH_FREQ, "平均跳槽频率", "switchFreq");
                switchFreq.subFilterConfigModel.addAll(advancedSearchBean.switchFreqEx);
                mSelectedCondition.add(switchFreq);
            }
        }
    }

    private void initGender() {
        if (LList.getCount(advancedSearchBean.newGenderEx) > 0) { // 回填求职状态
            String newGenderStr = advancedSearchBean.newGender;
            if (!FilterConstants.DEFAULT_NEW_GENDER.equals(newGenderStr)) {
                FilterBean newGender = new FilterBean(AdsSearchFilterType.CODE_GENDER, "性别", "gender");
                newGender.subFilterConfigModel.addAll(advancedSearchBean.newGenderEx);
                mSelectedCondition.add(newGender);
            }
        }
    }

    private void initExchangeResume() {
        if (LList.getCount(advancedSearchBean.exchangeResumeEx) > 0) { // 回填是否和同事交换简历
            String newExchangeResumeStr = advancedSearchBean.exchangeResume;
            if (!FilterConstants.DEFAULT_EXCHANGE_RESUME.equals(newExchangeResumeStr)) {
                FilterBean newExchangeResume = new FilterBean(AdsSearchFilterType.CODE_EXCHANGE_RESUME_WITH_COLLEAGUE, "是否和同事交换简历", "exchangeResumeWithColleague");
                newExchangeResume.subFilterConfigModel.addAll(advancedSearchBean.exchangeResumeEx);
                mSelectedCondition.add(newExchangeResume);
            }
        }
    }

    private void initRecentNotView() {
        if (LList.getCount(advancedSearchBean.recentNotViewEx) > 0) { // 回填是否和同事交换简历
            String newRecentNotViewStr = advancedSearchBean.recentNotView;
            if (!FilterConstants.DEFAULT_RECENT_NOT_VIEW.equals(newRecentNotViewStr)) {
                FilterBean newRecentNotView = new FilterBean(AdsSearchFilterType.CODE_RECENT_NOT_VIEW, "近期没看过", "recentNotView");
                newRecentNotView.subFilterConfigModel.addAll(advancedSearchBean.recentNotViewEx);
                mSelectedCondition.add(newRecentNotView);
            }
        }
    }

    private void initManageExperience() {
        if (LList.getCount(advancedSearchBean.manageExperienceEx) > 0) { // 回填有无管理经验
            String newManageExperienceStr = advancedSearchBean.manageExperience;
            if (!FilterConstants.DEFAULT_MANAGE_EXPERIENCE.equals(newManageExperienceStr)) {
                FilterBean newManageExperience = new FilterBean(AdsSearchFilterType.CODE_MANAGE_EXPERIENCE, "有无管理经验", "manageExperience");
                newManageExperience.subFilterConfigModel.addAll(advancedSearchBean.manageExperienceEx);
                mSelectedCondition.add(newManageExperience);
            }
        }
    }

    private void initWithDesignImgs() {
        if (LList.getCount(advancedSearchBean.withDesignImgsEx) > 0) { // 回填有无管理经验
            String newWithDesignImgsStr = advancedSearchBean.withDesignImgs;
            if (!FilterConstants.DEFAULT_WITH_DESIGN_IMGS.equals(newWithDesignImgsStr)) {
                markDesignGuideCardUsedForToday();
                FilterBean newWithDesignImgs = new FilterBean(AdsSearchFilterType.CODE_WITH_DESIGN_IMGS, "有无作品集", "withDesignImgs");
                newWithDesignImgs.subFilterConfigModel.addAll(advancedSearchBean.withDesignImgsEx);
                mSelectedCondition.add(newWithDesignImgs);
            }
        }
    }
    private void initOverSeaWorkExp() {
        if (LList.getCount(advancedSearchBean.overSeaWorkExpEx) > 0) { // 回填有无管理经验
            String str = advancedSearchBean.overSeaWorkExp;
            if (!FilterConstants.DEFAULT_OVER_SEA_WORK_EXP.equals(str)) {
                FilterBean bean = new FilterBean(AdsSearchFilterType.CODE_OVER_SEA_WORK_EXP, "海外经历", BossFilterConditionHelper.KEY_OVER_SEA_WORK_EXP);
                bean.subFilterConfigModel.addAll(advancedSearchBean.overSeaWorkExpEx);
                mSelectedCondition.add(bean);
            }
        }
    }
    private void initOverSeaWorkLang() {
        if (LList.getCount(advancedSearchBean.overSeaWorkLangEx) > 0) { // 回填有无管理经验
            String str = advancedSearchBean.overSeaWorkLang;
            if (!FilterConstants.DEFAULT_OVER_SEA_WORK_LANG.equals(str)) {
                FilterBean bean = new FilterBean(AdsSearchFilterType.CODE_OVER_SEA_WORK_LANG, "语言能力", BossFilterConditionHelper.KEY_OVER_SEA_WORK_LANG);
                bean.subFilterConfigModel.addAll(advancedSearchBean.overSeaWorkLangEx);
                mSelectedCondition.add(bean);
            }
        }
    }

    public void addWithDesignImgsToConditions(AdvancedSearchBean advancedSearchBean){
        advancedSearchBean.withDesignImgs = FilterConstants.SELECT_WITH_DESIGN_IMGS;
        advancedSearchBean.withDesignImgsEx = new ArrayList<>();
        advancedSearchBean.withDesignImgsEx.add(new FilterBean(1,"有作品集"));
    }

    public void markDesignGuideCardUsedForToday(){
        TLog.debug("designImg","markUse");
        String currentData = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
        SpManager.get().user().edit().putString(Constants.SP_ADS_RESULT_DESIGN_IMG, currentData).apply();
    }

    public void onConditionSelect(ArrayList<FilterBean> selectedFilterBean) {
        advancedSearchBean.resetDegree();
        advancedSearchBean.resetWorkYear();
        advancedSearchBean.resetSalary();
        advancedSearchBean.resetAge();
        advancedSearchBean.resetStatus();
        advancedSearchBean.resetGeekJobRequirement();
        advancedSearchBean.resetSwitchFreq();
        advancedSearchBean.resetGender();
        advancedSearchBean.resetExchangeResume();
        advancedSearchBean.resetRecentNotView();
        advancedSearchBean.resetManageExperience();
        advancedSearchBean.resetWithDesignImgs();
        advancedSearchBean.resetOverSeaWorkExp();
        advancedSearchBean.resetOverSeaWorkLang();
        mSelectedCondition.clear();
        mSelectedCondition.addAll(selectedFilterBean);
        if (LList.getCount(selectedFilterBean) > 0) {
            for (FilterBean bean : selectedFilterBean) {
                long code = bean.code;

                if (code == AdsSearchFilterType.CODE_DEGREE) {// 学历
                    if (LList.getCount(bean.subFilterConfigModel) != 2) continue;

                    FilterBean start = bean.subFilterConfigModel.get(0);
                    FilterBean end = bean.subFilterConfigModel.get(1);
                    if (start == null || end == null) continue;

                    advancedSearchBean.lowDegree.code = start.code;
                    advancedSearchBean.lowDegree.name = start.name;

                    advancedSearchBean.highDegree.code = end.code;
                    advancedSearchBean.highDegree.name = end.name;
                } else if (code == AdsSearchFilterType.CODE_SCHOOL) {// 院校
                    advancedSearchBean.schoolRequire = connectFilterBeansCode(bean.subFilterConfigModel);
                    advancedSearchBean.schoolRequiresEx = bean.subFilterConfigModel;
                } else if (code == AdsSearchFilterType.CODE_WORK_YEAR) {// 工作经验
                    if (LList.getCount(bean.subFilterConfigModel) != 2) continue;
                    FilterBean start = bean.subFilterConfigModel.get(0);
                    FilterBean end = bean.subFilterConfigModel.get(1);
                    if (start == null || end == null) continue;
                    advancedSearchBean.lowerYear = (int) start.code;
                    advancedSearchBean.higherYear = (int) end.code;
                } else if (code == AdsSearchFilterType.CODE_WORK_SALARY) {// 期望薪资
                    if (LList.getCount(bean.subFilterConfigModel) != 2) continue;
                    FilterBean start = bean.subFilterConfigModel.get(0);
                    FilterBean end = bean.subFilterConfigModel.get(1);
                    if (start == null || end == null) continue;
                    advancedSearchBean.lowSalary = (int) start.code;
                    advancedSearchBean.highSalary = (int) end.code;
                } else if (code == AdsSearchFilterType.CODE_WORK_AGE) {// 年龄
                    if (LList.getCount(bean.subFilterConfigModel) != 2) continue;
                    FilterBean start = bean.subFilterConfigModel.get(0);
                    FilterBean end = bean.subFilterConfigModel.get(1);
                    if (start == null || end == null) continue;
                    advancedSearchBean.lowAge = (int) start.code;
                    advancedSearchBean.highAge = (int) end.code;
                } else if (code == AdsSearchFilterType.CODE_STATUS) {// 求职状态
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.positionStatus = connectFilterBeansCode(subBeans);
                    advancedSearchBean.positionStatusEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_JOB_REQUIREMENT) {// 牛人职位要求
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.geekJobRequirement = connectFilterBeansCode(subBeans);
                    advancedSearchBean.geekJobRequirementEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_SWITCH_FREQ) {// 平均跳槽频率
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.switchFreq = connectFilterBeansCode(subBeans);
                    advancedSearchBean.switchFreqEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_GENDER) {// 性别
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.newGender = connectFilterBeansCode(subBeans);
                    advancedSearchBean.newGenderEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_EXCHANGE_RESUME_WITH_COLLEAGUE) {// 是否和同事交换简历
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.exchangeResume = connectFilterBeansCode(subBeans);
                    advancedSearchBean.exchangeResumeEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_RECENT_NOT_VIEW) {// 近期没看过
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.recentNotView = connectFilterBeansCode(subBeans);
                    advancedSearchBean.recentNotViewEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_MANAGE_EXPERIENCE) {// 有无管理经验
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.manageExperience = connectFilterBeansCode(subBeans);
                    advancedSearchBean.manageExperienceEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_WITH_DESIGN_IMGS) {// 有无管理经验
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.withDesignImgs = connectFilterBeansCode(subBeans);
                    advancedSearchBean.withDesignImgsEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_OVER_SEA_WORK_EXP) {// 海外经历
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.overSeaWorkExp = connectFilterBeansCode(subBeans);
                    advancedSearchBean.overSeaWorkExpEx = subBeans;
                } else if (code == AdsSearchFilterType.CODE_OVER_SEA_WORK_LANG) {// 语言能力
                    List<FilterBean> subBeans = bean.subFilterConfigModel;
                    advancedSearchBean.overSeaWorkLang = connectFilterBeansCode(subBeans);
                    advancedSearchBean.overSeaWorkLangEx = subBeans;
                }
            }
        }
//        listener.listRefreshing();
        hasConditionSelected = true;
    }

    public static String connectFilterBeansCode(List<FilterBean> filterBeans) {
        String connectedCodes;
        if (LList.getCount(filterBeans) > 0) {
            StringBuilder builder = new StringBuilder();
            for (FilterBean sb : filterBeans) {
                builder.append(sb.code);
                builder.append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            connectedCodes = builder.toString();
        } else {
            connectedCodes = "-1";
        }
        return connectedCodes;
    }

    public static String connectFilterBeansCode(List<FilterBean> filterBeans, String emptyStr) {
        String connectedCodes;
        if (LList.getCount(filterBeans) > 0) {
            StringBuilder builder = new StringBuilder();
            for (FilterBean sb : filterBeans) {
                builder.append(sb.code);
                builder.append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            connectedCodes = builder.toString();
        } else {
            connectedCodes = emptyStr;
        }
        return connectedCodes;
    }

    public void addQuery(PQuery query) {

        /* 将搜索词放于集合首位 */
        advancedSearchBean.companyNames.clear();
        advancedSearchBean.companyNames.add(0, query);
        listener.listRefreshing();
    }

    public List<PQuery> getQueryList() {
        return advancedSearchBean.companyNames;
    }

    public PQuery getFirstQuery() {
        return null != advancedSearchBean ? LList.getElement(advancedSearchBean.companyNames, 0) : null;
    }

    public String getFirstQueryText() {
        PQuery element = getFirstQuery();
        return null != element ? element.query : "";
    }

    /**
     * 1001.246【商业】搜索结果页增加意图提示
     */
    public void addIntentQuery(boolean intentQuery) {
        if (null != advancedSearchBean) {
            advancedSearchBean.addJobTitleToQuery = intentQuery;
        }
    }

    /**
     * 转换成JsonString
     */
    public String parseToJsonString() {
        if (advancedSearchBean == null) {
            return "";
        }
        final Map<String, Object> map = new ArrayMap<>();
        putLegencyQueryParams(advancedSearchBean, map, null);
        JSONObject jsonObject = new JSONObject(map);
        TLog.print("AdvanceSearchFilter", "filter = %s", jsonObject.toString());
        return jsonObject.toString();
    }

    /**
     * 订阅筛选条件 917.254【商业】匿名牛人订阅迭代
     *
     * @param replaceQueryList 服务端推荐的订阅词
     */
    public String paresSubscribeParamsToJson(List<String> replaceQueryList) {
        if (advancedSearchBean == null) {
            return "";
        }
        final Map<String, Object> map = new ArrayMap<>();
        putLegencyQueryParams(advancedSearchBean, map, replaceQueryList);
        JSONObject jsonObject = new JSONObject(map);
        TLog.print("AdvanceSearchFilter", "filter = %s", jsonObject.toString());
        return jsonObject.toString();
    }


    private void putLegencyQueryParams(@NonNull AdvancedSearchBean advancedSearchBean, @NonNull Map<String, Object> map, List<String> replaceQueryList) {
        putJobId(advancedSearchBean, map);
        putQueryList(advancedSearchBean, map, replaceQueryList);
        putCommonQueryParams(advancedSearchBean, map);
    }

    private static void putJobId(@NonNull AdvancedSearchBean advancedSearchBean, @NonNull Map<String, Object> map) {
        map.put("jobId", advancedSearchBean.currJobId);
    }

    private static void putQueryList(@NonNull AdvancedSearchBean advancedSearchBean, @NonNull Map<String, Object> map, List<String> replaceQueryList) {
        if (LList.isEmpty(replaceQueryList)) {
            putNormalQueryList(advancedSearchBean, map);
        } else {
            putReplaceQueryList(advancedSearchBean, map, replaceQueryList);
        }
    }

    /**
     * 原有筛选词逻辑
     */
    private static void putNormalQueryList(@NonNull AdvancedSearchBean advancedSearchBean, @NonNull Map<String, Object> map) {
        final JSONArray queryList = new JSONArray();
        /* 追加前缀到搜索词请求接口 */
        for (PQuery pQuery : advancedSearchBean.companyNames) {
            if (null != pQuery && !TextUtils.isEmpty(pQuery.query)) {
                String query = pQuery.query;
                final String prefix = pQuery.prefix;
                try {
                    /* 实际请求接口需要：{"type":"前缀", "value":"搜索词"} */
                    JSONObject queryObj = new JSONObject();
                    queryObj.put("type", prefix == null ? "" : prefix);
                    queryObj.put("value", query);

                    queryList.put(queryObj);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        map.put("queryList", queryList);
    }

    /**
     * 917.254【商业】匿名牛人订阅迭代
     * 替换搜索关键词，使用replaceQueryList替换companyNames中的query
     */
    private static void putReplaceQueryList(@NonNull AdvancedSearchBean advancedSearchBean,
                                            @NonNull Map<String, Object> map,
                                            List<String> replaceQueryList) {

        final JSONArray queryList = new JSONArray();
        if (!LList.isEmpty(replaceQueryList)) {
            /* 追加前缀到搜索词请求接口 */
            String type = "";
            PQuery pQuery = LList.getElement(advancedSearchBean.companyNames, 0);
            if (null != pQuery && !TextUtils.isEmpty(pQuery.prefix)) {
                type = pQuery.prefix;
            }
            for (String query : replaceQueryList) {
                if (!TextUtils.isEmpty(query)) {
                    try {
                        /* 实际请求接口需要：{"type":"前缀", "value":"搜索词"} */
                        JSONObject queryObj = new JSONObject();
                        queryObj.put("type", type);
                        queryObj.put("value", query);

                        queryList.put(queryObj);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        map.put("queryList", queryList);
    }

    /**
     * 筛选参数拼接
     */
    private static void putCommonQueryParams(@NonNull AdvancedSearchBean advancedSearchBean, @NonNull Map<String, Object> map) {

        map.put("keyWordsTag", advancedSearchBean.keyWordsTag);
        map.put("isAny", 1);
        map.put("companyType", 0);
        map.put("schoolType", advancedSearchBean.isSchoolFamous ? 1 : 0);
        // 学历
        map.put("lowDegree", AdvancedSearchFormatter.getLowDegree(advancedSearchBean));
        map.put("highDegree", AdvancedSearchFormatter.getHighDegree(advancedSearchBean));
        //工作经验
        map.put("lowWorkYear", AdvancedSearchFormatter.getLowWorkYear(advancedSearchBean));
        map.put("highWorkYear", AdvancedSearchFormatter.getHighWorkYear(advancedSearchBean));
        //期望薪资
        map.put("lowSalary", AdvancedSearchFormatter.getLowSalary(advancedSearchBean));
        map.put("highSalary", AdvancedSearchFormatter.getHighSalary(advancedSearchBean));
        //求职状态
        map.put("applyStatus", AdvancedSearchFormatter.getApplyStatus(advancedSearchBean));
        //年龄要求
        map.put("age", AdvancedSearchFormatter.getAge(advancedSearchBean));
        //院校要求
        map.put("schoolLevel", AdvancedSearchFormatter.getSchoolLevel(advancedSearchBean));
        //牛人行业要求
        map.put("geekJobRequirements", AdvancedSearchFormatter.getGeekRequirements(advancedSearchBean));
        //性别
        map.put("gender", AdvancedSearchFormatter.getGender(advancedSearchBean));
        //跳槽频率
        map.put("switchFreq", AdvancedSearchFormatter.getSwitchFreq(advancedSearchBean));
        //是否和同事交换简历
        map.put("exchangeResume", AdvancedSearchFormatter.getExchangeResume(advancedSearchBean));
        //近期没有看过
        map.put("viewResume", AdvancedSearchFormatter.getViewResume(advancedSearchBean));
        //有无管理经验
        map.put("manageExperience", AdvancedSearchFormatter.getManageExperience(advancedSearchBean));
        map.put("withDesignImgs", AdvancedSearchFormatter.getWithDesignImgs(advancedSearchBean));
        //海外经历
        map.put(BossFilterConditionHelper.KEY_OVER_SEA_WORK_EXP, AdvancedSearchFormatter.getOverSeaWorkExp(advancedSearchBean));
        //语言能力
        map.put(BossFilterConditionHelper.KEY_OVER_SEA_WORK_LANG, AdvancedSearchFormatter.getOverSeaWorkLang(advancedSearchBean));


        // 附近
        if (advancedSearchBean.distance != null) {
            map.put("distance", advancedSearchBean.distance.code);
        }
        // 默认获取当前定位
        String cityCode = "";
        String cityName = "";
        if (advancedSearchBean.location == null) {
            advancedSearchBean.location = LocationService.location;
        }
        if (advancedSearchBean.location != null) {
            cityCode = advancedSearchBean.location.localCityCode;
            cityName = advancedSearchBean.location.city;

            map.put("latitude", advancedSearchBean.location.latitude);
            map.put("longitude", advancedSearchBean.location.longitude);
            map.put("localCityCode", cityCode);
            map.put("cityName", cityName);
        }

        //819.47【商业】搜索支持区域筛选
        if (advancedSearchBean.region != null) {
            JSONObject jsonObject = RegionBean.obtainRegionJSONObject(advancedSearchBean.region);
            if (null != jsonObject) {
                map.put("region", jsonObject);
            }
        }

        // 城市列表
        LevelBean city;
//        JobBean targetJob = getCurrJob();
        if (advancedSearchBean.region != null && advancedSearchBean.region.code > 0
                && !TextUtils.isEmpty(advancedSearchBean.region.name)) {
            city = advancedSearchBean.region;
        }
//        else if (targetJob != null && LList.getElement(targetJob.spreadCity, 0) != null) { // 优先取推广城市
//            city = LList.getElement(targetJob.spreadCity, 0);
//        }


        else {
            city = LList.getElement(advancedSearchBean.cityList, 0);
        }

        if (city != null && city.code > 0 && !TextUtils.isEmpty(city.name)) {
            JSONArray ja = new JSONArray();
            try {
                JSONObject jo = new JSONObject();
                jo.put("code", city.code);
                jo.put("name", city.name);
                ja.put(jo);
                map.put("cities", ja);
            } catch (JSONException e) {
                e.printStackTrace();
            }

        } else {
            JSONArray ja = new JSONArray();
            try {
                JSONObject jo = new JSONObject();
                jo.put("code", LText.getLong(cityCode));
                jo.put("name", cityName);
                ja.put(jo);
                map.put("cities", ja);
            } catch (JSONException e) {
                e.printStackTrace();
            } catch (NumberFormatException e) {
                TLog.error("AdSearchError", "城市码转换异常");
            }
        }

        // 职位列表
        List<LevelBean> positionList = advancedSearchBean.positionList;
        if (LList.getCount(positionList) > 0) {
            JSONArray ja = new JSONArray();
            for (LevelBean bean : positionList) {
                if (bean == null) continue;
                JSONObject jo = new JSONObject();
                try {
                    jo.put("code", bean.code);
                    jo.put("name", bean.name);
                    ja.put(jo);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            map.put("positions", ja);
        }

        // 排序筛选
        if (advancedSearchBean.sortType <= 0) {
            advancedSearchBean.sortType = 1;
        }
        map.put("sortType", advancedSearchBean.sortType);

        map.put("sugUuid", advancedSearchBean.sugUuid == null ? "" : advancedSearchBean.sugUuid);
        map.put("intentType", advancedSearchBean.intentType);
        map.put("termIconType", advancedSearchBean.termIconType);

        /*1001.246【商业】搜索结果页增加意图提示 addJobTitleToQuery: 1。其他情况可以不传*/
        if (advancedSearchBean.addJobTitleToQuery) {
            map.put("addJobTitleToQuery", "1");
        }

    }

}
