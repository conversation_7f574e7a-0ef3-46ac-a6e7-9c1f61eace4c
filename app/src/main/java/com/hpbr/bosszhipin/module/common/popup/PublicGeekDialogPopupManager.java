package com.hpbr.bosszhipin.module.common.popup;

import static com.hpbr.bosszhipin.module.position.utils.F1ShowCardTimeManager.F1_PUSH_OPEN_SHOW_TIME;
import static com.hpbr.bosszhipin.module_geek_export.GeekPageRouter.jumpToBlueUpdateResume;

import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;

import androidx.annotation.NonNull;

import com.bszp.kernel.user.UserHelper;
import com.facebook.drawee.view.SimpleDraweeView;
import com.fresco.lib.FrescoUtils;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dialog.CertificationViolateRuleDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.GeekItemF1PopWindowDialog;
import com.hpbr.bosszhipin.common.dialog.GeekStudyOverseasAuthenticationDialog;
import com.hpbr.bosszhipin.common.dialog.UserAgreementConfirmDialog;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.interviews.export.InterviewsRouter;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.login.config.ChangeMobileFrom;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.manager.PreDownloadImageManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.common.GeekSimpleWelcomeActivity;
import com.hpbr.bosszhipin.module.common.popup.dialog.F1PersonalityRecommendGuideDialog;
import com.hpbr.bosszhipin.module.common.router.PageRouter;
import com.hpbr.bosszhipin.module.guideprocess.manager.GuideProcessJumpManager;
import com.hpbr.bosszhipin.module.interview.api.InterviewGeekSecurityFeedbackRequest;
import com.hpbr.bosszhipin.module.interview.api.InterviewGeekSecurityFeedbackResponse;
import com.hpbr.bosszhipin.module.main.fragment.geek.dialog.GeekNotifyDialog;
import com.hpbr.bosszhipin.module.main.fragment.geek.dialog.GeekWorkStatusDialog;
import com.hpbr.bosszhipin.module.main.fragment.geek.dialog.InstructOperationDialog;
import com.hpbr.bosszhipin.module.main.fragment.geek.dialog.StudentSwitchDialog;
import com.hpbr.bosszhipin.module.position.utils.F1ShowCardTimeManager;
import com.hpbr.bosszhipin.module_geek_export.GeekBackflowRouter;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.utils.InterviewConstant;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.PopupHelper;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.popup.ZPUIPopup;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.F1DacSafeTipRequest;
import net.bosszhipin.api.F1MiddleTipQueryRequest;
import net.bosszhipin.api.F1MiddleTipQueryResponse;
import net.bosszhipin.api.GeekStudyOverseasAuthenticationRequest;
import net.bosszhipin.api.GeekStudyOverseasAuthenticationResponse;
import net.bosszhipin.api.GeekStudyOverseasAuthenticationUpdateRequest;
import net.bosszhipin.api.GetAgreementNoticeRequest;
import net.bosszhipin.api.GetCertViolateRuleRequest;
import net.bosszhipin.api.GetCertViolateRuleResponse;
import net.bosszhipin.api.GetCheckPhoneStatusRequest;
import net.bosszhipin.api.GetCheckPhoneStatusResponse;
import net.bosszhipin.api.GetGeekItemF1PopWindowRequest;
import net.bosszhipin.api.GetSatisfactionInvestigateRequest;
import net.bosszhipin.api.GetSatisfactionInvestigateResponse;
import net.bosszhipin.api.GetSearchChatCardGuideMessageRequest;
import net.bosszhipin.api.GetSearchChatCardGuideMessageResponse;
import net.bosszhipin.api.GetUserAgreementPopupRequest;
import net.bosszhipin.api.GetUserAgreementPopupResponse;
import net.bosszhipin.api.NativeServicePopCloseRequest;
import net.bosszhipin.api.NativeServicePopRequest;
import net.bosszhipin.api.NativeServicePopResponse;
import net.bosszhipin.api.PublicGeekDialogDataBatchRequest;
import net.bosszhipin.api.PublicGeekDialogDataBatchResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UserActiveQueryRequest;
import net.bosszhipin.api.UserActiveQueryResponse;
import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.api.bean.QueryWorkBean;
import net.bosszhipin.api.bean.ServerCertViolateRuleDialogBean;
import net.bosszhipin.api.bean.ServerSearchChatCardGuideMessageBean;
import net.bosszhipin.api.bean.ServerUserAgreementBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.ArrayList;

import zpui.lib.ui.popup.cookie.ZPUICookieBar;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: ZhouYou
 * Date: 2018/5/8.
 */
public class PublicGeekDialogPopupManager {

    private PublicGeekDialogDataBatchResponse geekDialogDataBatchResponse;

    private BaseActivity activity;
    private int currentSelectIndex;

    private boolean nothingToShow = false;// 本地缓存是否没有需要显示的Dialog

    /*是否已经显示过老用户再次活跃的引导*/
    private boolean isHasShowUserActivePopup;
    /*是否已经显示过「其他」期望职位的完善引导流程*/
    private boolean isHasOtherJobGuide;
    private boolean isShowStatus5;

    private boolean isShowGarbageguide;


    /**
     * 批量获取Dialog数据的网络请求
     */
    public void dialogDataBatchRequest() {
        GetUserAgreementPopupRequest userAgreementPopupRequest = new GetUserAgreementPopupRequest();
        GeekStudyOverseasAuthenticationRequest overseasAuthenticationRequest = new GeekStudyOverseasAuthenticationRequest();
        GetSatisfactionInvestigateRequest userSatisfactionInvestigationRequest = new GetSatisfactionInvestigateRequest();
        UserActiveQueryRequest userActiveQueryRequest = new UserActiveQueryRequest();
        GetSearchChatCardGuideMessageRequest guideMessageRequest = new GetSearchChatCardGuideMessageRequest();
        InterviewGeekSecurityFeedbackRequest interviewFeedbackRequest = new InterviewGeekSecurityFeedbackRequest();
        GetGeekItemF1PopWindowRequest getGeekItemF1PopWindowRequest = new GetGeekItemF1PopWindowRequest();

        GetCheckPhoneStatusRequest getCheckPhoneStatusRequest = new GetCheckPhoneStatusRequest();

        F1MiddleTipQueryRequest f1MiddleTipQueryRequest =new F1MiddleTipQueryRequest();

        PublicGeekDialogDataBatchRequest batchRequest = new PublicGeekDialogDataBatchRequest(new ApiRequestCallback<PublicGeekDialogDataBatchResponse>() {

            @Override
            public void handleInChildThread(ApiData<PublicGeekDialogDataBatchResponse> data) {
                super.handleInChildThread(data);
                if (data == null || data.resp == null) return;
                InterviewGeekSecurityFeedbackResponse interviewFeedbackResponse = data.resp.interviewFeedbackResponse;
                if (interviewFeedbackResponse != null && LList.getCount(interviewFeedbackResponse.evaluateList) > 0) /*缓存图片*/
                    for (InterviewGeekSecurityFeedbackResponse.FeedbackBean option : interviewFeedbackResponse.evaluateList) {
                        if (option == null || TextUtils.isEmpty(option.selectWebapp)) {
                            continue;
                        }
                        PreDownloadImageManager.getFileByImageUrl(App.getAppContext(), option.selectWebapp, PreDownloadImageManager.IMAGE_CACHE_DIR, PreDownloadImageManager.DownLoadFileType.IMAGE);
                    }
            }

            @Override
            public void onSuccess(ApiData<PublicGeekDialogDataBatchResponse> data) {
                geekDialogDataBatchResponse = data.resp;
                if (geekDialogDataBatchResponse != null) {
                    // 牛人登录后的用户协议，当有数据时，则阻断用户操作
                    GetUserAgreementPopupResponse userAgreementPopupResponse = geekDialogDataBatchResponse.userAgreementPopupResponse;
                    if (userAgreementPopupResponse != null && userAgreementPopupResponse.agreement != null) {
                        showUserAgreementDialog(userAgreementPopupResponse.agreement);
                        return;
                    }
                }

                // 首页弹窗
                if (nothingToShow && activity != null) {
                    setData(true);
                    show(activity, currentSelectIndex);
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.d("DialogPopupManager", "数据请求失败");
            }
        });
        batchRequest.overseasAuthenticationRequest = overseasAuthenticationRequest;
        batchRequest.userSatisfactionInvestigationRequest = userSatisfactionInvestigationRequest;
        batchRequest.interviewGeekSecurityFeedbackRequest = interviewFeedbackRequest;
        batchRequest.userAgreementPopupRequest = userAgreementPopupRequest;
        batchRequest.userActiveQueryRequest = userActiveQueryRequest;
        batchRequest.guideMessageRequest = guideMessageRequest;
        batchRequest.getGeekItemF1PopWindowRequest = getGeekItemF1PopWindowRequest;
        batchRequest.nativeServicePopRequest = new NativeServicePopRequest();
        batchRequest.f1DacSafeTipRequest = new F1DacSafeTipRequest();
        batchRequest.getCheckPhoneStatusRequest = getCheckPhoneStatusRequest;

        batchRequest.certViolateRuleRequest = new GetCertViolateRuleRequest();

        batchRequest.f1MiddleTipQueryRequest =f1MiddleTipQueryRequest;
        batchRequest.execute();
    }

    public void judgeDialogSequence(final BaseActivity activity, final int currentSelectIndex) {
        this.activity = activity;
        this.currentSelectIndex = currentSelectIndex;

        if (show(activity, currentSelectIndex)) {
            return;
        } else {
            nothingToShow = true;
        }

        if (geekDialogDataBatchResponse != null) {
            setData(false);

            nothingToShow = !show(activity, currentSelectIndex);
        }
    }

    private void setData(boolean isRefresh) {
        if (geekDialogDataBatchResponse != null) {
            // 1. 海外留学认证引导
            GeekStudyOverseasAuthenticationResponse overseasAuthenticationResponse = geekDialogDataBatchResponse.overseasAuthenticationResponse;
            if (overseasAuthenticationResponse != null
                    && GeekOverseasAuthenticationPopupTask.getInstance().needShow()) {
                boolean canShow = overseasAuthenticationResponse.needGuide == 1;
                if (canShow) {
                    GeekOverseasAuthenticationPopupTask.getInstance().setData(overseasAuthenticationResponse);
                }
            }
            // 2. 用户满意度弹框
            GetSatisfactionInvestigateResponse satisfactionInvestigateResponse = geekDialogDataBatchResponse.satisfactionInvestigateResponse;
            if (satisfactionInvestigateResponse != null
                    && satisfactionInvestigateResponse.surveyId > 0
                    && SatisfactionInvestigateTask.getInstance().needShow()) {
                SatisfactionInvestigateTask.getInstance().setData(satisfactionInvestigateResponse);
            }
            // 3. 获取面试反馈弹框
            InterviewGeekSecurityFeedbackResponse interviewResponse = geekDialogDataBatchResponse.interviewFeedbackResponse;
            if (interviewResponse != null && interviewResponse.exist == 1 && InterviewGeekSecurityFeedbackTask.getInstance().needShow()) {
                InterviewGeekSecurityFeedbackTask.getInstance().setData(interviewResponse);
            }
            // 4. 老用户新召回
            UserActiveQueryResponse userActiveQueryResponse = geekDialogDataBatchResponse.userActiveQueryResponse;
            if (userActiveQueryResponse != null && !userActiveQueryResponse.isShowed) {
                if (userActiveQueryResponse.status == 1 && !isHasShowUserActivePopup) {
                    UserActivePopupTask.getInstance().setData(userActiveQueryResponse);
                } else if (userActiveQueryResponse.status == 2) {
                    QueryWorkBean lastUpdateWork = userActiveQueryResponse.lastUpdateWork;
                    if (lastUpdateWork != null) {
                        if (GeekPageRouter.hasJumpToBlueUpdateResume) return;
                        GeekPageRouter.hasJumpToBlueUpdateResume = true;
                        jumpToBlueUpdateResume(activity, lastUpdateWork.company, lastUpdateWork.position, lastUpdateWork.startDate);
                    }
                } else if (userActiveQueryResponse.status == 3) {
                    UserWorkStatusTask.getInstance().setData(userActiveQueryResponse);
                } else if (userActiveQueryResponse.status == 4) {
                    StudentSwitchTask.getInstance().setData(userActiveQueryResponse);
                } else if (userActiveQueryResponse.status == 5 && !isShowStatus5) {
                    int expGroup = userActiveQueryResponse.extraMap != null ? userActiveQueryResponse.extraMap.expGroup : 0;//0-对照组，1-实验组1（欢迎页）
                    if (!TextUtils.isEmpty(userActiveQueryResponse.url)) {
                        if (expGroup == 1) {
                            GeekSimpleWelcomeActivity.startActivity(activity, userActiveQueryResponse.certName, expGroup, userActiveQueryResponse.url);
                            userActiveQueryResponse.isShowed = true;
                            isShowStatus5 = true;
                        } else {
                            new ZPManager(activity, userActiveQueryResponse.url + "&expGroup=0").handler();
                            userActiveQueryResponse.isShowed = true;
                            isShowStatus5 = true;
                        }
                    }
                } else if (userActiveQueryResponse.status == 6) { // 6无障碍求职C引导完善信息
                    GeekPageRouter.openAccessibilityIntroduction(activity, GeekConsts.DisabledFrom.FROM_F1);
                    userActiveQueryResponse.isShowed = true;
                } else if (userActiveQueryResponse.status == 8 && !isHasOtherJobGuide) { // 1106.7「其他」桶引导添加期望
                    ExtraMapBean extraMap = userActiveQueryResponse.extraMap;
                    if (extraMap != null && LList.getCount(extraMap.recPositions) > 0 && extraMap.expectInfo != null) {

                        Bundle bundle = new Bundle();
                        bundle.putSerializable(Constants.KEY_EXTRA_MAP, userActiveQueryResponse.extraMap);
                        bundle.putInt(Constants.KEY_STATUS, userActiveQueryResponse.status);
                        PageRouter.openOtherJobGuideComplete(activity, bundle);
                        userActiveQueryResponse.isShowed = true;
                        isHasOtherJobGuide = true;
                    }
                } else if (userActiveQueryResponse.status == 9 && !isHasShowUserActivePopup) { //status=9 //垃圾简历引导，使用 url 跳转诊断详情页
                    UserActivePopupTask.getInstance().setData(userActiveQueryResponse);

                }
            }

            // 6. 搜畅引导用户点击卡片
            GetSearchChatCardGuideMessageResponse guideMessageResponse = geekDialogDataBatchResponse.guideMessageResponse;
            if (guideMessageResponse != null
                    && guideMessageResponse.searchChatCardReadMsgGuideVO != null
                    && SearchChatCardGuideMessagePopTask.getInstance().needShow()) {
                SearchChatCardGuideMessagePopTask.getInstance().setData(guideMessageResponse.searchChatCardReadMsgGuideVO);
            }

            //9.本地服务tips卡片
            NativeServicePopResponse nativeServicePopResponse = geekDialogDataBatchResponse.nativeServicePopResponse;
            if (nativeServicePopResponse != null) {
                NativeServiceTipsTask.getInstance().setData(nativeServicePopResponse);
            }



            GetCheckPhoneStatusResponse getCheckPhoneStatusResponse = geekDialogDataBatchResponse.getCheckPhoneStatusResponse;

            if (getCheckPhoneStatusResponse != null) {
                if (getCheckPhoneStatusResponse.isShow) {
                    GetCheckPhoneDialogTask.getInstance().setHasShow(false);
                }
                GetCheckPhoneDialogTask.getInstance().setData(getCheckPhoneStatusResponse);
            }

            GetCertViolateRuleResponse certViolateRuleResponse = geekDialogDataBatchResponse.certViolateRuleResponse;
            if (certViolateRuleResponse != null && certViolateRuleResponse.show && certViolateRuleResponse.dialog != null) {
                CertViolateRuleGeekTask.getInstance().setData(certViolateRuleResponse.dialog);
                geekDialogDataBatchResponse.certViolateRuleResponse = null;
            }

            F1MiddleTipQueryResponse f1MiddleTipQueryResponse =geekDialogDataBatchResponse.f1MiddleTipQueryResponse;
            if (f1MiddleTipQueryResponse!=null&& f1MiddleTipQueryResponse.show){
                if (f1MiddleTipQueryResponse.type==F1MiddleTipQueryResponse.TYPE_F1_LONG_TIME_NO_OPERATION){
                    F1MiddleTipTask.getInstance().setData(f1MiddleTipQueryResponse);
                }else if (f1MiddleTipQueryResponse.type==F1MiddleTipQueryResponse.TYPE_F1_PERSONALITY_RECOMMEND_GUIDE){
                    F1PersonalityRecommendGuideTask.getInstance().setData(f1MiddleTipQueryResponse);
                }
            }


            //endregion

        }

    }

    private static final int HANDLER_SEARCH_CARD_MESSAGE_POP = 1000;
    private final Handler handler = AppThreadFactory.createMainHandler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == HANDLER_SEARCH_CARD_MESSAGE_POP) {
                ServerSearchChatCardGuideMessageBean guideMessageBean = (ServerSearchChatCardGuideMessageBean) msg.obj;
                if (guideMessageBean != null && currentSelectIndex == 0) {
                    showSearchChatCardGuideMessageCookieBar(activity, guideMessageBean);
                    return true;
                }
            }
            return false;
        }
    });

    /**
     * // 优先级越来越低
     * 0.法务弹窗{@link  GetAgreementNoticeRequest}
     * 1.708 【引导提示】用户协议更新提示
     * 2.安全提示
     * 3.BOSS直聘安全官
     * 4.老用户新召回
     * 5.期望城市修改引导
     * 5. 打开本地通知新规则 1105.603
     * 5.海外留学认证引导
     * 5.F1长时间未关键操作引导
     * 6.获取面试反馈弹框
     * 7.用户满意度弹框
     * 8.搜畅引导用户点击卡片
     * 9.908.0 本地服务tips卡片 ： https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=114650125
     * 10. 805.1 校招直播tips卡片
     * 11.道具节f1牛人弹窗
     * 12.打开本地通知
     * 13. 910.204【C】在职暂不考虑牛人求职状态提示
     * 14、1006.061 账号安全提示
     * 15、1007.061 身份验证引导弹窗
     * 16、金牛认证邀请
     * <p>
     * <p>
     * 其他 {@link net.bosszhipin.api.GetGeekDirectionGuideRequest}
     */
    private boolean show(final BaseActivity activity, final int currentSelectIndex) {
        if (ActivityUtils.isInvalid(activity) || UserManager.isBossRole()) {
            return false;
        }

        if (geekDialogDataBatchResponse != null
                && geekDialogDataBatchResponse.f1DacSafeTipResponse != null
                && geekDialogDataBatchResponse.f1DacSafeTipResponse.show
                && currentSelectIndex == 0) {
            // 0 安全提示
            geekDialogDataBatchResponse.f1DacSafeTipResponse.showDialog(activity);
            return true;
        } else if (CertViolateRuleGeekTask.getInstance().getData() != null) {
            ServerCertViolateRuleDialogBean bean = CertViolateRuleGeekTask.getInstance().getData();
            CertificationViolateRuleDialog d = new CertificationViolateRuleDialog(activity, bean);
            d.show();
            CertViolateRuleGeekTask.getInstance().saveDialogShowTime();
            return true;
        } else if (UserActivePopupTask.getInstance().getData() != null && !isHasShowUserActivePopup) {

            //1. 老用户新召回
            UserActiveQueryResponse userActiveQueryResponse = UserActivePopupTask.getInstance().getData();
            if (userActiveQueryResponse != null) {
                boolean certName = userActiveQueryResponse.certName;
                ExtraMapBean extraMap = userActiveQueryResponse.extraMap;

                if (extraMap != null && LList.getCount(extraMap.supplementList) > 0) {
                    ArrayList<Integer> supplementList = extraMap.supplementList;
                    int guideProcessType = GuideProcessJumpManager.getGuideProcessType(supplementList);
                    if (guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_STUDENT || guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_WORK_EXP ||
                            guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR) {

                        Bundle bundle = new Bundle();
                        bundle.putInt(Constants.KEY_DAYS, userActiveQueryResponse.days);
                        bundle.putBoolean(Constants.KEY_CERT_NAME, userActiveQueryResponse.certName);
                        bundle.putSerializable(Constants.KEY_EXTRA_MAP, extraMap);
                        bundle.putInt(Constants.KEY_STATUS, userActiveQueryResponse.status);
                        bundle.putInt(Constants.KEY_GUIDE_PROGRESS_TYPE, guideProcessType);
                        PageRouter.openGeekOldUserActiveAgainPage(activity, bundle);

                        isHasShowUserActivePopup = true;
                        UserActivePopupTask.getInstance().saveDialogShowTime();
                    } else if (guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_FLOW_BACK_COMPLETE) {
                        GeekBackflowRouter.builder(activity, certName, userActiveQueryResponse.status, GuideProcessJumpManager.getFlowBackPageCodeList(supplementList))
                                .setIsFirstGuideProcess(true)
                                .build()
                                .jump();

                        isHasShowUserActivePopup = true;
                        UserActivePopupTask.getInstance().saveDialogShowTime();
                    }
                } else {
                    if (userActiveQueryResponse.status == 9 && currentSelectIndex == 0) {
                        new ZPManager(activity, userActiveQueryResponse.url).handler();
                        isHasShowUserActivePopup = true;
                        UserActivePopupTask.getInstance().saveDialogShowTime();
                    }

                }

            }
        }else if (F1PersonalityRecommendGuideTask.getInstance().getData()!=null && F1PersonalityRecommendGuideTask.getInstance().needShow() && currentSelectIndex == 0){
            F1MiddleTipQueryResponse response =F1PersonalityRecommendGuideTask.getInstance().getData();
            F1PersonalityRecommendGuideDialog.getInstance(response).show(activity.getSupportFragmentManager(),F1PersonalityRecommendGuideDialog.class.getSimpleName());
            F1PersonalityRecommendGuideTask.getInstance().saveDialogShowTime();
            return true;

        }  else if (!NotificationCheckUtils.areNotificationsEnabled(activity) && F1ShowCardTimeManager.f1NotifyOpenNeedShow()) {
            showNotifyOpenPopup(true);
            return true;
        } else if (GeekOverseasAuthenticationPopupTask.getInstance().getData() != null && currentSelectIndex == 0) {
            //2. 海外留学认证引导
            GeekStudyOverseasAuthenticationDialog d = new GeekStudyOverseasAuthenticationDialog(activity, GeekOverseasAuthenticationPopupTask.getInstance().getData());
            boolean isShow = d.show();
            if (isShow) {
                GeekOverseasAuthenticationPopupTask.getInstance().saveDialogShowTime();
                updateGeekStudyOverseaStatus();
            }
            return isShow;
        } else if (F1MiddleTipTask.getInstance().getData()!=null && F1MiddleTipTask.getInstance().needShow() && currentSelectIndex == 0){
            F1MiddleTipQueryResponse response =F1MiddleTipTask.getInstance().getData();
            if (response.show){
                if (response.type==F1MiddleTipQueryResponse.TYPE_F1_LONG_TIME_NO_OPERATION){
                    InstructOperationDialog.getInstance(response).show(activity.getSupportFragmentManager(),InstructOperationDialog.class.getSimpleName());
                    F1MiddleTipTask.getInstance().saveDialogShowTime();
                    return true;
                }
            }


        } else if (InterviewGeekSecurityFeedbackTask.getInstance().getData() != null
                && InterviewGeekSecurityFeedbackTask.getInstance().getData().exist == 1
                && currentSelectIndex == 0) {
            // 3. 获取面试反馈弹框
            boolean isShow = InterviewsRouter.startGeekFeedbackDialog(activity, InterviewGeekSecurityFeedbackTask.getInstance().getData(), InterviewConstant.FROM_SOURCE_F1);
            if (isShow) {
                InterviewGeekSecurityFeedbackTask.getInstance().saveDialogShowTime();
            }
            return isShow;
        } else if (SatisfactionInvestigateTask.getInstance().getData() != null && currentSelectIndex == 0) {
            //4. 用户满意度弹框
            GetSatisfactionInvestigateResponse data = SatisfactionInvestigateTask.getInstance().getData();
            long surveyId = data.surveyId;
            int messageType = data.preDialog != null ? data.preDialog.messageType : 0;
            int userType = data.userType;

            SatisfactionInvestigateNewStyleDialog investigateNewStyleDialog = new SatisfactionInvestigateNewStyleDialog();
            investigateNewStyleDialog.setOnEventListener((score, content) -> new SatisfactionSubmitSuccessDialog().showDialog(activity, null));
            boolean isShow = investigateNewStyleDialog.showDialog(activity, surveyId, messageType, userType, data.title);
            if (isShow) {
                SatisfactionInvestigateTask.getInstance().saveDialogShowTime();
            }
            return isShow;
        } else if (SearchChatCardGuideMessagePopTask.getInstance().getData() != null && currentSelectIndex == 0) {
            //5. 搜畅引导用户点击卡片
            ServerSearchChatCardGuideMessageBean guideMessageBean = SearchChatCardGuideMessagePopTask.getInstance().getData();

            Message msg = Message.obtain();
            msg.what = HANDLER_SEARCH_CARD_MESSAGE_POP;
            msg.obj = guideMessageBean;
            handler.sendMessageDelayed(msg, 3000);
            return true;
        } else if (NativeServiceTipsTask.getInstance().getData() != null && NativeServiceTipsTask.getInstance().getData().showBubble) {
            //6. 本地服务tips卡片
            showLocalServiceTips(activity, NativeServiceTipsTask.getInstance().getData());
            NativeServiceTipsTask.getInstance().getData().showBubble = false;
            return true;
        } else if (geekDialogDataBatchResponse != null
                && geekDialogDataBatchResponse.getGeekItemF1PopWindowResponse != null
                && geekDialogDataBatchResponse.getGeekItemF1PopWindowResponse.isDisplay()) {
            //9. 道具节f1牛人弹窗(优先级较低)
            GeekItemF1PopWindowDialog d = new GeekItemF1PopWindowDialog(activity, geekDialogDataBatchResponse.getGeekItemF1PopWindowResponse);
            boolean isShow = d.show();
            if (isShow) {
                if (null != geekDialogDataBatchResponse) {
                    geekDialogDataBatchResponse.getGeekItemF1PopWindowResponse = null;
                }
            }
            return isShow;
        } else if (!NotificationCheckUtils.areNotificationsEnabled(activity) && F1ShowCardTimeManager.f1NotifyOpenNeedShow()) {
            //10. 打开本地通知
            showNotifyOpenPopup(false);
            return true;
        } else if (UserWorkStatusTask.getInstance().getData() != null && !UserWorkStatusTask.getInstance().isHasShow()) {
            //11. 910.204【C】在职暂不考虑牛人求职状态提示   https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=117256506
            UserActiveQueryResponse response = UserWorkStatusTask.getInstance().getData();
            GeekWorkStatusDialog geekWorkStatusDialog = new GeekWorkStatusDialog(activity, response.applyStatus, response.title);
            if (ActivityUtils.isValid(activity)) {
                geekWorkStatusDialog.show();
                UserWorkStatusTask.getInstance().setHasShow(true);
            }
            return true;
        } else if (StudentSwitchTask.getInstance().getData() != null && !StudentSwitchTask.getInstance().isHasShow()) {
            //11. 910.204【C】在职暂不考虑牛人求职状态提示   https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=117256506
            UserActiveQueryResponse response = StudentSwitchTask.getInstance().getData();
            StudentSwitchDialog studentSwitchDialog = new StudentSwitchDialog(activity, response);
            studentSwitchDialog.attach(activity);
            if (ActivityUtils.isValid(activity)) {
                studentSwitchDialog.show();
                StudentSwitchTask.getInstance().setHasShow(true);
                response.isShowed = true;
            }
            return true;
        } else if (GetCheckPhoneDialogTask.getInstance() != null && GetCheckPhoneDialogTask.getInstance().getData() != null && GetCheckPhoneDialogTask.getInstance().getData().isShow
                && currentSelectIndex == 0 && !GetCheckPhoneDialogTask.getInstance().isHasShow()) {
            // 1006.061 账号安全提醒
            GetCheckPhoneStatusResponse response = GetCheckPhoneDialogTask.getInstance().getData();
            if (response != null) {
                showChangePhoneDialog();
                GetCheckPhoneDialogTask.getInstance().setHasShow(true);
                response.isShow = false;
                return true;
            }


        } else if (!IgnoreBatteryDialog.isShowed && IgnoreBatteryDialog.isNeedShow()) {
            new IgnoreBatteryDialog().showDialog(activity);
            IgnoreBatteryDialog.isShowed = true;
        }

        return false;
    }

    private void showNotifyOpenPopup(boolean isNewStyle) {
        if (DataStarGray.getInstance().isShowF1NewGuideABTest()){ //F1 列表batch 接口直接引导
            return;
        }
        NotificationCheckUtils.dialogIsShowing = true;
        // isNewStyle 1105.603 半层通知样式
        if (isNewStyle) {
            long currentTime = System.currentTimeMillis();
            new GeekNotifyDialog(activity).show();
            SpManager.get().user().edit().putLong(F1_PUSH_OPEN_SHOW_TIME, currentTime).apply();

            AnalyticsFactory.create().action("push-notice-alert").param("p", 2).build();
            return;
        }
        long currentTime = System.currentTimeMillis();
        ZPUIPopup notifyPopup = ZPUIPopup.create(activity);
        notifyPopup.setContentView(R.layout.layout_f1_notify_open, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT).setOnViewListener((view, popup) -> {
            MTextView tvToOpen = view.findViewById(R.id.tv_to_open);
            SimpleDraweeView lottie = view.findViewById(R.id.mLottieView);
            Uri uri = (new Uri.Builder()).scheme("res")
                    .path(String.valueOf(NightUtil.isDarkMode(activity) ? R.drawable.check_notification_open_animation_dark : R.drawable.check_notification_open_animation)).build();
            String url = uri.toString();
            FrescoUtils.loadGifFromNetwork(lottie, url, true);
            ImageView ivCancel = view.findViewById(R.id.iv_cancle);
            tvToOpen.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AnalyticsFactory.create().action("push-notice-alert-click").param("p", 1).param("p3", 0).build();
                    notifyPopup.dismiss();
                   if (DataStarGray.getInstance().f1PushGuideClickABTest()){
                       NotificationCheckUtils.requestNotificationPermissionToSet(activity);

                   }else{
                       NotificationCheckUtils.requestNotificationPermission(activity, null);
                    }

                }
            });
            ivCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    notifyPopup.dismiss();
                }
            });
        }).setOnDismissListener(() -> NotificationCheckUtils.dialogIsShowing = false).apply();
        activity.getWindow().getDecorView().post(new Runnable() {
            @Override
            public void run() {
                if (ActivityUtils.isValid(activity)) {
                    SpManager.get().user().edit().putLong(F1_PUSH_OPEN_SHOW_TIME, currentTime).apply();
                    notifyPopup.showAtLocation(activity.getWindow().getDecorView(), Gravity.CENTER, 0, 0);
                    AnalyticsFactory.create().action("push-notice-alert").param("p", 0).build();
                }
            }
        });
    }




    private void showChangePhoneDialog() {


        //删除指定的设备
        DialogUtils d = new DialogUtils.Builder(activity)
                .setTitle("手机号还在正常使用吗")
                .setDoubleButton()
                .setDesc("手机号" + ViewCommon.keepPhoneSecret(UserHelper.getPhone()) + "还在正常使用吗，若不使用请及时修改账号绑定的手机号，以免影响正常使用")
                .setNegativeAction("正常使用", v1 -> {

                    finishRequest();
                })
                .setPositiveAction("修改手机号", v12 -> {
                    //删除指定的设备

                    LoginRouter.startChangeMobileActivity(activity, ChangeMobileFrom.FROM_MAINPAGE_DIALOG);
                    finishRequest();
                })
                .build();
        d.show();

    }

    private void finishRequest() {
        SimpleApiRequest.GET(LoginURLConfig.URL_GEEK_CHECK_PHONE_FINISH).execute();
    }


    private void showSearchChatCardGuideMessageCookieBar(BaseActivity
                                                                 activity, @NonNull ServerSearchChatCardGuideMessageBean guideMessageBean) {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_search_chat_card_guide_message_cookie_bar, null);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvMsgTip = view.findViewById(R.id.tv_msg_tip);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        SimpleDraweeView ivAvatar = view.findViewById(R.id.iv_avatar);

        ivAvatar.setImageURI(guideMessageBean.headImg);

        String comInfo = StringUtil.connectTextWithChar("", guideMessageBean.companyName, guideMessageBean.bossTitle);
        String bossInfo = StringUtil.connectTextWithChar("·", guideMessageBean.bossName, comInfo);
        tvTitle.setText(bossInfo);
        tvMsgTip.setText(guideMessageBean.subText);
        tvDesc.setText(guideMessageBean.text);

        view.findViewById(R.id.cl_parent).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_SEARCH_CHAT_CLICK)
                        .param("p", guideMessageBean.bossId)
                        .param("p2", guideMessageBean.jobId)
                        .build();
                SearchChatCardGuideMessagePopTask.getInstance().clearShowTimesAfterClicking();
                new ZPManager(activity, guideMessageBean.jumpUrl).handler();
            }
        });
        ZPUICookieBar cookieBar = new ZPUICookieBar.Builder(activity)
                .setCustomView(view)
                .setDuration(5000)
                .create();
        cookieBar.show();
        SearchChatCardGuideMessagePopTask.getInstance().saveDialogShowTime();
        if (geekDialogDataBatchResponse != null && geekDialogDataBatchResponse.guideMessageResponse != null) {
            geekDialogDataBatchResponse.guideMessageResponse.searchChatCardReadMsgGuideVO = null;
        }
    }

    private void showLocalServiceTips(BaseActivity activity, NativeServicePopResponse response) {
        if (ActivityUtils.isValid(activity)) {
            PopupHelper.Builder builder = new PopupHelper.Builder();
            PopupWindow popupWindow = builder
                    .from(activity)
                    .measure(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                    .inflate(R.layout.view_local_service_tips_bar, null)
                    .background(new ColorDrawable(0x00000000))
                    .build();
            View view = popupWindow.getContentView();
            if (view != null) {
                activity.getWindow().getDecorView().post(new Runnable() {
                    @Override
                    public void run() {
                        if (ActivityUtils.isValid(activity)) {
                            popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
                            popupWindow.setClippingEnabled(true);
                            popupWindow.setOutsideTouchable(false);
                            popupWindow.setFocusable(false);
                            float locationX = (float) (ScreenUtil.getScreenWidth(activity) / 8);
                            popupWindow.showAtLocation(activity.getWindow().getDecorView(), Gravity.BOTTOM, (int) -locationX, 0);
                        }
                    }
                });
                ZPUIRoundButton zrbText = view.findViewById(R.id.zrb_text);
                zrbText.setText(response.desc);

                zrbText.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        popupWindow.dismiss();
                        new ZPManager(activity, response.url).handler();
                    }
                });

                view.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        popupWindow.dismiss();
                    }
                });


                view.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (ActivityUtils.isValid(activity)) {
                            if (popupWindow != null) {
                                popupWindow.dismiss();
                            }
                        }
                    }
                }, 5000);

                popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        NativeServicePopCloseRequest request = new NativeServicePopCloseRequest();
                        request.execute();
                    }
                });
            }

        }
    }

    private void updateGeekStudyOverseaStatus() {
        GeekStudyOverseasAuthenticationUpdateRequest request = new GeekStudyOverseasAuthenticationUpdateRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    private void showUserAgreementDialog(@NonNull ServerUserAgreementBean mUserAgreementBean) {
        UserAgreementConfirmDialog d = new UserAgreementConfirmDialog(activity, mUserAgreementBean, () -> {
            // 当点击同意成功回调之后，则走首页其他弹框的正常逻辑
            if (nothingToShow && activity != null) {
                setData(false);
                show(activity, currentSelectIndex);
            }
        });
        d.show();
    }


}
