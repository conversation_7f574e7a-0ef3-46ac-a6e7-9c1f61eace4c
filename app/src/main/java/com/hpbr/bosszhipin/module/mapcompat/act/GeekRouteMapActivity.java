package com.hpbr.bosszhipin.module.mapcompat.act;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.IMapDelegate;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.map.MarkerOptions;
import com.hpbr.bosszhipin.map.location.LatLonPoint;
import com.hpbr.bosszhipin.map.model.CameraPosition;
import com.hpbr.bosszhipin.map.search.geocode.GeocodeResult;
import com.hpbr.bosszhipin.map.search.geocode.GeocodeSearchCompat;
import com.hpbr.bosszhipin.map.search.geocode.OnGeocodeSearchListener;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeAddress;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeQuery;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeResult;
import com.hpbr.bosszhipin.map.search.poi.PoiItem;
import com.hpbr.bosszhipin.map.search.route.OnRouteSearchListener;
import com.hpbr.bosszhipin.map.search.route.RoutePath;
import com.hpbr.bosszhipin.map.search.route.RoutePlanOption;
import com.hpbr.bosszhipin.map.search.route.RoutePlanSearchCompat;
import com.hpbr.bosszhipin.module.map.bean.GeekRouteBubbleBean;
import com.hpbr.bosszhipin.module.map.bean.GeekRouteParam;
import com.hpbr.bosszhipin.module.map.bean.TimeGuideReport;
import com.hpbr.bosszhipin.module.map.util.AMapUtil;
import com.hpbr.bosszhipin.module.map.util.GeekRouteUtil;
import com.hpbr.bosszhipin.module.map.view.GeekRouteBackDialog;
import com.hpbr.bosszhipin.module.map.view.GeekRouteBottomView;
import com.hpbr.bosszhipin.module.map.view.GeekRouteMapSelectDialog;
import com.hpbr.bosszhipin.module.map.view.GeekRouteTopView;
import com.hpbr.bosszhipin.module.mapcompat.GeekRouteHelper;
import com.hpbr.bosszhipin.module.mapcompat.fragment.GeekRouteBottomContainerFragment;
import com.hpbr.bosszhipin.module.mapcompat.fragment.GeekRouteLeftNavFragment;
import com.hpbr.bosszhipin.module.mapcompat.fragment.GeekRouteRightTrafficSubFragment;
import com.hpbr.bosszhipin.module.mapcompat.helper.GeekRouteOverlayCompat;
import com.hpbr.bosszhipin.module.mapcompat.listener.OnBottomSheetClickListener;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.AppInfoUtils;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.behavior.ViewPagerBottomSheetBehavior;
import com.monch.lbase.LBase;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.DisplayHelper;
import com.twl.utils.GsonUtils;
import com.twl.utils.NetworkUtils;

import net.bosszhipin.api.GetGeekAddressRequest;
import net.bosszhipin.api.GetGeekAddressResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;


/**
 * C看B路线规划页面
 * =====copy from GeekRoute1004NewActivity=====
 */
public class GeekRouteMapActivity extends BaseAwareActivity<GeekRouteMapViewModel> implements OnBottomSheetClickListener, GeekRouteTopView.OnTopViewClickListener,
        GeekRouteRightTrafficSubFragment.OnTrafficOnResumeListener, IMapDelegate.OnCameraChangeListener {

    private static final String TAG = GeekRouteHelper.TAG;
    private static final String KEY_GEEK_ROUTE_PARAM = Constants.PREFIX + ".KEY_GEEK_ROUTE_PARAM";
    /**
     * 用户上次选择的 规划方式
     */
    public static final String KEY_LAST_SELECT_TRAFFIC_TYPE = Constants.PREFIX + ".KEY_LAST_SELECT_TRAFFIC_TYPE";

    /**
     * 终点
     */
    private LatLonPoint mJobLatLonPoint;
    /**
     * 家庭住址坐标位置
     */
    private LatLonPoint mHomeLatLonPoint;
    /**
     * 当前定位位置
     */
    private LatLonPoint mCurrentLocation;

    private MapViewCompat mMapView;
    private FrameLayout mLocationFl;
    private final TimeGuideReport mTimeGuideReport = new TimeGuideReport();
    private boolean mNeedRefresh;// 是否需要刷新 在拒绝权限 或者定位错误的时候， onresume 自动刷新
    /**
     * 起点终点的 marker 上的 文案，分别是起点终点的地址， 如果是定位 则自己拼接
     */
    private String mStartMarkAddress;
    private String mEndMarkAddress;
    private String mCurrentLocationAddress;
    private LocationService.LocationBean locationBean;
    private ViewPagerBottomSheetBehavior<LinearLayout> bottomSheetBehavior;
    private LinearLayout mBottomSheetLayout;
    private GeekRouteBottomContainerFragment mGeekRouteBottomAllFragment;
    private GeekRouteTopView mGeekRouteTopView;
    private boolean mUseCurrent = true;

    /**
     * 是否进入 第一次使用定位， 如果是的话 要判断是否同城 然后切换对应tab
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=182983474
     */
    private boolean mFirstUseLocation;
    /**
     * 第一次进图页面 定位到了 通勤tab 下
     */
    private boolean mFirstSwitchTrafficTab;
    private GeekRouteOverlayCompat mClusterOverlay;
    private final int PEEK_HEIGHT = 320;
    private RoutePlanSearchCompat routePlanSearch;
    private String endCity;
    private int cityCode;

    public static void gotoThisActivity(@NonNull Context context, GeekRouteParam param) {
        Intent intent = new Intent(context, GeekRouteMapActivity.class);
        intent.putExtra(KEY_GEEK_ROUTE_PARAM, param);
        AppUtil.startActivity(context, intent);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        MapViewCompat.initializer(AndroidDataStarGray.getInstance().isUserAMapType() ? MapViewCompat.A_MAP_TYPE : MapViewCompat.B_MAP_TYPE);
        MapViewCompat.setWalkDistanceLimit(AndroidDataStarGray.getInstance().getBMapWalkLimit());
        super.onCreate(savedInstanceState);
        TLog.info(TAG, "isUserAMapType %s style = %s", AndroidDataStarGray.getInstance().isUserAMapType(), AndroidDataStarGray.getInstance().isUserBMapStyle());

        if (!getData()) {
            AppUtil.finishActivity(this, ActivityAnimType.NONE);
            return;
        }

        makeStatusBarTransparent();
        setLightStatusBarFlag();

        mGeekRouteTopView = findViewById(R.id.top_view);

        mBottomSheetLayout = findViewById(R.id.bottomSheetLayout);
        bottomSheetBehavior = ViewPagerBottomSheetBehavior.from(mBottomSheetLayout);
        mLocationFl = findViewById(R.id.location_fl);
        mLocationFl.setOnClickListener(mLocationClickListener);

        mMapView = findViewById(R.id.map_view);
        mMapView.onCreate(savedInstanceState);
        mGeekRouteTopView.setOnTopViewClickListener(this);
        mGeekRouteTopView.setVisibility(View.GONE);
        mMapView.setVisibility(View.GONE);
        if (getParam().analyticParams.source == GeekRouteHelper.SOURCE_FROM_MAP || getParam().analyticParams.source == GeekRouteHelper.SOURCE_FROM_ADDRESS_BAR) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_DETAIL_MAP_ADDRESS)
                    .param("p", getParam().analyticParams.jobId)
                    .param("p2", hasAddress() ? 1 : 2)
                    .param("p3", getParam().analyticParams.source)
                    .param("p4", getParam().analyticParams.lid)
                    .build();
        }

        initBottomTab();
        AnalyticsFactory.create()
                .action("system-newguide-transportation-exposure")
                .param("p", AMapUtil.translateSourceForPageExpose(getParam().analyticParams.guideSource)) //1 职位详情；2 公司主页；3 面试卡片；4 聊天卡片
                .param("p2", 1)
                .debug()
                .build();

        bottomSheetBehavior.setPeekHeight(DisplayHelper.dp2px(this, PEEK_HEIGHT));
        changeBsHeight(mBottomSheetLayout, DisplayHelper.getScreenHeight(this) - DisplayHelper.dp2px(this, 80));
        initRefreshBtn(DisplayHelper.dp2px(this, PEEK_HEIGHT));
        bottomSheetBehavior.setBottomSheetCallback(new ViewPagerBottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == ViewPagerBottomSheetBehavior.STATE_COLLAPSED) {
                    AppUtil.hideSoftInput(GeekRouteMapActivity.this);
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {

            }
        });
    }

    @Override
    protected int contentLayout() {
        return R.layout.geek_activity_route_map;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {

    }

    /**
     * 头部 tab 的展现  用于打点
     */
    private String mTopShowAnalytic;

    /**
     * 要根据 接口是否返回 职位 ， 显示 职位tab
     */
    private void initBottomTab() {
        StringBuilder titleP = new StringBuilder("路线");

        titleP.append("-周边交通");

        mTopShowAnalytic = titleP.toString();
        mGeekRouteTopView.setVisibility(View.VISIBLE);
        mMapView.setVisibility(View.VISIBLE);

        mGeekRouteBottomAllFragment = GeekRouteBottomContainerFragment.newInstance(this, mJobLatLonPoint, getParam().targetAddress, this);
        getSupportFragmentManager().beginTransaction().replace(R.id.bottom_tab_container_fl, mGeekRouteBottomAllFragment, "floatTab").commitAllowingStateLoss();

        mGeekRouteBottomAllFragment.setOnRouteFragmentInitListener(new GeekRouteLeftNavFragment.OnRouteFragmentInitListener() {
            @Override
            public void onFragmentInit() {//有关面板的操作
                mGeekRouteBottomAllFragment.setEndText(getParam().targetAddress);
                mGeekRouteBottomAllFragment.setHomeAddress(getParam().homeAddress.poiTitle);
                mGeekRouteTopView.post(new Runnable() {
                    @Override
                    public void run() {
                        bottomSheetBehavior.invalidateScrollingChild();
                    }
                });

                initMap();
            }
        });
        //新增设置终点city，如果家庭住址和定位都为空，才使用
        if (mGeekRouteBottomAllFragment != null && !TextUtils.isEmpty(endCity)) {
            TLog.info(TAG, "reset end city %s", endCity);
            mGeekRouteBottomAllFragment.setEndCity(endCity);
        }
    }

    private void initMap() {
        if (mMapView == null || mMapView.getMap() == null) {
            ToastUtils.showText("数据错误");
            AppUtil.finishActivity(GeekRouteMapActivity.this);
            return;
        }
        mMapView.getMap().moveCamera(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), 18);
        mMapView.getMap().setOnCameraChangeListener(this);

        //看地图marker绘制
        mClusterOverlay = new GeekRouteOverlayCompat(mMapView, null, this);
        routePlanSearch = RoutePlanSearchCompat.newInstance(this);
        routePlanSearch.setOnRouteSearchListener(new OnRouteSearchListener() {
            @Override
            public void onDriveRouteSearched(List<RoutePath> routePaths) {
                if (!LList.isEmpty(routePaths)) {
                    mTimeGuideReport.drive = String.valueOf((int) routePaths.get(0).getDuration() / 60);
                } else {
                    showErrorView();
                }

                if(null!=mGeekRouteBottomAllFragment){
                    if (!LList.isEmpty(routePaths)) {
                        mGeekRouteBottomAllFragment.setDrivePathList(routePaths);
                    }
                    mGeekRouteBottomAllFragment.showDriveRoute();
                }
                report(null!=routePaths ? 0 : 2);
            }

            @Override
            public void onBusRouteSearched(List<RoutePath> routePaths) {
                //==公交
                TLog.info(TAG, "onBusRouteSearched====" + "routePaths = [" + routePaths + "]");
                if (LList.isEmpty(routePaths) || null == mGeekRouteBottomAllFragment) {
                    showErrorView();
                }

                if(null!=mGeekRouteBottomAllFragment){
                    mGeekRouteBottomAllFragment.hideLoading();
                    mGeekRouteBottomAllFragment.setBusPathList(routePaths);
                    mGeekRouteBottomAllFragment.showBusRoute();
                }
                report(null!=routePaths ? 0 : 2);
            }

            @Override
            public void onWalkRouteSearched(List<RoutePath> routePaths) {
                //==步行
                TLog.info(TAG, "onWalkRouteSearched====" + "routePaths = [" + routePaths + "]");
                if (null!=routePaths && !LList.isEmpty(routePaths)) {
                    mTimeGuideReport.walk = String.valueOf((int) routePaths.get(0).getDuration() / 60);
                } else {
                    showErrorView();
                }

                if(null!=mGeekRouteBottomAllFragment){
                    mGeekRouteBottomAllFragment.setWalkPathList(routePaths);
                    mGeekRouteBottomAllFragment.showWalkRoute();
                }

                report(null!=routePaths ? 0 : 2);
            }

            @Override
            public void onBikingRouteSearched(List<RoutePath> routePaths) {
                //==骑行
                TLog.info(TAG, "onBikingRouteSearched====" + "routePaths = [" + routePaths + "]");
                if (!LList.isEmpty(routePaths)) {
                    mTimeGuideReport.ride = String.valueOf(routePaths.get(0).getDuration() / 60);
                } else {
                    showErrorView();
                }

                if(null!=mGeekRouteBottomAllFragment){
                    mGeekRouteBottomAllFragment.setRidePathList(routePaths);
                    mGeekRouteBottomAllFragment.showRideRoute();
                }

                report(null!=routePaths ? 0 : 2);
            }
        });
        mMapView.showMapContentApprovalNumber(ZPUIDisplayHelper.dp2px(this, 270));
        initRoute();

    }


    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        TLog.info("AMap", "onCameraChangeFinish %s", cameraPosition);
    }

    private void initRoute() {
        mGeekRouteBottomAllFragment.showLoading();
        if (UserManager.isGeekRole()) {
            if (hasAddress()) {
                if (TextUtils.isEmpty(getParam().homeAddress.poiTitle)) {
                    getParam().homeAddress.poiTitle = "我的住址";
                }
                mStartMarkAddress = getParam().homeAddress.poiTitle;
                mGeekRouteBottomAllFragment.setStartText(mStartMarkAddress);
                mGeekRouteBottomAllFragment.setHomeAddress(mStartMarkAddress);
                mViewModel.startPositionTagLiveData.postValue(LText.getString(R.string.string_my_home_position));
                useHomeAddressRefreshUi(true);
                getCityByLatLon(getParam().homeAddress.latitude, getParam().homeAddress.longitude);
                switchAnalytic(2);
                GeekRouteUtil.addUseRouteCount();
            } else if (getParam().cityCode != 0) {
                getGeekHomeAddress();
            } else {
                dealLogicWhenFirstIn();
            }
        } else { //  这块逻辑是自己加的 因为boss 从 预览公司主页   也可能进入这个页面 不走上面的逻辑
            requestPermission(false);
        }

    }


    private void searchTrafficWay(int trafficMode) {
        mTimeGuideReport.clear();

        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.GONE);
        }

        LatLonPoint startLocationPoint = getStartLocationPoint();
        if (startLocationPoint == null) {
            showErrorView();
            return;
        }
        if (mJobLatLonPoint == null) {
            ToastUtils.showText("终点未设置");
            showErrorView();
            return;
        }
        if (routePlanSearch == null) {
            TLog.info(TAG, "未实例化成功");
            return;
        }

        LatLonPoint fromLatLon = new LatLonPoint(startLocationPoint.getLatitude(), startLocationPoint.getLongitude());
        LatLonPoint toLatLon = new LatLonPoint(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude());

        if (trafficMode == GeekRouteBottomView.NAV_MODE.MODE_BUS) {
            // 公交路径规划
            // 第一个参数表示路径规划的起点和终点，第二个参数表示公交查询模式，第三个参数表示公交查询城市区号，第四个参数表示是否计算夜班车，0表示不计算
            // 异步路径规划公交模式查询
            routePlanSearch.masstransitSearch(new RoutePlanOption(fromLatLon, toLatLon, getRouteCity()));
        } else if (trafficMode == GeekRouteBottomView.NAV_MODE.MODE_DRIVE) {
            // 驾车路径规划
            // 第一个参数表示路径规划的起点和终点，第二个参数表示驾车模式，第三个参数表示途经点，第四个参数表示避让区域，第五个参数表示避让道路
            // 异步路径规划驾车模式查询
            routePlanSearch.drivingSearch(new RoutePlanOption(fromLatLon, toLatLon, getRouteCity()));
        } else if (trafficMode == GeekRouteBottomView.NAV_MODE.MODE_WALK) {
            // 步行路径规划
            // 异步路径规划步行模式查询
            routePlanSearch.walkingSearch(new RoutePlanOption(fromLatLon, toLatLon, getRouteCity()));
        } else if (trafficMode == GeekRouteBottomView.NAV_MODE.MODE_RIDE) {
            // 骑行路径规划
            routePlanSearch.bikingSearch(new RoutePlanOption(fromLatLon, toLatLon, getRouteCity()));
        }
        TLog.info(TAG, "trafficMode=%s", trafficMode);
    }

    private GeekRouteParam getParam() {
        return (GeekRouteParam) getIntent().getSerializableExtra(KEY_GEEK_ROUTE_PARAM);
    }

    private boolean getData() {
        GeekRouteParam param = getParam();
        if (param == null) {
            ToastUtils.showText("没有合适参数");
            TLog.info("GeekRouteNewActivity", "没有合适参数");
            return false;
        }
        mEndMarkAddress = param.targetAddress;
        if (param.homeAddress.latitude != 0) {
            mHomeLatLonPoint = new LatLonPoint(param.homeAddress.latitude, param.homeAddress.longitude);
        }
        if (param.latitude <= 0 || param.longitude <= 0 || param.latitude >= 180 || param.longitude >= 180) {
            ToastUtils.showText(this, getString(R.string.string_address_latlon_null));
            return false;
        }
        mJobLatLonPoint = new LatLonPoint(param.latitude, param.longitude);
        endCity = param.city;
        cityCode = param.cityCode;
        return true;
    }

    private void switchAnalytic(int p1) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_MAP_SWITCH_ADDRESS)
                .param("p", p1) //1、点击“当前定位”；2、点击“我的住址”
                .param("p2", AMapUtil.translateSwitchSource(getParam().analyticParams.guideSource)) //点击用户的入口来源： 0 职位详情页； 1 面试卡片； 2 聊天位置卡片； 3 品牌主页； 4 其他
                .debug()
                .build();

        //0其他 1公司主页 2职位详情 3面试详情 4聊天卡片
    }

    private void requestPermission(boolean isForceRequestLocation) {
        PermissionHelper.getLocationHelper(this, LocationPermissionHelper.GEEK_NAV_ROUTE, new PermissionHelper.ExtendBean().setAnalyticName(PermissionConstants.SCENE_MAP_GUIDE))
                .sync(true)
                .setPermissionCallback(new PermissionCallback<LocationPermissionHelper.LocationPermissionData>() {

                    @Override
                    public void onResult(boolean yes, LocationPermissionHelper.LocationPermissionData permission) {
                        if(null!=mGeekRouteBottomAllFragment){
                            mGeekRouteBottomAllFragment.resetRoute();
                        }
                        mUseCurrent = true;
                        if (yes && permission != null && permission.locationBean != null) {
                            locationBean = permission.locationBean;

                            mStartMarkAddress = "当前定位";
                            mCurrentLocationAddress = locationBean.address;
                            if (null!=mGeekRouteBottomAllFragment) {
                                mGeekRouteBottomAllFragment.setStartText(mCurrentLocationAddress);
                                mViewModel.startPositionTagLiveData.postValue(LText.getString(R.string.string_my_position));
                                mGeekRouteBottomAllFragment.setCurrentLocAddress(mCurrentLocationAddress);
                            }

                            if (mFirstUseLocation && getParam().cityCode != 0 && !TextUtils.isEmpty(locationBean.localCityCode) &&
                                    !locationBean.localCityCode.equals(String.valueOf(getParam().cityCode))) {
                                // 第一次进入  没有地址 使用定位的时候，如果不同城，切换到 看地图tab
                                //有家庭住址 或者 有定位且同城  落地看通勤Tab
                                //其他情况落地 看地图Tab
                                mFirstSwitchTrafficTab = true;
                                mGeekRouteTopView.selectTraffic();
                            } else {
                                if (mFirstUseLocation) {
                                    GeekRouteUtil.addUseRouteCount();
                                }
                                onLocationLogicDeal(locationBean);
                            }

                            TLog.info(TAG, "request permission yes  locationBean %s", locationBean.toString());
                        } else {
                            requestPermissionFail();
                        }
                        mFirstUseLocation = false; //第一次使用结束  置为false
                    }

                    @Override
                    public boolean onSkipPermission(@NonNull LocationPermissionHelper.LocationPermissionData permission) {
                        if(isForceRequestLocation){
                            return false;
                        }else{
                            requestPermissionFail();
                            return true;
                        }
                    }
                }).requestPermission();
    }

    private void requestPermissionFail() {
        // 获取定位权限失败，点击重试
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.pathRemoveAllView();
            mGeekRouteBottomAllFragment.resetRoute();
            int mode = getLastSelectMode();
            // 点击对应的通勤模式，如果没有路线，ontabClick 回调回来，再去请求
            mGeekRouteBottomAllFragment.modeClick(mode);
        }

        showErrorView();
        report(1);
        TLog.info(TAG, "request permission no");
    }

    private OnClickNoFastListener mLocationClickListener = new OnClickNoFastListener() {
        @Override
        public void onNoFastClick(View v) {
            //在周边交通下 点击缩放到终点, 点击右下角的重新定位
            if (mGeekRouteBottomAllFragment != null && mGeekRouteBottomAllFragment.getCurrentItem() == 1) {
                mMapView.getMap().moveCamera(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), 15);
            } else {
                if (!mUseCurrent) {
                    useHomeAddressRefreshUi(true);
                } else {
                    requestPermission(false);
                }
            }
        }
    };

    /**
     * 骑行路线点击
     */
    private void rideClick(RoutePath ridePath) {
        translateWayAnalytic(2);
        //isTrafficTab() 为了解决落地看地图，快点到看通勤，再点回看地图，由于看通勤路线绘制较慢，会在看地图视图下绘制
        if (ridePath == null || mGeekRouteTopView.isTrafficTab()) return;
        mMapView.getMap().drawRoute(getStartLocationPoint(),mJobLatLonPoint,ridePath);
    }

    /**
     * 步行路线点击
     */
    private void walkClick(RoutePath walkPath) {
        translateWayAnalytic(4);
        if (walkPath == null || mGeekRouteTopView.isTrafficTab()) return;
        mMapView.getMap().drawRoute(getStartLocationPoint(),mJobLatLonPoint,walkPath);
        int dur = (int) walkPath.getDuration();
        if (dur > 60 * 40) {
            // 超过40分钟
            mGeekRouteTopView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showText(R.string.geek_string_route_walk_too_long);
                }
            }, 400);

        }
    }

    /**
     * 驾驶路线点击
     */
    private void driveClick(RoutePath drivePath) {
        translateWayAnalytic(3);
        if (drivePath == null || mGeekRouteTopView.isTrafficTab()) return;
        mMapView.getMap().drawRoute(getStartLocationPoint(),mJobLatLonPoint,drivePath);
    }

    private void busClick(RoutePath busPath) {
        translateWayAnalytic(1);
        //isTrafficTab() 为了解决落地看地图，快点到看通勤，再点回看地图，由于看通勤路线绘制较慢，会在看地图视图下绘制
        if (busPath == null || mGeekRouteTopView.isTrafficTab()) return;
        mMapView.getMap().drawRoute(getStartLocationPoint(),mJobLatLonPoint,busPath);
    }

    private void showRouteStartAndEndMarke() {
        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        if (getStartLocationPoint() != null && mJobLatLonPoint != null) {
            drawMarkers(getStartLocationPoint().getLatitude(), getStartLocationPoint().getLongitude(), false);
            drawMarkers(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), true);
            mMapView.getMap().moveCamera(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), 15);
        }

    }

    public void onCurrentLocationClick(boolean isForceRequestLocation) {
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        mNeedRefresh = false;
        if (hasLocationPermission() && PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE)) {
            ToastUtils.showText("起点变更成功");
        }
        requestPermission(isForceRequestLocation);
        switchAnalytic(1);

        AnalyticsFactory.create().action("mapaddr-origin")
                .param("p", 1)
                .build();

    }

    public void onHomeAddressClick(boolean isForceRefresh) {
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        AnalyticsFactory.create().action("mapaddr-origin")
                .param("p", 2)
                .build();

        if (mHomeLatLonPoint == null) {
            //是否蓝领职位和没有家庭住址
            jumpToChangeHomeAddress();
        } else {
            if (!mUseCurrent && !isForceRefresh) {
                return;
            }
            switchAnalytic(2);
            ToastUtils.showText("起点变更成功");
            mStartMarkAddress = getParam().homeAddress.poiTitle;
            if (mGeekRouteBottomAllFragment != null) {
                mGeekRouteBottomAllFragment.setStartText(mStartMarkAddress);
                mViewModel.startPositionTagLiveData.postValue(LText.getString(R.string.string_my_home_position));
            }
            useHomeAddressRefreshUi(true);
        }
    }

    @Override
    public void onBottomLocationClick() {
        //我的位置点击
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.recoverUI();
            mGeekRouteBottomAllFragment.resetRoute();
        }
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        onCurrentLocationClick(false);
        mTemporaryPoi = null;
    }

    @Override
    public void onBottomOpenLocationClick() {
        //我的位置点击
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.recoverUI();
            mGeekRouteBottomAllFragment.resetRoute();
        }
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        onCurrentLocationClick(true);
        mTemporaryPoi = null;
    }

    @Override
    public void onBottomHomeAddressClick(boolean isForceRefresh) {
        //我的地址点击
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.recoverUI();
        }
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        mTemporaryPoi = null;
        onHomeAddressClick(isForceRefresh);
    }

    @Override
    public void onBottomHomeAddressEditClick() {
        jumpToChangeHomeAddress();
    }

    /**
     * 临时的选择的POI
     */
    private PoiItem mTemporaryPoi;

    @Override
    public void onBottomPoiClick(PoiItem poiItem) {
        //搜索后位置点击
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.recoverUI();
            mGeekRouteBottomAllFragment.resetRoute();
        }
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        mTemporaryPoi = poiItem;

        ToastUtils.showText("起点变更成功");
        mStartMarkAddress = getParam().homeAddress.poiTitle;
        useHomeAddressRefreshUi(false); //这时候传的是 临时的选点

        TLog.info(TAG, "bottom poi click temporaryPoi %s", null != mTemporaryPoi ? mTemporaryPoi.toString() : "");
    }

    /**
     * 准备输入起点 弹起弹层
     */
    @Override
    public void onStartClick() {
        mGeekRouteTopView.hideBack();
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_EXPANDED);
    }

    @Override
    public void showBack() {
        mGeekRouteTopView.showBack();
    }

    @Override
    public void setBehaviorState(int state) {
        if (bottomSheetBehavior != null) {
            bottomSheetBehavior.setState(state);
        }
    }

    /**
     * 跳转到设置 家庭住址
     */
    private void jumpToChangeHomeAddress() {
        JobIntentBean jobIntentBean = GeekExpectManager.getCurrSelectExpect();

        int jobIntentCityCode = jobIntentBean == null ? 0 : jobIntentBean.locationIndex;
        String jobIntentCityName = jobIntentBean == null ? "" : jobIntentBean.locationName;

        if (jobIntentCityCode <= 0 && cityCode > 0) {
            jobIntentCityCode = cityCode;
            jobIntentCityName = endCity;
        }
        GeekPageRouter.openSelectHomeAddress(this, new GeekPageRouter.SetHomeAddressParam()
                .setCityCode(jobIntentCityCode).setCityName(jobIntentCityName));

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_GEEK_LOCATION_UPDATE).param("p", String.valueOf(getParam().analyticParams.jobId)).build();
    }

    private void analytic(int action) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_TRANSPORTATION_SWITCH)
                .param("p", getParam().analyticParams.jobId)
                .param("p2", String.valueOf(action))
                .param("p3", 1)
                .build();
    }

    /**
     * 公交tab 点击
     */
    @Override
    public void onBusTabClick(boolean refresh) {
        //起点家庭住址或者临时选点，只请求一次。起点当前位置时，点击重新定位会重新请求
        //1、当起点为家庭住址或者临时选点（自己输入的）时
        //--只会请求一次，点击右下角重新定位不会重新请求。修改家庭住址位置，会重新请求。
        //2、当起点为家庭住址
        //--点击导航tab只请求一次，点击右下角重新定位会重新请求一次
        //3、家庭住址、临时选点、当前位置，切换的时候会重新请求
        if (refresh) {
            searchTrafficWay(GeekRouteBottomView.NAV_MODE.MODE_BUS);
        } else{
            TLog.info(TAG, "onBusTabClick refreshLimit");
        }

        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        analytic(1);

    }

    /**
     * 单个公交路线点击
     *
     * @param path
     */
    @Override
    public void onBusItemPathClick(RoutePath path, boolean isUserClick) {
        if (isUserClick) {
            bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        }
        busClick(path);
    }

    /**
     * 骑行tab 点击
     *
     * @param refresh
     */
    @Override
    public void onRideTabClick(boolean refresh) {
        //当前位置或者临时选点或家庭住址，使用缓存，只请求一次。
        if (refresh) {
            searchTrafficWay(GeekRouteBottomView.NAV_MODE.MODE_RIDE);
        } else{
            TLog.info(TAG, "onRideTabClick refreshLimit");
        }

        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        analytic(2);
    }

    /**
     * 单个骑行路线点击
     *
     * @param path
     */
    @Override
    public void onRideItemPathClick(RoutePath path) {
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        rideClick(path);
    }

    /**
     * 驾车tab 点击
     *
     * @param refresh
     */
    @Override
    public void onDriveTabClick(boolean refresh) {
        //当前位置或者临时选点或家庭住址，使用缓存，只请求一次。
        if (refresh) {
            searchTrafficWay(GeekRouteBottomView.NAV_MODE.MODE_DRIVE);
        } else{
            TLog.info(TAG, "onDriveTabClick refreshLimit");
        }


        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        analytic(4);

    }

    /**
     * 单个驾车路线点击
     *
     * @param path
     */
    @Override
    public void onDriveItemPathClick(RoutePath path) {
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        driveClick(path);
    }

    /**
     * 步行tab 点击
     *
     * @param refresh
     */
    @Override
    public void onWalkTabClick(boolean refresh) {
        //当前位置或者临时选点或家庭住址，使用缓存，只请求一次。
        if (refresh) {
            searchTrafficWay(GeekRouteBottomView.NAV_MODE.MODE_WALK);
        } else{
            TLog.info(TAG, "onWalkTabClick refreshLimit");
        }

        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        analytic(3);
    }

    /**
     * 单个步行路线点击
     *
     * @param path
     */
    @Override
    public void onWalkItemPathClick(RoutePath path) {
        bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
        walkClick(path);
    }

    /**
     * 改变底部弹窗高度
     */
    private void changeBsHeight(ViewGroup viewGroup, int newHeight) {
        CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) viewGroup.getLayoutParams();
        layoutParams.height = newHeight;
        viewGroup.setLayoutParams(layoutParams);
    }

    @Override
    public void onBackClick() {
        backClick();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            backClick();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 获取上次选中的通勤模式
     *
     * @return
     */
    private int getLastSelectMode() {
        int mode = SpManager.get().user().getInt(KEY_LAST_SELECT_TRAFFIC_TYPE, 0);
        if (mode == 0) {
            return GeekRouteBottomView.NAV_MODE.MODE_BUS;
        }
        return mode;
    }

    public void backClick() {
        if (mGeekRouteBottomAllFragment != null) {
            SpManager.get().user().edit().putInt(KEY_LAST_SELECT_TRAFFIC_TYPE, mGeekRouteBottomAllFragment.getSelectType()).apply();
        }

        if (hasAddress()) {
            GeekRouteUtil.clearUseRouteCount();
            AppUtil.finishActivity(GeekRouteMapActivity.this);
            return;
        }
        if (mTemporaryPoi != null) {
            String cityName = mTemporaryPoi.getCityName();
            if (cityName != null && cityName.lastIndexOf("市") > 0) {
                cityName = cityName.replace("市", "");
            }
            String cityCode = LBase.getCityCode(cityName);
            JobIntentBean jobIntentBean = GeekExpectManager.getCurrSelectExpect();
            if (jobIntentBean != null && LText.equal(jobIntentBean.locationIndex, cityCode)) {
                AnalyticsFactory.create().action("start-location-setting-asking")
                        .debug().build();
                GeekRouteBackDialog dialog = new GeekRouteBackDialog(GeekRouteMapActivity.this);
                dialog.setTitle("保存以下地址为家庭住址？")
                        .setContent(mTemporaryPoi.getTitle())
                        .setAllHighLight(true)
                        .setPositiveButton("确认保存", v -> {
                            AnalyticsFactory.create().action("start-location-setting-asking-click")
                                    .param("p", 1)
                                    .debug().build();
                            // 设置地址
                            GeekRouteUtil.saveHomeAddressCompat(GeekRouteMapActivity.this, mTemporaryPoi, jobIntentBean);
                        })
                        .setNegativeButton("不保存", new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                AnalyticsFactory.create().action("start-location-setting-asking-click")
                                        .param("p", 0)
                                        .debug().build();
                                AppUtil.finishActivity(GeekRouteMapActivity.this);
                            }
                        })
                        .show();
                GeekRouteUtil.clearUseRouteCount();
            } else {
                do1101GrayDialog();
            }
        } else {
            do1101GrayDialog();
        }
    }

    /**
     * 用户在使用看通勤模式“一定时机”后，点击返回入口出拦截弹窗
     * 一、进入地图通勤页次数+1，若+1后次数达到了策略触发的时机；
     * <p>
     * 1、但在返回触发时，发现有了家庭住址，则不触发，客户端次数清零；
     * <p>
     * 2、返回时触发了1018版本修改的触发机制，则这个策略不触发，客户端出次数数据清零；
     */
    private void do1101GrayDialog() {
        if (GeekRouteUtil.map1101Gray()) {
            if (mUseCurrent && locationBean != null) { // 没有家庭住址  使用的是当前定位， 且 同城的话 弹窗
                JobIntentBean jobIntentBean = GeekExpectManager.getCurrSelectExpect();
                if (jobIntentBean != null && LText.equal(jobIntentBean.locationIndex, locationBean.localCityCode) && GeekRouteUtil.shouldShow()) {
                    AnalyticsFactory.create().action("save-home-location-expose")
                            .debug().build();
                    GeekRouteBackDialog dialog = new GeekRouteBackDialog(GeekRouteMapActivity.this);
                    dialog.setTitle("保存以下地址为家庭住址？")
                            .setContent(locationBean.poiTitle)
                            .setAllHighLight(true)
                            .setPositiveButton("确认保存", v -> {
                                PoiItem poiItem = new PoiItem();
                                poiItem.setLatLonPoint(new LatLonPoint(locationBean.latitude, locationBean.longitude));
                                poiItem.setTitle(locationBean.poiTitle);
                                poiItem.setProvinceName(locationBean.province);
                                poiItem.setCityName(locationBean.city);
                                poiItem.setAdName(locationBean.district);
                                // 设置地址
                                GeekRouteUtil.saveHomeAddressCompat(GeekRouteMapActivity.this, poiItem, jobIntentBean);
                                AnalyticsFactory.create().action("save-home-location-click")
                                        .param("p", "1")
                                        .debug().build();
                            })
                            .setNegativeButton("不保存", new OnClickNoFastListener() {
                                @Override
                                public void onNoFastClick(View v) {
                                    GeekRouteUtil.setRejectTime();
                                    AnalyticsFactory.create().action("save-home-location-click")
                                            .param("p", "0")
                                            .debug().build();
                                    AppUtil.finishActivity(GeekRouteMapActivity.this);
                                }
                            })
                            .show();
                } else {
                    AppUtil.finishActivity(GeekRouteMapActivity.this);
                }

            } else {
                AppUtil.finishActivity(GeekRouteMapActivity.this);
            }
        } else {
            AppUtil.finishActivity(GeekRouteMapActivity.this);
        }
    }

    @Override
    public void onNavClick() {
        if (ClickProtectedUtil.blockClickEvent()) {
            return;
        }
        jumpToMapApp2();
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_GUIDE_CLICK)
                .param("p", String.valueOf(getParam().analyticParams.jobId))
                .param("p2", 1).build();
    }

    @Override
    public void onRouteTabClick() {
        GeekRouteUtil.addUseRouteCount();
        if (mFirstSwitchTrafficTab) { //第一次 进入 定位到了交通tab，首次再切入 通勤tab
            mFirstSwitchTrafficTab = false;
            requestPermission(false);
        }

        //修复回切「看通勤」tab，地图数据不刷新问题「https://jira.kanzhun-inc.com/browse/BSA-27241」，每次走权限获取，可避免手动关权限问题
        searchTrafficWay(getLastSelectMode());
        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }

        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.switchToRoute();
        }


        AnalyticsFactory.create().action("mapaddr-guide-card-click")
                .param("p", mTopShowAnalytic)
                .param("p2", 1)
                .build();
        AnalyticsFactory.create().action("mapaddr-guide-card-show")
                .param("p", mTopShowAnalytic)
                .param("p2", 1)
                .build();
    }

    @Override
    public void onTrafficTabClick() {
        mGeekRouteTopView.showBack();
        AppUtil.hideSoftInput(GeekRouteMapActivity.this);
        if (mGeekRouteBottomAllFragment != null) {
            mGeekRouteBottomAllFragment.switchToTraffic();
        }
        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().removeDelayZoomRunnable();
        }

        AnalyticsFactory.create().action("mapaddr-guide-card-click")
                .param("p", mTopShowAnalytic)
                .param("p2", 2)
                .build();
        AnalyticsFactory.create().action("mapaddr-guide-card-show")
                .param("p", mTopShowAnalytic)
                .param("p2", 2)
                .build();
    }

    public boolean isTrafficTab() {
        return null != mGeekRouteTopView && mGeekRouteTopView.isTrafficTab();
    }

    private void initRefreshBtn(int margin) {
        CoordinatorLayout.LayoutParams locationParams = (CoordinatorLayout.LayoutParams) mLocationFl.getLayoutParams();
        locationParams.bottomMargin = margin - DisplayHelper.dp2px(this, 18);
        mLocationFl.setLayoutParams(locationParams);
        mLocationFl.setVisibility(View.VISIBLE);
    }


    @Override
    public void onResumeTabData(com.hpbr.bosszhipin.map.search.poi.PoiResult result, int type) { // 交通tab 下
        drawOverlay(result, type);
        drawEndPoint(15);
    }

    private void drawOverlay(com.hpbr.bosszhipin.map.search.poi.PoiResult result, int type) {
        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        if (result == null || LList.isEmpty(result.getPoiItems())) {
            return;
        }
        List<GeekRouteBubbleBean> list = new ArrayList<>();
        for (int i = 0; i < LList.getCount(result.getPoiItems()); i++) {
            GeekRouteBubbleBean bean = new GeekRouteBubbleBean();
            com.hpbr.bosszhipin.map.search.poi.PoiItem item = result.getPoiItems().get(i);
            bean.poiTitle = item.getTitle();
            bean.type = type;
            bean.latitude = item.getLatLonPoint().getLatitude();
            bean.longitude = item.getLatLonPoint().getLongitude();
            list.add(bean);
        }

        mClusterOverlay.setItems(list);
    }

    private boolean hasAddress() {
        return getParam().homeAddress.hasAddress() && getParam().homeAddress.latitude != 0;
    }

    private void onLocationLogicDeal(LocationService.LocationBean locationBean) {
        if (locationBean != null) {
            mCurrentLocation = new LatLonPoint(locationBean.latitude, locationBean.longitude);
            mMapView.getMap().moveCamera(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), -1);
            // 把当前地址标注在地图上
            if (locationBean != null) {
                int mode = getLastSelectMode();
                // 点击对应的通勤模式，如果没有路线，ontabClick 回调回来，再去请求
                mGeekRouteBottomAllFragment.modeClick(mode);
            }
            TLog.info(TAG, "currentLocation %s", locationBean.toString());
        }

        dismissProgressDialog();
    }

    private void showErrorView() {
        if (mGeekRouteBottomAllFragment == null) return;
        if (mMapView != null && null != mMapView.getMap()) {
            mMapView.getMap().clear();
        }
        mGeekRouteBottomAllFragment.hideLoading();
        if (mGeekRouteBottomAllFragment.getBottomButtonView() != null) {
            mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.GONE);
        }
        mGeekRouteBottomAllFragment.setFailedLlVisible(View.VISIBLE);
        mGeekRouteBottomAllFragment.setFailedTvVisible(View.VISIBLE);


        if (NetworkUtils.hasNetwork(this)) { // 没有路线 可能是没网
            if (!hasAddress() && (!hasLocationPermission() || !PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE))) {
                // 如果没有地址 且没有权限
                if (mGeekRouteBottomAllFragment.getBottomButtonView() != null) {
                    mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.VISIBLE);
                    mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn("获取起点", new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            mGeekRouteBottomAllFragment.enterInputState();

                        }
                    });
                }

                mGeekRouteBottomAllFragment.setFailedText("暂无起点信息");


                return;
            }
            if ((!hasLocationPermission() || !PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE)) && mUseCurrent) {
                mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.VISIBLE);
                mNeedRefresh = true;
                if (hasAddress()) {
                    mGeekRouteBottomAllFragment.setFailedText("未开启位置授权\n可添加权限查看交通方案");
                    mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn("添加权限", new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            requestPermission(false);
                        }
                    });
                } else {
                    if (getParam().analyticParams.source == GeekRouteHelper.SOURCE_FROM_COMPANY && getParam().cityCode == 0) { // 非公司过来  提示添加地址
                        mGeekRouteBottomAllFragment.setFailedText("未开启位置授权\n可添加权限查看交通方案");
                        mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn("添加权限", new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                requestPermission(false);
                            }
                        });
                    } else {
                        mGeekRouteBottomAllFragment.setFailedText("未开启位置授权\n可添加住址查看交通方案");
                        mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn(R.string.geek_string_route_add_address, new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                onHomeAddressClick(false);
                            }
                        });
                    }
                }
                drawEndPoint();
            } else if (!AMapUtil.isLocServiceEnable(this)) {
                mNeedRefresh = true;
                drawEndPoint();
                mGeekRouteBottomAllFragment.setFailedText("未开启 GPS 定位\n请在设置中开启");
                mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.VISIBLE);
                mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn("去设置", new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {// 打开位置 设置
                        Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                        startActivity(intent);
                    }
                });
            } else {
                //有网  并且位置开启
                mGeekRouteBottomAllFragment.setFailedText("未找到适合的路线");
                showRouteStartAndEndMarke();
            }
        } else {
            mNeedRefresh = true;
            drawEndPoint();
            mGeekRouteBottomAllFragment.setFailedText("当前网络不佳，请再次重试");
            mGeekRouteBottomAllFragment.getBottomButtonView().setVisibility(View.VISIBLE);
            mGeekRouteBottomAllFragment.getBottomButtonView().setRightBtn("重新尝试", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    if (NetworkUtils.hasNetwork(GeekRouteMapActivity.this)) {
                        if (mUseCurrent) {
                            requestPermission(false);
                        } else {
                            useHomeAddressRefreshUi(true);
                        }
                    }
                }
            });
        }
    }

    private void drawEndPoint() {
        drawEndPoint(15);
    }

    private void drawEndPoint(float level) {
        if (mJobLatLonPoint != null) {
            mMapView.getMap().moveCamera(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), level);
            mMapView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    TLog.info(GeekRouteHelper.TAG,  "drawEndPoint");
                    drawMarkers(mJobLatLonPoint.getLatitude(), mJobLatLonPoint.getLongitude(), true);
                }
            },100);
        }
    }

    /**
     * 检测是否有定位权限
     */
    private boolean hasLocationPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mMapView != null) {
            mMapView.onResume();
        }
        if (mGeekRouteBottomAllFragment != null && mUseCurrent && hasLocationPermission() &&
                PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE) && mNeedRefresh) {
            //去设置中开启了权限，这时候需要刷新
            requestPermission(false);
            mUseCurrent = true;
            mNeedRefresh = false;
        }

    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mMapView != null) {
            mMapView.onPause();
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (mMapView != null) {
            mMapView.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (routePlanSearch != null) {
            routePlanSearch.destroy();
        }
        if (mMapView != null) {
            mMapView.onDestroy();
        }
    }

    /**
     * 第一次进入 没有住址，如果有权限 则定位 判断是否同城，不同城切换交通tab
     * 如果没权限 则直接切换到交通tab
     */
    private void dealLogicWhenFirstIn() {
        if (hasLocationPermission() &&
                PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE)) {
            mFirstUseLocation = true;
            requestPermission(false);
        } else {
            mFirstSwitchTrafficTab = true;
            mGeekRouteTopView.selectTraffic();
        }
    }

    private void getGeekHomeAddress() {
        showProgressDialog();
        GetGeekAddressRequest request = new GetGeekAddressRequest(new ApiRequestCallback<GetGeekAddressResponse>() {
            @Override
            public void onSuccess(ApiData<GetGeekAddressResponse> data) {
                dismissProgressDialog();
                if (data != null && data.resp != null && data.resp.addressInfo != null && data.resp.addressInfo.latitude != 0) {
                    if (!TextUtils.isEmpty(data.resp.addressInfo.address)) {
                        getParam().homeAddress.poiTitle = data.resp.addressInfo.poiTitle;
                        mStartMarkAddress = data.resp.addressInfo.poiTitle;
                        mGeekRouteBottomAllFragment.setStartText(mStartMarkAddress);
                        mViewModel.startPositionTagLiveData.postValue(LText.getString(R.string.string_my_home_position));
                        mGeekRouteBottomAllFragment.setHomeAddress(mStartMarkAddress);
                    }
                    getParam().homeAddress.latitude = data.resp.addressInfo.latitude;
                    getParam().homeAddress.longitude = data.resp.addressInfo.longitude;
                    getParam().homeAddress.status = 1;
                    getCityByLatLon(getParam().homeAddress.latitude, getParam().homeAddress.longitude);
                    switchAnalytic(2);
                    mHomeLatLonPoint = new LatLonPoint(data.resp.addressInfo.latitude, data.resp.addressInfo.longitude);
                    useHomeAddressRefreshUi(true);
                    GeekRouteUtil.addUseRouteCount();

                    TLog.info(TAG,"get geek home address resp %s",data.resp.addressInfo.toString());
                } else {
                    dealLogicWhenFirstIn();
                }

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                dismissProgressDialog();
                dealLogicWhenFirstIn();
            }
        });
        request.cityCode = getParam().cityCode;
        request.addressType = 0;
        request.execute();
    }

    private void drawMarkers(double latitude, double longitude, boolean isEnd) {
        mMapView.getMap().addMarker(new MarkerOptions().position(latitude, longitude).icon(isEnd ? R.mipmap.amap_end_point : R.mipmap.amap_start_point));
    }

    private void jumpToMapApp2() {
        /**
         * t->t = 0（驾车）= 1（公交）= 2（步行）= 3（骑行）= 4（火车）= 5（长途客车）
         */
        String type = "0";
        if (mGeekRouteBottomAllFragment != null) {
            switch (mGeekRouteBottomAllFragment.getSelectType()) {
                case GeekRouteBottomView.NAV_MODE.MODE_BUS://本地公交
                    type = "1";
                    break;
                case GeekRouteBottomView.NAV_MODE.MODE_RIDE://本地骑行
                    type = "3";
                    break;
                case GeekRouteBottomView.NAV_MODE.MODE_DRIVE://本地驾车
                    type = "0";
                    break;
                case GeekRouteBottomView.NAV_MODE.MODE_WALK://本地步行
                    type = "2";
                    break;
            }
        }

        //出发点的位置信息
        AMapUtil.MapNavationLocation startLocation;
        if (mUseCurrent) { //当前位置
            String startAddress = mStartMarkAddress;
            if (LText.empty(startAddress)) {
                startAddress = "我的地址";
            }
            startLocation = new AMapUtil.MapNavationLocation();
            startLocation.setSname(startAddress);
            startLocation.setSlat(locationBean == null ? 0 : locationBean.latitude);
            startLocation.setSlon(locationBean == null ? 0 : locationBean.longitude);
        } else {//我的住址
            String startAddress = mStartMarkAddress;
            if (LText.empty(startAddress)) {
                startAddress = "我的地址";
            }
            startLocation = new AMapUtil.MapNavationLocation();

            if (mTemporaryPoi != null) {
                startLocation.setSname(mTemporaryPoi.getTitle());
                startLocation.setSlat(mTemporaryPoi.getLatLonPoint().getLatitude());
                startLocation.setSlon(mTemporaryPoi.getLatLonPoint().getLongitude());
            } else {
                startLocation.setSname(startAddress);
                startLocation.setSlat(mHomeLatLonPoint == null ? 0 : mHomeLatLonPoint.getLatitude());
                startLocation.setSlon(mHomeLatLonPoint == null ? 0 : mHomeLatLonPoint.getLongitude());
            }

        }


        /**
         * 目标位置的信息
         */
        AMapUtil.MapNavationLocation targetLocation = new AMapUtil.MapNavationLocation();
        targetLocation.setSlat(mJobLatLonPoint.getLatitude());
        targetLocation.setSlon(mJobLatLonPoint.getLongitude());
        targetLocation.setSname(getParam().targetAddress);
        targetLocation.setGoiId(getParam().analyticParams.geoId);//POITitle

        List<AppInfoUtils.AppInfoEntity> appList = AMapUtil.resolveMapApps(this,
                startLocation,
                targetLocation,
                type);

        TLog.info(TAG, "startLocation:" + startLocation.toString() + "; targetLocation : " + targetLocation.toString());

        int count = LList.getCount(appList);
        //没有安装地图
        if (count == 0) {
            ToastUtils.showText(this, getString(R.string.string_no_map_app_installed));
            return;
        }

        //显示地图对话框让用户选择
        GeekRouteMapSelectDialog dialog = new GeekRouteMapSelectDialog(this, appList, getParam().targetAddress);
        dialog.show();


    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) { // 进了地址页  不选地址
            if ((!hasLocationPermission() || !PermissionHelper.hasPermission(LocationPermissionHelper.GEEK_NAV_ROUTE))
                    && (mHomeLatLonPoint == null || mHomeLatLonPoint.getLatitude() == 0)) {
                // 没有权限 并且 住址也没有
                showErrorView();
            }
            return;
        }
        if (requestCode == GeekPageRouter.REQ_HOME_ADDRESS_SETUP) {
            //修改我的地址
            double latitude = data.getDoubleExtra("latitude", 0);
            double longitude = data.getDoubleExtra("longitude", 0);
            PoiItem poiItem = (PoiItem)data.getSerializableExtra("poiItem");
            if (latitude == 0 && longitude == 0) {
                return;
            }
            if (poiItem != null) {
                getParam().homeAddress.poiTitle = poiItem.getTitle();
                getParam().homeAddress.latitude = latitude;
                getParam().homeAddress.longitude = longitude;
                getParam().homeAddress.status = 1;
                mStartMarkAddress = poiItem.getTitle();
                if (mGeekRouteBottomAllFragment != null) {
                    mGeekRouteBottomAllFragment.setStartText(mStartMarkAddress);
                    mViewModel.startPositionTagLiveData.postValue(LText.getString(R.string.string_my_home_position));
                    mGeekRouteBottomAllFragment.setHomeAddress(poiItem.getTitle());
                    mGeekRouteBottomAllFragment.setCity(poiItem.getCityName());
                }
            }
            mTemporaryPoi = null;
            if (mGeekRouteBottomAllFragment != null) {
                mGeekRouteBottomAllFragment.recoverUI();
                mGeekRouteBottomAllFragment.resetRoute();
            }

            bottomSheetBehavior.setState(ViewPagerBottomSheetBehavior.STATE_COLLAPSED);
            switchAnalytic(2);
            ToastUtils.showText("起点变更成功");
            mHomeLatLonPoint = new LatLonPoint(latitude, longitude);
            useHomeAddressRefreshUi(true);

            TLog.info(TAG, "home address setup latitude %s , longitude %s", latitude, longitude);
        }
    }

    private void useHomeAddressRefreshUi(boolean isHomeAddress) {
        if (mGeekRouteBottomAllFragment != null) {
            mUseCurrent = false;
            int mode = getLastSelectMode();
            // 点击对应的通勤模式，如果没有路线，ontabClick 回调回来，再去请求
            mGeekRouteBottomAllFragment.modeClick(mode);

        }
    }

    private LatLonPoint getStartLocationPoint() {
        if (mTemporaryPoi != null) {
            LatLonPoint latLonPoint = mTemporaryPoi.getLatLonPoint();
            if (latLonPoint != null) {
                return new LatLonPoint(latLonPoint.getLatitude(), latLonPoint.getLongitude());
            } else {
                TLog.error(TAG,"getStartLocationPoint LatLonPoint is null");
                return null;
            }
        }
        if (mUseCurrent) {
            return mCurrentLocation;
        } else {
            return mHomeLatLonPoint != null && mHomeLatLonPoint.getLatitude() > 0 ? mHomeLatLonPoint : mCurrentLocation;
        }
    }

    private String getRouteCity() {
        if (mUseCurrent) {
            String locationCityCode = LocationService.getLocationCityCode();
            return TextUtils.isEmpty(locationCityCode) ? "010" : locationCityCode;
        } else {
            return TextUtils.isEmpty(getParam().city) ? "010" : getParam().city;
        }
    }

    private void report(int code) {

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_MAP_TRANSPORTATION)
                .param("p", getParam().analyticParams.guideSource == GeekRouteHelper.SOURCE_GUIDE_COMPANY ? getParam().analyticParams.brandId : getParam().analyticParams.jobId)
                .param("p2", code)
                .param("p3", mUseCurrent ? 1 : 2)//计算来源：1 当前定位 2 我的住址（816）
                .param("p4", getParam().analyticParams.lid)
                .param("p5", GsonUtils.toJson(mTimeGuideReport))
                .param("p6", getParam().analyticParams.guideSource)
                .debug()
                .build();
    }

    /**
     * @param translateWay 交通方式：1 公交 2 骑行 3 驾车 4步行
     */

    private int mLastTranslateWay;

    private void translateWayAnalytic(int translateWay) {
        if (mLastTranslateWay != translateWay && mLastTranslateWay != 0) {
            AnalyticsFactory.create().action("action-tools-transportation-route")
                    .param("p", translateWay)
                    .param("p2", 1)
                    .debug()
                    .build();
        }
        mLastTranslateWay = translateWay;
    }

    /**
     * 根据家庭住址经纬度  设置 搜索的当前城市名
     *
     * @param latitude
     * @param longitude
     */
    private void getCityByLatLon(double latitude, double longitude) {
        GeocodeSearchCompat geocodeSearchCompat = GeocodeSearchCompat.newInstance(this);
        geocodeSearchCompat.getFromLocationAsyn(new RegeocodeQuery(new LatLonPoint(latitude, longitude)), new OnGeocodeSearchListener() {
            @Override
            public void onRegeocodeSearched(RegeocodeResult regeocodeResult) {
                if (regeocodeResult != null) {
                    RegeocodeAddress regeocodeAddress = regeocodeResult.getRegeocodeAddress();
                    if (null != regeocodeAddress && regeocodeAddress.getProvince() != null) {
                        if (mGeekRouteBottomAllFragment != null) {
                            mGeekRouteBottomAllFragment.setCity(regeocodeAddress.getCity());
                        }
                    }
                    TLog.info(TAG, "get city location latitude %s , longitude %s ,regeocodeAddress %s", latitude, longitude, null != regeocodeAddress ? regeocodeAddress.toString() : "");
                }
                geocodeSearchCompat.destroy();
            }

            @Override
            public void onGeocodeSearched(GeocodeResult var1) {
            }
        });
    }

}
