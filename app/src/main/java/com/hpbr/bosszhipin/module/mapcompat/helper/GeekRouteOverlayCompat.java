package com.hpbr.bosszhipin.module.mapcompat.helper;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.LruCache;
import android.view.View;
import android.widget.TextView;

import com.amap.api.maps.model.BitmapDescriptor;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.map.Marker;
import com.hpbr.bosszhipin.map.MarkerOptions;
import com.hpbr.bosszhipin.module.map.bean.GeekRouteBubbleBean;
import com.hpbr.bosszhipin.module.map.fragment.GeekRouteTrafficFragment;
import com.hpbr.utils.platform.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 整体设计采用了两个线程,一个线程用于计算组织聚合数据,一个线程负责处理Marker相关操作
 * copy from com.hpbr.bosszhipin.module.map.activity.GeekRouteOverlay
 */
public class GeekRouteOverlayCompat {
    private MapViewCompat mAMap;
    private Context mContext;
    private List<GeekRouteBubbleBean> mClusterItems;  //所有数据
    private List<GeekRouteBubbleBean> mClusters;//// 可视的 渲染的
    private LruCache<Integer, BitmapDescriptor> mLruCache;
    private HandlerThread mSignClusterThread = new HandlerThread("calculateCluster");
    private MarkerHandler mMarkerhandler; //更新marker
    private SignClusterHandler mSignClusterHandler;  //计算marker
    private boolean mIsCanceled = false;
    private Map<Integer, Drawable> mBackDrawAbles = new HashMap<Integer, Drawable>();
    private List<Marker> Markers = new ArrayList<>();

    public GeekRouteOverlayCompat(MapViewCompat amap, List<GeekRouteBubbleBean> clusterItems,
                                  Context context) {
        //默认最多会缓存80张图片作为聚合显示元素图片,根据自己显示需求和app使用内存情况,可以修改数量
        mLruCache = new LruCache<Integer, BitmapDescriptor>(80) {
            protected void entryRemoved(boolean evicted, Integer key, BitmapDescriptor oldValue,
                                        BitmapDescriptor newValue) {
                oldValue.getBitmap().recycle();
            }
        };

        if (clusterItems != null) {
            mClusterItems = clusterItems;
        } else {
            mClusterItems = new ArrayList<GeekRouteBubbleBean>();
        }
        mContext = context;
        mClusters = new ArrayList<GeekRouteBubbleBean>();
        this.mAMap = amap;
        initThreadHandler();//初始化线程
    }

    public void setItems(List<GeekRouteBubbleBean> items) {
        mClusterItems = items;
        assignClusters();
    }

    /**
     * 销毁资源
     */
    public void onDestroy() {
        mIsCanceled = true;
        mSignClusterHandler.removeCallbacksAndMessages(null);
        mMarkerhandler.removeCallbacksAndMessages(null);
        mSignClusterThread.quit();
        mLruCache.evictAll();
    }

    //初始化Handler
    private void initThreadHandler() {
        mSignClusterThread.start();
        mSignClusterHandler = new SignClusterHandler(mSignClusterThread.getLooper());
    }

    /**
     * 将聚合元素添加至地图上
     */
    private void addClusterToMap(List<GeekRouteBubbleBean> clusters) {
        //然后再把所有的聚合元素重新添加
        for (int i = 0; i < clusters.size(); i++) {
            addSingleClusterToMap(clusters.get(i));
        }

    }
    /**
     * 将单个聚合元素添加至地图显示
     *
     * @param cluster
     */
    private void addSingleClusterToMap(GeekRouteBubbleBean cluster) {
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.position(cluster.latitude, cluster.longitude);
        markerOptions.view(getBitmapDes(cluster));
        markerOptions.anchor(0.5f,0.5f);
        Marker Marker = mAMap.getMap().addMarker(markerOptions);
        Markers.add(Marker);
    }


    /**
     * 这个是处理多个坐标点。将附近相邻的点 聚合成一个，需求用不到 就注释吧  一个点  用一个
     */
    private void calculateClusters() {
        mIsCanceled = false;
        mClusters.clear();
        //判断现在地图上的区域是不是应该包含这个点，如果包含，就把点加到聚合数据里边，然后去通知mMarkerhandler更新一下。
        for (int i = 0; i < mClusterItems.size(); i++) {
            if (mIsCanceled) {
                return;
            }
            GeekRouteBubbleBean bean = mClusterItems.get(i);
            GeekRouteBubbleBean temp = new GeekRouteBubbleBean();
            temp.latitude = bean.latitude;
            temp.longitude = bean.longitude;
            temp.poiTitle = bean.poiTitle;
            temp.type = bean.type;
            mClusters.add(temp);
        }
        //复制一份数据，规避同步
        //创建一个新的聚合点数组
        //把现有的聚合点加进去
        List<GeekRouteBubbleBean> clusters = new ArrayList<GeekRouteBubbleBean>();
        clusters.addAll(mClusters);
        //给后给控制marher的headler发送消息。把所有的聚合点信息发过去
        Message message = Message.obtain();
        message.what = 0;
        message.obj = clusters;
        if (mIsCanceled) {
            return;
        }
        Utils.runOnUiThread(() -> addClusterToMap(clusters));
    }

    /**
     * 对点进行聚合
     */
    private void assignClusters() {
        mIsCanceled = true;
        mSignClusterHandler.removeMessages(0);//先把队列里边的消息移除
        mSignClusterHandler.sendEmptyMessage(0);//然后再发消息
    }

    /**
     * 获取每个聚合点的绘制样式
     */
    private View getBitmapDes(GeekRouteBubbleBean cluster) {
        TextView textView = new TextView(mContext);
        Drawable drawable = getDrawAble(cluster);
        textView.setBackground(drawable);
        return textView;
    }

    public Drawable getDrawAble(GeekRouteBubbleBean cluster) {
        Drawable bitmapDrawable;

        if (cluster.type == GeekRouteTrafficFragment.TYPE_BUS_FRAGMENT) {
            bitmapDrawable = mBackDrawAbles.get(4);
            if (bitmapDrawable == null) {
                bitmapDrawable = mContext.getResources().getDrawable(
                        R.mipmap.ic_geek_route_bubble_bus);

                mBackDrawAbles.put(4, bitmapDrawable);
            }
        } else if (cluster.type == GeekRouteTrafficFragment.TYPE_SUBWAY_FRAGMENT) {
            bitmapDrawable = mBackDrawAbles.get(5);
            if (bitmapDrawable == null) {
                bitmapDrawable = mContext.getResources().getDrawable(
                        R.mipmap.ic_geek_route_bubble_subway);

                mBackDrawAbles.put(5, bitmapDrawable);
            }
        } else {
            bitmapDrawable = mBackDrawAbles.get(6);
            if (bitmapDrawable == null) {
                bitmapDrawable = mContext.getResources().getDrawable(
                        R.mipmap.ic_geek_route_bubble_park);

                mBackDrawAbles.put(6, bitmapDrawable);
            }
        }
        return bitmapDrawable;
    }
    //-----------------------辅助内部类用---------------------------------------------

    /**
     * 处理market添加，更新等操作
     */
    class MarkerHandler extends Handler {

        MarkerHandler(Looper looper) {
            super(looper);
        }

        public void handleMessage(Message message) {
            switch (message.what) {
                case 0://接收到在当前区域内应该显示的所有的聚合点，把聚合点加到地图上
                    List<GeekRouteBubbleBean> clusters = (List<GeekRouteBubbleBean>) message.obj;
                    addClusterToMap(clusters);
                    break;
            }
        }
    }

    /**
     * 处理聚合点算法线程
     */
    class SignClusterHandler extends Handler {

        SignClusterHandler(Looper looper) {
            super(looper);
        }

        public void handleMessage(Message message) {
            switch (message.what) {
                case 0:
                    calculateClusters();
                    break;
            }
        }
    }

    public String dealStringLength(String str, int length) {
        if (TextUtils.isEmpty(str) || str.length() <= length) return str;

        return str.substring(0, length) + "...";
    }
}