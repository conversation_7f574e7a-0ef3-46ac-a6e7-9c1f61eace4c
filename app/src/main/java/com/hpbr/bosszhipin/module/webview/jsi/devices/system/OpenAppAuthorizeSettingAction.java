package com.hpbr.bosszhipin.module.webview.jsi.devices.system;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.view.View;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.TwlAppProvider;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.webview.WebViewCommon;
import com.hpbr.bosszhipin.module.webview.WebViewPermissionReport;
import com.hpbr.bosszhipin.module.webview.bean.OpenAppAuthorizeSettingMessage;
import com.hpbr.bosszhipin.module.webview.bean.WebBodyValue;
import com.hpbr.bosszhipin.module.webview.jsi.BZWebAction;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.ActivityUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * created by tongxiaoyun.
 * date: 2025/5/8
 * time: 下午8:26
 * description:
 */
public class OpenAppAuthorizeSettingAction extends BZWebAction<OpenAppAuthorizeSettingMessage> {
    private static final String ALBUM_AUTHORIZED = "albumAuthorized";
    private static final String CAMERA_AUTHORIZED = "cameraAuthorized";
    private static final String LOCATION_AUTHORIZED = "locationAuthorized";
    private static final String MICROPHONE_AUTHORIZED = "microphoneAuthorized";
    private static final String AUTHORIZED = "authorized";
    private static final String NOT_AUTHORIZED = "not authorized";
    private static final String RESULT = "result";


    public OpenAppAuthorizeSettingAction(@NonNull WebViewCommon webViewCommon) {
        super(webViewCommon);
    }

    @Override
    public void handle(@NonNull OpenAppAuthorizeSettingMessage message) {
        requestPermission(message);
    }


    private void requestPermission(@NonNull OpenAppAuthorizeSettingMessage message) {
        if (LList.isEmpty(message.permType)) return;
        List<String> permissionArr = new ArrayList<>();
        List<String> permType = message.permType;
        for (String authorized : permType) {
            if (LText.equal(ALBUM_AUTHORIZED, authorized)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    permissionArr.add(Manifest.permission.READ_MEDIA_IMAGES);
                    permissionArr.add(Manifest.permission.READ_MEDIA_VIDEO);
                } else {
                    permissionArr.add(Manifest.permission.READ_EXTERNAL_STORAGE);
                    permissionArr.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                }
            } else if (LText.equal(CAMERA_AUTHORIZED, authorized)) {
                permissionArr.add(Manifest.permission.CAMERA);
            } else if (LText.equal(MICROPHONE_AUTHORIZED, authorized)) {
                permissionArr.add(Manifest.permission.RECORD_AUDIO);
            } else if (LText.equal(LOCATION_AUTHORIZED, authorized)) {
                permissionArr.add(Manifest.permission.ACCESS_COARSE_LOCATION);
                permissionArr.add(Manifest.permission.ACCESS_FINE_LOCATION);
            }
        }
        if (LList.isEmpty(permissionArr)) return;
        String[] permissions = permissionArr.toArray(new String[0]);
        if (message.needCompliancePopup) {
            requestCompliancePopupPermission(message, permissions);
        } else {
            requestDirectPermission(message, permissions);
        }

    }

    /**
     * 请求带合规弹窗的权限
     */
    private void requestCompliancePopupPermission(@NonNull OpenAppAuthorizeSettingMessage message, String[] permissions) {
        String source = message.source;
        if (LText.isEmptyOrNull(message.title) || LText.isEmptyOrNull(message.refuseTitle) || LText.isEmptyOrNull(message.desc)) {
            //已和FE那边沟通成功 needCompliancePopup = true 有合规弹窗的必须需要title、refuseTitle、desc
            return;
        }
        App.get().getMainHandler().post(() -> PermissionHelper.getH5PermissionHelper(getActivity(), permissions, message.title, message.desc, message.refuseTitle, message.scene).setPermissionCallback(new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                if (message.permType.contains(LOCATION_AUTHORIZED)) {
                    Map<String, Boolean> permissionResult = permission.permissionResult;
                    if (!permissionResult.isEmpty() && permissionResult.get(Manifest.permission.ACCESS_COARSE_LOCATION) != null && permissionResult.get(Manifest.permission.ACCESS_FINE_LOCATION) != null) {
                        if (yes) {
                            LocationPermissionHelper.isLocPer = 1;
                        } else {
                            if (LocationPermissionHelper.isLocPer == -1) {
                                TwlAppProvider.refreshLocPer();
                            }
                        }
                    }
                }
                if (yes) {
                    JSONObject bodyValue = WebBodyValue.obj().appendBodyValue(RESULT, AUTHORIZED).getBodyValue();
                    onSuccessLoadJavascript(message.callbackName, bodyValue);
                } else {
                    JSONObject bodyValue = WebBodyValue.obj().appendBodyValue(RESULT, NOT_AUTHORIZED).getBodyValue();
                    onSuccessLoadJavascript(message.callbackName, bodyValue);
                }
                if (LText.notEmpty(source) && !LText.equal(source, "0")) {
                    getWebViewCommon().reportPermissionResult(permission, source);
                }
            }

            @Override
            public void onStartRequest(@NonNull PermissionData permission) {
                if (LText.notEmpty(source) && !LText.equal(source, "0")) {
                    getWebViewCommon().reportPermissionRequest(permission, source, 1);
                }
            }
        }).setUiHandlerCallback(new PermissionHelper.UIHandlerCallback() {
            @Override
            public void onShowType(PermissionHelper.SceneBean sceneBean, int step) {
                if (step == PermissionHelper.DIALOG_TYPE_NO_REMINDERS && LText.notEmpty(source) && !LText.equal(source, "0")) {
                    getWebViewCommon().reportNoRemindersPermissionRequest();
                }
                if (message.permType.contains(LOCATION_AUTHORIZED)) {
                    String title = step == PermissionHelper.DIALOG_TYPE_NO_REMINDERS ? message.refuseTitle : message.title;
                    getWebViewCommon().onPermissionDialogShow(title);
                }
            }

            @Override
            public void onClick(PermissionHelper.SceneBean sceneBean, int step, int clickType) {
                if (message.permType.contains(LOCATION_AUTHORIZED)) {
                    boolean noReminders = step == PermissionHelper.DIALOG_TYPE_NO_REMINDERS;
                    String title = noReminders ? message.refuseTitle : message.title;
                    String posBtnText = noReminders ? LText.getString(R.string.string_location_permission_dialog2_positive_action) : LText.getString(R.string.string_location_permission_dialog1_positive_action);
                    String negBtnText = noReminders ? LText.getString(R.string.string_location_permission_dialog2_negative_action) : LText.getString(R.string.string_location_permission_dialog1_negative_action);
                    getWebViewCommon().onPermissionDialogClick(title, clickType == 1 ? posBtnText : negBtnText);
                }
            }
        }).requestPermission());
    }


    private void requestDirectPermission(@NonNull OpenAppAuthorizeSettingMessage message, String[] permissions) {
        if (message.permType.contains(LOCATION_AUTHORIZED) && message.permType.size() == 1) {
            PermissionData permissionData = PermissionData.create(getActivity(), permissions);
            String source = message.source;
            if (LText.notEmpty(source) && !LText.equal(source, "0")) {
                getWebViewCommon().reportPermissionRequest(permissionData, source, 1);
            }
            long requestPermissionTime = System.currentTimeMillis();
            new PermissionManager(getActivity()).requestPermission(permissions, (hasPermission, shouldShowAllRequestPermissionRationale) -> {
                LocationPermissionHelper.isLocPer = hasPermission ? 1 : 0;
                if (hasPermission) {
                    if (LText.notEmpty(source) && !LText.equal(source, "0")) {
                        getWebViewCommon().reportPermissionResult(permissionData, source);
                    }
                    JSONObject bodyValue = WebBodyValue.obj().appendBodyValue(RESULT, AUTHORIZED).getBodyValue();
                    onSuccessLoadJavascript(message.callbackName, bodyValue);
                } else {
                    JSONObject bodyValue = WebBodyValue.obj().appendBodyValue(RESULT, NOT_AUTHORIZED).getBodyValue();
                    onSuccessLoadJavascript(message.callbackName, bodyValue);
                    if (!shouldShowAllRequestPermissionRationale) {
                        if (LText.notEmpty(source) && !LText.equal(source, "0") && System.currentTimeMillis() - requestPermissionTime >= WebViewPermissionReport.DELAY_TIME) {
                            getWebViewCommon().reportPermissionResult(permissionData, source);
                            getWebViewCommon().reportPermissionRequest(permissionData, source, 2);
                        } else {
                            getWebViewCommon().reportNoRemindersPermissionRequest();
                        }
                        showDisallowRational(message, permissionData);
                    } else {
                        if (LText.notEmpty(source) && !LText.equal(source, "0")) {
                            getWebViewCommon().reportPermissionResult(permissionData, source);
                        }
                    }
                }
            });
        }
    }


    private void showDisallowRational(@NonNull OpenAppAuthorizeSettingMessage message, PermissionData permissionData) {
        if (!ActivityUtils.isValid(getActivity())) return;
        String source = message.source;
        String defaultDesc = UserManager.isBossRole() ? Utils.getApp().getString(R.string.string_location_permission_dialog2_common_boss_content) : Utils.getApp().getString(R.string.string_location_permission_dialog2_common_geek_content);
        String title = LText.isEmptyOrNull(message.refuseTitle) ? LText.getString(R.string.string_location_permission_default_desc) : message.refuseTitle;
        String positiveBtnText = LText.getString(R.string.string_location_permission_dialog2_positive_action);
        String negativeBtnText = LText.getString(R.string.string_location_permission_dialog2_negative_action);
        DialogUtils dialogUtils = new DialogUtils.Builder(getActivity())
                .setDoubleButton()
                .setCancelable(false)
                .setTitle(title)
                .setDesc(LText.isEmptyOrNull(message.desc) ? defaultDesc : message.desc)
                .setNegativeAction(negativeBtnText, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        if (permissionData != null && LText.notEmpty(source) && !LText.equal(source, "0")) {
                            permissionData.dialogClickType = 1;
                            getWebViewCommon().reportPermissionResult(permissionData, source);
                        }
                        if (message.permType.contains(LOCATION_AUTHORIZED)) {
                            getWebViewCommon().onPermissionDialogClick(title, negativeBtnText);
                        }
                    }
                })
                .setPositiveAction(positiveBtnText, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.fromParts("package", Utils.getApp().getPackageName(), null));
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        AppUtil.startActivity(getActivity(), intent);
                        if (permissionData != null && LText.notEmpty(source) && !LText.equal(source, "0")) {
                            permissionData.dialogClickType = 2;
                            getWebViewCommon().reportPermissionResult(permissionData, source);
                        }
                        if (message.permType.contains(LOCATION_AUTHORIZED)) {
                            getWebViewCommon().onPermissionDialogClick(title, positiveBtnText);
                        }
                    }
                })
                .build();
        dialogUtils.show();
        if (message.permType.contains(LOCATION_AUTHORIZED)) {
            getWebViewCommon().onPermissionDialogShow(title);
        }
    }


    @Override
    public void release() {

    }
}
