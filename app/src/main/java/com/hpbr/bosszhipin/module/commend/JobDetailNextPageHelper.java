package com.hpbr.bosszhipin.module.commend;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.MainThread;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.dictionary.StaticTransferParams;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import java.util.List;

/**
 * 作者：ZhouYou
 * 日期：2017/3/2.
 */
public class JobDetailNextPageHelper {

    private static final String ERROR_REASON = "com.hpbr.bosszhipin.ERROR_REASON";

    private static final int ACTION_TYPE_REQUEST = 1;
    private static final int ACTION_TYPE_RESPONSE = 2;

    private Context context;

    public JobDetailNextPageHelper(Context context) {
        this.context = context;
    }

    public void sendRequest(int from, String tag) {
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(Constants.DATA_FROM, from);
        intent.putExtra(Constants.DATA_TAG, tag);
        intent.putExtra(Constants.DATA_INT, ACTION_TYPE_REQUEST);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }

    public JobDetailNextPageHelper registerRequestReceiver() {
        try {
            //修复偶现一例崩溃 apm.weizhipin.com/admin/application/android/crash-details?appKey=n9mObpaxi2d2AFbV&signature=93dceb455343fbfce540903a8f593b2e&id=3511403709&crashType=&appVersion=&userId=
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
            ReceiverUtils.registerSystem(context, intentFilter, receiverRequest);
        } catch (Throwable throwable) {
            TLog.info(ApmAnalyticsAction.ACTION_FIX_APM_BUG, "jd register receiver =%s", Log.getStackTraceString(throwable));
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_FIX_APM_BUG, "registerRequestReceiverJD").p2(Log.getStackTraceString(throwable)).report();
        }
        return this;
    }

    public JobDetailNextPageHelper unregisterRequestReceiver() {
        ReceiverUtils.unregisterSystem(context, receiverRequest);
        return this;
    }

    /**
     * Geek身份列表页中需要注册的广播
     * 用来接收从详情页发来的请求下一页的数据通知
     */
    private BroadcastReceiver receiverRequest = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST)) {
                int actionType = intent.getIntExtra(Constants.DATA_INT, 0);
                if (actionType != ACTION_TYPE_REQUEST) return;
                if (requestListener == null) return;
                int from = intent.getIntExtra(Constants.DATA_FROM, ParamBean.FROM_NONE);
                String tag = intent.getStringExtra(Constants.DATA_TAG);
                requestListener.request(from, tag);
            }
        }
    };

    /**
     * 发送成功获取到数据的广播
     *
     * @param paramList
     * @param securityId
     * @param from
     * @param hasMore
     */
    @MainThread
    public void sendResponse(List<ParamBean> paramList, String securityId, int from, boolean hasMore, boolean isRequestFromJobPage) {
        final String paramsToken = String.valueOf(System.currentTimeMillis());  // 生成Token
        StaticTransferParams.getInstance().setParams(paramsToken, paramList);   // 存储数据
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(Constants.DATA_INT, ACTION_TYPE_RESPONSE);
        intent.putExtra(Constants.DATA_ENTITY, paramsToken);    // 传递Token
        intent.putExtra(Constants.DATA_SECURITY_ID, securityId);
        intent.putExtra(Constants.DATA_FROM, from);
        intent.putExtra(Constants.DATA_BOOLEAN, hasMore);
        intent.putExtra(Constants.DATA_BOOLEAN9, isRequestFromJobPage);
        ReceiverUtils.sendBroadcastSystem(context, intent);
        TLog.info("JDHasMoreTag", "sendResponse before send broadcast hasMore %s", hasMore);
    }

    /**
     * 发送失败的广播
     *
     * @param error
     */
    public void sendResponse(String error) {
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(ERROR_REASON, error);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }

    public JobDetailNextPageHelper registerResponseReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        ReceiverUtils.registerSystem(context, intentFilter, receiverResponse);
        return this;
    }

    public JobDetailNextPageHelper unregisterResponseReceiver() {
        ReceiverUtils.unregisterSystem(context, receiverResponse);
        return this;
    }

    /**
     * Geek身份查看的职位详情页中需要注册的广播
     * 用来接收从列表页发来的各种数据参数
     */
    private BroadcastReceiver receiverResponse = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST)) {
                    int actionType = intent.getIntExtra(Constants.DATA_INT, 0);
                    if (actionType != ACTION_TYPE_RESPONSE) return;
                    String paramsToken = intent.getStringExtra(Constants.DATA_ENTITY);  // 获取Token
                    List<ParamBean> paramList = StaticTransferParams.getInstance().getParams(paramsToken);  // 获取数据
                    if (LList.isEmpty(paramList)) return;
                    String securityId = intent.getStringExtra(Constants.DATA_SECURITY_ID);
                    boolean isRequestFromJobPage = intent.getBooleanExtra(Constants.DATA_BOOLEAN9, false);
                    int from = intent.getIntExtra(Constants.DATA_FROM, ParamBean.FROM_NONE);
//                long userId = intent.getLongExtra(Constants.DATA_ID, 0);
//                long jobId = intent.getLongExtra(Constants.DATA_JOB_ID, 0);
//                long expectId = intent.getLongExtra(Constants.DATA_LONG, 0);
                    boolean hasMore = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
                    String errorReason = intent.getStringExtra(ERROR_REASON);
                    if (responseListener == null) return;
                    if (TextUtils.isEmpty(errorReason)) {
                        responseListener.response(paramList, securityId, from, hasMore, isRequestFromJobPage);
                    } else {
                        responseListener.responseError(errorReason);
                    }
                }
            } catch (Exception e) {
                //偶现一例 ConcurrentModificationException，埋点观察下具体情况
                TLog.error(JobDetailNextPageHelper.class.getSimpleName(), "JobDetailNextPageHelper handler catch =%s", e.getMessage());
                ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_FIX_APM_BUG, "JobDetailNextPageHelper").param("p2", Log.getStackTraceString(e)).report();
            }
        }
    };

    private OnJobDetailPageRequestListener requestListener;

    public JobDetailNextPageHelper setRequestListener(OnJobDetailPageRequestListener requestListener) {
        this.requestListener = requestListener;
        return this;
    }

    public interface OnJobDetailPageRequestListener {
        void request(int from, String tag);
    }

    private OnJobDetailPageResponseListener responseListener;

    public JobDetailNextPageHelper setResponseListener(OnJobDetailPageResponseListener responseListener) {
        this.responseListener = responseListener;
        return this;
    }

    public interface OnJobDetailPageResponseListener {
        void response(List<ParamBean> paramList, String securityId, int from, boolean hasMore, boolean isRequestFromJobPage);

        void responseError(String error);
    }
}
