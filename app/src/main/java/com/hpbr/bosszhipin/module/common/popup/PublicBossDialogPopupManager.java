package com.hpbr.bosszhipin.module.common.popup;


import android.app.Activity;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;

import com.bszp.kernel.user.UserHelper;
import com.facebook.drawee.view.SimpleDraweeView;
import com.fresco.lib.FrescoUtils;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.advancedsearch.export.AdvancedSearchRouter;
import com.hpbr.bosszhipin.advancedsearch.export.SearchConst;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.dialog.BossItemF1PopWindowDialog;
import com.hpbr.bosszhipin.common.dialog.BossPushEngineContentTioDialog;
import com.hpbr.bosszhipin.common.dialog.CertificationViolateRuleDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.ReportOnlineJobDialog;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.login.config.ChangeMobileFrom;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.activity.MainUserAgreementActivity;
import com.hpbr.bosszhipin.module.position.utils.F1ShowCardTimeManager;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.ui.popup.ZPUIPopup;
import com.twl.utils.ActivityUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.F1DacSafeTipRequest;
import net.bosszhipin.api.GetAgreementUpdateNoticeRequest;
import net.bosszhipin.api.GetAgreementUpdateNoticeResponse;
import net.bosszhipin.api.GetBossCompleteSupplementPopWindowRequest;
import net.bosszhipin.api.GetBossCompleteSupplementPopWindowResponse;
import net.bosszhipin.api.GetBossItemF1PopWindowRequest;
import net.bosszhipin.api.GetBossItemF1PopWindowResponse;
import net.bosszhipin.api.GetBossLivingRoomInfoRequest;
import net.bosszhipin.api.GetBossLivingRoomInfoResponse;
import net.bosszhipin.api.GetBossReportDisposeJobListRequest;
import net.bosszhipin.api.GetBossReportDisposeJobListResponse;
import net.bosszhipin.api.GetCertViolateRuleRequest;
import net.bosszhipin.api.GetCertViolateRuleResponse;
import net.bosszhipin.api.GetCheckPhoneStatusRequest;
import net.bosszhipin.api.GetCheckPhoneStatusResponse;
import net.bosszhipin.api.GetF2ItemUsingEntranceRequest;
import net.bosszhipin.api.GetF2ItemUsingEntranceResponse;
import net.bosszhipin.api.GetHunterSpotCheckPopRequest;
import net.bosszhipin.api.GetHunterSpotCheckPopResponse;
import net.bosszhipin.api.GetPushEngineContentTipRequest;
import net.bosszhipin.api.GetPushEngineContentTipResponse;
import net.bosszhipin.api.GetSatisfactionInvestigateRequest;
import net.bosszhipin.api.GetSatisfactionInvestigateResponse;
import net.bosszhipin.api.PublicBossDialogDataBatchRequest;
import net.bosszhipin.api.PublicBossDialogDataBatchResponse;
import net.bosszhipin.api.SettingPushNoticeGetRequest;
import net.bosszhipin.api.SettingPushNoticeGetResponse;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerCertViolateRuleDialogBean;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

/**
 * Author: ZhouYou
 * Date: 2018/4/18.
 * 首页公共弹窗业务管理
 */
public class PublicBossDialogPopupManager {
    public static final  String TAG ="PublicBossDialog";
    public static  final  int PUSH_NOTIFY_STATUS_ALLOW=1;//允许 1天一次
    public static  final  int PUSH_NOTIFY_STATUS_REJECT=2;//2 拒绝 10天一次
    public PublicBossDialogDataBatchResponse bossDialogDataBatchResponse;

    private static GetBossItemF1PopWindowResponse bossItemF1PopWindowResponse;

    /**
     * 批量获取Dialog数据的网络请求
     */
    public void dialogDataBatchRequest(final BaseActivity activity, final int currentSelectIndex) {
        PublicBossDialogDataBatchRequest batchRequest = new PublicBossDialogDataBatchRequest(new ApiRequestCallback<PublicBossDialogDataBatchResponse>() {
            @Override
            public void onSuccess(ApiData<PublicBossDialogDataBatchResponse> data) {
                bossDialogDataBatchResponse = data.resp;
                judgeDialogSequence(activity, currentSelectIndex);
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.d("DialogPopupManager", "数据请求失败");
            }
        });

        // 809 视频直播弹框
        GetPushEngineContentTipRequest getPushEngineContentTipRequest = new GetPushEngineContentTipRequest();
        getPushEngineContentTipRequest.popType = 1;

        batchRequest.getPushEngineContentTipRequest = getPushEngineContentTipRequest;
        batchRequest.userSatisfactionInvestigationRequest = new GetSatisfactionInvestigateRequest();
        batchRequest.bossLivingRoomInfoRequest = new GetBossLivingRoomInfoRequest();
        batchRequest.agreementUpdateNoticeRequest = new GetAgreementUpdateNoticeRequest();
        batchRequest.bossItemF1PopWindowRequest = new GetBossItemF1PopWindowRequest();
        batchRequest.f1DacSafeTipRequest = new F1DacSafeTipRequest();
        batchRequest.hunterSpotCheckPopRequest = new GetHunterSpotCheckPopRequest();
        batchRequest.getCheckPhoneStatusRequest = new GetCheckPhoneStatusRequest();
        batchRequest.certViolateRuleRequest = new GetCertViolateRuleRequest();
        batchRequest.reportDisposeJobListRequest = new GetBossReportDisposeJobListRequest();
        batchRequest.completeSupplementPopWindowRequest = new GetBossCompleteSupplementPopWindowRequest();
        if (!NotificationCheckUtils.areNotificationsEnabled(activity) && DataStarGray.getInstance().isShowBF1NewGuideABTest()) {
            batchRequest.settingPushNoticeGetRequest = new SettingPushNoticeGetRequest();
        }
        batchRequest.execute();

        getItemUsingTipEntry();
        AdvancedSearchRouter.getAdvSearchTip(0);
    }

    /**
     * 获取F2联系人道具使用情况入口
     */
    private void getItemUsingTipEntry() {
        GetF2ItemUsingEntranceRequest request = new GetF2ItemUsingEntranceRequest(new ApiRequestCallback<GetF2ItemUsingEntranceResponse>() {
            @Override
            public void onSuccess(ApiData<GetF2ItemUsingEntranceResponse> data) {
                GetF2ItemUsingEntranceResponse resp = data.resp;
                if (resp != null) {
                    String json = "";
                    try {
                        json = GsonUtils.getGson().toJson(resp.f2Tip);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    SpManager.get().user().edit().putString(SearchConst.ITEM_USING_TIP_ENTRANCE, json).apply();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    public void judgeDialogSequence(final BaseActivity activity, final int currentSelectIndex) {
        // 判断是否有弹框缓存数据
        if (show(activity, currentSelectIndex)) {
            return;
        }

        if (bossDialogDataBatchResponse != null) {
            GetAgreementUpdateNoticeResponse agreementUpdateNoticeResponse = bossDialogDataBatchResponse.agreementUpdateNoticeResponse;
            if (agreementUpdateNoticeResponse != null
                    && agreementUpdateNoticeResponse.view == 1
                    && BossAgreementNoticeTask.getInstance().needShow()) {
                BossAgreementNoticeTask.getInstance().setData(agreementUpdateNoticeResponse);
            }
            GetBossLivingRoomInfoResponse bossLivingRoomInfoResponse = bossDialogDataBatchResponse.bossLivingRoomInfoResponse;
            if (bossLivingRoomInfoResponse != null && !TextUtils.isEmpty(bossLivingRoomInfoResponse.clickUrl)) {
                BossLivingRoomInfoTask.getInstance().setData(bossLivingRoomInfoResponse);
                bossDialogDataBatchResponse.bossLivingRoomInfoResponse = null;
            }
            // Boss首页道具弹框
            GetBossItemF1PopWindowResponse itemF1PopWindowResponse = bossDialogDataBatchResponse.bossItemF1PopWindowResponse;
            if (itemF1PopWindowResponse != null && itemF1PopWindowResponse.isDisplay()) {
                bossItemF1PopWindowResponse = itemF1PopWindowResponse;
            } else {
                bossItemF1PopWindowResponse = null;
            }
            // 用户满意度弹框
            GetSatisfactionInvestigateResponse satisfactionInvestigateResponse = bossDialogDataBatchResponse.satisfactionInvestigateResponse;
            if (satisfactionInvestigateResponse != null
                    && satisfactionInvestigateResponse.surveyId > 0
                    && SatisfactionInvestigateTask.getInstance().needShow()) {
                SatisfactionInvestigateTask.getInstance().setData(satisfactionInvestigateResponse);
            }

            GetHunterSpotCheckPopResponse hunterSpotCheckPopResponse = bossDialogDataBatchResponse.hunterSpotCheckPopResponse;
            if (hunterSpotCheckPopResponse != null && hunterSpotCheckPopResponse.isDisplay()) {
                HunterSpotCheckTask.getInstance().setData(hunterSpotCheckPopResponse);
                bossDialogDataBatchResponse.hunterSpotCheckPopResponse = null;
            }

            GetCheckPhoneStatusResponse getCheckPhoneStatusResponse = bossDialogDataBatchResponse.getCheckPhoneStatusResponse;
            if (getCheckPhoneStatusResponse != null) {
                if (getCheckPhoneStatusResponse.isShow) {
                    GetCheckPhoneDialogTask.getInstance().setHasShow(false);
                }
                GetCheckPhoneDialogTask.getInstance().setData(getCheckPhoneStatusResponse);
            }

            GetCertViolateRuleResponse certViolateRuleResponse = bossDialogDataBatchResponse.certViolateRuleResponse;
            if (certViolateRuleResponse != null && certViolateRuleResponse.show && certViolateRuleResponse.dialog != null) {
                CertViolateRuleBossTask.getInstance().setData(certViolateRuleResponse.dialog);
                bossDialogDataBatchResponse.certViolateRuleResponse = null;
            }

            GetBossReportDisposeJobListResponse reportDisposeJobListResponse = bossDialogDataBatchResponse.reportDisposeJobListResponse;

            if (reportDisposeJobListResponse != null && !LList.isEmpty(reportDisposeJobListResponse.list)) {
                BossReportOnlineJobTask.getInstance().setData(reportDisposeJobListResponse);
                bossDialogDataBatchResponse.reportDisposeJobListResponse = null;
            }

            GetPushEngineContentTipResponse getPushEngineContentTipResponse = bossDialogDataBatchResponse.getPushEngineContentTipResponse;
            if (getPushEngineContentTipResponse != null && getPushEngineContentTipResponse.show && !LText.empty(getPushEngineContentTipResponse.img)) {
                BossPushEngineContentTipTask.getInstance().setData(getPushEngineContentTipResponse);
                bossDialogDataBatchResponse.getPushEngineContentTipResponse = null;
            }

            GetBossCompleteSupplementPopWindowResponse completeSupplementPopWindowResponse = bossDialogDataBatchResponse.completeSupplementPopWindowResponse;
            if (completeSupplementPopWindowResponse != null
                    && completeSupplementPopWindowResponse.isShow
                    && BossCompleteSupplementTask.getInstance().needShow()) {
                BossCompleteSupplementTask.getInstance().setData(completeSupplementPopWindowResponse);
                bossDialogDataBatchResponse.completeSupplementPopWindowResponse = null;
            }
            SettingPushNoticeGetResponse settingPushNoticeGetResponse = bossDialogDataBatchResponse.settingPushNoticeGetResponse;
            if (settingPushNoticeGetResponse != null && settingPushNoticeGetResponse.show) {
                BossShowPushSwitchGuideTask.getInstance().setData(settingPushNoticeGetResponse);
                bossDialogDataBatchResponse.settingPushNoticeGetResponse = null;
            }

            show(activity, currentSelectIndex);
        }
    }

    private boolean show(BaseActivity activity, int currentSelectIndex) {
        if (!ActivityUtils.isValid(activity) || !UserManager.isBossRole()) {
            return false;
        }

        // 如果有用户协议弹框，则不弹其他弹框
        if (ForegroundUtils.get().hasActivity(MainUserAgreementActivity.class)) {
            return false;
        }

        if (currentSelectIndex == 0 || currentSelectIndex == 1) {
            UserBean user = UserManager.getLoginUser();
            if (user != null && user.bossInfo != null && user.bossInfo.certification == BossInfoBean.AUTH_STATUS_PASSED) {
                if (BossAgreementNoticeTask.getInstance().getData() != null) {
                    showBossAgreement(activity, BossAgreementNoticeTask.getInstance().getData());
                    return true;
                } else if (BossCompleteSupplementTask.getInstance().getData()  != null)  {
                    GetBossCompleteSupplementPopWindowResponse data = BossCompleteSupplementTask.getInstance().getData();
                    BossPageRouter.jumpToBossAvatarAndPositionCompletionActivity(activity, data.job);
                    BossCompleteSupplementTask.getInstance().saveDialogShowTime();
                    return true;
                } else if (bossDialogDataBatchResponse != null
                        && bossDialogDataBatchResponse.f1DacSafeTipResponse != null
                        && bossDialogDataBatchResponse.f1DacSafeTipResponse.show
                        && currentSelectIndex == 0) {// 0 安全提示
                    bossDialogDataBatchResponse.f1DacSafeTipResponse.showDialog(activity);
                    return true;
                } else if (CertViolateRuleBossTask.getInstance().getData() != null) {
                    ServerCertViolateRuleDialogBean bean = CertViolateRuleBossTask.getInstance().getData();
                    CertificationViolateRuleDialog d = new CertificationViolateRuleDialog(activity, bean);
                    d.show();
                    CertViolateRuleBossTask.getInstance().saveDialogShowTime();
                    return true;
                } else if (GetCheckPhoneDialogTask.getInstance() != null && GetCheckPhoneDialogTask.getInstance().getData() != null && GetCheckPhoneDialogTask.getInstance().getData().isShow && currentSelectIndex == 0 && !GetCheckPhoneDialogTask.getInstance().isHasShow()) {
                    GetCheckPhoneStatusResponse response = GetCheckPhoneDialogTask.getInstance().getData();
                    if (response != null) {
                        showChangePhoneDialog(activity);
                        GetCheckPhoneDialogTask.getInstance().setHasShow(true);
                        response.isShow = false;
                        return true;
                    }
                } else if (HunterSpotCheckTask.getInstance().getData() != null) {
                    showHunterSpotCheckDialog(activity, HunterSpotCheckTask.getInstance().getData());
                    HunterSpotCheckTask.getInstance().saveDialogShowTime();
                    return true;
                } else if (BossLivingRoomInfoTask.getInstance().getData() != null) {
                    GetBossLivingRoomInfoResponse data = BossLivingRoomInfoTask.getInstance().getData();
                    showBossLiveRoomInfoDialog(activity, data);
                    BossLivingRoomInfoTask.getInstance().saveDialogShowTime();
                    return true;
                } else if (bossItemF1PopWindowResponse != null) {
                    BossItemF1PopWindowDialog d = new BossItemF1PopWindowDialog(activity, bossItemF1PopWindowResponse);
                    boolean isShow = d.show();
                    if (isShow) {
                        bossItemF1PopWindowResponse = null;
                        if (null != bossDialogDataBatchResponse) {
                            bossDialogDataBatchResponse.bossItemF1PopWindowResponse = null;
                        }
                    }
                    return isShow;
                } else if (BossReportOnlineJobTask.getInstance().getData() != null) {
                    GetBossReportDisposeJobListResponse data = BossReportOnlineJobTask.getInstance().getData();
                    ReportOnlineJobDialog d = new ReportOnlineJobDialog(activity, data);
                    d.show();
                    BossReportOnlineJobTask.getInstance().saveDialogShowTime();
                    return true;
                } else if (SatisfactionInvestigateTask.getInstance().getData() != null) {
                    GetSatisfactionInvestigateResponse data = SatisfactionInvestigateTask.getInstance().getData();
                    long surveyId = data.surveyId;
                    int messageType = data.preDialog != null ? data.preDialog.messageType : 0;
                    int userType = data.userType;

                    SatisfactionInvestigateNewStyleDialog investigateNewStyleDialog = new SatisfactionInvestigateNewStyleDialog();
                    investigateNewStyleDialog.setOnEventListener((score, content) -> new SatisfactionSubmitSuccessDialog().showDialog(activity, null));
                    boolean isShow = investigateNewStyleDialog.showDialog(activity, surveyId, messageType, userType, data.title);
                    if (isShow) {
                        SatisfactionInvestigateTask.getInstance().saveDialogShowTime();
                    }
                    return isShow;
                } else if (BossPushEngineContentTipTask.getInstance().needShow()) {
                    BossPushEngineContentTioDialog dialog = new BossPushEngineContentTioDialog(activity);
                    dialog.show();
                    return true;
                } else if (DataStarGray.getInstance().isShowBF1NewGuideABTest()){
                    if (BossShowPushSwitchGuideTask.getInstance().needShow()){
                        if (DataStarGray.getInstance().isShowBF1NewGuideABTest1()){
                            showNotifyOpenPopup(activity,true);
                        }else {
                            showSystemNotifyOpenPop(activity);
                        }
                        BossShowPushSwitchGuideTask.getInstance().saveDialogShowTime();
                        return true;
                    }
                }else if (!NotificationCheckUtils.areNotificationsEnabled(activity) && F1ShowCardTimeManager.f1NotifyOpenNeedShow()) {
                    showNotifyOpenPopup(activity,false);
                    return true;
                }
//                else if (!IgnoreBatteryDialog.isShowed && IgnoreBatteryDialog.isNeedShow()) {
//                    new IgnoreBatteryDialog().showDialog(activity);
//                    IgnoreBatteryDialog.isShowed = true;
//                }
            }
        }
        return false;
    }




    private void showSystemNotifyOpenPop(BaseActivity activity){
        NotificationCheckUtils.dialogIsShowing = true;
        AnalyticsFactory.create().action("b-push-open-popup-exposure").build();
        NotificationCheckUtils.requestNotificationPermissionABTest2(activity, new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                if (yes){
                    AnalyticsFactory.create().action("b-push-open-popup-click").param("p",1).build();
                    if (permission!=null){ //系统弹窗 点击了允许获得权限
                        submitPushNotifyStatus(PUSH_NOTIFY_STATUS_ALLOW);
                    }
                }else {
                    ToastUtils.showText("请开启通知权限");
                    if (permission!=null){
                        if (permission.dialogClickType==0){//系统弹窗用户点击了拒绝
                            submitPushNotifyStatus(PUSH_NOTIFY_STATUS_REJECT);
                            AnalyticsFactory.create().action("b-push-open-popup-click").param("p",2).build();
                        }else if (permission.dialogClickType==1){// app 弹窗 用户点击了取消
                            submitPushNotifyStatus(PUSH_NOTIFY_STATUS_REJECT);
                            AnalyticsFactory.create().action("b-push-open-popup-click").param("p",2).build();
                        }else if (permission.dialogClickType==2){// app 弹窗用户点击了去设置
                            submitPushNotifyStatus(PUSH_NOTIFY_STATUS_ALLOW);
                            AnalyticsFactory.create().action("b-push-open-popup-click").param("p",1).build();
                        }
                    }
                }
            }

            @Override
            public void onRemindersClick(int clickType) {
                if (clickType==PermissionCallback.REMINDERS_CLICK_OK){
                    submitPushNotifyStatus(PUSH_NOTIFY_STATUS_ALLOW);
                    AnalyticsFactory.create().action("b-push-open-popup-click").param("p",1).build();
                }else {
                    submitPushNotifyStatus(PUSH_NOTIFY_STATUS_REJECT);
                    AnalyticsFactory.create().action("b-push-open-popup-click").param("p",2).build();
                }
            }
        });
    }


    private void showNotifyOpenPopup(Activity activity,boolean isAbTest) {
        long currentTime = System.currentTimeMillis();
        ZPUIPopup notifyPopup = ZPUIPopup.create(activity);
        notifyPopup.setContentView(R.layout.layout_f1_notify_open, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT).setOnViewListener((view, popup) -> {
            MTextView tvToOpen = view.findViewById(R.id.tv_to_open);
            SimpleDraweeView lottie = view.findViewById(R.id.mLottieView);
            Uri uri = (new Uri.Builder()).scheme("res")
                    .path(String.valueOf(NightUtil.isDarkMode(activity) ? R.drawable.check_notification_open_animation_dark : R.drawable.check_notification_open_animation)).build();
            String url = uri.toString();
            FrescoUtils.loadGifFromNetwork(lottie, url, true);
            ImageView ivCancel = view.findViewById(R.id.iv_cancle);
            tvToOpen.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AnalyticsFactory.create().action("push-notice-alert-click").param("p", 1).build();
                    notifyPopup.dismiss();
                    if (DataStarGray.getInstance().bF1PushGuideClickABTest()) {
                        NotificationCheckUtils.requestNotificationPermissionToSet(activity);
                    } else {
                        NotificationCheckUtils.requestNotificationPermission(activity, null);
                    }
                    if (isAbTest){
                        submitPushNotifyStatus(PUSH_NOTIFY_STATUS_ALLOW);
                    }
                }
            });
            ivCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (isAbTest){
                        submitPushNotifyStatus(PUSH_NOTIFY_STATUS_REJECT);
                    }
                    notifyPopup.dismiss();
                }
            });

        }).setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                NotificationCheckUtils.dialogIsShowing = false;
            }
        }).apply();
        if (!isAbTest){
            SpManager.get().user().edit().putLong(F1ShowCardTimeManager.F1_PUSH_OPEN_SHOW_TIME, currentTime).apply();
        }
        NotificationCheckUtils.dialogIsShowing = true;
        activity.getWindow().getDecorView().post(new Runnable() {
            @Override
            public void run() {
                if (ActivityUtils.isValid(activity)) {
                    notifyPopup.showAtLocation(activity.getWindow().getDecorView(), Gravity.CENTER, 0, 0);
                    AnalyticsFactory.create().action("push-notice-alert").build();
                }
            }
        });
    }


    private void  submitPushNotifyStatus(int status){

        SimpleApiRequest.POST(ChatUrlConfig.URL_CHAT_NOTIFY_SETTING_PUSH_NOTICE_UPDATE)
                .addParam("status", status)
                .execute();
    }

    /**
     * 展示进入直播间的Boss信息
     *
     * @param activity
     * @param data
     */
    public void showBossLiveRoomInfoDialog(Activity activity, @NonNull GetBossLivingRoomInfoResponse data) {
        DialogUtils d = new DialogUtils.Builder(activity)
                .setCancelable(false) // 不可消除
                .setSingleButton()
                .setTitle(data.title)
                .setDesc(data.desc)
                .setPositiveAction(data.buttonText, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        new ZPManager(activity, data.clickUrl).handler();
                    }
                })
                .build();
        d.show();
    }

    private void showBossAgreement(Activity activity, GetAgreementUpdateNoticeResponse data) {
        SpannableStringBuilder builder = null;
        if (!TextUtils.isEmpty(data.content)) {
            int length = data.content.length();
            builder = new SpannableStringBuilder(data.content);
            if (!LList.isEmpty(data.contentHighlightList)) {
                int size = data.contentHighlightList.size();
                int startIndex;
                int endIndex;
                for (int i = 0; i < size; i++) {
                    GetAgreementUpdateNoticeResponse.ContentHighlightListBean bean = data.contentHighlightList.get(i);
                    if (bean == null) continue;
                    startIndex = bean.startIndex;
                    endIndex = bean.endIndex;
                    if (startIndex < 0 || endIndex <= startIndex || endIndex > length) {
                        continue;
                    }
                    builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.app_green_dark)), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    builder.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            new ZPManager(activity, bean.linkUrl).handler();
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            ds.setColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                            ds.setUnderlineText(false);
                        }
                    }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }

        DialogUtils d = new DialogUtils.Builder(activity)
                .setSingleButton()
                .setTitle(data.title)
                .setDesc(builder)
                .setPositiveAction(data.buttonText, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        BossAgreementNoticeTask.getInstance().saveDialogShowTime();
                    }
                })
                .build();
        d.show();
    }

    private void showHunterSpotCheckDialog(Activity activity, @NonNull GetHunterSpotCheckPopResponse data) {
        ServerDialogBean dialog = data.dialog;
        if (dialog == null || LList.isEmpty(dialog.buttonList)) {
            return;
        }
        ServerButtonBean button = LList.getElement(dialog.buttonList, 0);
        if (button != null) {
            DialogUtils d = new DialogUtils.Builder(activity)
                    .setSingleButton()
                    .setTitle(dialog.title)
                    .setDesc(dialog.content)
                    .setPositiveAction(button.text, new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            new ZPManager(activity, button.url).handler();
                        }
                    })
                    .build();
            d.show();
        }
    }


    private void showChangePhoneDialog(Activity activity) {


        //删除指定的设备
        DialogUtils d = new DialogUtils.Builder(activity)
                .setTitle("手机号还在正常使用吗")
                .setDoubleButton()
                .setDesc("手机号" + ViewCommon.keepPhoneSecret(UserHelper.getPhone()) + "还在正常使用吗，若不使用请及时修改账号绑定的手机号，以免影响正常使用")
                .setNegativeAction("正常使用", v1 -> {

                    finishPhoneRequest();
                })
                .setPositiveAction("修改手机号", v12 -> {
                    //删除指定的设备

                    LoginRouter.startChangeMobileActivity(activity, ChangeMobileFrom.FROM_MAINPAGE_DIALOG);
                    finishPhoneRequest();
                })
                .build();
        d.show();


    }

    private void finishPhoneRequest() {
        SimpleApiRequest.GET(LoginURLConfig.URL_GEEK_CHECK_PHONE_FINISH).execute();
    }

    public void destroy() {
        BossCompleteSupplementTask.getInstance().clearData();
    }

}
