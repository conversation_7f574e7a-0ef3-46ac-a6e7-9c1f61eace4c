package com.hpbr.bosszhipin.module.main.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.bszp.kernel.account.AccountHelper;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.advancedsearch.export.AdvancedSearchRouter;
import com.hpbr.bosszhipin.advancedsearch.export.SearchConst;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.common.AppMainThemeColorConfig;
import com.hpbr.bosszhipin.common.DialogTaskCommon;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.EntryAppDialog;
import com.hpbr.bosszhipin.common.dialog.StorageDirectoryDialog;
import com.hpbr.bosszhipin.common.helper.PreloadWebViewTool;
import com.hpbr.bosszhipin.common.pub.entity.LOGIN_STATUS;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactCache;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.get.export.GetConsts;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.manager.ApmManager;
import com.hpbr.bosszhipin.manager.AutoProtocolUtil;
import com.hpbr.bosszhipin.manager.LoginDeviceManager;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.manager.PreDownloadImageManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.common.dialog.manager.GeekF1DialogManager;
import com.hpbr.bosszhipin.module.common.dialog.manager.GeekF2DialogManager;
import com.hpbr.bosszhipin.module.common.identity.ChangeIdentityActivity;
import com.hpbr.bosszhipin.module.common.identity.IdentitySwitchJumpUtils;
import com.hpbr.bosszhipin.module.common.popup.PublicBossDialogPopupManager;
import com.hpbr.bosszhipin.module.common.popup.PublicGeekDialogPopupManager;
import com.hpbr.bosszhipin.module.company.circle.manager.GetHomeRedDotManager;
import com.hpbr.bosszhipin.module.contacts.entity.AdvertBean;
import com.hpbr.bosszhipin.module.contacts.entity.AllDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.manager.CommonWordManager;
import com.hpbr.bosszhipin.module.contacts.service.AdvertService;
import com.hpbr.bosszhipin.module.contacts.util.ChatUtils;
import com.hpbr.bosszhipin.module.contacts.views.GuideDialogHelper;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCardBean;
import com.hpbr.bosszhipin.module.interview.entity.InterviewComplainCardBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.main.DeskTopNoneReadManager;
import com.hpbr.bosszhipin.module.main.common.JobDelayHelper;
import com.hpbr.bosszhipin.module.main.common.LocationServiceInitializer;
import com.hpbr.bosszhipin.module.main.fragment.contacts.ContactDrawExpose;
import com.hpbr.bosszhipin.module.main.fragment.contacts.base.BaseContactFragment;
import com.hpbr.bosszhipin.module.main.fragment.geek.F2ContainerFragment;
import com.hpbr.bosszhipin.module.main.fragment.geek.GFindContainerFragment;
import com.hpbr.bosszhipin.module.main.fragment.manager.InteractedChatManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.module.main.helper.BossSpUtil;
import com.hpbr.bosszhipin.module.main.presenter.MainTabLayoutPresenter;
import com.hpbr.bosszhipin.module.main.views.MainTabLayoutView;
import com.hpbr.bosszhipin.module.my.activity.LoginDeviceVerifyActivity;
import com.hpbr.bosszhipin.module.pay.entity.PayResult;
import com.hpbr.bosszhipin.module.position.utils.SalaryDescShowGrayController;
import com.hpbr.bosszhipin.module.update.UpgradeManager;
import com.hpbr.bosszhipin.module.videointerview.ReceiveCallingManager;
import com.hpbr.bosszhipin.module_boss_export.BossConst;
import com.hpbr.bosszhipin.module_boss_export.BossMineModuleService;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekMineModuleService;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.receiver.LoginStatusManager;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.hpbr.bosszhipin.utils.screenshot.ScreenShotSetting;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.window.FloatWindowManager;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.mms.service.AppStatus;
import com.twl.ui.ToastUtils;
import com.twl.utils.IdleHandler;
import com.twl.utils.bitmap.BitmapLruCacheMemoryReuse;

import net.bosszhipin.api.GetAgreementNoticeRequest;
import net.bosszhipin.api.GetAgreementNoticeResponse;
import net.bosszhipin.api.LoginDeviceConfirmResponse;
import net.bosszhipin.api.ShortUrlResponse;
import net.bosszhipin.api.bean.ServerAgreementNoticeBean;
import net.bosszhipin.api.bean.ServerInteractBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;

//                              _oo0oo_
//                             o8888888o
//                             88" . "88
//                             (| -_- |)
//                             0\  =  /0
//                           ___/'---'\___
//                        .' \\\|     |// '.
//                       / \\\|||  :  |||// \\
//                      / _ ||||| -:- |||||- \\
//                      | |  \\\\  -  /// |   |
//                      | \_|  ''\---/''  |_/ |
//                      \  .-\__  '-'  __/-.  /
//                    ___'. .'  /--.--\  '. .'___
//                 ."" '<  '.___\_<|>_/___.' >'  "".
//                | | : '-  \'.;'\ _ /';.'/ - ' : | |
//                \  \ '_.   \_ __\ /__ _/   .-' /  /
//            ====='-.____'.___ \_____/___.-'____.-'=====
//                              '=---='
//
//          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
//               佛祖保佑          永无BUG       镇类之宝

/**
 * Created by monch on 15/3/31.
 */
public class MainActivity extends BaseActivity implements View.OnClickListener {
    private static final String TAG = "MainActivity";
    private static final int CLICK_INTERVAL = 400; // ms
    /**
     * 用于标记当前MainActivity是否在页面显示
     */
    public static boolean mMainActivityIsTopping = false;

    /**
     * 用户当前的登录状态
     */
    private int mUserLoginStatus;
    /**
     * 用户的当前角色，0代表牛人，1代表BOSS
     */
    private ROLE mUserRole;
    // 当前选择的tab的位置
    private int currentSelectIndex = 0;

    private MainTabLayoutView mainTabLayoutView;
    private MainTabLayoutPresenter mainTabLayoutPresenter;

    // 承载fragment的view
    private LinearLayout llContentView;

    // 3个BOSS的Fragment的实例
    private Fragment bFindFragment;
    private Fragment bossIncompleteInformationOnRecommend;
    private BaseContactFragment bContactsFragment;
    private Fragment bMyFragment;
    private Fragment advancedSearchTabFragment;
    private Fragment bossIncompleteInformationOnSearch;
    // 3个牛人Fragment的实例
    private GFindContainerFragment gFindContainerFragment;
    private F2ContainerFragment f2ContainerFragment;
    private BaseContactFragment gContactsFragment;
    private Fragment gMyFragment;

    // 版本更新是否已经初始化过
    private boolean mVersionUpgradeInit = false;
    // 是否第一次启动：-1 第一次；0 牛人；1 Boss
    private int isRoleFirstStart = -1;

    // GEEK初始化业务弹窗Manager
    private final PublicGeekDialogPopupManager geekDialogPopupManager = new PublicGeekDialogPopupManager();
    // BOSS初始化业务弹窗Manager
    private final PublicBossDialogPopupManager bossDialogPopupManager = new PublicBossDialogPopupManager();

    private Dialog mAdvertDialog;

    /**
     * 初始化牛人身份位置服务
     * true - 直接请求首页弹框逻辑
     */
    private boolean isGeekRoleLocationInitialized; // 只弹一次

    // 处理F3底部栏的双击事件
    private long lastClickF1Time = 0;
    private long lastClickF2Time = 0;
    private long lastClickF3Time = 0;
    private long lastClickF4Time = 0;
    private final Runnable clickF1AgainAction = this::onClickF1Again;
    private final Runnable clickF2AgainAction = this::onClickF2Again;
    private final Runnable clickF3AgainAction = this::onClickF3Again;
    private final Runnable clickF4AgainAction = this::onClickF4Again;

    private long lastBackTimer = 0;

    private int tabIndex = 0;
    private int subTabIndex = 1;
    // 标记，是否是通过代码切换 tab（非用户手动）
    private boolean isSwithTabProgrammatically;
    private ScreenShotSetting screenShotSetting;
    /*f2机会角标是否正在显示*/
    private boolean isF2ChangeLabelShowing = false;
    /**
     * 常用设备验证
     */
    private Observer<LoginDeviceConfirmResponse> mObserver;


    /**
     * 跳转MainActivity 并且选中 tab
     */
    public static void gotoMain(Context context) {
        //发送广播选择F1
        Intent broadcastIntent = new Intent(Constants.RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION);
        broadcastIntent.putExtra(Constants.DATA_INT, 0);
        context.sendBroadcast(broadcastIntent);
        //跳转到主界面
        Intent intent = new Intent(context, MainActivity.class);
        AppUtil.startActivity(context, intent, true, ActivityAnimType.NONE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        boolean isMainNaviStatusBarThemeDark = AppMainThemeColorConfig.get().mainNaviRes(this).isMainNaviStatusBarThemeDark();
        ImmersiveUtils.immersiveStyleBar(this, isMainNaviStatusBarThemeDark, NightUtil.isDarkMode(this));

        PrivacyDelayInitializer.getInstance().ensureInit(0);

        setContentView(R.layout.activity_main);
        // 获取用户的当前登录状态
        mUserLoginStatus = UserManager.getUserLoginStatus();
        mUserRole = UserManager.getUserRole();
        initView(); // 初始化view
        observeContacts();
        initReceiverService();  // 初始化广播接收者服务
        initLogin();

        showCommonUserAgreementWindows();

        refreshBottomTabAnimation(); // 刷新底部tab的动画

        getWindow().getDecorView().postDelayed(() -> {
            // 是否需要显示打开微信通知引导
            WechatGuideActivity.shouldStart(this);
        }, getResources().getInteger(android.R.integer.config_mediumAnimTime));
        NotificationCheckUtils.checkPermission(this);
        L.d("uid", "==========uid:" + UserManager.getUID());
        SalaryDescShowGrayController.getInstance().reset();

        /** 常用设备验证 */
        if (UserManager.isCurrentLoginStatus()) {
            LoginDeviceManager.confirmLogin();
            PreDownloadImageManager.preLoadImage();
        }
        registerObserver();

        // 刷新F4红点
        GetHomeRedDotManager.getInstance().getDiscoverDot();
        Intent loginSuccess = new Intent();
        loginSuccess.setAction(Constants.RECEIVER_LOGIN_SUCCESS);
        sendBroadcast(loginSuccess);

        bgAction();
    }

    private void bgAction() {
        if (UserManager.isBossRole()) {
            int lastTabSelection = SpManager.get().user().getInt(Constants.SP_MAIN_TAB_INDEX_SELECTION, 0);
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_SEARCH_SELECT_BY_DEFAULT)
                    .param("p", 2)
                    .param("p2", lastTabSelection == 1 ? 1 : 0)
                    .build();
        }

        boolean hasPermission = PermissionManager.checkAllSelfPermissions(this, Manifest.permission.WRITE_CALENDAR);
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_LOG_IN_CALENDAR_ACCESS_OR_NOT)
                .param("p", hasPermission ? 1 : 0)
                .build();
    }

    private void registerObserver() {
        /** 收到常用设备验证消息 */
        mObserver = new Observer<LoginDeviceConfirmResponse>() {
            @Override
            public void onChanged(LoginDeviceConfirmResponse model) {
                if (model != null && model.source > 0 && !TextUtils.isEmpty(model.uuid)) {
                    Activity topActivity = ForegroundUtils.get().getTopActivity();
                    if (UserManager.isCurrentLoginStatus() && !(topActivity instanceof LoginDeviceVerifyActivity)) {
                        LoginDeviceVerifyActivity.startActivity(MainActivity.this, model.source, model.uuid, model.time);
                    }
                }
            }
        };
        LiveBus.with(ChannelConstants.APP_LOGIN_DEVICE_CONFIRM, LoginDeviceConfirmResponse.class).observeForever(mObserver);
    }

    /**
     * 截图分享监听器注册
     */

    private void setScreenShot() {
        if (screenShotSetting == null) {
            screenShotSetting = new ScreenShotSetting(this);
        }
        if (currentSelectIndex == 0 && AccountHelper.isGeek()) {
            screenShotSetting.setAction(3);
            screenShotSetting.setNeedShowDialog(true);
            getLifecycle().addObserver(screenShotSetting);
            screenShotSetting.onResume();
        } else if (currentSelectIndex == 3 && AccountHelper.isGeek()) {
            screenShotSetting.setAction(5);
            screenShotSetting.setNeedShowDialog(true);
            getLifecycle().addObserver(screenShotSetting);
            screenShotSetting.onResume();
        } else {
            screenShotSetting.onPause();
            getLifecycle().removeObserver(screenShotSetting);
        }
    }

    /**
     * 初始化所有view
     */
    private void initView() {
        llContentView = findViewById(R.id.ll_main_view);
        mainTabLayoutView = findViewById(R.id.main_tab_layout_view);
        mainTabLayoutPresenter = new MainTabLayoutPresenter(mainTabLayoutView);
        mainTabLayoutPresenter.bindListener(this);
    }

    public String getThemeName(Context context) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(getPackageName(), PackageManager.GET_META_DATA);
            int themeResId = packageInfo.applicationInfo.theme;
            return getResources().getResourceEntryName(themeResId);
        } catch (PackageManager.NameNotFoundException e) {
            return null;
        }
    }

    private void showCommonUserAgreementWindows() {
        // 判断身份对应的数据是否完善
        boolean needRequest = false;
        if (UserManager.isBossRole()
                && UserManager.isBasicInfoCompleteBoss(UserManager.getLoginUser())
                && TextUtils.isEmpty(UserManager.getSecurityUrl())) {
            needRequest = true;
        } else if (UserManager.isGeekRole()
                && UserManager.isInfoCompleteGeek(UserManager.getLoginUser())
                && TextUtils.isEmpty(UserManager.getSecurityUrl())) {
            needRequest = true;
        }
        if (needRequest) {
            GetAgreementNoticeRequest request = new GetAgreementNoticeRequest(new ApiRequestCallback<GetAgreementNoticeResponse>() {
                @Override
                public void onSuccess(ApiData<GetAgreementNoticeResponse> data) {
                    GetAgreementNoticeResponse resp = data.resp;
                    boolean needShow = false;
                    if (resp != null && !LList.isEmpty(resp.popWindows)) {
                        for (ServerAgreementNoticeBean item : resp.popWindows) {
                            if (item != null && item.needShow()) {
                                needShow = true;
                                break;
                            }
                        }
                        if (needShow) {
                            MainUserAgreementActivity.startActivity(MainActivity.this, resp.popWindows);
                        }
                    }

                    if (!needShow) {
                        // 初始化弹框服务
                        initDialogRequest();
                    }
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    // 初始化弹框服务
                    initDialogRequest();
                }
            });
            HttpExecutor.execute(request);
        } else {
            // 初始化弹框服务
            initDialogRequest();
        }
    }

    private void initDialogRequest() {
        // 对于Boss端来说，首页的弹窗的业务
        if (UserManager.isBossRole()) {
            if (CommonConfigManager.getInstance().isBossF1Location()) {
                LocationServiceInitializer initializer = new LocationServiceInitializer(this);
                initializer.initLocationService();
            }
            bossDialogPopupManager.dialogDataBatchRequest(this, currentSelectIndex);
        } else {

            boolean needRequestGeekDialog = true;
            if (CommonConfigManager.getInstance().isGeekF1Location()) {
                needRequestGeekDialog = initGeekRoleLocationService();
            }
            if (needRequestGeekDialog) {
                geekDialogPopupManager.dialogDataBatchRequest();
            }
        }
    }

    private void refreshBottomTabAnimation() {
        mainTabLayoutPresenter.refreshBottomTabAnimation();
    }

    private boolean initGeekRoleLocationService() {
        if (isGeekRoleLocationInitialized) {
            return true;
        }
        isGeekRoleLocationInitialized = true;
        LocationServiceInitializer initializer = new LocationServiceInitializer(this, new LocationServiceInitializer.OnLocationInitializerCallback() {
            @Override
            public void onComplete() {
                geekDialogPopupManager.dialogDataBatchRequest();
            }
        });
        return initializer.checkLocationMaimGeekPermission();
    }

    /**
     * 初始化广播接收者服务
     */
    private void initReceiverService() {
        //信息未完整
        IntentFilter mModifySelectIndexFilter = new IntentFilter();
        mModifySelectIndexFilter.addAction(Constants.RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION);
        registerReceiver(mModifySelectIndexReceiver, mModifySelectIndexFilter);
        // 独占式广播广播
        IntentFilter mAdvertFilter = new IntentFilter();
        mAdvertFilter.addAction(Constants.RECEIVER_ADVERT_SHOW_ACTION);
        registerReceiver(mAdvertBroadcast, mAdvertFilter);

        IntentFilter mGuideDialogFilter = new IntentFilter();
        mGuideDialogFilter.addAction(Constants.RECEIVER_GUIDE_DIALOG_SHOW_ACTION);
        registerReceiver(mGuideDialogReceiver, mGuideDialogFilter);
        // 注册公共的广播接收器
        ReceiverUtils.register(this, mCommonReceiver, Constants.RECEIVER_ZP_PROTOCOL_ACTION,
                Constants.RECEIVER_NOTIFY_UPDATE_DIALOG_DATA_ACTION,
                Constants.RECEIVER_ADVANCED_SEARCH_TAB_SUBSCRIBED_TIP,
                Constants.RECEIVER_ADVANCED_SEARCH_TAB_FUNCTIONAL_TIP,
                Constants.RECEIVER_REFRESH_YOU_LE_ACTION,
                Constants.RECEIVER_YOU_LE_SHOW_VIDEO_ACTION,
                Constants.RECEIVER_STUDENT_ROLE_SWITCH);
        // 注册app升级广播接收器
        ReceiverUtils.register(this, mAppUpgradeReceiver, Constants.RECEIVER_APP_UPGRADE_ACTION);


        // 关闭安全框架的广播接受
        IntentFilter mCloseSafeFrameworkFilter = new IntentFilter();
        mCloseSafeFrameworkFilter.addAction(Constants.RECEIVER_COLOSE_SAFE_WINDOWS);
        registerReceiver(closeSafeFrameworkReceiver, mCloseSafeFrameworkFilter);
    }

    private void observeContacts() {
        ContactManager.getInstance().observeContacts(this, new Observer<List<ContactBean>>() {
            @Override
            public void onChanged(List<ContactBean> contactBeans) {
                refreshF2NoneRead();
                refreshF3NoneRead();
            }
        });
    }

    /**
     * 初始化登录服务
     */
    private void initLogin() {
        if (UserManager.getUserLoginStatus() == LOGIN_STATUS.LOGIN) {
            LoginStatusManager.changed(this, true);
        }
        // 提交统计数据
        AnalyticsFactory.commitAction();
        // 初始化独占式广告服务
        AdvertService.startupAdvertService(this);
    }

    public int getCurrentSelectIndex() {
        return currentSelectIndex;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        AppUtil.hideSoftInput(this);

        refreshBottomTabAnimation();
        SalaryDescShowGrayController.getInstance().reset();
        showCommonUserAgreementWindows();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Intent intent = getIntent();
        // 设置此变量为true，证明当前MainActivity为栈顶
        MainActivity.mMainActivityIsTopping = true;
        // 处理外部进入Boss直聘的协议解析
        handleExternalAgreement(intent);
        // 处理进入Boss直聘的协议解析
        handlerZPManagerAgreement(intent);
        // 根据用户登录状态，分别刷新不同的view样式
        refreshLoginStatus();
        // 获取用户的当前登录状态
        mUserLoginStatus = UserManager.getUserLoginStatus();
        // 检测并处理用户登录但未完善信息的情况
        if (!handleUserInfoComplete()) {
            handleNoCompleteWebView(intent);
            return;
        }
        // 处理是否为聊天启动
        handleChatMessage(intent);
        // 处理未登录预览的数据实例
        handleNoneLoginPreviewPositionData(intent);
        // 刷新当前的用户角色，并分配用户的页面
        refreshUserRole();
        // 初始化底部样式
        mainTabLayoutPresenter.refreshTabs(currentSelectIndex);
        //刷新底部tab 背景色
        mainTabLayoutPresenter.refreshTabLayoutBackground();
//        initBottomTab();
        // 刷新fragment和tab
        selectFragments(currentSelectIndex);

        // 刷新F3底部导航红点
        mainTabLayoutPresenter.refreshF3NoneRead();
//        refreshF3NoneRead();

        UpgradeManager.getInstance().check(this);
        ApmManager.checkForUpgrade(this);
        // 第一次启动
        if (isRoleFirstStart != UserManager.getUserRole().get() && UserManager.isCurrentLoginStatus()) {
            isRoleFirstStart = UserManager.getUserRole().get();
        }
        // 处理面试房卡任务
        InterviewCardBean.inApplication();
        // 处理面试投诉房卡任务
        InterviewComplainCardBean.inApplication();
        // 处理对话框任务
        DialogTaskCommon.handlerAll();

        SecurityFrameworkManager.getInstance().check(true);

        // 业务弹窗
        if (UserManager.isBossRole()) {
            bossDialogPopupManager.judgeDialogSequence(this, currentSelectIndex);
        } else {
            geekDialogPopupManager.judgeDialogSequence(this, currentSelectIndex);
        }

        if (!MSGManager.get().isConnected()) {
            MSGManager.get().connect();
        }
        NotificationCheckUtils.checkPermission(this);
        //检查DB损坏 ，引导修复弹窗
        MessageDaoFactory.checkDBRepair();
        new StorageDirectoryDialog(this).show();

        handleLazyTask();

        AutoProtocolUtil.checkProtocol(this);
    }

    /**
     * 处理外部连接打开Boss直聘的协议
     */
    private void handleExternalAgreement(Intent intent) {
        if (intent == null) return;
        //取得外部链接打开app的协议
        String action = intent.getAction();
        if (!Intent.ACTION_VIEW.equals(action)) return;
        Uri uri = intent.getData();
        if (uri == null) return;
        final String url = uri.toString();
        intent.setAction(null);
        intent.setData(null);
        if (LText.empty(url)) return;
        if (isAppLinkUrl(uri)) {
            requestAppLinkUrl(url);
            return;
        }
        if (isAppLinkLongUrl(uri)) {
            return;
        }
        App.get().getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                ZPManager zpManager = new ZPManager(MainActivity.this, url);
                String backtext = zpManager.params.get("backtext");
                String backurl = zpManager.params.get("backurl");
                String backba = zpManager.params.get("backba");
                FloatWindowManager.getInstance().showRtaWindow(MainActivity.this, backtext, backurl, backba);
                zpManager.handler();
            }
        }, 100);

    }

    private void requestAppLinkUrl(String url) {
        SimpleApiRequest.GET(URLConfig.URL_SHORTURL_ORIGINAL)
                .addParam("shortUrl", url)
                .setRequestCallback(new SimpleApiRequestCallback<ShortUrlResponse>() {
                    @Override
                    public void onSuccess(ApiData<ShortUrlResponse> data) {
                        if (data.resp.extra != null) {
                            new ZPManager(MainActivity.this, data.resp.extra.appUrl).handler();
                        }
                    }
                }).execute();
    }

    //短链
    private boolean isAppLinkUrl(@NonNull Uri uri) {
        String host = uri.getHost();
        String path = uri.getPath();
        if (path != null && "zpurl.cn".equals(host) && path.startsWith("/d/")) {
            return true;
        }
        return false;
    }

    //长链
    private boolean isAppLinkLongUrl(@NonNull Uri uri) {
        String host = uri.getHost();
        String path = uri.getPath();
        if (path != null && "d.bosszhipin.com".equals(host) && path.startsWith("/dl/openprotocol")) {
            String url = uri.getQueryParameter("url");
            if (!TextUtils.isEmpty(url)) {
                try {
                    String decode = URLDecoder.decode(url, "UTF-8");
                    new ZPManager(this, decode).handler();
                } catch (Exception ignored) {
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 处理直聘URL，打开Boss直聘的协议
     */
    private void handlerZPManagerAgreement(Intent intent) {
        if (intent == null) return;
        String url = intent.getStringExtra(Constants.DATA_ZP_URL);
        if (LText.empty(url)) return;
        intent.removeExtra(Constants.DATA_ZP_URL);
        new ZPManager(this, url).handler();
    }

    /**
     * 刷新登录状态，用于根据不同的登录状态，区分不同的样式
     */
    private void refreshLoginStatus() {
        if (mUserLoginStatus != UserManager.getUserLoginStatus()) {
            currentSelectIndex = 0; // 将页面刷新至F1页面
        }
    }

    /**
     * 检测并处理用户登录但未完善基本信息情况
     *
     * @return true：可以继续向下执行 false：不向下继续执行
     */
    private boolean handleUserInfoComplete() {
        // 如果当前未登录，不阻断执行流程

        if (mUserLoginStatus != LOGIN_STATUS.LOGIN) return true;
        // 获取登录用户信息
        UserBean mLoginUser = UserManager.getLoginUser();
        if (mLoginUser == null) {
            AccountHelper.restoreState();
            ApmAnalyzer.create().action("userInfo", "isEmpty")
                    .p2(String.valueOf(AccountHelper.getUid()))
                    .p3(String.valueOf(AccountHelper.getAccount()))
                    .report();
            return true;
        }
        boolean jumpCompletePage = getIntent().getBooleanExtra(Constants.DATA_BOOLEAN_JUNMP_COMPLETE_PAGE, false);
        L.d("jump", "======jumpCompletePage========:" + jumpCompletePage);
        if (jumpCompletePage) {
            IdentitySwitchJumpUtils.dealJump(this, false);
            return true;
        }
        if (UserManager.getUserRole() == ROLE.BOSS) {
            if (!UserManager.isBasicInfoCompleteBoss(mLoginUser)) {
                Intent intent = new Intent(this, ChangeIdentityActivity.class);
                intent.putExtra(ChangeIdentityActivity.CHANGE_IDENTITY_COME_KEY, false);
                intent.putExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, getExpectedRole());
                AppUtil.startActivity(this, intent, ActivityAnimType.NONE);
                return false;
            }
        } else if (UserManager.getUserRole() == ROLE.GEEK) {
            if (!UserManager.isInfoCompleteGeek(mLoginUser)) {
                Intent intent = new Intent(this, ChangeIdentityActivity.class);
                intent.putExtra(ChangeIdentityActivity.CHANGE_IDENTITY_COME_KEY, false);
                intent.putExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, getExpectedRole());
                AppUtil.startActivity(this, intent, ActivityAnimType.NONE);
                return false;
            }
        } else {
            Intent intent = new Intent(this, ChangeIdentityActivity.class);
            intent.putExtra(ChangeIdentityActivity.CHANGE_IDENTITY_COME_KEY, false);
            intent.putExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, getExpectedRole());
            AppUtil.startActivity(this, intent, ActivityAnimType.NONE);
            return false;
        }
        return true;
    }


    /**
     * 这是一个特殊逻辑
     * 未完善跳转 H5
     *
     * @param intent
     * @return
     */
    private void handleNoCompleteWebView(Intent intent) {
        String url = intent.getStringExtra(Constants.NOTIFY_STRING_URL);
        if (url != null && (url.startsWith("http") || url.startsWith("bosszp://bosszhipin.app/openwith?type=webview"))) {
            //复用
            handleChatMessage(intent);
        }
    }

    private int getExpectedRole() {
        int intExtra = getIntent().getIntExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, -1);
        /*
         * 解决一个bug, Boss或者牛人在创建页面点击返回键,回到MainActivity 发现身份不完整,
         * 跳转到 IdentityHelper 切换身份页面,切换身份根据getExpectedRole()的值!=-1会自动跳转到Boss创建/牛人创建页面
         * ..............................
         * 只有第一次登录成功才会帮助用户选择跳转到Boss创建/牛人创建页面
         */
        if (intExtra != -1) {
            getIntent().putExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, -1);
        }
        return intExtra;
    }

    /**
     * 处理聊天的消息
     *
     * @param intent 需要处理的intent
     */
    private void handleChatMessage(Intent intent) {
        if (mUserLoginStatus != LOGIN_STATUS.LOGIN || intent == null) return;
        String url = intent.getStringExtra(Constants.NOTIFY_STRING_URL);
        if (LText.empty(url)) return;
        String msgIdStr = intent.getStringExtra(Constants.MAIN_MSG_ID_KEY);
        String pushMothed = intent.getStringExtra(Constants.MAIN_MSG_PUSH_MOTHED);
        int isSystemPush = intent.getIntExtra(Constants.MAIN_MSG_PUSH_SYSTEM, 0);
        String serverPushType = intent.getStringExtra(Constants.MAIN_MSG_SERVER_PUSH_TYPE);
        String bizId = intent.getStringExtra(Constants.MAIN_MSG_PUSH_BIZID);
        String bizType = intent.getStringExtra(Constants.MAIN_MSG_PUSH_BIZTYPE);
        intent.removeExtra(Constants.NOTIFY_STRING_URL);
        intent.removeExtra(Constants.MAIN_MSG_ID_KEY);
        intent.removeExtra(Constants.MAIN_FROM_ID_KEY);
        intent.removeExtra(Constants.MAIN_MSG_PUSH_MOTHED);
        intent.removeExtra(Constants.MAIN_MSG_SERVER_PUSH_TYPE);
        intent.removeExtra(Constants.MAIN_MSG_PUSH_BIZID);
        intent.removeExtra(Constants.MAIN_MSG_PUSH_BIZTYPE);

        if (isSystemPush == 0) {
            /*
             * 自有通道或者sdk透传 push点击打点
             */
            AnalyticsFactory.create()
                    .action(AnalyticsAction.ACTION_PUSH_CLICK)
                    .param("p2", msgIdStr)
                    .param("p3", AppStatus.isForeground() ? "foreground" : "background")
                    .param("p4", pushMothed)
                    .param("p8", serverPushType)
                    .param("p9", bizId)
                    .param("p12", bizType)
                    .param("p13", bizId)
                    .build();
        }
        ZPManager manager = new ZPManager(this, url);
        manager.handler();
    }

    /**
     * 处理未登录时预览的数据
     */
    private void handleNoneLoginPreviewPositionData(Intent intent) {
        if (mUserLoginStatus == LOGIN_STATUS.LOGIN || intent == null) return;
        intent.removeExtra(Constants.DATA_STRING);
    }

    /**
     * 这个方法较为重要，它是整个系统的角色的分配方法，
     * 整个APP中，以此为角色分界点，所有的子activity、fragment都不应该存在大量的判断角色，
     * 都要从此入口引入不同的角色数据
     */
    private void refreshUserRole() {
        ROLE tempUserRole = UserManager.getUserRole();
        if (tempUserRole != mUserRole) {
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
            if (tempUserRole == ROLE.GEEK) {
                // ... 此处判断，证明用户已经切换为牛人身份，应释放BOSS的相关数据
                if (bFindFragment != null) {
                    transaction.remove(bFindFragment);
                    if (bFindFragment instanceof BaseFragment) {
                        ((BaseFragment) bFindFragment).destroy();
                    }
                    bFindFragment = null;
                }
                if (bossIncompleteInformationOnRecommend != null) {
                    transaction.remove(bossIncompleteInformationOnRecommend);
                    if (bossIncompleteInformationOnRecommend instanceof BaseFragment) {
                        ((BaseFragment) bossIncompleteInformationOnRecommend).destroy();
                    }
                    bossIncompleteInformationOnRecommend = null;
                }
                if (bossIncompleteInformationOnSearch != null) {
                    transaction.remove(bossIncompleteInformationOnSearch);
                    if (bossIncompleteInformationOnSearch instanceof BaseFragment) {
                        ((BaseFragment) bossIncompleteInformationOnSearch).destroy();
                    }
                    bossIncompleteInformationOnSearch = null;
                }
                if (bContactsFragment != null) {
                    transaction.remove(bContactsFragment);
                    bContactsFragment.destroy();
                    bContactsFragment = null;
                }
                if (bMyFragment != null) {
                    transaction.remove(bMyFragment);
                    BossMineModuleService service = Router.getService(BossMineModuleService.class, BossConst.KEY_BOSS_MODULE_SERVICE);
                    if (service != null) {
                        service.destroyBMyFragment(bMyFragment);
                    }
                    bMyFragment = null;
                }
                if (advancedSearchTabFragment != null) {
                    transaction.remove(advancedSearchTabFragment);
                    if (advancedSearchTabFragment instanceof BaseFragment) {
                        ((BaseFragment) advancedSearchTabFragment).destroy();
                    }
                    advancedSearchTabFragment = null;
                }
                mUserRole = ROLE.GEEK;
            } else if (tempUserRole == ROLE.BOSS) {
                // ... 此处判断，证明用户已经切换为BOSS身份，应释放牛人的相关数据
                if (gFindContainerFragment != null) {
                    transaction.remove(gFindContainerFragment);
                    gFindContainerFragment.destroy();
                    gFindContainerFragment = null;
                }
                if (gContactsFragment != null) {
                    transaction.remove(gContactsFragment);
                    gContactsFragment.destroy();
                    gContactsFragment = null;
                }
                if (gMyFragment != null) {
                    transaction.remove(gMyFragment);
                    GeekMineModuleService service = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
                    if (service != null) {
                        service.destroyGMyFragment(gMyFragment);
                    }
                    gMyFragment = null;
                }
                mUserRole = ROLE.BOSS;
            }

            // 无论BOSS还是牛人，切换身份即重置F2数据
            if (f2ContainerFragment != null) {
                transaction.remove(f2ContainerFragment);
                f2ContainerFragment.destroy();
                f2ContainerFragment = null;
            }

            llContentView.removeAllViews();
            transaction.commitAllowingStateLoss();
        }
    }

    /**
     * 当前显示聊天tab
     *
     * @return
     */
    public boolean isShowF2() {
        return currentSelectIndex == 2;
    }

    /**
     * 当前 显示是 聊天（0） or 互动（1）
     *
     * @return
     */
    public int getF2TabIndex() {
        if (UserManager.isGeekRole()) {
            if (gContactsFragment != null && gContactsFragment.isAdded()) {
                return gContactsFragment.getTabIndex();
            }
        }
        if (UserManager.isBossRole()) {
            if (bContactsFragment != null && bContactsFragment.isAdded()) {
                return bContactsFragment.getTabIndex();
            }
        }
        return 0;
    }

    private void onDoubleClickF1() {
        long currClickTime = System.currentTimeMillis();
        if (currClickTime - lastClickF1Time > CLICK_INTERVAL) { // 单击触发
            lastClickF1Time = currClickTime;
            App.get().getMainHandler().postDelayed(clickF1AgainAction, CLICK_INTERVAL);
        } else {
            // 双击忽视之前的单击
            App.get().getMainHandler().removeCallbacks(clickF1AgainAction);
        }
    }

    private void onClickF1Again() {
        if (UserManager.isGeekRole() && gFindContainerFragment != null) {
            gFindContainerFragment.onClickBottomTabAgain();
        } else if (UserManager.isBossRole()) {
            BossPageRouter.onDoubleClickBossF1FindList(bFindFragment);
        }
    }

    private void onDoubleClickF2() {
        long currClickTime = System.currentTimeMillis();
        if (currClickTime - lastClickF2Time > CLICK_INTERVAL) { // 单击触发
            lastClickF2Time = currClickTime;
            App.get().getMainHandler().postDelayed(clickF2AgainAction, CLICK_INTERVAL);
        } else {
            // 双击忽视之前的单击
            App.get().getMainHandler().removeCallbacks(clickF2AgainAction);
        }
    }

    private void onClickF2Again() {
        if (UserManager.isGeekRole() && f2ContainerFragment != null) {
            f2ContainerFragment.onClickBottomTabAgain();
        } else if (UserManager.isBossRole() && advancedSearchTabFragment != null) {
            AdvancedSearchRouter.onDoubleClickBossAdvancedSearchTab(advancedSearchTabFragment);
        }
    }

    private void onDoubleClickF3() {
        long currClickTime = System.currentTimeMillis();
        if (currClickTime - lastClickF3Time > CLICK_INTERVAL) { // 单击触发
            lastClickF3Time = currClickTime;
            App.get().getMainHandler().postDelayed(clickF3AgainAction, CLICK_INTERVAL);
        } else { // 双击触发
            App.get().getMainHandler().removeCallbacks(clickF3AgainAction);
            if (UserManager.getUserRole() == ROLE.GEEK && gContactsFragment != null) {
                gContactsFragment.onDoubleClick();
            } else if (UserManager.getUserRole() == ROLE.BOSS && bContactsFragment != null) {
                bContactsFragment.onDoubleClick();
            }
        }
    }

    private void onClickF3Again() {
        if (UserManager.getUserRole() == ROLE.GEEK && gContactsFragment != null) {
            gContactsFragment.onSingleClick();
        } else if (UserManager.getUserRole() == ROLE.BOSS && bContactsFragment != null) {
            bContactsFragment.onSingleClick();
        }
    }

    private void onDoubleClickF4() {
        long currClickTime = System.currentTimeMillis();
        if (currClickTime - lastClickF4Time > CLICK_INTERVAL) { // 单击触发
            lastClickF4Time = currClickTime;
            App.get().getMainHandler().postDelayed(clickF4AgainAction, CLICK_INTERVAL);
        } else {
            // 双击忽视之前的单击
            App.get().getMainHandler().removeCallbacks(clickF4AgainAction);
        }
    }

    private void onClickF4Again() {
        if (UserManager.getUserRole() == ROLE.GEEK && gMyFragment != null) {
            GeekMineModuleService service = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
            if (service != null) {
                service.onClickBottomTabAgain(gMyFragment);
            }
        } else if (UserManager.getUserRole() == ROLE.BOSS && bMyFragment != null) {
            BossMineModuleService service = Router.getService(BossMineModuleService.class, BossConst.KEY_BOSS_MODULE_SERVICE);
            if (service != null) {
                service.onClickBottomTabAgain(bMyFragment);
            }
        }
    }

    /**
     * 刷新F1的page guide
     */
    public void changeF1PageGuide() {
        if (UserManager.isBossRole()) {
            if (currentSelectIndex == 0 || currentSelectIndex == 1) {
                selectFragments(currentSelectIndex);
            }
        }
    }

    /**
     * 选择显示Fragment
     *
     * @param index 选择tab的下标
     */
    private void selectFragments(int index) {
        SpManager.get().user().edit().putInt(Constants.SP_MAIN_TAB_INDEX_SELECTION, index).apply();
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        hideFragments(transaction);
        boolean isAdd = false;
        Fragment F = null;
        if (mUserLoginStatus == LOGIN_STATUS.LOGIN) {
            ReceiverUtils.sendBroadcast(this, new Intent(GetConsts.ACTION_VISIBILITY_CHANGED).putExtra(GetConsts.KEY_IN_FOREGROUND, false));
            setScreenShot();
            // 正常加载Fragment
            if (mUserRole == ROLE.GEEK) {
                // 牛人加载判断
                switch (index) {
                    case 0:
                        if (gFindContainerFragment == null) {
                            gFindContainerFragment = GFindContainerFragment.newInstance(null);
                            isAdd = true;
                        }
                        F = gFindContainerFragment;
                        break;
                    case 1:
                        if (f2ContainerFragment == null) {
                            Bundle args = new Bundle();
                            args.putInt(F2ContainerFragment.KEY_FRAGMENT_USER, F2ContainerFragment.FragmentUser.FRAGMENT_USER_MAIN);
                            f2ContainerFragment = F2ContainerFragment.getInstance(args);
                            isAdd = true;
                        }
                        F = f2ContainerFragment;
                        ReceiverUtils.sendBroadcast(this, new Intent(GetConsts.ACTION_VISIBILITY_CHANGED).putExtra(GetConsts.KEY_IN_FOREGROUND, true));
                        //F2机会角标显示，点击后消失
                        hideF2TabChangeLabel();
                        break;
                    case 2:
                        if (gContactsFragment == null) {
                            gContactsFragment = GeekPageRouter.getContactFragment(BaseContactFragment.class);
                            isAdd = true;
                        }
                        F = gContactsFragment;
                        break;
                    case 3:
                        if (gMyFragment == null) {
                            GeekMineModuleService service = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
                            if (service != null) {
                                gMyFragment = service.getGMyFragmentInstance();
                            }
                            isAdd = true;
                        }
                        F = gMyFragment;
                        break;
                    default:
                        break;
                }
            } else if (mUserRole == ROLE.BOSS) {
                // BOSS加载判断
                switch (index) {
                    case 0:
                        UserBean user = UserManager.getLoginUser();
                        if (UserManager.isMoreInfoCompleteBoss(user)) {
                            if (bFindFragment == null) {
                                bFindFragment = BossPageRouter.getBossF1FindFragment(null);
                                isAdd = true;
                            }
                            F = bFindFragment;
                        } else {
                            if (bossIncompleteInformationOnRecommend == null) {
                                Bundle data = new Bundle();
                                data.putInt(Constants.DATA_INT, 0);
                                data.putString(Constants.DATA_STRING, "牛人");
                                bossIncompleteInformationOnRecommend = BossPageRouter.getBossIncompleteInformationFragment(data);
                                isAdd = true;
                            }
                            F = bossIncompleteInformationOnRecommend;
                        }
                        break;
                    case 1:
                        if (UserManager.isMoreInfoCompleteBossRegardlessUnpaidJobs(UserManager.getLoginUser())) {
                            if (advancedSearchTabFragment == null) {
                                advancedSearchTabFragment = AdvancedSearchRouter.getAdvancedSearchTabFragment();
                                isAdd = true;
                            }
                            F = advancedSearchTabFragment;
                            AdvancedSearchRouter.clearAdvSearchTip();
                        } else {
                            if (bossIncompleteInformationOnSearch == null) {
                                Bundle data = new Bundle();
                                data.putInt(Constants.DATA_INT, 1);
                                String title = UserGrayFeatureManager.getInstance().getF1AdvanceSearchEntryDesc();
                                if (TextUtils.isEmpty(title)) {
                                    title = "人才库";
                                }
                                data.putString(Constants.DATA_STRING, title);
                                bossIncompleteInformationOnSearch = BossPageRouter.getBossIncompleteInformationFragment(data);
                                isAdd = true;
                            }
                            F = bossIncompleteInformationOnSearch;
                        }
                        break;
                    case 2:
                        if (bContactsFragment == null) {
                            bContactsFragment = BossPageRouter.getContactFragment(BaseContactFragment.class);
                            isAdd = true;
                        }
                        F = bContactsFragment;
                        break;
                    case 3:
                        if (bMyFragment == null) {
                            BossMineModuleService service = Router.getService(BossMineModuleService.class, BossConst.KEY_BOSS_MODULE_SERVICE);
                            if (service != null) {
                                bMyFragment = service.getBMyFragmentInstance();
                            }
                            isAdd = true;
                        }
                        F = bMyFragment;
                        break;
                    default:
                        break;
                }
            }
        } else {
            if (gFindContainerFragment != null) {
                transaction.remove(gFindContainerFragment);
                gFindContainerFragment.destroy();
                gFindContainerFragment = null;
            }
            if (f2ContainerFragment != null) {
                transaction.remove(f2ContainerFragment);
                f2ContainerFragment.destroy();
                f2ContainerFragment = null;
            }
            if (gContactsFragment != null) {
                transaction.remove(gContactsFragment);
                gContactsFragment.destroy();
                gContactsFragment = null;
            }
            if (gMyFragment != null) {
                transaction.remove(gMyFragment);
                GeekMineModuleService service = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
                if (service != null) {
                    service.destroyGMyFragment(gMyFragment);
                }
                gMyFragment = null;
            }

            if (bFindFragment != null) {
                transaction.remove(bFindFragment);
                if (bFindFragment instanceof BaseFragment) {
                    ((BaseFragment) bFindFragment).destroy();
                }
                bFindFragment = null;
            }
            if (bossIncompleteInformationOnRecommend != null) {
                transaction.remove(bossIncompleteInformationOnRecommend);
                if (bossIncompleteInformationOnRecommend instanceof BaseFragment) {
                    ((BaseFragment) bossIncompleteInformationOnRecommend).destroy();
                }
                bossIncompleteInformationOnRecommend = null;
            }
            if (bossIncompleteInformationOnSearch != null) {
                transaction.remove(bossIncompleteInformationOnSearch);
                if (bossIncompleteInformationOnSearch instanceof BaseFragment) {
                    ((BaseFragment) bossIncompleteInformationOnSearch).destroy();
                }
                bossIncompleteInformationOnSearch = null;
            }
            if (bContactsFragment != null) {
                transaction.remove(bContactsFragment);
                bContactsFragment.destroy();
                bContactsFragment = null;
            }
            if (bMyFragment != null) {
                transaction.remove(bMyFragment);
                BossMineModuleService service = Router.getService(BossMineModuleService.class, BossConst.KEY_BOSS_MODULE_SERVICE);
                if (service != null) {
                    service.destroyBMyFragment(bMyFragment);
                }
                bMyFragment = null;
            }
            if (advancedSearchTabFragment != null) {
                transaction.remove(advancedSearchTabFragment);
                if (advancedSearchTabFragment instanceof BaseFragment) {
                    ((BaseFragment) advancedSearchTabFragment).destroy();
                }
                advancedSearchTabFragment = null;
            }
        }
        if (F != null) {
            if (isAdd) {
                transaction.add(R.id.ll_main_view, F);
            } else {
                transaction.show(F);
            }
        }
        transaction.commitAllowingStateLoss();
    }

    /**
     * 隐藏掉所有Fragment
     *
     * @param transaction 加载fragment的FragmentTransaction
     */
    private void hideFragments(FragmentTransaction transaction) {
        if (gFindContainerFragment != null) {
            transaction.hide(gFindContainerFragment);
        }
        if (f2ContainerFragment != null) {
            transaction.hide(f2ContainerFragment);
        }
        if (gContactsFragment != null) {
            transaction.hide(gContactsFragment);
        }
        if (gMyFragment != null) {
            transaction.hide(gMyFragment);
        }
        if (bFindFragment != null) {
            transaction.hide(bFindFragment);
        }
        if (bossIncompleteInformationOnRecommend != null) {
            transaction.hide(bossIncompleteInformationOnRecommend);
        }
        if (bossIncompleteInformationOnSearch != null) {
            transaction.hide(bossIncompleteInformationOnSearch);
        }
        if (bContactsFragment != null) {
            transaction.hide(bContactsFragment);
        }
        if (bMyFragment != null) {
            transaction.hide(bMyFragment);
        }
        if (advancedSearchTabFragment != null) {
            transaction.hide(advancedSearchTabFragment);
        }
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        // 将此方法复写，然后删除掉super，为保证app崩溃不会重启动此页而按钮无法点选
    }

    @Override
    protected void onPause() {
        super.onPause();
        MainActivity.mMainActivityIsTopping = false;
    }

    @Override
    protected void onStop() {
        super.onStop();
        //退出APP或者按了返回键都需要刷新一下桌面Icon总数
        DeskTopNoneReadManager.refreshDeskNoneRead();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mModifySelectIndexReceiver != null) {
            unregisterReceiver(mModifySelectIndexReceiver);
        }
        if (mAdvertBroadcast != null) {
            unregisterReceiver(mAdvertBroadcast);
        }
        if (mGuideDialogReceiver != null) {
            unregisterReceiver(mGuideDialogReceiver);
        }
        if (closeSafeFrameworkReceiver != null) {
            unregisterReceiver(closeSafeFrameworkReceiver);
        }
        if (mCommonReceiver != null) {
            ReceiverUtils.unregister(this, mCommonReceiver);
        }
        if (mAppUpgradeReceiver != null) {
            ReceiverUtils.unregister(this, mAppUpgradeReceiver);
        }
        //清空常用语
        CommonWordManager.getInstance().clear();
        ReceiveCallingManager.getInstance().release();
        BitmapLruCacheMemoryReuse.getInstance().clearLruCache();
        if (bossDialogPopupManager != null) {
            bossDialogPopupManager.destroy();
        }
        GeekF1DialogManager.getInstance().clearTask();
        GeekF2DialogManager.getInstance().clearTask();
        LiveBus.with(ChannelConstants.APP_LOGIN_DEVICE_CONFIRM, LoginDeviceConfirmResponse.class).removeObserver(mObserver);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mUserLoginStatus == LOGIN_STATUS.LOGIN) {
                if (UserManager.isBossRole() && currentSelectIndex == 1) {
                    boolean reset = AdvancedSearchRouter.resetAdvancedSearchSubPage(advancedSearchTabFragment);
                    if (reset) {
                        return true;
                    }
                }

                // 执行退出操作
                long timer = System.currentTimeMillis();
                if (timer - lastBackTimer > 2000) {
                    ToastUtils.showText("再按一次退出程序");
                    lastBackTimer = timer;
                    return true;
                }
                //推出程序 停止播放
                FloatWindowManager.getInstance().resetWindow();
                finish();
                App.get().exit();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.cl_tab_1) {
            if (currentSelectIndex == 0) {
                onDoubleClickF1();
            } else {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_F1_TAB_CLICK).build();
            }
            currentSelectIndex = 0;
        } else if (i == R.id.cl_tab_2) {
            if (mUserLoginStatus != LOGIN_STATUS.LOGIN) {
                new EntryAppDialog(this, 0, false).show();
                return;
            }

            if (currentSelectIndex == 1) {
                onDoubleClickF2();
            } else {
                // 添加发现灰度策略
                if (mUserRole == ROLE.BOSS) {
                    String tipDesc = SpManager.get().user().getString(SearchConst.SEARCH_TIP_DESC, "");
                    int tipType = SpManager.get().user().getInt(SearchConst.SEARCH_TIP_TYPE, 0);
                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_SUPER_SEARCH)
                            .param("p", 1)
                            .param("p3", TextUtils.isEmpty(tipDesc) ? "" : tipDesc)
                            .param("p4", tipType)
                            .build();
                } else {
                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_GEEK_F4_TAB)
                            .param("p", String.valueOf(GetHomeRedDotManager.getInstance().getRedDotType())) // 是否有引导，包括红点/气泡/无（0无，1红点，2气泡）
                            .param("p2", GetHomeRedDotManager.getInstance().getBubbleText()) // 如果是气泡，气泡文案
                            .build();
                    GetHomeRedDotManager.getInstance().clearDotOrBubble();
                    refreshGetTabDot();
                }
            }
            currentSelectIndex = 1;
        } else if (i == R.id.cl_tab_3) {
            if (mUserLoginStatus != LOGIN_STATUS.LOGIN) {
                new EntryAppDialog(this, 0, false).show();
                return;
            }

            if (currentSelectIndex == 2) {
                onDoubleClickF3();
            } else {
                f2Analytics();
            }
            currentSelectIndex = 2;
        } else if (i == R.id.cl_tab_4) {
            if (mUserLoginStatus != LOGIN_STATUS.LOGIN) {
                new EntryAppDialog(this, 0, false).show();
                return;
            }

            if (currentSelectIndex == 3) {
                onDoubleClickF4();
            }else {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_F3_TAB_CLICK).build();
            }
            PreloadWebViewTool.getInstance().preload();
            currentSelectIndex = 3;
        }
        selectFragments(currentSelectIndex);
        mainTabLayoutPresenter.selectTab(currentSelectIndex);

        sendMainTabChangeEvent();

        onSelectedFragments(currentSelectIndex);
    }

    private void onSelectedFragments(int currentSelectIndex) {
        if (UserManager.isGeekRole() && gMyFragment != null && currentSelectIndex == 3) {
            GeekMineModuleService service = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
            if (service != null) {
                service.onClickMine(gMyFragment);
            }
        }
    }

    private void hideF2TabChangeLabel() {
        //正在显示
        if (isF2ChangeLabelShowing) {
            SpManager.get().user().edit().putBoolean(Constants.SP_F2_CHANGE_LABEL_CLICK, true).apply();
            refreshAdvancedSearchTabFunctionalTip(0, "");
            isF2ChangeLabelShowing = false;
        }
    }

    /**
     * F2 埋点
     */
    private void f2Analytics() {
        //当前关注联系人数量
        final int topCount = F2ContactHelper.getInstance().getTopCount();
        int topContactNoneRead = F2ContactHelper.getInstance().getTopNoneReadCount();
        //通知未读数
        final int noticeNoneRead = ContactManager.getInstance().getNoticeNoneReadCount();
        //感兴趣未读数
        ServerInteractBean interested = InteractedChatManager.getInstance().getInterested();
        final int interestedNoneRead = interested != null ? interested.noneReadCount : 0;
        //查看未读数
        ServerInteractBean viewed = InteractedChatManager.getInstance().getViewedMe();
        final int viewedNoneRead = viewed != null ? viewed.noneReadCount : 0;

        if (mUserRole == ROLE.BOSS) {
            AppThreadFactory.POOL.submit(new Runnable() {
                @Override
                public void run() {

                    List<ContactBean> singleContact = F2ContactHelper.getInstance().getSingleContact(false);
                    int contactCount = LList.getCount(singleContact);
                    List<GroupInfoBean> groupInfoList = GroupManager.getInstance().getGroupInfoList();
                    int NoneReadgroup = 0;
                    for (GroupInfoBean groupInfoBean : groupInfoList) {
                        if (groupInfoBean.noneReadCount > 0) {
                            NoneReadgroup++;
                        }
                    }
                    int groupCount = groupInfoList.size();


                    int singleContactNoneRead = 0;
                    for (ContactBean contactBean : singleContact) {
                        if (contactBean.noneReadCount > 0) {
                            singleContactNoneRead++;
                        }
                    }

                    AnalyticsFactory.create()
                            .action("bf2-tab")
                            .param("p", String.valueOf(contactCount + groupCount))
                            .param("p2", String.valueOf(singleContactNoneRead + NoneReadgroup))
                            .param("p3", String.valueOf(topCount))//当前关注联系人数量 (5.52)
                            .param("p4", String.valueOf(topContactNoneRead))//当前关注未读联系人数量 (5.52)
                            .param("p5", String.valueOf(noticeNoneRead))
                            .param("p6", String.valueOf(interestedNoneRead))
                            .param("p7", String.valueOf(viewedNoneRead))
                            .param("p8", String.valueOf(groupCount))
                            .param("p9", String.valueOf(NoneReadgroup))
                            .build();

                    //抽屉曝光
                    ContactDrawExpose.doMsgDrawExpose();
                }
            });
        } else {
            List<ContactBean> singleContact = F2ContactHelper.getInstance().getSingleContact(false);
            int contactCount = LList.getCount(singleContact);
            int singleContactNoneRead = 0;

            StringBuilder exposeBg = new StringBuilder();

            for (ContactBean contactBean : singleContact) {
                if (contactBean.noneReadCount > 0) {
                    singleContactNoneRead++;

                    if (!LText.empty(contactBean.starTypes)) {
                        exposeBg.append(contactBean.friendId);
                        exposeBg.append(",");
                    }
                }

                boolean hasTop300 = (contactBean.iconFlag & 1) != 0 && LText.empty(contactBean.userFromTitle);
                if (hasTop300) {
                    AnalyticsFactory.create().action("extension-hunter-OfferShow-ClabelExpo")
                            .param("p", "4")
                            .param("p2", contactBean.friendId)
                            .param("p3", contactBean.jobId)
                            .param("p4", "1")
                            .build();
                }
            }


            List<GroupInfoBean> groupInfoList = GroupManager.getInstance().getGroupInfoList();
            int NoneReadgroup = 0;
            for (GroupInfoBean groupInfoBean : groupInfoList) {
                if (groupInfoBean.noneReadCount > 0) {
                    NoneReadgroup++;
                }
            }
            int groupCount = groupInfoList.size();

            AnalyticsFactory.create().action(AnalyticsAction.ACTION_GEEK_F2_TAB)
                    .param("p", String.valueOf(contactCount + groupCount))
                    .param("p2", String.valueOf(singleContactNoneRead + NoneReadgroup))
                    .param("p3", String.valueOf(topCount))
                    .param("p4", String.valueOf(topContactNoneRead))
                    .param("p5", String.valueOf(noticeNoneRead))
                    .param("p6", String.valueOf(interestedNoneRead))
                    .param("p7", String.valueOf(viewedNoneRead))
                    .param("p8", String.valueOf(groupCount))
                    .param("p9", String.valueOf(NoneReadgroup))
                    .buildSync();
            //抽屉曝光
            ContactDrawExpose.doMsgDrawExpose();

        }
    }

    private void sendMainTabChangeEvent() {
        Intent intent = new Intent(Constants.ACTION_ON_MAIN_TAB_CHANGE);
        intent.putExtra(Constants.KEY_CURRENT_TAB_INDEX, currentSelectIndex);
        ReceiverUtils.sendBroadcast(this, intent);
    }

    /**
     * 【我的】tab下的未读消息，以及红点提示
     */
    public void refreshF3NoneRead() {
        mainTabLayoutPresenter.refreshF3NoneRead();
    }

    /**
     * 【公司】tab下的未读消息，红点提示
     */
    private void refreshGetTabDot() {
        mainTabLayoutPresenter.refreshGetTabDot();
    }
//

    /**
     * 【搜索】tab下的订阅红点提示
     */
    private void refreshAdvancedSearchTabSubscribedTip() {
        boolean showRedDot = mainTabLayoutPresenter.refreshAdvancedSearchTabSubscribedTip();
        AdvancedSearchRouter.refreshSubscribeRedDot(advancedSearchTabFragment, showRedDot);
    }

    /**
     * 【有了】修改底部tab背景色
     */
    private void refreshTabLayoutBackground(boolean isDark) {
        mainTabLayoutPresenter.refreshTabLayoutBackground(isDark);
    }
//

    /**
     * 【搜索】tab下的功能活动气泡提示
     *
     * @param tipType
     * @param tipDesc
     */
    private void refreshAdvancedSearchTabFunctionalTip(int tipType, String tipDesc) {
        mainTabLayoutPresenter.refreshAdvancedSearchTabFunctionalTip(tipType, tipDesc);
    }
//

    /**
     * 联系人tab下的未读消息，以及红点提示
     * <b>联系人<b/>
     * <b>看过我<b/>
     * <b>感兴趣<b/>
     * <b>通知<b/>
     */
    private void refreshF2NoneRead() {
        mainTabLayoutPresenter.refreshF2NoneRead();
    }

    /**
     * APP更新广播接收器
     */
    private final BroadcastReceiver mAppUpgradeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (mVersionUpgradeInit) {
                mVersionUpgradeInit = false;
            }
        }
    };

    /**
     * 接收信息未完整状态跳转到f3进行信息填写
     */
    private final BroadcastReceiver mModifySelectIndexReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (!UserManager.isCurrentLoginStatus()) {
                UserManager.setAccountInvalid(context, false);
                return;
            }
            /* 设置 <b>f1<b/> <b>f2<b/><b>f3<b/> <b>f4<b/> 下标 **/
            int mainTabIndex = intent.getIntExtra(Constants.DATA_INT, 0);
            if (mainTabIndex < 0 || mainTabIndex > 3) return;
            // 跳转到f1并通过jobId，自动选择到相应的选项卡上
            // 通过sortType，选择到相应的筛选类型上
            if (mainTabIndex == 0) {
                int sortType = intent.getIntExtra(Constants.DATA_F1_SUB_TAB, 0);
                String extParams = intent.getStringExtra(Constants.DATA_F1_EXT_PARAMS);
                if (UserManager.isBossRole()) {
                    BossPageRouter.loadF1AccordingToConditions(bFindFragment, intent);
                } else {
                    long expectId = intent.getLongExtra(Constants.DATA_JOB_EXPECT_ID, 0);
                    int sourceFrom = intent.getIntExtra(Constants.DATA_F1_NEAR_SOURCE, 0);
                    if (gFindContainerFragment != null && gFindContainerFragment.isAdded()) {
                        gFindContainerFragment.selectSortType(sortType, expectId, sourceFrom,extParams);
                    }
                }
            }

            /* 模拟选择 【F1】  【F2】 【F3】 【F4】  */
            if (currentSelectIndex != mainTabIndex) {
                isSwithTabProgrammatically = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
//                onClick(clTabs[mainTabIndex]);
                currentSelectIndex = mainTabIndex;
                mainTabLayoutPresenter.getTab(mainTabIndex).performClick();
            }

            //选择 聊天 和 互动 tab
            int level2Tab = intent.getIntExtra(Constants.DATA_INT_2, -1);

            if (level2Tab != -1) {
                //设置交互<b>看过我<b/> <b>感兴趣<b/> <b>新牛人<b/>下标
                int level3Tab = intent.getIntExtra(Constants.DATA_INT_3, -1);

                //从push进来后,fragment还没有初始化，所有等待fragment加载完成后 在刷新广播

                App.get().getMainHandler().postDelayed(() -> {
                    if (isFinishing()) return;

                    if (UserManager.isBossRole()) {
                        if (bContactsFragment == null) {
                            tabIndex = level2Tab;
                            subTabIndex = level3Tab;
                        } else {
                            bContactsFragment.setPagerIndex(level2Tab, level3Tab, true);
                        }
                    } else {
                        if (gContactsFragment == null) {
                            tabIndex = level2Tab;
                            subTabIndex = level3Tab;
                        } else {
                            gContactsFragment.setPagerIndex(level2Tab, level3Tab, true);
                        }
                    }

                    if (level2Tab == 0 && level3Tab >= 0) { // 跳转聊天并选择 分类
                        Intent level3TabIntent = new Intent(Constants.ACTION_CHAT_CONTACT_GROUP_INDEX);
                        level3TabIntent.putExtra(Constants.DATA_INT, level3Tab);
                        ReceiverUtils.sendBroadcast(context, level3TabIntent);
                    }
                }, 500);
            }
        }
    };

    // 关闭安全框架的广播回调
    private final BroadcastReceiver closeSafeFrameworkReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_COLOSE_SAFE_WINDOWS)) {
                if (UserManager.isBossRole()) {
                    AdvancedSearchRouter.getAdvSearchTip(0);
                }
                TLog.print("BrandInvite", "closeSafeFramework");
            }
        }
    };

    /**
     * 接收独占式广告弹出的广播消息
     */
    private final BroadcastReceiver mAdvertBroadcast = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (LText.equal(Constants.RECEIVER_ADVERT_SHOW_ACTION, intent.getAction()) && mMainActivityIsTopping) {
                AdvertBean bean = (AdvertBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
                if (bean == null) return;
                if (bean.type == 0) {
                    showAdvertDialog(bean);
                } else if (bean.type == 1) {
                    showAdvert2Dialog(bean);
                }
            }
        }
    };

    // 新手引导dialog的receiver
    private final BroadcastReceiver mGuideDialogReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (!Constants.RECEIVER_GUIDE_DIALOG_SHOW_ACTION.equals(action)) return;
            AllDialogBean bean = (AllDialogBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
            if (bean == null) return;
            new GuideDialogHelper.Builder(MainActivity.this).data(bean).show();
        }
    };

    private void showAdvertDialog(final AdvertBean bean) {
        if (!UserManager.isCurrentLoginStatus()) return;
        if (mAdvertDialog != null && mAdvertDialog.isShowing()) return;
        if (bean == null) return;
        bean.isShowed = true;
        App.get().db().delete(bean);
        LayoutInflater inflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        @SuppressLint("InflateParams") View view = inflater.inflate(R.layout.view_advert_dialog, null);
        SimpleDraweeView sdv = view.findViewById(R.id.iv_photo);
        int width = App.get().getDisplayWidth() - Scale.dip2px(this, 40);
        int height = width / 43 * 24;
        sdv.setLayoutParams(new RelativeLayout.LayoutParams(width, height));
        float round = Scale.dip2px(this, 10);
        RoundingParams roundingParams = RoundingParams.fromCornersRadii(round, round, 0, 0);
        sdv.getHierarchy().setRoundingParams(roundingParams);
        ImageView ivCancel = view.findViewById(R.id.tv_cancel);
        MTextView desc = view.findViewById(R.id.tv_desc);
        MTextView tvButton = view.findViewById(R.id.tv_button);
        ViewCommon.setAvatar(sdv, 0, bean.photoUrl);
        mAdvertDialog = new Dialog(this, R.style.AdvertDialogStyle);
        mAdvertDialog.addContentView(view,
                new ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
        mAdvertDialog.setCancelable(false);
        desc.setText(bean.descText);
        if (LText.empty(bean.buttonText)) {
            bean.buttonText = "分享";
        }
        tvButton.setText(bean.buttonText);
        tvButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdvertDialog.dismiss();
                advertGoPoint("ada_go", bean.extension);
                new Thread(new ShowAdvertDialogAndXMSRunnable(bean)).start();
                new ZPManager(MainActivity.this, bean.whereUrl).handler();
            }
        });
        ivCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdvertDialog.dismiss();
            }
        });
        if (!isFinishing()) {
            mAdvertDialog.show();
            advertShowPoint("ada_show", bean.extension);
        }
        Window window = mAdvertDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = width;
            window.setAttributes(params);
        }
    }

    /**
     * 独占式广告弹出，未读数量减1
     */
    class ShowAdvertDialogAndXMSRunnable implements Runnable {

        private final AdvertBean advertBean;

        ShowAdvertDialogAndXMSRunnable(AdvertBean bean) {
            advertBean = bean;
        }

        @Override
        public void run() {
            if (advertBean == null || advertBean.id <= 0) return;
            ChatBean chat = MessageDaoFactory.getMessageDao().queryChatByMsgId(advertBean.id);
            if (chat == null) return;
            long friendId = -1;
            if (chat.fromUserId != UserManager.getUID() // 消息发送人不是我
                    && chat.toUserId == UserManager.getUID()    // 消息接收人是我
                    && chat.myRole == UserManager.getUserRole().get()) {    // 消息的角色是我当前的角色
                friendId = chat.fromUserId;
            }
            if (friendId == -1) return;
            ContactBean contact = ContactManager.getInstance()
                    .queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
            if (contact == null) return;
            if (contact.noneReadCount > 0)
                contact.noneReadCount--;
            ContactManager.getInstance().initContactData(contact);
            ContactManager.getInstance().refreshContacts();
        }

    }

    /**
     * 显示第二种首页弹出广告
     */
    private void showAdvert2Dialog(final AdvertBean bean) {
        if (!UserManager.isCurrentLoginStatus()) return;
        if (mAdvertDialog != null && mAdvertDialog.isShowing()) return;
        if (bean == null) return;
        bean.isShowed = true;
        App.get().db().delete(bean);
        LayoutInflater inflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        @SuppressLint("InflateParams") View view = inflater.inflate(R.layout.view_advert2_dialog, null);
        SimpleDraweeView sdv = view.findViewById(R.id.iv_photo);
        int width = App.get().getDisplayWidth() - Scale.dip2px(this, 40);
        int height = width / 43 * 24;
        sdv.setLayoutParams(new RelativeLayout.LayoutParams(width, height));
        float round = Scale.dip2px(this, 10);
        RoundingParams roundingParams = RoundingParams.fromCornersRadii(round, round, 0, 0);
        sdv.getHierarchy().setRoundingParams(roundingParams);
        ImageView ivCancel = view.findViewById(R.id.tv_cancel);
        MTextView desc = view.findViewById(R.id.tv_desc);
        MTextView tvButton = view.findViewById(R.id.tv_button);
        MTextView tvButtonCancel = view.findViewById(R.id.tv_button_cancel);
        ViewCommon.setAvatar(sdv, 0, bean.photoUrl);
        mAdvertDialog = new Dialog(this, R.style.AdvertDialogStyle);
        mAdvertDialog.addContentView(view,
                new ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
        mAdvertDialog.setCancelable(false);

        desc.setText(bean.descText);

        tvButton.setText(bean.buttonText);
        tvButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdvertDialog.dismiss();
                advertGoPoint("shgo", bean.extension);
                String url = bean.whereUrl;
                new ZPManager(MainActivity.this, url).handler();
            }
        });

        tvButtonCancel.setText(bean.buttonCancelText);
        tvButtonCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdvertDialog.dismiss();
            }
        });

        ivCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdvertDialog.dismiss();
            }
        });

        //控制btn是否可见
        tvButton.setVisibility(LText.empty(bean.buttonText) ? View.GONE : View.VISIBLE);
        tvButtonCancel.setVisibility(LText.empty(bean.buttonCancelText) ? View.GONE : View.VISIBLE);


        if (!isFinishing()) {
            mAdvertDialog.show();
            advertShowPoint("adb_show", bean.extension);
        }
        Window window = mAdvertDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = width;
            window.setAttributes(params);
        }
    }

    private void advertShowPoint(String action, String info) {
        if (LText.empty(info)) return;
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(info);
        } catch (JSONException ignored) {
        }
        if (jsonObject == null) return;
        final String p2 = jsonObject.optString("p2");
        final String p3 = jsonObject.optString("p3");
        final String p4 = jsonObject.optString("p4");
        AnalyticsFactory.create().action(action)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .build();
    }

    private void advertGoPoint(String action, String info) {
        if (LText.empty(info)) return;
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(info);
        } catch (JSONException ignored) {
        }
        if (jsonObject == null) return;
        final String p2 = jsonObject.optString("p2");
        final String p3 = jsonObject.optString("p3");
        final String p4 = jsonObject.optString("p4");
        AnalyticsFactory.create().action(action)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .build();
    }

//    private boolean hasAuthorized = false; // 是否已经授权过，不论同意或者拒绝

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
//        if (ev.getAction() == MotionEvent.ACTION_UP) {
//            hasAuthorized = SpManager.get().global().getBoolean(AppLoginAuthorizationUtils.SP_KEY_PERMISSION_AUTHORIZED, hasAuthorized);
//            if (!hasAuthorized) {
//                AppLoginAuthorizationUtils2 authorizationUtils = new AppLoginAuthorizationUtils2();
//                authorizationUtils.applyForPermissionAuthorize(this);
//                hasAuthorized = true;
//                return false;
//            }
//        }
//        if (hasAuthorized) {
        if (currentSelectIndex == 3) {
            if (bMyFragment != null && !bMyFragment.isHidden()) {
                BossMineModuleService service = Router.getService(BossMineModuleService.class, BossConst.KEY_BOSS_MODULE_SERVICE);
                if (service != null) {
                    service.dispatchTouchEventOfBMyFragment(bMyFragment, ev);
                }
            }
        }
//        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 公共的广播接收器
     */
    private final BroadcastReceiver mCommonReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (Constants.RECEIVER_ZP_PROTOCOL_ACTION.equals(action)) {
                // 执行直聘协议的广播
                String url = intent.getStringExtra(Constants.DATA_ZP_URL);
                if (!TextUtils.isEmpty(url)) {
                    new ZPManager(MainActivity.this, url).handler();
                }
            } else if (Constants.RECEIVER_NOTIFY_UPDATE_DIALOG_DATA_ACTION.equals(action)) {
                App.get().getMainHandler().post(MainActivity.this::initDialogRequest);
            } else if (TextUtils.equals(Constants.RECEIVER_ADVANCED_SEARCH_TAB_SUBSCRIBED_TIP, action)) {
                refreshAdvancedSearchTabSubscribedTip();
            } else if (TextUtils.equals(Constants.RECEIVER_ADVANCED_SEARCH_TAB_FUNCTIONAL_TIP, action)) {
                String tipDesc = intent.getStringExtra(SearchConst.SEARCH_TIP_DESC);
                int tipType = SpManager.get().user().getInt(SearchConst.SEARCH_TIP_TYPE, 0);
                refreshAdvancedSearchTabFunctionalTip(tipType, tipDesc);
            } else if (TextUtils.equals(Constants.RECEIVER_REFRESH_YOU_LE_ACTION, action)) {
                if (currentSelectIndex == 1) return; // 当前处于『有了』tab，不刷新红点、气泡

                // 刷新『有了』tab 红点、气泡
                int redDotType = intent.getIntExtra(GetConsts.KEY_RED_DOT_TYPE, 0);
                String bubbleText = intent.getStringExtra(GetConsts.KEY_BUBBLE_TEXT);
                GetHomeRedDotManager.getInstance().reloadStatus(redDotType, bubbleText);
                refreshGetTabDot();
            } else if (TextUtils.equals(Constants.RECEIVER_STUDENT_ROLE_SWITCH, action)) {
                try {
                    if (f2ContainerFragment != null) {
                        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
                        transaction.remove(f2ContainerFragment);
                        f2ContainerFragment.destroy();
                        f2ContainerFragment = null;

                        transaction.commitAllowingStateLoss();
                    }
                } catch (Exception ignored) {

                }
            } else if (TextUtils.equals(Constants.RECEIVER_YOU_LE_SHOW_VIDEO_ACTION, action)) {
                boolean isDark = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
                refreshTabLayoutBackground(isDark);
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == MainUserAgreementActivity.AGREEMENT_RESULT_CODE
                && requestCode == MainUserAgreementActivity.AGREEMENT_REQUEST_CODE) { // 从用户协议返回时再处理首页弹框的逻辑
            initDialogRequest();
        }
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case BusinessPageRouter.IReqCode.BLOCK_REQUEST:
                case BusinessPageRouter.IReqCode.REQ_JOB_DELAY:
                    if (data != null) {
                        // 道具或者阻断购买成功时，走下面的逻辑
                        Object obj = data.getSerializableExtra(Constants.DATA_ENTITY);
                        if (obj instanceof PayResult) {
                            // 7.09 为了关闭f1和f2列表的延长职位招聘卡片，也是拼了命了
                            if (JobDelayHelper.delayJobId > 0) {
                                JobDelayHelper.closeCardBroadcast();
                                PayResult payResult = (PayResult) obj;
                                DialogUtils d = new DialogUtils.Builder(this)
                                        .setSingleButton()
                                        .setTitle(payResult.title)
                                        .setDesc(payResult.desc)
                                        .setPositiveAction(R.string.string_confirm)
                                        .build();
                                d.show();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        if (gFindContainerFragment != null) {
            gFindContainerFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (bFindFragment != null) {
            bFindFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (gContactsFragment != null) {
            gContactsFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (bContactsFragment != null) {
            bContactsFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (advancedSearchTabFragment != null) {
            advancedSearchTabFragment.onActivityResult(requestCode, resultCode, data);
        }

        if (f2ContainerFragment != null) {
            f2ContainerFragment.onActivityResult(requestCode, resultCode, data);
        }

    }

    public boolean isBFindFragmentVisibleToUser() {
        return bFindFragment != null && bFindFragment.isAdded() && bFindFragment.isResumed() && bFindFragment.isVisible();
    }

    @Override
    protected int getNavigationBarColor() {
        return ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F);
    }

    /**
     * 切换搜索tab上的职位
     */
    public void changeSearchTabCurrSelectJob() {
        AdvancedSearchRouter.changeCurrSelectJob(advancedSearchTabFragment);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    private void handleLazyTask() {
        IdleHandler idleHandler = new IdleHandler();
        idleHandler.addIdleHandler(App.get().getMainHandler(), () -> {
            // 1217.40 协助认证应用内弹窗
            ChatBean chatBean = BossSpUtil.getAssistConfirmMsg();
            if (chatBean != null) {
                ChatUtils.handleAssistConfirmMsg(chatBean);
                SpManager.get().user().edit().remove(BossSpUtil.KEY_BOSS_ASSIST_CONFIRM).apply();
            }

            return false;
        });
    }
}