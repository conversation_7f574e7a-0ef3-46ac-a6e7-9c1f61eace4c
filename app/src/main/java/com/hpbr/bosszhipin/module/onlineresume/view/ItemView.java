package com.hpbr.bosszhipin.module.onlineresume.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.onlineresume.util.ResumeAnalysisUtil;
import com.hpbr.bosszhipin.module.onlineresume.util.ResumeSpUtil;
import com.hpbr.bosszhipin.module_geek_export.OnlineResumeConstants;
import com.hpbr.bosszhipin.views.BubbleLayout;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;

import net.bosszhipin.api.bean.GarbageResumeBean;
import net.bosszhipin.api.bean.ServerResumeFieldsItemDetailBean;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;
import zpui.lib.ui.utils.ZPUIDrawableHelper;

public class ItemView extends FrameLayout implements View.OnClickListener {
    private Context mContext;
    private MTextView tvTitle;
    private MTextView tvContent;
    private MTextView secTitle;
    private ImageView ivTitleRedDot;
    private ImageView ivRedDot;
    private MTextView tvSubContent;
    private View divider;
    private MTextView tvTipBelow;
    private ImageView ivTipBelow;
    private boolean withBackground;

    /*错误提醒*/
    private ZPUIConstraintLayout clErrorSection;
    private MTextView tvErrorText;
    private ImageView ivTipIcon;
    private BubbleLayout bl_diagnose_tip;
    private OnItemViewActionClickListener listener;

    @Deprecated
    private ItemCallback callback;

    @Deprecated
    private String fieldCode;//例如, 工作经历的工作内容code, GarbageResumeBean.CODE_WORK_CONTENT = "24";

    public ItemView(Context context) {
        this(context, null);
    }

    public ItemView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        mContext = context;
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.resume_item_view);
        String title = typedArray.getString(R.styleable.resume_item_view_title);
        int titleColor = typedArray.getColor(R.styleable.resume_item_view_title_color, getResources().getColor(R.color.text_c6));
        String hint = typedArray.getString(R.styleable.resume_item_view_hint_content);
        boolean withDivider = typedArray.getBoolean(R.styleable.resume_item_view_with_divider, false);
        int contentMaxLines = typedArray.getInt(R.styleable.resume_item_view_content_max_lines, 1);
        boolean withRedDot = typedArray.getBoolean(R.styleable.resume_item_view_with_red_dot, false);
        withBackground = typedArray.getBoolean(R.styleable.resume_item_view_with_background, false);

        typedArray.recycle();


        View view = LayoutInflater.from(context).inflate(R.layout.item_filling_view, this);
        tvContent = view.findViewById(R.id.content);
        ivTitleRedDot = view.findViewById(R.id.iv_title_dot);
        ivRedDot = view.findViewById(R.id.iv_red_dot);
        tvSubContent = view.findViewById(R.id.sub_content);
        tvTitle = view.findViewById(R.id.title);
        divider = view.findViewById(R.id.divider);
        clErrorSection = view.findViewById(R.id.cl_error_section);
        tvErrorText = view.findViewById(R.id.tv_error_text);
        ivTipIcon = view.findViewById(R.id.iv_tip_icon);
        bl_diagnose_tip = view.findViewById(R.id.bl_diagnose_tip);
        secTitle = view.findViewById(R.id.second_title);
        tvTipBelow = view.findViewById(R.id.tv_tip_below);
        ivTipBelow = view.findViewById(R.id.iv_tip_below);


        tvTitle.setText(title);

        tvTitle.setTextColor(titleColor);

        setHint(hint);
        setDividerVisibility(withDivider);
        setContentMaxLines(contentMaxLines);
        setRedDotVisibility(withRedDot);
        updateBackground(withBackground);
        ivTipIcon.setOnClickListener(this);
        bl_diagnose_tip.setOnClickListener(this);
    }

    @Deprecated
    public ItemView setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
        return this;
    }

    public void setSecondTitle(String str) {
        secTitle.setText(str);
        secTitle.setVisibility(VISIBLE);
    }

    @Deprecated
    public ItemView setCallback(ItemCallback callback) {
        this.callback = callback;
        return this;
    }

    public void setOnItemViewActionClickListener(OnItemViewActionClickListener listener) {
        this.listener = listener;
    }

    public void setTitle(CharSequence title) {
        tvTitle.setText(title);
    }


    public void setTitle(@StringRes int resId) {
        tvTitle.setText(resId);
    }


    public void setTitleDrawableLeft(@DrawableRes int res) {
        tvTitle.setCompoundDrawablesWithIntrinsicBounds(res, 0, 0, 0);
        tvTitle.setCompoundDrawablePadding(Scale.dip2px(getContext(), 9));
    }

    public MTextView getContentView() {
        return tvContent;
    }

    public MTextView getTitleView() {
        return tvTitle;
    }

    public void setContent(String content) {
        setContent(content, true);
    }

    public void setContent(String content, boolean withChangeTextColor) {
        tvContent.setText(content);
        if (!TextUtils.isEmpty(content) && withChangeTextColor) {
            tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.text_c2));
        }
    }

    /**
     * 设置为已输入状态的颜色（tvContent设置书名号时没有调用setContent方法）
     */
    public void setSelectTitleColor() {
        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.text_c2));
    }

    public void setSubContent(String subContent) {
        if (!TextUtils.isEmpty(subContent)) {
            tvSubContent.setText(subContent);
            tvSubContent.setVisibility(VISIBLE);
        } else {
            tvSubContent.setVisibility(View.GONE);
        }
    }

    public MTextView getSubContentTextView() {
        return tvSubContent;
    }

    public void setItemEnable(boolean isItemEnable) {
        tvContent.setTextColor(ContextCompat.getColor(mContext, isItemEnable ? R.color.text_c6 : R.color.text_c4));
        tvSubContent.setTextColor(ContextCompat.getColor(mContext, isItemEnable ? R.color.text_c6 : R.color.text_c4));
        tvContent.setCompoundDrawablesWithIntrinsicBounds(0, 0, isItemEnable ? R.mipmap.ic_item_arrow : 0, 0);
        updateBackground(withBackground && isItemEnable);
    }

    public String getContent() {
        return tvContent.getText().toString().trim();
    }


    public void setArrowVisibility(boolean isShow) {
        tvContent.setCompoundDrawablesWithIntrinsicBounds(0, 0, isShow ? R.mipmap.ic_item_arrow : 0, 0);
    }

    public void setTitleRedDotVisibility(boolean isShow) {
        ivTitleRedDot.setVisibility(isShow ? View.VISIBLE : View.GONE);
    }

    public void setRedDotVisibility(boolean isShow) {
        ivRedDot.setVisibility(isShow ? View.VISIBLE : View.GONE);
    }

    public ImageView getRightRedDotImageView() {
        return ivRedDot;
    }

    public void setIvTipIconVisibility(boolean isShow) {
        ivTipIcon.setVisibility(isShow ? View.VISIBLE : View.GONE);
    }

    public boolean getTipIconVisibility() {
        return ivTipIcon.getVisibility() == View.VISIBLE;
    }

    public void setArrowDirectDown() {
        tvContent.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.mipmap.ic_arrow_down_black, 0);
    }

    public void setDividerVisibility(boolean isShow) {
        divider.setVisibility(isShow ? VISIBLE : INVISIBLE);
    }

    public void setHint(String hint) {
        tvContent.setHint(hint);
    }

    public void setHint(@StringRes int resId) {
        tvContent.setHint(resId);
    }


    private void updateBackground(boolean enable) {
        if (enable) {
            TypedValue outValue = new TypedValue();
            getContext().getTheme().resolveAttribute(android.R.attr.selectableItemBackground, outValue, true);
            setBackgroundResource(outValue.resourceId);
        } else {
            setBackgroundResource(0);
        }
    }

    public void clear() {
        tvContent.setText("");
    }

    public void setContentMaxLines(int maxLines) {
        tvContent.setMaxLines(maxLines);
        tvContent.setEllipsize(TextUtils.TruncateAt.END);
    }

    public void clearErrorSectionColor() {
        clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFF0F0F0_FF303033));
    }

    public void displayWarnning(String warningMessage) {
        List<String> warningMessageList = null;
        if (!TextUtils.isEmpty(warningMessage)) {
            warningMessageList = new ArrayList<>();
            warningMessageList.add(warningMessage);
        }
        displaWarning(warningMessageList);
    }

    private void displaWarning(List<String> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = warningMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = warningMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle(true);
            tvErrorText.setText(sb.toString());
        }
    }

    private void setTipStyle(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            int _15dp = ZPUIDisplayHelper.dp2px(getContext(), 15);
            int _3dp = ZPUIDisplayHelper.dp2px(getContext(), 3);
            Drawable drawable = getResources().getDrawable(R.mipmap.ic_warning_orange);
            drawable.setBounds(0, 0, _15dp, _15dp);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setCompoundDrawablePadding(_3dp);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FF5E5E5E));
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setCompoundDrawablePadding(0);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }


    /**
     * 区别：字体显示黄色的
     *
     * @param warningMessage
     */
    public void displayWarning2(String warningMessage) {
        List<String> warningMessageList = null;
        if (!TextUtils.isEmpty(warningMessage)) {
            warningMessageList = new ArrayList<>();
            warningMessageList.add(warningMessage);
        }
        displayWarning2(warningMessageList);
    }

    public void displayWarning2(List<String> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = warningMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = warningMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle2(true);
            tvErrorText.setText(sb.toString());
        }
    }

    /**
     * 显示诊断黄线
     */
    public void displayWarning3(List<ServerResumeFieldsItemDetailBean> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            setTipStyle3(true);

            setViewVisibility(ivTipIcon, true);

            if (!ResumeSpUtil.hasShowResumeItemSuggestPop()) {
                setViewVisibility(bl_diagnose_tip, true);
                ResumeSpUtil.recordShowResumeItemSuggestPop();
                Utils.runOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (bl_diagnose_tip != null && bl_diagnose_tip.isAttachedToWindow()) {
                            setViewVisibility(bl_diagnose_tip, false);
                        }

                    }
                }, 5000);
            }

        }
    }

    public void displayWarning4(String warningMessage) {
        List<String> warningMessageList = null;
        if (!TextUtils.isEmpty(warningMessage)) {
            warningMessageList = new ArrayList<>();
            warningMessageList.add(warningMessage);
        }
        displayWarning4(warningMessageList);
    }

    public void displayWarning4(List<String> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = warningMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = warningMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle4(true);
            tvErrorText.setText(sb.toString());
        }
    }

    public void displayWarning5(String warningMessage) {
        List<String> warningMessageList = null;
        if (!TextUtils.isEmpty(warningMessage)) {
            warningMessageList = new ArrayList<>();
            warningMessageList.add(warningMessage);
        }
        displayWarning5(warningMessageList);
    }

    public void displayWarning5(List<String> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = warningMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = warningMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle5(true);
            tvErrorText.setText(sb.toString());
        }
    }

    public void displayWarning6(boolean display, @DrawableRes int resId) {
        if (resId != 0) {
            if (ivTipIcon != null) {
                ivTipIcon.setImageResource(resId);
            }
        }

        setViewVisibility(ivTipIcon, display);
    }

    public void displayWarning7(String warningMessage) {
        List<String> warningMessageList = null;
        if (!TextUtils.isEmpty(warningMessage)) {
            warningMessageList = new ArrayList<>();
            warningMessageList.add(warningMessage);
        }
        displayWarning7(warningMessageList);
    }

    public void displayWarning7(List<String> warningMessageList) {
        if (LList.isEmpty(warningMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = warningMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = warningMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle7(true);
            tvErrorText.setText(sb.toString());
        }
    }

    public void displayWarning8(String warningMessage, @ColorInt int textColor, @ColorInt int iconColor) {
        if (LText.empty(warningMessage)) {
            return;
        }
        tvTipBelow.setVisibility(View.VISIBLE);
        ivTipBelow.setVisibility(View.VISIBLE);

        tvTipBelow.setText(warningMessage);
        tvTipBelow.setTextColor(textColor);
        ivTipBelow.setImageTintList(ColorStateList.valueOf(iconColor));
    }

    public void displayTip(String warningMessage) {
        if (LText.empty(warningMessage)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFF0F0F0_FF303033));
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_GY7));
            tvErrorText.setText(warningMessage);
        }
    }

    private void setTipStyle2(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            int dp2px = ZPUIDisplayHelper.dp2px(getContext(), 15);
            Drawable drawable = getDrawableTint(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            drawable.setBounds(0, 0, dp2px, dp2px);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }

    private void setTipStyle3(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            setViewVisibility(tvErrorText, false);
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            setViewVisibility(tvErrorText, true);
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }

    private void setTipStyle4(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            setViewVisibility(tvErrorText, true);
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            setViewVisibility(tvErrorText, true);
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }

    private void setTipStyle5(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
            int dp2px = ZPUIDisplayHelper.dp2px(getContext(), 15);
            Drawable drawable = getResources().getDrawable(R.mipmap.ic_warning_orange);
            drawable.setBounds(0, 0, dp2px, dp2px);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setCompoundDrawablePadding(ZPUIDisplayHelper.dp2px(getContext(), 4));
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }

    private void setTipStyle7(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFF0F0F0_FF303033));
            int dp2px = ZPUIDisplayHelper.dp2px(getContext(), 15);
            Drawable drawable = getResources().getDrawable(R.mipmap.ic_warning_orange);
            drawable.setBounds(0, 0, dp2px, dp2px);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setCompoundDrawablePadding(ZPUIDisplayHelper.dp2px(getContext(), 4));
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
        } else {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
            tvErrorText.setCompoundDrawables(null, null, null, null);
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF5A5F));
        }
    }


    private void setTipStyle8(boolean isWarning) {
        if (isWarning) {
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_FFF0F0F0_FF303033));
            int dp2px = ZPUIDisplayHelper.dp2px(getContext(), 15);
            Drawable drawable =  ContextCompat.getDrawable(getContext(),R.mipmap.ic_warning_orange);
            if(drawable ==null)return;
            drawable.setBounds(0, 0, dp2px, dp2px);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setCompoundDrawablePadding(ZPUIDisplayHelper.dp2px(getContext(), 4));
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFF7847));
        } else {
            int dp2px = ZPUIDisplayHelper.dp2px(getContext(), 15);
            Drawable drawable = getDrawableTint(ContextCompat.getColor(getContext(), R.color.color_R7));
            if(drawable == null) return;
            drawable.setBounds(0, 0, dp2px, dp2px);
            tvErrorText.setCompoundDrawables(drawable, null, null, null);
            tvErrorText.setCompoundDrawablePadding(ZPUIDisplayHelper.dp2px(getContext(), 4));
            clErrorSection.updateTopSeparatorColor(ContextCompat.getColor(getContext(), R.color.color_R7));
            tvErrorText.setTextColor(ContextCompat.getColor(getContext(), R.color.color_R7));
        }
    }


    public void displayError2(String errorMessage) {
        List<String> errorMessageList = null;
        if (!TextUtils.isEmpty(errorMessage)) {
            errorMessageList = new ArrayList<>();
            errorMessageList.add(errorMessage);
        }
        displayError2(errorMessageList);
    }

    /**
     * 设置错误提示
     *
     * @param errorMessageList 错误提示文案
     */
    public void displayError2(List<String> errorMessageList) {
        if (LList.isEmpty(errorMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = errorMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = errorMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle8(false);
            tvErrorText.setText(sb.toString());
        }
    }


    public void displayError(String errorMessage) {
        List<String> errorMessageList = null;
        if (!TextUtils.isEmpty(errorMessage)) {
            errorMessageList = new ArrayList<>();
            errorMessageList.add(errorMessage);
        }
        displayError(errorMessageList);
    }

    /**
     * 设置错误提示
     *
     * @param errorMessageList 错误提示文案
     */
    public void displayError(List<String> errorMessageList) {
        if (LList.isEmpty(errorMessageList)) {
            divider.setVisibility(VISIBLE);
            clErrorSection.setVisibility(GONE);
        } else {
            divider.setVisibility(GONE);
            clErrorSection.setVisibility(VISIBLE);

            StringBuilder sb = new StringBuilder();
            int size = errorMessageList.size();
            for (int i = 0; i < size; i++) {
                String msg = errorMessageList.get(i);
                if (TextUtils.isEmpty(msg)) continue;
                if (i == size - 1) {
                    sb.append(msg);
                } else {
                    sb.append(msg).append("\n");
                }
            }
            setTipStyle(false);
            tvErrorText.setText(sb.toString());
        }
    }

    private Drawable getDrawableTint(@ColorInt int tintColor) {
        final Drawable drawable = ContextCompat.getDrawable(getContext(), R.mipmap.ic_warning_yellow);
        Drawable drawableTint = null;
        if (drawable != null) {
            drawableTint = drawable.mutate();
            ZPUIDrawableHelper.setDrawableTintColor(drawableTint, tintColor);
        }
        return drawableTint;
    }

    private void setViewVisibility(View view, boolean visible) {
        if (view != null) {
            view.setVisibility(visible ? View.VISIBLE : View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_tip_icon) {//诊断icon
            if (listener != null) {
                listener.onTipIconClick(tvTitle, ivTipIcon);
            }

            // TODO 需要优化，将业务代码移到相应的业务场景中
            if (!TextUtils.isEmpty(fieldCode)) {
                setViewVisibility(bl_diagnose_tip, false);
                ResumeAnalysisUtil.trackClickResumeItemDiagnoseIcon(TextUtils.equals(OnlineResumeConstants.THESIS_FIELD_CODE, fieldCode) ? GarbageResumeBean.CODE_EDU_THESIS_TITLE : fieldCode);
                callback.onClickDiagnoseIcon(fieldCode);
            }
        } else if (id == R.id.bl_diagnose_tip) {//诊断气泡
            setViewVisibility(bl_diagnose_tip, false);
        }
    }

    public interface OnItemViewActionClickListener {
        void onTipIconClick(View anchor, View tipIconView);
    }

    @Deprecated
    public interface ItemCallback {
        void onClickDiagnoseIcon(@NonNull String fieldCode);
    }
}
