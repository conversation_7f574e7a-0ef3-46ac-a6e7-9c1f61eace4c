package com.hpbr.bosszhipin.module.map.adapter;


import static com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper.GEEK_NAV_ROUTE;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.services.core.PoiItem;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module.my.activity.boss.location.utils.PoiItemHelper;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.bean.HighlightItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUILinearLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * created by yhy
 * date 2022/11/29
 * desc:
 */
public class BottomAddressAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private Context mContext;
    private List<Object> mData;

    public BottomAddressAdapter(Context context) {
        mContext = context;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == 1) {
            View view = LayoutInflater.from(mContext).inflate(R.layout.geek_route_bottom_edit_style_1, parent, false);
            return new TopHolder(view);
        } else {
            View view = LayoutInflater.from(mContext).inflate(R.layout.geek_route_bottom_edit_style_2, parent, false);
            return new CommonHolder(view);
        }
    }
    private boolean hasLocationPermission(Context context) {
        try {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        } catch (Exception e) {
            TLog.error(ApmAnalyticsAction.ACTION_FIX_APM_BUG,"hasLocation permission error %s", Log.getStackTraceString(e));
        }
        return false;
    }
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof TopHolder) {
            TopBean bean = (TopBean) mData.get(position);
            TopHolder topHolder = (TopHolder) holder;

            if (TextUtils.isEmpty(bean.locationDesc)) {

                if(!hasLocationPermission(mContext) || !PermissionHelper.hasPermission(GEEK_NAV_ROUTE)){
                    topHolder.mLocationDescTv.setVisibility(View.VISIBLE);
                    topHolder.mLocationDescTv.setText("当前无位置信息");
                    topHolder.mOpenLocationBtn.setVisibility(View.VISIBLE);
                    topHolder.mLocationDescTv.setTextColor(ContextCompat.getColor(mContext, R.color.text_c3));
                }else{
                    topHolder.mLocationDescTv.setVisibility(View.GONE);
                    topHolder.mOpenLocationBtn.setVisibility(View.GONE);
                }
            } else {
                topHolder.mLocationDescTv.setVisibility(View.VISIBLE);
                topHolder.mOpenLocationBtn.setVisibility(View.GONE);
                topHolder.mLocationDescTv.setText(bean.locationDesc);
            }

            if (TextUtils.isEmpty(bean.homeDesc)) {
                topHolder.mHomeDescTv.setText("当前无位置信息");
                topHolder.mAddAddressBtn.setVisibility(View.VISIBLE);
                topHolder.mEditIv.setVisibility(View.GONE);
                topHolder.mHomeDescTv.setTextColor(ContextCompat.getColor(mContext, R.color.text_c3));
            } else {
                topHolder.mHomeDescTv.setText(bean.homeDesc);
                topHolder.mAddAddressBtn.setVisibility(View.GONE);
                topHolder.mEditIv.setVisibility(View.VISIBLE);
            }

            topHolder.mLocationCl.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AnalyticsFactory.create().action("start-location-choose")
                            .param("p",0)
                            .debug().build();
                    mOnItemClickListener.locationClick();
                }
            });
            topHolder.mOpenLocationBtn.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    mOnItemClickListener.locationClick();
                }
            });

            //1212.34 双端不一致，b身份在公司主页进来，不显示该条
            if(UserManager.isBossRole()){
                topHolder.mHomeAddressCl.setVisibility(View.GONE);
            }else{
                topHolder.mHomeAddressCl.setVisibility(View.VISIBLE);
            }

            topHolder.mHomeAddressCl.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AnalyticsFactory.create().action("start-location-choose")
                            .param("p",1)
                            .debug().build();
                    mOnItemClickListener.homeAddressClick();
                }
            });
            topHolder.mEditIv.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    mOnItemClickListener.editAddress();
                }
            });
            topHolder.mAddAddressBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AnalyticsFactory.create().action("start-location-choose")
                            .param("p",2)
                            .debug().build();
                    mOnItemClickListener.editAddress();
                }
            });

        } else if (holder instanceof CommonHolder) {
            CommonBean commonBean = (CommonBean) mData.get(position);
            PoiItem bean = commonBean.poiItem;

            CommonHolder commonHolder = (CommonHolder) holder;
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) commonHolder.itemView.getLayoutParams();
            if (position == 1) {
                params.topMargin = DisplayHelper.dp2px(mContext,8);
                params.bottomMargin = 0;
                commonHolder.mContainerLl.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_geek_route_bottom_top));
                commonHolder.mLabelTv.setVisibility(View.GONE);
            } else if (position == LList.getCount(mData) - 1) {
                params.topMargin = 0;
                params.bottomMargin = DisplayHelper.dp2px(mContext,16);
                commonHolder.mContainerLl.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_geek_route_bottom_bottom));
                commonHolder.mLabelTv.setVisibility(View.GONE);
            } else {
                params.topMargin = 0;
                params.bottomMargin = 0;
                commonHolder.mContainerLl.setBackgroundColor(ContextCompat.getColor(mContext, R.color.color_FFFFFFFF_FF131314));
                commonHolder.mLabelTv.setVisibility(View.GONE);
            }
            commonHolder.itemView.setLayoutParams(params);
            commonHolder.mLocationTitleTv.setText(StringUtil.highLightString(commonBean.highlightItems,bean.getTitle()));
            commonHolder.mLocationDescTv.setText(PoiItemHelper.getDispalyText(bean));

            commonHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AnalyticsFactory.create().action("start-location-choose")
                            .param("p",3)
                            .debug().build();
                    mOnItemClickListener.onItemClick(bean);
                }
            });
        }
    }

    public void setData(List<Object> data) {
        mData = data;
        notifyDataSetChanged();
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public int getItemCount() {
        return LList.getCount(mData);
    }

    public static class TopHolder extends RecyclerView.ViewHolder {

        private ZPUILinearLayout mContainerCl;
        private ConstraintLayout mLocationCl;
        private TextView mLocationDescTv;
        private ZPUIRoundButton mOpenLocationBtn;
        private ConstraintLayout mHomeAddressCl;
        private ImageView mEditIv;
        private TextView mHomeDescTv;
        private ZPUIRoundButton mAddAddressBtn;
        public TopHolder(@NonNull View itemView) {
            super(itemView);

            mEditIv = itemView.findViewById(R.id.address_location_iv);

            mContainerCl = itemView.findViewById(R.id.container_cl);
            mLocationCl = itemView.findViewById(R.id.location_cl);
            mLocationDescTv = itemView.findViewById(R.id.location_desc_tv);
            mOpenLocationBtn = itemView.findViewById(R.id.open_location_btn);
            mHomeAddressCl = itemView.findViewById(R.id.home_address_cl);
            mHomeDescTv = itemView.findViewById(R.id.home_desc_tv);
            mAddAddressBtn = itemView.findViewById(R.id.add_address_btn);
        }
    }

    public static class CommonHolder extends RecyclerView.ViewHolder {
        private ZPUILinearLayout mContainerLl;
        private ConstraintLayout mLocationCl;
        private ImageView mLocationIv;
        private TextView mLocationTitleTv;
        private TextView mLocationDescTv;
        private TextView mLabelTv;

        public CommonHolder(@NonNull View itemView) {
            super(itemView);

            mContainerLl = itemView.findViewById(R.id.container_ll);
            mLocationCl = itemView.findViewById(R.id.location_cl);
            mLocationIv = itemView.findViewById(R.id.location_iv);
            mLocationTitleTv = itemView.findViewById(R.id.location_title_tv);
            mLocationDescTv = itemView.findViewById(R.id.location_desc_tv);
            mLabelTv = itemView.findViewById(R.id.label_tv);
        }
    }

    public static class TopBean implements Serializable {
        private static final long serialVersionUID = 3734221505600600631L;
        public String locationDesc;
        public String homeDesc;
    }
    public static class CommonBean implements Serializable {
        private static final long serialVersionUID = 8192558933951608807L;
        public PoiItem poiItem;
        public List<HighlightItem> highlightItems = new ArrayList<>();
    }
    private onItemClickListener mOnItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener{
        void locationClick();

        void editAddress();
        void homeAddressClick();

        void onItemClick(PoiItem poiItem);
    }
}