package com.hpbr.bosszhipin.module.mapcompat;

import android.content.Context;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.module.mapcompat.act.GeekRouteMapActivity;
import com.hpbr.bosszhipin.module.map.activity.GeekRoute1004NewActivity;
import com.hpbr.bosszhipin.module.map.bean.GeekRouteParam;
import com.techwolf.lib.tlog.TLog;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2024/10/8 20:58
 *     desc   :
 *     version:
 * </pre>
 */
public class GeekRouteHelper {

    public static final String TAG = "GeekRouteHelper";

    //----GuideSource----
    public static final int SOURCE_GUIDE_DEFAULT = 0;
    public static final int SOURCE_GUIDE_COMPANY = 1;
    public static final int SOURCE_GUIDE_JD = 2;
    public static final int SOURCE_GUIDE_INTERVIEW = 3;
    public static final int SOURCE_GUIDE_CHAT = 4;
    public static final int SOURCE_HAILUO_INTERVIEW_CODE = 5;
    //----Source----
    public static final int SOURCE_FROM_MAP = 1;
    public static final int SOURCE_FROM_ADDRESS_BAR = 2;
    public static final int SOURCE_FROM_COMPANY = 3;



    public static void gotoThisActivity(@NonNull Context context, GeekRouteParam param) {
        TLog.info(TAG, "gotoThisActivity %s %s ,param = %s", MapViewCompat.isForceAMap(), AndroidDataStarGray.getInstance().isUserNewMap(), null != param ? param.toString() : "");
        GeekRouteMapActivity.gotoThisActivity(context, param);

    }
}
