package com.hpbr.bosszhipin.module.login.util;

import android.content.Intent;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.data.db.entry.GroupUserCardBean;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.module.commend.activity.advanced.common.BossFilterConditionHelper;
import com.hpbr.bosszhipin.module.contacts.entity.NewQuickReplyBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.QuickReplyManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.F3BannerManager;
import com.hpbr.bosszhipin.module_boss_export.entity.BossIntentionF2EntranceParams;
import com.hpbr.bosszhipin.setting_export.SettingConstants;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.chatbottom2.emotion.data.ChatEmotionDataManager;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;

import net.bosszhipin.api.BossIsDisplaySwitchRequest;
import net.bosszhipin.api.BossIsDisplayVIPResponse;
import net.bosszhipin.api.BossUserBatchResponse;
import net.bosszhipin.api.BossUserDataBatchRequest;
import net.bosszhipin.api.GetAdvBannerListRequest;
import net.bosszhipin.api.GetAdvBannerListResponse;
import net.bosszhipin.api.GetBossBlockVipStatusRequest;
import net.bosszhipin.api.GetBossBlockVipStatusResponse;
import net.bosszhipin.api.GetBossIntentionF2EntranceRequest;
import net.bosszhipin.api.GetBossIntentionF2EntranceResponse;
import net.bosszhipin.api.GetBossJobGrayRequest;
import net.bosszhipin.api.GetBossJobGrayResponse;
import net.bosszhipin.api.GetUserFeatureRequest;
import net.bosszhipin.api.GetUserFeatureResponse;
import net.bosszhipin.api.GetUserGroupCardRequest;
import net.bosszhipin.api.GetUserGroupCardResponse;
import net.bosszhipin.api.GetUserProxyInfoRequest;
import net.bosszhipin.api.GetUserProxyInfoResponse;
import net.bosszhipin.api.GetUserQuickReplyRequest;
import net.bosszhipin.api.GetUserQuickReplyResponse;
import net.bosszhipin.api.GetUserStickerRequest;
import net.bosszhipin.api.GetUserStickerResponse;
import net.bosszhipin.api.UserNotifySettingRequest;
import net.bosszhipin.api.UserNotifySettingResponse;
import net.bosszhipin.api.bean.ServerUserQuickReplyBean;
import net.bosszhipin.api.bean.UserNotifySetting;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

class BossUserDetailUtils {

    private static boolean advancedSearchTipRequest;

    public static void refreshBossUserOtherInfo() {
        BossFilterConditionHelper.getInstance().requestFilterData();
        BossUserDataBatchRequest batchRequest = new BossUserDataBatchRequest(new SimpleApiRequestCallback<BossUserBatchResponse>() {
            @Override
            public void handleInChildThread(ApiData<BossUserBatchResponse> data) {
                BossUserBatchResponse response = data.resp;
                // 1. 群聊卡
                GetUserGroupCardResponse groupCardResponse = response.groupCardResponse;
                if (groupCardResponse != null && groupCardResponse.initFlag == 0) {
                    GroupUserCardBean userCardBean = new GroupUserCardBean();
                    userCardBean.parseFromServer(groupCardResponse);
                    GroupManager.getInstance().saveUserCard(userCardBean);
                }

                // 2. GIF表情
                GetUserStickerResponse stickerResponse = response.stickerResponse;
                if (stickerResponse != null && !LList.isEmpty(stickerResponse.packs)) {
                    //删除老的SP里面表情，移动到新的SP里面,老的sp需要外面传递进来User信息
                    ChatEmotionDataManager.getInstance().replaceSpValue(App.get(), PackageConfigContants.PACKAGE_NAME + "SP_ALL_EMOTION_KEY" + UserManager.getUID() + "_" + UserManager.getUserRole().get());
                    ChatEmotionDataManager.getInstance().initEmotion(stickerResponse.packs);
                }
                // 3. 快速回复
                GetUserQuickReplyResponse quickReplyResponse = response.quickReplyResponse;
                if (quickReplyResponse != null) {
                    List<NewQuickReplyBean> newQuickReplyBeans = new ArrayList<>();
                    if (!LList.isEmpty(quickReplyResponse.result)) {
                        for (ServerUserQuickReplyBean item : quickReplyResponse.result) {
                            if (item == null) continue;
                            if (item.identity == AccountHelper.getIdentity()) {
                                NewQuickReplyBean bean = new NewQuickReplyBean();
                                bean.parseFromServer(item);
                                newQuickReplyBeans.add(bean);
                            }
                        }
                    }
                    QuickReplyManager.saveQuickReply(newQuickReplyBeans);
                }
                // 4. AB test功能
                GetUserFeatureResponse featureResponse = response.featureResponse;
                if (featureResponse != null) {
                    if (null != featureResponse.advanceSearchEntry) {//BossF1强化搜索灰度新样式
                        UserGrayFeatureManager.getInstance().saveF1AdvanceSearchEntryDesc(featureResponse.advanceSearchEntry.searchDesc);
                    }
                    UserGrayFeatureManager.getInstance().saveShowHometownTag(featureResponse.showHometown);
                    UserGrayFeatureManager.getInstance().saveCommonDeviceTag(featureResponse.showCommonDevice);
                    UserGrayFeatureManager.getInstance().saveBlockActivitySwitch(featureResponse.blockActivitySwitch);
                    UserGrayFeatureManager.getInstance().saveSwitchIdentityEntrance(featureResponse.switchIdentity);
                    UserGrayFeatureManager.getInstance().saveNewContextStyle(featureResponse.chatOptimization, featureResponse.chatOptimizationV2, featureResponse.readUser, featureResponse.unReadUser);
                    UserGrayFeatureManager.getInstance().saveChatReportUrl(featureResponse.reportUrl);
                    UserGrayFeatureManager.getInstance().saveAccountAuthStatusGray(featureResponse.userAccountAuthStatusGray);
                    UserGrayFeatureManager.getInstance().saveInternetPhoneGray(featureResponse.internetPhone);
                }

                // 5. Boss阻断VIP状态
                GetBossBlockVipStatusResponse vipStatusResponse = response.vipStatusResponse;
                GetBossBlockVipStatusResponse lastStatusData = UserGrayFeatureManager.getInstance().getBossBlockVipStatus();
                boolean isVipStateNeedRefresh = false;
                if (vipStatusResponse != null) {
                    if (lastStatusData != null && (lastStatusData.isFunctionVisible() != vipStatusResponse.isFunctionVisible())) {
                        isVipStateNeedRefresh = true;
                    }
                    UserGrayFeatureManager.getInstance().saveBossBlockVipStatus(vipStatusResponse);
                }
                data.setExtendParam("vipStatusRefresh", isVipStateNeedRefresh);

                // 6
                BossIsDisplayVIPResponse isDisplayEntranceResponse = response.isDisplayEntranceResponse;
                if (isDisplayEntranceResponse != null) {
                    UserGrayFeatureManager.getInstance().saveBossIsDisplayVipEntrance(isDisplayEntranceResponse);
                }

                // 8. boss身份道具配置
                GetUserProxyInfoResponse userProxyInfoResponse = response.userProxyInfoResponse;

                if (userProxyInfoResponse != null) {
                    UserGrayFeatureManager.getInstance().saveUserProxyConfig(userProxyInfoResponse.recruit);
                    UserGrayFeatureManager.getInstance().saveOnlineNotify(userProxyInfoResponse.onlineRemind);
                    UserGrayFeatureManager.getInstance().saveBossSubscribedRedTip(userProxyInfoResponse.subscribeTip);
                    UserGrayFeatureManager.getInstance().saveSearchCard4JobGray(userProxyInfoResponse.searchCard4JobGray);
                    UserGrayFeatureManager.getInstance().saveRefinedDefSelection(userProxyInfoResponse.refinedSelected);
                    UserGrayFeatureManager.getInstance().saveItemMallNewStyleGray(userProxyInfoResponse.itemMallNewStylePageUrl);
                    UserGrayFeatureManager.getInstance().saveRiskMessage(userProxyInfoResponse.filterRiskMessage);
                    UserGrayFeatureManager.getInstance().setHasEasyChat(userProxyInfoResponse.searchEasyChatGray);
                    UserGrayFeatureManager.getInstance().setSmsLabelText(userProxyInfoResponse.smsLabelText);
                    UserGrayFeatureManager.getInstance().setIntentionOrderGray(userProxyInfoResponse.intentionOrderGray);
                    UserGrayFeatureManager.getInstance().savePositionKeyWordsSelectGray(userProxyInfoResponse.positionKeyWordsSelectGray);
                }
                // 9. F3 banner
                GetAdvBannerListResponse bannerListResponse = response.bannerListResponse;
                if (bannerListResponse != null) {
                    F3BannerManager.getInstance().saveBanner(bannerListResponse.adActivityList);
                }

                GetBossJobGrayResponse bossJobGrayResponse = response.bossJobGrayResponse;
                if (bossJobGrayResponse != null) {
                    UserGrayFeatureManager.getInstance().saveCanUseJobProject(bossJobGrayResponse.canUsePreferredProject);
                    UserGrayFeatureManager.getInstance().saveBossPreferredPubInfoV2(bossJobGrayResponse.preferredPubInfoV2);
                    UserGrayFeatureManager.getInstance().saveBossShowJobNameGuide(bossJobGrayResponse.showJobNameGuide);
                    UserGrayFeatureManager.getInstance().saveRefinedGeekSeniorScreenFreeGray(bossJobGrayResponse.refinedGeekSeniorScreenFree);
                    UserGrayFeatureManager.getInstance().saveComOptimizationType(bossJobGrayResponse.optimizationType);
                    UserGrayFeatureManager.getInstance().saveAddressUseAMap(bossJobGrayResponse.useAmap);
                    UserGrayFeatureManager.getInstance().saveAddressUseComPoi(bossJobGrayResponse.useComPoi);
                    UserGrayFeatureManager.getInstance().saveJobDescGuidList(bossJobGrayResponse.jobDescGuidList);
                    UserGrayFeatureManager.getInstance().saveCanUseSelectProject(bossJobGrayResponse.canUseSelectedProject);
                    UserGrayFeatureManager.getInstance().saveSelectedPubInfo(bossJobGrayResponse.selectedPubInfo);
                }

                GetBossIntentionF2EntranceResponse bossIntentionF2EntranceResponse = response.bossIntentionF2EntranceResponse;
                BossIntentionF2EntranceParams intentionF2EntranceParams;
                if (bossIntentionF2EntranceResponse != null) {
                    intentionF2EntranceParams = new BossIntentionF2EntranceParams();
                    intentionF2EntranceParams.showEntrance = bossIntentionF2EntranceResponse.showEntrance;
                    intentionF2EntranceParams.entranceTag = bossIntentionF2EntranceResponse.entranceTag;
                    intentionF2EntranceParams.bannerButton = bossIntentionF2EntranceResponse.bannerButton;
                    intentionF2EntranceParams.bannerText = bossIntentionF2EntranceResponse.bannerText;
                    intentionF2EntranceParams.bannerButtonJumpUrl = bossIntentionF2EntranceResponse.bannerButtonJumpUrl;
                    intentionF2EntranceParams.bannerStyle = bossIntentionF2EntranceResponse.bannerStyle;
                    UserGrayFeatureManager.getInstance().saveBossIntentionF2EntranceParams(intentionF2EntranceParams);
                }

                //10. 根据开关类型获取开关设置
                boolean isPersonalitySwitchChange = false;
                UserNotifySettingResponse userNotifySettingResponse = response.userNotifySettingResponse;
                if (null != userNotifySettingResponse) {
                    UserNotifySetting userNotifySetting = userNotifySettingResponse.userNotifySetting;
                    if (userNotifySetting != null) {
                        boolean open = userNotifySetting.settingType == 4;
                        isPersonalitySwitchChange = (open != NotifyUtils.isPersonalRecommendationOpen());
                        if (open != NotifyUtils.isPersonalRecommendationOpen()) {
                            NotifyUtils.setPersonalRecommendationOpen(open);
                        }
                    }
                }
                data.setExtendParam("isPersonalitySwitchChange", isPersonalitySwitchChange);
            }

            @Override
            public void onSuccess(ApiData<BossUserBatchResponse> data) {
                boolean isPersonalitySwitchChange = (boolean) data.getExtendParam("isPersonalitySwitchChange");
                if (isPersonalitySwitchChange) {
                    ReceiverUtils.sendBroadcast(App.getAppContext(), new Intent(Constants.RECEIVER_PERSONALITY_FUNCTION_SWITCH_CHANGE));
                }
                boolean isVipStateNeedRefresh = (boolean) data.getExtendParam("vipStatusRefresh");
                if (isVipStateNeedRefresh) { // 若VIP状态需要刷新时，则进行F1筛选条件上VIP标记的刷新
                    ReceiverUtils.sendBroadcast(App.getAppContext(), new Intent(Constants.RECEIVER_BOSS_VIP_STATE_TIP));
                }
                ReceiverUtils.sendBroadcast(App.getAppContext(), new Intent(Constants.RECEIVER_ADVANCED_SEARCH_TAB_SUBSCRIBED_TIP));
            }
        });
        // 1107 如果存在缓存, 则不请求更新数据， 减少接口调用
        if (GroupManager.getInstance().getUserCard() == null) {
            // 1. 用户群聊卡
            batchRequest.groupCardRequest = new GetUserGroupCardRequest();
        }
        // 2. 聊天GIF表情
        batchRequest.stickerRequest = new GetUserStickerRequest();
        // 3. 快速回复
        batchRequest.quickReplyRequest = new GetUserQuickReplyRequest();
        // 4. AB测试功能
        batchRequest.featureRequest = new GetUserFeatureRequest();
        // 5. Boss阻断类型VIP状态
        batchRequest.vipStatusRequest = new GetBossBlockVipStatusRequest();
        // 6. vip功能管理入口判断
        batchRequest.mBossIsDisplaySwitchRequest = new BossIsDisplaySwitchRequest();
        // 18 白领猎头牛人操作
        batchRequest.userProxyInfoRequest = new GetUserProxyInfoRequest();
        // 9 F3 banner
        GetAdvBannerListRequest bannerRequest = new GetAdvBannerListRequest();
        bannerRequest.extra_map.put("dataType", "9");
        batchRequest.bannerListRequest = bannerRequest;
        // 10 个性化开关
        UserNotifySettingRequest userNotifySettingRequest = new UserNotifySettingRequest();
        userNotifySettingRequest.notifyType = SettingConstants.NOTIFY_TYPE_POSITION_RECOMMENDATION;
        batchRequest.userNotifySettingRequest = userNotifySettingRequest;

        batchRequest.bossJobGrayRequest = new GetBossJobGrayRequest();

        batchRequest.bossIntentionF2EntranceRequest = new GetBossIntentionF2EntranceRequest();

        batchRequest.execute();
    }
}
