package com.hpbr.bosszhipin.module.main.viewholder;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.decoration.GridDividerItemDecoration;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.main.adapter.BossPropListAdapter;
import com.hpbr.bosszhipin.module.main.entity.BarItem;
import com.hpbr.bosszhipin.module.main.views.BossDeliverScheduleCompletedSingleView;
import com.hpbr.bosszhipin.module.main.views.BossDeliverScheduleProgressBarView;
import com.hpbr.bosszhipin.module.main.views.BossDeliverScheduleView;
import com.hpbr.bosszhipin.module.main.views.BossPropTipView;
import com.hpbr.bosszhipin.module.main.views.BossPropTipView2;
import com.hpbr.bosszhipin.module.main.views.BossPropTipView4;
import com.hpbr.bosszhipin.module.main.views.BossPropTipView5;
import com.hpbr.bosszhipin.module.main.views.BossPropTipView6;
import com.hpbr.bosszhipin.module.main.views.BossUnpaidOrderView;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.BossGetItemMallF3Response;
import net.bosszhipin.api.BossUnpaidOrderResponse;
import net.bosszhipin.api.GetBossDeliverScheduleResponse;
import net.bosszhipin.api.GetUserRechargeGuideResponse;
import net.bosszhipin.api.bean.ServerBossUnpaidOrderBean;
import net.bosszhipin.api.bean.ServerDeliverScheduleBean;
import net.bosszhipin.api.bean.ServerMyItemTipBean;

import java.util.ArrayList;
import java.util.List;

public class PropAndTipBarViewHolder implements BarViewHolder<BarItem> {

    private final View mItemView;
    private final RecyclerView rvpPropList; // 道具列表
    private final FrameLayout flOperationTipContainer; // 引导操作类提示条（在道具下方）：待支付订单、生效中的道具
    private BossPropListAdapter propListAdapter;
    private final List<BossGetItemMallF3Response.ItemListBean> itemList = new ArrayList<>();

    private PropAndTipBarViewHolder(ViewGroup parent) {
        mItemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_dynamic_type_boss_prop, parent, false);
        rvpPropList = mItemView.findViewById(R.id.rvPropList);
        flOperationTipContainer = mItemView.findViewById(R.id.flOperationTipContainer);
        rvpPropList.setNestedScrollingEnabled(false);
    }

    @NonNull
    @Override
    public View getItemView() {
        return mItemView;
    }

    @Override
    public void bindAsBar(@NonNull BarItem itemData) {
        bindPropList(itemData);
        bindTipData(itemData);
    }

    /**
     * 绑定道具列表
     */
    private void bindPropList(@NonNull BarItem itemData) {
        if (itemData.itemMallF3Response == null || LList.isEmpty(itemData.itemMallF3Response.itemList)) {
            return;
        }
        itemList.clear();
        itemList.addAll(itemData.itemMallF3Response.itemList);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(mItemView.getContext(), 3, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollHorizontally() {
                return false;
            }

            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
        rvpPropList.setLayoutManager(gridLayoutManager);
        propListAdapter = new BossPropListAdapter(itemList, itemData.itemMallF3Response);

        //用于埋点
        List<Integer> typeList = new ArrayList<>();
        for (BossGetItemMallF3Response.ItemListBean bean : itemList) {
            if (bean == null) continue;
            typeList.add(bean.itemType);
        }
        String typeListStr = GsonUtils.toJson(typeList);

        propListAdapter.setOnItemClickListener((adapter, view, position) -> {
            BossGetItemMallF3Response.ItemListBean item = propListAdapter.getItem(position);
            if (item != null && item.isValid()) {
                new ZPManager(mItemView.getContext(), item.url).handler();

                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CLICK_BOSS_ITEM)
                        .param("p", item.itemType)
                        .param("p2", item.title)
                        .param("p3", typeListStr)
                        .param("p5", item.title)
                        .param("p6", item.discountTag)
                        .build();

                AnalyticsFactory.create().action("biz-item-F3-click")
                        .param("p", item.itemType)
                        .param("p2", LList.getCount(item.subTitleList) > 1 ? "1" : "0")
                        .param("p3", item.subTitle)
                        .build();
            }
        });

        GridDividerItemDecoration decoration = new GridDividerItemDecoration(1, 10, ContextCompat.getColor(mItemView.getContext(), R.color.color_FFF0F0F0_FF303033));
        rvpPropList.addItemDecoration(decoration);
        rvpPropList.setAdapter(propListAdapter);
    }

    /**
     * 绑定道具道具提示条数据
     */
    private void bindTipData(@NonNull BarItem itemData) {
        BossGetItemMallF3Response response = itemData.itemMallF3Response;
        if (response == null) {
            return;
        }
        GetBossDeliverScheduleResponse deliverScheduleResponse = response.deliverScheduleResponse;
        ServerMyItemTipBean myItemTip = response.myItemTip;
        BossUnpaidOrderResponse unpaidOrderResponse = response.bossUnpaidOrderResponse;
        List<ServerBossUnpaidOrderBean> orderList = BossUnpaidOrderResponse.getValidOrderList(unpaidOrderResponse);
        GetUserRechargeGuideResponse rechargeGuideResponse = response.rechargeGuideResponse;

        if (LList.hasElement(orderList)) {
            // 1222.211 待支付订单展示优先级调整为最高
            handleUnpaidOrder(orderList);
        } else if (deliverScheduleResponse != null && LList.hasElement(deliverScheduleResponse.deliverSchedule)) {
            handleDeliverSchedule(deliverScheduleResponse.deliverSchedule);
        } else if (myItemTip != null) {
            handleItemTip(myItemTip);
        } else if (rechargeGuideResponse != null) {
            handleRechargeGuide(rechargeGuideResponse);
        }
    }

    /**
     * 1201.245 简历交付
     */
    private void handleDeliverSchedule(@NonNull List<ServerDeliverScheduleBean> list) {
        for (ServerDeliverScheduleBean bean : list) {
            if (bean == null) continue;
            bean.viewType = bean.completed == 1 ? ServerDeliverScheduleBean.TYPE_COMPLETED : ServerDeliverScheduleBean.TYPE_IN_PROGRESS;
        }

        flOperationTipContainer.setVisibility(View.VISIBLE);
        flOperationTipContainer.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        Context context = mItemView.getContext();
        if (list.size() > 1) {
            BossDeliverScheduleView bannerView = new BossDeliverScheduleView(context);
            bannerView.setData(list);
            flOperationTipContainer.addView(bannerView, params);
        } else {
            ServerDeliverScheduleBean bean = LList.getElement(list, 0);
            if (bean == null) return;
            if (bean.completed == 1) {
                BossDeliverScheduleCompletedSingleView completedSingleView = new BossDeliverScheduleCompletedSingleView(context);
                completedSingleView.setData(bean);
                flOperationTipContainer.addView(completedSingleView, params);
            } else {
                BossDeliverScheduleProgressBarView progressBarView = new BossDeliverScheduleProgressBarView(context);
                progressBarView.setData(bean, true);
                flOperationTipContainer.addView(progressBarView, params);
            }
        }
    }

    private void handleItemTip(@NonNull ServerMyItemTipBean myItemTip) {
        flOperationTipContainer.setVisibility(View.VISIBLE);
        flOperationTipContainer.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        Context context = mItemView.getContext();
        switch (myItemTip.tipType) {
            case ServerMyItemTipBean.TipType.SINGLE_EFFECTING:
                // 1125.248 单个道具生效 轮播
                BossPropTipView5 tipView5 = new BossPropTipView5(context);
                tipView5.setData(myItemTip);
                flOperationTipContainer.addView(tipView5, params);
                break;
            case ServerMyItemTipBean.TipType.MULTI_EFFECTING:
                // 1125.248 多个道具生效
                BossPropTipView6 tipView6 = new BossPropTipView6(context);
                tipView6.setData(myItemTip);
                flOperationTipContainer.addView(tipView6, params);
                break;
            case ServerMyItemTipBean.TipType.NEW_GUIDE:
                // 1004.255 道具新手引导
                BossPropTipView2 vipCardView2 = new BossPropTipView2(context);
                vipCardView2.setData(myItemTip);
                flOperationTipContainer.addView(vipCardView2, params);
                break;
            default:
                BossPropTipView vipCardView = new BossPropTipView(context);
                vipCardView.setData(myItemTip);
                flOperationTipContainer.addView(vipCardView, params);
                break;
        }
    }

    /**
     * 1108.201 待支付订单
     */
    private void handleUnpaidOrder(@NonNull List<ServerBossUnpaidOrderBean> orderList) {
        flOperationTipContainer.setVisibility(View.VISIBLE);
        flOperationTipContainer.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        BossUnpaidOrderView unpaidOrderView = new BossUnpaidOrderView(mItemView.getContext());
        unpaidOrderView.setData(orderList);
        unpaidOrderView.setCallback(() -> {
            flOperationTipContainer.removeAllViews();
            flOperationTipContainer.setVisibility(View.GONE);
        });
        flOperationTipContainer.addView(unpaidOrderView, params);

        List<String> p1List = new ArrayList<>();
        String p2 = "2";
        List<String> p3List = new ArrayList<>();
        List<String> p4List = new ArrayList<>();
        List<String> p5List = new ArrayList<>();
        List<String> p6List = new ArrayList<>();
        for (ServerBossUnpaidOrderBean orderBean : orderList) {
            if (orderBean == null) continue;
            p1List.add(orderBean.unBzbOrderId);
            p3List.add(orderBean.subTitleType);
            p4List.add(orderBean.gray1125);
            p5List.add(orderBean.discountAmount);
            p6List.add(orderBean.beanAmount);
        }
        AnalyticsFactory.create().action("biz-block-f4-prep-exp")
                .param("p", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, p1List))
                .param("p2", p2)
                .param("p3", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, p3List))
                .param("p4", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, p4List))
                .param("p5", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, p5List))
                .param("p6", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, p6List))
                .build();
    }

    /**
     * 1124.201 充值引导
     */
    private void handleRechargeGuide(@NonNull GetUserRechargeGuideResponse rechargeGuideResponse) {
        flOperationTipContainer.setVisibility(View.VISIBLE);
        flOperationTipContainer.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        BossPropTipView4 propTipView4 = new BossPropTipView4(mItemView.getContext());
        propTipView4.setData(rechargeGuideResponse);
        flOperationTipContainer.addView(propTipView4, params);
    }

    public void release() {
        if (rvpPropList != null && propListAdapter != null && propListAdapter.getItemCount() > 0) {
            int itemCount = propListAdapter.getItemCount();
            for (int i = 0; i < itemCount; i++) {
                RecyclerView.ViewHolder holder = rvpPropList.findViewHolderForAdapterPosition(i);
                if (holder instanceof BossPropListAdapter.PropViewHolder) {
                    BossPropListAdapter.PropViewHolder propViewHolder = (BossPropListAdapter.PropViewHolder) holder;
                    propViewHolder.release();
                }
            }
        }
    }

    public static class Factory extends BoundBarViewHolderFactory {

        @Override
        BarViewHolder<BarItem> newBarViewHolder(ViewGroup parent) {
            return new PropAndTipBarViewHolder(parent);
        }

    }

}
