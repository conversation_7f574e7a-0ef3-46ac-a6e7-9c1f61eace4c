package com.hpbr.bosszhipin.module.webview.jsi;

import android.util.Log;

import com.google.gson.Gson;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module.webview.WebViewCommon;
import com.hpbr.bosszhipin.module.webview.jsi.devices.clipboard.ZpGetClipboardDataAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.clipboard.ZpSetClipboardDataAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.net.GetNetworkTypeAction2;
import com.hpbr.bosszhipin.module.webview.jsi.devices.screen.OffAndOnUserCaptureScreenAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.screen.SetKeepScreenOnAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.GetAppBaseInfoAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.GetDeviceInfoAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.GetSystemSettingAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.GetWindowInfoAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.HasBzbAppInstalledAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.OpenAppAuthorizeSettingAction;
import com.hpbr.bosszhipin.module.webview.jsi.devices.system.SetSystemSettingAction;
import com.hpbr.bosszhipin.module.webview.jsi.files.GetFileSystemRootAction;
import com.hpbr.bosszhipin.module.webview.jsi.keyboard.HideKeyboardAction;
import com.hpbr.bosszhipin.module.webview.jsi.keyboard.OffKeyboardHeightChangeAction;
import com.hpbr.bosszhipin.module.webview.jsi.keyboard.OnKeyboardHeightChangeAction;
import com.hpbr.bosszhipin.module.webview.jsi.thirdservice.pay.RequestPaymentAction;
import com.hpbr.bosszhipin.module.webview.jsi.thirdservice.share.ShareAction;
import com.hpbr.bosszhipin.module.webview.jsi.thirdservice.share.ShowOrHideShareAction;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

/**
 * Created by zhangxiangdong on 2019/8/10 13:38.
 * <p>
 * 处理新的（7.09）Javascript和Native交互逻辑。
 * <p>
 * <a href="https://xieyi.weizhipin.com/#/webMethod/list">协议管理平台</a>
 */
public class JavascriptMessageHandler {

    public static final String TAG = "JavascriptMessageHandler";

    //region ACTIONS
    private static final String ACTION_SHARE_PIC = "sharePic";
    private static final String ACTION_DOWNLOAD_PIC = "downloadPic";
    private static final String ACTION_SHARE_URL = "shareUrl";
    private static final String ACTION_SYNC_DATA = "syncData";//默认工作体验点赞
    /*
        717.69【招聘者】客户端内H5上传图片可一次多张@FE张磊@香玉
        809.60【招聘者+求职者】意见反馈支持上传视频
     */
    private static final String ACTION_UPLOAD_PIC = "uploadPic";
    private static final String ACTION_GET_NETWORK_TYPE = "getNetWorkType"; // 720 客户端新增获取当前网络类型（是WiFi还是5G、4G等）协议
    private static final String ACTION_IDLE_TIMER_DISABLED = "idleTimerDisabled"; // 防止H5直播息屏 在webview释放的时候，还原系统息屏设置
    //813 安卓-隐藏WebView导航返回按钮和屏幕上的物理返回键
    private static final String ACTION_HIDE_NAV_BACK_BTN = "hideNavBackBtn";
    //禁用返回 不涉及左上角的返回按钮
    private static final String ACTION_SWIPE_BACK = "swipeBack";
    /*816 F3点击个人主页进入的信息引导H5关闭以及个人主页"去完善"进入的H5关闭*/
    private static final String ACTION_PERSON_INFO_COMPLETE = "personInfoComplete";
    /*8.19 获取微信认证回调*/
    public static final String ACTION_GET_WX_CODE = "getWxCode";
    public static final String ACTION_OPEN_TWLWAIT_FACETIME = "openTWLWaitFacetime";
    public static final String ACTION_SHARE_WX_MINI_APP = "shareWxMiniAPP";
    public static final String ACTION_ZP_FACE_VECTOR = "postzpfacevector";
    public static final String ACTION_SHARE_FILE = "shareFile";
    public static final String ACTION_SHARE_ENCRYPT_RESUME = "shareEncryptResume";
    public static final String ACTION_SWITCH_SCREEN_ORIENTATION = "switchScreenOrientation";
    public static final String ACTION_FULL_SCREEN = "fullScreen";
    public static final String ACTION_VIDEO_RECORD = "videoRecord";

    /*1011 通过调用客户端方法，获取页面全屏状态*/
    public static final String ACTION_GET_FULL_SCREEN_STATUS = "getFullScreenStatus";
    /*1011 电池栏高度获取方法*/
    public static final String ACTION_GET_STATUS_BAR_HEIGHT = "getStatusBarHeight";
    /*1012 【设计体验优化】视觉语言升级3.0 - 乘风破浪 万物向新（2期）*/
    public static final String ACTION_GET_APP_NEW_STYLE = "getAppNewStyle";
    /*1016.706 打开拍摄VR页面*/
    public static final String ACTION_SHOOT_VR_PHOTOS = "shootVrPhotos";
    /*1016.706 上传VR文件完成（上传成功和上传失败都会回调）*/
    public static final String ACTION_UPLOAD_VR_PHOTOS_FINISH = "uploadVrPhotosFinish";
    /*1101.81 审核中的VR预览页面按钮点击事件回调*/
    public static final String ACTION_VR_CHECKING_PREVIEW = "VrCheckingPreview";
    /*1111.709【安全】地址自助认证增加目标检测引导*/
    public static final String ACTION_SEND_TARGET_RESULTS = "sendTargetResults";

    /**
     * 1211 终止环境认证表单提交流程, 关闭表单页面
     */
    public static final String ACTION_ADDRESS_VERIFY_INTERRUPT = "addressVerifyH5Interrupt";

    /*1108.214【商业】安卓端部分用户缩短支付路径实验 */
    public static final String ACTION_GET_BZB_APP_INSTALLED = "getBzbAppInstalled";

    /*1108.201【商业】新增待支付订单列表 - 待支付订单列表状态*/
    public static final String ACTION_NOTIFY_BZB_UNPAID_ORDER_STATUS = "notifyBzbUnpaidOrderStatus";
    /* 1112 本机号码认证*/
    public static final String ACTION_VERIFY_MOBILE_PHONE = "verifyMobilePhone";

    /*1105*/
    public static final String getZPwt = "getZPwt";

    public static final String ACTION_1107_REPORTED_F1_REQUEST = "getF1ReportRecList";

    /**
     * 1112.256
     * <p>
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=210154197
     */
    @SuppressWarnings("JavadocLinkAsPlainText")
    public static final String GET_SHOP_TITLE_NOTICE_RED_DOT = "getShopTitleNoticeRedDot";

    /*1113加入 H5缓存*/
    public static final String H5LocalStorageValue = "H5LocalStorageValue";

    /*1116加入*/
    public static final String SET_CLIPBOARD_DATA_ACTION = "setClipboardData";
    public static final String GET_CLIPBOARD_DATA_ACTION = "getClipboardData";

    public static final String FACE_DETECT_ACTION = "faceDetect";
    public static final String UPLOAD_FILE_ACTION = "uploadFile";
    public static final String GET_UNREAD_CHAT_MESSAGE_NUM_ACTION = "getUnreadChatMessageNum";
    public static final String GET_REBIND_VIDEO_POSITION = "rebindVideoPosition";

    public static final String ACTION_WX_OPEN_CUSTOMER_CHAT = "wxOpenCustomerServiceChat";

    public static final String ACTION_CLOSE_ALL_WEBVIEW = "closeAllView";
    public static final String ACTION_REFRESH_WHEN_WEB_RESUME = "refreshWhenViewDidAppear";
//    public static final String ACTION_CLOSE_BRAND_INVITE = "closeBrandInvite";
    public static final String ACTION_NOTIFY_GEEK_F1_REFRESH = "notifyGeekF1Refresh";
    public static final String ACTION_UPDATE_SWITCH_STATUS = "updateSwitchStatus";
    public static final String ACTION_DOWNLOAD_VIDEO = "downloadVideo";
    public static final String ACTION_ZP_SCORE_SEND_TO_MESSAGE = "zpScoreSendToMessage";
    // 1217.910【内容】职位视频发布流程优化，新增视频发布进度/发布状态同步协议
    public static final String ACTION_GET_SYNC_PUBLISH_VIDEO_STATUS = "syncGetPublishVideoStatus";
    // 1217.910【内容】职位视频发布流程优化，新增视频发布进度/发布状态同步协议
    public static final String ACTION_GET_SYNC_PUBLISH_MULTIPLE_VIDEO = "syncGetPublishMultipleVideo";
    //1222.705【安全-非版本】新增网络身份认证处置
    public static final String ACTION_GET_NETWORK_IDENTITY_AUTHENTICATION = "networkIdentityAuthentication";
    public static final String ACTION_SYNC_WEB_TAG = "syncWebTagAction";
    //1224.701 同步工作环境地址认证连拍点位
    public static final String ACTION_SYNC_ADDRESS_AUTH_SHOT_POINT = "syncAddressAuthShotPoint";
    //1225.704【安全】安卓定位异常提示细化
    public static final String ACTION_GET_OPEN_GPS_SERVICE = "getOpenGpsService";
    public static final String ACTION_SYNC_WEB_VIEW_LOAD_TIME = "syncWebviewLoadTime";
    public static final String ACTION_SEND_NEBULA_AI_COMMAND = "sendNebulaAiCommand";

    public static final String ACTION_ADD_CHAT_WIDGET = "addChatWidget";
    public static final String ACTION_CLEAR_WEB_VIEW_STORAGE = "clearWebviewStorage";
    public static final String ACTION_GET_DEVICE_BASE_INFO = "getDeviceBaseInfo";

    public static final String ACTION_GET_DEVICE_INFO = "getDeviceInfo";
    public static final String ACTION_GET_WINDOW_INFO = "getWindowInfo";
    public static final String ACTION_GET_APP_BASE_INFO = "getAppBaseInfo";
    public static final String ACTION_HAS_BZB_APP_INSTALLED = "hasBzbAppInstalled";
    public static final String ACTION_GET_SYSTEM_SETTING = "getSystemSetting";
    public static final String ACTION_SET_SYSTEM_SETTING = "setSystemSetting";
    public static final String ACTION_OPEN_APP_AUTHORIZE_SETTING = "openAppAuthorizeSetting";
    public static final String ACTION_GET_NETWORK_TYPE_2 = "getNetworkType";
    public static final String ACTION_SET_SCREEN_ON = "setKeepScreenOn";
    public static final String ACTION_ON_KEYBOARD_HEIGHT_CHANGE = "onKeyboardHeightChange";
    public static final String ACTION_OFF_KEYBOARD_HEIGHT_CHANGE = "offKeyboardHeightChange";
    public static final String ACTION_HIDE_KEYBOARD = "hideKeyboard";
    public static final String ACTION_REQUEST_PAYMENT = "requestPayment";
    public static final String ACTION_SYNC_PRIVACY_PERSONALIZATION_SETTINGS = "syncPrivacyPersonalizationSettings";
    public static final String ACTION_GET_PRIVACY_PERSONALIZATION_SETTINGS = "getPrivacyPersonalizationSettings";
    public static final String ACTION_FILE_SYSTEM_ROOT = "getFileSystemRoot";
    public static final String ACTION_ON_USER_CAPTURE_SCREEN = "onUserCaptureScreen";
    public static final String ACTION_OFF_USER_CAPTURE_SCREEN = "offUserCaptureScreen";
    public static final String ACTION_SHOW_SHARE = "showShare";
    public static final String ACTION_HIDE_SHARE = "hideShare";
    public static final String ACTION_SHARE = "share";
    //endregion

    @NonNull
    private static final Map<String, Class<? extends WebAction>> ACTION_MAP = new ArrayMap<>();

    static {
        ACTION_MAP.put(ACTION_SHARE_PIC, SharePicAction.class);
        ACTION_MAP.put(ACTION_DOWNLOAD_PIC, DownloadPicAction.class);
        ACTION_MAP.put(ACTION_SHARE_URL, ShareUrlAction.class);
        ACTION_MAP.put(ACTION_SYNC_DATA, SyncDataAction.class);
        ACTION_MAP.put(ACTION_UPLOAD_PIC, UploadPicAction.class);
        ACTION_MAP.put(ACTION_GET_NETWORK_TYPE, GetNetworkTypeAction.class);
        ACTION_MAP.put(ACTION_IDLE_TIMER_DISABLED, IdleTimerDisabledAction.class);
        ACTION_MAP.put(ACTION_HIDE_NAV_BACK_BTN, HideNavBackBtnAction.class);
        ACTION_MAP.put(ACTION_SWIPE_BACK, SwipeBackAction.class);
        ACTION_MAP.put(ACTION_PERSON_INFO_COMPLETE, PersonInfoCompleteCloseAction.class);
        ACTION_MAP.put(ACTION_GET_WX_CODE, GetWxCodeAction.class);
        ACTION_MAP.put(ACTION_OPEN_TWLWAIT_FACETIME, OpenTWLWaitFacetimeAction.class);
        ACTION_MAP.put(ACTION_SHARE_WX_MINI_APP, ShareWxMiniAppAction.class);
        ACTION_MAP.put(ACTION_ZP_FACE_VECTOR, ZpFaceVectorAction.class);
        ACTION_MAP.put(ACTION_SHARE_FILE, ShareFileAction.class);
        ACTION_MAP.put(ACTION_SHARE_ENCRYPT_RESUME, ShareEncryptResumeAction.class);
        ACTION_MAP.put(ACTION_SWITCH_SCREEN_ORIENTATION, SwitchScreenOrientationAction.class);
        ACTION_MAP.put(ACTION_FULL_SCREEN, FullScreenAction.class);
        ACTION_MAP.put(ACTION_VIDEO_RECORD, VideoRecordAction.class);
        ACTION_MAP.put(ACTION_GET_FULL_SCREEN_STATUS, GetFullScreenStatusAction.class);
        ACTION_MAP.put(ACTION_GET_STATUS_BAR_HEIGHT, GetStatusBarHeightAction.class);
        ACTION_MAP.put(ACTION_GET_APP_NEW_STYLE, GetAppNewStyleAction.class);
        ACTION_MAP.put(ACTION_SHOOT_VR_PHOTOS, ShootVrPhotosAction.class);
        ACTION_MAP.put(ACTION_UPLOAD_VR_PHOTOS_FINISH, UploadVrPhotosFinishAction.class);
        ACTION_MAP.put(ACTION_SEND_TARGET_RESULTS, SendTargetResultsAction.class);
        ACTION_MAP.put(ACTION_VR_CHECKING_PREVIEW, VrCheckingPreviewAction.class);
        ACTION_MAP.put(ACTION_GET_BZB_APP_INSTALLED, GetBzbAppInstalledAction.class);
        ACTION_MAP.put(getZPwt, TokenAction.class);
        ACTION_MAP.put(ACTION_1107_REPORTED_F1_REQUEST, F1ReportedRcmdRequestAction.class);
        ACTION_MAP.put(ACTION_NOTIFY_BZB_UNPAID_ORDER_STATUS, NotifyBzbUnpaidOrderStatusAction.class);
        ACTION_MAP.put(ACTION_VERIFY_MOBILE_PHONE, VerifyMobilePhoneAction.class);
        ACTION_MAP.put(GET_SHOP_TITLE_NOTICE_RED_DOT, GetShopTitleNoticeRedDotAction.class);
        ACTION_MAP.put(H5LocalStorageValue, H5LocalStorageValue.class);

        //新postBZMessage 的action
        ACTION_MAP.put(FACE_DETECT_ACTION, ZpFaceCollectionAction.class);
        ACTION_MAP.put(UPLOAD_FILE_ACTION, UploadFileAction.class);
        ACTION_MAP.put(GET_UNREAD_CHAT_MESSAGE_NUM_ACTION, UnreadChatMessageNumAction.class);
        ACTION_MAP.put(GET_REBIND_VIDEO_POSITION, RebindVideoPositionAction.class);
        ACTION_MAP.put(ACTION_WX_OPEN_CUSTOMER_CHAT, ZpOpenWxCustomerChatAction.class);
        ACTION_MAP.put(ACTION_CLOSE_ALL_WEBVIEW, ZpCloseAllWebViewAction.class);
        ACTION_MAP.put(ACTION_REFRESH_WHEN_WEB_RESUME, ZpNotifyH5WebViewAppear.class);
        ACTION_MAP.put(ACTION_ADDRESS_VERIFY_INTERRUPT, SendInterruptAddressAuthAction.class);
        ACTION_MAP.put(ACTION_NOTIFY_GEEK_F1_REFRESH, NotifyGeekF1RefreshAction.class);
        ACTION_MAP.put(ACTION_UPDATE_SWITCH_STATUS, UpdateSwitchStatusAction.class);
        ACTION_MAP.put(ACTION_DOWNLOAD_VIDEO, DownloadVideoAction.class);
        ACTION_MAP.put(ACTION_ZP_SCORE_SEND_TO_MESSAGE, ZPScoreSendToMessageAction.class);
        ACTION_MAP.put(ACTION_GET_SYNC_PUBLISH_VIDEO_STATUS, SyncGetPublishVideoStatusAction.class);
        ACTION_MAP.put(ACTION_GET_SYNC_PUBLISH_MULTIPLE_VIDEO, SyncGetPublishMultipleVideoAction.class);
        ACTION_MAP.put(ACTION_GET_NETWORK_IDENTITY_AUTHENTICATION, NetworkIdentityAuthenticationAction.class);
        ACTION_MAP.put(ACTION_SYNC_WEB_TAG, SyncWebTagAction.class);
        ACTION_MAP.put(ACTION_SYNC_ADDRESS_AUTH_SHOT_POINT, SyncAddressAuthShotPointAction.class);
        ACTION_MAP.put(ACTION_SYNC_WEB_VIEW_LOAD_TIME, SyncWebViewLoadTimeAction.class);
        ACTION_MAP.put(ACTION_SEND_NEBULA_AI_COMMAND, SendNebulaAiCommandAction.class);
        ACTION_MAP.put(ACTION_GET_OPEN_GPS_SERVICE, GetOpenGpsServiceAction.class);
        ACTION_MAP.put(ACTION_ADD_CHAT_WIDGET, AddChatWidgetAction.class);
        ACTION_MAP.put(ACTION_CLEAR_WEB_VIEW_STORAGE, ClearWebViewStorageAction.class);
        ACTION_MAP.put(ACTION_GET_DEVICE_BASE_INFO, GetDeviceBaseInfoAction.class);
        ACTION_MAP.put(ACTION_SYNC_PRIVACY_PERSONALIZATION_SETTINGS, SyncPrivacyPersonalizationAction.class);
        ACTION_MAP.put(ACTION_GET_PRIVACY_PERSONALIZATION_SETTINGS, GetPrivacyPersonalizationSettingsAction.class);
        //基础api
        ACTION_MAP.put(ACTION_GET_DEVICE_INFO, GetDeviceInfoAction.class);
        ACTION_MAP.put(ACTION_GET_WINDOW_INFO, GetWindowInfoAction.class);
        ACTION_MAP.put(ACTION_GET_APP_BASE_INFO, GetAppBaseInfoAction.class);
        ACTION_MAP.put(ACTION_HAS_BZB_APP_INSTALLED, HasBzbAppInstalledAction.class);
        ACTION_MAP.put(ACTION_GET_SYSTEM_SETTING, GetSystemSettingAction.class);
        ACTION_MAP.put(ACTION_SET_SYSTEM_SETTING, SetSystemSettingAction.class);
        ACTION_MAP.put(ACTION_OPEN_APP_AUTHORIZE_SETTING, OpenAppAuthorizeSettingAction.class);
        ACTION_MAP.put(ACTION_GET_NETWORK_TYPE_2, GetNetworkTypeAction2.class);
        ACTION_MAP.put(SET_CLIPBOARD_DATA_ACTION, ZpSetClipboardDataAction.class);
        ACTION_MAP.put(GET_CLIPBOARD_DATA_ACTION, ZpGetClipboardDataAction.class);
        ACTION_MAP.put(ACTION_SET_SCREEN_ON, SetKeepScreenOnAction.class);
        ACTION_MAP.put(ACTION_ON_KEYBOARD_HEIGHT_CHANGE, OnKeyboardHeightChangeAction.class);
        ACTION_MAP.put(ACTION_OFF_KEYBOARD_HEIGHT_CHANGE, OffKeyboardHeightChangeAction.class);
        ACTION_MAP.put(ACTION_HIDE_KEYBOARD, HideKeyboardAction.class);
        ACTION_MAP.put(ACTION_REQUEST_PAYMENT, RequestPaymentAction.class);
        ACTION_MAP.put(ACTION_FILE_SYSTEM_ROOT, GetFileSystemRootAction.class);
        ACTION_MAP.put(ACTION_ON_USER_CAPTURE_SCREEN, OffAndOnUserCaptureScreenAction.class);
        ACTION_MAP.put(ACTION_OFF_USER_CAPTURE_SCREEN, OffAndOnUserCaptureScreenAction.class);
        ACTION_MAP.put(ACTION_SHOW_SHARE, ShowOrHideShareAction.class);
        ACTION_MAP.put(ACTION_HIDE_SHARE, ShowOrHideShareAction.class);
        ACTION_MAP.put(ACTION_SHARE, ShareAction.class);
    }

    @NonNull
    private final WebViewCommon webViewCommon;
    @NonNull
    private final List<Action> workingActions = new ArrayList<>();

    public JavascriptMessageHandler(@NonNull WebViewCommon webViewCommon) {
        this.webViewCommon = webViewCommon;
    }

    /**
     * 处理前端传输的JSON
     *
     * @param json {@link PostMessage}
     */
    public void postMessage(@NonNull String json) {
        TLog.info("postMessage", "postMessage() called with: json = [ %s ]", json);
        try {
            PostMessage postMessage = new Gson().fromJson(json, PostMessage.class);
            String actionName = postMessage.name;

            webViewCommon.collectJsApiCall(actionName);

            Class<? extends WebAction> actionCls = ACTION_MAP.get(actionName);
            if (actionCls != null && WebAction.class.isAssignableFrom(actionCls)) {
                Action action = findSingletonAction(actionCls);
                if (null == action) {
                    action = actionCls.getConstructor(WebViewCommon.class).newInstance(webViewCommon);
                    workingActions.add(action);
                }
                action.handle(postMessage);
            } else {
                toastError("内部错误，请稍后重试：" + actionName);
            }
        } catch (Exception e) {
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_CATCH_EXCEPTION, ApmAnalyticsAction.TYPE_WEB_VIEW_POST_MESSAGE)
                    .p2("postMessage")
                    .p3(Log.getStackTraceString(e))
                    .p4(json)
                    .reportNow();
            toastError("内部错误，请稍后重试：" + e.getMessage());
        }
    }

    /**
     * json 格式 http://hybrid-qa.weizhipin.com/guide/faceDetect/faceDetect.html#%E6%8E%A5%E5%8F%A3%E8%AF%B4%E6%98%8E
     */
    public void postBZMessage(@NonNull String json) {
        TLog.info("postBZMessage", "postBZMessage() called with: json = [ %s ]", json);
        try {
            JSONObject jsonObject = new JSONObject(json);
            String actionName = jsonObject.optString("name");
            String callbackName = jsonObject.optString("callbackName");
            JSONObject params = jsonObject.optJSONObject("params");

            webViewCommon.collectJsApiCall(actionName, true);

            Class<? extends WebAction> actionCls = ACTION_MAP.get(actionName);
            if (actionCls != null && WebAction.class.isAssignableFrom(actionCls)) {
                Action action = findSingletonAction(actionCls);
                if (null == action) {
                    action = actionCls.getConstructor(WebViewCommon.class).newInstance(webViewCommon);
                    workingActions.add(action);
                }
                if (action instanceof BZWebAction) {
                    BZWebAction bzWebAction = ((BZWebAction) action);
                    bzWebAction.parseMessage(params, actionName, callbackName);
                }
                if (action == null) {
                    load404Error(callbackName);
                }
            } else {
                if (actionCls == null) {
                    load404Error(callbackName);
                }
                toastError("内部错误，请稍后重试：" + actionName);
            }
        } catch (Exception e) {
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_CATCH_EXCEPTION, ApmAnalyticsAction.TYPE_WEB_VIEW_POST_MESSAGE)
                    .p2("postBZMessage")
                    .p3(Log.getStackTraceString(e))
                    .p4(json)
                    .reportNow();
            toastError("内部错误，请稍后重试：" + e.getMessage());
        }

    }

    private void load404Error(String callbackName) {
        if (webViewCommon == null) return;
        TLog.info("load404Error", "load404Error() called with: callbackName = [ %s ]", callbackName);
        JSONObject zpData = new JSONObject();
        JSONObject detailcode = new JSONObject();
        try {
            zpData.put("code", 404);
            zpData.put("message", "Unable to find this API");
            zpData.put("zpData", detailcode);
            StringBuffer jsBuffer = new StringBuffer();
            jsBuffer.append("javascript:");
            jsBuffer.append(callbackName);
            jsBuffer.append("(");
            jsBuffer.append(zpData.toString());
            jsBuffer.append(")");
            webViewCommon.getWebView().loadUrl(jsBuffer.toString());
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

    }

    private void toastError(@NonNull String msg) {
        if (BuildInfoUtils.isDebug()) {
            ToastUtils.showText(msg);
        }
    }

    public void onDestroy() {
        for (Action workingAction : workingActions) {
            workingAction.release();
        }
        workingActions.clear();
    }

    public void onResume() {
        for (Action workingAction : workingActions) {
            workingAction.onResume();
        }
    }

    public void cancelUploadVideo() {
        if (LList.isEmpty(workingActions)) return;
        for (Action workingAction : workingActions) {
            if (workingAction instanceof UploadPicAction) {
                ((UploadPicAction) workingAction).cancelUploadVideo();
            }

            if (workingAction instanceof UploadFileAction) {
                ((UploadFileAction) workingAction).cancelUploadVideo();
            }
        }
    }

    public Action findSingletonAction(Class<? extends WebAction> actionCls) {
        Action sAction = null;
        for (Action action : workingActions) {
            if (null != action && action.isSingleton() && action.getClass() == actionCls) {
                sAction = action;
                break;
            }
        }
        return sAction;
    }
}
