package com.hpbr.bosszhipin.module.webview.jsi.thirdservice.share;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.util.Base64;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.android.dingtalk.share.ddsharemodule.DDShareApiFactory;
import com.android.dingtalk.share.ddsharemodule.IDDShareApi;
import com.android.dingtalk.share.ddsharemodule.message.DDMediaMessage;
import com.android.dingtalk.share.ddsharemodule.message.DDWebpageMessage;
import com.android.dingtalk.share.ddsharemodule.message.SendMessageToDD;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.DownloadPhotoCommon;
import com.hpbr.bosszhipin.config.OtherConfig;
import com.hpbr.bosszhipin.module.webview.bean.ShareMessage;
import com.monch.lbase.util.LText;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.twl.ui.ToastUtils;
import com.twl.utils.ConverterUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class WebViewShareHelper {
    public static final String MESSAGE_ERROR = "message_error";
    private static final String DOWNLOAD_ERROR = "download_error";
    private static final String SHARE_ERROR = "share_error";

    public static final long SIZE_10K = 10 * 1024L;

    public static final long SIZE_64K = 64 * 1024L;

    public static final long SIZE_128K = 128 * 1024L;

    public static final long SIZE_250K = 250 * 1024L;

    public static final String FORMAT_DING_TALK_LINK = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true&_dt_no_comment=false";

    /**
     * 分享到微信
     */
    public static void shareToWechat(@NonNull ShareMessage shareMessage, @NonNull ShareCallback shareCallback) {
        IWXAPI api = WXAPIFactory.createWXAPI(App.getAppContext().getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        api.registerApp(OtherConfig.WE_CHAT_APP_ID);
        if (!api.isWXAppInstalled()) {
            showToast(R.string.string_no_install_wechat);
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            return;
        }
        if (shareMessage.type == ShareMessage.ShareType.TEXT) {
            // 分享纯文字
            shareWechatText(shareMessage, api, shareCallback);
        } else if (shareMessage.isImageRequired() && LText.empty(shareMessage.imageUrl)) {
            // 分享带有图片的方式但没有图片链接
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share wechat image empty");
        } else {
            // 先下载图片后再分享
            long maxStreamLength = WebViewShareHelper.getMaxStreamLength(shareMessage.type);
            WebViewShareHelper.downloadThumbnail(shareMessage.imageUrl, shareMessage.imageUrlType, maxStreamLength, !shareMessage.isImageRequired(), new WebViewShareHelper.DownloadCallback() {
                @Override
                public void onSuccess(byte[] bitmapArray) {
                    if (shareMessage.type == ShareMessage.ShareType.WEBPAGE) {
                        // 微信图文链接
                        shareWechatWebpage(shareMessage, bitmapArray, api, shareCallback);
                    } else if (shareMessage.type == ShareMessage.ShareType.IMAGE) {
                        // 微信图片
                        shareWechatImage(shareMessage, bitmapArray, api, shareCallback);
                    } else if (shareMessage.type == ShareMessage.ShareType.MINI_PROGRAM) {
                        // 微信小程序
                        shareWechatMiniProgram(shareMessage, bitmapArray, api, shareCallback);
                    } else {
                        shareCallback.onError();
                        reportError(MESSAGE_ERROR, "unknown wechat type: " + shareMessage.type);
                    }
                }

                @Override
                public void onFailed() {
                    shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
                    reportError(DOWNLOAD_ERROR, "download wechat image failed, imageUrlType = " +  shareMessage.imageUrlType);
                }
            });
        }
    }

    /**
     * 分享到短信
     */
    public static void shareToSms(@NonNull Activity activity, @Nullable String smsTitle, @NonNull ShareCallback shareCallback) {
        if (LText.empty(smsTitle)) {
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share sms title empty");
            return;
        }

        Uri uri = Uri.parse("smsto:");
        Intent intent = new Intent(Intent.ACTION_SENDTO, uri);
        intent.putExtra("sms_body", smsTitle);
        try {
            activity.startActivity(intent);
            shareCallback.onComplete(ShareMessage.Provider.SMS, true);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.SMS, false);
            reportError(SHARE_ERROR, "share sms failed", e.getMessage());
        }
    }

    /**
     * 分享到钉钉
     */
    public static void shareToDingTalk(@NonNull Activity activity, @NonNull ShareMessage shareMessage, @NonNull ShareCallback shareCallback) {
        IDDShareApi api = DDShareApiFactory.createDDShareApi(activity, OtherConfig.DING_TALK_APP_ID, false);
        if (!api.isDDAppInstalled()) {
            showToast(R.string.string_install_dingding_first);
            shareCallback.onComplete(ShareMessage.Provider.DING_TALK, false);
            return;
        }

        if (!api.isDDSupportAPI()) {
            showToast(R.string.string_dingding_not_support_share);
            shareCallback.onComplete(ShareMessage.Provider.DING_TALK, false);
            return;
        }

        if (shareMessage.type == ShareMessage.ShareType.WEBPAGE) {
            long maxStreamLength = WebViewShareHelper.getMaxStreamLength(shareMessage.type);
            WebViewShareHelper.downloadThumbnail(shareMessage.imageUrl, shareMessage.imageUrlType, maxStreamLength, true, new WebViewShareHelper.DownloadCallback() {
                @Override
                public void onSuccess(byte[] bitmapArray) {
                    shareDingTalkWebpage(shareMessage, bitmapArray, api, shareCallback);
                }

                @Override
                public void onFailed() {
                    shareCallback.onComplete(ShareMessage.Provider.DING_TALK, false);
                    reportError(DOWNLOAD_ERROR, "download ding talk image failed, imageUrlType = " +  shareMessage.imageUrlType);
                }
            });
        } else {
            // 暂不支持其他类型
            shareCallback.onError();
        }
    }

    /**
     * 分享微信-图文链接
     */
    private static void shareWechatWebpage(@NonNull ShareMessage shareMessage, @NonNull byte[] bitmapArray, @NonNull IWXAPI api, @NonNull ShareCallback shareCallback) {
        String href = shareMessage.href;
        if (LText.empty(href)) {
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share wechat link empty");
            return;
        }

        byte[] textBytes = href.getBytes(StandardCharsets.UTF_8);
        if (textBytes.length > WebViewShareHelper.SIZE_10K) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(MESSAGE_ERROR, "share wechat link too long");
            return;
        }

        WXWebpageObject object = new WXWebpageObject(href);
        WXMediaMessage msg = new WXMediaMessage(object);
        msg.title = shareMessage.title;
        msg.description = shareMessage.summary;
        msg.thumbData = bitmapArray;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("web");
        req.scene = shareMessage.getWechatScene();
        req.message = msg;
        try {
            boolean shareSuccess = api.sendReq(req);
            if (!shareSuccess) {
                reportError(SHARE_ERROR, "share wechat webpage failed");
            }
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, shareSuccess);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(SHARE_ERROR, "share wechat webpage failed", e.getMessage());
        }
    }

    /**
     * 微信分享-文字
     */
    private static void shareWechatText(@NonNull ShareMessage shareMessage, @NonNull IWXAPI api, @NonNull ShareCallback shareCallback) {
        String shareText = shareMessage.title;
        if (LText.empty(shareText)) {
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share wechat text empty");
            return;
        }

        byte[] textBytes = shareText.getBytes(StandardCharsets.UTF_8);
        if (textBytes.length > WebViewShareHelper.SIZE_10K) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(MESSAGE_ERROR, "share wechat text too long");
            return;
        }

        WXTextObject textObj = new WXTextObject(shareMessage.title);
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = textObj;
        msg.description = shareMessage.title;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("text");
        req.message = msg;
        req.scene = shareMessage.getWechatScene();
        try {
            boolean shareSuccess = api.sendReq(req);
            if (!shareSuccess) {
                reportError(SHARE_ERROR, "share wechat text failed");
            }
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, shareSuccess);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(SHARE_ERROR, "share wechat text failed", e.getMessage());
        }
    }

    /**
     * 微信分享-图片
     */
    private static void shareWechatImage(@NonNull ShareMessage shareMessage, byte[] bitmapArray, @NonNull IWXAPI api, @NonNull ShareCallback shareCallback) {
        WXImageObject imgObj = new WXImageObject(bitmapArray);
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;
        msg.thumbData = bitmapArray;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = shareMessage.getWechatScene();
        try {
            boolean shareSuccess = api.sendReq(req);
            if (!shareSuccess) {
                reportError(SHARE_ERROR, "share wechat image failed");
            }
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, shareSuccess);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(SHARE_ERROR, "share wechat image failed", e.getMessage());
        }
    }

    /**
     * 分享微信-小程序
     */
    private static void shareWechatMiniProgram(@NonNull ShareMessage shareMessage, byte[] bitmapArray, @NonNull IWXAPI api, @NonNull ShareCallback shareCallback) {
        if (shareMessage.miniProgram == null || !shareMessage.miniProgram.isValid()) {
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share wechat mini program invalid");
            return;
        }

        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
        miniProgramObj.webpageUrl = LText.empty(shareMessage.href) ? "https://m.zhipin.com/" : shareMessage.href;
        miniProgramObj.miniprogramType = shareMessage.miniProgram.type;
        miniProgramObj.userName = shareMessage.miniProgram.id;
        miniProgramObj.path = shareMessage.miniProgram.path;

        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        msg.title = shareMessage.title;
        msg.description = shareMessage.summary;
        msg.thumbData = bitmapArray;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("miniprogram");
        req.message = msg;
        req.scene = shareMessage.getWechatScene();

        try {
            boolean shareSuccess = api.sendReq(req);
            if (!shareSuccess) {
                reportError(SHARE_ERROR, "share wechat mini program failed");
            }
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, shareSuccess);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.WE_CHAT, false);
            reportError(SHARE_ERROR, "share wechat mini program failed", e.getMessage());
        }
    }

    /**
     * 分享钉钉-图文链接
     */
    private static void shareDingTalkWebpage(@NonNull ShareMessage message, byte[] bitmapArray, @NonNull IDDShareApi api, @NonNull ShareCallback shareCallback) {
        if (LText.empty(message.href)) {
            shareCallback.onError();
            reportError(MESSAGE_ERROR, "share ding talk link empty");
            return;
        }

        DDWebpageMessage webPageObject = new DDWebpageMessage();
        try {
            webPageObject.mUrl = String.format(WebViewShareHelper.FORMAT_DING_TALK_LINK, URLEncoder.encode(message.href, "UTF-8"));
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.DING_TALK, false);
            reportError(MESSAGE_ERROR, "share ding talk webpage failed", e.getMessage());
        }

        DDMediaMessage webMessage = new DDMediaMessage();
        webMessage.mMediaObject = webPageObject;
        webMessage.mTitle = message.title;
        webMessage.mContent = message.summary;
        webMessage.mThumbData = bitmapArray;

        SendMessageToDD.Req webReq = new SendMessageToDD.Req();
        webReq.mMediaMessage = webMessage;
        try {
            boolean shareSuccess = api.sendReq(webReq);
            if (!shareSuccess) {
                reportError(SHARE_ERROR, "share ding talk webpage failed");
            }
            shareCallback.onComplete(ShareMessage.Provider.DING_TALK, shareSuccess);
        } catch (Exception e) {
            shareCallback.onComplete(ShareMessage.Provider.DING_TALK, false);
            reportError(SHARE_ERROR, "share ding talk webpage failed", e.getMessage());
        }
    }

    /**
     * 下载缩略图
     * @param imageUrl 缩略图链接
     * @param imageUrlType 缩略图链接类型 0-http 1-base64
     * @param maxStreamLength 最大流长度
     * @param useDefault 下载失败时是否使用默认兜底图片
     * @param callback 回调
     */
    private static void downloadThumbnail(@NonNull String imageUrl, int imageUrlType, long maxStreamLength, boolean useDefault, @NonNull DownloadCallback callback) {
        if (imageUrlType == ShareMessage.ImageType.BASE64) {
            try {
                String dataPart = imageUrl;
                if (imageUrl.contains(",")) {
                    dataPart = imageUrl.substring(imageUrl.indexOf(",") + 1);
                }

                byte[] bitmapArray = Base64.decode(dataPart, Base64.DEFAULT);
                Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
                if (bitmap != null) {
                    callback.onSuccess(compressBitmap(bitmap, maxStreamLength));
                } else if (useDefault) {
                    callback.onSuccess(getDefaultImage());
                } else {
                    callback.onFailed();
                }
            } catch (Exception e) {
                if (useDefault) {
                    callback.onSuccess(getDefaultImage());
                } else {
                    callback.onFailed();
                }
            }
        } else {
            DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
            downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
                @Override
                public void onDownloadComplete(Bitmap bitmap) {
                    if (bitmap != null) {
                        callback.onSuccess(compressBitmap(bitmap, maxStreamLength));
                    } else if (useDefault) {
                        callback.onSuccess(getDefaultImage());
                    } else {
                        callback.onFailed();
                    }
                }

                @Override
                public void onDownloadFailed() {
                    if (useDefault) {
                        callback.onSuccess(getDefaultImage());
                    } else {
                        callback.onFailed();
                    }
                }

            });
            downloadPhotoCommon.onNewDownloadTask(imageUrl);
        }
    }

    private static byte[] getDefaultImage() {
        Bitmap bitmap = BitmapFactory.decodeResource(App.getAppContext().getResources(), R.mipmap.logo_icon);
        return ConverterUtils.bitmap2Bytes(bitmap);
    }

    /**
     * 压缩图片
     */
    private static byte[] compressBitmap(@NonNull Bitmap bitmap, long maxStreamLength) {
        long streamLength = maxStreamLength;
        int compressQuality = 105;
        ByteArrayOutputStream bmpStream = new ByteArrayOutputStream();
        while (streamLength >= maxStreamLength && compressQuality > 5) {
            try {
                bmpStream.flush();
                bmpStream.reset();
            } catch (IOException e) {
                // ignore
            }
            compressQuality -= 5;
            bitmap.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpStream);
            byte[] bmpPicByteArray = bmpStream.toByteArray();
            streamLength = bmpPicByteArray.length;
        }
        return bmpStream.toByteArray();
    }

    private static String buildTransaction(@NonNull String type) {
        return type + System.currentTimeMillis();
    }

    private static long getMaxStreamLength(int shareType) {
        switch (shareType) {
            case ShareMessage.ShareType.WEBPAGE:
                return WebViewShareHelper.SIZE_64K;
            case ShareMessage.ShareType.IMAGE:
                return WebViewShareHelper.SIZE_250K;
            default:
                return WebViewShareHelper.SIZE_128K;
        }
    }

    public static void reportError(@NonNull String type, @NonNull String errorMsg) {
        reportError(type, errorMsg, null);
    }

    public static void reportError(@NonNull String type, @NonNull String errorMsg, @Nullable String extra) {
        showToast(R.string.string_share_failed);
        ApmAnalyzer apmAnalyzer = ApmAnalyzer.create().action("action_h5_share_error", type)
                .p2(errorMsg); // 异常信息
        if (LText.notEmpty(extra)) {
            apmAnalyzer.p3(extra);
        }
        apmAnalyzer.report();
    }

    private static void showToast(@StringRes int stringResId) {
        App.get().getMainHandler().post(() -> ToastUtils.showText(stringResId));
    }

    public interface DownloadCallback {
        void onSuccess(byte[] bitmapArray);
        void onFailed();
    }

    public interface ShareCallback {
        void onComplete(@NonNull String provider, boolean success); // 分享完成
        void onError(); // H5参数错误
    }
}
