package com.hpbr.bosszhipin.module.commend.presenter;

import com.hpbr.bosszhipin.module.commend.entity.FilterBean;

import java.util.ArrayList;

/**
 * Author: <PERSON>Y<PERSON>
 * Date: 2017/9/14.
 */
public interface ISearchAdvancedResultListener {

    void initSearchText(String query);

    void initCity(String city);

    void initSearchResultArrange(int selectArrange);

    void initPosition(String jobName);

    void initCondition(ArrayList<FilterBean> filterBeans);

    void listRefreshing();
}
