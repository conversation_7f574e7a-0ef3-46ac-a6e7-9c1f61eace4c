package com.hpbr.bosszhipin.module.map.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.route.BusPath;
import com.amap.api.services.route.BusStep;
import com.amap.api.services.route.RouteBusLineItem;
import com.amap.api.services.route.RouteRailwayItem;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.map.activity.GeekRoute1004NewActivity;
import com.hpbr.bosszhipin.module.map.bean.ResolveInfoBean;
import com.hpbr.bosszhipin.module.map.route.ChString;
import com.hpbr.bosszhipin.utils.AppInfoUtils;
import com.hpbr.bosszhipin.utils.AppInstallUtils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.bean.BaseServerBean;

import java.net.URISyntaxException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 地图工具类
 *
 * <AUTHOR>
 */
public class AMapUtil {

    public static String getFriendlyLength(int lenMeter) {
        if (lenMeter > 10000) // 10 km
        {
            int dis = lenMeter / 1000;
            return dis + ChString.Kilometer;
        }

        if (lenMeter > 1000) {
            float dis = (float) lenMeter / 1000;
            DecimalFormat fnum = new DecimalFormat("##0.0");
            String dstr = fnum.format(dis);
            return dstr + ChString.Kilometer;
        }

        if (lenMeter > 100) {
            int dis = lenMeter / 50 * 50;
            return dis + ChString.Meter;
        }

        int dis = lenMeter / 10 * 10;
        if (dis == 0) {
            dis = 10;
        }

        return dis + ChString.Meter;
    }

    public static String getFriendlyLength2(int lenMeter) {
        if (lenMeter > 10000) // 10 km
        {
            int dis = lenMeter / 1000;
            return dis + ChString.Kilometer_KM;
        }

        if (lenMeter > 1000) {
            float dis = (float) lenMeter / 1000;
            DecimalFormat fnum = new DecimalFormat("##0.0");
            String dstr = fnum.format(dis);
            return dstr + ChString.Kilometer_KM;
        }

        if (lenMeter > 100) {
            int dis = lenMeter / 50 * 50;
            return dis + ChString.Meter_M;
        }

        int dis = lenMeter / 10 * 10;
        if (dis == 0) {
            dis = 10;
        }

        return dis + ChString.Meter_M;
    }

    /**
     * 把LatLng对象转化为LatLonPoint对象
     */
    public static LatLonPoint convertToLatLonPoint(LatLng latlon) {
        return new LatLonPoint(latlon.latitude, latlon.longitude);
    }

    /**
     * 把LatLonPoint对象转化为LatLon对象
     */
    public static LatLng convertToLatLng(LatLonPoint latLonPoint) {
        if(latLonPoint!=null){
            return new LatLng(latLonPoint.getLatitude(), latLonPoint.getLongitude());
        }else{
            return new LatLng(0, 0);
        }

    }

    /**
     * 把集合体的LatLonPoint转化为集合体的LatLng
     */
    public static ArrayList<LatLng> convertArrList(List<LatLonPoint> shapes) {
        ArrayList<LatLng> lineShapes = new ArrayList<LatLng>();
        for (LatLonPoint point : shapes) {
            LatLng latLngTemp = AMapUtil.convertToLatLng(point);
            lineShapes.add(latLngTemp);
        }
        return lineShapes;
    }

    public static String getSimpleSecondTime(int second) {
        if (second >= 60) {
            int minute = second / 60;
            return minute + "分钟";
        }
        return second + "秒";
    }

    public static String getFriendlyTime(int second) {
        if (second > 3600) {
            int hour = second / 3600;
            int minute = (second % 3600) / 60;
            return hour + "小时" + minute + "分钟";
        }
        if (second >= 60) {
            int minute = second / 60;
            return minute + "分钟";
        }
        return second + "秒";
    }

    public static String getSimpleFriendlyTime(int second) {
        if (second > 3600) {
            DecimalFormat fnum = new DecimalFormat("##0.0");
            String dstr = fnum.format((float) second / 3600);
            return dstr + "小时";
        }
        if (second >= 60) {
            int minute = second / 60;
            return minute + "分钟";
        }
        return second + "秒";
    }

    public static String getBusPathTitle(BusPath busPath) {
        if (busPath == null) {
            return "";
        }
        List<BusStep> busSetps = busPath.getSteps();
        if (busSetps == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (BusStep busStep : busSetps) {
            StringBuffer title = new StringBuffer();
            if (busStep.getBusLines().size() > 0) {
                for (RouteBusLineItem busline : busStep.getBusLines()) {
                    if (busline == null) {
                        continue;
                    }

                    String buslineName = getSimpleBusLineName(busline.getBusLineName());
                    title.append(buslineName);
                    title.append(" / ");
                }
                sb.append(title.substring(0, title.length() - 3));
                sb.append(" > ");
            }
            if (busStep.getRailway() != null) {
                RouteRailwayItem railway = busStep.getRailway();
                sb.append(railway.getTrip() + "(" + railway.getDeparturestop().getName()
                        + " - " + railway.getArrivalstop().getName() + ")");
                sb.append(" > ");
            }
        }
        return sb.substring(0, sb.length() - 3);
    }

    public static String getBusPathDes(BusPath busPath) {
        if (busPath == null) {
            return "";
        }
        long second = busPath.getDuration();
        String time = getFriendlyTime((int) second);
        float subDistance = busPath.getDistance();
        String subDis = getFriendlyLength((int) subDistance);
        float walkDistance = busPath.getWalkDistance();
        String walkDis = getFriendlyLength((int) walkDistance);
        return time + " | " + subDis + " | 步行" + walkDis;
    }

    public static String getTotalBusStation(BusPath busPath) {
        if (busPath == null) {
            return "";
        }
        int count = 0;
        for (int i = 0; i < busPath.getSteps().size(); i++) {
            BusStep busStep = busPath.getSteps().get(i);
            if (!LList.isEmpty(busStep.getBusLines())) {
                count += busStep.getBusLines().get(0).getPassStationNum() + 1; // +1是因为 它除去了起点终点站，如果是1站的话 返回0
            }
        }
        return count + "站";
    }

    public static String getSimpleBusLineName(String busLineName) {
        if (busLineName == null) {
            return "";
        }
        return busLineName.replaceAll("\\(.*?\\)", "");
    }

    /**
     * 由于各种地图的Uri不同，这里用了四种地图的Uri解析
     * 地图的先后顺序为高德地图->百度地图->腾讯地图->google地图
     */
    public static List<ResolveInfoBean> resolveMapApps(LatLonPoint mLatLonPoint, String address, PackageManager pm) {
        LatLonPoint baiDuPoint = gaode2baidu(mLatLonPoint);
        String aMapUriStr = "androidamap://viewMap?sourceApplication=growth_boss&poiname=" + address
                + "&lat=" + mLatLonPoint.getLatitude() + "&lon=" + mLatLonPoint.getLongitude()
                + "&dev=0";
        String baiDuUriStr = "intent://map/marker?location=" + baiDuPoint.getLatitude() + ","
                + baiDuPoint.getLongitude() + "&title=" + address + "&content=" + address
                + "&src=BOSS直聘|BOSS直聘#Intent;scheme=bdapp;package=com.baidu.BaiduMap;end";
        String googleUriStr = "geo:" + mLatLonPoint.getLatitude() + ","
                + mLatLonPoint.getLongitude();

        List<ResolveInfoBean> resolveInfoBeans = new ArrayList<>();

        Intent aMapIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(aMapUriStr));
        aMapIntent.addCategory(Intent.CATEGORY_DEFAULT);
        aMapIntent.setPackage("com.autonavi.minimap");
        List<ResolveInfoBean> aMapInfoBean = ResolveInfoBean.fromResolveInfo(
                pm.queryIntentActivities(aMapIntent,
                        PackageManager.GET_RESOLVED_FILTER), aMapIntent);
        if (aMapInfoBean != null && !aMapInfoBean.isEmpty()) {
            resolveInfoBeans.addAll(aMapInfoBean);
        }

        List<ResolveInfoBean> baiDuInfo;
        try {
            Intent baiDuIntent = Intent.parseUri(baiDuUriStr, 0);
            baiDuIntent.setPackage("com.baidu.BaiduMap");
            baiDuIntent.addCategory(Intent.CATEGORY_DEFAULT);
            baiDuInfo = ResolveInfoBean.fromResolveInfo(
                    pm.queryIntentActivities(baiDuIntent,
                            PackageManager.GET_RESOLVED_FILTER), baiDuIntent);
            if (baiDuInfo != null && !baiDuInfo.isEmpty()) {
                resolveInfoBeans.addAll(baiDuInfo);
            }
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        Intent tencentIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(googleUriStr));
        tencentIntent.addCategory(Intent.CATEGORY_DEFAULT);
        tencentIntent.setPackage("com.tencent.map");
        List<ResolveInfoBean> tencentInfo = ResolveInfoBean.fromResolveInfo(
                pm.queryIntentActivities(tencentIntent,
                        PackageManager.GET_RESOLVED_FILTER), tencentIntent);
        if (tencentInfo != null && !tencentInfo.isEmpty()) {
            resolveInfoBeans.addAll(tencentInfo);
        }

        Intent googleIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(googleUriStr));
        googleIntent.addCategory(Intent.CATEGORY_DEFAULT);
        googleIntent.setPackage("com.google.android.apps.maps");
        List<ResolveInfoBean> googleInfo = ResolveInfoBean.fromResolveInfo(
                pm.queryIntentActivities(googleIntent,
                        PackageManager.GET_RESOLVED_FILTER), googleIntent);
        if (googleInfo != null && !googleInfo.isEmpty()) {
            resolveInfoBeans.addAll(googleInfo);
        }

        if (resolveInfoBeans.isEmpty()) {
            return null;
        }

        return resolveInfoBeans;
    }

    public static class MapNavationLocation extends BaseServerBean {

        /**
         * sid->起点的POI ID
         * * slat->起点纬度。
         * * slon->起点经度
         * * sname->起点名称
         */

        private static final long serialVersionUID = -4765999235433627219L;


        private double slat;

        private double slon;

        private String sname;

        private String goiId;

        public void setGoiId(String goiId) {
            this.goiId = goiId;
        }

        public String getGoiId() {
            return goiId;
        }


        public void setSlat(double slat) {
            this.slat = slat;
        }

        public void setSlon(double slon) {
            this.slon = slon;
        }

        public void setSname(String sname) {
            this.sname = sname;
        }


        public double getSlat() {
            return slat;
        }

        public double getSlon() {
            return slon;
        }

        public String getSname() {
            return sname;
        }

        @Override
        public String toString() {
            return "MapNavationLocation{" +
                    "slat=" + slat +
                    ", slon=" + slon +
                    ", sname='" + sname + '\'' +
                    ", goiId='" + goiId + '\'' +
                    '}';
        }
    }

    /**
     * @param context        上下文
     * @param startLocation  出发点的信息
     * @param targetLocation 目标点的信息
     * @param type           0（驾车）= 1（公交）= 2（步行）= 3（骑行）= 4（火车）= 5（长途客车）
     * @return
     */
    public static List<AppInfoUtils.AppInfoEntity> resolveMapApps(Context context,
                                                                  @Nullable MapNavationLocation startLocation,
                                                                  @NonNull MapNavationLocation targetLocation,
                                                                  String type) {
        List<AppInfoUtils.AppInfoEntity> appList = new ArrayList<>();

        if (AppInstallUtils.checkApkExist(context, "com.autonavi.minimap")) {

            AppInfoUtils.AppInfoEntity entity = AppInfoUtils.getAppInfo(context, "com.autonavi.minimap");
            /**
             * https://lbs.amap.com/api/amap-mobile/guide/android/route
             *
             * route->服务类型
             * sourceApplication->第三方调用应用名称。如 amap
             * sid->起点的POI ID
             * slat->起点纬度。
             * slon->起点经度
             * sname->起点名称
             * did->终点的POI ID
             * dlat->终点纬度
             * dlon->终点经度
             * dname->终点名称
             * dev->起终点是否偏移(0:lat 和 lon 是已经加密后的,不需要国测加密; 1:需要国测加密)
             * t->t = 0（驾车）= 1（公交）= 2（步行）= 3（骑行）= 4（火车）= 5（长途客车）
             *  （骑行仅在V7.8.8以上版本支持）
             *  rideType->仅当 t = 3 时该参数生效。rideType = elebike    电动车，rideType = bike/为空 自行车
             * （电动车规划仅在V8.65.0及以上版本支持）
             */
            String sid = startLocation != null ? startLocation.getGoiId() : "";
            double slat = startLocation != null ? startLocation.getSlat() : 0;
            double slon = startLocation != null ? startLocation.getSlon() : 0;
            String sname = startLocation != null ? startLocation.getSname() : "";
            StringBuilder aMapUriStr = new StringBuilder();
            if(slat==0 || slon == 0 ){
                aMapUriStr.append("amapuri://route/plan/?")

                        .append("did=").append(targetLocation.getGoiId())
                        .append("&dlat=").append(targetLocation.getSlat())
                        .append("&dlon=").append(targetLocation.getSlon())
                        .append("&dname=").append(targetLocation.getSname())
                        .append("&dev=0")
                        .append("&t=").append(type);
            }else{
                aMapUriStr.append("amapuri://route/plan/?")
                        .append("sid=").append(sid) // 如果起点为空 下面这4个参数不要传  不然高德会报错
                        .append("&slat=").append(slat)
                        .append("&slon=").append(slon)
                        .append("&sname=").append(sname)

                        .append("&did=").append(targetLocation.getGoiId())
                        .append("&dlat=").append(targetLocation.getSlat())
                        .append("&dlon=").append(targetLocation.getSlon())
                        .append("&dname=").append(targetLocation.getSname())
                        .append("&dev=0")
                        .append("&t=").append(type);
            }

            /*String aMapUriStr = "amapuri://route/plan/?" +
                    "sid=" + sid +
                    "&slat=" + slat +
                    "&slon=" + slon +
                    "&sname=" + sname +
                    "&did=" + targetLocation.getGoiId() +
                    "&dlat=" + targetLocation.getSlat() +
                    "&dlon=" + targetLocation.getSlon() +
                    "&dname=" + targetLocation.getSname() +
                    "&dev=0" +
                    "&t=" + type;*/
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(aMapUriStr.toString()));
            intent.setPackage("com.autonavi.minimap");
            intent.addCategory(Intent.CATEGORY_DEFAULT);

            entity.mapIntent = intent;
            appList.add(entity);
        }

        double slat = targetLocation.getSlat();
        double slon = targetLocation.getSlon();
        String address = targetLocation.getSname();

        final LatLonPoint mLatLonPoint = new LatLonPoint(slat, slon);

        if (AppInstallUtils.checkApkExist(context, "com.baidu.BaiduMap")) {
            LatLonPoint baiDuPoint = gaode2baidu(mLatLonPoint);
            AppInfoUtils.AppInfoEntity entity = AppInfoUtils.getAppInfo(context, "com.baidu.BaiduMap");
            String baiDuUriStr = "intent://map/marker?location=" + baiDuPoint.getLatitude() + ","
                    + baiDuPoint.getLongitude() + "&title=" + address + "&content=" + address
                    + "&src=BOSS直聘|BOSS直聘#Intent;scheme=bdapp;package=com.baidu.BaiduMap;end";
            Intent intent;
            try {
                intent = Intent.parseUri(baiDuUriStr, 0);
                intent.setPackage("com.baidu.BaiduMap");
                intent.addCategory(Intent.CATEGORY_DEFAULT);
                entity.mapIntent = intent;
                appList.add(entity);
            } catch (URISyntaxException e) {
                e.printStackTrace();
                TLog.error("GeekRouteMap", "baidu intent error = %s",e);
            }
        }

        if (AppInstallUtils.checkApkExist(context, "com.tencent.map")) {

            AppInfoUtils.AppInfoEntity entity = AppInfoUtils.getAppInfo(context, "com.tencent.map");
            //https://lbs.qq.com/webApi/uriV1/uriGuide/uriMobilePoisearch
            String googleUriStr = "qqmap://map/search&keyword=" + address + "&center=" + mLatLonPoint.getLatitude() + "," + mLatLonPoint.getLongitude() + "&radius=800";
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(googleUriStr));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setPackage("com.tencent.map");
            entity.mapIntent = intent;
            appList.add(entity);

        }

        if (AppInstallUtils.checkApkExist(context, "com.google.android.apps.maps")) {
            AppInfoUtils.AppInfoEntity entity = AppInfoUtils.getAppInfo(context, "com.google.android.apps.maps");

            String googleUriStr = "geo:" + mLatLonPoint.getLatitude() + ","
                    + mLatLonPoint.getLongitude();
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(googleUriStr));
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setPackage("com.google.android.apps.maps");
            entity.mapIntent = intent;
            appList.add(entity);
        }
        return appList;
    }

    /**
     * AMap transform to BaiDuMap location
     *
     * @param gaode AMap location
     * @return BaiDuMap location
     */
    private static LatLonPoint gaode2baidu(LatLonPoint gaode) {
        double x = gaode.getLongitude(), y = gaode.getLatitude();
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * Math.PI);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * Math.PI);
        double bd_lon = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        return new LatLonPoint(bd_lat, bd_lon);
    }

    public static boolean isLocServiceEnable(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    public static BitmapDescriptor getEndBitmapDescriptor(Context context, String endAddress) {
        if (TextUtils.isEmpty(endAddress)) {
            return BitmapDescriptorFactory.fromResource(R.mipmap.amap_end_point);
        } else {
            View view2 = LayoutInflater.from(context).inflate(R.layout.map_geek_route_info_window, null, false);
            TextView mStartInfoTv2 = view2.findViewById(R.id.tv_content);
            ImageView pointIv = view2.findViewById(R.id.point_iv);
            pointIv.setImageResource(R.mipmap.amap_end_point);
            mStartInfoTv2.setText(endAddress);
            return BitmapDescriptorFactory.fromView(view2);
        }

    }

    public static BitmapDescriptor getStartBitmapDescriptor(Context context, String startAddress) {
        if (TextUtils.isEmpty(startAddress)) {
            return BitmapDescriptorFactory.fromResource(R.mipmap.amap_start_point);
        } else {
            View view = LayoutInflater.from(context).inflate(R.layout.map_geek_route_info_window, null, false);
            TextView mStartInfoTv = view.findViewById(R.id.tv_content);
            ImageView pointIv = view.findViewById(R.id.point_iv);
            pointIv.setImageResource(R.mipmap.amap_start_point);
            mStartInfoTv.setText(startAddress);
            return BitmapDescriptorFactory.fromView(view);
        }

    }

    public static boolean poiItemEquals(PoiItem p1,PoiItem p2){
        if(p1!=null && p2!=null && p1.getLatLonPoint()!=null&& p2.getLatLonPoint()!=null){
           return LText.equal(p1.getTitle(),p2.getTitle())&&
                   p1.getLatLonPoint().getLatitude()!=0 &&  p1.getLatLonPoint().getLatitude() == p2.getLatLonPoint().getLatitude()
                    && p1.getLatLonPoint().getLongitude()!=0 &&  p1.getLatLonPoint().getLongitude() == p2.getLatLonPoint().getLongitude();
        }
        return false;
    }
    public static boolean poiItemEquals(com.hpbr.bosszhipin.map.search.poi.PoiItem p1, com.hpbr.bosszhipin.map.search.poi.PoiItem p2){
        if(p1!=null && p2!=null && p1.getLatLonPoint()!=null&& p2.getLatLonPoint()!=null){
           return LText.equal(p1.getTitle(),p2.getTitle())&&
                   p1.getLatLonPoint().getLatitude()!=0 &&  p1.getLatLonPoint().getLatitude() == p2.getLatLonPoint().getLatitude()
                    && p1.getLatLonPoint().getLongitude()!=0 &&  p1.getLatLonPoint().getLongitude() == p2.getLatLonPoint().getLongitude();
        }
        return false;
    }
    public static boolean poiItemLatLngEquals(PoiItem p1,PoiItem p2){
        if(p1!=null && p2!=null && p1.getLatLonPoint()!=null&& p2.getLatLonPoint()!=null){
            return
                    p1.getLatLonPoint().getLatitude()!=0 &&  p1.getLatLonPoint().getLatitude() == p2.getLatLonPoint().getLatitude()
                    && p1.getLatLonPoint().getLongitude()!=0 &&  p1.getLatLonPoint().getLongitude() == p2.getLatLonPoint().getLongitude();
        }
        return false;
    }


    public static boolean poiItemLatLngEquals(com.hpbr.bosszhipin.map.search.poi.PoiItem p1, com.hpbr.bosszhipin.map.search.poi.PoiItem p2){
        if(p1!=null && p2!=null && p1.getLatLonPoint()!=null&& p2.getLatLonPoint()!=null){
            return
                    p1.getLatLonPoint().getLatitude()!=0 &&  p1.getLatLonPoint().getLatitude() == p2.getLatLonPoint().getLatitude()
                            && p1.getLatLonPoint().getLongitude()!=0 &&  p1.getLatLonPoint().getLongitude() == p2.getLatLonPoint().getLongitude();
        }
        return false;
    }


    /**
     * @param source
     * @return
     */
    public static int translateSourceForPageExpose(int source) {
        //原来来源 需要的来源 0其他 1公司主页 2职位详情 3面试详情 4聊天卡片
        //ACTION_MAP_SWITCH_ADDRESS： 入口来源：1 职位详情；2 公司主页；3 面试卡片；4 聊天卡片
        switch (source) {
            case GeekRoute1004NewActivity.SOURCE_GUIDE_COMPANY:
                return 2;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_JD:
                return 1;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_INTERVIEW:
                return GeekRoute1004NewActivity.SOURCE_GUIDE_INTERVIEW;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_CHAT:
                return GeekRoute1004NewActivity.SOURCE_GUIDE_CHAT;
        }
        return 0;
    }



    public static  int translateSwitchSource(int source) {
        //原来来源 需要的来源 0其他 1公司主页 2职位详情 3面试详情 4聊天卡片
        //ACTION_MAP_SWITCH_ADDRESS： 0 职位详情页； 1 面试卡片； 2 聊天位置卡片； 3 品牌主页； 4 其他
        switch (source) {
            case GeekRoute1004NewActivity.SOURCE_GUIDE_DEFAULT:
                return 4;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_COMPANY:
                return 3;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_JD:
                return 0;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_INTERVIEW:
                return 1;
            case GeekRoute1004NewActivity.SOURCE_GUIDE_CHAT:
                return 2;
        }
        return 4;
    }
}
