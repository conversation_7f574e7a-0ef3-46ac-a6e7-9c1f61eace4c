package com.hpbr.bosszhipin.module.commend;

import android.text.TextUtils;

import com.bszp.kernel.account.Account;
import com.google.gson.JsonSyntaxException;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.module.commend.entity.AdvancedSearchBean;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.commend.presenter.SearchAdvancedResultPresenter;
import com.hpbr.bosszhipin.module.commend.util.Common;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.search.FilterConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekJDService;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.CloneUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by zhangxiangdong on 2017/11/2.
 */
public class SearchHistoryHelper2 {

    private static final String TAG = "Query";
    private static final int MAX_NUM_OF_HISTORIES = 10;
    private static final int VERSION_HISTORY_DATA_OLD = 810;
    private static final int VERSION_HISTORY_DATA_812 = 812;
    private static final int VERSION_HISTORY_DATA_820 = 820;

    private final QueryStore store = new QueryStore();
    private String KEY_HISTORY_RECORD = "com.hpbr.bosszhipin.HISTORY_RECORD2_" + UserManager.getUID() + "_" + UserManager.getUserRole().get();
    private String KEY_HISTORY_RECORD_VERSION = "com.hpbr.bosszhipin.HISTORY_RECORD2_VERSION" + UserManager.getUID() + "_" + UserManager.getUserRole().get();

    private String SPKEY_HISTORY_RECORD = "SPKEY_HISTORY_RECORD";
    private String SPKEY_HISTORY_RECORD_VERSION = "SPKEY_HISTORY_RECORD_VERSION";

    public SearchHistoryHelper2() {
        compatHistoryData();
        refresh();
    }

    /**
     * 兼容历史数据
     * 由于SP.get()的key在初始化后，不在更新，切换身份后，导致获取SP数据异常
     * 这里进行调整为SpManager，并对数据进行兼容
     */
    private void compatHistoryData() {
        int version = SpManager.get().user().getInt(SPKEY_HISTORY_RECORD_VERSION, 0);
        if (version == 0) {
            SpManager.get().user().edit().putString(SPKEY_HISTORY_RECORD, "").apply();
        } else if (version == VERSION_HISTORY_DATA_812) {
            fixAgeChangeOn820();
        }
        if (version != VERSION_HISTORY_DATA_820) {
            SpManager.get().user().edit().putInt(SPKEY_HISTORY_RECORD_VERSION, VERSION_HISTORY_DATA_820).apply();
        }
    }

    /**
     * 刷新历史搜索数据
     */
    public List<Query> refresh() {
        /* 从本地检索最新的数据 */
        String history = SpManager.get().user().getString(SPKEY_HISTORY_RECORD, "");

        try {
            QueryStore queryStore = GsonUtils.fromJson(history, QueryStore.class);
            store.clear();
            if (queryStore != null) {
                List<Query> queryList = queryStore.queryList;
                if (LList.getCount(queryList) > 0) {
                    boolean hasChange = false;
                    Iterator<Query> iterator = queryList.iterator();
                    while (iterator.hasNext()) {
                        Query query = iterator.next();
                        if (query == null) continue;
                        JobBean job = BossJobListProvider.getInstance().findJobById(query.currJobId);
                        if (job != null) {
                            if (JobStatusChecker.isPositionAvailable(job)) {
                                // 如果职位变更成非驻外时，则清除调驻外的信息
                                if (!JobStatusChecker.isOverSeaAccept(job.acceptOverseas)) {
                                    query.overSeaWorkExpEx = null;
                                    query.overSeaWorkLangEx = null;
                                    hasChange = true;
                                }
                            } else {
                                iterator.remove();
                                hasChange = true;
                            }
                        }
                    }
                    store.addAll(queryList);

                    // 保存过滤后的搜索历史到本地
                    if (hasChange) {
                        String newHistory = GsonUtils.toJson(store);
                        SpManager.get().user().edit().putString(SPKEY_HISTORY_RECORD, newHistory).apply();
                    }
                }
            }

        } catch (JsonSyntaxException e) {
            clear(); // 本地存储的搜索历史格式有错误，清除数据
        }
        return store.queryList;
    }

    /**
     * 保存搜索词到历史记录
     *
     * @param query 搜索词
     */
    public List<Query> save(Query query) {
        // 1. 移除已经搜过的词（如果有）
        store.remove(query);
        // 2. 最新搜索的放在第一位
        store.add(0, query);
        /* 3. 最多保存10个搜索记录，超出则移除最后一个 */
        int count = store.size();
        if (count > MAX_NUM_OF_HISTORIES) {
            store.remove(count - 1);
        }
        // 4. 保存搜索历史到本地
        String history = GsonUtils.toJson(store);
        SpManager.get().user().edit().putString(SPKEY_HISTORY_RECORD, history).apply();
        return store.queryList;
    }

    /**
     * 清空历史记录
     */
    public List<Query> clear() {
        SpManager.get().user().edit().putString(SPKEY_HISTORY_RECORD, "").apply();
        store.clear();
        return store.queryList;
    }

    /**
     * 清空历史记录  退出登录时 清理数据使用
     */
    public List<Query> clearLogout() {
        SpManager.get().temp(Account.IDENTITY_BOSS).edit().putString(SPKEY_HISTORY_RECORD, "").apply();
        store.clear();
        return store.queryList;
    }

    @SuppressWarnings("WeakerAccess")
    public static class QueryStore implements Serializable {

        private static final long serialVersionUID = -7849404560532216572L;
        public final List<Query> queryList = new ArrayList<>();

        /**
         * 如果addAll 被其他非序列化的方法传入，记得深克隆，避免引用传递
         * CloneUtils.cloneObject(newQuery);
         */
        private void addAll(List<Query> queries) {
            //CloneUtils.cloneObject(queries)
            if (LList.getCount(queries) > 0) {
                this.queryList.addAll(queries);
            }
        }

        public void add(int index, Query query) {
            this.queryList.add(index, CloneUtils.cloneObject(query));
        }

        public void remove(int index) {
            this.queryList.remove(index);
        }

        public void remove(Query query) {
            this.queryList.remove(query);
        }

        public void clear() {
            this.queryList.clear();
        }

        public int size() {
            return this.queryList.size();
        }
    }

    @SuppressWarnings("WeakerAccess")
    public static class Query implements Serializable {
        private static final long serialVersionUID = 1144861139654044870L;

        // 关键词-职类(地点+学历+工作年限+薪资+院校要求+求职状态）

        @Nullable
        public String query;

        public List<LevelBean> cityList;

        public String currJobName;
        public long currJobId;

        @Deprecated
        public List<LevelBean> positionList;

        public LevelBean lowDegree;
        public LevelBean highDegree;

        public int lowerYear;
        public int higherYear;

        public int lowAge;
        public int highAge;

        public int lowSalary;
        public int highSalary;

        public String brandName;

        public List<FilterBean> schoolRequires;
        public List<FilterBean> positionStatus;
        public List<FilterBean> geekJobRequirementEx;
        public List<FilterBean> switchFreqEx;
        public List<FilterBean> newGenderEx;

        public LevelBean distance;
        public LocationService.LocationBean location;
        public LevelBean region; //区域筛选

        public List<FilterBean> exchangeResumeEx;
        public List<FilterBean> recentNotViewEx;
        public List<FilterBean> manageExperienceEx;
        public List<FilterBean> withDesignImgsEx;
        public List<FilterBean> overSeaWorkExpEx;
        public List<FilterBean> overSeaWorkLangEx;

        public String sugUuid;
        public int intentType = -1;
        // 1310.92371【B】高搜词场景埋点需求：新增打点参数引用 uuid
        public String uuid;

        public Query(@Nullable String query) {
            this.query = query;
        }

        public String displayText(boolean withCompany) {

            // 职类
//            LevelBean position = LList.getElement(positionList, 0);

            // 关键词-职类
            String jobName = (currJobName == null || currJobName.startsWith("不限")) ? "" : currJobName;
            String queryAndJobName = StringUtil.connectTextWithChar("-", query, jobName);

            // 距离 + 城市
            // 地点
            LevelBean city = LList.getElement(cityList, 0);
            String distanceAndCity = getDistanceAndCity(distance, location, region, city);
            //关键词｜在线职位·地点·学历·工作年限·年龄·薪资·院校要求·求职状态·跳槽频率·性别

            //region 学历
            String degree = Common.getDegree(lowDegree, highDegree);

            //region 工作年限
            String workYear = Common.getWorkYear(lowerYear, higherYear);

            //region 薪资
            String salary = "";
            if (lowSalary > 0 && highSalary > 0) {
//                boolean newUnit = SalaryDescShowGrayController.getInstance().isNewUnit();
//                salary = SalaryDescShowBaseHelper.getSalaryDescForShow(newUnit, lowSalary, highSalary, 0);

                GeekJDService service = GeekPageRouter.getGeekJDService();
                if (service != null) {
                    salary = service.getSalary(lowSalary, highSalary);
                }


            } else {
                salary = "";
            }
            //endregion

            //region 年龄
            String ageRange = Common.calcAgeRangeText(lowAge, highAge);
            String switchFreqExText = concateFilterBeanNames(switchFreqEx);
            String newGenderExText = concateFilterBeanNames(newGenderEx);
            String exchangeResumeExText = concatFilterBeanNames(exchangeResumeEx, "近一个月未与同事交换简历");
            String newRecentNotViewExText = concatFilterBeanNames(recentNotViewEx, "近14天没有看过");
            String manageExperienceExText = concatFilterBeanNames(manageExperienceEx, "有管理经验");
            String withDesignImgsExText = concatFilterBeanNames(withDesignImgsEx, "有作品集");
            String overSeaWorkExpExText = concatFilterBeanNames(overSeaWorkExpEx, null);
            String overSeaWorkLangExText = concatFilterBeanNames(overSeaWorkLangEx, null);
            //endregion
            String brandName = (withCompany && !TextUtils.isEmpty(this.brandName)) ? this.brandName : "";
            String moreDetails = Common.filterAndConnectTextWithChar(" · ",
                    brandName,
                    distanceAndCity,
//                    city != null ? ("不限".equals(city.name) ? "" : city.name) : "", // 地点
                    degree, // 学历
                    workYear, // 工作年限
                    salary, // 薪资
                    overSeaWorkExpExText,
                    overSeaWorkLangExText,
                    concateFilterBeanNames(positionStatus), // 求职状态
                    ageRange, // 年龄
                    concateFilterBeanNames(schoolRequires), // 院校要求
                    newGenderExText,
                    switchFreqExText,
                    concateFilterBeanNames(geekJobRequirementEx),
                    exchangeResumeExText,
                    newRecentNotViewExText,
                    manageExperienceExText,
                    withDesignImgsExText

            );


            if (LText.empty(moreDetails)) {
                return queryAndJobName;
            }
            return StringUtil.connectTextWithChar(" · ", queryAndJobName, moreDetails);
        }

        static String getDistanceAndCity(LevelBean distance, LocationService.LocationBean location, LevelBean region, LevelBean city) {
            String s;
            // 如果有定位时
            if (location != null && location.longitude > 0 && location.latitude > 0 && distance != null) {
                String distanceString = distance.name;
                s = StringUtil.connectTextWithChar(" · ", distanceString, location.city);
            } else if (null != region) {
                s = region.name;
                LevelBean area = LList.getElement(region.subLevelModeList, 0);
                if (null != area) {
                    s = area.name;
                }
            } else if (city != null && !TextUtils.isEmpty(city.name) && !TextUtils.equals(city.name, "不限")) {
                s = city.name;
            } else {
                s = "全国";
            }
            return s;
        }

        @NonNull
        private String concateFilterBeanNames(@Nullable List<FilterBean> filterBeans) {
            int count = LList.getCount(filterBeans);
            String[] names = null;
            if (count > 0) {
                names = new String[count];
                for (int i = 0; i < count; i++) {
                    //noinspection ConstantConditions
                    names[i] = filterBeans.get(i).name;
                }
            }
            return StringUtil.connectTextWithChar("/", names);
        }

        public String concatFilterBeanNames(@Nullable List<FilterBean> filterBeans, String singleReplaceName) {
            String concatString = "";
            int count = LList.getCount(filterBeans);
            if (count == 1 && null != singleReplaceName) {
                FilterBean element = LList.getElement(filterBeans, 0);
                if (null != element && element.code > 0) {
                    concatString = singleReplaceName;
                }
            } else {
                concatString = concateFilterBeanNames(filterBeans);
            }
            return concatString;
        }

        public Query store(@NonNull AdvancedSearchBean searchBean) {
            currJobId = searchBean.currJobId;
            currJobName = searchBean.currJobName;
            positionList = searchBean.positionList;
            cityList = searchBean.cityList;
            lowDegree = searchBean.lowDegree;
            highDegree = searchBean.highDegree;
            lowerYear = searchBean.lowerYear;
            higherYear = searchBean.higherYear;
            lowAge = searchBean.lowAge;
            highAge = searchBean.highAge;
            lowSalary = searchBean.lowSalary;
            highSalary = searchBean.highSalary;
            schoolRequires = searchBean.schoolRequiresEx;
            positionStatus = searchBean.positionStatusEx;
            geekJobRequirementEx = searchBean.geekJobRequirementEx;
            switchFreqEx = searchBean.switchFreqEx;
            newGenderEx = searchBean.newGenderEx;
            brandName = searchBean.currBrandName;
            distance = searchBean.distance;
            location = searchBean.location;
            region = searchBean.region;

            exchangeResumeEx = searchBean.exchangeResumeEx;
            recentNotViewEx = searchBean.recentNotViewEx;
            manageExperienceEx = searchBean.manageExperienceEx;
            withDesignImgsEx = searchBean.withDesignImgsEx;
            overSeaWorkExpEx = searchBean.overSeaWorkExpEx;
            overSeaWorkLangEx = searchBean.overSeaWorkLangEx;

            sugUuid = searchBean.sugUuid;
            intentType = searchBean.intentType;

            // 判断选择的最高学历是否低于本科
            if (Common.isLowerThanBachelorDegree(searchBean.lowDegree.code)
                    && Common.isLowerThanBachelorDegree(searchBean.highDegree.code)) {
                // 本科及以上学历时可以选择院校要求
                schoolRequires = null;
            }

            return this;
        }

        @NonNull
        private String getPositionName() {
            String positionName = "";
            LevelBean position = LList.getElement(positionList, 0);
            if (position != null) {
                positionName = position.name;
            }
            return positionName == null ? "" : positionName;
        }

        @NonNull
        public AdvancedSearchBean transformToSearchBean() {
            AdvancedSearchBean searchBean = new AdvancedSearchBean();
            if (LList.getCount(positionList) > 0) {
                searchBean.positionList.addAll(positionList);
            }
            if (LList.getCount(cityList) > 0) {
                searchBean.cityList.addAll(cityList);
            }

            if (lowDegree != null) {
                searchBean.lowDegree.name = lowDegree.name;
                searchBean.lowDegree.code = lowDegree.code;
            }
            if (highDegree != null) {
                searchBean.highDegree.name = highDegree.name;
                searchBean.highDegree.code = highDegree.code;
            }
            searchBean.currJobId = currJobId;
            searchBean.currJobName = currJobName;
            searchBean.lowerYear = lowerYear;
            searchBean.higherYear = higherYear;
            searchBean.lowAge = lowAge;
            searchBean.highAge = highAge;
            searchBean.lowSalary = lowSalary;
            searchBean.highSalary = highSalary;

            searchBean.schoolRequire = SearchAdvancedResultPresenter.connectFilterBeansCode(schoolRequires);
            searchBean.schoolRequiresEx = schoolRequires;

            searchBean.positionStatus = SearchAdvancedResultPresenter.connectFilterBeansCode(positionStatus);
            searchBean.positionStatusEx = positionStatus;

            searchBean.geekJobRequirement = SearchAdvancedResultPresenter.connectFilterBeansCode(geekJobRequirementEx, FilterConstants.DEFAULT_GEEK_JOB_REQUIREMENT);
            searchBean.geekJobRequirementEx = geekJobRequirementEx;

            searchBean.switchFreq = SearchAdvancedResultPresenter.connectFilterBeansCode(switchFreqEx, FilterConstants.DEFAULT_SWITCH_FREQ);
            searchBean.switchFreqEx = switchFreqEx;

            searchBean.newGender = SearchAdvancedResultPresenter.connectFilterBeansCode(newGenderEx, FilterConstants.DEFAULT_NEW_GENDER);
            searchBean.newGenderEx = newGenderEx;

            searchBean.distance = distance;
            searchBean.location = location;
            searchBean.region = region;

            searchBean.exchangeResume = SearchAdvancedResultPresenter.connectFilterBeansCode(exchangeResumeEx, FilterConstants.DEFAULT_EXCHANGE_RESUME);
            searchBean.exchangeResumeEx = exchangeResumeEx;

            searchBean.recentNotView = SearchAdvancedResultPresenter.connectFilterBeansCode(recentNotViewEx, FilterConstants.DEFAULT_RECENT_NOT_VIEW);
            searchBean.recentNotViewEx = recentNotViewEx;

            searchBean.manageExperience = SearchAdvancedResultPresenter.connectFilterBeansCode(manageExperienceEx, FilterConstants.DEFAULT_MANAGE_EXPERIENCE);
            searchBean.manageExperienceEx = manageExperienceEx;

            searchBean.withDesignImgs = SearchAdvancedResultPresenter.connectFilterBeansCode(withDesignImgsEx, FilterConstants.DEFAULT_WITH_DESIGN_IMGS);
            searchBean.withDesignImgsEx = withDesignImgsEx;

            searchBean.overSeaWorkExp = SearchAdvancedResultPresenter.connectFilterBeansCode(overSeaWorkExpEx, FilterConstants.DEFAULT_OVER_SEA_WORK_EXP);
            searchBean.overSeaWorkExpEx = overSeaWorkExpEx;

            searchBean.overSeaWorkLang = SearchAdvancedResultPresenter.connectFilterBeansCode(overSeaWorkLangEx, FilterConstants.DEFAULT_OVER_SEA_WORK_LANG);
            searchBean.overSeaWorkLangEx = overSeaWorkLangEx;

            searchBean.intentType = intentType;
            searchBean.sugUuid = sugUuid;
            return searchBean;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            Query q = (Query) o;

            /* 一个【“关键词”+“职类”】组合进入搜索结果页，为一个有效的历史记录 */

            String query = this.query;
            boolean sameQuery = false;
            if (LText.empty(query) && LText.empty(q.query)) {
                sameQuery = true;
            } else if (query != null && query.equalsIgnoreCase(q.query)) {
                sameQuery = true;
            }


            boolean sameJobId = false;
            if (this.currJobId == q.currJobId) {
                sameJobId = true;
            }

            return sameQuery && sameJobId;
        }

    }

    private void fixAgeChangeOn820() {
        String history = SpManager.get().user().getString(SPKEY_HISTORY_RECORD, "");
        if (LText.empty(history)) {
            return;
        }

        try {
            QueryStore queryStore = GsonUtils.fromJson(history, QueryStore.class);
            if (queryStore != null && !LList.isEmpty(queryStore.queryList)) {
                List<Query> queryList = queryStore.queryList;
                boolean hasChange = false;
                Iterator<Query> iterator = queryList.iterator();
                while (iterator.hasNext()) {
                    Query query = iterator.next();
                    if (query != null && query.lowAge == FilterConstants.AGE_DEFAULT_HIGH_LEGACY && query.highAge == FilterConstants.AGE_DEFAULT_HIGH_LEGACY) {
                        query.highAge = FilterConstants.AGE_DEFAULT_HIGH;
                        hasChange = true;
                    }
                }
                // 保存过滤后的搜索历史到本地
                if (hasChange) {
                    QueryStore backup = new QueryStore();
                    backup.addAll(queryList);
                    String newHistory = GsonUtils.toJson(backup);
                    SpManager.get().user().edit().putString(SPKEY_HISTORY_RECORD, newHistory).apply();
                }
            }

        } catch (JsonSyntaxException e) {
            clear(); // 本地存储的搜索历史格式有错误，清除数据
        }
    }

}
