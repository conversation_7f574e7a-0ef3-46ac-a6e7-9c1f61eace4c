package com.hpbr.bosszhipin.module.my.activity.geek.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bszp.kernel.utils.SingleLiveEvent;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.my.activity.geek.model.CheckEduExpModel;
import com.hpbr.bosszhipin.module.my.entity.EduBean;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.views.wheelview.eduexp.EducateExpUtil;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.DiagnoseOptimizerDetailRequest;
import net.bosszhipin.api.DiagnoseOptimizerDetailResponse;
import net.bosszhipin.api.EduExpUpdateRequest;
import net.bosszhipin.api.EduExpUpdateResponse;
import net.bosszhipin.api.GeekBaseInfoTipResponse;
import net.bosszhipin.api.GeekResumeEduExpCheckDateRequest;
import net.bosszhipin.api.GeekResumeEduExpCheckDateResponse;
import net.bosszhipin.api.GeekResumeSuggestGarbageRequest;
import net.bosszhipin.api.GeekResumeSuggestGarbageResponse;
import net.bosszhipin.api.GetUserInfoLeftCountRequest;
import net.bosszhipin.api.GetUserInfoLeftCountResponse;
import net.bosszhipin.api.bean.GarbageResumeBean;
import net.bosszhipin.api.bean.ServerResumeSuggestTipBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/09/01 17:42
 * @description 牛人个人信息页
 */
public class GeekInfoEditViewModel extends BaseViewModel {

    public final SingleLiveEvent<CheckEduExpModel> mCheckEduExpLiveData = new SingleLiveEvent<>();//教育经历时间段修改检查
    public final SingleLiveEvent<Boolean> mUpdateNameLiveData = new SingleLiveEvent<>();
    public final SingleLiveEvent<Boolean> mShowChangeAvatarDialogLiveData = new SingleLiveEvent<>();//显示修改头像弹窗
    public final SingleLiveEvent<GeekBaseInfoTipResponse> baseInfoTipLiveData = new SingleLiveEvent<>();//查询基本信息页提醒
    public final SingleLiveEvent<ServerResumeSuggestTipBean> resumeSuggestTipLiveData = new SingleLiveEvent<>();//基本信息简历诊断提示
    public final SingleLiveEvent<DiagnoseOptimizerDetailResponse> diagnoseTipLiveData = new SingleLiveEvent<>(); // 1309.801 新在线简历诊断
    public GetUserInfoLeftCountResponse mAvatarState;//头像
    public GetUserInfoLeftCountResponse mNameState;//姓名
    public GetUserInfoLeftCountResponse mWeChatState;//微信
    private boolean firstIn = true;//首次进入需要弹修改头像弹窗时，用到
    public final boolean newOnlineResume = DataStarGray.getInstance().isNewOnlineResume();
    public long diagnoseRuleId;

    public GeekInfoEditViewModel(@NonNull Application application) {
        super(application);
    }

    public EduBean edu;//教育经历（滚轮）

    /**
     * 获取毕业时间
     */
    public String getRecentEduDate() {
        String date = "";
        if (UserManager.isStudentRecord()) {
            UserBean userBean = UserManager.getLoginUser();
            if (userBean != null && userBean.geekInfo != null) {
                List<EduBean> eduList = userBean.geekInfo.eduList;
                if (!LList.isEmpty(eduList)) {
                    for (EduBean item : eduList) {
                        if (item != null && item.highestDegree == 1) {
                            date = EducateExpUtil.createGraduateDateText(item.endDate);
                            this.edu = item;
                            break;
                        }
                    }
                }
            }
        }
        return date;
    }

    /**
     * 【教育经历】时间段修改检查
     * <a href="http://api.weizhipin.com/project/30/interface/api/204702">...</a>
     */
    public void checkEduExpDate(int startDate, int endDate) {
        if (edu == null) return;

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("school", TextUtils.isEmpty(edu.school) ? "" : edu.school);
        paramMap.put("degree", edu.degreeIndex + "");
        paramMap.put("major", edu.major);
        paramMap.put("startDate", startDate + "");
        if (endDate <= 0) {
            paramMap.put("endDate", "");
        } else {
            paramMap.put("endDate", endDate + "");
        }
        paramMap.put("eduDescription", edu.schoolExperience);
        paramMap.put("eduType", String.valueOf(edu.eduType));

        GeekResumeEduExpCheckDateRequest request = new GeekResumeEduExpCheckDateRequest(new ApiRequestCallback<GeekResumeEduExpCheckDateResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GeekResumeEduExpCheckDateResponse> data) {
                if (data.resp != null) {
                    mCheckEduExpLiveData.postValue(new CheckEduExpModel(startDate, endDate, data.resp));
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.extra_map = paramMap;
        request.execute();
    }

    /**
     * 保存教育经历
     * 保存完教育经历，发现服务端会下发消息，同步用户数据
     */
    public void saveEduExp(int startDate, int endDate) {

        if (edu == null) return;

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("eduId", edu.updateId + "");
        paramMap.put("school", TextUtils.isEmpty(edu.school) ? "" : edu.school);
        paramMap.put("schoolId", String.valueOf(edu.schoolId));
        paramMap.put("degree", edu.degreeIndex + "");
        paramMap.put("major", edu.major);
        paramMap.put("startDate", startDate + "");
        if (endDate <= 0) {
            paramMap.put("endDate", "");
        } else {
            paramMap.put("endDate", endDate + "");
        }
        paramMap.put("eduDescription", edu.schoolExperience);
        paramMap.put("eduType", String.valueOf(edu.eduType));

        EduExpUpdateRequest request = new EduExpUpdateRequest(new ApiRequestCallback<EduExpUpdateResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EduExpUpdateResponse> data) {
//                mSaveEduExpSuccessLiveData.postValue(new SaveEduExpModel(startDate, endDate, true));
                //保存完教育经历，发现服务端会下发消息，同步用户数据
            }

            @Override
            public void onComplete() {
//                syncUserInfo();
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.extra_map = paramMap;
        request.execute();
    }


    /**
     * 获取当月可更换微信/姓名/头像的剩余次数
     * <p>
     * 头像：URLConfig.URL_GET_USER_AVATAR_LEFT_COUNT
     * 姓名：URLConfig.URL_GET_USER_NAME_LEFT_COUNT
     * 微信：URLConfig.URL_GET_USER_WECHAT_LEFT_COUNT
     * 头像+姓名+微信：URLConfig.URL_GET_USER_INFO_LEFT_COUNT
     *
     */
    public void getInfoLeftCount(@NonNull String url, boolean showChangAvatarDialog) {
        GetUserInfoLeftCountRequest request = new GetUserInfoLeftCountRequest(new ApiRequestCallback<GetUserInfoLeftCountResponse>() {
            @Override
            public void onSuccess(ApiData<GetUserInfoLeftCountResponse> data) {
                if (data == null || data.resp == null) return;
                GetUserInfoLeftCountResponse resp = data.resp;
                if (TextUtils.equals(url, LoginURLConfig.URL_GET_USER_AVATAR_LEFT_COUNT)) {//头像
                    mAvatarState = new GetUserInfoLeftCountResponse(resp.leftCount, resp.totalCount, resp.auditStatus);
                } else if (TextUtils.equals(url, LoginURLConfig.URL_GET_USER_NAME_LEFT_COUNT)) {//姓名
                    mNameState = new GetUserInfoLeftCountResponse(resp.leftCount, resp.totalCount, resp.auditStatus);
                } else if (TextUtils.equals(url, LoginURLConfig.URL_GET_USER_WECHAT_LEFT_COUNT)) {//微信
                    mWeChatState = new GetUserInfoLeftCountResponse(resp.leftCount, resp.totalCount, resp.auditStatus, resp.nextChangeToast, resp.changeToast);
                } else if (TextUtils.equals(url, LoginURLConfig.URL_GET_USER_INFO_LEFT_COUNT)) {//头像+姓名+微信
                    mAvatarState = resp.avatar;
                    mNameState = resp.name;
                    mWeChatState = resp.wechat;
                    mUpdateNameLiveData.postValue(true);
                    if (showChangAvatarDialog && firstIn) {
                        firstIn = false;
                        mShowChangeAvatarDialogLiveData.postValue(true);
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.url = url;
        request.execute();
    }

    public List<String> getMessage(String code) {

        GeekInfoBean geekInfo = getGeekInfoBean();
        if (geekInfo == null) {
            return null;
        }

        GarbageResumeBean garbageResume = geekInfo.garbageResume;
        if (garbageResume == null) {
            return null;
        }

        GarbageResumeBean.ArrayMessage baseInfo = garbageResume.baseInfo;
        if (baseInfo == null) {
            return null;
        }

        if (LList.hasElement(baseInfo.fieldList)) {
            List<GarbageResumeBean.Message> fieldList = baseInfo.fieldList;
            for (GarbageResumeBean.Message message : fieldList) {
                if (message != null && TextUtils.equals(code, message.code)) {
                    return message.message;
                }
            }
        }
        return null;
    }

    public GeekInfoBean getGeekInfoBean() {
        UserBean userBean = UserManager.getLoginUser();

        if (userBean == null) {
            return null;
        }

        return userBean.geekInfo;
    }


    /**
     * 【基本信息】查询基本信息页提醒
     */
    public void requestBaseInfoTip() {
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPGEEK_BASE_INFO_TIP_QUERY)
                .setRequestCallback(new SimpleApiRequestCallback<GeekBaseInfoTipResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekBaseInfoTipResponse> data) {
                        baseInfoTipLiveData.setValue(data.resp);
                    }

                }).execute();
    }

    /**
     * 【基本信息】简历诊断提示
     */
    public void requestResumeSuggestTip() {
        GeekResumeSuggestGarbageRequest request = new GeekResumeSuggestGarbageRequest(new SimpleApiRequestCallback<GeekResumeSuggestGarbageResponse>() {
            @Override
            public void onSuccess(ApiData<GeekResumeSuggestGarbageResponse> data) {
                super.onSuccess(data);
                if (data == null || data.resp == null) {
                    resumeSuggestTipLiveData.setValue(null);
                    return;
                }
                GeekResumeSuggestGarbageResponse response = data.resp;
                if (response.resumeSuggestTip == null) {
                    resumeSuggestTipLiveData.setValue(null);
                    return;
                }
                resumeSuggestTipLiveData.setValue(response.resumeSuggestTip);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                resumeSuggestTipLiveData.setValue(null);
            }
        });
        request.source = 1;
        request.execute();
    }


    /**
     * 1309.801
     * 在线简历新版诊断
     */
    public void requestDiagnoseTip() {
        DiagnoseOptimizerDetailRequest request = new DiagnoseOptimizerDetailRequest(new SimpleApiRequestCallback<DiagnoseOptimizerDetailResponse>() {
           @Override
           public void onSuccess(ApiData<DiagnoseOptimizerDetailResponse> data) {
               diagnoseTipLiveData.setValue(data.resp);
           }
        });

        request.moduleType = "10";
        request.ruleId = diagnoseRuleId;
        request.execute();
    }


    /**
     * 【基本信息】关闭基本信息页提醒
     */
    public void requestBaseInfoTipClose() {
        SimpleApiRequest.POST(GeekUrlConfig.URL_ZPGEEK_BASE_INFO_TIP_CLOSE)
                .addParam("tipType", getPhoneTipType())
                .execute();
    }


    public String getPhoneTipText() {
        GeekBaseInfoTipResponse response = baseInfoTipLiveData.getValue();
        return response != null && response.phone != null ? response.phone.tipText : "";
    }

    public int getPhoneTipType() {
        GeekBaseInfoTipResponse response = baseInfoTipLiveData.getValue();
        return response != null && response.phone != null ? response.phone.tipType : 0;
    }


}
