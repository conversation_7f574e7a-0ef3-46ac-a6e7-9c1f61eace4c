package com.hpbr.bosszhipin.module.my.activity.boss.location;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.apm.Apm;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseCheckLocationFragment;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.map.IMapDelegate;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.map.Marker;
import com.hpbr.bosszhipin.map.MarkerOptions;
import com.hpbr.bosszhipin.map.location.LatLonPoint;
import com.hpbr.bosszhipin.map.model.CameraPosition;
import com.hpbr.bosszhipin.map.search.geocode.GeocodeResult;
import com.hpbr.bosszhipin.map.search.geocode.GeocodeSearchCompat;
import com.hpbr.bosszhipin.map.search.geocode.OnGeocodeSearchListener;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeAddress;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeQuery;
import com.hpbr.bosszhipin.map.search.geocode.RegeocodeResult;
import com.hpbr.bosszhipin.map.search.poi.OnGetPoiSearchResultListener;
import com.hpbr.bosszhipin.map.search.poi.PoiItem;
import com.hpbr.bosszhipin.map.search.poi.PoiResult;
import com.hpbr.bosszhipin.map.search.poi.PoiSearch;
import com.hpbr.bosszhipin.map.search.poi.PoiSearchCompat;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module.my.activity.boss.adapter.WorkLocationAdapter;
import com.hpbr.bosszhipin.module.my.activity.boss.location.utils.PoiItemHelper;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.AmapUtils;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.UiUtils;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Map;


/**
 * Author: Zhang Lishun
 * Date: 2018/09/25.<br/>
 * 地图选点的主要流程<br/
 * <p>
 * 1 requestLocation()开始定位<br/>
 * 2 onLocationCallback()得到经纬度<br/
 * 3.1 把marker放在地图的中心位置(在上面)<br/
 * 3.2 显示定位蓝点<br/
 * 3.3 移动Camera移动到经纬度处<br/
 * 4 使用marker的经纬度，去搜索200内的Poi集合<br/
 * <p>
 * https://lbs.amap.com/api/android-sdk/guide/create-map/mylocation
 */
public class SelectLocationByMapFragment extends BaseCheckLocationFragment
        implements OnGeocodeSearchListener, LocationService.OnLocationCallback {

    private static final String TAG = SelectLocationByMapFragment.class.getSimpleName();
    private ArrayList<PoiItem> poiItems = new ArrayList<>();
    private long jobId;
    private GeocodeSearchCompat geocoderSearch;

    private View mContainer;
    private MapViewCompat mMapView;
    private IMapDelegate mMap;
    private RecyclerView recyclerView;

    private Marker marker;
    private View infoWindow;
    private MTextView tvInfo;
    private ImageView ivMyLocationButton;
    private View ivLoading;
    private MTextView tx_loading;

    private Group gMap;

    private JobBean job;
    private boolean canEditLocation;
    private boolean isLocation = false;
    private PoiItem searchPoiItem; // 搜索界面带回来的 poiItem
    private boolean ignoreMapSearchOnce; // 屏蔽一次地图移动搜索
    private Handler poiSearchHandler = new Handler(Looper.getMainLooper());

    private String scene;

    public SelectLocationByMapFragment() {

    }

    public static SelectLocationByMapFragment newInstance(JobBean jobBean, boolean canEditLocation, String okAction, String scene) {
        SelectLocationByMapFragment fragment = new SelectLocationByMapFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_ENTITY, jobBean);
        bundle.putBoolean(Constants.DATA_BOOLEAN, canEditLocation);
        bundle.putString(Constants.DATA_STRING, okAction);
        bundle.putString(SelectWorkLocationActivity.SCENE, scene);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle data = getArguments();
        if (data != null) {
            job = (JobBean) data.getSerializable(Constants.DATA_ENTITY);
            canEditLocation = data.getBoolean(Constants.DATA_BOOLEAN);
            scene = data.getString(SelectWorkLocationActivity.SCENE);
        }
        if (job == null) {
            job = new JobBean();
        }
        jobId = job.id;
        geocoderSearch = GeocodeSearchCompat.newInstance(activity);
    }


    /**
     * 聊天页面发送地理位置流程 其他业务场景都是false,只有点击更多发送地理位置聊天是true
     *
     * @return
     */
    private boolean isSendLocationProcess() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return TextUtils.equals(arguments.getString(Constants.DATA_STRING), "发送");
        }
        return false;
    }

    /**
     * 右上角是否存在按钮相应事件
     */
    private boolean hasLocationAction() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return !TextUtils.isEmpty(arguments.getString(Constants.DATA_STRING));
        }
        return false;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        mContainer = inflater.inflate(R.layout.fragment_select_map_location, container, false);
        return mContainer;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        gMap = view.findViewById(R.id.g_map);

        ivMyLocationButton = view.findViewById(R.id.iv_my_location_button);
        ivMyLocationButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isLocation = true;
                ivMyLocationButton.setSelected(true);
                locationPermissionRequire();
            }
        });

        //获取地图控件引用
        mMapView = view.findViewById(R.id.map_view);
        //在activity执行onCreate时执行mMapView.onCreate(savedInstanceState)，创建地图
        mMapView.onCreate(savedInstanceState);
        mMap = mMapView.getMap();
        initMap();

        recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(activity));

        refreshAdapter(null, null, null);

        ivLoading = view.findViewById(R.id.iv_loading);
        tx_loading = view.findViewById(R.id.tx_loading);

        useLocation();
    }

    private boolean isFirstTime = true;

    private void locationPermissionRequire() {
        AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                LocationPermissionHelper helper = PermissionHelper.getLocationHelper((FragmentActivity) activity,
                        LocationPermissionHelper.CHAT_SEND_LOCATION_CLICK,
                        new PermissionHelper.ExtendBean().setAnalyticName(scene));

                helper.setPermissionCallback(new PermissionCallback<LocationPermissionHelper.LocationPermissionData>() {
                    @Override
                    public void onResult(boolean yes, LocationPermissionHelper.LocationPermissionData permission) {
                        if (yes) {
//                            onGrantLocationPermission();
                            requestLocation(SelectLocationByMapFragment.this);
                        } else {
                            onRejectLocationPermission();
                        }
                    }
                });
                helper.requestPermission();
            }
        }, 16);
    }

    @Override
    protected void onRejectLocationPermission() {
//        stateLayoutManager.getEmptyLayout().setImage(R.mipmap.ic_error_location)
//                .setTitle("您未开启定位服务\n只能手动选择城市后输入办公大楼～");
//        stateLayoutManager.showEmptyScene();
        ivMyLocationButton.setVisibility(View.VISIBLE);
        SelectWorkLocationActivity locationActivity = (SelectWorkLocationActivity) activity;
        locationActivity.updateCity("选择城市", canEditLocation);

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_EDIT_JOB_LOCATION)
                .param("p", "0").build();
    }

    @Override
    protected void onGrantLocationPermission() {
        gMap.setVisibility(View.VISIBLE);
        ivMyLocationButton.setVisibility(View.VISIBLE);
//        ivMyLocationButton.setVisibility(canEditLocation ? View.VISIBLE : View.GONE);
        boolean noLocation = job.latitude <= 0 && job.longitude <= 0
                && LText.empty(job.poiTitle);
        if (noLocation) {
            requestLocation(this);
        } else {
            mMap.setOnMapLoadedListener(this::useLocation);
            editJobLocationAction(job.province, job.city, job.area);

//            reGeoCodeSearch(job.latitude, job.longitude);
            poiSearch(job.poiTitle, job.city, job.province);

            useLocation(job.latitude, job.longitude);
//            initMap();
//            refreshAdapter(job.addressText, job.city, job.province);
//
            SelectWorkLocationActivity locationActivity = (SelectWorkLocationActivity) activity;
            locationActivity.updateCity(StringUtil.trimUnwanted(job.city), canEditLocation);
        }
    }

    @Override
    public void onLocationCallback(boolean success, LocationService.LocationBean locationBean) {
        if (success && locationBean != null) {
            L.d(TAG, "onLocationCallback: " + locationBean.toString());
            SelectWorkLocationActivity locationActivity = (SelectWorkLocationActivity) activity;
            locationActivity.updateCity(StringUtil.trimUnwanted(locationBean.city), canEditLocation);

//            MyLocationStyle locationStyle = new MyLocationStyle()
//                    .radiusFillColor(activity.getResources().getColor(R.color.my_location_circle_color))
//                    .strokeColor(activity.getResources().getColor(R.color.my_location_circle_color))
//                    .strokeWidth(0)
//                    .myLocationIcon(BitmapDescriptorFactory.fromResource(R.mipmap.ic_my_location))
//                    .showMyLocation(true);
//            mMap.setMyLocationStyle(locationStyle);
//            mMap.setMyLocationEnabled(false);
            useLocation(locationBean.latitude, locationBean.longitude);

            editJobLocationAction(locationBean.province, locationBean.city, locationBean.district);
        }
    }

    private void editJobLocationAction(String province, String city, String district) {
        AppThreadFactory.createThread(() -> {
            Map<Integer, LevelBean> beans = VersionAndDatasCommon.getInstance().getProvinceCityArea(
                    province,
                    city,
                    district);

            AppThreadFactory.getMainHandler().post(() -> {
                LevelBean cityBean = beans.get(1);
                if (cityBean == null) {
                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_EDIT_JOB_LOCATION)
                            .param("p", "1")
                            .build();
                } else {
                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_EDIT_JOB_LOCATION)
                            .param("p", "1")
                            .param("p2", String.valueOf(cityBean.code))
                            .build();
                }
            });
        }).start();
    }

    public void useLocation() {
        if (checkJobHaveWorkLocation()) {
            useLocation(job.latitude, job.longitude);
        } else {
            requestLocation(this);
        }
    }


    /**
     * 检测职位是否有办公地址
     *
     * @return true有办公地址, false没有办公地址
     */
    private boolean checkJobHaveWorkLocation() {
        if (job != null) {
            int workType = job.workType;
            return JobStatusChecker.isWorkTypeNormal(workType) && job.latitude != 0;
        }
        return false;
    }


    private void initMap() {
//        UiSettings uiSettings = mMap.getUiSettings();
//        uiSettings.setMyLocationButtonEnabled(false);
//        uiSettings.setZoomControlsEnabled(false);
//        uiSettings.setMyLocationButtonEnabled(true); //显示默认的定位按钮
//        mMap.setMyLocationEnabled(false);
        //定义了当可视范围改变时回调的接口
        mMap.setOnCameraChangeListener(new IMapDelegate.OnCameraChangeListener() {

            @Override
            public void onCameraChange(CameraPosition cameraPosition) {
                L.d(TAG, "onCameraChange: ");
                ivMyLocationButton.setSelected(false);
                marker.hideInfoWindow(); // 拖动地图，隐藏信息气泡
                TLog.info(TAG, "onRegeocodeSearched: onCameraChange");
//                marker.setPositionByPixels(mMapView.getWidth() / 2, mMapView.getHeight() / 2-Scale.dip2px(activity,10));
            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                L.d(TAG, "onCameraChangeFinish: ");
                if (isLocation) {
                    ivMyLocationButton.setSelected(true);
                    ivMyLocationButton.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            isLocation = false;
                        }
                    }, 500);
                }
                if (ignoreMapSearchOnce) {
                    ignoreMapSearchOnce = false;
                    return;
                }

//                com.hpbr.bosszhipin.map.location.LatLonPoint position2 = marker.getPosition();
//                TLog.info(TAG, "onRegeocodeSearched: onCameraChangeFinish" + position2.latitude + "=" + position2.longitude);

                LatLonPoint position = cameraPosition.target;
                TLog.info(TAG, "onCameraChangeFinish: %s %s", position.latitude, position.longitude);
                if (position.latitude == 0 && position.longitude == 0) return;

                poiSearchHandler.removeCallbacksAndMessages(null);
                poiSearchHandler.postDelayed(() -> reGeoCodeSearch(position.latitude, position.longitude), 600);
            }
        });

        infoWindow = LayoutInflater.from(activity).inflate(R.layout.map_info_window, mMapView, false);
        tvInfo = infoWindow.findViewById(R.id.tv_content);
        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.icon_bg_bubble, null);
        if (drawable != null) {
            drawable.setBounds(0, 0, (int) UiUtils.convertDpToPixel(12, activity), (int) UiUtils.convertDpToPixel(6, activity));
        }
        tvInfo.setBackground(drawable);

        mMap.setInfoWindowAdapter(new IMapDelegate.InfoWindowAdapter() {

            @Override
            public View getInfoWindow(Marker m) {
                return infoWindow;
            }

        });
        mMapView.showMapContentApprovalNumber(AmapUtils.getApprovalNumberBottomMargin(activity, 8));
    }

    public void refreshLocation(PoiItem poiItem) {
        final LatLonPoint latLonPoint = poiItem.getLatLonPoint();
        if (latLonPoint != null) {
            job.latitude = latLonPoint.getLatitude();
            job.longitude = latLonPoint.getLongitude();
            TLog.info(TAG, "onRegeocodeSearched: 点击的经纬度" + latLonPoint.getLongitude() + "==" + latLonPoint.getLatitude() + poiItem.getTitle());
            if (adapter != null && hasLocationAction()) {
                searchPoiItem = poiItem;
                adapter.setSelectPoi(poiItem);
//            ignoreMapSearchOnce = true;
            }
            useLocation();
        } else {
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_LATLNG_IS_NULL)
                    .param("p2", this.getClass().getSimpleName())
                    .param("p3", String.format("%1s   %2s", poiItem.getTitle(), poiItem.getCityName()))
                    .report();
        }
    }

    private void moveMapTo(double latitude, double longitude, String poiTitle) {
        mMap.moveCamera(latitude, longitude, 17);
        AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!TextUtils.isEmpty(poiTitle)) {
                    tvInfo.setText(poiTitle);
                    marker.showInfoWindow();
                } else {
                    tvInfo.setText("");
                    marker.hideInfoWindow();
                }
            }
        }, 200);
    }

    /**
     * 逆地理编码查询的地理坐标点、查询范围、坐标类型
     *
     * @param latitude
     * @param longitude
     */
    public void reGeoCodeSearch(double latitude, double longitude) {
        // 第一个参数表示一个Latlng，第二参数表示范围多少米，第三个参数表示是火系坐标系还是GPS原生坐标系
        LatLonPoint latLonPoint = new LatLonPoint(latitude, longitude);
        RegeocodeQuery query = new RegeocodeQuery(latLonPoint, 1000);
        if (geocoderSearch != null) {
            geocoderSearch.getFromLocationAsyn(query, this);
        }
    }


    private static boolean hasReport;

    /*添加APM上报检测 高德POI搜索失败*/
    private void reportPoiSearchFailed(int resultID) {
        if (!hasReport) {
            ApmAnalyzer.create()
                    .action(AnalyticsAction.ACTION_GAO_DE, "POISearchError")
                    .p2(String.valueOf(resultID))
                    .p3(String.valueOf(job))
                    .p4(String.valueOf(isAPMGrayStage()))
                    .report();
        }
        hasReport = true;
    }

    /**
     * 搜索Pois
     *
     * @param regeocodeResult
     */
    @Override
    public void onRegeocodeSearched(RegeocodeResult regeocodeResult) {
//        AMapErrorReporter.dotMapReverseGeoCodeResult(regeocodeResult, resultID);
//        AMapErrorReporter.dotMapError(AMapErrorReporter.TYPE_B_LOCATION_RE_GEO, resultID);
        if (regeocodeResult == null) {
            tvInfo.setText("");
            marker.hideInfoWindow();
            ivLoading.setVisibility(View.VISIBLE);
            tx_loading.setText("获取位置失败");
            tx_loading.setGravity(Gravity.CENTER_HORIZONTAL);

//            reportPoiSearchFailed(resultID);
            return;
        }

        RegeocodeAddress regeocodeAddress = regeocodeResult.getRegeocodeAddress();
        String address = regeocodeAddress.getFormattedAddress();
        if (!LList.isEmpty(regeocodeAddress.getPois()) && regeocodeAddress.getPois().get(0) != null) {
            address = regeocodeAddress.getPois().get(0).getTitle();
        }
        StringBuilder searchAddress = new StringBuilder();
        String provinceName = checkNonNull(regeocodeAddress.getProvince()); // 省份
        String cityName = checkNonNull(regeocodeAddress.getCity()); // 城市
        if (TextUtils.isEmpty(job.city)) {
            job.city = regeocodeAddress.getCity();
        }
        if (searchPoiItem != null) { //是搜索
            if (TextUtils.isEmpty(searchPoiItem.getCityName())) { // 防止高德 脏数据。
                searchPoiItem.setCityName(cityName);
            }
            if (TextUtils.isEmpty(searchPoiItem.getProvinceName())) {
                searchPoiItem.setProvinceName(provinceName);
            }

            if (!provinceName.equalsIgnoreCase(cityName)) {
                searchAddress.append(provinceName);
            }
            searchAddress.append(cityName);
            searchAddress.append(checkNonNull(searchPoiItem.getTitle()));
        } else if (!TextUtils.isEmpty(job.poiTitle) && job.longitude > 0 && job.latitude > 0 && isFirstTime) {
            // 第一次进来的时候是以职位搜的，不是第一次可能是搜索 或者挪地图
            if (!provinceName.equalsIgnoreCase(cityName)) {
                searchAddress.append(provinceName);
            }
            searchAddress.append(cityName);


            if (isAPMGrayStage()) {
                if (job.workAddress != null && job.workAddress.contains(job.poiTitle)) {
                    searchAddress.append(job.workAddress);
                } else {
                    searchAddress.append(job.workAddress);
                    searchAddress.append(job.poiTitle);
                }
            } else {
                searchAddress.append(job.poiTitle);
            }


        } else {
            searchAddress.append(address); // 手动挪地图
        }

        poiSearch(searchAddress.toString(), regeocodeAddress.getCity(), regeocodeAddress.getProvince());
    }


    private boolean isAPMGrayStage() {
        JSONObject privateConfigJsonObject = Apm.getPrivateConfigJsonObject();
        if (privateConfigJsonObject != null) {
            return privateConfigJsonObject.optBoolean("isAPMLocationPOISearchGrayStage", true);
        }
        return true;
    }


    @Override
    public void onGeocodeSearched(GeocodeResult geocodeResult) {

    }

    private String checkNonNull(@Nullable String nullableString) {
        return nullableString == null ? "" : nullableString;
    }

    /**
     * 调用高德搜索
     *
     * @param address
     * @param city
     */
    public void poiSearch(final String address, final String city, final String province) {
        PoiSearch mQuery = new PoiSearch()
                .setKeyWord(address)
                .setTag(SelectWorkLocationActivity.POI_FILTER_STR)
                .setCity(GeekF1Util.getCityWithSuffix(city))
                .setCityLimit(true);
        ivLoading.setVisibility(View.VISIBLE);
        tx_loading.setText("正在获取位置…");
        tx_loading.setGravity(Gravity.START);

        PoiSearchCompat poiSearch = PoiSearchCompat.newInstance(activity);
        if (null != poiSearch) {
            poiSearch.searchPoi(mQuery, new OnGetPoiSearchResultListener() {
                @Override
                public void onGetPoiResult(PoiResult poiResult) {
                    if (poiResult == null) {
                        // 返回结果成功或者失败的响应码。1000为成功，其他为失败
                        tx_loading.setText("获取位置失败");
                        tx_loading.setGravity(Gravity.CENTER_HORIZONTAL);
//                        reportPoiSearchFailed(code);
                        return;
                    }

                    poiItems.clear();
                    if (!LList.isEmpty(poiResult.getPoiItems())) {
                        poiItems.addAll(poiResult.getPoiItems());
                        sortPoiItem();
                    }

                    // 解决选中的 poi 不在后刷新的列表中的问题
                    if (searchPoiItem != null) {
                        for (PoiItem poiItem : poiItems) {
                            if (TextUtils.equals(poiItem.getTitle(), searchPoiItem.getTitle())) {
                                poiItems.remove(poiItem);
                                break;
                            }
                        }
                        poiItems.add(0, searchPoiItem);
                        searchPoiItem = null;
                        // 更新顶部 market
                        PoiItem poiItem = LList.getElement(poiItems, 0);
                        if (poiItem != null) {
                            tvInfo.setText(poiItem.getTitle());
                            marker.showInfoWindow();
                        } else {
                            tvInfo.setText("");
                            marker.hideInfoWindow();
                        }
                    }

                    refreshAdapter(address, city, province);
                    if (LList.getCount(poiResult.getPoiItems()) > 0 && LList.getCount(poiItems) == 0) {
                        ToastUtils.showText("没有结果，请使用上方搜索功能");// 高德返回了没有城市名称的脏数据 被过滤掉了
                    }

                    if (LList.isEmpty(poiItems)) {
                        ivLoading.setVisibility(View.VISIBLE); // 定位获取位置成功，隐藏定位提示
                        tx_loading.setText("当前位置无结果");
                        tx_loading.setGravity(Gravity.CENTER_HORIZONTAL);
                    } else {
                        ivLoading.setVisibility(View.GONE);
                    }
                    isFirstTime = false;
                }

            });
        }
    }


    private WorkLocationAdapter adapter;

    /**
     * 标记Marker
     *
     * @param latitude
     * @param longitude
     */
    public void useLocation(double latitude, double longitude) {
        MarkerOptions markerOptions = new MarkerOptions();
//        markerOptions.title("title");
//        markerOptions.snippet("snippet");
//        markerOptions.draggable(false);
//        markerOptions.zIndex(2);
        markerOptions
                .position(latitude, longitude)
                .icon(R.drawable.icon_coordinate);
        if (marker != null) {
            marker.hideInfoWindow();
            marker.clear();
        }
        marker = mMap.addMarker(markerOptions);
        ivLoading.post(new Runnable() {
            @Override
            public void run() {
                marker.setPositionByPixels(mMapView.getWidth() / 2, mMapView.getHeight() / 2);
            }
        });
        if (!TextUtils.isEmpty(tvInfo.getText())) {
            marker.showInfoWindow();
        }
        ignoreMapSearchOnce = true;
        mMap.moveCamera(latitude, longitude, 17);
        reGeoCodeSearch(latitude, longitude);
    }

    private void refreshAdapter(String query, String city, String province) {
        if (adapter == null) {
            adapter = new WorkLocationAdapter(activity, poiItems, province, city, query, jobId);
            adapter.setHasRecommend(true);
            adapter.setOnItemCallBack((jobId, poiItem) -> {
                if (hasLocationAction()) {//从聊天进来选择职位走这个

                    if (poiItem.getLatLonPoint() == null) {
                        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_LATLNG_IS_NULL)
                                .param("p2", SelectLocationByMapFragment.this.getClass().getSimpleName())
                                .param("p3", String.format("%1s   %2s", query, city))
                                .report();
                        return;
                    }
                    adapter.notifyItemChanged(adapter.getSelectPoi());
                    adapter.setSelectPoi(poiItem);
                    adapter.notifyItemChanged(poiItem);
                    final LatLonPoint latLonPoint = poiItem.getLatLonPoint();
                    job.latitude = latLonPoint.getLatitude();
                    job.longitude = latLonPoint.getLongitude();
                    job.poiTitle = poiItem.getTitle();
                    ignoreMapSearchOnce = true;
                    moveMapTo(job.latitude, job.longitude, job.poiTitle);
                    TLog.info(TAG, "onRegeocodeSearched: 点击的经纬度" + latLonPoint.getLongitude() + "==" + latLonPoint.getLatitude() + poiItem.getTitle());
//                    useLocation();
                } else {//都是之前的流程
//                    PoiLocationTranslator translator = new PoiLocationTranslator(activity, jobId);
//                    translator.poiTranslate(poiItem);
                }
            });
            //只有聊天页面 发送地理位置 才需要设置
            if (isSendLocationProcess()) {
                adapter.setJobWorkAddress(job.workAddress);
            }
            recyclerView.setAdapter(adapter);
        } else {


            //有固定工作地址
            if (checkJobHaveWorkLocation()) {
                if (isSendLocationProcess()) {
                    replaceJobPoi(); // 第一次进来 且是聊天发送地址 才有这个逻辑。拖动地图或者搜索后 会消失
                }
            }


            PoiItem poiItem = LList.getElement(poiItems, 0);
            if (poiItem != null) {
                tvInfo.setText(poiItem.getTitle());
                marker.showInfoWindow();
            } else {
                tvInfo.setText("");
                marker.hideInfoWindow();
            }
            adapter.setData(poiItems, query, city, province);
            adapter.notifyDataSetChanged();
        }


        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager != null) {
            layoutManager.scrollToPosition(0);
        }
    }

    private void replaceJobPoi() {
        if (!isFirstTime) {
            return;
        }

        if (LList.isEmpty(poiItems)) {
            //判断kjob de 经纬度正常
            boolean noLocation = job.latitude <= 0 && job.longitude <= 0
                    && LText.empty(job.poiTitle);
            if (!noLocation) {
                LatLonPoint lng = new LatLonPoint(job.latitude, job.longitude);
                PoiItem poi = new PoiItem();
                poi.setLatLonPoint(lng);
                poi.setTitle(job.workAddress);
                poi.setCityName(job.city);
                poi.setAdName(job.area);
                poi.setCityName(String.valueOf(job.locationIndex));
                poi.setProvinceName(job.province);
                poiItems.add(0, poi);
                tvInfo.setText(job.poiTitle);
                marker.showInfoWindow();
            }
        } else {
            PoiItem p = null;
            for (int i = 0; i < poiItems.size(); i++) {
                if (poiItems.get(i) != null && poiItems.get(i).getTitle().equals(job.poiTitle)) {
                    p = poiItems.get(i);
                    break;
                }
            }
            if (p != null) {
                poiItems.remove(p);
                LatLonPoint lng;

                if (job != null && job.latitude > 0 && job.longitude > 0) {
                    // 913 版本，优先取职位地址中的经纬度，反馈：杭州初拾电子商务 ，搜索后会出现2个。 一模一样， 经纬度会有差别
                    lng = new LatLonPoint(job.latitude, job.longitude);
                    PoiItem poi = new PoiItem();
                    poi.setLatLonPoint(lng);
                    poi.setTitle(job.workAddress);
                    poi.setSnippet(p.getSnippet());
                    poi.setCityName(job.city);
                    poi.setAdName(job.area);
                    poi.setCityName(String.valueOf(job.locationIndex));
                    poi.setProvinceName(job.province);
                    poiItems.add(0, poi);
                    tvInfo.setText(job.poiTitle);
                    marker.showInfoWindow();
                }

            } else {
                //判断kjob de 经纬度正常
                boolean noLocation = job.latitude <= 0 && job.longitude <= 0
                        && LText.empty(job.poiTitle);
                PoiItem poiItem = poiItems.get(0);
                if (!noLocation) {
                    LatLonPoint lng = new LatLonPoint(job.latitude, job.longitude);
                    PoiItem poi = new PoiItem();
                    poi.setLatLonPoint(lng);
                    poi.setTitle(job.workAddress);
                    poi.setCityName(job.city);
                    poi.setAdName(job.area);
//                    poi.setCityCode(String.valueOf(job.locationIndex));
                    poi.setProvinceName(job.province);
                    poiItems.add(0, poi);

                    tvInfo.setText(job.poiTitle);
                    marker.showInfoWindow();
                }

            }
        }
    }

    public void sortPoiItem() {

        PoiItem next = null;

        ArrayList<PoiItem> needMove = new ArrayList<>();
        for (int i = 0; i < poiItems.size(); i++) {
            PoiItem poiItem = poiItems.get(i);

            if (searchPoiItem != null) {
                if (searchPoiItem.getTitle().equals(poiItem.getTitle()) && !PoiItemHelper.poiItemLatLngEquals(searchPoiItem, poiItem)) {
                    needMove.add(poiItem);
                }
                if (PoiItemHelper.poiItemEquals(poiItem, searchPoiItem)) {
                    next = poiItem;
                    needMove.add(poiItem);
                }
            }

            if (TextUtils.isEmpty(poiItem.getCityName())) {
                needMove.add(poiItem);  // 据iOS所说 会有city name 为空的情况 这种POI 需要过滤
            }

        }

        poiItems.removeAll(needMove);
        if (next != null) {
            poiItems.add(0, next);
        }

    }

    public PoiItem getSelectPoiItem() {
        if (adapter != null) {
            return adapter.getSelectPoi();
        }
        return null;
    }

    @Override
    public void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        mMapView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        poiSearchHandler.removeCallbacksAndMessages(null);
        mMapView.onDestroy();
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        mMapView.onSaveInstanceState(outState);
    }
}
