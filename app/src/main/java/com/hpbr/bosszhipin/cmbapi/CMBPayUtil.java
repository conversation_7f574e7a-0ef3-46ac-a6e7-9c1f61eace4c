package com.hpbr.bosszhipin.cmbapi;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.business_export.function.bzb.BzbLocalError;
import com.hpbr.bosszhipin.business_export.function.result.BzbReqResult;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.Objects;

import cmbapi.CMBApi;
import cmbapi.CMBApiFactory;
import cmbapi.CMBConstants;
import cmbapi.CMBRequest;

/**
 * 招行一网通跳转入口
 * <p>
 * 920.201【商业】接入快捷支付
 * scheme_debug： aabaaaahai
 * app_id_deubg:  0010000708
 * <p>
 * scheme_release： aabaaaahai
 * app_id_deubg:  0010631904
 * <p>
 * wiki:https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=140108899
 * 文档：http://openhome.cmbchina.com/paynew/pay/Home
 */
public class CMBPayUtil {

    private static final String TAG = "CMBPayUtil";

    /*app id sp*/
    private static final String SP_ZP_CMB_PAY_UTIL_CMB_APP_ID = Constants.PREFIX + ".SP_ZP_CMB_PAY_UTIL_CMB_APP_ID";

    public static final int REQ_CMB_PAY_CODE = 5999;

    //OtherConfig.CMB_APP_ID
    public static String mLastCMBAppId;

    /**
     * 唤起招行一网通支付
     */
    public static BzbReqResult startPay(Activity activity, CMBParams cmbParams) {
        if (null == cmbParams || !cmbParams.isCMBParamsValid()) {
            TLog.error(TAG, "startPay() --> %s, cmbParams: %s",
                    "数据错误，一网通购买失败", (null != cmbParams ? cmbParams.toString() : ""));
            return BzbReqResult.cmb()
                    .setErrorCode(BzbLocalError.BZB_ERROR_CODE_CMB_4)
                    .setErrorMsg(BzbLocalError.BZB_ERROR_CODE_CMB_4_DESC);
        }

        String cmbAppId = cmbParams.cmbAppId;

        CMBApi cmbApi = CMBApiFactory.getCMBApi();
        if (null == cmbApi || (!Objects.equals(cmbAppId, mLastCMBAppId))) {
            mLastCMBAppId = cmbAppId;
            saveCMBAppId(cmbAppId);
            cmbApi = CMBApiFactory.createCMBAPI(activity, cmbAppId);
        }

        boolean cmbAppInstalled = cmbApi.isCMBAppInstalled();

        /*跳转招行APP*/
        if (!LText.isEmptyOrNull(cmbParams.cmbJumpAppUrl) && cmbAppInstalled) {

            CMBRequest request = new CMBRequest();
            request.requestData = cmbParams.requestData;
            request.CMBJumpAppUrl = cmbParams.cmbJumpAppUrl;
            request.CMBH5Url = cmbParams.cmbH5JumpUrl;
            request.method = cmbParams.method;
            request.isShowNavigationBar = cmbParams.isShowNavigationBar;

            int result = CMBConstants.RequestCode_Params_Error;
            try {
                result = cmbApi.sendReq(request);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (result != CMBConstants.RequestCode_Params_Well_Done) {
                TLog.error(TAG, "startPay() --> %s", "请求订单失败");
                return BzbReqResult.cmb()
                        .setErrorCode(BzbLocalError.BZB_ERROR_CODE_CMB_5)
                        .setErrorMsg(BzbLocalError.BZB_ERROR_CODE_CMB_5_DESC);
            }

        } else if (!LText.isEmptyOrNull(cmbParams.cmbH5JumpUrl)) {

            Intent intent = new Intent(activity, CMBPayH5EntryActivity.class);
            intent.putExtra(CMBConstant.CMB_DATA_H5_JUMP_URL, cmbParams.cmbH5JumpUrl);
            intent.putExtra(CMBConstant.CMB_DATA_METHOD, cmbParams.method);
            intent.putExtra(CMBConstant.CMB_DATA_REQUEST_DATA, cmbParams.requestData);
            AppUtil.startActivityForResult(activity, intent, REQ_CMB_PAY_CODE);

        } else if (!cmbAppInstalled) {
            TLog.error(TAG, "startPay() --> %s", "未检测到您的招商银行客户端");
            return BzbReqResult.cmb()
                    .setErrorCode(BzbLocalError.BZB_ERROR_CODE_CMB_6)
                    .setErrorMsg(BzbLocalError.BZB_ERROR_CODE_CMB_6_DESC);
        }

        return null;
    }

    /**
     * 释放资源
     */
    public static void destroyCMBAPI() {
        mLastCMBAppId = null;
        CMBApiFactory.destroyCMBAPI();
    }

    public static void saveCMBAppId(String cmbAppId) {
        if (cmbAppId == null) cmbAppId = "";
        SpManager.get().user().edit().putString(
                SP_ZP_CMB_PAY_UTIL_CMB_APP_ID, cmbAppId).apply();
    }

    public static String getCMBAppId() {
        return SpManager.get().user().getString(SP_ZP_CMB_PAY_UTIL_CMB_APP_ID, "");
    }

    public static void sendCMBPayResultBroadcast(int respCode, String respMsg, boolean isFromH5) {

        boolean success = respCode == CMBConstant.CMB_RESULT_SUCCEED
                || respCode == CMBConstant.CMB_RESULT_SIGNED_SUCCEED;

        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_PAY_RESULT, "cmbpay")
                .p2(String.valueOf(respCode))
                .p3(respMsg)
                .p4(isFromH5 ? "From H5" : "From APP")
                .report();

        Context context = App.get().getApplicationContext();
        Intent intent = new Intent(Constants.RECEIVER_WX_PAY_RESULT_ACTION);
        intent.putExtra(Constants.DATA_BOOLEAN, success);
        intent.putExtra(Constants.DATA_INT, Constants.APP_PAY_PLATFORM_CMB); // 3为招行一网通订单
        intent.putExtra(Constants.APP_PAY_PLATFORM, Constants.APP_PAY_PLATFORM_CMB); // 3为招行一网通订单
        intent.putExtra(Constants.APP_PAY_RESULT, getCMBResultState(respCode));
        intent.putExtra(Constants.APP_PAY_CODE, String.valueOf(respCode));
        intent.putExtra(Constants.APP_PAY_ERR_MSG, respMsg);
        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        context.sendBroadcast(intent);
    }

    public static int getCMBResultState(int respCode) {
        int resultStatus;
        if (respCode == CMBConstant.CMB_RESULT_SUCCEED) {
            resultStatus = Constants.APP_PAY_RESULT_SUCCEED;
        } else if (respCode == CMBConstant.CMB_RESULT_SIGNED_SUCCEED) {
            resultStatus = Constants.APP_PAY_RESULT_SIGNED_CMB_SUCCEED;
        } else if (respCode == CMBConstant.CMB_RESULT_FAILED) {
            resultStatus = Constants.APP_PAY_RESULT_FAILED;
        } else if (respCode == CMBConstant.CMB_RESULT_SIGNED_FAILED) {
            resultStatus = Constants.APP_PAY_RESULT_SIGNED_CMB_FAILED;
        } else if (respCode == CMBConstant.CMB_RESULT_USER_CANCEL) {
            resultStatus = Constants.APP_PAY_RESULT_USER_CANCEL;
        } else {
            resultStatus = Constants.APP_PAY_RESULT_FAILED;
        }
        return resultStatus;
    }

    public static boolean isCMBAppInstalled(@NonNull Context context) {
        PackageManager packageManager = context.getPackageManager();
        boolean installed = false;
        try {
            PackageInfo packageInfo = packageManager.getPackageInfo("cmb.pb", 0);
            if (packageInfo != null) {
                if ("cmb.pb".equals(packageInfo.packageName) && !TextUtils.isEmpty(packageInfo.versionName)) {
                    installed = true;
                }
            }
        } catch (Exception ignore) {
        }
        TLog.debug(TAG, "【CMBPayUtil】-【isCMBAppInstalled】: %b", installed);
        return installed;
    }
}
