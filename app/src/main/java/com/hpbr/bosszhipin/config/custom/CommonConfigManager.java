package com.hpbr.bosszhipin.config.custom;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.hpbr.bosszhipin.config.CustomHttpDNS;
import com.hpbr.bosszhipin.config.GrayStageManager;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.update.UpgradeManager;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.DialogConfig;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import net.bosszhipin.api.AIAssistInfo;
import net.bosszhipin.api.CommonConfigBatchRequest;
import net.bosszhipin.api.CommonConfigBatchResponse;
import net.bosszhipin.api.CommonConfigRequest;
import net.bosszhipin.api.CommonConfigResponse;
import net.bosszhipin.api.CommonH5DomainMappingRequest;
import net.bosszhipin.api.CommonUserConfigRequest;
import net.bosszhipin.api.CommonUserConfigResponse;
import net.bosszhipin.api.EduDegreeConfigBean;
import net.bosszhipin.api.GetInnerLinkRuleListRequest;
import net.bosszhipin.api.GetInnerLinkRuleListResponse;
import net.bosszhipin.api.bean.H5DomainMappingBean;
import net.bosszhipin.api.bean.InnerLinkRuleBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

/**
 * Created by wangtian on 2018/1/22.
 */

public class CommonConfigManager {
    private static CommonConfigManager mInstance = new CommonConfigManager();
    private static final String SP_NAME_COMMON_CONFIG = "user_common_config";
    private static final String KEY_COMMON_CONFIG = "key_common_config";
    private static final String KEY_COMMON_USER_CONFIG = "key_common_user_config";
    private static final String INNER_LINK_DOMAIN_RULE = "inner_link_domain_rule";
    private static final String KEY_INNER_LINK_DOMAIN_RULE_LIST = "key_inner_link_domain_rule_list";
    private SpImpl sp;
    private CommonConfigResponse commonConfig;//通用配置，不需要登录
    private CommonUserConfigResponse commonUserConfig;//需要登录
    private GetInnerLinkRuleListResponse innerLinkRuleList;//白名单域名
    @NonNull
    private ArrayMap<String, String> h5DomainGuardMap = new ArrayMap<>();
    @NonNull
    private ArrayMap<String, String> h5DomainInterceptMap = new ArrayMap<>();

    private CommonConfigManager() {
        sp = SpFactory.create(App.get(), SP_NAME_COMMON_CONFIG);
        String config = sp.getString(KEY_COMMON_CONFIG);
        String userConfig = sp.getString(KEY_COMMON_USER_CONFIG);

        if (!TextUtils.isEmpty(config)) {
            try {
                CommonConfigResponse configResponse = GsonUtils.getGson().fromJson(config, CommonConfigResponse.class);
                initCommonConfig(configResponse);
            } catch (Exception e) {
            }
        }

        if (!TextUtils.isEmpty(userConfig)) {
            try {
                CommonUserConfigResponse userConfigResponse = GsonUtils.getGson().fromJson(userConfig, CommonUserConfigResponse.class);
                initCommonUserConfig(userConfigResponse);
            } catch (Exception e) {
            }
        }
    }

    /***
     *
     * ---------------------------------------------------------开关控制开始----------------------------------------------------------------
     *
     */

    /**
     * 是否展示校招小红点
     *
     * @return
     */
    public boolean isShowLiveRecruitRedDot() {
        return commonUserConfig != null && commonUserConfig.liveRecruitRedPoint == 1;
    }

    //微信提醒开关灰度
    public boolean isGrayOpenWXNotice() {
        return commonUserConfig != null && commonUserConfig.weiXinRemind;

    }

    //是否显示视频招呼灰度
    public boolean isShowVideoGreatting(int friendSource) {
        if (friendSource == ContactBean.FROM_BOSS) {
            return commonUserConfig != null && commonUserConfig.videoFastReply;
        }
        return false;
    }

    public boolean isExchangeWxExtendPhone() {
        return commonUserConfig != null && commonUserConfig.exchangeWxExtendPhone;
    }

    /**
     * 清空校招红点
     */
    public void clearLiveRecruitRedDot() {
        if (isShowLiveRecruitRedDot()) {
            commonUserConfig.liveRecruitRedPoint = 0;
        }
    }

    /**
     * 912.29 未完善牛人查看职位个数上限
     *
     * @return
     */
    public int getInCompleteViewJobLimit() {
        if (commonUserConfig != null) {
            return commonUserConfig.zpgeek_incomplete_view_job_limit;
        }
        return 3;
    }

    public int getListItemLimit() {
        if (commonConfig != null && commonConfig.list_exposure != null && commonConfig.list_exposure.item_limit > 0) {
            return commonConfig.list_exposure.item_limit;
        }

        return 15;
    }

    public boolean isGeekF1Location() {
        return commonConfig != null && commonConfig.f1_location != null && commonConfig.f1_location.geek == 1;
    }

    public boolean isBossF1Location() {
        return commonConfig != null && commonConfig.f1_location != null && commonConfig.f1_location.boss == 1;
    }

    public int getListTimeLimit() {
        if (commonConfig != null && commonConfig.list_exposure != null && commonConfig.list_exposure.time_limit > 0) {
            return commonConfig.list_exposure.time_limit;
        }
        return 60;
    }

    public boolean isScreenShot() {
        return commonConfig != null && commonConfig.android_config != null && commonConfig.android_config.disable_screen_shot == 0;
    }

    /**
     * 三个tab灰度
     */
    public boolean isMomentTab1117TestUser() {
        if (commonUserConfig != null && commonUserConfig.momentGrayConfig != null) {
            return commonUserConfig.momentGrayConfig.isMomentTab1117TestUser == 1;
        }
        return false;
    }

    public int getRecCover1126() {
        if (commonUserConfig != null && commonUserConfig.momentGrayConfig != null) {
            return commonUserConfig.momentGrayConfig.recCover1126;
        }
        return 0;//默认对照组
    }

    public boolean isMomentDefaultVideoTabUser() {
        if (commonUserConfig != null && commonUserConfig.momentGrayConfig != null) {
            return commonUserConfig.momentGrayConfig.isMomentDefaultVideoTabUser == 1;
        }
        return false;
    }

    public CommonUserConfigResponse.MomentActivityTabConfig getMomentActivityTabConfig() {
        if (commonUserConfig != null && commonUserConfig.momentGrayConfig != null) {
            return commonUserConfig.momentGrayConfig.activityTabConfig;
        }
        return null;
    }

    //1006 搜索是否进入有了tab
    public boolean isSearchDefaultContent() {
        if (commonUserConfig != null && commonUserConfig.momentGrayConfig != null) {
            return commonUserConfig.momentGrayConfig.isDefaultContentTabUser;
        }
        return false;
    }

    public boolean isGeekExpose2OtdGray() {
        if (commonUserConfig != null) {
            return commonUserConfig.geekExpose2OtdGray == 1;
        }
        return false;
    }

    public boolean isGetNewFound() {
        if (commonUserConfig != null) {
            return commonUserConfig.discoverGray == 1;
        }
        return false;
    }

    //909 是否显示rta窗口
    public boolean isShowRtaWindow() {
        if (commonConfig != null && commonConfig.rta_config != null && commonConfig.rta_config.baidu_show == 1) {
            return true;
        }
        return false;
    }

    /**
     * C端搜索  标签过滤器灰度  是否点击之后回填到输入框
     *
     * @return
     */
    public boolean searchLabelFilterIsFillToInput() {
        return commonUserConfig != null && commonUserConfig.zpgeek_search_label_filter == 1;
    }

    //8.04增加http连接超时，默认最少15s
    public int getHttpConnectTimeout() {
        int httpConnectTimeout = 0;
        if (commonConfig != null && commonConfig.android_config != null && commonConfig.android_config.http_connect_timeout != 0) {
            httpConnectTimeout = commonConfig.android_config.http_connect_timeout;
        }
        if (httpConnectTimeout >= 15) {
            return httpConnectTimeout;
        } else {
            return 15;
        }
    }

    //是否关闭hook位置相关api 9.03隐私合规需求添加
    public boolean isCloseHookLocationApi() {
        return commonConfig != null && commonConfig.android_config != null && commonConfig.android_config.close_hook_loc == 1;
    }

    public boolean isAxbInterviewForceNotice() {
        if (commonUserConfig != null) {
            return commonUserConfig.axbInterviewForceNotice;
        }
        return false;
    }

    public String getWeiXinBindJumpPath() {
        if (commonUserConfig != null) {
            return commonUserConfig.wxNotifyBindJumpPath;
        }
        return "";
    }

    public boolean geekInterestJobSpread() {
        if (commonUserConfig != null) {
            return commonUserConfig.geekInterestJobSpread;
        }
        return false;
    }

    public String getHunterMyOrderUrl() {
        if (commonConfig == null || commonConfig.hunter_config == null) {
            return null;
        }
        return commonConfig.hunter_config.intent_myorder_drawer_default_url;
    }

    public String getHunterRecommendUrl() {
        if (commonConfig == null || commonConfig.hunter_config == null) {
            return null;
        }
        return commonConfig.hunter_config.intent_recommend_geek_drawer_default_url;
    }

    public boolean isProgressStyle() {
        if (commonUserConfig != null) {
            return commonUserConfig.progressStyle;
        }
        return false;
    }

    public AIAssistInfo getAIAssistInfo() {
        if (commonUserConfig != null) {
            return commonUserConfig.aiAssistInfo;
        }
        return null;
    }

    public void updateAIAssistInfo(AIAssistInfo aiAssistInfo) {
        if (commonUserConfig != null) {
            commonUserConfig.aiAssistInfo = aiAssistInfo;
            GrayStageManager.getInstance().setAiAssistInfo(aiAssistInfo);
        }
    }


    public boolean isBeginQuestionGray() {
        return commonUserConfig != null && commonUserConfig.bossOpenChatQuestionOptimize;
    }

    /**
     * 1205.92【BC】新版聊天部分用户未读勾选策略优化
     *
     * @return
     */
    public int isF2ContactUnreadLimit() {
        if (commonUserConfig == null) {
            return -1;
        }
        return commonUserConfig.f2ContactUnreadLimit;
    }


    public boolean isBossBlockSwitch() {
        if (commonUserConfig == null) {
            return false;
        }
        return commonUserConfig.bossChatBlockSwitch == 1;
    }

    public int getBossBlockThreshold() {
        if (commonUserConfig == null) {
            return -1;
        }
        return commonUserConfig.bossChatBlockThreshold;
    }

    /**
     * 1121.91【B/C】新招呼消息的【推荐排序】模式@笑笑@周鹏祖@郭俊威@邹东@陈青云@姬帅@刘鹏飞
     *
     * @return
     */
    public int getGreetingRecSort() {
        if (commonUserConfig != null) {
            return commonUserConfig.greetingRecSort;
        }
        return 0;
    }

    public int getUnReplyCheckInterval() {
        if (commonUserConfig != null) {
            return commonUserConfig.unReplyCheckInterval;
        }
        return Integer.MAX_VALUE;
    }

    public boolean needShowExchangeGuide() {
        if (commonUserConfig != null) {
            return commonUserConfig.F2AcceptGuide;
        }

        return false;
    }

    public boolean isOptEncryptResume() {
        return commonUserConfig != null && commonUserConfig.encryptResumeOpt == 1;
    }

    public long getWidgetRefreshPeriod() {
        if (commonUserConfig != null) {
            return commonUserConfig.widgetRefreshPeriod;
        }
        return 60;
    }

    public CommonUserConfigResponse.WidgetInfo getWidgetInfo() {
        if (commonUserConfig != null) {
            return commonUserConfig.widgetInfo;
        }
        return null;
    }

    public CommonUserConfigResponse.MsgStatusConfigs getMsgStatusConfigs () {
        if (commonUserConfig != null) {
            return commonUserConfig.msgStatusConfigs;
        }
        return null;
    }

    public String getComponentText() {
        if (commonUserConfig != null && commonUserConfig.componentText != null) {
            return commonUserConfig.componentText;
        }
        return "";
    }

    public boolean shouldShowSoundRedPoint(long time) {
        if (commonUserConfig != null) {
            return commonUserConfig.soundRedPointRule == -1 || (commonUserConfig.soundRedPointRule > 0 && DateUtil.isWithinXDays(time, commonUserConfig.soundRedPointRule));
        }
        return false;
    }

    /**
     * 1303.1.4【智能沟通助手10期】交互优化：F3 聊天列表页，聊天设置中是否展示 AI 沟通助手设置项
     *
     * @return boolean
     */
    public boolean showAiChatHelper() {
        return commonUserConfig != null && commonUserConfig.aiChatHelperEnable == 1;
    }

    /***
     *
     *
     * ---------------------------------------------------------开关控制结束----------------------------------------------------------------
     *
     */


    private void initCommonConfig(CommonConfigResponse commonConfigResponse) {
        if (commonConfigResponse == null) {
            return;
        }
        if (commonConfig != null) { //如果需要实时处理，可以放这里
            commonConfig.eduDegreeConfigList = commonConfigResponse.eduDegreeConfigList;
            return;
        }

        commonConfig = commonConfigResponse;

        SP.get().putBoolean("toggle_f1_location", isGeekF1Location());
        if (commonConfig != null && commonConfig.android_config != null) {
            GrayStageManager.getInstance().setBlueInterviewStatus(commonConfig.relation_config.blue_interview_open);
            UpgradeManager.supportVivoStore = commonConfig.android_config.support_vivo_store == 1 ? true : false;
            //多进程使用 不能用这个CommonConfigManager  兜底策略
            SpManager.get().global().edit().putInt(CustomHttpDNS.SUPPORT_MQTT_IPV6_ONLY, commonConfig.android_config.support_mqtt_ipv6_only).apply();
            SpManager.get().global().edit().putInt(DNSCommon.SUPPORT_CHECK_IPV6_ONLY, commonConfig.android_config.support_check_ipv6_only).apply();
        }
    }

    private void initCommonUserConfig(CommonUserConfigResponse commonUserConfigResponse) {
        if (commonUserConfigResponse == null) {
            return;
        }
        if (commonUserConfig != null) {//如果需要实时处理，可以放这里
            commonUserConfig.weiXinRemind = commonUserConfigResponse.weiXinRemind;
            commonUserConfig.liveRecruitRedPoint = commonUserConfigResponse.liveRecruitRedPoint;
            commonUserConfig.liveRecruitF1Tip = commonUserConfigResponse.liveRecruitF1Tip;
            commonUserConfig.momentGrayConfig = commonUserConfigResponse.momentGrayConfig;
            commonUserConfig.exchangeOptimization = commonUserConfigResponse.exchangeOptimization;
            commonUserConfig.greetingRecSort = commonUserConfigResponse.greetingRecSort;
            commonUserConfig.geekInterestJobSpread = commonUserConfigResponse.geekInterestJobSpread;
            commonUserConfig.aiAssistInfo = commonUserConfigResponse.aiAssistInfo;
            commonUserConfig.dialogConfig = commonUserConfigResponse.dialogConfig;
            commonUserConfig.msgStatusConfigs = commonUserConfigResponse.msgStatusConfigs;
            GrayStageManager.getInstance().setAiAssistInfo(commonUserConfigResponse.aiAssistInfo);
            return;
        }

        commonUserConfig = commonUserConfigResponse;
        GrayStageManager.getInstance().setExchangeOptGray(commonUserConfigResponse.exchangeOptGray);
    }

    public static CommonConfigManager getInstance() {
        return mInstance;
    }

    public void clear() {
        sp.remove(KEY_COMMON_USER_CONFIG);
    }

    public void refresh() {
        CommonConfigBatchRequest batchRequest = new CommonConfigBatchRequest(new ApiRequestCallback<CommonConfigBatchResponse>() {
            @Override
            public void onSuccess(ApiData<CommonConfigBatchResponse> data) {
                if (data.resp == null) {
                    return;
                }

                if (data.resp.commonConfigResponse != null) {
                    initCommonConfig(data.resp.commonConfigResponse);
                    sp.putString(KEY_COMMON_CONFIG, GsonUtils.getGson().toJson(data.resp.commonConfigResponse));
                }

                if (data.resp.h5DomainMapping != null && LList.isNotEmpty(data.resp.h5DomainMapping.mappings)) {
                    for (H5DomainMappingBean bean : data.resp.h5DomainMapping.mappings) {
                        if (bean != null && LText.notEmpty(bean.mainDomain) && LText.notEmpty(bean.bakDomain)) {
                            h5DomainGuardMap.put(bean.mainDomain, bean.bakDomain);
                        }
                    }
                }

                if (data.resp.h5DomainMapping != null && LList.isNotEmpty(data.resp.h5DomainMapping.mappings_reveal)) {
                    for (H5DomainMappingBean bean : data.resp.h5DomainMapping.mappings_reveal) {
                        if (bean != null && LText.notEmpty(bean.mainDomain) && LText.notEmpty(bean.bakDomain)) {
                            h5DomainInterceptMap.put(bean.mainDomain, bean.bakDomain);
                        }
                    }
                }

                if (data.resp.innerLinkRuleListResponse != null) {
                    SpManager.get().global(INNER_LINK_DOMAIN_RULE).edit().putString(KEY_INNER_LINK_DOMAIN_RULE_LIST, GsonUtils.toJson(data.resp.innerLinkRuleListResponse)).apply();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        batchRequest.commonConfigRequest = new CommonConfigRequest();
        batchRequest.h5DomainMappingRequest = new CommonH5DomainMappingRequest();
        batchRequest.getInnerLinkRuleListRequest = new GetInnerLinkRuleListRequest();
        batchRequest.execute();
    }

    public void refreshUserConfig() {
        if (TextUtils.isEmpty(UserManager.getToken())) {
            return;
        }
        CommonUserConfigRequest request = new CommonUserConfigRequest(new ApiRequestCallback<CommonUserConfigResponse>() {
            @Override
            public void onSuccess(ApiData<CommonUserConfigResponse> data) {
                if (data.resp != null) {
                    initCommonUserConfig(data.resp);
                    MqttConfig.setOpenMqttTls(data.resp.mqttTlsOn);
                    sp.putString(KEY_COMMON_USER_CONFIG, GsonUtils.getGson().toJson(data.resp));
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }


    /**
     * 1104.89 是否使用V组提供的音频采集功能
     */
    public boolean audioCaptureByVGroup() {
        return commonUserConfig != null && commonUserConfig.audioCaptureByVGroup;
    }

    // 游客模式 内存缓存
    private boolean tourist = false;

    public boolean isTourist() {
        return tourist;
    }

    public void setTourist(boolean tourist) {
        this.tourist = tourist;
    }

    public void cleanUserConfig() {
        sp.remove(KEY_COMMON_USER_CONFIG);

        commonUserConfig = null;
    }

    public int getSuggestInputStatusPeriod() {
        if (commonUserConfig != null) {
            return commonUserConfig.zpgeek_suggest_input_status_period;
        } else {
            return 0;
        }
    }


    /**
     * 获取教育经历最小间隔时间
     */
    public int getEduMinInterval(int degreeType) {
        if (commonConfig == null || LList.isEmpty(commonConfig.eduDegreeConfigList)) {
            return EduDegreeConfigBean.EDU_DEFAULT_MIN_INTERVAL;
        }
        for (EduDegreeConfigBean item : commonConfig.eduDegreeConfigList) {
            if (item != null && item.degreeType == degreeType && item.minInterval != null) {
                return item.minInterval;
            }
        }
        return EduDegreeConfigBean.EDU_DEFAULT_MIN_INTERVAL;
    }

    /**
     * 获取教育经历最大间隔时间
     */
    public int getEduMaxInterval(int degreeType) {
        if (commonConfig == null || LList.isEmpty(commonConfig.eduDegreeConfigList)) {
            return EduDegreeConfigBean.EDU_DEFAULT_MAX_INTERVAL;
        }
        for (EduDegreeConfigBean item : commonConfig.eduDegreeConfigList) {
            if (item != null && item.degreeType == degreeType && item.maxInterval != null) {
                return item.maxInterval;
            }
        }
        return EduDegreeConfigBean.EDU_DEFAULT_MAX_INTERVAL;
    }

    @Nullable
    public List<InnerLinkRuleBean> getInnerLinkRuleList() {
        List<InnerLinkRuleBean> ruleList = null;
        if (innerLinkRuleList == null) {
            String ruleListConfig = SpManager.get().global(INNER_LINK_DOMAIN_RULE).getString(KEY_INNER_LINK_DOMAIN_RULE_LIST, null);
            if (LText.notEmpty(ruleListConfig)) {
                innerLinkRuleList = GsonUtils.getGson().fromJson(ruleListConfig, GetInnerLinkRuleListResponse.class);
                ruleList = innerLinkRuleList.ruleList;
            }
        } else {
            ruleList = innerLinkRuleList.ruleList;
        }
        return ruleList;
    }

    @NonNull
    public ArrayMap<String, String> getH5DomainGuardMap() {
        return h5DomainGuardMap;
    }

    @NonNull
    public ArrayMap<String, String> getH5DomainInterceptMap() {
        return h5DomainInterceptMap;
    }

    public List<DialogConfig> getDialogConfig() {
        if (commonUserConfig != null) {
            return commonUserConfig.dialogConfig;
        } else {
            return null;
        }
    }
}
