package com.hpbr.bosszhipin.base.helper;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bzl.platform.BuildKey;
import com.bzl.platform.SharedConst;
import com.bzl.platform.kv.SharedProvider;
import com.bzl.platform.provider.BuildInfoProvider;
import com.bzl.safe.crashprotect.BZLCrashProtectManager;
import com.bzl.safe.crashprotect.bean.BZLCrashStrategyBean;
import com.bzl.safe.crashprotect.common.BZLCrashProtectConfig;
import com.bzl.safe.crashprotect.common.BZLCrashStrategyFactory;
import com.bzl.safe.crashprotect.interfaces.ICleanFileAction;
import com.bzl.safe.crashprotect.interfaces.IExceptionCatchAction;
import com.bzl.safe.crashprotect.interfaces.IHotFix;
import com.bzl.safe.crashprotect.interfaces.ILogAction;
import com.bzl.safe.crashprotect.internal.enums.BZLErrorReason;
import com.bzl.safe.hotfix.BZLTinkerManager;
import com.bzl.safe.hotfix.internal.report.interfaces.IHotFixResult;
import com.bzl.safe.hotfix.internal.report.interfaces.ILogInterface;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.helper.sync.ApmConfigBean;
import com.hpbr.bosszhipin.base.helper.sync.ApmConfigMan;
import com.hpbr.bosszhipin.common.app.ActivityThreadHook;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.manager.apm.func.ApmRunnable;
import com.hpbr.bosszhipin.module.launcher.LauncherPopActivity;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.BuglyStrategy;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2024/6/4 15:14
 *     desc   : 线上容灾初始化
 *     version:
 * </pre>
 */
@Keep
@SuppressWarnings("unused")
public class CrashProtectInitHelper {
    public static final String TAG = "CrashProtectManager";
    /**
     * 崩溃防护点
     */
    public static final String ACTION_CRASH_PROTECT = "action_crash_protect";
    /**
     * 捕获的异常
     */
    public static final String TYPE_CRASH_CATCH = "type_crash_catch";
    /**
     * 未捕获的异常
     */
    public static final String TYPE_CRASH_THROW = "type_crash_throw";
    /**
     * 业务运行异常
     */
    public static final String TYPE_RUN_EXCEPTION = "type_run_exception";
    public static boolean isRunSync = false;
    private static Boolean isAgreePrivacyAgreement;

    private CrashProtectInitHelper() {
    }

    public static void init(Context base, Application application) {
        BZLCrashProtectManager.init(application, createConfig(application), isSuccess -> {
            if (!isSuccess) {
                try {
                    ActivityThreadHook.hookActivityThread();
                } catch (Throwable throwable) {
                    TLog.error(TAG, "hookActivityThread Error. %s", throwable);
                }
            }
        });
    }

    /**
     * 是否初始化成功
     */
    public static boolean isInitSuccess() {
        return BZLCrashProtectManager.isEnable();
    }

    /**
     * 注册崩溃Catcher
     */
    public static void registerCrashCatcher() {
        BZLCrashProtectManager.registerCrashCatcher();
    }


    private static BZLCrashProtectConfig createConfig(@NonNull Application application) {
        return new BZLCrashProtectConfig().Builder()
                //设置默认配置
                .actionIDefaultStrategy(() -> makeDefaultList(application))
                //删除文件
                .actionCleanFile(new ICleanFileAction() {
                    @Override
                    public SharedPreferences getMMKVUser(@Nullable String mmkvSpaceName) {
                        if (TextUtils.isEmpty(mmkvSpaceName)) {
                            return SpManager.get().user();
                        } else {
                            return SpManager.get().user(mmkvSpaceName);
                        }
                    }

                    @Override
                    public SharedPreferences getMMKVGlobal(@Nullable String mmkvSpaceName) {
                        if (TextUtils.isEmpty(mmkvSpaceName)) {
                            return SpManager.get().global();
                        } else {
                            return SpManager.get().global(mmkvSpaceName);
                        }
                    }

                    @Override
                    public void custom(Map<String, Object> customParams) {
                        ApmRunnable.handleDisposable(customParams);
                    }

                    @Override
                    public String getUserId() {
                        return String.valueOf(UserManager.getUID());
                    }

                    @Override
                    public String getIdentity() {
                        return String.valueOf(UserManager.getUserRole().get());
                    }

                    @Override
                    public String getDid() {
                        return MobileUtil.getUniqId(App.getAppContext());
                    }
                })
                //跳转协议链接
                .actionHandleUrl((context, url) -> new ZPManager(context, url).handler())
                //兜住异常处理
                .actionExceptionCatch(new IExceptionCatchAction() {
                    @Override
                    public void onExceptionCatchSuccess(String stackTraceString) {
                        reportApmCrashForProtected(stackTraceString, TYPE_CRASH_CATCH);
                    }

                    @Override
                    public void onExceptionCatchFail(Throwable throwable) {
                        reportApmCrashForProtected(Log.getStackTraceString(throwable), TYPE_CRASH_THROW);
                        tryRescueBeforeCrash(throwable);
                    }
                })
                //埋点和log
                .actionLog(new ILogAction() {
                    @Override
                    public void logOnline(String tag, String format, Object... args) {
                        TLog.info(tag, format, args);
                    }

                    @Override
                    public void reportApm(@BZLErrorReason String errorType, String msg, String expandMsg) {
                        startReportApm(errorType, msg, expandMsg);
                    }
                })
                .actionIHotFix(new IHotFix() {
                    @Override
                    public void hotFixSilent() {
                        hotFix(application, false);
                    }

                    @Override
                    public void hotFixDialog() {
                        hotFix(application, true);
                    }

                })
                .actionISafeMode(() -> {
                    //ignore
                })
                //一次性配置弹框 在主页面延时弹出
                .actionDisposableConfig(() -> {
                    if (null == ForegroundUtils.get()) {
                        ForegroundUtils.init(application);
                    }
                    Activity topActivity = ForegroundUtils.get().getTopActivity();
                    if (null == topActivity) return false;//App#attachBaseContext和onCreate崩溃，没有值
                    boolean isTopLauncherAct = topActivity instanceof WelcomeActivity || topActivity instanceof LauncherPopActivity;
                    return !isTopLauncherAct;
                });
    }

    private static void hotFix(@NonNull Application application, boolean showDialog) {
        String apmKey = BuildInfoProvider.isDebuggable() ? BuildInfoProvider.getString(BuildKey.APM_KEY_DEBUG, "") : BuildInfoProvider.getString(BuildKey.APM_KEY_RELEASE, "");
        String iv = BuildInfoProvider.isDebuggable() ? BuildInfoProvider.getString(BuildKey.APM_IV_DEBUG, "") : BuildInfoProvider.getString(BuildKey.APM_IV_RELEASE, "");
        String encUserId = ApmConfigMan.encUserId(String.valueOf(UserManager.getUID()), apmKey, iv);
        String did = MobileUtil.getUniqId(Utils.getApp());
        ILogInterface iLogInterface = new ILogInterface() {
            @Override
            public void reportApm(@NonNull String action, @NonNull String type, @Nullable String p2, @Nullable String p3, @Nullable String p4, @Nullable String p5, @Nullable String p6) {
                reportApmForHotFix(action, type, p2, p3, p4, p5, p6);
            }

            @Override
            public void logDebug(String tag, String msg) {
                TLog.debug(tag, msg);
            }

            @Override
            public void logInfo(String tag, String msg) {
                TLog.info(tag, msg);
            }

            @Override
            public void logError(String tag, String msg) {
                TLog.error(tag, msg);
            }

            @Override
            public void logError(String tag, String msg, Throwable tr) {
                TLog.error(tag, tr, msg);
            }
        };
        IHotFixResult iHotFixResult = new IHotFixResult() {
            @Override
            public void onSuccess() {
                BZLCrashProtectManager.cleanForceHotFixFlag();
            }

            @Override
            public void onFail() {
                //ignore
            }

            @Override
            public void onNoPatch() {
                BZLCrashProtectManager.cleanForceHotFixFlag();
            }
        };
        if (showDialog) {
            BZLTinkerManager.showDialogHotFix(application, encUserId, did, "crashProtect", iLogInterface, iHotFixResult);
        } else {
            BZLTinkerManager.silentHotFix(application, encUserId, did, "crashProtect", iLogInterface, iHotFixResult);
        }
    }

    /**
     * 本地默认策略
     */
    @NonNull
    private static List<BZLCrashStrategyBean> makeDefaultList(@NonNull Application application) {
        List<BZLCrashStrategyBean> list = new ArrayList<>();
        //boss进程
        String bossProgressName = application.getPackageName();
        list.add(BZLCrashStrategyFactory.create("android.widget.Toast\\$TN", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("io.rong.imlib.ConnectChangeReceiver", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("android.hardware.SystemSensorManager\\$SensorEventQueue.dispatchSensorEvent_", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("com.tencent.wcdb.database.SQLiteConnection.nativePrepareStatement", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("android.widget.Editor\\$ActionPinnedPopupWindow.computeLocalPosition", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("android.view.ViewGroup.resetCancelNextUpFlag", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("android.widget.TextView.canPasteAsPlainText", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("android.app.RemoteServiceException\\$CannotDeliverBroadcastException", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("can't deliver broadcast", bossProgressName));
        list.add(BZLCrashStrategyFactory.create("Activity client record must not be null to execute transaction item", bossProgressName));

        //mqtt进程
        String mqttProgressName = application.getPackageName() + ":mms";
        list.add(BZLCrashStrategyFactory.create("notification", mqttProgressName));
        list.add(BZLCrashStrategyFactory.create("MMSReceiver", mqttProgressName));
        list.add(BZLCrashStrategyFactory.create("onGetTokenComplete", mqttProgressName));

        return list;
    }

    //=================================配置=================================

    /**
     * 更新本地的配置信息
     * 外层是整个公共配置结构 {"crashProtectV1": {"list":[], "versionCode": 0}}
     */
    public static void updateConfigParent(@NonNull JSONObject jsonObject) {
        BZLCrashProtectManager.updateConfigParent(jsonObject);
    }

    public static void updateConfigParent(@NonNull String jsonString) {
        BZLCrashProtectManager.updateConfigParent(jsonString);
    }

    /**
     * 一次性配置信息，不存本地，不需命中崩溃，直接执行
     * 外层是整个公共配置结构 {"crashProtectV1": {"list":[], "versionCode": 0}}
     */
    public static void executeActionsDisposableParent(@NonNull Map<String, Object> disposableConfig) {
        BZLCrashProtectManager.executeActionsDisposableParent(disposableConfig);
    }

    /**
     * 检查是否有一次性配置
     */
    public static boolean checkDisposableConfig(@NonNull Map<String, Object> disposableConfig) {
        return BZLCrashProtectManager.checkDisposableConfig(disposableConfig);
    }

    /**
     * 检查是否有公共配置或者私有配置
     */
    public static boolean checkConfig(@NonNull JSONObject jsonObject) {
        return BZLCrashProtectManager.checkConfig(jsonObject);
    }

    public static boolean checkConfig(@NonNull String jsonString) {
        return BZLCrashProtectManager.checkConfig(jsonString);
    }

    /**
     * native崩溃检测
     */
    @SuppressWarnings("all")
    public static boolean checkNativeCrash(int crashType, String errorMessage, @NonNull String errorStack) {
        if (crashType == BuglyStrategy.a.CRASHTYPE_NATIVE) {
            return BZLCrashProtectManager.checkNativeCrash(errorMessage, errorStack);
        }
        return false;
    }

    /**
     * 同意了隐私协议
     */
    public static void allowPrivacyAgreement() {
        isAgreePrivacyAgreement = true;
        SharedProvider.getInstance(SharedConst.Global.FILE_NAME).edit().put(SharedConst.Global.KEY_ALLOW_PRIVACY, true).commit();
    }

    public static boolean isAllowPrivacyAgreement() {
        if (null == isAgreePrivacyAgreement) {
            isAgreePrivacyAgreement = SharedProvider.getInstance(SharedConst.Global.FILE_NAME).getBoolean(SharedConst.Global.KEY_ALLOW_PRIVACY);
        }
        return isAgreePrivacyAgreement;
    }

    /**
     * 是否需要强制热修复，给Apm传递参数使用
     */
    public static boolean checkForceHotFixFlag(){
        return BZLCrashProtectManager.checkForceHotFixFlag();
    }

    /**
     * Apm热修复完需要调用清空
     */
    public static void cleanForceHotFixFlag(){
        //case1: 修复成功了
        //case2: 没有补丁了
        BZLCrashProtectManager.cleanForceHotFixFlag();
    }

    /**
     * crash异常防护，兜住的崩溃在这里进行埋点上报
     * 为什么这么做？ 重复报点问题
     * 1、apm接收到异常，将异常缓存本地，在未进行删除的时候，crash框架接收异常进行数据上报，上报重复两条
     */
    @SuppressWarnings("all")
    private static void reportApmCrashForProtected(@Nullable String stack, @NonNull String type) {
        if (null == stack) return;
        try {
            ApmAnalyzer.create()
                    .action(ACTION_CRASH_PROTECT, type)
                    .p4(stack)
                    .reportNow();
        } catch (Throwable e) {
            TLog.error(TAG, "report crash error %s", Log.getStackTraceString(e));
        }
    }

    private static void startReportApm(@BZLErrorReason String errorType, String msg, String expandMsg) {
        try {
            ApmAnalyzer.create()
                    .action(ACTION_CRASH_PROTECT, errorType)
                    .p2(msg)
                    .p3(expandMsg)
                    .reportNow();
        } catch (Throwable throwable) {
            TLog.error(TAG, "report apm error %s", Log.getStackTraceString(throwable));
        }
    }

    private static void reportApmForHotFix(@NonNull String action, @NonNull String type, @Nullable String p2, @Nullable String p3, @Nullable String p4, @Nullable String p5, @Nullable String p6) {
        try {
            ApmAnalyzer.create()
                    .action(action, type)
                    .p2(p2)
                    .p3(p3)
                    .p4(p4)
                    .p5(p5)
                    .p6(p6)
                    .reportNow();
        } catch (Throwable throwable) {
            TLog.error(TAG, "report apm error %s", Log.getStackTraceString(throwable));
        }
    }

    public static synchronized void tryRescueBeforeCrash(@NonNull Throwable throwable) {
        if (isRunSync || !isAllowPrivacyAgreement()) return;
        isRunSync = true;
        Thread thread = AppThreadFactory.createThread(() -> {
            try {
                requestConfigAndCheckJavaCrash(throwable);
            } catch (Throwable t) {
                TLog.error(TAG, "tryRescueBeforeCrash error");
            }
        });
        thread.start();
        try {
            thread.join(5000);
        } catch (Throwable t) {
            Thread.currentThread().interrupt();
            TLog.error(TAG, t, "tryRescueBeforeCrash interrupt");
        }
    }

    /**
     * 同步拉配置并且匹配崩溃
     */
    private static void requestConfigAndCheckJavaCrash(@NonNull Throwable throwable) {
        String userId;
        String did;
        try {
            userId = String.valueOf(UserManager.getUID());
        } catch (Throwable t) {
            userId = "0";
            TLog.error(TAG, t, "userId error ");
        }
        try {
            did = MobileUtil.getUniqId(Utils.getApp());
        } catch (Throwable t) {
            did = UUID.randomUUID().toString();
            TLog.error(TAG, t, "did error ");
        }
        try {

            ApmConfigBean configSyncBean = ApmConfigMan.getConfigSync(userId, did);

            if (configSyncBean == null) {
                return;
            }
            //获取crash防护参数
            //--私有配置
            if (null != configSyncBean.pri_config_json_string && checkConfig(configSyncBean.pri_config_json_string)) {
                updateConfigParent(configSyncBean.pri_config_json_string);
            }
            //--公共配置
            if (null != configSyncBean.pub_config_json_string && checkConfig(configSyncBean.pub_config_json_string)) {
                updateConfigParent(configSyncBean.pub_config_json_string);
            }
            //--一次性配置
            if (null != configSyncBean.disposable_config && !configSyncBean.disposable_config.isEmpty() && checkDisposableConfig(configSyncBean.disposable_config)) {
                executeActionsDisposableParent(configSyncBean.disposable_config);
            }

            BZLCrashProtectManager.checkJavaCrash(throwable);
        } catch (Throwable t) {
            TLog.error(TAG, t, "requestConfigAndCheckJavaCrash");
        }
    }

}
