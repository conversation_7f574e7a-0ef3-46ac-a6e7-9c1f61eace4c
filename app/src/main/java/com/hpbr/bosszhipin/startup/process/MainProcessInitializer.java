package com.hpbr.bosszhipin.startup.process;

import android.annotation.SuppressLint;
import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.TwlUtilsInit;
import com.bszp.kernel.DataKernel;
import com.bszp.kernel.account.AccountHelper;
import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.request.target.Target;
import com.bzl.sdk.voice.VoiceConfig;
import com.bzl.sdk.voice.VoiceManager;
import com.bzl.sdk.voice.constant.TicketHeaderConstant;
import com.bzl.sdk.voice.internal.utils.IVoiceLog;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.facebook.imagepipeline.listener.RequestListener;
import com.hpbr.apm.event.AnalyzerConst;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.PushCallback;
import com.hpbr.bosszhipin.common.AppTrimMemoryCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.hpbr.bosszhipin.common.helper.ShuMengHelper;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.config.custom.ThemeConfigManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.ListAnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.imageloader.BzlOkHttpNetworkFetcher;
import com.hpbr.bosszhipin.imageloader.RequestImageUrlCallback;
import com.hpbr.bosszhipin.imageloader.glide.OkHttpUrlLoader;
import com.hpbr.bosszhipin.manager.BlackAppManager;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.MessageBean;
import com.hpbr.bosszhipin.module.contacts.manager.ChatMessageFactory;
import com.hpbr.bosszhipin.push.PushSdkConfig;
import com.hpbr.bosszhipin.push.PushSdkManager;
import com.hpbr.bosszhipin.receiver.ChatReceiver;
import com.hpbr.bosszhipin.startup.DBUpgradeProcessor;
import com.hpbr.bosszhipin.startup.process.callback.BzlFrescoRequestListener;
import com.hpbr.bosszhipin.utils.AppStatusUtil;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.io.file.FileSystemSpaceChecker;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.NotificationConfig;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.Util;
import com.twl.http.error.ErrorReason;
import com.twl.mms.utils.MqttUtil;
import com.twl.utils.CollectionUtil;
import com.twl.utils.GsonUtils;
import com.zhihu.matisse.Matisse;

import net.bosszhipin.api.SignRefreshHttpResponse;
import net.bosszhipin.base.Constant;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.io.InputStream;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.net.ssl.SSLHandshakeException;

import cn.shuzilm.core.Listener;
import cn.shuzilm.core.Main;
import message.handler.ConnectHandler;
import message.handler.ReceiverHandler;
import message.handler.SenderHandler;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;
import message.server.SyncOverTaskManager;
import okhttp3.Call;
import okhttp3.Dispatcher;
import okhttp3.Dns;
import okhttp3.OkHttpClient;
import zpui.lib.ui.refreshlayout.ZPUIRefreshInit;

/**
 * Created by monch on 2017/2/7.
 */

public class MainProcessInitializer extends PublicProcessInitializer {
    private static final String TAG = "MainProcessInitializer";
    private final InitializerTimer timer = new InitializerTimer();

    @Override
    public void initializer(Application application) {
        timer.start();
        
        timer.beginItem("super.initializer");
        super.initializer(application);
        
        timer.beginItem("TwlUtils");
        TwlUtilsInit.init(application);
        
        timer.beginItem("TLog");
        initTLog(application);
        
        timer.beginItem("DataKernel");
        initDataKernel(application);
        
        timer.beginItem("PushChannel");
        initPushChannel();
        
        timer.beginItem("AppStoreSubSource");
        initAppStoreSubSource();
        
        timer.beginItem("MobileUtil");
        MobileUtil.initFullUserAgent();

        timer.beginItem("Router");
        initRouter(application);
        
        timer.beginItem("Bugly");
        initBugly(application);
        
        timer.beginItem("Fresco");
        initFresco(application);
        
        timer.beginItem("Gallery");
        initGallery(application);

        timer.beginItem("MMS");
        initMMS(application);
        
        timer.beginItem("MSGManager");
        initMSGManager(application);

        timer.beginItem("queryMsgCount");
        App.get().db().queryCount(MessageBean.class);

        timer.beginItem("DeviceInfo");
        MobileUtil.printDeviceInfo(application);
        
        timer.beginItem("ForegroundUtils");
        ForegroundUtils.init(App.get());
        ForegroundUtils.get().addListener(ListAnalyticsFactory.getInstance());
        ForegroundUtils.get().addListener(new AppStatusUtil());
        
        timer.beginItem("PushSdk");
        initPushSdk(application);

        timer.beginItem("ZPUIRefresh");
        ZPUIRefreshInit.init();

        timer.beginItem("Apm");
        initApm(application);
        
        timer.beginItem("SpaceChecker");
        initSpaceChecker();
        
        timer.beginItem("ShuMeng");
        initShuMeng(application);
        
        timer.beginItem("CommonData");
        initCommonData(application);
        
        timer.beginItem("VoiceRecognition");
        initVoiceRecognitionSdk();

        timer.beginItem("checkInitFinish");
        DataKernel.getInstance().checkInitFinish();
        
        timer.end();
    }

    private void initSpaceChecker() {
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                FileSystemSpaceChecker.initCheckInternalSpace();
            }
        });
    }

    private void initDataKernel(Application application) {
        DataKernel.init(application);
    }

    private void initFresco(@NonNull Application application) {
        final Set<RequestListener> listeners = new HashSet<>();
        listeners.add(new BzlFrescoRequestListener());

        ImagePipelineConfig imagePipelineConfig =
                ImagePipelineConfig
                        .newBuilder(application)
                        .setRequestListeners(listeners)
                        .setDownsampleEnabled(true)
//                        .setNetworkFetcher(new BzlCustomNetworkFetcher(requestImageUrlCallback))
                        .setNetworkFetcher(new BzlOkHttpNetworkFetcher(createOkHttpClient(isInitHttpDnsGray()), requestImageUrlCallback)
                                .setSupportWebp(AndroidDataStarGray.getInstance().isFrescoSupportWebp()))
                        .setMemoryTrimmableRegistry(AppTrimMemoryCommon.getFrescoTrimmableRegister())
                        .build();

        Fresco.initialize(application, imagePipelineConfig);
    }

    private static OkHttpClient createOkHttpClient(boolean isUseHttpDns) {
        return createOkHttpClient(isUseHttpDns, true);
    }

    private static OkHttpClient createOkHttpClient(boolean isUseHttpDns,boolean fresco) {
        TLog.info("fresco", "createOkHttpClient = isUseHttpDns = %s   fresco= %s", isUseHttpDns, fresco);
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        if (fresco) {
            builder.dispatcher(new Dispatcher(Executors.newFixedThreadPool(5)))
                    .connectTimeout(30, TimeUnit.SECONDS);
        }

        if (isUseHttpDns) {
            builder.dns(new Dns() {
                public static final String TAG = "fresco_dns_parse";
                @NonNull
                @Override
                public List<InetAddress> lookup(@NonNull String hostname) throws UnknownHostException {
                    TLog.debug(TAG, "dns parse:%s  useHttpDns:%s", hostname,useHttpDns.get());
                    if (useHttpDns.get()) {
                        String errorInfo = "";
                        List<String> ips = Collections.emptyList();
                        try {
                            List<InetAddress> list = new ArrayList<>();
                            ips = DNSCommon.getIpListFromDnspod(hostname);
                            if (ips != null && !ips.isEmpty()) {
                                for (String ip : ips) {
                                    if (!MqttUtil.isRemoteIP(ip)) {//如果有一个ip是本地ip，则忽略httpdns的结果，使用本地host解析
                                        TLog.info(TAG, "initFresco has local ip ,use system dns parse:%s", ip);
                                        list.clear();
                                        break;
                                    }
                                    list.addAll(Arrays.asList(InetAddress.getAllByName(ip)));
                                }
                                if (!list.isEmpty()) {
                                    TLog.info(TAG, "initFresco use [%s]=[%s]", hostname, CollectionUtil.listToString(ips));
                                    return list;
                                }
                            }
                        } catch (UnknownHostException ex) {
                            TLog.info(TAG, "error is %s,and parse reuslt is :[%s]", ex.toString(), GsonUtils.toJson(ips));
                            errorInfo = "Fresco HttpDns UnknownHostException:" + ex + ":" + GsonUtils.toJson(ips);
                        } catch (Exception e) {
                            TLog.info(TAG, "error is %s,and parse reuslt is :[%s]", e.toString(), GsonUtils.toJson(ips));
                            errorInfo = "Fresco HttpDns Exception:" + ":" + GsonUtils.toJson(ips);
                        }
                        if (!TextUtils.isEmpty(errorInfo)) {
                            // 上报Http DNS解析失败
                            ApmAnalyzer.create()
                                    .action(AnalyzerConst.ACTION_NET_ERROR, Constant.Reporter.TYPE_HTTP_DNS_FAILED)
                                    .param("p9", errorInfo)
                                    .report();
                        }
                    }
                    return Dns.SYSTEM.lookup(hostname);
                }
            });
        }
        return builder.build();
    }

    private void initGallery(@NonNull Application application) {
        // 没有用 com.bumptech.glide.module.AppGlideModule 只能这样写
        Glide.init(application,new GlideBuilder().addGlobalRequestListener(new com.bumptech.glide.request.RequestListener<Object>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Object> target, boolean isFirstResource) {
                final String stackTraceString = Util.getStackTraceString(e);
                if (!LText.empty(stackTraceString)) {
                    ApmAnalyzer.create()
                            .action("action_fresco_image_request_failure","glide")
                            .p2(String.valueOf(model))
                            .p3(stackTraceString)
                            .report();
                }
                return false;
            }

            @Override
            public boolean onResourceReady(Object resource, Object model, Target<Object> target, DataSource dataSource, boolean isFirstResource) {
                return false;
            }
        }));
        Matisse.initialize(application); //这个库中手动调用 Glide.get(application).getRegistry().replace了
        Glide.get(application).getRegistry().replace(GlideUrl.class, InputStream.class,
                new OkHttpUrlLoader.Factory(createOkHttpClient(isInitHttpDnsGray(),false)).setRequestImageUrlCallback(requestImageUrlCallback));
    }

    private void initPushChannel() {
        NotificationManager manager = (NotificationManager)
                App.get().getSystemService(Context.NOTIFICATION_SERVICE);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(NotificationConfig.getMessageChannelId(), NotificationConfig.getMessageChannelName(), NotificationManager.IMPORTANCE_HIGH);
            manager.createNotificationChannel(channel);
        }
    }


    private void initAppStoreSubSource() {
        if (!"HUAWEI".equalsIgnoreCase(Build.MANUFACTURER)) {
            return;
        }
        AppThreadFactory.createThread(new Runnable() {
            @Override
            public void run() {
                long startTimestamp = System.currentTimeMillis();
                performInitAppStoreSubSource();//获取华为市场推广位渠道号
                InitializerTimer.reportItemTimeout("initAppStoreSubSource", startTimestamp);
            }
        }).start();
    }

    private void performInitAppStoreSubSource() {
        try {
            int COLUMN = 0;
            String PROVIDER_URI = "content://com.huawei.appmarket.commondata/item/3";
            String trackId = null;
            Uri uri = Uri.parse(PROVIDER_URI);
            ContentResolver contentResolver = App.get().getContentResolver();
            try (Cursor cursor = contentResolver.query(uri, null, "com.hpbr.bosszhipin", null, null)) {
                if (cursor != null) {
                    cursor.moveToFirst();
                    trackId = cursor.getString(COLUMN);
                } else {
                    TLog.print("trackId", "cursor is null");
                }
            } catch (Exception e) {
                TLog.error(TAG, e, "cursor query error");
            }
            if (!TextUtils.isEmpty(trackId)) {
                MobileUtil.SUB_SOURCE = trackId;
                MobileUtil.onSubSourceInfoInit();
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "performInitAppStoreSubSource error");
        }
    }

    public static void initPushSdk(Application application) {
        if (PrivacyDelayInitializer.getInstance().isDelay()) {
            return;
        }
        PushSdkConfig pushSdkConfig = new PushSdkConfig();
        pushSdkConfig.miAppkey = PackageConfigContants.PACKAGE_PUSH_MI_APP_KEY;
        pushSdkConfig.miAppid = PackageConfigContants.PACKAGE_PUSH_MI_APP_ID;
//        pushSdkConfig.miPassThroughRegisterType = PackageConfigContants.PACKAGE_TOKEN_TYPE_MI_PASS_THOUGH;
        pushSdkConfig.miRegisterType = PackageConfigContants.PACKAGE_TOKEN_TYPE_MI;
        pushSdkConfig.hwRegisterType = PackageConfigContants.PACKAGE_TOKEN_TYPE_HW;
        pushSdkConfig.hwPassThroughRegisterType = PackageConfigContants.PACKAGE_TOKEN_TYPE_HW_PASS_THOUGH;
        pushSdkConfig.pushCallback = new PushCallback();

        if (AccountHelper.isBoss()) {
            pushSdkConfig.self_push_channel = DataStarGray.getInstance().bossPushMqttChannel();
        } else if (AccountHelper.isGeek()) {
            pushSdkConfig.self_push_channel = DataStarGray.getInstance().geekPushMqttChannel();
        }

        PushSdkManager.getInstance().init(application, pushSdkConfig);

        ForegroundUtils.get().addListener(new ForegroundUtils.ForegroundListener() {
            @Override
            public void onForeground() {
                PushSdkManager.getInstance().setForeground(true);
            }

            @Override
            public void onBackground() {
                PushSdkManager.getInstance().setForeground(false);
            }
        });
    }

    static void initMSGManager(Context context) {
        if (PrivacyDelayInitializer.getInstance().isDelay()) {
            return;
        }
        MSGManager.initializer(new ConnectHandler(),
                new ReceiverHandler(MessageDaoFactory.getMessageDao()),
                new SenderHandler(MessageDaoFactory.getMessageDao()),
                !DBUpgradeProcessor.mustUpdateMessageDB());
        if (UserManager.isCurrentLoginStatus()) {
            ChatMessageFactory.getInstance().createChatTransfer().register(ChatReceiver.getInstance());
        }
        SyncOverTaskManager.getInstance().initializer();
    }

    @SuppressLint("twl_postdelay")
    public static void initShuMeng(Context context) {
        if (PrivacyDelayInitializer.getInstance().isDelay()) {
            return;
        }
        if (ShuMengHelper.isDisableShuMeng()) {
            TLog.info("appinfo", "======initShuMeng====: disable");
            return;
        }
        Context ctx = context;
        try {
            Main.init(ctx, "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfN84K/ZL2Y/Evzhlmxm4IRWkawCTdB6ENm3cTDCnDaqjI8m9Z/tQnFTZi7a/Rz2JbmcmFJBfQXzN9YhC/4Ky8CAwEAAQ==");
//            Main.setConfig("url", "d2api-shumeng.zhipin.com");
//            Main.setConfig("url2", "dai-shumeng.zhipin.com");
//            Main.setConfig("url3", "daa-shumeng.zhipin.com");
//            Main.setConfig("url4", "dcc2-shumeng.zhipin.com");

            Main.setConfig("url", "uni-shumeng.zhipin.com");
            //该方法支持自定义配置信息采集和服务地址等
            Main.setConfig("pkglist", "1");
            Main.setConfig("cdlmt", "1");

            if (!UserManager.isCurrentLoginStatus() || !AndroidDataStarGray.getInstance().isUploadWifiInfo()) {
                Main.setConfig("wifi", "1");
                Main.setConfig("bluetooth", "1");
                Main.setConfig("location", "1");
            }
            Main.getQueryID(ctx, MobileUtil.getChannel(), "message", 1, new Listener() {
                long startTime = System.currentTimeMillis();
                @Override
                public void handler(String s) {
                    LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.SHU_ZI_LM).put("queryId", s).put("processId",Processer.getProcessId()).put("time",System.currentTimeMillis() - startTime).info();
                    if (!TextUtils.isEmpty(s)) {
                        String preDid = MobileUtil.getDid();
                        MobileUtil.putDid(s);
                        MobileUtil.onSubSourceInfoInit();
                        if (!TextUtils.isEmpty(preDid)
                                && !s.equals(preDid)) {
                            BlackAppManager.reportDidChanage(preDid, s);
                        }
                    } else {
                        ApmAnalyzer.create().action("ShuMeng", "queryId").p2("empty").debug().report();
                    }
                }
            });

            // 调用获取OAID接口，需要在调用初始化方法5s后再调用，因为有的设备运行效率比较差，得先确保完成初始化，防止代码冲突。
            AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Main.getOpenAnmsID(context, oaid -> {
                        L.d("appinfo", "OAID : " + oaid);
                        MobileUtil.putOAID(oaid);
                    });
                    Main.getHMSOpenAnmsID(context, oaid -> {
                        L.d("appinfo", "HMSOpenAnmsID : " + oaid);
                        MobileUtil.putHMSOID(oaid);
                    });
                }
            }, 5000);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    static void initUmeng(Context context) {
//        UMConfigure.preInit(context, PackageConfigContants.PACKAGE_UMENG_KEY, MobileUtil.getChannel());
//        if (PrivacyDelayInitializer.getInstance().isDelaySdkOnPhoneAuthority()) {
//            return;
//        }
//        UMConfigure.init(context, PackageConfigContants.PACKAGE_UMENG_KEY, MobileUtil.getChannel(), UMConfigure.DEVICE_TYPE_PHONE, null);
//        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.LEGACY_AUTO);
//
////        //umeng apm需要的配置
////        final Bundle customInfo = new Bundle();
////        customInfo.putBoolean("mCallNativeDefaultHandler",true);
////        CrashApi.getInstance().updateCustomInfo(customInfo);
////        UMCrash.setAppVersion(MobileUtil.getShowVersionName(), "release", ""+MobileUtil.getVersionCode(App.getAppContext()));
//    }

    static void initCommonData(Context context) {
        if (PrivacyDelayInitializer.getInstance().isDelay()) {
            return;
        }
        CommonConfigManager.getInstance().refresh();
        // MainTabIconConfigManager.getInstance().getTabIconsConfig();
        ThemeConfigManager.getInstance().requestConfig();
    }

    /**
     * 初始化语音识别SDK
     */
    private void initVoiceRecognitionSdk() {
        VoiceConfig config = new VoiceConfig.Builder().enableLog(true)
                .ticketNameSupplier(() -> TicketHeaderConstant.HEADER_TICKET_NAME)
                .isFixGray(AndroidDataStarGray.getInstance().isAudioFixGray())
                .setLogger(new IVoiceLog() {
                    @Override
                    public void e(String s, String s1) {
                        TLog.error(s, s1);
                    }

                    @Override
                    public void d(String s, String s1) {
                        TLog.debug(s, s1);
                    }

                    @Override
                    public void w(String s, String s1) {
                        TLog.info(s, s1);
                    }

                    @Override
                    public void i(String s, String s1) {
                        TLog.info(s, s1);
                    }
                })
                .ticketValueSupplier(() -> {
                    String token = UserManager.getToken();
                    return TextUtils.isEmpty(token) ? "" : token;
                }).build();
        VoiceManager.init(config);
    }


    //十位数 = 1 开启初始化使用HTTPDNS
    private static boolean isInitHttpDnsGray(){
        return AndroidDataStarGray.getInstance().isFrescoOkhttpDns() / 10 == 1;
    }

    //个位数 = 2 默认使用HTTPDNS
    private static boolean isDefaultHttpDnsGray(){
        return AndroidDataStarGray.getInstance().isFrescoOkhttpDns() % 10 == 2;
    }

    //个位数 = 1 与SSL切换 httpDns
    private static boolean isChangeHttpDnsGray(){
        return AndroidDataStarGray.getInstance().isFrescoOkhttpDns() % 10 == 1;
    }

    private static final AtomicBoolean useHttpDns = new AtomicBoolean(isDefaultHttpDnsGray());

    final static RequestImageUrlCallback requestImageUrlCallback = new RequestImageUrlCallback() {
        @Override
        public String requestNewUrl(String url) {
            //异步接口请求，需要等待请求结果
            final String[] newUrl = {url};
            CountDownLatch countDownLatch = new CountDownLatch(1);
            try {
                SimpleApiRequest.GET(URLConfig.URL_SIGN_REFRESH_UPLOAD)
                        .addParam("url", URLEncoder.encode(url))
                        .setRequestCallback(new SimpleApiRequestCallback<SignRefreshHttpResponse>() {
                            @Override
                            public void handleErrorInChildThread(ErrorReason reason) {
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleInChildThread(ApiData<SignRefreshHttpResponse> data) {
                                newUrl[0] = data.resp.url;
                                countDownLatch.countDown();
                            }
                        })
                        .execute();
                countDownLatch.await();
            } catch (Exception e) {
                countDownLatch.countDown();
            }
            return newUrl[0];
        }



        /**
         * 网络异常时 SSLHandshakeException  切换HTTPDNS访问
         * @param call
         * @param e
         */
        @Override
        public void requestFailure(Call call, Exception e) {
//            TLog.debug("RequestImageUrlCallback","requestImageUrlCallback = call = %s  e = %s",call,e);
            if (!useHttpDns.get() && e instanceof SSLHandshakeException && isChangeHttpDnsGray()) {
                useHttpDns.set(true);
                if (!isInitHttpDnsGray()) { // 没有初始化时 重新执行初始化
                    TLog.error("RequestImageUrlCallback","requestImageUrlCallback = call = %s  e = %s",call,e);
                    //重新初始化Fresco
                    Application application = Utils.getApp();
                    final Set<RequestListener> listeners = new HashSet<>();
                    listeners.add(new BzlFrescoRequestListener());
                    ImagePipelineConfig imagePipelineConfig =
                            ImagePipelineConfig
                                    .newBuilder(application)
                                    .setRequestListeners(listeners)
                                    .setDownsampleEnabled(true)
//                        .setNetworkFetcher(new BzlCustomNetworkFetcher(requestImageUrlCallback))
                                    .setNetworkFetcher(new BzlOkHttpNetworkFetcher(createOkHttpClient(useHttpDns.get()), requestImageUrlCallback)
                                            .setSupportWebp(AndroidDataStarGray.getInstance().isFrescoSupportWebp()))
                                    .setMemoryTrimmableRegistry(AppTrimMemoryCommon.getFrescoTrimmableRegister())
                                    .build();

                    Fresco.initialize(application, imagePipelineConfig);

                    //重新初始化 Glide
                    Glide.get(application).getRegistry().replace(GlideUrl.class, InputStream.class,
                            new OkHttpUrlLoader.Factory(createOkHttpClient(useHttpDns.get(),false)).setRequestImageUrlCallback(requestImageUrlCallback));
                }
            }
        }
    };
}
