package com.hpbr.bosszhipin.startup.pipeline;

import android.os.Handler;

import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;

/**
 * Created by monch on 2017/3/11.
 */

public class PublicInitWork extends AbsWork {

    PublicInitWork(Handler operator) {
        super(operator);
    }

    @Override
    boolean execute(Object[] params) {
        if (CommonConfigManager.getInstance().isTourist()) {
            return true;
        }
        PrivacyDelayInitializer.getInstance().ensureInit(-1);

        if (AndroidDataStarGray.getInstance().isNeedBaiduMap()) { //开启百度
            MapViewCompat.preInitializer(AndroidDataStarGray.getInstance().isUserBMapStyle());
        }
        return true;
    }

}
