package net.bosszhipin.api;

import net.bosszhipin.base.HttpResponse;

/**
 * Author: zhouyou
 * Date: 2020/6/16
 */
public class GetUserProxyInfoResponse extends HttpResponse {

    private static final long serialVersionUID = 3890851331984698595L;

    public int recruit; //0:不是代招 1:高端猎头 2:白领猎头
    public int onlineRemind;//0:不展示上线提醒 1:展示上线提醒 2优先提醒
    public int subscribeTip; // 8.15订阅红点

    public int refinedSelected; // 精选是否默认选中  1-默认选中

    public String itemMallNewStylePageUrl; //1112.256【商业】B端道具商城改版  1-新样式(H5)



    public String filterRiskMessage; // 1205.234【商业】风险提示


    /**
     * 908.41【商业】搜畅职位版尝试 0默认值, 1命中灰度
     */
    public int searchCard4JobGray;

    /**
     * 1207.233【商业】随心聊灰度，1-实验组
     *
     * 【后续操作】
     * @心宇，今晚操作观星台关闭实验，先关闭一期实验，再关闭二期实验；
     * @伟伟@乐元，在实验关闭后，辛苦进行线上回测，实验关闭后，已经购买随心聊的用户需要能够继续用（包含激活、使用）；
     * @河川@南萌@应雄，因随心聊激活有效期为一年，故相关代码需要一年后才能清理；
     */
    public int searchEasyChatGray; // 1207.233【商业】随心聊灰度，1-实验组

    /**
     * 职位关键词选择灰度，1-命中灰度
     */
    public int positionKeyWordsSelectGray;

    /**
     * 1219.251【商业】F1高搜短列表优化
     *【已全量 | value = 0】
     */
    @Deprecated
    public int f1RecommendSearchGray;

    /**
     * 1121.255【商业】可拨打标签体验优化
     * 【已全量 | value = 1】
     */
    @Deprecated
    public int availableCallOptimizeGray;//1121.255【商业】可拨打标签体验优化 - 1-实验组

    /**
     * 1111.256
     * 【已全量 | value = 2】
     */
    @Deprecated
    public int searchFrontPageKeywordGray; // 1111.256 0:对照组 | 1：实验组A | 2：实验组B

    /**
     * 1002.236 默认选中高搜
     * 【已全量 | value = 0】
     */
    @Deprecated
    public int defaultSelectAdvanceSearch; // // 1002.236 默认选中高搜 1-实验组 0-对照组
    /**
     * 1112.256【商业】B端道具商城改版
     * 【已全量 | value = 1】
     */
    @Deprecated
    public int itemMallNewStyle; // 1-新样式(H5)
    /**
     * 1114.257 【商业】优先提醒新增送达入口
     * 【已全量 | value = 0】
     */
    @Deprecated
    public int quickRemindChatMessageDeliveryLabelGray; //1114.257 【商业】优先提醒新增送达入口
    /**
     * 1115.256
     * 【已全量 | value = 0】
     */
    @Deprecated
    public int searchMiddlePageTabSwitchAutoRefresh;

    /**
     * 1218.252【商业】搜索中间页屏效提升
     * 【已全量 | value = 1】
     */
    @Deprecated
    public int searchKeyWordsGray;

    /**
     * 1312.243【商业】短信通知升级服务：聊天短信通知入口角标提示文案
     */
    public String smsLabelText;
    /**
     * 1312.243【商业】短信通知升级服务：是否展示聊天短信通知入口角标提示文案的灰度字段
     */
    public int intentionOrderGray;
}
