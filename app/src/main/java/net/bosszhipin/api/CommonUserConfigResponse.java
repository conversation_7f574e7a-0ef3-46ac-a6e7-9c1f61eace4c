package net.bosszhipin.api;

import com.hpbr.bosszhipin.utils.permission.DialogConfig;

import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.base.HttpResponse;

import java.util.List;

/**
 * Created by zhangxiangdong on 2017/11/28.
 */
public class CommonUserConfigResponse extends HttpResponse {

    private static final long serialVersionUID = 1L;

    /*@since 912.29 未完善牛人查看职位个数上限*/
    public int zpgeek_incomplete_view_job_limit;

    public int liveRecruitRedPoint; // 719 校招直播小红点 1 - 展示

    public int discoverGray; // 921 每日新发现灰度

    /**
     * 【B端全量 - false】
     * 【C端暂未全量】
     */
    public boolean weiXinRemind;//8.05微信开关提醒

    public boolean exchangeWxExtendPhone;//1106.5【C】支持用户一次申请多种交换

    public MomentGrayConfig momentGrayConfig;

    public LiveRecruitF1Tip liveRecruitF1Tip;

    public int exchangeOptimization;////0 不合并 ，1 联系方式，2 换电话/微信

    public boolean videoFastReply;//是否显示视频播放

    public boolean messageChangeSkin;//深色模式是否灰度

    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=129768042
    public int notifyGuideStyle;//0 不显示， 1 聊天详情页 ，2 聊天列表页F2

    /**
     * 1008.602 标签过滤器灰度,    1：点击标签后文本显示在输入框,   0：点击标签后文本不显示在输入框
     */
    public int zpgeek_search_label_filter;

    /**
     * 1119.112
     * 【全量 - true】
     */
    public boolean keepTalkingClose;

    /**
     * 1013.62 求职记录 重新统计按钮 灰度
     * 【全量 - true】
     */
    public boolean geekApplyHistorySetButton;

    /**
     * 1019.41 面试进程标签和标记整合灰度
     */
    public boolean progressStyle;

    public String wxNotifyBindJumpPath;

    public boolean geekInterestJobSpread;

    public boolean axbInterviewForceNotice; // 1109.42【面试非版本】安心保面试打通

    public  int geekScoreConfig;//0表示不展示 1表示展示
    public  int geekExpose2OtdGray;//0表示不展示 1表示展示  1212.601【C】【APP】陆港融合潜在牛人授权曝光CV到otd引导@婧含@UI https://zhishu.zhipin.com/wiki/tYexnEw6EXt

    public boolean bossOpenChatQuestionOptimize; // 1117.161【招聘者】开场问题-B端流程优化

    /**
     * AI聊天助手配置信息
     */
    public AIAssistInfo aiAssistInfo;

    public WidgetInfo widgetInfo;

    /**
     * "msgStatusConfigs": {
     *     "received": { // 送达
     *       "text": "送达", // 可能没值，没值使用默认值
     *       "color": "#1415B3B3_#1415B3B3", // 颜色 可能没值 没值用默认
     *       "chatListDisplay": 1, // 聊天列表是否展示0显示 1隐藏
     *       "chatDetailDisplay": 1,// 聊天详情是否展示0显示 1隐藏
     *     },
     *     "read": {// 已读状态
     *       "text": "已读", // 可能没值，没值使用默认值
     *       "color": "#1415B3B3_#1415B3B3", // 颜色 可能没值 没值用默认
     *       "chatListDisplay": 1, // 聊天列表是否展示0显示 1隐藏
     *       "chatDetailDisplay": 1,// 聊天详情是否展示0显示 1隐藏
     *     }
     *   }
     */
    
    public MsgStatusConfigs msgStatusConfigs;
    
    public static class MsgStatusConfigItem extends BaseServerBean {
        private static final long serialVersionUID = 8226028843764840864L;
        public String text;
        public String color;
        public int chatListDisplay;
        public int chatDetailDisplay;
    }
    
    
    public static class MsgStatusConfigs extends BaseServerBean {
        private static final long serialVersionUID = 6477846569059967359L;
        public MsgStatusConfigItem received;
        public MsgStatusConfigItem read;
    }

    public static class MomentGrayConfig extends BaseServerBean {

        private static final long serialVersionUID = 1L;

        public boolean isDefaultContentTabUser;
        public int isMomentTab1117TestUser;//1117.913【内容】主框架结构调整 是否是实验用户 1是 0否
        public int isMomentDefaultVideoTabUser;//1117.913【内容】主框架结构调整 是否默认进视频tab  1是 0否
        public int recCover1126;// 1126推荐封面 0 对照组（A)， 1 实验组（B)
        public MomentActivityTabConfig activityTabConfig;//活动tab配置
    }

    public static class MomentActivityTabConfig extends BaseServerBean {
        private static final long serialVersionUID = -8242202038397675637L;
        /**
         * TAB图标地址
         */
        public String icon;
        /**
         * TAB图标宽度
         */
        public int width;
        /**
         * TAB图标高度
         */
        public int height;
        /**
         * TAB标题
         */
        public String title;
        /**
         * TAB链接
         */
        public String url;
        /**
         * TAB是否默认进入
         */
        public boolean isDefaultTab;
    }

    public static class LiveRecruitF1Tip extends BaseServerBean {

        private static final long serialVersionUID = 1L;

        public boolean showTip;// 8.02 校招直播提醒

        public String tipTitle;

        public String tipDesc;

    }

    /**
     * 1104.89
     * true-表示使用v组音频采集； false-表示不使用；
     */
    public boolean audioCaptureByVGroup;

    /**
     * 1121.91 0未命中, 1 默认推荐排  2 默认时间排
     */
    public int greetingRecSort;
    // mqtt tls
    public boolean mqttTlsOn;

    public int f2ContactUnreadLimit;

    public int bossChatBlockSwitch; //是否开启boss聊天阻断判断功能1-开启(默认) 0-关闭
    public int bossChatBlockThreshold;//牛人未回复boss聊天阻断触发消息数量阈值"
    /**
     * 1214.612【C】针对开聊后无达成的情况，在消息对话时给牛人推荐更活跃的职位
     * 未读未回时间间隔
     */
    public int unReplyCheckInterval;

    /**
     * 1215.613【C端】达成—引导C 接受交换
     * https://zhishu.zhipin.com/wiki/b0DRBXJ8hRg
     */
    public boolean F2AcceptGuide;

    /**
     * 1215.618【C】【搜索】搜索sug优化迭代
     * 未点击搜索sug列表的事件
     */
    public int zpgeek_suggest_input_status_period;

    /**
     * 1222.611 【App&小程序】简历隐私号牛人端体验优化
     */
    public int encryptResumeOpt;

    /**
     * 小组件 前台切后台刷新间隔 单位s <=0 代表每次都刷
     */
    public long widgetRefreshPeriod;

    /**
     * 语音红点展示规则
     * 控制客户端红点展示规则 > 0 即代表只展示最近多少天的消息红点 < 0 即代表展示所有消息红点 =0 代表不展示
     */
    public int soundRedPointRule;

    /**
     * 1303.921【C】聊天交换限制逻辑优化
     * 优先使用该字段，如果为false，在使用geekenter的同名字段
     */
    public boolean exchangeOptGray;

    //1303.181 小组件联系人列表引导横幅文案
    public String componentText;

    /**
     * 1303.1.4【智能沟通助手10期】交互优化：F3 聊天列表页，聊天设置中是否展示 AI 沟通助手设置项
     */
    public int aiChatHelperEnable;


    /**
     * 1311 位置权限弹窗改为服务端控制
     */
    public List<DialogConfig> dialogConfig;

    public static class WidgetInfo extends BaseServerBean {
        private static final long serialVersionUID = 1L;
        public FreqConfig chatFreq;
        public FreqConfig greetFreq;
    }

    public static class FreqConfig extends BaseServerBean {
        private static final long serialVersionUID = 1L;
        public int times;
        public int interval;
    }
}
