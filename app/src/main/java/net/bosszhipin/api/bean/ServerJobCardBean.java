package net.bosszhipin.api.bean;

import com.amap.api.maps.model.Marker;
import com.hpbr.bosszhipin.base.GeekBaseCardBean;
import com.hpbr.bosszhipin.event.ListAnalyticsSwitch;

import net.bosszhipin.api.GetF1ChatedRcmdResponse;
import net.bosszhipin.api.ServerWordHighlightBean;
import net.bosszhipin.api.bean.geek.RecommendListAdBean;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/1/19.
 */
@ListAnalyticsSwitch(key = {"lid", "jobId"}, params = {"lid:lid", "jobId:jobId", "expectId:expectId", "bossId:bossId"})
public class ServerJobCardBean extends GeekBaseCardBean {

    public static final  int COMMUNICATE_TYPE_CHAT=1;
    public static final  int COMMUNICATE_TYPE_ONE_BTN_APPLY=2;

    public ServerJobCardBean() {
        super(TYPE_JOB_CARD_BASE);
    }


    private static final long serialVersionUID = -5220630190574619120L;
    /**
     * jobId : 1234
     * jobName : Java
     * jobValidStatus : 1
     * jobExperience : 3-5年
     * jobDegree : 本科
     * jobSalary : 10K-20K
     * bossId : 222872
     * bossName : 姓名
     * bossAvatar : 头像
     * bossTitle : 职称
     * bossCert : 1
     * cityName : 北京
     * areaDistrict : 朝阳区
     * businessDistrict : 太阳宫
     * brandName : Boss直聘
     * brandStageName : D轮及以上
     * lid : dddxxx.SSS.1
     * expectId : 123456
     * actionDateDesc : 今天 8:30
     */


    // 基础字段（非基础字段不允许在这里添加）
    public String securityId;
    public int friendSource;

    public long jobId;
    public String jobName;
    public int jobValidStatus;
    public String jobExperience;
    public String jobDegree;
    @Deprecated
    public String jobSalary;
    public long bossId;
    public String bossName;
    public String bossAvatar;
    public String bossTitle;
    public String bossTag;
    public int bossCert;
    public int bossSource; // 职位来源，0:boss直聘，1:店长直聘
    public String cityName;
    public String areaDistrict;
    // 1217.616【C】【小程序&APP】地图找工作，新增商圈Code
    public long businessCode;
    public String businessDistrict;
    public String brandName;
    public String bossBrandName;
    public String brandStageName;
    public String industryName;
    public String postDescription;
    public String lid;
    public long expectId;

    public String bottomRightURL;
    public String bottomRightText;

    public String itemSource;//"来自"简历刷新""

    public List<ServerAfterNameIconBean> afterNameIcons;
    public List<ServerAfterNameIconBean> beforeNameIcons;


    public String actionDateDesc;// 614新增字段，最后操作的时间

    public String distance; // 701新增字段，蓝领职位附近距离

    public String brandScaleName; // 710 职位卡片新增公司规模
    public String scaleName; //1107 609  F2 添加人数

    public String subwayDistance; //821.5【C】职位卡片增加地铁线路站点标签、距地铁站距离描述

    /*@since 9.11 职位卡片高亮字段*/
    public JobDescBean jobDesc;
    /*@since 915 是否屏蔽公司职位 0为未屏蔽，1为屏蔽*/
    public int isShield;

    /*是否已经查看过该职位的详情（非server端下发）*/
    public boolean isViewedJobDetail;
    public  boolean detailed; // 服务端下发是否查看过
    public boolean online; //boss 是否在线  1012.2 添加
    public String encryptJobId;

    /**
     * 详情页面是否查看过，改变也是基于服务数据返回情况下 修改
     * @return
     */
    public  boolean isDetailed(){
        return detailed;
    }

    public boolean isJobInvalid() {
        return jobValidStatus == 2;
    }

    public boolean isJobValid() {
        return jobValidStatus == 1;
    }

    public boolean isCertificated() {
        return bossCert == 3;
    }


    // 其他功能字段定义
    public ServerHighlightDescBean interactDesc;
    @Deprecated
    public boolean clicked;


    // 自己定义的字段，用于高搜的卡片中显示是否已经开聊
    public boolean hasChat;

    // 快速处理
    public String activeTimeDesc;


    // 7.04 职位标志
    public List<String> jobLabels;
    // 7.04 包含日薪、月薪及薪资月数的完整薪资描述
    public String salaryDesc;

    public List<ServerWordHighlightBean> hlmatches;

    public double longitude;
    public double latitude;

    // "rcdReason":"根据过往经验推荐", // 混合推荐理由
    public String rcdReason;

    public String liveRecordId; //808 直播投递增加

    public String deliverDateTime;

    public String jumpUrl;

    //809 学生负反馈 推荐 "X" 按钮
    public int canFeedback; //是否展示反馈入口， 0:不展示反馈入口，1:可反馈，展示反馈入口

    public ServerJobDividerInfo dividerInfo;// 817.1 更多相似职位

    /**
     * 8.19
     * 新增返回参数：zpData->jobList->positionCategory, 职类名称
     */
    public String positionCategory;
    //@since 907 三级职类code
    public String position;
    //@since 907  相似来源
    public String dataSource;

    //902
    public int cardType; //"cardType": 2, // 0:职位卡片，1:直播职位卡片， 2:列表广告卡片, 3:安心保广告卡片  4:综合列表卡片 7：特征标签卡片类型 （1107.6） 11 推荐列表置顶职位卡片12.09
    public int outland;

    public ServerJobLiveRoomInfo liveRoomInfo;

    //903.203【大蓝用户产品】蓝领招聘职类醒目化
    public String bluePositionName;

    //908 【TitanStar】广告位

    public ServerAdCardDetailInfo adCard;

    public ServerAnXinBaoAdCardInfo factoryAd; // 安心保卡片信息

    public ServerMixAdCardInfo mixAd;

    public List<String> factoryJobLabels; // 安心保tag标签

    /**
     * 1105 601 添加
     */
    public F1FeedBackCardBean cusAddrSugAd;// cartType=5时返回

    /**
     * 1106.055【学生】为应届生提供校招公司列表
     */
    public F1StuZoneBean schoolJobBrandAd;// cartType=6时返回

    @Deprecated
    public String interviewLabel;//1108 41  和服务确认F1场景删除了使用

    //911 NEW 图片 1017.032复用
    public String iconUrl;
    public String bottomRcdReason; //卡片底部推荐的理由 917 新增
    public ServerRcmdResaonBean axbRcdReason;
    /*卡片样式灰度，1:实验组B优化样式（非server下发，917.037 实验组B：展示一行职位描述，优化卡片样式,目前只有搜索列表的职位卡片有此字段)*/
    public int grayJobCardStyle;
    //917 互动特有 时间戳
    public long happenTime;
    //919.035 职位描述预览
    public String jobPreviewDesc;
    /*@since 1003 企业静态标签 (如：世界500强、中国500强、独角兽、瞪羚企业)*/
    public List<String> companyStaticLabels;
    /*@since 1003 企业动态标签(如: 收藏TOP)*/
    public List<String> companyDynamicLabels;
    /*1104.603 实验一 icon*/
    public List<ServerAfterNameIconBean> beforeJobDescIcons;

    public List<ServerJobPictrueBean> picVideoList;

    public String templateHighlight;

    public ServerGreeBean greeting;

    public String similarUrl;

    public String chatButtonFlag;

    public String experimentConfigs;
    public String activeInfo;

    public int beforeNameIconFlag;// icon类型, 10:安心保，11:优选人资，12:企业直招

    /*@since 1107.6 特征标签卡片内容*/
    public ServerCharacterLabelEntryBean traitTip;

    public ServerJobCardGpsBean gps = new ServerJobCardGpsBean();// 1109.63
    public String brandLogo;

    //1111.3【C】收藏列表开聊前置-促开聊
    public int contactStatus;// 0 不展示沟通按钮 1 未沟通 2 沟通中

    public boolean contact;//是否有好友关系;
    public int interestingStyle;// 感兴趣新样式 1 新 2旧   本地字段 取自收藏接口

    public Marker marker;
    public com.hpbr.bosszhipin.map.Marker marker1;
    public boolean isSelect;
    public String closeAction; //卡片关闭 打点。根据需求 可能不返回


    public String shareId; // 1109.51 本地使用 群聊 全部职位分享 shareId
    public ServerButtonBean chatButton; // ats可网申职位按钮文案和跳转地址
    public int jobNameLineNumber;   //1118.157 职位名称展示行数


    public ServerCommonIconBean bossIcon; //1119.155【C】&【优选】优选BOSS增加蓝V标识

    /*1201.711 职位相关直播*/
    public SearchPositionLiveInfoBean liveInfo;

    public String brandUrl;//跳转 店铺协议
    public String  brandIndustry; // 行业类型

    public transient int preChat;  // 0 1 2 是否需要前置开聊 关联两个实验
    public int hasContact;

    public boolean isRecommendListAdBean;
    public RecommendListAdBean listAd;//广告卡片

    public  String communicateText;
    public  int  communicateType; // 1219.801 按钮类型1:立即沟通，2:一键报名

    public  ServerExperimentInfoBean experimentInfo;

    public List<String>  tinyActiveInfos; // 小屏boss活跃信息，字符串数组，（仅在实验组二返回）
    public  List<String> largeActiveInfos; // 大屏boss活跃信息，字符串数组，（仅在实验组二返回）

    public ServerJobF1LabelUnderSalaryBean  labelUnderSalary;

    public String highlightDesc;

    public String url;

    public GetF1ChatedRcmdResponse rcmdResponse;

    public   GeekF2JobCardFeedbackBean jobCardFeedback;

}
