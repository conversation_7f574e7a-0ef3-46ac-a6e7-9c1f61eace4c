package net.bosszhipin.api.bean;

import androidx.annotation.Nullable;

import com.monch.lbase.util.LText;

import net.bosszhipin.boss.bean.ServerRecommendReasonBean;

import org.json.JSONObject;

import java.util.List;

/**
 * Author: zhouyou
 * Date: 2019/2/18
 */
public class ServerItemContentBean extends BaseServerBean {
    private static final long serialVersionUID = -7961152208573020597L;
    /**
     * amountDesc : (1直豆/次)
     * decreaseAmount : 0
     * encryptItemId : 5326cfea4e342b231nR5
     * itemId : 103
     * price : 60
     * priceType : 1
     * specAmount : 40
     * specDesc : +40次
     * vipPrice : 0
     */

    public String amountDesc;
    public int decreaseAmount;
    public String encryptItemId;
    public long itemId;

    /**/
    public int sprice;

    public int price;

    /*1125.290 如果有就展示，没有就原逻辑展示*/
    public String priceDesc;

    public int priceType;
    public int specAmount;
    public String specUnit;
    public String specDesc;
    public int vipPrice;
    public String unit;
    public String opriceDesc;
    /*1003.250【商业】蓝领电话搭售电话曝光 原价*/
    public int oprice;


    //组合类型，默认0。0:仅道具 1：道具+vipIcon ,preOrder时加入到paramJson中传递给后端
    public int combinationType;
    //组合售卖时方框顶部文案,combinationType=1时有效
    public String combinationHeadDesc;

    //组合售卖 提醒文案，hyperLink类型  （取subUrl）
    public List<ServerHlShotDescBean> combinationNoteList;

    //1008.251【商业】电话直拨搭售优化-仅APP
    public ServerDirectCallPhoneAssistBean directCallPhoneAssist;

    public boolean isItemSelect() {
        return priceType == 1;
    }

    /*是否是搭售商品*/
    public boolean isCombinationType() {
        return combinationType == 1;
    }

    /**
     * 921.257 短信通知搭售VIP4
     * combinationType: 组合类型，默认0。0:仅道具 1：道具+vipIcon ,preOrder时加入到paramJson中传递给后端
     */
    @Nullable
    public String findBindVIPParamJson() {
        try {
            JSONObject jsonObject = new JSONObject();
            if (isCombinationType()) {
                jsonObject.put("combinationType", String.valueOf(combinationType));
            }
            // 1312.243【商业】短信通知升级服务：新增入参字段
            if (intentionItem != null) {
                jsonObject.put("intentionGray", 1);
            }
            return LText.urlEncode(jsonObject.toString(), "");
        } catch (Exception e) {
            return null;
        }
    }

    /*优惠浮层入口文案*/
    public String mergeDiscountDesc;

    /*1001.241【商业】道具价格优惠明细优化  免责声明*/
    public String statementText;

    /*1015.201【商业】直豆促消耗策略二期实验 - 浮层标题*/
    public String discountPageTitle;

    /*1015.201【商业】直豆促消耗策略二期实验 - 直豆抵扣描述*/
    public ServerRecommendReasonBean beanBzbDesc;

    public ServerHlShotDescBean covertBeanBzbDesc() {
        return null != beanBzbDesc ? beanBzbDesc.covertToHlShotBean() : null;
    }

    /*1015.201【商业】直豆促消耗策略二期实验 - 埋点：1实验组 2对照组 0不埋点*/
    public int zeroBzbGray;

    public boolean isZeroBzbGrayBg() {
        return zeroBzbGray == 1 || zeroBzbGray == 2;
    }

    /*1108.234【商业-非版本】道具优惠信息整改 - 加入优惠字段*/
    public List<ServerPreferentialPrivilegeBean> discountList;

    /*1312.243【商业】短信通知升级服务：赠送道具信息*/
    public ServerSMSItemIntentionBean intentionItem;

}
