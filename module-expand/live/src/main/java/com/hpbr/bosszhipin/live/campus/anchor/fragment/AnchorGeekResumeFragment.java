package com.hpbr.bosszhipin.live.campus.anchor.fragment;

import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.interview.dialog.resume.adapter.InterResumeAdapter;
import com.hpbr.bosszhipin.live.interview.dialog.resume.data.ResumeItemParseData;
import com.hpbr.bosszhipin.live.interview.dialog.resume.data.entity.status.ResumeFailureItem;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.response.GeekOnlineResumeResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.behavior.ViewPagerBottomSheetBehavior;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;

import zpui.lib.ui.shadow.helper.IZPUILayout;
import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * 牛人简历
 */
public class AnchorGeekResumeFragment extends BaseChildLiveFragment {
    private View viewTopArea;
    private View viewLeftArea;
    private Button btnChange;
    private InterResumeAdapter mInterResumeAdapter;
    private BossDeliveryGeekBean geekBean;
    @LiveConstants.GeekDeliverType
    private int deliverType = LiveConstants.GeekDeliverType.TYPE_PHONE; // 默认为电话类型

    public static AnchorGeekResumeFragment getInstance(Bundle data) {
        AnchorGeekResumeFragment f = new AnchorGeekResumeFragment();
        f.setArguments(data);
        return f;
    }

    @Nullable
    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        if (enter) {
            return AnimationUtils.loadAnimation(activity, mViewModel.isLandscape() ? R.anim.live_recruit_right_in : R.anim.live_recruit_bottom_in);
        } else {
            return AnimationUtils.loadAnimation(activity, mViewModel.isLandscape() ? R.anim.live_recruit_right_out : R.anim.live_recruit_bottom_out);
        }
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.live_fragment_campus_anchor_geek_resume;
    }

    @Override
    protected void initViews(View view) {
        if (getArguments() != null) {
            geekBean = (BossDeliveryGeekBean) getArguments().getSerializable(Constants.DATA_ENTITY);
            deliverType = getArguments().getInt(Constants.DATA_INT, LiveConstants.GeekDeliverType.TYPE_PHONE);
            if (geekBean == null) return;
        } else {
            return;
        }
        initView(view);
        initObserver();
        mViewModel.getGeekOnlineResume(geekBean.encryptGeekId, geekBean.encryptExpectId, mViewModel.liveRecordId, 0);
    }

    private void initView(View view) {
        viewTopArea = view.findViewById(R.id.view_top_area);
        viewLeftArea = view.findViewById(R.id.view_left_area);
        btnChange = view.findViewById(R.id.btn_change);
        ZPUIRoundButton zpBtn = view.findViewById(R.id.zp_btn);
        zpBtn.setVisibility(mViewModel.isLandscape() ? View.GONE : View.VISIBLE);
        view.findViewById(R.id.cl_back).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                hideBottomSheet(true);
                App.get().getMainHandler().postDelayed(() -> {
                    if (mViewModel.detailResponse != null && mViewModel.detailResponse.isNormalLevel() && mViewModel.detailResponse.isOnlineBzpType()) {
                        mViewModel.dialogManager.showGeekPostFragment();
                    } else {
                        mViewModel.dialogManager.showIntentionFragment(1);
                    }
                }, 200);
                LiveAnalysisUtil.reportLiveBossingGeekDetailClick(mViewModel.liveRecordId, geekBean.encryptGeekId, "0");
            }
        });
        view.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                hideBottomSheet(true);
                LiveAnalysisUtil.reportLiveBossingGeekDetailClick(mViewModel.liveRecordId, geekBean.encryptGeekId, "2");
            }
        });
        setOutAreaClickListener(view);
        RecyclerView mRecyclerView = view.findViewById(R.id.rv_list);
        mInterResumeAdapter = new InterResumeAdapter();
        mInterResumeAdapter.setEmptyView(R.layout.zpui_state_empty_default_layout, mRecyclerView);
        mRecyclerView.setAdapter(mInterResumeAdapter);
    }

    private void initObserver() {
        mViewModel.resumeLoadingLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean show) {
                if (show) {
                    if (null != mInterResumeAdapter) {
                        mInterResumeAdapter.notifyLoading();
                    }
                }
            }
        });
        mViewModel.resumeFailedLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (null != mInterResumeAdapter) {
                    mInterResumeAdapter.notifyFailure(s, true, new ResumeFailureItem.OnRetryClickListener() {
                        @Override
                        public void onClick(View v) {
                            mViewModel.getGeekOnlineResume(geekBean.encryptGeekId, geekBean.encryptExpectId,mViewModel.liveRecordId, 0);
                        }
                    });
                }
            }
        });
        mViewModel.geekResumeLiveData.observe(this, new Observer<GeekOnlineResumeResponse>() {
            @Override
            public void onChanged(GeekOnlineResumeResponse resumeResponse) {
                if (null != mInterResumeAdapter) {
                    if (null != resumeResponse && !resumeResponse.isEmpty()) {
                        mInterResumeAdapter.setNewData(mInterResumeAdapter.covertToItemBeanList(new ResumeItemParseData(resumeResponse)));
                    } else {
                        mInterResumeAdapter.notifyEmpty();
                    }
                }
                if (resumeResponse != null) {
                    updateBtnChangeStatus(resumeResponse.exchangeState);
                    btnChange.setOnClickListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            if (LText.empty(resumeResponse.exchangeUrl)) {
                                return;
                            }
                            new ZPManager(activity, resumeResponse.exchangeUrl).handler();
                            LiveAnalysisUtil.reportLiveBossingGeekDetailClick(mViewModel.liveRecordId, geekBean.encryptGeekId, "1");
                        }
                    });
                }
            }
        });
        mViewModel.geekPostStatusLiveData.observe(this, statusBean -> {
            if (statusBean == null) return;
            if (LText.equal(statusBean.encryptGeekId, geekBean.encryptGeekId) && (geekBean.deliverType == statusBean.deliverType)) {
                updateBtnChangeStatus(statusBean.exchangeState);
                ToastUtils.showText("已交换，可在聊天页查看详情");
            }
        });
    }

    private void updateBtnChangeStatus(int exchangeState) {
        if (deliverType == LiveConstants.GeekDeliverType.TYPE_PHONE || deliverType == LiveConstants.GeekDeliverType.TYPE_WX) {
            btnChange.setVisibility(View.VISIBLE);
            if (exchangeState == 0) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(true);
                btnChange.setText("同意交换");
            } else if (exchangeState == 1) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(false);
                btnChange.setText("已交换");
            } else if (exchangeState == 2) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(false);
                btnChange.setText("已拒绝");
            } else {
                btnChange.setVisibility(View.GONE);
            }
        } else {
            btnChange.setVisibility(View.GONE);
        }
    }

    private void initBehavior(View view) {
        BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(view);
        behavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
        behavior.setHideable(true);
        behavior.setSkipCollapsed(false);
        behavior.setPeekHeight((int) (ZPUIDisplayHelper.getScreenHeight(activity) * 0.5));
        behavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == ViewPagerBottomSheetBehavior.STATE_HIDDEN) {
                    hideBottomSheet(true);
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
            }
        });
    }

    private void setOutAreaClickListener(View view) {
        if (!mViewModel.isLandscape()) {
            ZPUIConstraintLayout cl_container = view.findViewById(R.id.cl_container);
            cl_container.setRadius(ZPUIDisplayHelper.dp2px(activity, 12), IZPUILayout.HIDE_RADIUS_SIDE_BOTTOM);
            viewLeftArea.setVisibility(View.GONE);
            viewTopArea.setVisibility(View.VISIBLE);
            viewTopArea.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    hideBottomSheet(true);
                }
            });
            initBehavior(view);
        } else {
            viewLeftArea.setVisibility(View.VISIBLE);
            viewTopArea.setVisibility(View.GONE);
            viewLeftArea.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    hideBottomSheet(true);
                }
            });
        }
    }

}
