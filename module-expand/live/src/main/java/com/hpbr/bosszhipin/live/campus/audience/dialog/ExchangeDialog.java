package com.hpbr.bosszhipin.live.campus.audience.dialog;

import android.app.Dialog;
import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.ActivityUtils;

/**
 * 交换信息对话框
 * 用于显示电话、微信、简历等交换信息的弹窗
 * copy from com.hpbr.bosszhipin.chat.exchange.dialog.ExchangeSecondDialog
 *
 * <AUTHOR>
 * @date 2022/7/11
 */
public class ExchangeDialog {
    private static final String TAG = "ExchangeDialog";

    // 交换类型常量
    public static final int TYPE_PHONE = 1;
    public static final int TYPE_WECHAT = 2;
    public static final int TYPE_RESUME = 3;

    // 对话框相关
    private Context mContext;
    private Dialog mDialog;
    private OnExchangeCallBack mCallBack;

    private String mDefaultTitle;
    private String mDefaultText;
    private String mDefaultHeadTitle;
    private String mCancelText;
    private String mSureText;
    private String mTip;
    private String mType;
    private String encJobId;
    private String encLiveId;

    /**
     * 设置Activity
     */
    public void setContext(Context context) {
        this.mContext = context;
    }

    /**
     * 设置默认标题
     */
    public void setDefaultTitle(String defaultTitle) {
        this.mDefaultTitle = defaultTitle;
    }

    /**
     * 设置默认文本
     */
    public void setDefaultText(String defaultText) {
        this.mDefaultText = defaultText;
    }

    public void setEncJobId(String encJobId) {
        this.encJobId = encJobId;
    }

    public void setEncLiveId(String encLiveId) {
        this.encLiveId = encLiveId;
    }

    public void setDefaultHeadTitle(String defaultText) {
        this.mDefaultHeadTitle = defaultText;
    }

    /**
     * 设置取消按钮文本
     */
    public void setCancelText(String cancelText) {
        this.mCancelText = cancelText;
    }

    /**
     * 设置确认按钮文本
     */
    public void setSureText(String sureText) {
        this.mSureText = sureText;
    }

    /**
     * 设置提示文本
     */
    public void setTip(String tip) {
        this.mTip = tip;
    }

    /**
     * 设置回调
     */
    public void setCallBack(OnExchangeCallBack callBack) {
        this.mCallBack = callBack;
    }

    /**
     * 设置类型
     */
    public void setType(String type) {
        this.mType = type;
    }

    /**
     * 显示对话框
     */
    public void show() {
        showAlterStyle();
        LiveAnalysisUtil.reportLiveExChangeWindowShow(encLiveId, encJobId, mType);
    }

    /**
     * 显示高亮样式的对话框
     */
    private void showAlterStyle() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.live_exchange_dialog, null);
        setupExchangeInfo(view);
        setupContent(view, mDefaultTitle, mDefaultText);
        setupButtons(view);
        showDialog(view);
    }

    /**
     * 设置交换信息
     */
    private void setupExchangeInfo(View view) {
        MTextView mExchangeText = view.findViewById(R.id.mExchangeText);
        ImageView mExchangeIcon = view.findViewById(R.id.mExchangeIcon);

        mExchangeText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13);
        mExchangeIcon.setVisibility(View.VISIBLE);

        mExchangeText.setText(mDefaultHeadTitle);

        switch (mType) {
            case "1":
                mExchangeIcon.setImageResource(R.mipmap.ic_exchange_dia_phone);
                mExchangeText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
                break;
            case "2":
                mExchangeText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
                mExchangeIcon.setImageResource(R.mipmap.ic_exchange_dia_wechat);
                break;
            case "3":
            case "4":
                setExchangeIcon(mExchangeIcon);
                break;
        }
    }

    /**
     * 设置内容
     */
    private void setupContent(View view, String title, String content) {
        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        MTextView mDescView = view.findViewById(R.id.mDescView);
        MTextView mTipView = view.findViewById(R.id.mTipView);

        mTitleView.setText(title);

        mDescView.setText(content);

        if (!TextUtils.isEmpty(mTip)) {
            mTipView.setVisibility(View.VISIBLE);
            mTipView.setText(mTip);
        }
    }

    /**
     * 设置按钮
     */
    private void setupButtons(View view) {
        MTextView mCancelView = view.findViewById(R.id.mCancelView);
        MTextView mSureView = view.findViewById(R.id.mSureView);

        mCancelView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (mCallBack != null) {
                    mCallBack.onCancelListener();
                }
                LiveAnalysisUtil.reportLiveExChangeWindowClick(encLiveId, mType,"0" ,encJobId);
            }
        });

        mSureView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (mCallBack != null) {
                    mCallBack.onUserExchangeListener("");
                }
                LiveAnalysisUtil.reportLiveExChangeWindowClick(encLiveId, mType,"1" ,encJobId);
                dismiss();
            }
        });

        if (!TextUtils.isEmpty(mSureText)) {
            mSureView.setText(mSureText);
        }

        if (!TextUtils.isEmpty(mCancelText)) {
            mCancelView.setText(mCancelText);
        }
    }

    /**
     * 设置交换图标
     */
    private void setExchangeIcon(ImageView exchangeIconView) {
        if (UserManager.isGeekRole()) {
            exchangeIconView.setVisibility(View.VISIBLE);
            exchangeIconView.setImageResource(R.mipmap.ic_exchange_dia_resume);
        } else {
            exchangeIconView.setVisibility(View.GONE);
        }
    }

    /**
     * 显示对话框
     */
    private void showDialog(View contentView) {
        mDialog = new Dialog(mContext, com.twl.ui.R.style.twl_ui_common_dialog);
        mDialog.setCanceledOnTouchOutside(false);

        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
        );
        mDialog.addContentView(contentView, lp);

        Window window = mDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        if (ActivityUtils.isValid(mContext)) {
            try {
                mDialog.show();
            } catch (Exception e) {
                TLog.error(TAG, e, "exchange dialog");
            }
        }
    }

    /**
     * 关闭对话框
     */
    private void dismiss() {
        if (mDialog != null && mDialog.isShowing()) {
            mDialog.dismiss();
        }
    }

    /**
     * 交换回调接口
     */
    public interface OnExchangeCallBack {
        void onUserExchangeListener(String mulResumeWaring);

        default void onCancelListener() {
        }
    }
} 