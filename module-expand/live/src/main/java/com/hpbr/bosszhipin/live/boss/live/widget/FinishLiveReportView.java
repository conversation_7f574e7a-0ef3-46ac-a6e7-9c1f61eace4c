package com.hpbr.bosszhipin.live.boss.live.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.LiveFinishViewModel;
import com.hpbr.bosszhipin.live.boss.util.AnchorUtil;
import com.hpbr.bosszhipin.live.net.response.BossEndOverviewResponse;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Created by <PERSON><PERSON> on 2023/2/23
 */
public class FinishLiveReportView extends ConstraintLayout implements View.OnClickListener {
    private final Context mContext;

    private ConstraintLayout clNumContainer;
    private LinearLayout llIntention, llQuestionAnswer, llSeeExplain, llLoveTalentRank, llLuckyDrawFeedback, llSeeMore;
    private MTextView tvAudienceNum, tvElapseTime,tvElapseTimeTips, tvResumeNum, tvResumeNumTips,tvQuestionAnswer, tvQuestionAnswerDesc;
    private View dividerIntention, dividerQuestionAnswer, dividerLoveTalentRank, dividerFeedback, dividerLuckyDrawFeedback;

    private BossEndOverviewResponse overviewResponse;
    private boolean isThisLiveBoss, isExperienceLive;
    private boolean isAnchor;
    private LiveFinishViewModel viewModel;
    private MTextView tvDeliverPhoneNum;
    private MTextView textViewDeliverPhoneTips;
    private MTextView tvDeliverWechatNum;
    private MTextView textViewDeliverWechatTips;

    public FinishLiveReportView(Context context) {
        this(context, null);
    }

    public FinishLiveReportView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FinishLiveReportView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        initView();
        bindListener();
    }

    private void initView() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.live_view_finish_report, this);
        clNumContainer = view.findViewById(R.id.cl_num_container);
        llIntention = view.findViewById(R.id.ll_intention);
        llQuestionAnswer = view.findViewById(R.id.ll_question_answer);
        llSeeExplain = view.findViewById(R.id.ll_see_explain);
        llLuckyDrawFeedback = view.findViewById(R.id.ll_lucky_draw_feedback);
        llSeeMore = view.findViewById(R.id.ll_see_more);
        tvQuestionAnswer = view.findViewById(R.id.tv_question_answer);
        tvQuestionAnswerDesc = view.findViewById(R.id.tv_question_answer_desc);
        tvAudienceNum = view.findViewById(R.id.tv_audience_num);
        tvElapseTime = view.findViewById(R.id.tv_elapse_time);
        tvElapseTimeTips = view.findViewById(R.id.textView_time_tips);
        tvResumeNum = view.findViewById(R.id.tv_resume_num);
        tvResumeNumTips = view.findViewById(R.id.textView_resume_num_tips);
        llLoveTalentRank = view.findViewById(R.id.ll_love_talent_rank);
        dividerIntention = view.findViewById(R.id.divider_intention);
        dividerQuestionAnswer = view.findViewById(R.id.divider_question_answer);
        dividerLoveTalentRank = view.findViewById(R.id.divider_love_talent_rank);
        dividerFeedback = view.findViewById(R.id.divider_feedback);
        dividerLuckyDrawFeedback = view.findViewById(R.id.divider_lucky_draw_feedback);
        tvDeliverPhoneNum = view.findViewById(R.id.tv_deliver_phone_num);
        textViewDeliverPhoneTips = view.findViewById(R.id.textView_deliver_phone_tips);
        tvDeliverWechatNum = view.findViewById(R.id.tv_deliver_wechat_num);
        textViewDeliverWechatTips = view.findViewById(R.id.textView_deliver_wechat_tips);
    }

    private void bindListener() {
        llQuestionAnswer.setOnClickListener(this);
        llIntention.setOnClickListener(this);
        llLoveTalentRank.setOnClickListener(this);
        llSeeExplain.setOnClickListener(this);
        llLuckyDrawFeedback.setOnClickListener(this);
        llSeeMore.setOnClickListener(this);
    }

    public void setData(@NonNull BossEndOverviewResponse resp, boolean isThisLiveBoss, boolean isExperienceLive, boolean isAnchor, LiveFinishViewModel viewModel) {
        if (resp == null) return;
        overviewResponse = resp;
        this.isThisLiveBoss = isThisLiveBoss;
        this.isExperienceLive = isExperienceLive;
        this.isAnchor = isAnchor;
        this.viewModel = viewModel;

        checkShowBzpView(resp);

        if (resp.liveState != LiveState.DELAY) {// 已结束
            checkShowItems(true);
        }
    }

    private void checkShowBzpView(@NonNull BossEndOverviewResponse resp) {
        boolean onlineNormalRoom = resp.isOnlineNormalRoom();
        if(onlineNormalRoom){
            tvDeliverPhoneNum.setVisibility(View.VISIBLE);
            textViewDeliverPhoneTips.setVisibility(View.VISIBLE);
            tvDeliverWechatNum.setVisibility(View.VISIBLE);
            textViewDeliverWechatTips.setVisibility(View.VISIBLE);
            tvElapseTime.setVisibility(View.GONE);
            tvElapseTimeTips.setVisibility(View.GONE);
            tvResumeNumTips.setText("简历投递");
            tvAudienceNum.setText(AnchorUtil.formatViewers(resp.liveViewersCnt));
            tvResumeNum.setText(AnchorUtil.formatViewers(resp.resumeDeliverCnt));
            tvDeliverWechatNum.setText(AnchorUtil.formatViewers(resp.deliverWxCount));
            tvDeliverPhoneNum.setText(AnchorUtil.formatViewers(resp.deliverPhoneCount));
        }else{
            tvResumeNumTips.setText("收获简历");
            tvElapseTime.setVisibility(View.VISIBLE);
            tvElapseTimeTips.setVisibility(View.VISIBLE);
            tvDeliverPhoneNum.setVisibility(View.GONE);
            textViewDeliverPhoneTips.setVisibility(View.GONE);
            tvDeliverWechatNum.setVisibility(View.GONE);
            textViewDeliverWechatTips.setVisibility(View.GONE);
            tvAudienceNum.setText(AnchorUtil.formatViewers(resp.liveViewersCnt));
            tvResumeNum.setText(AnchorUtil.formatViewers(resp.resumeDeliverCnt));
            if (!TextUtils.isEmpty(resp.liveElapsedTime)) {
                tvElapseTime.setText(resp.liveElapsedTime);
            } else {
                tvElapseTime.setText("00:00:00");
            }
        }
    }

    public void setExplainViewData(BossEndOverviewResponse overviewResponse) {
        this.overviewResponse = overviewResponse;
        if (null == overviewResponse || null == tvQuestionAnswer) return;
        //只有普通直播间处理直播高光
        /*直播高光 or 我的讲解*/
        if (overviewResponse.hasExplainOrHighlight()) {
            tvQuestionAnswer.setText("直播讲解");
            tvQuestionAnswerDesc.setVisibility(View.VISIBLE);
            if (overviewResponse.isHasLiveHighlight()) {
                tvQuestionAnswerDesc.setText(overviewResponse.publishLiveHighlight == 1 ? "已为您推广精彩片段" : "您的精彩片段待推广");
            } else {
                tvQuestionAnswerDesc.setText("");
            }
        } else {
            tvQuestionAnswer.setText("直播讲解");
            tvQuestionAnswerDesc.setText("");
        }
    }

    /**
     * 需求：当item数量大于等于3个时，展示前2项且展示"查看更多"
     *
     * @param collapseMode true 折叠模式，false 展开模式
     */
    private void checkShowItems(boolean collapseMode) {
        int MAX_COLLAPSE_ITEM_COUNT = 2;
        // 当前item前显示了几项
        int preShownCount = 0;
        /*牛人榜*/
        // 普通直播间显示「牛人榜」按钮，高级直播间不显示 体验直播间也不显示 本场boss不显示
        // 房间类型 4:活动  5:体验，不显示；课程直播也不显示
        boolean intentionShow = overviewResponse.roomLevel == 1 && !isExperienceLive && !isThisLiveBoss
                && overviewResponse.liveRoomType != 4 && overviewResponse.liveRoomType != 5 && overviewResponse.roomKind != 5;
        llIntention.setVisibility(intentionShow ? View.VISIBLE : View.GONE);
        checkShowDivider(dividerIntention, preShownCount);
        preShownCount += intentionShow ? 1 : 0;
        if (intentionShow) {
            viewModel.addP4("牛人榜");
        }

        /*我的讲解*/
        //只有普通直播间处理直播高光
        /*直播高光 or 我的讲解*/
        if (overviewResponse.hasExplainOrHighlight()) {
            tvQuestionAnswer.setText("直播讲解");
            tvQuestionAnswerDesc.setVisibility(View.VISIBLE);
            if (overviewResponse.isHasLiveHighlight()) {
                tvQuestionAnswerDesc.setText(overviewResponse.publishLiveHighlight == 1 ? "已为您推广精彩片段" : "您的精彩片段待推广");
            } else {
                tvQuestionAnswerDesc.setText("");
            }
        } else {
            tvQuestionAnswer.setText("直播讲解");
            tvQuestionAnswerDesc.setText("");
        }

        boolean questionAnswerShow = (overviewResponse.hasExplain == 1 && !isThisLiveBoss) || overviewResponse.isHasLiveHighlight();
        llQuestionAnswer.setVisibility(questionAnswerShow ? View.VISIBLE : View.GONE);
        checkShowDivider(dividerQuestionAnswer, preShownCount);
        preShownCount += questionAnswerShow ? 1 : 0;


        /*爱才热力榜 代播主播和管理员显示后台判断*/
        boolean loveTalentRankShow = !TextUtils.isEmpty(overviewResponse.heatPowerH5Url);
        llLoveTalentRank.setVisibility(loveTalentRankShow ? View.VISIBLE : View.GONE);
        if (collapseMode && preShownCount >= MAX_COLLAPSE_ITEM_COUNT) {
            llLoveTalentRank.setVisibility(View.GONE);
        }
        checkShowDivider(dividerLoveTalentRank, preShownCount);
        preShownCount += loveTalentRankShow ? 1 : 0;

        /*观看回放*/
        boolean showSeeExplain = overviewResponse.liveVideoFlag == 1 && !TextUtils.isEmpty(overviewResponse.liveVideoUrl) && overviewResponse.liveVideoState != 1;
        llSeeExplain.setVisibility(showSeeExplain ? View.VISIBLE : View.GONE);
        if (showSeeExplain) {
            viewModel.addP4("观看回放");
        }
        if (collapseMode && preShownCount >= MAX_COLLAPSE_ITEM_COUNT) {
            llSeeExplain.setVisibility(View.GONE);
        }

        checkShowDivider(dividerFeedback, preShownCount);
        preShownCount += showSeeExplain ? 1 : 0;

        /*上传抽奖订单*/
        boolean luckyDrawFeedbackShow = !TextUtils.isEmpty(overviewResponse.luckyDrawTrackingNumberH5Url);
        llLuckyDrawFeedback.setVisibility(luckyDrawFeedbackShow ? View.VISIBLE : View.GONE);
        if (collapseMode && preShownCount >= MAX_COLLAPSE_ITEM_COUNT) {
            llLuckyDrawFeedback.setVisibility(View.GONE);
        }
        if (luckyDrawFeedbackShow) {
            viewModel.addP4("上传抽奖订单");
        }
        checkShowDivider(dividerLuckyDrawFeedback, preShownCount);
        preShownCount += luckyDrawFeedbackShow ? 1 : 0;

        /*查看更多*/
        llSeeMore.setVisibility(collapseMode && preShownCount > MAX_COLLAPSE_ITEM_COUNT ? View.VISIBLE : View.GONE);
    }

    /**
     * 检查显示item的分割线是否显示
     */
    private void checkShowDivider(View divider, int preShownCount) {
        divider.setVisibility(View.VISIBLE);
    }

    public void hideQuestionAnswerLayout() {
        llQuestionAnswer.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View v) {
        if (overviewResponse == null) return;
        if (ClickProtectedUtil.blockClickEvent()) {
            return;
        }
        if (actionListener == null) return;

        int id = v.getId();
        if (id == R.id.ll_question_answer) {
            actionListener.onQuestionAnswerClicked();
        } else if (id == R.id.ll_intention) {/*点击「牛人榜」*/
            actionListener.onIntentionClicked();
        } else if (id == R.id.ll_see_explain) {
            actionListener.onSeeExplain();
        } else if (id == R.id.ll_lucky_draw_feedback) {// 上传抽奖订单
            actionListener.onLuckyDrawFeedbackClicked();
        } else if (id == R.id.ll_see_more) {// 查看更多
            checkShowItems(false);
            actionListener.onSeeMoreClicked();
        } else if (id == R.id.ll_love_talent_rank) {// 爱才热力榜
            actionListener.onLoveTalentRankClicked();
        }
    }

    private IOnActionListener actionListener;

    public void setActionListener(IOnActionListener actionListener) {
        this.actionListener = actionListener;
    }

    public interface IOnActionListener {
        void onQuestionAnswerClicked();

        void onIntentionClicked();

        void onSeeExplain();

        void onLuckyDrawFeedbackClicked();

        void onLoveTalentRankClicked();

        void onSeeMoreClicked();
    }
}
