package com.hpbr.bosszhipin.live.campus.audience.job.manage;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ImageSpan;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.facebook.imagepipeline.common.ResizeOptions;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dialog.ChatWechatDialog;
import com.hpbr.bosszhipin.company.export.Company;
import com.hpbr.bosszhipin.company.export.CompanyConsts;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.campus.audience.dialog.ExchangeDialog;
import com.hpbr.bosszhipin.live.campus.audience.dialog.LiveDeliverTypeChooseDialog;
import com.hpbr.bosszhipin.live.campus.audience.viewmodel.AudienceViewModel;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.geek.audience.adapter.LiveHandiInfoAdapter;
import com.hpbr.bosszhipin.live.geek.audience.mvp.view.RecruitJobDetailView;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.response.GeekGetJobStatusResponse;
import com.hpbr.bosszhipin.live.net.response.GeekRecruitDetailResponse;
import com.hpbr.bosszhipin.live.util.ICommonCallback;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.util.ReportUtil;
import com.hpbr.bosszhipin.module.position.entity.detail.JobBasicInfo;
import com.hpbr.bosszhipin.module.position.entity.detail.JobDescriptionInfo;
import com.hpbr.bosszhipin.module.position.entity.detail.JobRequiredSkillsInfo;
import com.hpbr.bosszhipin.module.resume.utils.ResumeHighlight;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.KeyWordFloatLayout;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.weiget.CenterImageSpan;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.ui.CollapseTextView3;
import com.twl.ui.ToastUtils;
import com.twl.ui.flexbox.StringTagAdapter;
import com.twl.utils.ActivityUtils;
import com.twl.utils.BitmapUtil;

import net.bosszhipin.api.GetJobDetailResponse;
import net.bosszhipin.api.GetJobQueryBannerResponse;
import net.bosszhipin.api.ServerSalaryWalfareInfoBean;
import net.bosszhipin.api.ServerWordHighlightBean;
import net.bosszhipin.api.bean.ServerAfterNameIconBean;
import net.bosszhipin.api.bean.ServerCommonIconBean;
import net.bosszhipin.api.bean.ServerHandicappedBean;
import net.bosszhipin.api.bean.ServerHandicappedDetailBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.api.bean.ServerHighlightPairContentBean;
import net.bosszhipin.api.bean.ServerJDBannerInfoBean;
import net.bosszhipin.api.bean.ServerProxyCertBean;
import net.bosszhipin.api.bean.job.ServerBrandComInfoBean;
import net.bosszhipin.api.bean.job.ServerJobBaseInfoBean;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：JobDetaiManager
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/4/7  6:24 下午
 */
public class JobDetailManager {

    private FragmentActivity activity;
    private AudienceViewModel mViewModel;
    private RecruitJobDetailView view;

    public JobDetailManager(AudienceViewModel viewModel, RecruitJobDetailView view) {
        this.mViewModel = viewModel;
        this.view = view;
        this.activity = (FragmentActivity) view.getView().getContext();
    }

    /**
     * 渲染职位数据
     */
    public void renderJobInfo(@NonNull JobInfoBean jobInfoBean, @Nullable GeekGetJobStatusResponse jobStatusResponse,
                              @NonNull GetJobDetailResponse jobDetailResponse, GetJobQueryBannerResponse queryBanner) {
        ServerJobBaseInfoBean serverJobBaseInfoBean = jobDetailResponse.jobBaseInfo;
        if (serverJobBaseInfoBean == null) return;
        jobInfoBean.name = serverJobBaseInfoBean.positionName;//点击弹幕时，传进来的jobInfoBean的name为空，所以此时把详情接口中的职位名称赋值给jobInfoBean的name字段
        boolean isAgentJob = serverJobBaseInfoBean.proxyJob == 1 && (serverJobBaseInfoBean.proxyType == 1 || serverJobBaseInfoBean.proxyType == 3);

        /*渲染职位基本信息*/
        JobBasicInfo basicInfo = new JobBasicInfo(serverJobBaseInfoBean, jobDetailResponse.headhunterInfo);
        renderJobBasicInfo(basicInfo, jobDetailResponse);

        /*标题和Icons*/
        if(isAgentJob){
            renderTitleAndIcons(basicInfo);
            renderDescription(serverJobBaseInfoBean.salaryWelfareInfo);
        }else {
            view.getTvJobName().setText(serverJobBaseInfoBean.positionName);
            view.getViewJobSalaryDetail().setVisibility(View.GONE);
        }

        boolean isBossInvalid = jobDetailResponse.bossBaseInfo != null && jobDetailResponse.bossBaseInfo.jobPageInvalid; // Boss注销
        if (serverJobBaseInfoBean.isJobValid() && !isBossInvalid) {/*职位合法 && BOSS合法*/
            if (jobDetailResponse.bossBaseInfo != null) {
                jobInfoBean.bossId = jobDetailResponse.bossBaseInfo.bossId;// 手动赋值bossId，防止在跳转到去沟通页跳不过去
            }

            /*「技能要求」*/
            JobRequiredSkillsInfo skills = null;
            if (!LList.isEmpty(serverJobBaseInfoBean.requiredSkillsHighlights)) {
                view.getTvSkillTitle().setVisibility(View.GONE);
                skills = new JobRequiredSkillsInfo(serverJobBaseInfoBean.requiredSkills, serverJobBaseInfoBean.requiredSkillsHighlights);
            } else {
                view.getTvSkillTitle().setVisibility(View.GONE);
            }

            /* 渲染「无障碍接受情况」模块数据*/
            renderDisableApplyJob(jobDetailResponse.handicappedInfo);

            /*「职位详情」描述*/
            JobDescriptionInfo descriptionInfo = new JobDescriptionInfo(serverJobBaseInfoBean.jobId, serverJobBaseInfoBean.jobDesc, serverJobBaseInfoBean.jobDescHighlights, skills);
            setDescriptionInfo(descriptionInfo);

            /*技能要求*/
            setFlexBoxData(activity, descriptionInfo.skillsInfo);

            /*显示公司信息*/
            showCompanyInfo(serverJobBaseInfoBean, jobDetailResponse.brandComInfo, jobDetailResponse.securityId);
        } else {
            view.view_job_handicapped_ctb.setVisibility(View.GONE);
            view.getTvDescriptionTitle().setVisibility(View.GONE);
            view.getTvDescription().setText("该职位已关闭");
            view.getTvDescription().setVisibility(View.VISIBLE);
            view.getClCompanyInfo().setVisibility(View.GONE);
            view.getTvSkillTitle().setVisibility(View.GONE);
            view.getFlexboxLayout().setVisibility(View.GONE);
        }

        /*渲染底部布局*/
        jobInfoBean.applyOnlineUrl = jobDetailResponse.getNetPostUrl();
        renderBottomLayout(jobInfoBean, jobStatusResponse, serverJobBaseInfoBean.isJobValid(), jobDetailResponse.isShowNetPost());

        /*渲染认证资质*/
        renderCertQualification(jobDetailResponse);

        /*渲染海螺优选条*/
        renderBlueStar(jobDetailResponse, queryBanner);
    }

    /**
     * 渲染职位基本信息
     *
     * @param info
     */
    private void renderJobBasicInfo(@NonNull JobBasicInfo info, @NonNull GetJobDetailResponse jobDetailResponse) {
        ServerJobBaseInfoBean job = info.serverJob;
        if (job == null) return;
        /*「地址」*/
        view.getTvRequiredLocation().setText(getAddressDesc(job), View.GONE);
        /*「工作经验」*/
        view.getTvRequiredWorkExp().setText(job.experienceName, View.GONE);
        /*「学历」*/
        view.getTvRequiredDegree().setText(job.degreeName, View.GONE);
        /*「薪资」*/
        String salary;
        if (job.isJobValid()) {
            salary = job.salaryDesc;
            view.getTvJobSalary().setTextColor(ContextCompat.getColor(activity, com.hpbr.bosszhipin.app.R.color.app_green_dark));
        } else {
            salary = activity.getString(com.hpbr.bosszhipin.app.R.string.string_job_status_offline);
            view.getTvJobSalary().setTextColor(ContextCompat.getColor(activity, com.hpbr.bosszhipin.app.R.color.text_c3));
        }
        view.getTvJobSalary().setText(salary, View.GONE);
        /*客户公司*/
        if (null != jobDetailResponse.brandComInfo) {
            String customerBrandName = jobDetailResponse.brandComInfo.customerBrandName;
            view.getTvCustomerCompany().setVisibility(TextUtils.isEmpty(customerBrandName) ? View.GONE : View.VISIBLE);
            if (!TextUtils.isEmpty(customerBrandName)) {
                String hint = (jobDetailResponse.isAnXinBaoJob() || jobDetailResponse.isHaiLuoGaoXuanJob() ) ? "入职公司：" : "客户公司：";
                view.getTvCustomerCompany().setText(hint.concat(customerBrandName));
            }
        }
    }

    private void renderTitleAndIcons(@NonNull JobBasicInfo info) {
        ServerJobBaseInfoBean serverJob = info.serverJob;
        if (null == serverJob || TextUtils.isEmpty(serverJob.positionName)) {
            view.getTvJobName().setVisibility(View.GONE);
            return;
        }
        List<ServerAfterNameIconBean> afterNameIcons = serverJob.afterNameIcons;
        if (LList.isEmpty(afterNameIcons)) {
            view.getTvJobName().setText(serverJob.positionName);
            return;
        }
        SpannableStringBuilder ssb = new SpannableStringBuilder(serverJob.positionName);
        AppThreadFactory.POOL.execute(() -> {
            for (ServerAfterNameIconBean iconBean : afterNameIcons) {
                if (null == iconBean) continue;
                if (!TextUtils.isEmpty(iconBean.url)) {
                    ImageRequestBuilder builder = ImageRequestBuilder
                            .newBuilderWithSource(Uri.parse(iconBean.url))
                            .setProgressiveRenderingEnabled(true);

                    if (iconBean.width > 0 && iconBean.height > 0) {
                        builder.setResizeOptions(new ResizeOptions(iconBean.width, iconBean.height));
                    }

                    Bitmap agentBitmap = BitmapUtil.urlToBitmapSync(builder);

                    if (null != agentBitmap) {
                        SpannableStringBuilder imageBuilder = new SpannableStringBuilder();
                        imageBuilder.append("  ");
                        ImageSpan agentSpan = new CenterImageSpan(activity, agentBitmap, 2);
                        imageBuilder.setSpan(agentSpan, imageBuilder.length() - 1, imageBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                        ssb.append(imageBuilder);
                    }
                }
            }
            //更新UI
            activity.runOnUiThread(() -> {
                if (ActivityUtils.isValid(activity) && null != view) {
                    view.getTvJobName().setText(ssb);
                }
            });
        });
    }

    /**
     * 获取地址描述信息
     *
     * @param jobInfo
     * @return
     */
    private String getAddressDesc(ServerJobBaseInfoBean jobInfo) {
        if (jobInfo == null) return "";
        if (!TextUtils.isEmpty(jobInfo.locationDesc)) {
            return jobInfo.locationDesc;
        } else {
            List<String> addressDescList = new ArrayList<>();
            //城市
            if (!TextUtils.isEmpty(jobInfo.locationName)) {
                addressDescList.add(jobInfo.locationName.length() > 4 ? jobInfo.locationName.substring(0, 4) : jobInfo.locationName);
            }
            //区县
            if (!TextUtils.isEmpty(jobInfo.areaDistrict)) {
                addressDescList.add(jobInfo.areaDistrict.length() > 6 ? jobInfo.areaDistrict.substring(0, 6) : jobInfo.areaDistrict);
            }
            //商圈
            if (!TextUtils.isEmpty(jobInfo.businessDistrict)) {
                addressDescList.add(jobInfo.businessDistrict.length() > 6 ? jobInfo.businessDistrict.substring(0, 6) : jobInfo.businessDistrict);
            }
            return StringUtil.connectTextWithChar(StringUtil.COMMON_SEPERATOR, addressDescList);
        }
    }

    /**
     * 渲染「无障碍接受情况」模块数据
     */
    private void renderDisableApplyJob(ServerHandicappedBean handicappedInfo) {
        view.view_job_handicapped_ctb.setVisibility(handicappedInfo != null ? View.VISIBLE : View.GONE);
        if (handicappedInfo == null) return;
        if (handicappedInfo.showType == 0 && !LList.isEmpty(handicappedInfo.detailInfos)) {
            LiveHandiInfoAdapter adapter = new LiveHandiInfoAdapter(handicappedInfo.detailInfos);
            view.rcvSkills.setAdapter(adapter);
            view.llNomore.setVisibility(View.GONE);
            view.rcvSkills.setVisibility(View.VISIBLE);
        } else if (handicappedInfo.showType == 1 && !LList.isEmpty(handicappedInfo.detailInfos)) {
            view.llNomore.setVisibility(View.VISIBLE);
            view.rcvSkills.setVisibility(View.GONE);
            ServerHandicappedDetailBean bean = LList.getElement(handicappedInfo.detailInfos, 0);
            if (bean != null) {
                view.sdvIcon.setImageURI(bean.icon);
                view.mtvHandiDesc.setText(bean.title);
            }
        } else {
            view.view_job_handicapped_ctb.setVisibility(View.GONE);
        }
    }

    /**
     * 设置「职位详情」描述
     *
     * @param info
     */
    private void setDescriptionInfo(JobDescriptionInfo info) {
        SpannableStringBuilder descBuilder = getHighlightText(activity, info.desc, info.jobDescHighlights);
        if (TextUtils.isEmpty(descBuilder)) {
            view.getTvDescription().setVisibility(View.GONE);
        } else {
            view.getTvDescription().setText(descBuilder);
            view.getTvDescription().setVisibility(View.VISIBLE);
        }
        /*设置「职位详情」标题的显示与隐藏*/
        view.getTvDescriptionTitle().setVisibility(!TextUtils.isEmpty(info.desc) ? View.VISIBLE : View.GONE);
    }

    /**
     * 渲染「技能要求」标签数据
     *
     * @param activity
     * @param info
     */
    private void setFlexBoxData(Activity activity, JobRequiredSkillsInfo info) {
        if (info != null && !LList.isEmpty(info.requiredSkillsHighlights)) {
            view.getFlexboxLayout().setVisibility(View.VISIBLE);
            view.getFlexboxLayout().removeAllViews();
            for (ServerWordHighlightBean bean : info.requiredSkillsHighlights) {
                if (bean == null || TextUtils.isEmpty(bean.content)) continue;
                boolean isHighlight = bean.highlight;
                String tag = bean.content;
                MTextView textView = getTag(activity, tag, isHighlight);
                view.getFlexboxLayout().addView(textView);
            }
        } else if (info != null && !LList.isEmpty(info.requiredSkill)) {
            view.getFlexboxLayout().setVisibility(View.VISIBLE);
            view.getFlexboxLayout().removeAllViews();
            for (String text : info.requiredSkill) {
                if (TextUtils.isEmpty(text)) {
                    continue;
                }
                MTextView textView = getTag(activity, text, false);
                view.getFlexboxLayout().addView(textView);
            }
        } else {
            view.getFlexboxLayout().setVisibility(View.GONE);
        }
    }

    /**
     * 创建标签View
     *
     * @param activity
     * @param text
     * @param isHighlight
     * @return
     */
    private MTextView getTag(Activity activity, @NonNull String text, boolean isHighlight) {
        int paddingHorizontal = Scale.dip2px(activity, 10);
        int paddingVertical = Scale.dip2px(activity, 4);
        MTextView textView = new MTextView(activity);
        textView.setText(text);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setGravity(Gravity.CENTER);
        textView.setSingleLine();

        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13f);
        textView.setTextColor(ContextCompat.getColor(activity, isHighlight ? com.hpbr.bosszhipin.app.R.color.app_green_dark : com.hpbr.bosszhipin.app.R.color.text_c6_light));
        textView.setBackgroundResource(isHighlight ? com.hpbr.bosszhipin.app.R.drawable.bg_f1_match_word_green : com.hpbr.bosszhipin.app.R.drawable.bg_f1_match_word_gray);
        return textView;
    }

    private SpannableStringBuilder getHighlightText(Activity activity, String text, List<ServerHighlightListBean> jobDescHighlights) {
        return ResumeHighlight.lightOn(text, jobDescHighlights, ContextCompat.getColor(activity, R.color.live_2653CAC3), Scale.dip2px(activity, 4));
    }

    /**
     * 渲染公司信息
     *
     * @param job
     * @param brand
     * @param securityId
     */
    private void showCompanyInfo(ServerJobBaseInfoBean job, ServerBrandComInfoBean brand, String securityId) {
        if (job == null || brand == null) {
            view.getClCompanyInfo().setVisibility(View.GONE);
            return;
        }
        if (mViewModel.dataCenter.detailResponse.isHighLevelRoom()) {// 高级直播间
            view.getClCompanyInfo().setVisibility(View.VISIBLE);
            view.getIvCompany().setVisibility(UserManager.isBossRole() || !brand.canJumpToBrandPage ? View.GONE : View.VISIBLE); /*B身份隐藏向右的箭头*/

            if (job.proxyType == 3) {//派遣职位
                String companyDesc = StringUtil.connectTextWithChar(StringUtil.COMMON_BIG_DOT, brand.proxyStageName, brand.proxyScaleName, brand.proxyIndustryName);/*中介公司融资阶段    中介公司规模   中介公司行业  */
                view.getTvCompanyDesc().setText(companyDesc);
                view.getTvCompanyName().setText(brand.proxyBrandName);
                view.getSdvCompanyLogo().setImageURI(brand.proxyBrandLogo);
            } else {
                String companyDesc = StringUtil.connectTextWithChar(StringUtil.COMMON_BIG_DOT, brand.stageName, brand.scaleName);/*融资阶段    公司规模   中介公司行业  */
                List<String> industryList = brand.multiIndustryName;
                if (!LList.isEmpty(industryList)) {
                    StringBuilder sbIndustry = new StringBuilder();
                    for (String s : industryList) {
                        sbIndustry.append(s).append(" ");
                    }

                    if (!TextUtils.isEmpty(sbIndustry.toString())) {
                        if (LList.getCount(industryList) <= 1) { // 行业等于一个时
                            companyDesc = StringUtil.connectTextWithChar(StringUtil.COMMON_BIG_DOT, companyDesc, sbIndustry.toString());
                        } else {
                            companyDesc = StringUtil.connectTextWithChar(StringUtil.COMMON_BIG_DOT, companyDesc, "\n" + sbIndustry);
                        }
                    }
                } else if (!LText.empty(brand.industryName)) {
                    companyDesc = StringUtil.connectTextWithChar(StringUtil.COMMON_BIG_DOT, companyDesc, brand.industryName);
                }
                view.getTvCompanyDesc().setText(companyDesc);
                view.getSdvCompanyLogo().setImageURI(StringUtil.getNetworkUri(brand.logo));
                view.getTvCompanyName().setText(brand.brandName);
            }

            view.getClCompanyInfo().setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    if (UserManager.isBossRole()) return; /*B身份无点击操作*/
                    boolean hideBottomPosition = mViewModel.dataCenter.detailResponse.isPreLive() || mViewModel.dataCenter.detailResponse.canDeliver;//未开播允许投递，开播后需要判断是否可投递
                    if(brand.canJumpToBrandPage){
                        Company.builder()
                                .setBrandId(brand.brandId)
                                .setSf(CompanyConsts.VALUE_SF_LIVE_RECRUIT)
                                .setFrom(CompanyConsts.From.FROM_LIVE_RECRUIT)
                                .setHideBottomPosition(hideBottomPosition)
                                .setHideVideo(true)
                                .setSecurityId(securityId)
                                .build(activity)
                                .jump();
                    }
                }
            });
        } else {
            view.getClCompanyInfo().setVisibility(View.GONE);
        }
    }

    /**
     * 渲染底部布局
     */
    private void renderBottomLayout(@NonNull JobInfoBean jobInfoBean, GeekGetJobStatusResponse jobStatus, boolean isJobValid, boolean isAtsPostType) {
        if (jobStatus == null) return;
        /*设置底部布局的可见性*/
        renderBottomLayoutVisibility(isJobValid);
        /*渲染底部提示条*/
        renderBottomTip();
        /*渲染底部收藏按钮状态*/
        renderBottomCollect(jobInfoBean, jobStatus);
        /*渲染底部「投递」/「订阅」按钮*/
        renderBottomBtnActionButton(jobInfoBean, jobStatus, mViewModel.dataCenter.detailResponse, isAtsPostType);
    }

    /**
     * 设置底部布局的可见性
     *
     * @param isJobValid 是否职位有效
     */
    private void renderBottomLayoutVisibility(boolean isJobValid) {
        if (!isJobValid) {/*职位停止招聘，底部按钮隐藏*/
            view.getJobDetailBottom().setVisibility(View.GONE);
        } else if (mViewModel.dataCenter.detailResponse.isHighLevelRoom()) {//高级直播间底部布局可见
            view.getJobDetailBottom().setVisibility(View.VISIBLE);
        } else {//普通直播间
            if (mViewModel.dataCenter.detailResponse.isPreLive()) {/*直播前 B端身份不展示订阅及收藏按钮*/
                view.getJobDetailBottom().setVisibility(UserManager.isGeekRole() ? View.VISIBLE : View.GONE);
            } else {
                view.getJobDetailBottom().setVisibility(View.VISIBLE);
            }
        }
    }


    /**
     * 渲染底部提示条
     */
    private void renderBottomTip() {
        if (UserManager.isBossRole() && mViewModel.dataCenter.detailResponse.isLiving()) {/*B身份&&（暂停或直播中状态），展示提示文案*/
            view.getTvHint().setVisibility(View.VISIBLE);
        } else {
            view.getTvHint().setVisibility(View.GONE);
        }
    }

    /**
     * 渲染底部收藏按钮状态
     */
    private void renderBottomCollect(@Nullable JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus) {
        TextView tvCollect = view.getTvCollect();//「收藏」按钮
        if (mViewModel.dataCenter.detailResponse.isHighLevelRoom() && mViewModel.dataCenter.detailResponse.isPreLive() && UserManager.isGeekRole()) {//高级直播间&&直播前&&C身份
            tvCollect.setVisibility(View.VISIBLE);
            if (jobStatus.isInterested()) {
                tvCollect.setText("已收藏");
                tvCollect.setCompoundDrawablesWithIntrinsicBounds(0, R.mipmap.live_ic_job_detail_collected, 0, 0);
            } else {// 未感兴趣
                tvCollect.setText("收藏");
                tvCollect.setCompoundDrawablesWithIntrinsicBounds(0, R.mipmap.live_ic_job_detail_collect, 0, 0);
            }

            tvCollect.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    handleClickCollect(jobStatus, jobInfoBean);
                }
            });
        } else {
            tvCollect.setVisibility(View.GONE);
        }
    }

    /**
     * 渲染底部「投递」/「订阅」按钮
     */
    private void renderBottomBtnActionButton(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus, @NonNull GeekRecruitDetailResponse geekResp, boolean isAtsPostType) {
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) view.getBtnAction().getLayoutParams();

        if (mViewModel.dataCenter.detailResponse.isPreLive()) {//直播前
            if (mViewModel.dataCenter.detailResponse.isHighLevelRoom()) {//高级直播间
                view.getBtnAction().setText("开播后可投递");
                view.getBtnAction().setEnabled(false);
                lp.leftMargin = UserManager.isGeekRole() ? 0 : ZPUIDisplayHelper.dp2px(activity, 20);/**这里设置0 ，是因为只有 高级直播间&& 直播前&&C身份 左侧的「收藏」按钮才可见 {@link JobDetailManager#renderBottomCollect }*/
            } else {
                view.getBtnAction().setText(jobStatus.isSubscribed() ? "已订阅" : "订阅");
                view.getBtnAction().setEnabled(true);
                lp.leftMargin = ZPUIDisplayHelper.dp2px(activity, 20);
                view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        handleClickSubcribe(jobInfoBean, jobStatus);//处理 点击「订阅」/「取消订阅」事件
                    }
                });
            }
        } else {//非直播前
            lp.leftMargin = ZPUIDisplayHelper.dp2px(activity, 20);
            if (isAtsPostType) {
                view.getBtnAction().setText("立即网申");
                view.getBtnAction().setEnabled(true);
                view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        handleClickNetPost(jobInfoBean, jobStatus, geekResp);//处理 点击「立即网申」事件
                    }
                });
            } else {
                boolean onlineNormalLevelRoom = geekResp.isOnlineNormalLevelRoom();
                boolean hasRelationAndDeliverOnlineRoom = jobStatus.hasRelationAndDeliverOnlineRoom();
                if(onlineNormalLevelRoom){
                    if(!hasRelationAndDeliverOnlineRoom){
                        view.getBtnAction().setText("联系boss");
                        view.getBtnAction().setEnabled(true);
                        view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                handleClickDeliver(jobInfoBean, jobStatus, geekResp);//处理 点击「投简历」事件
                            }
                        });
                    }else{
                        refreshButtonWhenDelivered();
                        view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                handleClickChat(jobInfoBean, jobStatus, geekResp);//处理 点击「投简历」事件
                            }
                        });
                    }

                }else{
                    if (jobStatus.isDelivered()) {//已投递
                        refreshButtonWhenDelivered();
                        view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {
                                handleClickChat(jobInfoBean, jobStatus, geekResp);//处理 点击「投简历」事件
                            }
                        });
                    } else {//未投递
                        view.getBtnAction().setText(geekResp.canDeliver ? (geekResp.isBlueRoom() ? "立即报名" : "投简历") :
                                (geekResp.isBlueRoom() ? "已结束报名" : "已结束投递"));
                        view.getBtnAction().setEnabled(geekResp.canDeliver);
                        if (geekResp.canDeliver) {
                            view.getBtnAction().setOnClickListener(new OnClickNoFastListener() {
                                @Override
                                public void onNoFastClick(View v) {
                                    handleClickDeliver(jobInfoBean, jobStatus, geekResp);//处理 点击「投简历」事件
                                }
                            });
                        }
                    }
                }
            }
        }
    }

    private void handleDeliver(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus, @NonNull GeekRecruitDetailResponse geekResp, boolean isNetPost) {
        if (mViewModel.dataCenter.isOnlineNormalLevelRoom()) {
            showDeliverTypeDialog(jobInfoBean, jobStatus, geekResp, isNetPost);
        } else {
            handleOfflineDeliver(jobInfoBean, geekResp, isNetPost);
        }
    }

    private void showDeliverTypeDialog(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus,
                                       @NonNull GeekRecruitDetailResponse geekResp, boolean isNetPost) {
        new LiveDeliverTypeChooseDialog(activity).setListener(type -> {
            if (isContactType(type)) {
                handleContactTypeDeliver(jobInfoBean, type);
            } else {
                handleOnlineDeliver(jobInfoBean, geekResp, isNetPost);
            }
        }).setLiveId(mViewModel.dataCenter.liveRecordId).setEncJobId(jobInfoBean.encryptJobId).show();

        if (mViewModel.dialogManager != null) {
            mViewModel.dialogManager.dismissJobBottomSheetDialog();
        }
    }

    private boolean isContactType(String type) {
        return TextUtils.equals(type, LiveDeliverTypeChooseDialog.TYPE_PHONE) 
                || TextUtils.equals(type, LiveDeliverTypeChooseDialog.TYPE_WECHAT);
    }

    private void handleContactTypeDeliver(@NonNull JobInfoBean jobInfoBean, String type) {
        boolean isPhone = TextUtils.equals(LiveDeliverTypeChooseDialog.TYPE_PHONE, type);
        String weiXin = UserManager.getWeiXin();
        
        if (!isPhone && TextUtils.isEmpty(weiXin) && ActivityUtils.isValid(activity)) {
            new ChatWechatDialog(activity, new ChatWechatDialog.IWechatClickListener() {
                @Override
                public void setWechat(String input) {
                    mViewModel.dataCenter.sendWechat(jobInfoBean);
                    LiveAnalysisUtil.reportLiveNotWeChatClick(jobInfoBean.encryptJobId, mViewModel.dataCenter.liveRecordId);
                }
            }).show();
            LiveAnalysisUtil.reportLiveNotWeChatShow(jobInfoBean.encryptJobId, mViewModel.dataCenter.liveRecordId);
        } else {
            getExchangeDialog(jobInfoBean, type, activity, isPhone).show();
        }
    }

    private void handleOnlineDeliver(@NonNull JobInfoBean jobInfoBean, @NonNull GeekRecruitDetailResponse geekResp, boolean isNetPost) {
        if (isNetPost) {
            handleNetPost(jobInfoBean);
        } else {
            handleNormalDeliver(jobInfoBean, geekResp);
        }
    }

    private void handleNetPost(@NonNull JobInfoBean jobInfoBean) {
        if (mViewModel.dialogManager != null) {
            LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfoBean, "立即网申", "2");
            mViewModel.dialogManager.showAtsPostDialog(jobInfoBean.applyOnlineUrl);
        }
    }

    private void handleNormalDeliver(@NonNull JobInfoBean jobInfoBean, @NonNull GeekRecruitDetailResponse geekResp) {
        if (geekResp.liveRoomType == CampusConstant.LiveRoomType.LIVE_ROOM_TYPE_TRY) {
            if (mViewModel.dialogManager != null) {
                mViewModel.dialogManager.showTestLiveDialogTips();
            }
        } else {
            mViewModel.dataCenter.checkBlueAndDeliver(mViewModel.dialogManager, jobInfoBean, true);
        }
        LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfoBean, "投简历", "2");
    }

    private void handleOfflineDeliver(@NonNull JobInfoBean jobInfoBean, @NonNull GeekRecruitDetailResponse geekResp, boolean isNetPost) {
        if (isNetPost) {
            handleNetPost(jobInfoBean);
        } else {
            handleNormalDeliver(jobInfoBean, geekResp);
        }
    }

    @NonNull
    private ExchangeDialog getExchangeDialog(JobInfoBean jobInfo, String type, Context context, boolean isPhone) {
        ExchangeDialog exchangeWeChatSecurityDialog = new ExchangeDialog();
        exchangeWeChatSecurityDialog.setContext(context);
        exchangeWeChatSecurityDialog.setDefaultTitle(isPhone ? "将手机号发送给对方" : "将微信号发送给对方");
        exchangeWeChatSecurityDialog.setDefaultText(isPhone ? "Boss可以通过拨打手机直接联系您" : "Boss可以通过微信直接联系您");
        exchangeWeChatSecurityDialog.setDefaultHeadTitle(isPhone ? ViewCommon.keepPhoneSecret(UserManager.getPhone()) : UserManager.getWeiXin());
        exchangeWeChatSecurityDialog.setType(type);
        exchangeWeChatSecurityDialog.setEncJobId(jobInfo.encryptJobId);
        exchangeWeChatSecurityDialog.setEncLiveId(mViewModel.dataCenter.liveRecordId);
        exchangeWeChatSecurityDialog.setCallBack(mulResumeWaring -> {
            if (isPhone) {
                mViewModel.dataCenter.sendPhone(jobInfo);
            } else {
                mViewModel.dataCenter.sendWechat(jobInfo);
            }
        });
        return exchangeWeChatSecurityDialog;
    }

    /**
     * 渲染认证资质
     */
    private void renderCertQualification(GetJobDetailResponse jobDetailResponse) {
        if (!LList.isEmpty(jobDetailResponse.proxyCertMaterials)) {
            List<String> certTags = new ArrayList<>();
            for (ServerProxyCertBean serverProxyCertBean : jobDetailResponse.proxyCertMaterials) {
                certTags.add(serverProxyCertBean.certName);
            }
            StringTagAdapter adapter = new StringTagAdapter(activity, certTags);
            view.getFlowLayout().setAdapter(adapter);
            view.getViewJobProxyCertCtb().setVisibility(View.VISIBLE);
        } else {
            view.getViewJobProxyCertCtb().setVisibility(View.GONE);
        }
    }

    /**
     * 渲染海螺优选条
     */
    private void renderBlueStar(@NonNull GetJobDetailResponse jobDetailResponse, GetJobQueryBannerResponse queryBanner) {
        if (null == queryBanner) return;
        ServerJDBannerInfoBean info = queryBanner.getCommonBanner();
        if (null != info && jobDetailResponse.pageType == 2) {
            view.getViewJobBlueStarInfo().setVisibility(View.VISIBLE);
            view.getTvTitleBlueStar().setText(info.title);
            view.getTvSubtitleBlueStar().setText("| " + info.content);
            ServerCommonIconBean icon = info.iconConfig;
            if (icon != null) {
                int iconHeight = ZPUIDisplayHelper.dp2px(activity, 40);
                int iconWidth = (int) (iconHeight * (float) icon.width / (float) icon.height);
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) view.getSdvIconBlueStar().getLayoutParams();
                params.width = iconWidth;
                params.height = iconHeight;
                view.getSdvIconBlueStar().setLayoutParams(params);
                view.getSdvIconBlueStar().setImageURI(icon.url);
            }

            if (!TextUtils.isEmpty(info.background)) {
                view.getSdvBgBlueStar().setImageURI(info.background);
            }
            view.getViewJobBlueStarInfo().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    new ZPManager(activity, info.url).handler();
                }
            });

            if (!TextUtils.isEmpty(info.url)) {
                view.getIvArrowRightBlueStar().setVisibility(View.VISIBLE);
            } else {
                view.getIvArrowRightBlueStar().setVisibility(View.GONE);
            }
        } else {
            view.getViewJobBlueStarInfo().setVisibility(View.GONE);
        }
    }

    /**
     * 当状态是已投递时，刷新按钮
     **/
    public void refreshButtonWhenDelivered() {
        view.getBtnAction().setText("继续沟通");
        view.getBtnAction().setEnabled(true);
    }

    /**
     * 处理「收藏」按钮的点击事件
     *
     * @param jobStatus
     * @param jobInfoBean
     */
    private void handleClickCollect(@NonNull GeekGetJobStatusResponse jobStatus, @Nullable JobInfoBean jobInfoBean) {
        if (jobInfoBean == null) return;
        if (jobStatus.isInterested()) {
            mViewModel.dataCenter.unInterest(jobInfoBean, new ICommonCallback() {//取消收藏
                @Override
                public void onSuccess() {
                    jobStatus.interestStatus = 1;
                    ToastUtils.showText("取消收藏");
                    view.getTvCollect().setText("收藏");
                    view.getTvCollect().setCompoundDrawablesWithIntrinsicBounds(0, R.mipmap.live_ic_job_detail_collect, 0, 0);
                }
            });
        } else {
            jobInterest(jobInfoBean, jobStatus);//执行收藏职位
        }
    }


    private void handleClickNetPost(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus, @NonNull GeekRecruitDetailResponse geekResp) {
        if (UserManager.isBossRole()) { /*B身份只展示Toast*/
            ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
            return;
        }
        handleDeliver(jobInfoBean, jobStatus, geekResp, true);
    }

    private void handleClickChat(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus, @NonNull GeekRecruitDetailResponse geekResp) {
        //继续沟通 跳转聊天页
        LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfoBean, "继续沟通", "1");
        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setJobId(jobInfoBean.id);
        singleChatParam.setFriendId(jobInfoBean.bossId);
        singleChatParam.setSecurityId(jobInfoBean.securityId);
        singleChatParam.setChangePositionDesc(jobInfoBean.name);
        singleChatParam.setFromDZUser(false);
        SingleRouter.startChat(activity, singleChatParam);
        ReportUtil.reportNoJobId(0, "JobDetailManager", "setData");
    }

        /**
         * 处理 点击「投简历」事件
         */
    private void handleClickDeliver(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus, @NonNull GeekRecruitDetailResponse geekResp) {
        if (UserManager.isBossRole()) { /*B身份只展示Toast*/
            ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
            return;
        }
        handleDeliver(jobInfoBean, jobStatus, geekResp, false);
    }

    /**
     * 处理 点击「订阅」/「取消订阅」事件
     *
     * @param jobInfoBean
     * @param jobStatus
     */
    private void handleClickSubcribe(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus) {
        if (jobStatus.isSubscribed()) {
            mViewModel.dataCenter.jobUnSubscribe(jobInfoBean, new ICommonCallback() { // 取消订阅
                @Override
                public void onSuccess() {
                    jobStatus.subscribeStatus = 1;
                    ToastUtils.showText("取消订阅");
                    view.getBtnAction().setText("订阅");
                }
            });
        } else {
            jobSubscribe(jobInfoBean, jobStatus);//订阅职位
        }
    }

    /**
     * 收藏职位
     */
    private void jobInterest(JobInfoBean jobInfoBean, GeekGetJobStatusResponse jobStatus) {
        mViewModel.dataCenter.interest(jobInfoBean, new ICommonCallback() {
            @Override
            public void onSuccess() {
                jobStatus.interestStatus = 2;
                view.getTvCollect().setText("已收藏");
                view.getTvCollect().setCompoundDrawablesWithIntrinsicBounds(0, R.mipmap.live_ic_job_detail_collected, 0, 0);

                if (!mViewModel.dataCenter.detailResponse.subscribed) {//如果未订阅，则执行订阅
                    mViewModel.dataCenter.subscribe(false);
                }

                mViewModel.dataCenter.getResumeList(new ICommonCallback() { // 先检查用户是否有附件简历
                    @Override
                    public void onSuccess() {
                        mViewModel.onCollected(activity, jobInfoBean);
                    }
                });
            }
        });
    }

    /**
     * 订阅职位
     *
     * @param jobInfoBean
     * @param jobStatus
     */
    private void jobSubscribe(@NonNull JobInfoBean jobInfoBean, @NonNull GeekGetJobStatusResponse jobStatus) {
        mViewModel.dataCenter.jobSubscribe(jobInfoBean, new ICommonCallback() {
            @Override
            public void onSuccess() {
                view.getBtnAction().setText("已订阅");
                jobStatus.subscribeStatus = 2;

                if (!mViewModel.dataCenter.detailResponse.subscribed) {
                    mViewModel.dataCenter.subscribe(false);
                }

                // 先检查用户是否有附件简历
                mViewModel.dataCenter.getResumeList(new ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        mViewModel.onSubscribed(activity, jobInfoBean);

                    }
                });
            }
        });
    }

    public void renderDescription(ServerSalaryWalfareInfoBean walfareInfoBean) {
        if (walfareInfoBean == null) {
            return;
        }
        view.getViewJobSalaryDetail().setVisibility(View.VISIBLE);

        if (!TextUtils.isEmpty(walfareInfoBean.moduleName)) {
            view.getMtvSalaryTitle().setText(walfareInfoBean.moduleName);
        } else {
            view.getMtvSalaryTitle().setVisibility(View.GONE);
        }

        if (LList.isNotEmpty(walfareInfoBean.pairOfItemList)) {
            showContentStyleTwo(walfareInfoBean.pairOfItemList);
        } else {
            showContentStyleOne(walfareInfoBean);
        }
    }

    private void showContentStyleOne(@NonNull ServerSalaryWalfareInfoBean walfareInfoBean) {
        view.getStyleOne().setVisibility(View.VISIBLE);
        TextView tvDesc = view.getStyleOne().findViewById(com.hpbr.bosszhipin.app.R.id.tv_description);
        KeyWordFloatLayout flContent = view.getStyleOne().findViewById(com.hpbr.bosszhipin.app.R.id.fl_content_above);
        if (LText.notEmpty(walfareInfoBean.salarySchemeDesc)) {
            tvDesc.setText(walfareInfoBean.salarySchemeDesc);
            tvDesc.setVisibility(View.VISIBLE);
        } else {
            tvDesc.setVisibility(View.GONE);
        }
        if (!LList.isEmpty(walfareInfoBean.labels)) {
            flContent.setVisibility(View.VISIBLE);
            setFlexBoxData(flContent, walfareInfoBean.labels);
        } else {
            flContent.setVisibility(View.GONE);
        }
    }

    private void setFlexBoxData(KeyWordFloatLayout flContent, List<ServerWordHighlightBean> labels) {
        List<ServerWordHighlightBean> sourceList = new ArrayList<>();
        if (!LList.isEmpty(labels)) {
            //初始化技能标签
            flContent.setVisibility(View.VISIBLE);
            for (ServerWordHighlightBean bean : labels) {
                if (bean == null || TextUtils.isEmpty(bean.content)) continue;
                sourceList.add(bean);
            }
        }

        flContent.setViewCreator(new KeyWordFloatLayout.ViewCreator<ServerWordHighlightBean>() {
            @Override
            public View createView(Context context, List<ServerWordHighlightBean> list, int position) {
                ServerWordHighlightBean bean = LList.getElement(list, position);
                if (bean == null) {
                    return null;
                }
                int layoutId = bean.highlight ? com.hpbr.bosszhipin.app.R.layout.item_job_description_ctb_tag_highlight : com.hpbr.bosszhipin.app.R.layout.item_job_description_ctb_tag;
                View view = LayoutInflater.from(context).inflate(layoutId, null);
                ZPUIRoundButton btnWord = view.findViewById(com.hpbr.bosszhipin.app.R.id.btn_word);
                btnWord.setText(bean.content);
                return view;
            }

            @Override
            public View createExpandView(Context context) {
                View view = LayoutInflater.from(context).inflate(com.hpbr.bosszhipin.app.R.layout.item_job_description_ctb_tag, null);
                ZPUIRoundButton btnWord = view.findViewById(com.hpbr.bosszhipin.app.R.id.btn_word);
                btnWord.setText("查看全部");
                btnWord.setCompoundDrawablesWithIntrinsicBounds(0, 0, com.hpbr.bosszhipin.app.R.mipmap.ic_arrow_down_black, 0);
                return view;
            }
        });
        flContent.setMaxLines(3);
        flContent.setNewData(sourceList);
    }

    private void showContentStyleTwo( @NonNull List<ServerHighlightPairContentBean> pairOfItemList) {
        view.getStyleTwo().setVisibility(View.VISIBLE);
        RecyclerView rvContent = view.getStyleTwo().findViewById(R.id.rv_content);
        SalaryWalfareAdapter adapter = new SalaryWalfareAdapter(pairOfItemList);
        rvContent.setAdapter(adapter);
    }

    private static class SalaryWalfareAdapter extends BaseQuickAdapter<ServerHighlightPairContentBean, BaseViewHolder> {
        public SalaryWalfareAdapter(@Nullable List<ServerHighlightPairContentBean> data) {
            super(R.layout.item_job_common_structure_style, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerHighlightPairContentBean item) {
            if (item == null || item.left == null || item.right == null) {
                return;
            }
            TextView tvTitle = helper.getView(com.hpbr.bosszhipin.app.R.id.tv_title);
            CollapseTextView3 tvDesc = helper.getView(com.hpbr.bosszhipin.app.R.id.tv_desc);
            if (LText.notEmpty(item.left.content)) {
                SpannableStringBuilder builder = new SpannableStringBuilder(item.left.content);
                tvTitle.setText(builder.append("： "));
            }
            if (LText.notEmpty(item.right.content)) {
                SpannableStringBuilder builder = new SpannableStringBuilder(item.right.content);
                tvDesc.setExpandText("查看全部");
                tvDesc.initWidth(ZPUIDisplayHelper.getScreenWidth(App.getAppContext()) - Scale.dip2px(App.getAppContext(), 120));
                tvDesc.setMaxLines(4);
                tvDesc.setShowCollapseFeature(false, 4);
                tvDesc.setCloseText(builder);
            }
        }
    }

}
