package com.hpbr.bosszhipin.live.authenticate;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.utils.ObserverChange;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.InterfaceLiveActivity;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.common.EnvAuthEngineHelper;
import com.hpbr.bosszhipin.live.common.SimpleNebulaListenerAdapter;
import com.hpbr.bosszhipin.live.export.Constants;
import com.hpbr.bosszhipin.live.export.LiveUrlConfig;
import com.hpbr.bosszhipin.live.export.TwlVideoManager;
import com.hpbr.bosszhipin.live.net.request.CertificationInfoResponse;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.utils.AppForegroundUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.sankuai.waimai.router.annotation.RouterPage;
import com.sdk.nebulartc.constant.NebulaRtcDef;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.GsonUtils;
import com.twl.utils.NetworkUtils;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import org.json.JSONObject;

import java.util.ArrayList;

@RouterPage(path = Constants.PATH_COMPANY_AUTH, interceptors = AuthLiveInterceptor.class)
public class CompanyAuthLiveActivity extends BaseActivity implements InterfaceLiveActivity {
    // Constant
    private static final String TAG = "CompanyAuthLiveActivity";

    private static final int TIMER_DELAY = 1000;
    private static final int HIDE_DELAY = 4000;
    public static final int CHECK_AUDIO_ENABLE_TIME = 10 * 1000;
    private static final String ACTION_LIFECYCLE_CERTIFY_MIDF_BACK = "lifecycle-certify-midf-back";
    private static final String ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_FAILSERVICE = "lifecycle-certify-envcertify-failservice";
    private static final String ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_CONNECTSTAFF = "lifecycle-certify-envcertify-connectstaff";
    private static final String ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_QUITAPP = "lifecycle-certify-envcertify-quitapp";
    private static final String ACTION_CER_MIDF_TAB = "bs_cer_midf_tab";
    private final StringBuffer durationBuilder = new StringBuffer();
    private final Handler mHandler = new Handler();
    private String mRoomId;
    /**
     * 拉流工具
     */
    private EnvAuthEngineHelper engineHelper;
    private FrameLayout mVideoContainer;
    private TextView mConnectText;
    private View mCallingPanelView;
    private CustomerViewLayout mCustomerIcon;
    private TextView tvTitle;
    private TextView tvTip1;
    private TextView tvTip2;
    private ViewGroup ll_switch_camera;
    private ImageView switchCameraView;
    private String mSource; //  0 人工视频认证 、 1 企业视频认证
    private AuthQueueFrameLayout ff_queue;
    private MutableLiveData<Boolean> acceptLiveData = new MutableLiveData<>();
    private ImageView mHangUp;
    private TextView mHangUpText;
    private int duration;
    private boolean enterVolumeCheck;
    private Runnable hideChangeVolumeRunnable = new Runnable() {
        @Override
        public void run() {
            zllChangeVolume.setVisibility(View.GONE);
            sendMsgLog(6, "用户侧当前音量较小");
        }
    };
    private final Runnable timer = new Runnable() {
        @Override
        public void run() {
            duration++;
            mConnectText.setText(durationFormatter());
            mHandler.postDelayed(timer, TIMER_DELAY);
        }
    };
    private final Runnable hide = new Runnable() {
        @Override
        public void run() {
            hideContainer();
        }
    };
    int healthCheckSpan;
    private boolean isConnect;
    private int currentAudioRoute;
    private final AppForegroundUtils.OnAppStatusListener processLifecycleObserver = new AppForegroundUtils.OnAppStatusListener() {
        @Override
        public void onFront() {
            sendMsgLog(2, "用户切换前台");
            AuthForegroundService.stopForegroundService();
        }

        @Override
        public void onBack() {
            AuthForegroundService.startForegroundService();
            sendMsgLog(1, "用户切换后台");
        }
    };

    private final BroadcastReceiver networkChangeReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                sendMsgLog(3, "用户切换网络" + NetworkUtils.getNetworkType(context));
            }
        }
    };
    private TextView tv_head_set_flag;
    private View zllChangeVolume;
    private View zrbChangeVolume;

    private void startTimer() {
        PermissionHelper.getMicroAndCameraHelper(CompanyAuthLiveActivity.this).setPermissionCallback(new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                if (yes) {
                    requestPrepareInfo();
                } else {
                    ToastUtils.showText(CompanyAuthLiveActivity.this, "使用此功能需要打开您的相机以及麦克风权限");
                    closeActivity();
                }
            }
        }).requestPermission();
    }

    private final Runnable healthCheckRunnable = new Runnable() {
        @Override
        public void run() {
            SimpleApiRequest.GET(LiveUrlConfig.URL_CERTIFICATION_USERVIDEO_HEALTHCHECK)
                    .addParam("roomId", mRoomId)
                    .addParam("sceneType", mSource)
                    .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                        @Override
                        public void onSuccess(ApiData<EmptyResponse> apiData) {
                        }
                    }).execute();
            startHealthCheck();
        }
    };


    /**
     * 在创建新活动时调用。
     */
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SecurityFrameworkManager.getInstance().setInterceptor(true);
        isBlackBackendRun = true;
        //设置全屏
        setFullScreen();
        //初始化布局
        setContentView(R.layout.live_activity_company_video_chat_view);
        //初始化View
        initView();
        //初始化Intent值
        initIntent();
        //开始记时
        startTimer();
        AppForegroundUtils.getInstance().addAppStatusListener(processLifecycleObserver);
        ReceiverUtils.registerSystem(this, new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION), networkChangeReceiver);
    }

    private void requestPrepareInfo() {
        if (!NetworkUtils.hasNetwork(CompanyAuthLiveActivity.this)) { // 网络不可用
            ToastUtils.showText(this, "网络不可用");
            closeActivity();
            return;
        }
        SimpleApiRequest.GET(LiveUrlConfig.URL_CERTIFICATION_USERVIDEO_PREPARE)
                .addParam("sourceType", "0")
                .addParam("roomId", mRoomId)
                .addParam("sceneType", mSource)
                .setRequestCallback(new SimpleCommonApiRequestCallback<CertificationInfoResponse>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onSuccess(ApiData<CertificationInfoResponse> data) {
                        ff_queue.setData(data.resp);
                        healthCheckSpan = data.resp.healthCheckSpan;
                        mRoomId = data.resp.roomId;
                        if (data.resp.signSdkInfo != null) {
                            setupLocalVideo(data.resp);
                            startHealthCheck();
                        } else {
                            ToastUtils.showText(CompanyAuthLiveActivity.this, "服务异常");
                            closeActivity();
                        }

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        closeActivity();
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        dismissProgressDialog();
                    }
                }).execute();
    }

    /**
     *
     */
    private void startHealthCheck() {
        if (healthCheckSpan <= 0) {
            mHandler.removeCallbacks(healthCheckRunnable);
            return;
        }
        mHandler.postDelayed(healthCheckRunnable, healthCheckSpan);
    }


    @Override
    protected void onResume() {
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        super.onResume();
        acceptLiveData.observe(this, acceptObserver);
        if (engineHelper != null) {
            engineHelper.onResume();
        }
    }

    @Override
    protected void onPause() {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        if (engineHelper != null) {
            engineHelper.onPause();
        }
        super.onPause();
        acceptLiveData.removeObserver(acceptObserver);
    }

    private void initView() {
        mHangUpText = findViewById(R.id.mHangUpText);
        zllChangeVolume = findViewById(R.id.zll_change_volume);
        zrbChangeVolume = findViewById(R.id.zrb_change_volume);
        mHangUp = findViewById(R.id.mHangUp);
        mConnectText = findViewById(R.id.mConnectText);
        mVideoContainer = findViewById(R.id.local_video_view_container);
        mCallingPanelView = findViewById(R.id.mCallingPanelView);
        mCustomerIcon = findViewById(R.id.mCustomerIcon);
        ff_queue = findViewById(R.id.ff_queue);
        tvTitle = findViewById(R.id.tv_title);
        tvTip1 = findViewById(R.id.tv_tip1);
        tvTip2 = findViewById(R.id.tv_tip2);
        ll_switch_camera = findViewById(R.id.ll_switch_camera);
        switchCameraView = findViewById(R.id.iv_switch_camera);
        tv_head_set_flag = findViewById(R.id.tv_head_set_flag);
        mHangUp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showHangUpDialog();
            }
        });
        mVideoContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (zllChangeVolume != null && zllChangeVolume.getVisibility() == View.GONE) {
                    if (mConnectText.getVisibility() == View.GONE) {
                        showContainer();
                        mHandler.removeCallbacks(hide);
                        mHandler.postDelayed(hide, HIDE_DELAY);
                    } else {
                        hideContainer();
                    }
                }
            }
        });
        ff_queue.setOnBackClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doExitDialog();
            }
        });
    }

    Observer<Boolean> acceptObserver = new ObserverChange<Boolean>() {
        @Override
        public void onChangedValue(Boolean aBoolean) {
            if (aBoolean) {
                if (engineHelper != null) {
                    engineHelper.preview();
                    engineHelper.switchRole(NebulaRtcDef.NEBULA_RTC_ROLE_CODE_ANCHOR);
                }
            }
        }
    };

    private void doExitDialog() {
        new DialogUtils.Builder(CompanyAuthLiveActivity.this)
                .setDesc("您正在排队中，退出后再次进入需要重新排队。确定要退出排队吗？")
                .setDoubleButton()
                .setPositiveAction("继续排队")
                .setNegativeAction("退出排队", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                AnalyticsFactory.create().action(ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_FAILSERVICE).param("p", isConnect ? "2" : "1").param("p2", mRoomId).build();
                                closeActivity();
                            }
                        }
                ).build()
                .show();
    }

    private void userVideoRelease(int actionType, int code, String msg) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_CERTIFICATION_USERVIDEO_NOTIFY)
                .addParam("roomId", mRoomId)
                .addParam("sceneType", mSource)
                .addParam("actionType", String.valueOf(actionType))
                .addParam("code", String.valueOf(code))
                .addParam("msg", msg)
                .addParam("sourceType", "0")
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> apiData) {

                    }
                }).execute();
    }


    private void onHangUpListener() {
        closeActivity();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (ff_queue.getVisibility() == View.VISIBLE) {
                doExitDialog();
                return false;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {

    }


    private void initIntent() {
        mRoomId = getIntent().getStringExtra("roomId");
        mSource = getIntent().getStringExtra("source");
        tvTitle.setText("人工视频环境认证");
        tvTip1.setText("·确保本人进行认证");
        tvTip2.setText("·确保已处于公司办公环境内");
        switchCameraView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (engineHelper != null && mVideoContainer.getChildCount() > 0) {
                    engineHelper.switchCamera();
                }
            }
        });
    }

    private void setFullScreen() {
        Window window = getWindow();
//        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.setStatusBarColor(Color.TRANSPARENT);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                    | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        }
    }

    @Override
    protected void onDestroy() {
        SecurityFrameworkManager.getInstance().setInterceptor(false);
        removeAllCallBack();
        leaveRoom();
        AppForegroundUtils.getInstance().removeAppStatusListener(processLifecycleObserver);
        App.get().getMainHandler().removeCallbacks(hideChangeVolumeRunnable);
        super.onDestroy();
    }

    private void removeAllCallBack() {
        mHandler.removeCallbacksAndMessages(null);
    }


    private void showContainer() {
        mConnectText.setVisibility(View.VISIBLE);
        mHangUp.setVisibility(View.VISIBLE);
        mHangUpText.setVisibility(View.VISIBLE);
        ll_switch_camera.setVisibility(View.VISIBLE);
    }

    private void hideContainer() {
        mConnectText.setVisibility(View.GONE);
        mHangUp.setVisibility(View.GONE);
        mHangUpText.setVisibility(View.GONE);
        ll_switch_camera.setVisibility(View.GONE);
    }

    private String durationFormatter() {
        //重用StringBuffer
        durationBuilder.setLength(0);
        int mHour = duration / (60 * 60);
        int second = duration % (60 * 60);
        int mMinute = second / 60;
        int mSecond = second % (60);
        //聊天大于1小时
        if (mHour > 0) {
            if (mHour < 10) {
                durationBuilder.append("0");
            }
            durationBuilder.append(mHour);
            durationBuilder.append(":");
        } else {
            durationBuilder.append("0");
            durationBuilder.append("0");
            durationBuilder.append(":");
        }

        //聊天分
        if (mMinute < 10) {
            durationBuilder.append("0");
        }
        durationBuilder.append(mMinute);
        durationBuilder.append(":");
        //聊天秒
        if (mSecond < 10) {
            durationBuilder.append("0");
        }
        durationBuilder.append(mSecond);
        return durationBuilder.toString();
    }


    // 提示挂断对话框
    private void showHangUpDialog() {
        AnalyticsFactory.create().action(ACTION_CER_MIDF_TAB).param("p", isConnect ? "2" : "1").build();
        VideoDoubleNoticeDialog dialog = new VideoDoubleNoticeDialog(CompanyAuthLiveActivity.this);
        dialog.setContent("您确定要结束视频认证吗？");
        dialog.setOnCancelListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onHangUpListener();
                removeAllCallBack();
                userVideoRelease(1, isConnect ? 2 : 1, "HangUpClick");
                AnalyticsFactory.create().action(ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_FAILSERVICE).param("p", isConnect ? "2" : "1").param("p2", mRoomId).build();
            }
        });
        dialog.show();
    }


    //region 私有方法setup LocalVideo RemoteVideo showMessage
    private void setupLocalVideo(CertificationInfoResponse sdkInfoBean) {
        TLog.info(TAG, "setupLocalVideo==== ");
        if (null == engineHelper) {
            engineHelper = new EnvAuthEngineHelper(CompanyAuthLiveActivity.this, sdkInfoBean.nebulaId, sdkInfoBean.signSdkInfo);
            engineHelper.setListener(new SimpleNebulaListenerAdapter() {

                private void statCheckAudio() {
                    mHandler.removeCallbacks(runnable);
                    mHandler.postDelayed(runnable, CHECK_AUDIO_ENABLE_TIME);
                }

                @Override
                public void onEnterRoom(String selfId, long result) {
                    super.onEnterRoom(selfId, result);
                    TLog.info(TAG, "onSelfEnterRoom====" + "userId = %s", selfId);
                    if (result < 0) {
                        showMessage("发生错误", "加载失败，请稍后重试", "好的");
                        userVideoRelease(6, (int) result, "加载失败，请稍后重试");
                    } else {
                        engineHelper.enableAudioVolumeEvaluation(300);
                        userVideoRelease(5, 1, "进入房间成功");
                    }
                }

                @Override
                public void onUserAudioAvailable(String userId, boolean available) {
                    super.onUserAudioAvailable(userId, available);
                    if (available) {
                        if (!isSelf(userId)) {
                            if (!isConnect) {
                                acceptServer();
                            }
                        }
                    }
                }

                @Override
                public void onAudioRouteChanged(int newRoute, int oldRoute) {
                    super.onAudioRouteChanged(newRoute, oldRoute);
                    currentAudioRoute = newRoute;
                    if (newRoute == NebulaRtcDef.TYPE_CODE_AUDIO_OUTPUT_DEVICE_HEADSET ||
                            newRoute == NebulaRtcDef.TYPE_CODE_AUDIO_OUTPUT_DEVICE_HEADSETBLUETOOTH) {/*耳机*/
                        tv_head_set_flag.setVisibility(View.VISIBLE);
                    } else {
                        tv_head_set_flag.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onUserVoiceVolume(ArrayList<NebulaRtcDef.NebulaRtcVolumeInfo> userVolumes, int totalVolume) {
                    super.onUserVoiceVolume(userVolumes, totalVolume);
                    for (NebulaRtcDef.NebulaRtcVolumeInfo rtcVolumeInfo : userVolumes) {
                        //当前是用户自己说话
                        if (!isSelf(rtcVolumeInfo.userId)) {
                            if (rtcVolumeInfo.volume > 20) {
                                //10s检查用户是否能听到声音
                                statCheckAudio();
                            }
                            mCustomerIcon.refreshVoiceWave(rtcVolumeInfo.volume);
                        }
                    }

                }


                @Override
                public void onNetworkQuality(NebulaRtcDef.NebulaRtcQuality quality) {
                    super.onNetworkQuality(quality);
                    if (isSelf(quality.userId) && quality.quality > NebulaRtcDef.NEBULA_RTC_NETWORK_QUALITY_POOR) {
                        ToastUtils.showText(CompanyAuthLiveActivity.this, "当前网络环境较差,无法正常视频");
                        sendMsgLog(4, "用户网络卡顿");
                    }
                }

                @Override
                public void onRemoteUserLeaveRoom(String peerId) {
                    if (!isSelf(peerId)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                closeActivity();
                            }
                        });
                        userVideoRelease(2, -1, "onPeerLeavedRoom");
                    }
                }

                @Override
                public void onTextMessage(String groupId, String senderId, int type, String message) {
                    onCustomMessage(groupId, senderId, type, message);
                }

                /**
                 * @param groupId
                 * @param senderId
                 * @param type
                 * @param args     {"commandName":"accept","personNumber":"002"}
                 */
                @Override
                public void onCustomMessage(String groupId, String senderId, int type, String args) {
                    try {
                        JSONObject jsonObject = new JSONObject(args);
                        String eventName = jsonObject.optString("eventName");
                        String commandName = jsonObject.optString("commandName");
                        String personNumber = jsonObject.optString("personNumber");
                        if ("callServiceTwo".equals(eventName)) {
                            if (LText.equal(commandName, "accept")) {
                                acceptServer();
                                //刷新链接后的VIEW
                                mCustomerIcon.refreshConnectView(personNumber);
                                statCheckAudio();
                            }
                        } else if ("toast".equals(eventName)) {
                            ToastUtils.showText(jsonObject.optString("message"));
                        } else if ("Audio".equals(eventName)) {
                            if (LText.equal(commandName, "mute")) {
                                mCustomerIcon.waitCustomer(true);
                            } else if (LText.equal(commandName, "unmute")) {
                                mCustomerIcon.waitCustomer(false);
                            }
                        } else if ("userIndexUpdate".equals(eventName)) {
                            ff_queue.prepareData(GsonUtils.fromJson(args, CertificationInfoResponse.WaitingInfoBean.class));
                        } else if ("exitQueue".equals(eventName)) {
                            if (ff_queue.getVisibility() == View.VISIBLE) {
                                closeActivity();
                            }
                        } else if ("closeActivity".equals(eventName)) {
                            closeActivity();
                            userVideoRelease(8, -1, "客服挂断用户");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onError(int errCode, String errMsg) {
                    showMessage("发生错误", errMsg, "好的");
                    userVideoRelease(6, errCode, errMsg);
                }

                @SuppressLint("BZL-PostDelay")
                @Override
                public void onAudioPlayOutVolumeChange(int maxVolume, int curVolume) {
                    if (engineHelper != null && !enterVolumeCheck) {
                        enterVolumeCheck = true;
                        String audioRoute = engineHelper.currentAudioRoute();
                        if (LText.equal(audioRoute, NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_SPEAKER)) {
                            runOnUiThread(() -> {
                                if (curVolume * 1f / maxVolume < 0.5) {
                                    zllChangeVolume.setVisibility(View.VISIBLE);
                                    hideContainer();
                                    zrbChangeVolume.setOnClickListener(new OnClickNoFastListener() {
                                        @Override
                                        public void onNoFastClick(View v) {
                                            if (engineHelper != null) {
                                                engineHelper.setCurrentAudioPlayOutVolume(6);
                                                ToastUtils.showText("音量已调整至60%");
                                            }
                                        }
                                    });
                                    App.get().getMainHandler().postDelayed(hideChangeVolumeRunnable, 2000);
                                } else {
                                    App.get().getMainHandler().removeCallbacks(hideChangeVolumeRunnable);
                                    zllChangeVolume.setVisibility(View.GONE);
                                }
                            });
                        }
                    } else {
                        if (curVolume * 1f / maxVolume > 0.5) {
                            App.get().getMainHandler().removeCallbacks(hideChangeVolumeRunnable);
                            zllChangeVolume.setVisibility(View.GONE);
                        }
                    }

                }
            });
            engineHelper.startInitSDK();
            mVideoContainer.addView(engineHelper.getLiveView(String.valueOf(AccountHelper.getUid())));
            engineHelper.enterRoom();
        }

    }

    private void acceptServer() {
        //排队UI
        if (!isConnect) {
            AnalyticsFactory.create().action(ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_CONNECTSTAFF).param("p", mRoomId).build();
        }
        ff_queue.setVisibility(View.GONE);
        acceptLiveData.setValue(true);
        isConnect = true;
        mConnectText.setText("已接通");
        mHandler.removeCallbacks(healthCheckRunnable);
        mHandler.removeCallbacks(timer);
        mHandler.removeCallbacks(hide);
        mHandler.postDelayed(timer, TIMER_DELAY);
        mHandler.postDelayed(hide, HIDE_DELAY);
        //隐藏呼叫的面板
        mCallingPanelView.setVisibility(View.GONE);
        if (engineHelper != null) {
            engineHelper.currentMaxVolume();
        }
    }

    private void showMessage(String title, String msg, String button) {
        if (!ActivityUtils.isValid(this)) {
            return;
        }
        // Show dialog
        AlertDialog alertDialog = new AlertDialog.Builder(this).create();
        alertDialog.setTitle(title);
        alertDialog.setMessage(msg);
        alertDialog.setButton(
                DialogInterface.BUTTON_POSITIVE,
                button, new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int which) {
                        return;
                    }
                });
        if (ActivityUtils.isValid(this)) {
            alertDialog.show();
        }

    }

    private void closeActivity() {
        SecurityFrameworkManager.getInstance().setInterceptor(false);
        leaveRoom();
        if (isConnect && !TextUtils.isEmpty(UserManager.getSecurityUrl())) {
            TwlVideoManager.getInstance().refreshStatus();
        }
        finish();
    }

    private void leaveRoom() {
        if (engineHelper != null) {
            engineHelper.leaveRoom();
            engineHelper.setListener(null);
            engineHelper = null;
        }
    }
    //endregion

    //检查用户听不到您的声音
    private final Runnable runnable = () -> sendMsgLog(5, "用户听不到您的声音");

    /**
     * {
     * code : 111,
     * message: "",
     * nebulaId: ""
     * }
     * code:1 message:"用户切换后台"
     * code:2 message:"用户切换前台"
     * code:3 message:"用户切换网络" + WiFi/蜂窝移动网络/未知
     * code:4 message:"用户网络卡顿"
     * code:5 message:"用户听不到您的声音"
     * code:6 message:
     * 用户当前声音较小，建议与沟通用户调高音量"
     *
     * @param code
     * @param message
     */
    public void sendMsgLog(int code, String message) {
        AnalyticsFactory.create().action(ACTION_LIFECYCLE_CERTIFY_ENVCERTIFY_QUITAPP).param("p", isConnect ? "2" : "1").param("p2", message).build();
        if (engineHelper == null || !isConnect) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("code", code);
            jsonObject.put("message", message);
            jsonObject.put("nebulaId", engineHelper.getNebulaId());
            jsonObject.put("source", "android");
            jsonObject.put("type", "app");
            engineHelper.sendGroupCustomMessage(jsonObject.toString(), null);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
