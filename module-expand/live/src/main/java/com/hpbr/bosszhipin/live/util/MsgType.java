package com.hpbr.bosszhipin.live.util;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 校招直播间消息类型
 *
 * <AUTHOR>
 */
public class MsgType {
    public static final int TYPE_USER_COMMENT = 0;// 正常的发言
    public static final int TYPE_GIFT = 1;// 送礼物
    public static final int TYPE_COME_IN = 11;// 进入房间
    public static final int TYPE_SAFETY_NOTICE = 12;// 默认安全文案、系统消息
    public static final int TYPE_FINISH_LIVE = 13;// 关闭直播间消息
    public static final int TYPE_ANNOUNCEMENT = 14;// 公告更新
    public static final int TYPE_PPT = 16;// ppt事件,pptUrl为空为关闭
    public static final int TYPE_DELETE_MSG = 17;// 删除消息事件
    public static final int TYPE_RESUME = 20;// 收到简历
    public static final int TYPE_CONTROL_TOP = 22;// 置顶/取消 广播
    public static final int TYPE_ROOM_CONTROL = 24;// 直播间控制消息，主播的消息
    public static final int TYPE_LUCKY_DRAW_START = 26;// 开始抽奖
    public static final int TYPE_LUCKY_DRAW_NUM = 27;// 抽奖人数
    public static final int TYPE_LUCKY_DRAW_MESSAGE = 28;// C端参与抽奖，发送消息信令
    public static final int TYPE_LUCKY_DRAW_LIST = 29;// C端中奖名单
    public static final int TYPE_NOTICE_ONLINE_CNT = 30;// 更新在线人数
    public static final int TYPE_LIVE_START = 31;// 直播开始
    public static final int TYPE_LIVE_END = 32;// 直播结束
    public static final int TYPE_LIVE_PAUSE = 33;// 直播暂停
    public static final int TYPE_LIVE_RESUME = 34;// 直播继续
    public static final int TYPE_GROUP_TOP = 35;// 置顶职位组，此时msgId为置顶的职位组id
    public static final int TYPE_LUCKY_DRAW_PREPARE = 40;// 抽奖准备消息v2，带抽奖准备时间，收到26信令直接开始抽奖
    public static final int TYPE_LUCKY_DRAW_DRAFT_MODIFY = 41;// 抽奖草稿变更，变更后的草稿数量，目前只有1和0表示有和没有
    /*点赞通知：1.C端点赞时，B端出现点赞动画，服务器端限制推送频率，最短间隔为300毫秒
    2.信令中的点赞数，可以用来更新客户端本地数据，但由于此信令与43号信令推送顺序不确定，建议与本地数据取最大值
    3.此信令只推送给B端*/
    public static final int TYPE_CLICK_APPLAUD = 42;
    /*更新点赞数
    * 1.服务器每5秒钟推送一次
     2.目前给所有端推送，C端根据产品需求可以不处理
    */
    public static final int TYPE_UPDATE_CLICK_APPLAUD_NUM = 43;
    public static final int TYPE_ANCHOR_NOTICE = 44;// 系统提示，B端使用
    /**
     * 与12号信令保持一致，906新增
     * 仅C端处理的系统消息信令，为了解决V组无法实现只推送给所有C端而增加
     */
    public static final int TYPE_SYSTEM_NOTICE = 46;
    /*双聊达成提示信息：BOSS回复了xxx的投递*/
    public static final int TYPE_BOSS_REPLY = 47;
    public static final int TYPE_BOSS_INVITE_RESUME = 48;
    /*BOSS：xxx求讲解职位名*/
    public static final int TYPE_BOSS_REQUEST_EXPLAIN = 49;
    // 外部授权收回，只会推送给需要收回外部权限的用户，收到此信令直接踢出直播间
    public static final int TYPE_PERMISSION_ROLLBACK = 50;
    // 职位浏览数量和简历数量更新，代播boss更新浏览数量、简历数量
    public static final int TYPE_JOB_LIST_UPDATE = 51;
    // 首次获取「心动值」（C在一场直播中首次获得心动值）
    public static final int TYPE_INTENTION_SCORE = 52;
    // 关注品牌弹幕
    public static final int TYPE_BRAND_SUBSCRIBE = 55;
    // 打开/关闭打点开关
    public static final int TYPE_MARK_EXPLAIN_SWITCH = 56;
    // 打点弹窗广播，其他端弹了首次确认弹窗，告知我
    public static final int TYPE_MARK_EXPLAIN_DIALOG = 57;
    // 打点广播牛人信令，收到信令后在对应的职位/职位组或章节上打上看讲解标记
    public static final int TYPE_MARK_EXPLAIN_BROADCAST = 58;
    /*高级直播间求讲解职位组*/
    public static final int TYPE_BOSS_JOB_GROUP_REQUEST_EXPLAIN = 59;
    /*直播间禁言状态变更通知*/
    public static final int TYPE_COURSE_SHUT_UP = 61;
    /*参与抽奖信令通知*/
    public static final int TYPE_LUCKY_DRAW_PARTICIPATE = 62;
    /*课程纪要信息通知*/
    public static final int TYPE_COURSE_SUMMARY = 63;
    /*导播台置顶通知*/
    public static final int TYPE_LIVE_GUIDE_TOP = 64;
    // 互动组件信息变更（C端）
    public static final int TYPE_INTERACT_COMPONENT_C = 66;
    // 互动组件信息变更（B端）
    public static final int TYPE_INTERACT_COMPONENT_B = 67;
    // 求讲解录制服务端主动结束
    public static final int TYPE_BOSS_EXPLAIN_FINISH = 68;
    // 节目单章节标记讲解/取消讲解
    public static final int TYPE_CHAPTER_EXPLAIN = 70;
    // 主播处理连麦信息
    public static final int TYPE_BOSS_LIVE_CALL_CONNECT = 75;
    //@since1008 投票开始(投票开始时告诉各端)
    public static final int TYPE_VOTE_BEGIN = 77;
    //@since 1008 投票数据同步(最新投票数据同步各端)
    public static final int TYPE_VOTE_DATA_UPDATE = 78;
    //@since 1008 投票结束(投票结束告诉各端)
    public static final int TYPE_VOTE_END = 79;
    // 1009.813 讲解录制生成广播
    public static final int TYPE_EXPLAIN_CREATED = 80;
    // 1015新增 直播中流量购买
    public static final int TYPE_FLOW_PURCHASE = 82;
    // 1016新增 字幕状态变化
    public static final int TYPE_SUBTITLE_STATUS = 83;
    // 1016新增 代播企业火热标记
    public static final int TYPE_HOT_COMPANY = 84;
    // 1120新增 PK投票
    public static final int TYPE_PK_VOTE = 86;
    //1209.711 通知主播开始录制讲解
    public static final int TYPE_NOTIFY_B_START_EXPLAIN = 87;
    //1209.711  通知主播结束录制讲解 「主播刷职位列表」
    public static final int TYPE_NOTIFY_B_END_EXPLAIN = 88;

    //1209.711  真实开始录制讲解 「主播 管理员 C 都用」
    public static final int TYPE_NOTIFY_REAL_START_EXPLAIN = 89;
    //1209.711  真实结束录制讲解 「主播 管理员 C 都用」
    public static final int TYPE_NOTIFY_REAL_END_EXPLAIN = 90;
    //1209.711 b端 推职位卡状态更新
    public static final int TYPE_B_UPDATE_RECOMMENDED_JOB = 91;
    //1226.711 b端 直播间加热
    public static final int TYPE_B_UPDATE_NOTICE_HEAT = 92;
    //1226.711 b端 个人直播间主播交换简历或手机
    public static final int TYPE_B_UPDATE_EXCHANGE_STATUS = 93;
    @IntDef({
            TYPE_USER_COMMENT, TYPE_GIFT, TYPE_COME_IN, TYPE_SAFETY_NOTICE,
            TYPE_FINISH_LIVE, TYPE_ANNOUNCEMENT, TYPE_PPT, TYPE_DELETE_MSG,
            TYPE_RESUME, TYPE_CONTROL_TOP, TYPE_ROOM_CONTROL, TYPE_LUCKY_DRAW_START,
            TYPE_LUCKY_DRAW_NUM, TYPE_LIVE_PAUSE, TYPE_LIVE_RESUME, TYPE_NOTICE_ONLINE_CNT,
            TYPE_LUCKY_DRAW_MESSAGE, TYPE_LUCKY_DRAW_LIST, TYPE_LIVE_START, TYPE_LIVE_END,
            TYPE_GROUP_TOP, TYPE_INTERACT_COMPONENT_C, TYPE_LUCKY_DRAW_PREPARE, TYPE_LUCKY_DRAW_DRAFT_MODIFY,
            TYPE_CLICK_APPLAUD, TYPE_UPDATE_CLICK_APPLAUD_NUM, TYPE_ANCHOR_NOTICE, TYPE_SYSTEM_NOTICE,
            TYPE_BOSS_REPLY, TYPE_BOSS_INVITE_RESUME, TYPE_BOSS_REQUEST_EXPLAIN, TYPE_PERMISSION_ROLLBACK,
            TYPE_JOB_LIST_UPDATE, TYPE_BRAND_SUBSCRIBE, TYPE_MARK_EXPLAIN_SWITCH, TYPE_MARK_EXPLAIN_DIALOG,
            TYPE_MARK_EXPLAIN_BROADCAST, TYPE_BOSS_JOB_GROUP_REQUEST_EXPLAIN, TYPE_COURSE_SHUT_UP, TYPE_LUCKY_DRAW_PARTICIPATE,
            TYPE_COURSE_SUMMARY, TYPE_INTERACT_COMPONENT_B, TYPE_BOSS_EXPLAIN_FINISH, TYPE_CHAPTER_EXPLAIN,
            TYPE_BOSS_LIVE_CALL_CONNECT, TYPE_VOTE_BEGIN, TYPE_VOTE_DATA_UPDATE, TYPE_VOTE_END,
            TYPE_EXPLAIN_CREATED, TYPE_FLOW_PURCHASE, TYPE_SUBTITLE_STATUS, TYPE_HOT_COMPANY,
            TYPE_PK_VOTE, TYPE_B_UPDATE_RECOMMENDED_JOB, TYPE_NOTIFY_REAL_START_EXPLAIN, TYPE_NOTIFY_REAL_END_EXPLAIN,
            TYPE_NOTIFY_B_END_EXPLAIN, TYPE_NOTIFY_B_START_EXPLAIN, TYPE_B_UPDATE_NOTICE_HEAT, TYPE_B_UPDATE_EXCHANGE_STATUS
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface State {
    }
}
