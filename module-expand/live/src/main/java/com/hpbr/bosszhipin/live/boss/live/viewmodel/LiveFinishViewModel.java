package com.hpbr.bosszhipin.live.boss.live.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;

import com.bszp.kernel.utils.SingleLiveEvent;
import com.hpbr.bosszhipin.live.boss.live.helper.BossLiveDetailBatchRequestHelper;
import com.hpbr.bosszhipin.live.export.LiveUrlConfig;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryJobBean;
import com.hpbr.bosszhipin.live.net.bean.LiveHighlightItemBean;
import com.hpbr.bosszhipin.live.net.request.BossEndOverviewRequest;
import com.hpbr.bosszhipin.live.net.request.BossExperienceCreateAndGetLiveDetailRequest;
import com.hpbr.bosszhipin.live.net.request.BossExplainForEndRequest;
import com.hpbr.bosszhipin.live.net.request.BossGetDeliveryGeekListOnlineRequest;
import com.hpbr.bosszhipin.live.net.request.BossGetDeliveryGeekListRequest;
import com.hpbr.bosszhipin.live.net.request.BossIntentionAwardListRequest;
import com.hpbr.bosszhipin.live.net.request.BossRecoverLiveRequest;
import com.hpbr.bosszhipin.live.net.request.IntentionRankListRequest;
import com.hpbr.bosszhipin.live.net.response.BossEndOverviewResponse;
import com.hpbr.bosszhipin.live.net.response.BossExplainForEndResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListOnlineResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListResponse;
import com.hpbr.bosszhipin.live.net.response.BossHighlightBtnInfoResponse;
import com.hpbr.bosszhipin.live.net.response.BossHighlightListResponse;
import com.hpbr.bosszhipin.live.net.response.BossIntentionAwardListResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveDetailForSchoolBatchResponse;
import com.hpbr.bosszhipin.live.net.response.BossRecruitDetailResponse;
import com.hpbr.bosszhipin.live.net.response.EmptyResponse;
import com.hpbr.bosszhipin.live.net.response.IntentionRankListResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2021/6/5
 */
public class LiveFinishViewModel extends BaseShareViewModel {

    public MutableLiveData<BossLiveDetailForSchoolBatchResponse> detailForSchoolLiveData = new MutableLiveData<>();
    // 创建并获取体验直播间recordId
    public MutableLiveData<String> createExperienceLiveData = new MutableLiveData<>();
    // 问答和讲解视频页面
    public MutableLiveData<Boolean> questionExplainFragmentLiveData = new MutableLiveData<>();
    // 「牛人心动榜」菜单的显示与隐藏
    public MutableLiveData<Boolean> intentionFragmentShowLiveData = new SingleLiveEvent<>();
    // 获取「牛人心动榜」数据
    public MutableLiveData<IntentionRankListResponse> getIntentionRankDataLiveData = new SingleLiveEvent<>();
    // 直播奖励列表
    public MutableLiveData<BossIntentionAwardListResponse> intentionAwardListLiveData = new MutableLiveData<>();
    // 恢复直播成功
    public MutableLiveData<Boolean> recoverLiveLiveData = new SingleLiveEvent<>();
    // 直播间投递牛人列表
    public MutableLiveData<BossGetDeliveryGeekListResponse> deliveryGeekListObserver = new MutableLiveData<>();
    // 个人直播间投递牛人列表
    public MutableLiveData<BossGetDeliveryGeekListOnlineResponse> deliveryGeekListOnlineObserver = new MutableLiveData<>();
    // 筛选页的显示与隐藏
    public MutableLiveData<Boolean> selectorFragmentShowLiveData = new SingleLiveEvent<>();
    // 筛选条选中的职位
    public MutableLiveData<BossDeliveryJobBean> selectedJobBeanLiveData = new SingleLiveEvent<>();
    // 讲解视频列表
    public MutableLiveData<BossExplainForEndResponse> explainListRespLiveData = new SingleLiveEvent<>();
    public MutableLiveData<BossEndOverviewResponse> endOverRespLiveData = new SingleLiveEvent<>();
    public MutableLiveData<Boolean> showHighlightTipsLiveData = new SingleLiveEvent<>();
    // 添加高光列表数据的LiveData
    public MutableLiveData<BossHighlightListResponse> highlightListRespLiveData = new MutableLiveData<>();
    public MutableLiveData<BossHighlightBtnInfoResponse> highlightBtnInfoLiveData = new MutableLiveData<>();
    public MutableLiveData<Boolean> highlightPublishLiveData = new SingleLiveEvent<>();
    public MutableLiveData<LiveHighlightItemBean> highlightDeleteRefreshLiveData = new SingleLiveEvent<>();

    public BossEndOverviewResponse detailResp;
    public List<BossDeliveryJobBean> jobList;// 筛选条中所有职位列表
    public List<BossDeliveryGeekBean> geekList;//投递牛人
    public List<BossDeliveryGeekBean> rejectGeekList;//不合适牛人

    public boolean isAdmin;// 是否是管理员
    public boolean isExperienceLive;// 是否是"体验直播间"
    public boolean questionExplainFragmentShowing;// 问答列表页是否正在显示
    public boolean intentionFragmentShowing;// 心动榜页是否正在显示
    public boolean selectorFragmentShowing;// 筛选页是否正在显示
    public boolean isHighlightShowing;
    public boolean isHighLiveRoom;// 是否是'高级直播间'
    public boolean isThisLiveBoss;//是否是'本场boss'
    public int userPowerType;
    public StringBuilder analysisExposeBuilder;

    /**
     * 请求直播记录详情 && 直播间投递牛人查询
     */
    public void requestRecordDetailForSchool(String recordId) {
        liveRecordId = recordId;
        new BossLiveDetailBatchRequestHelper().requestRecordDetailForSchool(recordId, new ApiRequestCallback<BossLiveDetailForSchoolBatchResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossLiveDetailForSchoolBatchResponse> data) {
                if (data == null || data.resp == null) return;
                detailResp = data.resp.bossEndOverviewResponse;
                if (!detailResp.isSuccess()) {
                    onFailed(new ErrorReason(ErrorReason.CODE_DEFAULT, ErrorReason.ERROR_SERVER_FAILED));
                    return;
                }
                isExperienceLive = detailResp.liveRoomType == 5;// 体验直播间
                isHighLiveRoom = detailResp.roomLevel == 2;//高级直播间
                liveRecordId = recordId;
                jobList = data.resp.bossGetDeliveryGeekListResponse != null ? data.resp.bossGetDeliveryGeekListResponse.jobList : null;
                geekList = data.resp.bossGetDeliveryGeekListResponse != null ? data.resp.bossGetDeliveryGeekListResponse.geekList : null;
                rejectGeekList = data.resp.bossGetDeliveryGeekListResponse != null ? data.resp.bossGetDeliveryGeekListResponse.rejectGeekList : null;
                userPowerType = null!=data.resp.bossLivePowerTypeResponse ? data.resp.bossLivePowerTypeResponse.userPowerType : -1;
                isThisLiveBoss = userPowerType == -2 || userPowerType == -3;
                detailForSchoolLiveData.postValue(data.resp);
                dotGeekListExpose(data.resp.bossGetDeliveryGeekListResponse, "");
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }

        });
    }

    public void requestEndOver(String recordId){
        /*获取直播间详情的请求*/
        BossEndOverviewRequest bossEndOverviewRequest = new BossEndOverviewRequest(new SimpleApiRequestCallback<BossEndOverviewResponse>() {
            @Override
            public void onSuccess(ApiData<BossEndOverviewResponse> data) {
                super.onSuccess(data);
                endOverRespLiveData.postValue(data.resp);
            }
        });
        bossEndOverviewRequest.encryptLiveRecordId = recordId;
        HttpExecutor.execute(bossEndOverviewRequest);
    }

    /**
     * 直播间投递牛人查询
     */
    public void getDeliveryGeekList(String encryptJobId) {
        BossGetDeliveryGeekListRequest request = new BossGetDeliveryGeekListRequest(new ApiRequestCallback<BossGetDeliveryGeekListResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossGetDeliveryGeekListResponse> data) {
                deliveryGeekListObserver.postValue(data.resp);
                dotGeekListExpose(data.resp, encryptJobId);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.encryptJobId = encryptJobId;
        HttpExecutor.execute(request);
    }

    /**
     * 个人直播间投递牛人查询
     */
    public void getDeliveryGeekOnlineList(String encryptJobId) {
        BossGetDeliveryGeekListOnlineRequest request = new BossGetDeliveryGeekListOnlineRequest(new ApiRequestCallback<BossGetDeliveryGeekListOnlineResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossGetDeliveryGeekListOnlineResponse> data) {
                if (data == null || data.resp == null) return;
                data.resp.encryptJobId = encryptJobId;
                deliveryGeekListOnlineObserver.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.encryptJobId = encryptJobId;
        HttpExecutor.execute(request);
    }

    /**
     * 创建并获取体验直播间详情
     */
    public void createExperienceRecordDetail() {
        BossExperienceCreateAndGetLiveDetailRequest request = new BossExperienceCreateAndGetLiveDetailRequest(new SimpleCommonApiRequestCallback<BossRecruitDetailResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossRecruitDetailResponse> data) {
                createExperienceLiveData.postValue(data.resp != null ? data.resp.recordId : "");
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        HttpExecutor.execute(request);
    }


    /**
     * 获取「牛人心动榜」数据
     */
    public void getIntentionRankList() {
        IntentionRankListRequest request = new IntentionRankListRequest(new SimpleCommonApiRequestCallback<IntentionRankListResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<IntentionRankListResponse> data) {
                if (data == null || data.resp == null) return;
                getIntentionRankDataLiveData.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取心动值直播奖励
     */
    public void getIntentionAwardList() {
        BossIntentionAwardListRequest request = new BossIntentionAwardListRequest(new SimpleCommonApiRequestCallback<BossIntentionAwardListResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossIntentionAwardListResponse> data) {
                intentionAwardListLiveData.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 恢复直播
     */
    public void recoverLive() {
        BossRecoverLiveRequest request = new BossRecoverLiveRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void handleInChildThread(ApiData<EmptyResponse> data) {
                super.handleInChildThread(data);
                recoverLiveLiveData.postValue(true);// 恢复成功
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.recordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    public void getExplainList() {
        BossExplainForEndRequest request = new BossExplainForEndRequest(new SimpleCommonApiRequestCallback<BossExplainForEndResponse>() {

            @Override
            public void handleInChildThread(ApiData<BossExplainForEndResponse> data) {
                super.handleInChildThread(data);
                explainListRespLiveData.postValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                explainListRespLiveData.postValue(null);
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 查看直播间投递牛人
     */
    public void viewDeliveryGeek(long geekId) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_VIEW_DELIVERY_GEEK)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("geekId", geekId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                }).execute();
    }

    /**
     * 是否显示恢复直播按钮
     */
    public boolean showRecoverBtn(@NonNull BossEndOverviewResponse resp) {
        // 普通直播间 && 可以恢复直播 && 不是管理员 && 非体验直播间 && 不是本场boss
        return resp.roomLevel == 1 && resp.canRecoverLive == 1 && !isAdmin && !isExperienceLive && !isThisLiveBoss;
    }

    /**
     * 是否是主播身份
     */
    public boolean isAnchor() {
        return userPowerType == 1;
    }

    /**
     * 获取横竖屏流方向
     */
    public int getScrnOrient() {
        return detailResp != null ? detailResp.scrnOrient : 2;
    }

    /**
     * 列表曝光埋点
     */
    private void dotGeekListExpose(BossGetDeliveryGeekListResponse response, String encryptJobId) {
        if (response == null || (LList.isEmpty(response.geekList) && LList.isEmpty(response.rejectGeekList))) return;
        List<BossDeliveryGeekBean> allGeekList = new ArrayList<>();
        if (!LList.isEmpty(response.geekList)) {
            allGeekList.addAll(response.geekList);
        }
        if (!LList.isEmpty(response.rejectGeekList)) {
            allGeekList.addAll(response.rejectGeekList);
        }

        List<String> geekIdList = new ArrayList<>();
        List<String> btnTitleList = new ArrayList<>();
        for (BossDeliveryGeekBean bean : allGeekList) {
            if (bean == null) continue;
            geekIdList.add(String.valueOf(bean.geekId));
            btnTitleList.add(String.valueOf(bean.hasResume));
        }

        LiveAnalysisUtil.dotApplyGeekListExpose(
                liveRecordId,
                StringUtil.connectTextWithChar(",", geekIdList),
                StringUtil.connectTextWithChar(",", btnTitleList),
                encryptJobId,
                getUserIdentityForAnalysis(),
                "投递牛人"
        );
    }

    public static LiveFinishViewModel instance(FragmentActivity activity) {
        return new ViewModelProvider(activity).get(LiveFinishViewModel.class);
    }

    public LiveFinishViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 获取用户身份，埋点使用
     * 身份：1-主播，2-管理员，3-本场其他boss
     */
    public int getUserIdentityForAnalysis(){
        //权限类型：-1:无权限；0:管理员；1:主播；-2:代播boss，-3:直播创建人同事
        //101:外部授权-验证码（客户端当作无权限处理）；102:外部授权-二维码（客户端当作管理员处理）
        if(userPowerType == 1) return 1;
        if(userPowerType == 0) return 2;
        if(userPowerType == -2 || userPowerType == -3) return 3;
        return -1;
    }

    /**
     * extension-campuslive-pop-end P4
     */
    public void addP4(String p4) {
        if (null == analysisExposeBuilder) {
            analysisExposeBuilder = new StringBuilder();
        }
        if (analysisExposeBuilder.length() > 0){
            analysisExposeBuilder.append("、");
            analysisExposeBuilder.append(p4);
        }else{
            analysisExposeBuilder.append(p4);
        }
    }

    public String getP4() {
        return null != analysisExposeBuilder ? analysisExposeBuilder.toString() : "";
    }

    public void cleanP4(){
        analysisExposeBuilder = null;
    }

    // 添加获取高光列表的方法
    public void getHighlightList() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_LIST)  // 请替换为实际API
                .addParam("encryptLiveId", liveRecordId)
                .setRequestCallback(new SimpleApiRequestCallback<BossHighlightListResponse>() {
                    @Override
                    public void onSuccess(ApiData<BossHighlightListResponse> data) {
                        super.onSuccess(data);
                        if (null != data && null != data.resp) {
                            highlightListRespLiveData.setValue(data.resp);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        // 返回模拟数据
                        highlightListRespLiveData.setValue(null);
                    }
                }).execute();
    }

    public void getHighlightBtnInfo() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_BTN_INFO)  // 请替换为实际API
                .addParam("encryptLiveId", liveRecordId)
                .setRequestCallback(new SimpleApiRequestCallback<BossHighlightBtnInfoResponse>() {
                    @Override
                    public void onSuccess(ApiData<BossHighlightBtnInfoResponse> data) {
                        super.onSuccess(data);
                        if (null != data && null != data.resp) {
                            highlightBtnInfoLiveData.setValue(data.resp);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        // 返回模拟数据
                        highlightBtnInfoLiveData.setValue(null);
                    }
                }).execute();
    }

    public void getHighlightPublish() {
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_PUBLISH)  // 请替换为实际API
                .addParam("encryptLiveId", liveRecordId)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                        ToastUtils.showText("发布成功");
                        highlightPublishLiveData.postValue(true);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    public void requestHighlightAutoPublish(int autoPublishGet) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_AUTO_PUBLISH)  // 请替换为实际API
                .addParam("encryptLiveId", liveRecordId)
                .addParam("autoPublishGet", autoPublishGet)
                .execute();
    }

    public void requestDeleteHighlight(LiveHighlightItemBean itemBean) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_DELETE)  // 请替换为实际API
                .addParam("encryptLiveId", liveRecordId)
                .addParam("encryptId", itemBean.encryptId)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                        highlightDeleteRefreshLiveData.postValue(itemBean);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                })
                .execute();
    }
}
