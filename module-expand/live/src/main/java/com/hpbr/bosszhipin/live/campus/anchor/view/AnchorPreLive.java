package com.hpbr.bosszhipin.live.campus.anchor.view;

import android.os.CountDownTimer;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.beauty.bean.BeautyConfigListBean;
import com.hpbr.bosszhipin.beauty.bean.BeautyMultiConfigListBean;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.live.boss.live.widget.BossControlView;
import com.hpbr.bosszhipin.live.boss.util.TimeUtil;
import com.hpbr.bosszhipin.live.campus.anchor.activity.AnchorActivity;
import com.hpbr.bosszhipin.live.campus.anchor.viewmodel.AnchorViewModel;
import com.hpbr.bosszhipin.live.campus.listener.OnFragmentDismissListener;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.net.bean.BossLiveGuideInfoBean;
import com.hpbr.bosszhipin.live.net.request.BossLiveGetBeautyConfigRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveGetPasterConfigRequest;
import com.hpbr.bosszhipin.live.net.response.BossLiveBeautyMultiConfigResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetPasterConfigResponse;
import com.hpbr.bosszhipin.live.net.response.BossRecruitDetailResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.utils.BeautyUtil;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.utils.NetUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.List;
import java.util.Locale;

/**
 * 封装直播前逻辑
 */
public class AnchorPreLive {
    private static final String TAG = "AnchorPreLive";

    private final AnchorActivity activity;
    private final AnchorView anchorView;
    private final AnchorView.PreLiveView preLiveView;
    private final AnchorView.HeaderView headerView;
    private final AnchorView.LiveView liveView;
    private final AnchorViewModel mViewModel;

    private CountDownTimer startCountDownTimer;// 距离开始直播时间倒计时
    private CountDownTimer preStartCountDownTimer;// 开播前5秒倒计时

    private boolean canStartLive;// 是否可开始直播
    private boolean canNotJumpGuide;// 是否能进入直播前引导页
    private boolean preStartCounting;// 是否正在开播前5秒倒计时


    public AnchorPreLive(AnchorActivity activity, @NonNull AnchorView anchorView,
                         @NonNull DialogManager dialogManager, @NonNull AnchorViewModel viewModel) {
        this.activity = activity;
        this.anchorView = anchorView;
        this.preLiveView = anchorView.preLiveView;
        this.headerView = anchorView.headerView;
        this.liveView = anchorView.liveView;
        mViewModel = viewModel;

        preLiveView.viewBossControl.setActivity(activity);

        preLiveView.viewBossControl.setClickListener(new BossControlView.IOnClickListener() {
            @Override
            public void clickBeauty(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);

                BossLiveGetBeautyConfigRequest bossLiveGetBeautyConfigRequest = new BossLiveGetBeautyConfigRequest(new SimpleApiRequestCallback<BossLiveBeautyMultiConfigResponse>() {
                    @Override
                    public void onSuccess(ApiData<BossLiveBeautyMultiConfigResponse> data) {
                        if (null != data && null != data.resp) {
                            BeautyUtil.instance().handleConfig(data.resp.convert(), data.resp.getMakeUpList(), new BeautyUtil.OnBeautyConfigListener() {
                                @Override
                                public void onSuccess(BeautyMultiConfigListBean beautyMultiConfigBean) {
                                    // 兜底，如果接口首次返回美颜配置失败，则不跳转
                                    if (beautyMultiConfigBean != null && !LList.isEmpty(beautyMultiConfigBean.configTypeList)) {
                                        // 隐藏直播前除画面外其他元素
                                        mViewModel.hideBeautyUILiveData.setValue(true);
                                        dialogManager.showBeautyFragment(data.resp, new OnFragmentDismissListener() {
                                            @Override
                                            public void onDismiss() {
                                                // 显示直播前除画面外其他元素
                                                mViewModel.hideBeautyUILiveData.setValue(false);
                                                //上报美颜配置
                                                mViewModel.requestLiveBeautyParamSwitch(BeautyUtil.instance().getLiveBeautyConfigListJson());
                                                showMoreToolView();
                                            }
                                        });
                                    }
                                }

                                @Override
                                public void onSelected(BeautyConfigListBean beautyMultiBean) {

                                }

                            });
                        } else {
                            ToastUtils.showText("获取美颜美妆配置信息失败");
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText("配置异常，请点击重试");
                    }

                });
                HttpExecutor.execute(bossLiveGetBeautyConfigRequest);

            }

            @Override
            public void switchOrientation(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);

                if (mViewModel.detailResponse.rollbackStatus == 1) {// 恢复的直播间
                    ToastUtils.showText("恢复的直播间仅能以之前的横竖屏状态开播");
                    return;
                }
                mViewModel.landscapeLiveData.postValue(!mViewModel.isLandscape());

                //1214.972 直播美颜改版&特效贴纸，产品说将提示气泡去掉
//                //横竖屏切换增加popWindow
//                boolean hasShowLandPop = SpManager.get().user().getBoolean("hasShowLandPop", false);
//                if(!hasShowLandPop){
//                    SpManager.get().user().edit().putBoolean("hasShowLandPop", true).apply();
//                    BossLiveLandSwitchPopup landSwitchPopup = new BossLiveLandSwitchPopup();
//                    landSwitchPopup.show(activity,preLiveView.viewBossControl.rvList,mViewModel.isLandscape());
//                }

            }

            @Override
            public void switchCamera(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText,
                        mViewModel.detailResponse != null ? mViewModel.detailResponse.hasRecoverLive : false, 0);

                if (viewModel.engineHelper != null) {
                    viewModel.engineHelper.switchCamera();
                    LiveAnalysisUtil.campusLiveTryFuncCk(viewModel.isExperienceLive, 3,
                            viewModel.engineHelper.isFrontCamera() ? 1 : -1);

                    //更新画面镜像状态
                    boolean showMirror = mViewModel.detailResponse.showMirror;
                    boolean frontCamera = viewModel.engineHelper.isFrontCamera();
                    //更新镜像状态
                    preLiveView.viewBossControl.setFrameMirrorVisible(showMirror, frontCamera);
                }
            }

            @Override
            public void liveAdmin(String buttonText) {// 直播管理
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText,
                        mViewModel.detailResponse != null ? mViewModel.detailResponse.hasRecoverLive : false, 0);
                dialogManager.showMobileAndComputerFragment(new OnFragmentDismissListener() {
                    @Override
                    public void onDismiss() {
                        showMoreToolView();
                    }
                });
            }

            @Override
            public void startLive(String buttonText) {
                //如果没弹过电话权限弹框，先弹这个，再走业务逻辑
                startNextLive(dialogManager);
            }

            @Override
            public void clickFrameMirror(boolean isFrontCamera) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, "画面镜像", mViewModel.detailResponse.hasRecoverLive, 0);
                if (isFrontCamera) {
                    mViewModel.clickFrameMirrorTool();
                }
            }

            @Override
            public void clickResolution(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);
                dialogManager.showResolutionFragment(new OnFragmentDismissListener() {
                    @Override
                    public void onDismiss() {
                        showMoreToolView();
                    }
                });
            }

            @Override
            public void clickPaster(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);
                if (!LList.isEmpty(BeautyUtil.instance().getLiveBeautyPasterConfig())) {
                    showPasterFragment(dialogManager);
                } else {
                    BossLiveGetPasterConfigRequest bossLiveGetPasterConfigRequest = new BossLiveGetPasterConfigRequest(new SimpleApiRequestCallback<BossLiveGetPasterConfigResponse>() {
                        @Override
                        public void onSuccess(ApiData<BossLiveGetPasterConfigResponse> data) {
                            if (null != data && null != data.resp) {
                                BeautyUtil.instance().setLiveBeautyPasterList(data.resp.getPasterList());
                                BeautyUtil.instance().setVirtualBgAdapterData(data.resp.backgroundList);
                                showPasterFragment(dialogManager);
                            } else {
                                ToastUtils.showText("获取贴纸配置信息失败");
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText("配置异常，请点击重试");
                        }
                    });
                    HttpExecutor.execute(bossLiveGetPasterConfigRequest);
                }
            }

            @Override
            public void clickHighlight(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);
                dialogManager.showHighlightRecordFragment(() -> showMoreToolView());
            }

            @Override
            public void clickMore(String buttonText) {
                LiveAnalysisUtil.dotCampusStartFunctionClick(viewModel.liveRecordId, buttonText, mViewModel.detailResponse.hasRecoverLive, 0);
            }

        });

        anchorView.ivLiveShare.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                LiveAnalysisUtil.dotShareClick(viewModel.liveRecordId);
                dialogManager.showLiveShareDialog(null);
            }
        });

        // 显示/隐藏直播前除画面外其他view
        mViewModel.hideBeautyUILiveData.observe(activity, hideUI -> {
            if (mViewModel.isPreLive()) {
                if (hideUI) {
                    headerView.clHeaderParent.setVisibility(View.GONE);
                    anchorView.clClose.setVisibility(View.GONE);
                    preLiveView.clCoverPreParent.setVisibility(View.GONE);
                    liveView.flTopBgGradient.setVisibility(View.GONE);
                    preLiveView.viewBossControl.setVisibility(View.GONE);
                    showFaceFrame(false);
                } else {
                    headerView.clHeaderParent.setVisibility(View.VISIBLE);
                    anchorView.clClose.setVisibility(View.VISIBLE);
                    preLiveView.clCoverPreParent.setVisibility(View.VISIBLE);
                    liveView.flTopBgGradient.setVisibility(View.VISIBLE);
                    preLiveView.viewBossControl.setVisibility(View.VISIBLE);
                    showFaceFrame(true);
                }
            }
        });
        LiveBus.with(ChannelConstants.LIVE_CAMPUS_BOSS_ANCHOR_START_LIVE, Boolean.class).observe(activity, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean start) {
                if (start) {
                    start();
                }
            }
        });
    }

    private void startNextLive(@NonNull DialogManager dialogManager) {
        boolean guideHasShown = SpManager.get().user().getBoolean(CampusConstant.KEY_SP_ANCHOR_GUIDE_SHOWN, false);
        List<BossLiveGuideInfoBean> guideInfoList = mViewModel.guideInfoResponse != null ? mViewModel.guideInfoResponse.list : null;
        // 普通直播间 && 没显示过引导页 && 离预约时间小于25分钟 && 有引导视频
        if (mViewModel.isNormalRoom() && !guideHasShown && !canNotJumpGuide && !LList.isEmpty(guideInfoList)) {
            SpManager.get().user().edit().putBoolean(CampusConstant.KEY_SP_ANCHOR_GUIDE_SHOWN, true).apply();
            // 显示视频教学弹框
            dialogManager.showGuideVideoFragment();
        } else {
            start();
        }
    }

    public void renderPreLive() {
        if (mViewModel == null || mViewModel.detailResponse == null) return;
        BossRecruitDetailResponse detailResponse = mViewModel.detailResponse;

        showPreLive(true);

        final long timeForStart = detailResponse.liveStartTimeCountDown;
        // 小于BEGIN_COUNTER_TIME，开始直播按钮才可点击
        if (timeForStart <= CampusConstant.BEGIN_COUNTER_TIME) {
            canStartLive = true;
        }

        setPreLiveTools();

        if (mViewModel.isExperienceLive) {// 体验直播间
            preLiveView.viewBossControl.setCountDownTitle("直播画面不会对外展示，请放心体验", Gravity.CENTER);
        } else if (timeForStart > 0) {
            if (startCountDownTimer == null) {
                startCountDownTimer = new CountDownTimer(timeForStart, CampusConstant.COUNTDOWN_INTERVAL) {
                    @Override
                    public void onTick(long remain) {
                        canStartLive = remain < CampusConstant.BEGIN_COUNTER_TIME;
                        canNotJumpGuide = remain >= CampusConstant.GUIDE_COUNTER_TIME;
                        String value = TimeUtil.secToTime((int) (remain / CampusConstant.COUNTDOWN_INTERVAL));

                        String title = String.format(Locale.getDefault(), "距离直播开始还剩%s", value);
                        preLiveView.viewBossControl.setCountDownTitle(title, Gravity.CENTER);
                    }

                    @Override
                    public void onFinish() {
                        preLiveView.viewBossControl.setCountDownTitle("已过预约开始时间，请尽快开始直播", Gravity.CENTER);
                    }
                };
                startCountDownTimer.start();
            }
        } else {
            preLiveView.viewBossControl.setCountDownTitle("已过预约开始时间，请尽快开始直播", Gravity.CENTER);
        }
    }

    private void setPreLiveTools() {

        //是否显示画面镜像
        boolean frontCamera = mViewModel.engineHelper.isFrontCamera();
        preLiveView.viewBossControl.setFrameMirrorVisible(mViewModel.detailResponse.showMirror, frontCamera);

        //是否显示清晰度
        preLiveView.viewBossControl.setResolutionVisible(mViewModel.detailResponse.showResolution);

        //是否显示直播高光
        preLiveView.viewBossControl.setHighlightVisible(null != mViewModel.detailResponse && mViewModel.detailResponse.isNormalLevel() && mViewModel.detailResponse.isOnlineBzpType());
    }

    public void checkShowPreLive() {
        showPreLive(mViewModel.isPreLive());
    }

    public boolean onBackPressed() {
        if (preStartCounting) {
            preStartCounting = false;
            cancelPreStartTimer();

            showPreLive(true);
            preLiveView.tvCircleCountdown.setVisibility(View.GONE);
            anchorView.clClose.setVisibility(View.VISIBLE);
            preLiveView.clHeaderParent.setVisibility(View.VISIBLE);
            return true;
        }
        return false;
    }

    public void onBack() {
        if (preStartCounting) {// 开播前倒计时过程中进入后台，回到前台时要重新倒计时
            cancelPreStartTimer();
        }
        if (null != mViewModel.dialogManager) {
            mViewModel.dialogManager.checkPauseGuideVideoPlayer();
        }
    }

    public void onFront() {
        if (preStartCounting) {// 开播前倒计时过程中进入后台，回到前台时要重新倒计时
            showCountDown();
        }
    }

    public void onDestroy() {
        cancelPreStartTimer();
        cancelStartTimer();
        if (null != mViewModel.dialogManager) {
            mViewModel.dialogManager.checkReleaseGuideVideoPlayer();
        }
    }

    /**
     * 显示与隐藏直播前画面
     */
    public void showPreLive(boolean show) {
        if (show) {
            showFaceFrame(show);
            // 显示封面和有无抽奖
            preLiveView.sdvCover.setImageURI(mViewModel.detailResponse.livePromotionalPicture);
            preLiveView.ivLottery.setVisibility(mViewModel.detailResponse.supportLuckyDraw == 1 ? View.VISIBLE : View.GONE);

            if (null != mViewModel.detailResponse.liveTitle) {
                preLiveView.tvTitlePre.setText(mViewModel.detailResponse.liveTitle);
            }
            if (null != mViewModel.detailResponse.liveStartTimeDesc) {
                preLiveView.tvLiveTimePre.setText(mViewModel.detailResponse.liveStartTimeDesc);
            }

            // 显示分享
            if (mViewModel.detailResponse.canShare) {
                anchorView.ivLiveShare.setVisibility(View.VISIBLE);
            } else {
                anchorView.ivLiveShare.setVisibility(View.GONE);
            }
        } else {
            anchorView.ivLiveShare.setVisibility(View.GONE);
        }

        cancelPreStartTimer();
        preLiveView.showPreLiveView(show);
    }

    private void showFaceFrame(boolean isShow) {
        preLiveView.bgFaceFrame.setVisibility(isShow && !mViewModel.isLandscape() ? View.VISIBLE : View.GONE);
        preLiveView.bgFaceFrameLand.setVisibility(isShow && mViewModel.isLandscape() ? View.VISIBLE : View.GONE);
    }

    private void start() {
        TLog.info(TAG, "startLive");
        if (!NetUtil.isNetConnected()) {
            ToastUtils.showText("网络异常，请检查网络后重试");
            return;
        }

        if (mViewModel.isExperienceLive) {/*如果是体验直播间，那么直接进行开播*/
            showCountDown();
        } else {
            //非蓝领直接走这
            checkStartLive();
        }
    }

    /**
     * 开播校验
     */
    private void checkStartLive() {
        if (canStartLive) {
            DialogUtils d = new DialogUtils.Builder(activity)
                    .setDoubleButton()
                    .setTitle(String.format(Locale.getDefault(), "现在以%s模式进行直播？",
                            mViewModel.isLandscape() ? "横屏" : "竖屏"))
                    .setDesc("开播后将向观众推送画面，直播中不可切换横竖屏模式")
                    .setNegativeAction("取消")
                    .setLandscape(mViewModel.isLandscape())
                    .setPositiveAction("确定", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            showCountDown();
                        }
                    })
                    .build();
            d.show();
        } else {
            ToastUtils.showText("最早可提前15分钟开始直播，现在还太早");
        }
    }

    /**
     * 显示开始直播前的倒计时
     */
    private void showCountDown() {
        preStartCounting = true;
        showPreLive(false);

        if (preStartCountDownTimer == null) {
            preStartCountDownTimer = new CountDownTimer((CampusConstant.COUNTDOWN_TIME_SEC + 1) * 1000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    int countDownNum = (int) (millisUntilFinished / 1000);

                    if (countDownNum >= 0) {
                        preLiveView.tvCircleCountdown.setVisibility(View.VISIBLE);
                        preLiveView.tvCircleCountdown.setText(String.valueOf(countDownNum));
                        showFaceFrame(true);
                    } else {
                        preLiveView.tvCircleCountdown.setVisibility(View.GONE);
                        showFaceFrame(false);
                    }
                }

                @Override
                public void onFinish() {
                    preStartCounting = false;
                    showFaceFrame(false);
                    preLiveView.tvCircleCountdown.setVisibility(View.GONE);
                    anchorView.clClose.setVisibility(View.VISIBLE);
                    preLiveView.clHeaderParent.setVisibility(View.VISIBLE);
                    mViewModel.notifyLiveState(LiveState.PUBLISHING);

                    if (mViewModel.detailResponse.showAddFlowButton == 1) {// 购买流量入口
                        mViewModel.showFlowIconLiveData.postValue(true);
                    }
                }
            };
            preStartCountDownTimer.start();
        }
        showFaceFrame(true);
        preLiveView.tvCircleCountdown.setVisibility(View.VISIBLE);
        anchorView.clClose.setVisibility(View.GONE);
        preLiveView.clHeaderParent.setVisibility(View.GONE);
    }

    private void cancelPreStartTimer() {
        if (preStartCountDownTimer != null) {
            preStartCountDownTimer.cancel();
            preStartCountDownTimer = null;
        }
    }

    private void cancelStartTimer() {
        if (startCountDownTimer != null) {
            startCountDownTimer.cancel();
            startCountDownTimer = null;
        }
    }

    /**
     * 展示更多工具栏
     */
    private void showMoreToolView() {
        if (preLiveView != null && preLiveView.viewBossControl != null) {
            // 延迟300ms，防止某个工具栏没有彻底隐藏，就显示更多工具
            App.get().getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    preLiveView.viewBossControl.showMoreToolView();
                }
            }, 300);

        }
    }

    private void showPasterFragment(DialogManager dialogManager) {
        App.get().getMainHandler().post(new Runnable() {
            @Override
            public void run() {
                if (mViewModel != null && mViewModel.hideBeautyUILiveData != null) {
                    mViewModel.hideBeautyUILiveData.setValue(true);
                }
                dialogManager.showPasterFragment(new OnFragmentDismissListener() {
                    @Override
                    public void onDismiss() {
                        showMoreToolView();
                        if (mViewModel != null && mViewModel.hideBeautyUILiveData != null) {
                            mViewModel.hideBeautyUILiveData.setValue(false);
                        }
                    }
                });
            }
        });
    }

}
