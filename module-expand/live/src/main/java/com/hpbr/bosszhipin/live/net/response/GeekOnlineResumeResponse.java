package com.hpbr.bosszhipin.live.net.response;

import com.hpbr.bosszhipin.live.net.bean.resume.GeekBaseInfoBean;
import com.hpbr.bosszhipin.live.net.bean.resume.GeekEduExpBean;
import com.hpbr.bosszhipin.live.net.bean.resume.GeekExpectBean;
import com.hpbr.bosszhipin.live.net.bean.resume.GeekProjectExpBean;
import com.hpbr.bosszhipin.live.net.bean.resume.GeekWorkExpBean;
import com.monch.lbase.util.LList;

import net.bosszhipin.base.HttpResponse;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 【APP】牛人在线简历
 * http://api.kanzhun-inc.com/project/30/interface/api/147152
 */
public class GeekOnlineResumeResponse extends HttpResponse {
    private static final long serialVersionUID = 3775893860754683696L;
    /*@since 1001 是否已沟通过 0没有 1有*/
    public int doubleChat;
    /*@since 1001 职位名称*/
    public String jobName;
    /*@since 1001 明文的职位id*/
    public long jobId;
    /*@since 1001 securityId*/
    public String securityId;
    public String exchangeUrl;// 交换协议 微信和电话用
    public String msgId;// 交换消息id 微信和电话用
    public int exchangeState;// 0boss未处理 1已经交换 2拒绝 微信和电话用
    public GeekBaseInfoBean geekBaseInfo;
    public GeekExpectBean geekExpect;
    public List<GeekProjectExpBean> geekProjExpList;
    public List<GeekEduExpBean> geekEduExpList;
    public List<GeekWorkExpBean> geekWorkExpList;
    public int inviteState;//邀请投递状态，0未邀请 1已经邀请 2已经

    public boolean isEmpty() {
        return null == geekBaseInfo && null == geekExpect
                && LList.isEmpty(geekProjExpList)
                && LList.isEmpty(geekEduExpList)
                && LList.isEmpty(geekWorkExpList);
    }

    public static class GeekOnlineResumeChangePositionBean implements Serializable {
        private static final long serialVersionUID = 2812259534519387433L;

        public long jobId;
        public String enJobId;
        public String name;
        public String salary;

        public static List<GeekOnlineResumeChangePositionBean>  mock(){
            List<GeekOnlineResumeChangePositionBean> bossJobList = new ArrayList<>();
            GeekOnlineResumeChangePositionBean bean = new GeekOnlineResumeChangePositionBean();
            bean.jobId = System.currentTimeMillis();
            bean.name = "很好的职位";
            bean.salary = "20-30K";
            bossJobList.add(bean);
            bossJobList.add(bean);
            bossJobList.add(bean);
            return bossJobList;
        }
    }
}
