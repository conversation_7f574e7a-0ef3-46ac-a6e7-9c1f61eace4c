package com.hpbr.bosszhipin.live.campus.anchor.view;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.bean.BossLiveCallConnectItemBean;
import com.hpbr.bosszhipin.live.bean.BossLiveExplainItemBean;
import com.hpbr.bosszhipin.live.boss.live.dialog.LiveCloseDialog;
import com.hpbr.bosszhipin.live.boss.live.dialog.LivePauseCloseDialog;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.BossJobListModel;
import com.hpbr.bosszhipin.live.boss.live.widget.BossExplainCardView;
import com.hpbr.bosszhipin.live.boss.live.widget.BossPauseView;
import com.hpbr.bosszhipin.live.boss.util.TimeUtil;
import com.hpbr.bosszhipin.live.campus.anchor.activity.AnchorActivity;
import com.hpbr.bosszhipin.live.campus.anchor.adpater.AnchorRecordExplainAdapter;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorAIScriptExplainFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorAIScriptFragment;
import com.hpbr.bosszhipin.live.campus.anchor.model.FinishModel;
import com.hpbr.bosszhipin.live.campus.anchor.viewmodel.AnchorViewModel;
import com.hpbr.bosszhipin.live.campus.audience.interfaces.JobExplainType;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.net.bean.ExplainForLivingItemBean;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.response.BossRecruitDetailResponse;
import com.hpbr.bosszhipin.live.net.response.LuckyDrawDetailResponse;
import com.hpbr.bosszhipin.live.util.ICommonCallback;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import java.util.List;
import java.util.Locale;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class AnchorLive {
    private static final String TAG = "AnchorLive";
    public static final String TAG_AI_SCRIPT = "ai_script";
    public static final String TAG_AI_SCRIPT_EXPLAIN = "ai_script_explain";

    private final AnchorActivity activity;
    private final AnchorView anchorView;
    private final AnchorView.LiveView liveView;
    private final DialogManager dialogManager;
    private final AnchorViewModel mViewModel;
    private volatile boolean permissionHandler; //权限是否回调
    private long pauseRemainTime;// 暂停倒计时剩余时间，单位ms
    private String lastStrongAlertContent;// 上一次强提醒的内容，用于过滤相同提醒的埋点，防止频繁上报不断创建线程
    private AnchorRecordExplainAdapter explainAdapter;
    private Runnable endExplainHintRunnable;

    public AnchorLive(AnchorActivity activity, @NonNull AnchorView anchorView, @NonNull DialogManager dialogManager, @NonNull AnchorViewModel viewModel) {
        this.activity = activity;
        this.anchorView = anchorView;
        this.liveView = anchorView.liveView;
        this.dialogManager = dialogManager;
        mViewModel = viewModel;

        initViewListener();
        initLiveDataObserver();
    }

    private void initViewListener() {
        // 结束直播
        anchorView.ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                TLog.info(TAG, "ivClose clicked");
                LiveAnalysisUtil.campusLiveTryExitCk(mViewModel.isExperienceLive);
                onBackPressed();
            }
        });
        // 暂停
        liveView.ivControlPause.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                TLog.info(TAG, "ivControlPause clicked");
                if (mViewModel.isPreLive()) {
                    ToastUtils.showText("直播时间还未开始");
                } else {
                    showPauseConfirmDialog();
                }
                LiveAnalysisUtil.campusLiveTryFuncCk(mViewModel.isExperienceLive, 1, 99);
            }
        });
        // 暂停后继续
        liveView.viewBossPause.setClickListener(new BossPauseView.IOnClickListener() {
            @Override
            public void onLiveContinue() {
                TLog.info(TAG, "onLiveContinue clicked");
                mViewModel.continueLive();
            }
        });
        // 投递牛人列表
        liveView.btnPostCount.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (mViewModel.detailResponse != null && mViewModel.detailResponse.isNormalLevel() && mViewModel.detailResponse.isOnlineBzpType()) {
                    mViewModel.dialogManager.showGeekPostFragment();
                } else {
                    mViewModel.dialogManager.showIntentionFragment(1);
                }
                LiveAnalysisUtil.dotManageDeliverList(mViewModel.liveRecordId);
            }
        });
        // 工具
        liveView.ivControlMore.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dialogManager.showToolsFragment();
            }
        });
        // 职位列表
        liveView.btnPosition.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dialogManager.showJobListFragment();
            }
        });
        //邀请投递
        liveView.btnInvite.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dialogManager.showInviteDeliverBottomSheetDialog(AppUtil.isLandscape(activity), mViewModel.liveRecordId);
            }
        });
        // 线上权限，直播中购买流量
        liveView.sdvFlow.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dialogManager.showFlowPurchaseFragment(1);
                LiveAnalysisUtil.dotCampusLiveAddFlowCk(mViewModel.liveRecordId, 1);
            }
        });
        // 录制讲解卡片
        liveView.viewExplainCard.setActionListener(new BossExplainCardView.IOnExplainActionListener() {

            @Override
            public void endExplain(String encryptExplainId, int endType, String encryptJobId) {
                realEndExplain(encryptExplainId, endType, encryptJobId, false);
                removeAIScriptFragment(TAG_AI_SCRIPT_EXPLAIN);
            }

            @Override
            public void endExplainExpired(String encryptExplainId, int endType) {
                ExplainForLivingItemBean curExplain = mViewModel.curExplainLiveData.getValue();
                if (curExplain != null) {
                    mViewModel.endExplain(encryptExplainId, endType, msg -> {
                        if (!TextUtils.isEmpty(msg)) {
                            ToastUtils.showText(msg);
                        } else {
                            liveView.endExplainTipView.setSnapShot(mViewModel.isLandscape(), mViewModel.explainSuccessSnapShot);
                            mViewModel.explainSuccessSnapShot = null;
                            liveView.endExplainTipView.showTips(liveView.positionLayout.getX() + liveView.positionLayout.getMeasuredWidth() / 2F, liveView.positionLayout.getY() + liveView.positionLayout.getMeasuredHeight() / 2F);
                        }
                        mViewModel.curExplainLiveData.postValue(null);

                        mViewModel.requestNormalJobList(curExplain.encryptJobId, new Runnable() {
                            @Override
                            public void run() {
                                mViewModel.curExplainLiveData.postValue(null);
                            }
                        });
                        if (null != endExplainHintRunnable) {
                            Utils.removeHandlerCallbacks(endExplainHintRunnable);
                            endExplainHintRunnable = null;
                        }
                        removeAIScriptFragment(TAG_AI_SCRIPT_EXPLAIN);
                    });
                    LiveAnalysisUtil.doStopExplainAction(mViewModel.liveRecordId, curExplain.encryptJobId, "2", String.valueOf(curExplain.explainTime), "1");
                }
            }

            @Override
            public void onCounting(int seconds) {
                mViewModel.explainHeaderLiveData.postValue(seconds);
            }
        });
        //连麦挂断
        liveView.flVideoParent.getLiveCallConnectPhoneImage().setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (null != mViewModel.detailResponse)
                    LiveAnalysisUtil.dotLiveVoiceMuteSpot(mViewModel.detailResponse.recordId, mViewModel.detailResponse.callonUserId, 2, 0);
                DialogUtils d = new DialogUtils.Builder(activity).setDoubleButton().setTitle("挂断视频连线").setDesc("挂断后，此用户将会退出直播间").setLandscape(mViewModel.isLandscape()).setPositiveAction("挂断", new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        if (null != mViewModel.detailResponse)
                            LiveAnalysisUtil.dotLiveClickCallHangUpButtonSpot(mViewModel.detailResponse.recordId, mViewModel.detailResponse.callonUserId, 1);
                        mViewModel.requestKillCallConnect();
                    }
                }).setNegativeAction("取消", new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        if (null != mViewModel.detailResponse)
                            LiveAnalysisUtil.dotLiveClickCallHangUpButtonSpot(mViewModel.detailResponse.recordId, mViewModel.detailResponse.callonUserId, 0);
                    }
                }).build();
                d.show();
            }
        });
        //连麦静音/解除静音
        liveView.flVideoParent.getLiveCallConnectVolumeImage().setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                int volumeState = null != mViewModel.volumeStateLive.getValue() ? mViewModel.volumeStateLive.getValue() : 0;
                if (null != mViewModel.detailResponse)
                    LiveAnalysisUtil.dotLiveVoiceMuteSpot(mViewModel.detailResponse.recordId, mViewModel.detailResponse.callonUserId, 1, volumeState);
                int volumeNow = Math.abs(volumeState - 1);
                mViewModel.requestVolumeMuteCallConnect(volumeNow);
            }
        });
        //显示讲解话术
        liveView.ivShowAIScript.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if(mViewModel.isLandscape()){
                    ToastUtils.showText("请切换到竖屏再使用");
                    return;
                }
                showAIScriptAnimation();
            }
        });
    }

    /**
     * 结束录制
     *
     * @param encryptExplainId 录制讲解id
     * @param endType          0取消 1完成录制
     */
    public void realEndExplain(String encryptExplainId, int endType, String encryptJobId, boolean isAdmin) {
        //结束录制的时候把录制讲解的loading隐藏
        hideExplainStartLoadingView();

        ExplainForLivingItemBean curExplain = mViewModel.curExplainLiveData.getValue();
        if (curExplain != null) {
            mViewModel.endExplain(encryptExplainId, endType, msg -> {
                if (!TextUtils.isEmpty(msg)) {
                    ToastUtils.showText(msg);
                } else if (endType == 1) {
                    liveView.endExplainTipView.setSnapShot(mViewModel.isLandscape(), mViewModel.explainSuccessSnapShot);
                    mViewModel.explainSuccessSnapShot = null;
                    liveView.endExplainTipView.showTips(liveView.positionLayout.getX() + liveView.positionLayout.getMeasuredWidth() / 2F, liveView.positionLayout.getY() + liveView.positionLayout.getMeasuredHeight() / 2F);
                }
                mViewModel.requestNormalJobList(encryptJobId, new Runnable() {
                    @Override
                    public void run() {
                        mViewModel.curExplainLiveData.postValue(null);
                    }
                });
                if (null != endExplainHintRunnable) {
                    Utils.removeHandlerCallbacks(endExplainHintRunnable);
                    endExplainHintRunnable = null;
                }
            });
            LiveAnalysisUtil.doStopExplainAction(mViewModel.liveRecordId, curExplain.encryptJobId, String.valueOf(endType), String.valueOf(curExplain.explainTime), isAdmin ? "2" : "1");
        }
    }

    private void initLiveDataObserver() {
        // 点赞
        mViewModel.clickApplaudLiveData.observe(activity, clickApplaudModel -> {
            if (TimeUtil.blockShowEvent(TimeUtil.DURATION_TIME_300)) {/*在这里客户端也做了一个300ms频繁显示动画的拦截*/
                return;
            }
            mViewModel.detailResponse.applaudCount = Math.max(clickApplaudModel.applaudCount, mViewModel.detailResponse.applaudCount);
            mViewModel.headerLiveData.postValue(null != mViewModel.headerLiveData.getValue() ? mViewModel.headerLiveData.getValue() : 0);
        });
        // 抽奖状态
        mViewModel.luckyDrawLiveData.observe(activity, luckyDrawModel -> {
            LuckyDrawDetailResponse luckyDraw = luckyDrawModel != null ? luckyDrawModel.response : null;
            if (luckyDraw == null) return;
            // 1009.813 录制切片时不展示抽奖气泡
            if (mViewModel.curExplainLiveData.getValue() != null) {
                return;
            }
            if (luckyDraw.status == LiveConstants.STATUS_OVER_EMPTY) {// 轮空
                if (!luckyDrawModel.fromHttp) {// 从http接口请求回来的状态，不处理，否则一直弹轮空toast
                    ToastUtils.showText("抽奖人数太少，请重新发起本轮抽奖");
                }
                liveView.luckyDrawPopupView.hide();
            } else {
                liveView.ivControlMore.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        liveView.ivControlMore.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        int[] location = new int[2];
                        liveView.ivControlMore.getLocationInWindow(location);
                        // 更多按钮垂直方向中间位置距离右侧间距，用于定位气泡箭头
                        int menuIconVerticalCenter = ZPUIDisplayHelper.dp2px(activity, 74);
                        if (luckyDraw.status == LiveConstants.STATUS_DRAWING) {
                            liveView.luckyDrawPopupView.show(String.format(Locale.getDefault(), "抽奖已开始，%s后公布抽奖结果", TimeUtil.secToSingleTime(luckyDraw.totalLuckyDrawingTime)), menuIconVerticalCenter, 0);
                        } else if (luckyDraw.status == LiveConstants.STATUS_OVER_EXIST) {
                            if (!luckyDrawModel.fromHttp) {
                                liveView.luckyDrawPopupView.show("抽奖结果在这里查看", menuIconVerticalCenter, 0);
                            }
                        }
                    }
                });
            }
        });
        // 收到直播暂停或继续的信令
        mViewModel.suspendAndContinueLiveData.observe(activity, opItemBean -> {
            if ("suspend".equals(opItemBean.op)) {
                mViewModel.suspendLive(new ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        mViewModel.engineHelper.pauseAnchor();
                    }
                });
            } else if ("continue".equals(opItemBean.op)) {
                mViewModel.continueLive(new ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        mViewModel.engineHelper.resumeAnchor();
                    }
                });
            }
        });
        // 强提醒
        mViewModel.strongAlertLiveData.observe(activity, strongAlertBean -> {
            if (mViewModel.canShowStrongAlert() && strongAlertBean != null && !TextUtils.isEmpty(strongAlertBean.content) && !mViewModel.isPaused()) {
                liveView.btnStrongAlert.setText(strongAlertBean.content);
                liveView.btnStrongAlert.setVisibility(View.VISIBLE);

                if (!TextUtils.equals(strongAlertBean.content, lastStrongAlertContent)) {
                    lastStrongAlertContent = strongAlertBean.content;
                    LiveAnalysisUtil.dotCampusLivePopStrongBNotice(mViewModel.liveRecordId, strongAlertBean.content, strongAlertBean.priority);
                }
            } else {
                liveView.btnStrongAlert.setVisibility(View.GONE);
                lastStrongAlertContent = null;
            }
        });
        // 购买流量支付成功
        mViewModel.livePaySuccessLiveData.observe(activity, isPaySuccess -> {
            if (isPaySuccess) {
                dialogManager.dismissFlowPurchaseFragment();
                mViewModel.continueLive();// 继续直播
            }
        });
        // 当前正在讲解的请求卡片
        mViewModel.curExplainLiveData.observe(activity, curExplain -> {
            if (curExplain != null) {
                anchorView.clClose.setVisibility(View.GONE);
                liveView.sdvFlow.setVisibility(View.GONE);
                liveView.positionLayout.setVisibility(View.GONE);
                liveView.controlParentLayout.setVisibility(View.GONE);
                // 如果开始录制, 需要隐藏抽奖
                liveView.luckyDrawPopupView.hide();
                //滚动讲解卡
                liveView.rlRecordExplain.setVisibility(View.GONE);

                liveView.viewExplainCard.startExplain(curExplain);
                liveView.viewExplainCardDesc.showExplainContent(curExplain);
                LiveAnalysisUtil.dotLiveExplainToastShow(curExplain.desc, curExplain.encryptExplainId);
            } else {
                anchorView.clClose.setVisibility(View.VISIBLE);
                liveView.checkShowSdvEntrance();
                liveView.positionLayout.setVisibility(View.VISIBLE);
                liveView.controlParentLayout.setVisibility(View.VISIBLE);
                liveView.viewExplainCard.stopExplain();
                liveView.viewExplainCardDesc.showExplainContent(null);
                //滚动讲解卡
                showRecordExplainListView();
            }
            dialogManager.setRecordExplainCardShowing(curExplain != null);
            checkChangeContainerSize();
            addAIScriptFragment();
        });
        //主播处理连麦信息
        mViewModel.callConnectStateLiveData.observe(activity, bean -> {
            if (null != bean) {
                if ("app".equals(bean.handleClient)) {
                    if (bean.type == 0) {//请求连麦
                        dialogManager.showVerifyCallConnectFragment(bean);
                    } else if (bean.type == 1 || bean.type == 2) {//挂断连麦
                        closeVerifyCallConnectDialogAndKillConnect();
                        //连麦结束恢复遮挡物
                        checkShowGoldMedalLogo();
                        liveView.setSdvEntranceVisibility(mViewModel.detailResponse.showAddFlowButton == 1);
                    } else if (bean.type == 3) {//连麦成功
                        mViewModel.confirmCallConnect(bean.callonUserId);
                        //连麦隐藏遮挡物
                        liveView.setSdvEntranceVisibility(false);
                        checkShowGoldMedalLogo();
                    } else { //兜底 如果连麦弹框显示 关闭弹框并且挂麦
                        if (dialogManager.isShowingVerifyCallConnectFragment()) {
                            closeVerifyCallConnectDialogAndKillConnect();
                        }
                    }
                }
            }
        });
        // 连麦 0静音/1恢复
        mViewModel.volumeStateLive.observe(activity, volumeNow -> {
            liveView.flVideoParent.setVolumeImage(volumeNow);
        });
        mViewModel.showFlowIconLiveData.observe(activity, show -> {
            if (show && liveView.sdvFlow.getVisibility() != View.VISIBLE) {
                LiveAnalysisUtil.dotCampusLiveAddFlowPop(mViewModel.liveRecordId, 1);
            }

            liveView.showSdvEntrance(show);

            if (show) {
                // 连麦中，如果触发显示流量包入口，则不显示
                BossLiveCallConnectItemBean callConnectItemBean = mViewModel.callConnectStateLiveData.getValue();
                if (callConnectItemBean != null && callConnectItemBean.type == 3) {
                    liveView.setSdvEntranceVisibility(false);
                }
            }
        });
        mViewModel.hideBeautyUILiveData.observe(activity, hideUI -> {
            if (mViewModel.isLiving()) {
                if (hideUI) {
                    liveView.btnPosition.setVisibility(View.GONE);
                    liveView.btnInvite.setVisibility(View.GONE);
                    liveView.clControlRoot.setVisibility(View.GONE);
                } else {
                    checkShowLive();
                }
            }
        });
        mViewModel.startShowExplainAfterLoadingLiveData.observe(activity, new Observer<JobInfoBean>() {
            @Override
            public void onChanged(JobInfoBean jobInfoBean) {
                startExplainByRemote(jobInfoBean, false);
            }
        });
        mViewModel.endExplainLiveData.observe(activity, new Observer<BossLiveExplainItemBean>() {
            @Override
            public void onChanged(BossLiveExplainItemBean jobInfoBean) {
                realEndExplain(jobInfoBean.encryptExplainId, 1, jobInfoBean.encryptJobId, true);
            }
        });
        mViewModel.positionLiveData.observe(activity, new Observer<BossJobListModel>() {
            @Override
            public void onChanged(BossJobListModel bossJobListModel) {
                if (null == bossJobListModel || bossJobListModel.detail == null) return;
                List<JobInfoBean> applyJobInfos = bossJobListModel.detail.applyJobInfos;
                if (null != explainAdapter && LList.getCount(explainAdapter.getData()) > 0) {
                    for (JobInfoBean jobA : explainAdapter.getData()) {
                        int needRefreshIndex = -1;
                        for (JobInfoBean jobR : applyJobInfos) {
                            if (jobA.encryptJobId.equals(jobR.encryptJobId)) {
                                if (jobA.explainRecordStatus != jobR.explainRecordStatus) {
                                    needRefreshIndex = explainAdapter.getData().indexOf(jobA);
                                }
                                jobA.explainRecordStatus = jobR.explainRecordStatus;
                            }
                        }
                        if (needRefreshIndex >= 0) {
                            explainAdapter.notifyItemChanged(needRefreshIndex);
                        }
                    }
                }
            }
        });
    }

    private void preExplainChangeView() {
        // 如果开始录制, 需要隐藏抽奖
        liveView.luckyDrawPopupView.hide();
    }

    public void checkShowLive() {
        if (null == mViewModel.detailResponse || mViewModel.detailResponse.liveState != LiveState.PUBLISHING && mViewModel.detailResponse.liveState != LiveState.PAUSED) {
            return;
        }

        checkShowGoldMedalLogo();
        liveView.checkShowSdvEntrance();
        if (mViewModel.isExplainCardShowing()) {
            mViewModel.curExplainLiveData.postValue(null);
        }
        checkChangeContainerSize();
        checkShowShareButton();
        checkShowAIScriptLayer();

        switch (mViewModel.detailResponse.liveState) {
            case LiveState.PUBLISHING: // 宣讲中
                liveView.viewBossPause.setVisibility(View.GONE);
                liveView.viewHalfTransparentBg.setVisibility(View.GONE);
                liveView.clControlRoot.setVisibility(View.VISIBLE);
                liveView.luckyDrawPopupView.setVisibility(View.VISIBLE);
                liveView.btnPosition.setVisibility(mViewModel.detailResponse.jobCount > 0 ? View.VISIBLE : View.GONE);
                liveView.ivPosition.setVisibility(mViewModel.isShowInviteBtn() ? View.GONE : View.VISIBLE);
                liveView.btnInvite.setVisibility(mViewModel.isShowInviteBtn() ? View.VISIBLE : View.GONE);
                liveView.btnPostCount.setVisibility(mViewModel.isNormalRoom() ? View.VISIBLE : View.GONE);
                if (mViewModel.isShowInviteBtn()) {
                    mViewModel.dialogManager.showInviteHintTipPopup(liveView.btnInvite, mViewModel.liveRecordId);
                }
                showRecordExplainListView();
                break;
            case LiveState.PAUSED: // 暂停中状态
                liveView.viewBossPause.setVisibility(View.VISIBLE);
                liveView.viewHalfTransparentBg.setVisibility(View.VISIBLE);
                liveView.clControlRoot.setVisibility(View.GONE);
                liveView.luckyDrawPopupView.setVisibility(View.GONE);
                // 如果有职位按钮，暂停时需要占位，否则互动列表会下移
                liveView.btnPosition.setVisibility(mViewModel.detailResponse.jobCount > 0 ? View.INVISIBLE : View.GONE);
                liveView.ivPosition.setVisibility(mViewModel.isShowInviteBtn() ? View.GONE : View.VISIBLE);
                liveView.btnInvite.setVisibility(View.GONE);
                liveView.btnPostCount.setVisibility(View.GONE);
                liveView.rlRecordExplain.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    private void checkShowAIScriptLayer() {
        if (null == activity || null == liveView || null == mViewModel) return;
        int visibility = liveView.ivShowAIScript.getVisibility();
        if (mViewModel.isShowAIScript() && null != mViewModel.detailResponse && mViewModel.detailResponse.isStartNowLive() && visibility == View.GONE) {
            if(!mViewModel.isLandscape()){
                int visibilityLayer = liveView.flAIScriptLayer.getVisibility();
                //如果正在显示，则不执行动画
                if(visibilityLayer == View.VISIBLE){
                    addAIScriptFragment();
                }else{
                    liveView.flAIScriptLayer.setVisibility(View.VISIBLE);
                    liveView.ivShowAIScript.setVisibility(View.GONE);
                    // 只在竖屏时添加Fragment
                    showAIScriptAnimation();
                }
            }else{
                liveView.flAIScriptLayer.setVisibility(View.GONE);
                liveView.ivShowAIScript.setVisibility(View.GONE);
            }
        } else {
            liveView.flAIScriptLayer.setVisibility(View.GONE);
            liveView.ivShowAIScript.setVisibility(visibility);
        }
    }

    private void showAIScriptAnimation() {
        if (!ActivityUtils.isValid(activity) || null == liveView) return;
        // 1. 计算按钮和弹窗的坐标差值
        int[] btnLocation = new int[2];
        liveView.ivShowAIScript.getLocationOnScreen(btnLocation);

        int[] panelLocation = new int[2];
        liveView.flAIScriptLayer.getLocationOnScreen(panelLocation);

        float deltaX = btnLocation[0] - panelLocation[0];
        float deltaY = btnLocation[1] - panelLocation[1];

        liveView.flAIScriptLayer.setPivotX(0); // 左上角
        liveView.flAIScriptLayer.setPivotY(0);
        liveView.flAIScriptLayer.setScaleX(0f);
        liveView.flAIScriptLayer.setScaleY(0f);
        liveView.flAIScriptLayer.setAlpha(0f);
        liveView.flAIScriptLayer.setTranslationX(deltaX);
        liveView.flAIScriptLayer.setTranslationY(deltaY);
        liveView.flAIScriptLayer.setVisibility(View.VISIBLE);
        liveView.ivShowAIScript.setVisibility(View.GONE);

        //post 为了防抖
        liveView.flAIScriptLayer.post(() -> {
            addAIScriptFragment();

            liveView.flAIScriptLayer.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .alpha(1f)
                    .translationX(0)
                    .translationY(0)
                    .setDuration(300)
                    .start();
        });
    }

    private void hideAIScriptAnimation() {
        if (!ActivityUtils.isValid(activity) || null == liveView) return;
        // 1. 计算按钮和弹窗的坐标差值
        int[] btnLocation = new int[2];
        liveView.ivShowAIScript.getLocationOnScreen(btnLocation);

        int[] panelLocation = new int[2];
        liveView.flAIScriptLayer.getLocationOnScreen(panelLocation);

        float deltaX = btnLocation[0] - panelLocation[0];
        float deltaY = btnLocation[1] - panelLocation[1];

        // 2. 设置 pivot
        liveView.flAIScriptLayer.setPivotX(0);
        liveView.flAIScriptLayer.setPivotY(0);

        // 3. 执行动画
        liveView.flAIScriptLayer.animate()
                .scaleX(0f)
                .scaleY(0f)
                .alpha(0f)
                .translationX(deltaX)
                .translationY(deltaY)
                .setDuration(300)
                .withStartAction(() -> {
                    if (!ActivityUtils.isValid(activity)) return;
                    liveView.ivShowAIScript.setVisibility(View.VISIBLE);
                })
                .withEndAction(() -> {
                    if (!ActivityUtils.isValid(activity)) return;
                    liveView.flAIScriptLayer.setVisibility(View.GONE);
                })
                .start();
    }

    private void addAIScriptFragment() {
        if (mViewModel.isExplainCardShowing()) {
            hideAIScriptFragment(TAG_AI_SCRIPT);
            if (activity.getSupportFragmentManager().findFragmentByTag(TAG_AI_SCRIPT_EXPLAIN) == null) {
                Bundle data = new Bundle();
                ExplainForLivingItemBean explainBean = mViewModel.curExplainLiveData.getValue();
                if (null != explainBean) {
                    data.putString("jobId", explainBean.encryptJobId);
                }
                AnchorAIScriptExplainFragment instance = AnchorAIScriptExplainFragment.getInstance(data);
                instance.setCloseListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        hideAIScriptAnimation();
                        LiveAnalysisUtil.doLiveAIBtnExpendClick(mViewModel.liveRecordId, "1", "1");
                    }
                });
                activity.getSupportFragmentManager()
                        .beginTransaction()
                        .add(R.id.fl_ai_script_layer, instance, TAG_AI_SCRIPT_EXPLAIN)
                        .commitAllowingStateLoss();
            }
            showAIScriptFragment(TAG_AI_SCRIPT_EXPLAIN);
            LiveAnalysisUtil.doLiveAIBtnExpendClick(mViewModel.liveRecordId, "0", "1");
        } else {
            hideAIScriptFragment(TAG_AI_SCRIPT_EXPLAIN);
            if (activity.getSupportFragmentManager().findFragmentByTag(TAG_AI_SCRIPT) == null) {
                AnchorAIScriptFragment instance = AnchorAIScriptFragment.getInstance(null);
                instance.setCloseListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        hideAIScriptAnimation();
                        LiveAnalysisUtil.doLiveAIBtnExpendClick(mViewModel.liveRecordId, "1", "0");
                    }
                });
                activity.getSupportFragmentManager()
                        .beginTransaction()
                        .add(R.id.fl_ai_script_layer, instance, TAG_AI_SCRIPT)
                        .commitAllowingStateLoss();
            }
            showAIScriptFragment(TAG_AI_SCRIPT);
            LiveAnalysisUtil.doLiveAIBtnExpendClick(mViewModel.liveRecordId, "0", "0");
        }
    }

    private void removeAIScriptFragment(String tag) {
        Fragment fragment = activity.getSupportFragmentManager()
                .findFragmentByTag(tag);
        if (fragment != null) {
            activity.getSupportFragmentManager()
                    .beginTransaction()
                    .remove(fragment)
                    .commitAllowingStateLoss();
        }
    }
    private void hideAIScriptFragment(String tag) {
        Fragment fragment = activity.getSupportFragmentManager()
                .findFragmentByTag(tag);
        if (fragment != null) {
            activity.getSupportFragmentManager()
                    .beginTransaction()
                    .hide(fragment)
                    .commitAllowingStateLoss();
        }
    }

    private void showAIScriptFragment(String tag) {
        Fragment fragment = activity.getSupportFragmentManager()
                .findFragmentByTag(tag);
        if (fragment != null) {
            activity.getSupportFragmentManager()
                    .beginTransaction()
                    .show(fragment)
                    .commitAllowingStateLoss();
        }
    }

    private void checkShowGoldMedalLogo() {
        boolean isNotHasConnectCall = TextUtils.isEmpty(mViewModel.detailResponse.callonUserId);
        liveView.ivGoldMedalLogo.setVisibility(mViewModel.detailResponse.isShowGoldMedal() && isNotHasConnectCall ? View.VISIBLE : View.GONE);
    }

    /**
     * 通过信令开启直播，pc或者其他管理员发起
     *
     * @param jobInfoBean 职位
     * @param isSelf      如果是自己的话，不开启延时录制弹幕
     */
    public void startExplainByRemote(JobInfoBean jobInfoBean, boolean isSelf) {
        if (null == jobInfoBean) return;
        if (!isSelf) {
            showExplainStartLoadingView(jobInfoBean);
            dialogManager.dismissAllFragments();
            final JobInfoBean curJobBean = jobInfoBean;
            if (null != endExplainHintRunnable) {
                Utils.removeHandlerCallbacks(endExplainHintRunnable);
                endExplainHintRunnable = null;
            }

            endExplainHintRunnable = new Runnable() {
                @Override
                public void run() {
                    hideExplainStartLoadingView();
                    mViewModel.startExplainWrapper(curJobBean);
                }
            };
            Utils.runOnUiThreadDelayed(endExplainHintRunnable, 2_000);
        }
    }

    /**
     * 录制讲解前的loading
     */
    private void showExplainStartLoadingView(JobInfoBean jobInfoBean) {
        if (null != liveView.explainStartLoadingView) {
            liveView.explainStartLoadingView.setVisibility(View.VISIBLE);
            liveView.explainStartLoadingView.setData(jobInfoBean);
            preExplainChangeView();
        }
    }

    private void hideExplainStartLoadingView() {
        if (null != liveView.explainStartLoadingView) {
            liveView.explainStartLoadingView.setVisibility(View.GONE);
            liveView.explainStartLoadingView.dismiss();
        }
    }

    /**
     * 显示录制讲解
     */
    private void showRecordExplainListView() {
        if (mViewModel.isNormalRoom() && null != mViewModel.detailResponse && LList.getCount(mViewModel.detailResponse.applyJobInfos) > 0 && !mViewModel.isLandscape()) {
            RecyclerView rlRecordExplain = liveView.rlRecordExplain;
            rlRecordExplain.setVisibility(View.VISIBLE);
            if (null == explainAdapter) {
                explainAdapter = new AnchorRecordExplainAdapter();
                rlRecordExplain.setAdapter(explainAdapter);
                if (LList.getCount(explainAdapter.getData()) > 0) {
                    List<JobInfoBean> applyJobInfos = mViewModel.detailResponse.applyJobInfos;
                    if (null != explainAdapter && LList.getCount(explainAdapter.getData()) > 0) {
                        for (JobInfoBean jobA : explainAdapter.getData()) {
                            int needRefreshIndex = -1;
                            for (JobInfoBean jobR : applyJobInfos) {
                                if (jobA.encryptJobId.equals(jobR.encryptJobId)) {
                                    if (jobA.explainRecordStatus != jobR.explainRecordStatus) {
                                        needRefreshIndex = explainAdapter.getData().indexOf(jobA);
                                    }
                                    jobA.explainRecordStatus = jobR.explainRecordStatus;
                                }
                            }
                            if (needRefreshIndex >= 0) {
                                explainAdapter.notifyItemChanged(needRefreshIndex);
                                if (null != liveView.rlRecordExplain.getLayoutManager()) {
                                    liveView.rlRecordExplain.getLayoutManager().scrollToPosition(needRefreshIndex);
                                }
                            }
                        }
                    }
                } else {
                    explainAdapter.setNewData(mViewModel.detailResponse.applyJobInfos);
                }
                if (null != explainAdapter) {
                    explainAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                        @Override
                        public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                            if (view.getId() == R.id.btn_post) {
                                Object item = adapter.getItem(position);
                                if (null == item) return;
                                JobInfoBean jobInfoBean = (JobInfoBean) item;
                                mViewModel.startExplainWrapper(jobInfoBean);
                                LiveAnalysisUtil.doStartExplainAction(mViewModel.liveRecordId, jobInfoBean.encryptJobId, "1", "3", jobInfoBean.explainRecordStatus);
                                //更新本地
                                jobInfoBean.explainRecordStatus = JobExplainType.TYPE_REEXPLAIN;
                                adapter.notifyItemChanged(position);
                            }
                        }
                    });
                }
            }
        } else {
            liveView.rlRecordExplain.setVisibility(View.GONE);
        }

    }

    /**
     * 关闭连麦确认弹框并且挂麦
     */
    private void closeVerifyCallConnectDialogAndKillConnect() {
        //1.若有连麦弹框关闭
        dialogManager.dismissVerifyCallConnectFragment();
        //2.若正在连麦中 移除连麦视图
        if (null != mViewModel.mainLiveData.getValue()) {
            mViewModel.mainLiveData.getValue().callonUserId = null;
            mViewModel.callConnectLiveData.setValue(mViewModel.mainLiveData.getValue());
        } else {//兜底结束连麦
            BossRecruitDetailResponse bossRecruitDetailResponse = new BossRecruitDetailResponse();
            bossRecruitDetailResponse.callonUserId = null;
            mViewModel.callConnectLiveData.setValue(bossRecruitDetailResponse);
        }
    }

    /**
     * 开始预览
     * permissionHandler 用于处理 申请权限之后 未返回结果 再次申请问题
     */
    public void startPreview() {
        if (permissionHandler) return;
        permissionHandler = true;
        PermissionHelper.getMicroAndCameraHelper(activity).setPermissionCallback(new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                permissionHandler = false;
                if (yes) {
                    if (mViewModel.engineHelper != null) {
                        mViewModel.engineHelper.startLocalPreview();
                        mViewModel.setFrameMirror();
                        mViewModel.engineHelper.checkEnableBeauty();
                    }
                } else {
                    ToastUtils.showText("请授予麦克风和摄像头权限");
                    mViewModel.finishLiveData.postValue(new FinishModel(false));
                }
            }
        }).requestPermission();
    }

    private CountDownTimer pauseCountDownTimer;

    public void startPauseCountdown() {
        TLog.info(TAG, "startPauseCountdown suspendTimeRemaining=%d", mViewModel.detailResponse.suspendTimeRemaining);
        if (mViewModel.detailResponse.suspendTimeRemaining > 0) {
            mViewModel.pauseLastMinute = false;
            if (pauseCountDownTimer == null) {
                pauseCountDownTimer = new CountDownTimer(mViewModel.detailResponse.suspendTimeRemaining, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        pauseRemainTime = millisUntilFinished;
                        if (liveView != null && liveView.viewBossPause != null) {
                            String value = TimeUtil.secToEnTime((int) (pauseRemainTime / 1000));
                            liveView.viewBossPause.setCountDownTitle(String.format("%s 后将自动结束直播", value));
                        }

                        if (pauseRemainTime <= 60 * 1000) {// 距离结束小于1分钟，不能继续直播
                            mViewModel.pauseLastMinute = true;
                        }
                    }

                    @Override
                    public void onFinish() {
                        if (liveView != null && liveView.viewBossPause != null) {
                            liveView.viewBossPause.setCountDownTitle("直播暂停已超时");
                        }
                        mViewModel.finishLiveData.postValue(new FinishModel(true));
                    }
                };
                pauseCountDownTimer.start();
            }
        }
    }

    public void stopPauseCountdown() {
        if (pauseCountDownTimer != null) {
            pauseCountDownTimer.cancel();
            pauseCountDownTimer = null;
        }
    }

    private void showPauseConfirmDialog() {
        dialogManager.showPauseConfirmDialog(mViewModel.isExperienceLive, mViewModel.isLandscape(), new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                mViewModel.suspendLive(null);
                mViewModel.pauseAnchor();
            }
        });
    }

    private void showCloseDialog() {
        dialogManager.showCloseDialog(mViewModel.isLandscape(), new LiveCloseDialog.IOnTouchListener() {
            @Override
            public void pauseBtnClicked() {
                showPauseConfirmDialog();
                LiveAnalysisUtil.campusLiveTryExitConfirm(mViewModel.isExperienceLive, 0, 4);
            }

            @Override
            public void continueBtnClicked() {
                LiveAnalysisUtil.campusLiveTryExitConfirm(mViewModel.isExperienceLive, 0, 0);
            }

            @Override
            public void slideClosed() {
                mViewModel.finishLiveData.postValue(new FinishModel(true));
                LiveAnalysisUtil.campusLiveTryExitConfirm(mViewModel.isExperienceLive, 0, 1);
            }
        });
    }

    private void showPauseCloseDialog() {
        dialogManager.showPauseCloseDialog(mViewModel.isLandscape(), new LivePauseCloseDialog.IOnTouchListener() {
            @Override
            public void goBack() {
                // 显示暂停浮层
                liveView.viewBossPause.setVisibility(View.VISIBLE);
            }

            @Override
            public void slideClosed() {
                mViewModel.finishLiveData.postValue(new FinishModel(true));
                LiveAnalysisUtil.campusLiveTryExitConfirm(mViewModel.isExperienceLive, 0, 1);
            }
        });
        // 隐藏暂停浮层
        liveView.viewBossPause.setVisibility(View.GONE);
    }


    /**
     * 连线
     *
     * @param landscape
     * @param hasConnect
     */
    public void callConnectStyle(boolean landscape, boolean hasConnect) {
        checkChangeContainerSize();
        liveView.callConnectStyle(landscape, hasConnect);
    }

    /**
     * 如果有正在录制的求讲解，则不能点击其他任何区域；未录制的情况下，隐藏卡片。优先级最高
     */
    public boolean checkExplainCardOnBack() {
        if (liveView.viewExplainCard.isRecording()) {
            return true;
        }
        if (mViewModel.isExplainCardShowing()) {
            mViewModel.curExplainLiveData.postValue(null);
            return true;
        }
        return false;
    }

    public boolean onBackPressed() {
        if (mViewModel.isLiving()) {
            if (mViewModel.isPaused()) {
                if (dialogManager.pauseCloseDialogShowing()) {
                    dialogManager.dismissPauseCloseDialog();
                } else {
                    showPauseCloseDialog();
                }
            } else {
                if (dialogManager.closeDialogShowing()) {
                    dialogManager.dismissCloseDialog();
                } else {
                    showCloseDialog();
                }
            }
        } else {
            mViewModel.finishLiveData.postValue(new FinishModel(false));
        }
        return true;
    }

    /**
     * 释放资源
     */
    public void onDestroy() {
        stopPauseCountdown();
    }

    public void checkEndExplain() {
        ExplainForLivingItemBean explainItemBean = mViewModel.curExplainLiveData.getValue();
        if (explainItemBean != null && explainItemBean.explainTime > 0) {
            mViewModel.endExplain(explainItemBean.encryptExplainId, 0, msg -> {
                mViewModel.requestNormalJobList(explainItemBean.encryptJobId, new Runnable() {
                    @Override
                    public void run() {
                        mViewModel.curExplainLiveData.postValue(null);
                    }
                });
            });
        }
    }

    /**
     * 检查屏幕宽高比，小于16：9，画面充满；
     * 大于16：9，有圆角，横屏居中左右留黑边，竖屏底部留固定距离
     * 特殊情况！！！直播前充满
     */
    private void checkChangeContainerSize() {
        if (mViewModel.isLiving()) {
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(liveView.clRoot);

            int screenHeight = ZPUIDisplayHelper.getScreenHeight(activity);
            int screenWidth = ZPUIDisplayHelper.getScreenWidth(activity);

            // 高度和宽度比例
            float ratio = screenHeight * 1.0f / screenWidth;

            if (!mViewModel.isLandscape()) {
                if (ratio > 16.0f / 9) {
                    liveView.flVideoParent.setRadius(ZPUIDisplayHelper.dp2px(activity, 8));
                    ExplainForLivingItemBean explainItem = mViewModel.curExplainLiveData.getValue();
                    // 距离底部距离，职位讲解时距离更大
                    int marginBottom = ZPUIDisplayHelper.dp2px(activity, explainItem != null && !TextUtils.isEmpty(explainItem.encryptExplainId) ? 70 : 56);
                    constraintSet.connect(R.id.fl_video_parent, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, marginBottom);
                } else {
                    liveView.flVideoParent.setRadius(0);
                    constraintSet.connect(R.id.fl_video_parent, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0);
                }

                // 兼容竖屏直播时连麦，变横屏
                BossRecruitDetailResponse response = mViewModel.callConnectLiveData.getValue();
                boolean hasConnect = response != null && response.callonUserId != null;
                if (hasConnect) {
                    constraintSet.clear(R.id.fl_video_parent, ConstraintSet.BOTTOM);
                    constraintSet.connect(R.id.fl_video_parent, ConstraintSet.TOP, R.id.cl_header_parent, ConstraintSet.BOTTOM, ZPUIDisplayHelper.dp2px(Utils.getApp(), 20));
                    constraintSet.setDimensionRatio(R.id.fl_video_parent, "w,250:376");
                } else {
                    constraintSet.setDimensionRatio(R.id.fl_video_parent, null);
                    constraintSet.connect(R.id.fl_video_parent, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0);
                }
            } else {
                if (ratio < 16.0f / 9) {// 注意这里，横屏时获取的宽高是反的
                    liveView.flVideoParent.setRadius(ZPUIDisplayHelper.dp2px(activity, 8));
                    float realWidth = screenHeight * 16f / 9;
                    if (realWidth > screenWidth) {// 宽度比屏幕宽度大
                        constraintSet.setDimensionRatio(R.id.fl_video_parent, "h,16:9");
                    } else {
                        constraintSet.setDimensionRatio(R.id.fl_video_parent, "w,16:9");
                    }
                } else {
                    liveView.flVideoParent.setRadius(0);
                    constraintSet.constrainWidth(R.id.fl_video_parent, 0);
                }
            }
            constraintSet.applyTo(liveView.clRoot);

            //显示'邀'按钮。动态设置职位按钮宽度「显示-0，自动撑，不显示-wrap」
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) liveView.btnPosition.getLayoutParams();
            layoutParams.width = mViewModel.isShowInviteBtn() ? 0 : ViewGroup.LayoutParams.WRAP_CONTENT;
            layoutParams.weight = mViewModel.isShowInviteBtn() ? 1 : 0;
            liveView.btnPosition.setLayoutParams(layoutParams);
        }
    }

    /**
     * 检查并设置分享按钮的可见性
     */
    private void checkShowShareButton() {
        anchorView.ivLiveShare.setVisibility(mViewModel.detailResponse.canShare && !mViewModel.isFinishLive() ? View.VISIBLE : View.GONE);
    }

}
