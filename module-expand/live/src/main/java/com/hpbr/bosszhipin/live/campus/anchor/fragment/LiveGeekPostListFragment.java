package com.hpbr.bosszhipin.live.campus.anchor.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.campus.anchor.adpater.AnchorGeekPostListAdapter;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.twl.ui.ToastUtils;

import java.util.List;

import zpui.lib.ui.refreshlayout.ZPUIRefreshLayout;
import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;

/**
 * 直播招聘候选人列表
 */
public class LiveGeekPostListFragment extends BaseChildLiveFragment implements OnLoadMoreListener {
    private ZPUIRefreshLayout refreshLayout;
    private RecyclerView rvList;
    private AnchorGeekPostListAdapter adapter;
    private ZPUIStateLayoutManager layoutManager;
    @LiveConstants.GeekDeliverType
    private int deliverType = LiveConstants.GeekDeliverType.TYPE_PHONE; // 默认为电话类型
    private String deliveryId = ""; // 分页参数

    public static LiveGeekPostListFragment getInstance(@LiveConstants.GeekDeliverType int type) {
        LiveGeekPostListFragment f = new LiveGeekPostListFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.DATA_INT, type);
        f.setArguments(bundle);
        return f;
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.live_fragment_geek_post_list;
    }

    @Override
    protected void initViews(View view) {
        if (getArguments() != null) {
            deliverType = getArguments().getInt(Constants.DATA_INT, LiveConstants.GeekDeliverType.TYPE_PHONE);
        }
        refreshLayout = view.findViewById(R.id.refresh_layout);
        rvList = view.findViewById(R.id.rv_list);
        adapter = new AnchorGeekPostListAdapter(null);
        rvList.setAdapter(adapter);
        layoutManager = new ZPUIStateLayoutManager(activity, refreshLayout);
        initEventListener();
        initLiveDataListener();
    }

    @Override
    public void onResume() {
        super.onResume();
        onRefresh();
    }

    /**
     * 刷新数据，重置分页参数
     */
    public void onRefresh() {
        deliveryId = "";
        mViewModel.getGeekDeliveryListOnline(deliverType, deliveryId);
    }

    private void initEventListener() {
        refreshLayout.setEnableRefresh(false);
        refreshLayout.setOnLoadMoreListener(this);
        layoutManager.getErrorLayout().setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                onRefresh();
            }
        });
        adapter.setOnItemClickListener((adapter, view, position) -> {
            if (ClickProtectedUtil.blockClickEvent()) return;
            BossDeliveryGeekBean bean = (BossDeliveryGeekBean) adapter.getItem(position);
            if (bean == null) return;
            hideBottomSheet(true);
            App.get().getMainHandler().postDelayed(() -> mViewModel.dialogManager.showGeekResumeFragment(bean, deliverType), 200);
            LiveAnalysisUtil.reportLiveBossingDeliverListClick(mViewModel.liveRecordId, "1", "", bean.encryptGeekId, "0");
        });
        adapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (ClickProtectedUtil.blockClickEvent()) return;
            BossDeliveryGeekBean bean = (BossDeliveryGeekBean) adapter.getItem(position);
            if (bean == null) return;
            if (view.getId() == R.id.btn_change) {
                if (LText.empty(bean.exchangeUrl)) {
                    return;
                }
                new ZPManager(activity, bean.exchangeUrl).handler();
                LiveAnalysisUtil.reportLiveBossingDeliverListClick(mViewModel.liveRecordId, "1", "", bean.encryptGeekId, "1");
            }
        });
    }

    private void initLiveDataListener() {
        // 首次加载或刷新的数据监听
        mViewModel.deliveryListLiveData.observe(this, response -> {
            if (response == null) {
                layoutManager.showErrorScene();
            } else {
                calculateLastId(response.deliveryGeekList);
                renderEmpty(LList.isEmpty(response.deliveryGeekList));
                addLocalData(response.deliveryGeekList);
                adapter.setNewData(response.deliveryGeekList);
                refreshLayout.setEnableLoadMore(response.hasMore);
            }
            refreshLayout.finishRefresh();
            refreshLayout.finishLoadMore();
        });
        // 加载更多的数据监听
        mViewModel.moreDeliveryListLiveData.observe(this, response -> {
            if (response != null) {
                if (LList.isEmpty(response.deliveryGeekList)) {
                    refreshLayout.setEnableLoadMore(false);
                } else {
                    calculateLastId(response.deliveryGeekList);
                    addLocalData(response.deliveryGeekList);
                    adapter.addData(response.deliveryGeekList);
                    refreshLayout.setEnableLoadMore(response.hasMore);
                }
            }
            refreshLayout.finishRefresh();
            refreshLayout.finishLoadMore();
        });
        mViewModel.geekPostStatusLiveData.observe(this, statusBean -> {
            if (statusBean == null) return;
            List<BossDeliveryGeekBean> deliveryGeekList = adapter.getData();
            if (LList.isEmpty(deliveryGeekList)) return;
            for (BossDeliveryGeekBean geekBean : deliveryGeekList) {
                if (LText.equal(statusBean.encryptGeekId, geekBean.encryptGeekId) && (geekBean.deliverType == statusBean.deliverType)) {
                    geekBean.exchangeState = statusBean.exchangeState;
                    int position = deliveryGeekList.indexOf(geekBean);
                    if (position > -1) {
                        adapter.notifyItemChanged(position);
                    }
                    ToastUtils.showText("已交换，可在聊天页查看详情");
                }
            }
        });
    }
    
    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        mViewModel.getGeekDeliveryListOnline(deliverType, deliveryId);
    }
    
    /**
     * 计算最后一个投递ID，用于分页加载
     */
    private void calculateLastId(List<BossDeliveryGeekBean> deliveryGeekList) {
        if (!LList.isEmpty(deliveryGeekList)) {
            for (int i = deliveryGeekList.size() - 1; i >= 0; i--) {
                BossDeliveryGeekBean bean = deliveryGeekList.get(i);
                if (bean != null && !TextUtils.isEmpty(bean.encryptDeliveryId)) {
                    deliveryId = bean.encryptDeliveryId;
                    break;
                }
            }
        }
    }

    /**
     * 根据数据是否为空，来设置布局的显示状态
     *
     * @param isEmpty 数据是否为空
     */
    private void renderEmpty(boolean isEmpty) {
        if (isEmpty) {
            layoutManager.showEmptyScene();
            refreshLayout.setVisibility(View.GONE);
            adapter.setNewData(null);
        } else {
            layoutManager.dismiss();
            refreshLayout.setVisibility(View.VISIBLE);
        }
    }

    private void addLocalData(List<BossDeliveryGeekBean> deliveryGeekList) {
        if (LList.isEmpty(deliveryGeekList)) {
            return;
        }
        for (BossDeliveryGeekBean bean : deliveryGeekList) {
            if (bean != null) {
                bean.deliverType = deliverType;
            }
        }
    }
}