package com.hpbr.bosszhipin.live.campus.anchor.fragment;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import com.hpbr.bosszhipin.base.BaseAwareFragment;
import com.hpbr.bosszhipin.common.adapter.MyViewPagerAdapter;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.behavior.ViewPagerBottomSheetBehavior;
import com.hpbr.bosszhipin.views.titlebar.ScaleTransitionPagerTitleView;

import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.buildins.UIUtil;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.GradualChangeColorLinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.badge.BadgePagerTitleView;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.helper.IZPUILayout;
import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class LiveGeekPostDialogFragment extends BaseChildLiveFragment {
    private View viewTopArea;
    private View viewLeftArea;
    private ViewPagerBottomSheetBehavior<View> behavior;
    private MagicIndicator magicIndicator;
    private ViewPager viewPager;
    private List<String> titles = new ArrayList<>();
    private final List<BaseAwareFragment> mFragments = new ArrayList<>();

    public static LiveGeekPostDialogFragment getInstance() {
        return new LiveGeekPostDialogFragment();
    }

    @Nullable
    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        if (enter) {
            return AnimationUtils.loadAnimation(activity, mViewModel.isLandscape() ? R.anim.live_recruit_right_in : R.anim.live_recruit_bottom_in);
        } else {
            return AnimationUtils.loadAnimation(activity, mViewModel.isLandscape() ? R.anim.live_recruit_right_out : R.anim.live_recruit_bottom_out);
        }
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.live_fragment_geek_post_dialog;
    }

    @Override
    protected void initViews(View view) {
        viewTopArea = view.findViewById(R.id.view_top_area);
        viewLeftArea = view.findViewById(R.id.view_left_area);
        magicIndicator = view.findViewById(R.id.magic_indicator);
        viewPager = view.findViewById(R.id.view_pager);
        view.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                hideBottomSheet(true);
                LiveAnalysisUtil.reportLiveBossingDeliverListClick(mViewModel.liveRecordId, "2", "", "", "");
            }
        });
        setOutAreaClickListener(view);
        initTitlesAndFragments();
        initViewPagerAndIndicator();
        initMagicIndicator();
    }

    public void initTitlesAndFragments() {
        titles.add(activity.getResources().getString(R.string.live_post_list_phone));
        mFragments.add(LiveGeekPostListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_PHONE));
        titles.add(activity.getResources().getString(R.string.live_post_list_weixin));
        mFragments.add(LiveGeekPostListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_WX));
        titles.add(activity.getResources().getString(R.string.live_post_list_resume));
        mFragments.add(LiveGeekPostListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_RESUME));
    }

    private void initViewPagerAndIndicator() {
        MyViewPagerAdapter mViewAdapter = new MyViewPagerAdapter(getChildFragmentManager(), mFragments);
        viewPager.setOffscreenPageLimit(mFragments.size());
        viewPager.setAdapter(mViewAdapter);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                magicIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                magicIndicator.onPageSelected(position);
                mFragments.get(position).initData();
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                magicIndicator.onPageScrollStateChanged(state);
            }
        });
    }

    private void initMagicIndicator() {
        CommonNavigator commonNavigator = new CommonNavigator(activity);
        commonNavigator.setAdjustMode(false);
        commonNavigator.setSmoothScroll(false);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return titles.size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                BadgePagerTitleView badgePagerTitleView = new BadgePagerTitleView(context);
                ScaleTransitionPagerTitleView pagerTitleView = new ScaleTransitionPagerTitleView(context);
                pagerTitleView.setNormalColor(ContextCompat.getColor(context, R.color.color_FF141414));
                pagerTitleView.setSelectedColor(ContextCompat.getColor(context, R.color.color_FF141414));
                pagerTitleView.setMinScale(0.93f);
                pagerTitleView.setBothTextBold(true);
                pagerTitleView.setText(titles.get(index));
                pagerTitleView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
                pagerTitleView.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        viewPager.setCurrentItem(index, true);
                        LiveAnalysisUtil.reportLiveBossingDeliverListClick(mViewModel.liveRecordId, "0", titles.get(index), "", "");
                    }
                });
                badgePagerTitleView.setInnerPagerTitleView(pagerTitleView);
                return badgePagerTitleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                GradualChangeColorLinePagerIndicator indicator = new GradualChangeColorLinePagerIndicator(context);
                indicator.setGradualChangeColorStart(ContextCompat.getColor(context, R.color.app_green_dark));
                indicator.setGradualChangeColorEnd(ContextCompat.getColor(context, R.color.transparent));
                indicator.setMode(LinePagerIndicator.MODE_WRAP_CONTENT);
                indicator.setLineWidth(UIUtil.dip2px(context, 78));
                indicator.setLineHeight(UIUtil.dip2px(context, 6));
                indicator.setRoundRadius(UIUtil.dip2px(context, 3));
                indicator.setYOffset(UIUtil.dip2px(context, 14));
                return indicator;
            }
        });
        magicIndicator.setNavigator(commonNavigator);
        LinearLayout titleContainer = commonNavigator.getTitleContainer(); // must after setNavigator
        titleContainer.setShowDividers(LinearLayout.SHOW_DIVIDER_MIDDLE);
        titleContainer.setDividerDrawable(new ColorDrawable() {
            @Override
            public int getIntrinsicWidth() {
                return UIUtil.dip2px(activity, 20);
            }
        });
    }

    private void initBehavior(View view) {
        behavior = ViewPagerBottomSheetBehavior.from(view);
        behavior.setState(ViewPagerBottomSheetBehavior.STATE_EXPANDED);
        behavior.setHideable(true);
        behavior.setSkipCollapsed(true);
        behavior.setPeekHeight((int) (ZPUIDisplayHelper.getScreenHeight(activity) * 0.7));
        behavior.setBottomSheetCallback(new ViewPagerBottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == ViewPagerBottomSheetBehavior.STATE_HIDDEN) {
                    hideBottomSheet(true);
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
            }
        });
    }

    private void setOutAreaClickListener(View view) {
        if (!mViewModel.isLandscape()) {
            ZPUIConstraintLayout cl_container = view.findViewById(R.id.cl_container);
            cl_container.setRadius(ZPUIDisplayHelper.dp2px(activity, 12), IZPUILayout.HIDE_RADIUS_SIDE_BOTTOM);
            viewLeftArea.setVisibility(View.GONE);
            viewTopArea.setVisibility(View.VISIBLE);
            viewTopArea.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    hideBottomSheet(true);
                }
            });
            initBehavior(view);
        } else {
            viewLeftArea.setVisibility(View.VISIBLE);
            viewTopArea.setVisibility(View.GONE);
            viewLeftArea.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    hideBottomSheet(true);
                }
            });
        }
    }
}
