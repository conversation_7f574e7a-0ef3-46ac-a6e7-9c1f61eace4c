package com.hpbr.bosszhipin.live.campus.anchor.view;

import android.os.Bundle;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.bean.BossLiveCallConnectItemBean;
import com.hpbr.bosszhipin.live.boss.live.dialog.LiveCloseDialog;
import com.hpbr.bosszhipin.live.boss.live.dialog.LivePauseCloseDialog;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.AbsSettingItem;
import com.hpbr.bosszhipin.live.campus.anchor.dialog.LiveShareToolDialog;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorBeautyFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorFlowPurchaseFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorGeekResumeFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorGuideVideoFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorIntentionFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorInviteCallConnectFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorJobListFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorPasterFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorPosterShareFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorPreLiveChoosePositionFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorResolutionFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorResumeFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorSubtitleSwitchFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorToolsFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorVerifyCallConnectFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.BaseChildLiveFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LiveAiGenExplainFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LiveGeekPostDialogFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LiveHighlightTipsFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LiveRecordExplainSwitchFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LuckyDrawDraftFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LuckyDrawResultFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.MarkExplainConfirmFirstFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.MarkExplainSwitchFragment;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.MobileAndComputerFragment;
import com.hpbr.bosszhipin.live.campus.fragment.InviteDeliverFragment;
import com.hpbr.bosszhipin.live.campus.listener.OnBottomSheetListener;
import com.hpbr.bosszhipin.live.campus.listener.OnFragmentDismissListener;
import com.hpbr.bosszhipin.live.campus.listener.OnStartLiveClickListener;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.export.IntentKey;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.geek.audience.listener.OnShareEventListener;
import com.hpbr.bosszhipin.live.geek.audience.popup.BossInviteTipPopup;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.response.BossLiveBeautyMultiConfigResponse;
import com.hpbr.bosszhipin.live.net.response.BossRecruitDetailResponse;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.techwolf.lib.tlog.TLog;

import java.util.List;

public class DialogManager{
    public static final String TAG = "AnchorDialogManager";

    private FragmentActivity activity;
    private BossRecruitDetailResponse detailResponse;
    private boolean landscape;// 是否横屏

    private AnchorPosterShareFragment posterShareFragment;// 海报分享
    private MobileAndComputerFragment mobileAndComputerFragment;// 直播管理
    private AnchorToolsFragment toolsFragment;// 工具栏
    private AnchorResolutionFragment resolutionFragment;// 清晰度
    private AnchorPreLiveChoosePositionFragment preLiveChoosePositionFragment;// 立即开播直播前选择职位
    private LiveAiGenExplainFragment liveAiGenExplainFragment;
    private AnchorBeautyFragment beautyFragment;// 新版美颜等级
    private AnchorPasterFragment pasterFragment;// 特效
    private MarkExplainSwitchFragment markExplainSwitchFragment;// 标记讲解开关
    private MarkExplainConfirmFirstFragment markExplainConfirmFirstFragment;// 首次标记讲解
    private AnchorIntentionFragment intentionFragment;// 心动榜
    private LiveGeekPostDialogFragment liveGeekPostDialogFragment; //投递牛人弹层
    private InviteDeliverFragment anchorInviteDeliverFragment;// 邀请投递
    private AnchorGeekResumeFragment geekResumeFragment;// 牛人简历
    private AnchorResumeFragment resumeFragment;// 邀请牛人投递
    private LuckyDrawDraftFragment luckyDrawDraftFragment;// 抽奖草稿和发布
    private LuckyDrawResultFragment luckyDrawResultFragment;// 抽奖结果
    private AnchorJobListFragment jobListFragment;// 职位列表
    private AnchorInviteCallConnectFragment inviteColleagueConnectFragment;// 邀请同事连麦
    private AnchorVerifyCallConnectFragment verifyCallConnectFragment;// 视频连线确认弹框
    private LiveRecordExplainSwitchFragment mExplainSwitchFragment;// 1009.813 录制讲解视频竖屏不再提醒弹窗
    private AnchorFlowPurchaseFragment flowPurchaseFragment;// 流量购买
    private AnchorSubtitleSwitchFragment subtitleSwitchFragment;// 字幕开关
    private AnchorGuideVideoFragment guideVideoFragment;

    private DialogUtils pauseDialog;// 暂停确认弹框
    private LiveCloseDialog closeDialog;// 关闭确认弹框
    private LivePauseCloseDialog pauseCloseDialog;// 暂停后点关闭确认弹框
    private BossInviteTipPopup bossInviteTipPopup;
    private boolean recordExplainCardShowing;// 录制讲解卡片是否正在显示，如果正在显示，其他浮层不能再显示了

    private LiveHighlightTipsFragment highlightRecordFragment; // 直播高光记录

    private DialogManager(FragmentActivity activity) {
        this.activity = activity;
    }

    public void showLiveShareDialog(OnShareEventListener onShareEventListener) {
        new LiveShareToolDialog(activity, landscape, detailResponse)
                .setClickListener(new LiveShareToolDialog.OnClickListener() {
                    @Override
                    public void onClick(FragmentActivity activity, int type, View view) {
                        if (type == SHARE_POSTER) { //分享海报
                            showPosterFragment();
                        }
                    }

                    @Override
                    public void shareComplete() {
                        if (onShareEventListener != null) {
                            onShareEventListener.shareComplete();
                        }
                    }
                }).shareAction();
    }

    public void showMobileAndComputerFragment(OnFragmentDismissListener dismissListener) {
        mobileAndComputerFragment = MobileAndComputerFragment.getInstance(null);
        mobileAndComputerFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, mobileAndComputerFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                mobileAndComputerFragment = null;
                if (dismissListener != null) {
                    dismissListener.onDismiss();
                }
            }
        }));
        mobileAndComputerFragment.showBottomSheet();
    }

    public void showPosterFragment() {
        posterShareFragment = AnchorPosterShareFragment.getInstance(null);
        posterShareFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, posterShareFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                posterShareFragment = null;
            }
        }));
        posterShareFragment.showBottomSheet();
        if (landscape) {
            AppUtil.setRequestedOrientation(activity, false);// 海报分享不支持横屏
        }
    }

    public void showToolsFragment() {
        toolsFragment = AnchorToolsFragment.getInstance(null);
        toolsFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, toolsFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                toolsFragment = null;
            }
        }));
        toolsFragment.showBottomSheet();
    }

    public void hideToolsFragment() {
        if (null != toolsFragment) {
            toolsFragment.hideBottomSheet(true);
        }
    }

    public void updateToolsFragment(List<AbsSettingItem> list) {
        if (toolsFragment != null) {
            toolsFragment.updateList(list);
        }
    }

    public void showBeautyFragment(BossLiveBeautyMultiConfigResponse response, OnFragmentDismissListener dismissListener) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(AnchorBeautyFragment.KEY_BEAUTY_RESPONSE, response);
        beautyFragment = AnchorBeautyFragment.getInstance(bundle);
        beautyFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, beautyFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                beautyFragment = null;
                if (dismissListener != null) {
                    dismissListener.onDismiss();
                }
            }
        }));
        beautyFragment.showBottomSheet();
    }

    public void showPasterFragment(OnFragmentDismissListener dismissListener){
        pasterFragment = AnchorPasterFragment.getInstance(null);
        pasterFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, pasterFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                pasterFragment = null;
                if (dismissListener != null){
                    dismissListener.onDismiss();
                }
            }
        }));
        pasterFragment.showBottomSheet();
    }

    public void showMarkExplainSwitchFragment() {
        markExplainSwitchFragment = MarkExplainSwitchFragment.getInstance(null);
        markExplainSwitchFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, markExplainSwitchFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                markExplainSwitchFragment = null;
            }
        }));
        markExplainSwitchFragment.showBottomSheet();
    }

    public boolean markExplainSwitchFragmentShowing() {
        return markExplainSwitchFragment != null && markExplainSwitchFragment.isShowing();
    }

    /**
     * 首次标记讲解开关
     */
    public void showMarkExplainConfirmFirstFragment(String topJobId) {
        Bundle bundle = new Bundle();
        bundle.putString(LiveConstants.KEY_TOP_JOB_ID, topJobId);
        markExplainConfirmFirstFragment = MarkExplainConfirmFirstFragment.getInstance(bundle);
        markExplainConfirmFirstFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, markExplainConfirmFirstFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                markExplainConfirmFirstFragment = null;
            }
        }));
        markExplainConfirmFirstFragment.showBottomSheet();
    }

    public void dismissMarkConfirmFirstDialog() {
        if (markExplainConfirmFirstFragment != null && markExplainConfirmFirstFragment.isShowing()) {
            markExplainConfirmFirstFragment.hideBottomSheet(true);
        }
    }

    /**
     * 1 本场投递 2 牛人心动榜
     */
    public void showIntentionFragment(int type) {
        Bundle bundle = new Bundle();
        bundle.putInt(AnchorIntentionFragment.TYPE, type);
        intentionFragment = AnchorIntentionFragment.getInstance(bundle);
        intentionFragment.setOnBottomSheetListener(getOnBottomSheetListener(landscape ? R.id.fl_full_layer_container : R.id.bottom_sheet_parent, intentionFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                intentionFragment = null;
            }
        }));
        intentionFragment.showBottomSheet();
    }

    public void showGeekPostFragment() {
        liveGeekPostDialogFragment = LiveGeekPostDialogFragment.getInstance();
        liveGeekPostDialogFragment.setOnBottomSheetListener(getOnBottomSheetListener(landscape ? R.id.fl_full_layer_container : R.id.bottom_sheet_parent, liveGeekPostDialogFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                liveGeekPostDialogFragment = null;
            }
        }));
        liveGeekPostDialogFragment.showBottomSheet();
    }

    public void showInviteHintTipPopup(View view,String liveRecordId){
        boolean isShowed = SpManager.get().user().getBoolean(CampusConstant.KEY_USER_SHOW_INVITE_HINT+"_"+liveRecordId, false);
        if(isShowed) return;
        SpManager.get().user().edit().putBoolean(CampusConstant.KEY_USER_SHOW_INVITE_HINT+"_"+liveRecordId,true).apply();
        dismissInviteHintTipPopup();
        bossInviteTipPopup = new BossInviteTipPopup();
        bossInviteTipPopup.show(activity,view,null);
    }

    private void dismissInviteHintTipPopup(){
        if (bossInviteTipPopup != null) {
            bossInviteTipPopup.dismiss();
            bossInviteTipPopup = null;
        }
    }

    public void dismissIntentionFragment() {
        if (intentionFragment != null && intentionFragment.isShowing()) {
            intentionFragment.hideBottomSheet(true);
        }
    }

    public void showGeekResumeFragment(BossDeliveryGeekBean geekBean, @LiveConstants.GeekDeliverType int deliverType) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_ENTITY, geekBean);
        bundle.putInt(Constants.DATA_INT, deliverType);
        geekResumeFragment = AnchorGeekResumeFragment.getInstance(bundle);
        geekResumeFragment.setOnBottomSheetListener(getOnBottomSheetListener(landscape ? R.id.fl_full_layer_container : R.id.bottom_sheet_parent, geekResumeFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                geekResumeFragment = null;
            }
        }));
        geekResumeFragment.showBottomSheet();
    }


    public void showInviteDeliverBottomSheetDialog(boolean landscape,String liveRecordId) {
        Bundle bundle = new Bundle();
        bundle.putBoolean(CampusConstant.LANDSCAPE, landscape);
        bundle.putString(IntentKey.INTENT_LIVE_RECORD_ID, liveRecordId);
        anchorInviteDeliverFragment = InviteDeliverFragment.getInstance(bundle);
        anchorInviteDeliverFragment.setOnBottomSheetListener(getOnBaseFragBottomSheetListener(R.id.fl_full_layer_container,anchorInviteDeliverFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                anchorInviteDeliverFragment = null;
            }
        }));
        anchorInviteDeliverFragment.showBottomSheet();
    }

    public void dismissInviteDeliverBottomSheetDialog(){
        if(null!= anchorInviteDeliverFragment){
            anchorInviteDeliverFragment.hideBottomSheet();
            anchorInviteDeliverFragment = null;
        }
    }

    public void showResumeFragment() {
        resumeFragment = AnchorResumeFragment.getInstance(null);
        resumeFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, resumeFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                resumeFragment = null;
            }
        }));
        resumeFragment.showBottomSheet();
    }

    public void dismissResumeFragment() {
        if (resumeFragment != null && resumeFragment.isShowing()) {
            resumeFragment.hideBottomSheet(true);
        }
    }

    public void showLuckyDrawDraftFragment() {
        luckyDrawDraftFragment = LuckyDrawDraftFragment.getInstance(null);
        luckyDrawDraftFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, luckyDrawDraftFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                luckyDrawDraftFragment = null;
            }
        }));
        luckyDrawDraftFragment.showBottomSheet();
    }

    public void dismissLuckyDrawDraftFragment() {
        if (luckyDrawDraftFragment != null && luckyDrawDraftFragment.isShowing()) {
            luckyDrawDraftFragment.hideBottomSheet(true);
        }
    }

    public void showLuckyDrawResultFragment() {
        luckyDrawResultFragment = LuckyDrawResultFragment.getInstance(null);
        luckyDrawResultFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, luckyDrawResultFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                luckyDrawDraftFragment = null;
            }
        }));
        luckyDrawResultFragment.showBottomSheet();
    }

    public void showJobListFragment() {
        jobListFragment = AnchorJobListFragment.getInstance(null);
        jobListFragment.setOnBottomSheetListener(getOnBottomSheetListener(landscape ? R.id.fl_full_layer_container : R.id.bottom_sheet_parent, jobListFragment, () -> {
            // 延迟解决横屏下不再提醒无法展示问题
            App.get().getMainHandler().postDelayed(() -> {
                if (jobListFragment != null) {
                    jobListFragment.showExplainCard();
                    jobListFragment = null;
                }
            }, 300);
        }));
        jobListFragment.showBottomSheet();
    }

    public void showInviteCallConnectFragment() {
        inviteColleagueConnectFragment = AnchorInviteCallConnectFragment.getInstance(null);
        inviteColleagueConnectFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, inviteColleagueConnectFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                inviteColleagueConnectFragment = null;
            }
        }));
        inviteColleagueConnectFragment.showBottomSheet();
    }

    public void showSubtitleSwitchFragment() {
        subtitleSwitchFragment = AnchorSubtitleSwitchFragment.getInstance(null);
        subtitleSwitchFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, subtitleSwitchFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                subtitleSwitchFragment = null;
            }
        }));
        subtitleSwitchFragment.showBottomSheet();
    }

    public void hideSubtitleSwitchFragment() {
        if (null != subtitleSwitchFragment) {
            subtitleSwitchFragment.hideBottomSheet(true);
        }
    }

    public void showVerifyCallConnectFragment(BossLiveCallConnectItemBean bean) {
        if (isShowingVerifyCallConnectFragment()) return;
        Bundle bundle = new Bundle();
        bundle.putSerializable(LiveConstants.KEY_BOSS_LIVE_VERIFY_CALL_CONNECT_BEAN, bean);
        verifyCallConnectFragment = AnchorVerifyCallConnectFragment.getInstance(bundle);
        verifyCallConnectFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, verifyCallConnectFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                verifyCallConnectFragment = null;
            }
        }));
        verifyCallConnectFragment.showBottomSheet();
    }

    public boolean isShowingVerifyCallConnectFragment() {
        return verifyCallConnectFragment != null && verifyCallConnectFragment.isShowing();
    }

    public void dismissVerifyCallConnectFragment() {
        if (verifyCallConnectFragment != null && verifyCallConnectFragment.isShowing()) {
            verifyCallConnectFragment.hideBottomSheet(true);
        }
    }

    // 1009.813 录制讲解视频说明
    public void showRecordExplainFragment(LiveRecordExplainSwitchFragment.OnDismissCallback callback) {
        mExplainSwitchFragment = LiveRecordExplainSwitchFragment.getInstance(null);
        mExplainSwitchFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, mExplainSwitchFragment, () -> {
            if (callback != null) {
                callback.onDismiss();
            }
            mExplainSwitchFragment = null;
        }));
        mExplainSwitchFragment.showBottomSheet();
    }

    /**
     * @param fromType 弹层唤醒方式：1-icon点击，2-横幅点击
     */
    public void showFlowPurchaseFragment(int fromType) {
        Bundle bundle = new Bundle();
        bundle.putInt(CampusConstant.FROM, fromType);
        flowPurchaseFragment = AnchorFlowPurchaseFragment.getInstance(bundle);
        flowPurchaseFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, flowPurchaseFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                flowPurchaseFragment = null;
            }
        }));
        flowPurchaseFragment.showBottomSheet();
    }

    public AnchorFlowPurchaseFragment getAnchorFlowPurchaseFragment() {
        return flowPurchaseFragment;
    }

    public void dismissFlowPurchaseFragment() {
        if (flowPurchaseFragment != null && flowPurchaseFragment.isShowing()) {
            flowPurchaseFragment.hideBottomSheet(true);
        }
    }

    public void showGuideVideoFragment() {
        guideVideoFragment = AnchorGuideVideoFragment.getInstance(null);
        guideVideoFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, guideVideoFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                checkReleaseGuideVideoPlayer();
                guideVideoFragment = null;
            }
        }));
        guideVideoFragment.showBottomSheet();
    }

    public void showResolutionFragment(OnFragmentDismissListener dismissListener) {
        resolutionFragment = AnchorResolutionFragment.getInstance(null);
        resolutionFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, resolutionFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                resolutionFragment = null;
                if (dismissListener != null) {
                    dismissListener.onDismiss();
                }
            }
        }));
        resolutionFragment.showBottomSheet();
    }

    public void hideResolutionFragment() {
        if (null != resolutionFragment) {
            resolutionFragment.hideBottomSheet(true);
        }
    }

    public void showPreLiveChoosePositionFragment(OnStartLiveClickListener startLiveClickListener) {
        preLiveChoosePositionFragment = AnchorPreLiveChoosePositionFragment.getInstance(null);
        preLiveChoosePositionFragment.setStartLiveClickListener(startLiveClickListener);
        preLiveChoosePositionFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, preLiveChoosePositionFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                preLiveChoosePositionFragment = null;
                if(null!=startLiveClickListener){
                    startLiveClickListener.onDismiss();
                }
            }
        }));
        preLiveChoosePositionFragment.showBottomSheet();
    }

    public void showLiveAiGenExplainFragment(OnStartLiveClickListener startLiveClickListener) {
        liveAiGenExplainFragment = LiveAiGenExplainFragment.getInstance(null);
        liveAiGenExplainFragment.setStartLiveClickListener(startLiveClickListener);
        liveAiGenExplainFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, liveAiGenExplainFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                liveAiGenExplainFragment = null;
            }
        }));
        liveAiGenExplainFragment.showBottomSheet();
    }

    public void showHighlightRecordFragment(OnFragmentDismissListener dismissListener) {
        highlightRecordFragment = LiveHighlightTipsFragment.newInstance(null);
        highlightRecordFragment.setOnBottomSheetListener(getOnBottomSheetListener(R.id.fl_full_layer_container, highlightRecordFragment, new OnFragmentDismissListener() {
            @Override
            public void onDismiss() {
                highlightRecordFragment = null;
                if (dismissListener != null) {
                    dismissListener.onDismiss();
                }
            }
        }));
        highlightRecordFragment.showBottomSheet();
    }

    public void dismissHighlightRecordFragment() {
        if (highlightRecordFragment != null && highlightRecordFragment.isShowing()) {
            highlightRecordFragment.hideBottomSheet(true);
        }
    }

    /**
     * 检查并销毁直播前引导视频资源
     */
    public void checkReleaseGuideVideoPlayer() {
        if (guideVideoFragment != null) {
            guideVideoFragment.releasePlayerHelper();
        }
    }

    /**
     * 检查并销毁直播前引导视频资源
     */
    public void checkPauseGuideVideoPlayer() {
        if (guideVideoFragment != null && guideVideoFragment.isShowing()) {
            guideVideoFragment.checkPauseGuideVideoPlayer();
        }
    }


    public void showPauseConfirmDialog(boolean isExperienceLive, boolean landscape, OnClickNoFastListener positiveClickListener) {
        if (pauseDialog == null || !pauseDialog.isShowing()) {
            pauseDialog = new DialogUtils.Builder(activity)
                    .setDoubleButton()
                    .setTitle("暂停本次直播？")
                    .setDesc(isExperienceLive ? "将暂停你的视频，稍后你可继续直播。" : "将暂停你的视频，稍后你可继续直播，暂停期间求职者可继续发消息和投递简历。")
                    .setNegativeAction("取消")
                    .setPositiveAction("确定", positiveClickListener)
                    .setLandscape(landscape)
                    .build();
            pauseDialog.show();
        }
    }

    public void dismissPauseConfirmDialog() {
        if (pauseDialog != null && pauseDialog.isShowing()) {
            pauseDialog.dismiss();
        }
    }

    /**
     * 显示关闭直播间浮层
     */
    public void showCloseDialog(boolean landscape, LiveCloseDialog.IOnTouchListener touchListener) {
        closeDialog = new LiveCloseDialog(activity, landscape, touchListener);
        closeDialog.show();
    }

    /**
     * 隐藏关闭直播间浮层
     */
    public void dismissCloseDialog() {
        if (closeDialog != null) {
            closeDialog.dismiss();
        }
    }

    public boolean closeDialogShowing() {
        return closeDialog != null && closeDialog.isShowing();
    }

    /**
     * 暂停状态下，显示关闭直播间浮层
     */
    public void showPauseCloseDialog(boolean landscape, LivePauseCloseDialog.IOnTouchListener touchListener) {
        pauseCloseDialog = new LivePauseCloseDialog(activity, landscape, touchListener);
        pauseCloseDialog.show();
    }

    /**
     * 暂停状态下，隐藏关闭直播间浮层
     */
    public void dismissPauseCloseDialog() {
        if (pauseCloseDialog != null && pauseCloseDialog.isShowing()) {
            pauseCloseDialog.dismiss();
        }
    }

    public boolean pauseCloseDialogShowing() {
        return pauseCloseDialog != null && pauseCloseDialog.isShowing();
    }

    public void dismissAllFragments() {
        /* 直播管理 */
        if (mobileAndComputerFragment != null && mobileAndComputerFragment.isShowing()) {
            mobileAndComputerFragment.hideBottomSheet(true);
        }
        /* 分享海报 */
        if (posterShareFragment != null && posterShareFragment.isShowing()) {
            posterShareFragment.hideBottomSheet(true);
        }
        /* 工具 */
        if (toolsFragment != null && toolsFragment.isShowing()) {
            toolsFragment.hideBottomSheet(true);
        }
        /* 新版美颜等级 */
        if (beautyFragment != null && beautyFragment.isShowing()) {
            beautyFragment.hideBottomSheet(true);
        }
        /* 标记讲解开关 */
        if (markExplainSwitchFragment != null && markExplainSwitchFragment.isShowing()) {
            markExplainSwitchFragment.hideBottomSheet(true);
        }
        /* 横屏 首次标记讲解 */
        if (markExplainConfirmFirstFragment != null && markExplainConfirmFirstFragment.isShowing()) {
            markExplainConfirmFirstFragment.hideBottomSheet(true);
        }
        /* 心动榜 */
        if (intentionFragment != null && intentionFragment.isShowing()) {
            intentionFragment.hideBottomSheet(true);
        }
        /* 牛人简历 */
        if (geekResumeFragment != null && geekResumeFragment.isShowing()) {
            geekResumeFragment.hideBottomSheet(true);
        }
        /* 邀请投递 */
        if (resumeFragment != null && resumeFragment.isShowing()) {
            resumeFragment.hideBottomSheet(true);
        }
        /* 发布抽奖 */
        if (luckyDrawDraftFragment != null && luckyDrawDraftFragment.isShowing()) {
            luckyDrawDraftFragment.hideBottomSheet(true);
        }
        /* 抽奖结果 */
        if (luckyDrawResultFragment != null && luckyDrawResultFragment.isShowing()) {
            luckyDrawResultFragment.hideBottomSheet(true);
        }
        /* 职位列表 */
        if (jobListFragment != null && jobListFragment.isShowing()) {
            jobListFragment.hideBottomSheet(true);
        }
        /* 邀请同事连麦 */
        if (inviteColleagueConnectFragment != null && inviteColleagueConnectFragment.isShowing()) {
            inviteColleagueConnectFragment.hideBottomSheet(true);
        }
        /* 视频连线确认弹框 */
        if (verifyCallConnectFragment != null && verifyCallConnectFragment.isShowing()) {
            verifyCallConnectFragment.hideBottomSheetAndStopRemoteView();
        }
        /* 字幕开关 */
        if (subtitleSwitchFragment != null && subtitleSwitchFragment.isShowing()) {
            subtitleSwitchFragment.hideBottomSheet(true);
        }
        /* 直播前视频引导 */
        if (guideVideoFragment != null && guideVideoFragment.isShowing()) {
            guideVideoFragment.hideBottomSheet(true);
        }
        /* 清晰度 */
        if (resolutionFragment != null && resolutionFragment.isShowing()) {
            resolutionFragment.hideBottomSheet(true);
        }
        /* 邀 */
        dismissInviteHintTipPopup();
        dismissInviteDeliverBottomSheetDialog();
        /* 直播高光记录 */
        if (highlightRecordFragment != null && highlightRecordFragment.isShowing()) {
            highlightRecordFragment.hideBottomSheet(true);
        }
        /* AI生成讲解 */
        if (liveAiGenExplainFragment != null && liveAiGenExplainFragment.isShowing()) {
            liveAiGenExplainFragment.hideBottomSheet(true);
        }
    }

    public boolean onBackPressed() {
        /* 直播管理 */
        if (mobileAndComputerFragment != null && mobileAndComputerFragment.isShowing()) {
            mobileAndComputerFragment.hideBottomSheet(true);
            return true;
        }
        /* 分享海报 */
        if (posterShareFragment != null && posterShareFragment.isShowing()) {
            posterShareFragment.hideBottomSheet(true);
            return true;
        }
        /* 工具 */
        if (toolsFragment != null && toolsFragment.isShowing()) {
            toolsFragment.hideBottomSheet(true);
            return true;
        }
        /* 新版美颜等级 */
        if (beautyFragment != null && beautyFragment.isShowing()) {
            beautyFragment.hideBottomSheet(true);
            return true;
        }
        /* 标记讲解开关 */
        if (markExplainSwitchFragment != null && markExplainSwitchFragment.isShowing()) {
            markExplainSwitchFragment.hideBottomSheet(true);
            return true;
        }
        /* 横屏 首次标记讲解 */
        if (markExplainConfirmFirstFragment != null && markExplainConfirmFirstFragment.isShowing()) {
            markExplainConfirmFirstFragment.hideBottomSheet(true);
            return true;
        }
        /* 心动榜 */
        if (intentionFragment != null && intentionFragment.isShowing()) {
            intentionFragment.hideBottomSheet(true);
            return true;
        }
        /* 牛人简历 */
        if (geekResumeFragment != null && geekResumeFragment.isShowing()) {
            geekResumeFragment.hideBottomSheet(true);
            return true;
        }
        /* 邀请投递 */
        if (resumeFragment != null && resumeFragment.isShowing()) {
            resumeFragment.hideBottomSheet(true);
            return true;
        }
        /* 发布抽奖 */
        if (luckyDrawDraftFragment != null && luckyDrawDraftFragment.isShowing()) {
            luckyDrawDraftFragment.hideBottomSheet(true);
            return true;
        }
        /* 抽奖结果 */
        if (luckyDrawResultFragment != null && luckyDrawResultFragment.isShowing()) {
            luckyDrawResultFragment.hideBottomSheet(true);
            return true;
        }
        /* 职位列表 */
        if (jobListFragment != null && jobListFragment.isShowing()) {
            jobListFragment.hideBottomSheet(true);
            return true;
        }
        /* 邀请同事连麦 */
        if (inviteColleagueConnectFragment != null && inviteColleagueConnectFragment.isShowing()) {
            inviteColleagueConnectFragment.hideBottomSheet(true);
            return true;
        }
        /* 视频连线确认弹框 */
        if (verifyCallConnectFragment != null && verifyCallConnectFragment.isShowing()) {
            verifyCallConnectFragment.hideBottomSheetAndStopRemoteView();
            return true;
        }
        /* 流量购买 */
        if (flowPurchaseFragment != null && flowPurchaseFragment.isShowing()) {
            flowPurchaseFragment.hideBottomSheet(true);
            return true;
        }
        /* 字幕开关 */
        if (subtitleSwitchFragment != null && subtitleSwitchFragment.isShowing()) {
            subtitleSwitchFragment.hideBottomSheet(true);
            return true;
        }
        /* 直播前视频引导 */
        if (guideVideoFragment != null && guideVideoFragment.isShowing()) {
            guideVideoFragment.hideBottomSheet(true);
            return true;
        }
        /* 清晰度 */
        if (resolutionFragment != null && resolutionFragment.isShowing()) {
            resolutionFragment.hideBottomSheet(true);
            return true;
        }
        /* 邀 */
        if (anchorInviteDeliverFragment != null && anchorInviteDeliverFragment.isShowing()) {
            anchorInviteDeliverFragment.hideBottomSheet(true);
            return true;
        }
        /* AI生成讲解 */
        if (liveAiGenExplainFragment != null && liveAiGenExplainFragment.isShowing()) {
            liveAiGenExplainFragment.hideBottomSheet(true);
            return true;
        }
        /* 立即开播-直播前-职位弹层 */
        if (preLiveChoosePositionFragment != null && preLiveChoosePositionFragment.isShowing()) {
            preLiveChoosePositionFragment.hideBottomSheet(true);
            return true;
        }
        /* 直播高光记录 */
        if (highlightRecordFragment != null && highlightRecordFragment.isShowing()) {
            highlightRecordFragment.hideBottomSheet(true);
            return true;
        }
        return false;
    }

    private OnBottomSheetListener getOnBottomSheetListener(int layoutId, BaseChildLiveFragment targetFragment, OnFragmentDismissListener onFragmentDismissListener) {

        return new OnBottomSheetListener() {
            private final FragmentManager childFragmentManager = DialogManager.this.activity.getSupportFragmentManager();
            boolean isShow = false;
            final View container = activity.findViewById(layoutId);
            final Runnable runnable = () -> container.setVisibility(View.GONE);

            @Override
            public void hideBottomSheet(boolean cancel) {
                isShow = false;
                childFragmentManager
                        .beginTransaction()
                        .remove(targetFragment)
                        .commitAllowingStateLoss();
                Object tag = container.getTag();
                TLog.info(TAG, "hideBottomSheet:targetFragment=%s", targetFragment);
                if (tag == targetFragment) {
                    container.postDelayed(runnable, 200);
                    container.setOnClickListener(null);
                    container.setClickable(false);
                    container.setTag(null);
                } else {
                    TLog.info(TAG, "not same hideBottomSheet:tag=%s, targetFragment =%s", tag, targetFragment);
                    container.removeCallbacks(runnable);
                }
                targetFragment.onDestroy();

                if (onFragmentDismissListener != null) {
                    onFragmentDismissListener.onDismiss();
                }
            }

            @Override
            public void showBottomSheet() {
                if (recordExplainCardShowing) return;

                container.removeCallbacks(runnable);
                container.setVisibility(View.VISIBLE);
                container.setTag(targetFragment);
                container.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        targetFragment.hideBottomSheet(true);
                    }
                });
                TLog.info(TAG, "showBottomSheet:targetFragment=%s", targetFragment);
                isShow = true;
                FragmentTransaction fragmentTransaction = childFragmentManager.beginTransaction();
                Fragment fragmentByTag = childFragmentManager.findFragmentByTag(targetFragment.getClass().getName());
                if (fragmentByTag != null) {
                    fragmentTransaction.remove(fragmentByTag);
                }
                fragmentTransaction
                        .replace(layoutId, targetFragment, targetFragment.getClass().getName())
                        .commitAllowingStateLoss();
            }

            @Override
            public boolean isShowing() {
                return isShow;
            }
        };
    }

    private OnBottomSheetListener getOnBaseFragBottomSheetListener(int layoutId, BaseFragment targetFragment, OnFragmentDismissListener onFragmentDismissListener) {

        return new OnBottomSheetListener() {
            private final FragmentManager childFragmentManager = DialogManager.this.activity.getSupportFragmentManager();
            boolean isShow = false;
            final View container = activity.findViewById(layoutId);
            final Runnable runnable = () -> container.setVisibility(View.GONE);

            @Override
            public void hideBottomSheet(boolean cancel) {
                isShow = false;
                childFragmentManager
                        .beginTransaction()
                        .remove(targetFragment)
                        .commitAllowingStateLoss();
                Object tag = container.getTag();
                TLog.info(TAG, "hideBottomSheet:targetFragment=%s", targetFragment);
                if (tag == targetFragment) {
                    container.postDelayed(runnable, 200);
                    container.setOnClickListener(null);
                    container.setClickable(false);
                    container.setTag(null);
                } else {
                    TLog.info(TAG, "not same hideBottomSheet:tag=%s, targetFragment =%s", tag, targetFragment);
                    container.removeCallbacks(runnable);
                }
                targetFragment.onDestroy();

                if (onFragmentDismissListener != null) {
                    onFragmentDismissListener.onDismiss();
                }
            }

            @Override
            public void showBottomSheet() {
                if (recordExplainCardShowing) return;

                container.removeCallbacks(runnable);
                container.setVisibility(View.VISIBLE);
                container.setTag(targetFragment);
//                container.setOnClickListener(new OnClickNoFastListener() {
//                    @Override
//                    public void onNoFastClick(View v) {
//                        targetFragment.hideBottomSheet(true);
//                    }
//                });
                TLog.info(TAG, "showBottomSheet:targetFragment=%s", targetFragment);
                isShow = true;
                FragmentTransaction fragmentTransaction = childFragmentManager.beginTransaction();
                Fragment fragmentByTag = childFragmentManager.findFragmentByTag(targetFragment.getClass().getName());
                if (fragmentByTag != null) {
                    fragmentTransaction.remove(fragmentByTag);
                }
                fragmentTransaction
                        .replace(layoutId, targetFragment, targetFragment.getClass().getName())
                        .commitAllowingStateLoss();
            }

            @Override
            public boolean isShowing() {
                return isShow;
            }
        };
    }

    public void setLandscape(boolean landscape) {
        this.landscape = landscape;
    }

    public void setRecordExplainCardShowing(boolean recordExplainCardShowing) {
        this.recordExplainCardShowing = recordExplainCardShowing;
    }

    public void setRecruitDetailResponse(BossRecruitDetailResponse detailResponse) {
        this.detailResponse = detailResponse;
    }

    public static DialogManager with(FragmentActivity activity) {
        return new DialogManager(activity);
    }

}
