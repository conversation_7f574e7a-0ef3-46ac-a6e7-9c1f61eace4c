package com.hpbr.bosszhipin.live.campus.anchor.viewmodel;

import android.os.SystemClock;
import android.text.TextUtils;

import com.hpbr.bosszhipin.live.bean.AnchorFlowPurchaseBean;
import com.hpbr.bosszhipin.live.bean.AnchorNoticeBean;
import com.hpbr.bosszhipin.live.bean.BossExplainFinishItemBean;
import com.hpbr.bosszhipin.live.bean.BossLiveCallConnectItemBean;
import com.hpbr.bosszhipin.live.bean.BossLiveExplainItemBean;
import com.hpbr.bosszhipin.live.bean.ClickApplaudCountBean;
import com.hpbr.bosszhipin.live.bean.CommentItemBean;
import com.hpbr.bosszhipin.live.bean.LiveFinishBean;
import com.hpbr.bosszhipin.live.bean.LiveGeekPostStatusBean;
import com.hpbr.bosszhipin.live.bean.LiveStartItemBean;
import com.hpbr.bosszhipin.live.bean.LuckyDrawDraftItemBean;
import com.hpbr.bosszhipin.live.bean.LuckyDrawItemBean;
import com.hpbr.bosszhipin.live.bean.OnlineNumItemBean;
import com.hpbr.bosszhipin.live.bean.OpItemBean;
import com.hpbr.bosszhipin.live.bean.SubtitleStatusItemBean;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BOpenCloseSubtitleItem;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.ClickApplaudModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.LuckyDrawBDraftModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.LuckyDrawModel;
import com.hpbr.bosszhipin.live.campus.anchor.model.FinishModel;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.CommentModel;
import com.hpbr.bosszhipin.live.net.bean.ExplainForLivingItemBean;
import com.hpbr.bosszhipin.live.net.bean.MarkExplainSwitcherBean;
import com.hpbr.bosszhipin.live.net.response.LuckyDrawDetailResponse;
import com.hpbr.bosszhipin.live.util.BossAtUtil;
import com.hpbr.bosszhipin.live.util.ChatContractParser;
import com.hpbr.bosszhipin.live.util.MsgType;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import java.util.Locale;
import java.util.UUID;

public class AnchorMessageDispatcher {
    private static final String TAG = "MessageDispatcher";

    public static void dispatcherMessage(AnchorViewModel viewModel, int msgType, String message) {
        TLog.info(TAG, "anchor dispatcherMessage msgType=%d, message=%s", msgType, message);
        switch (msgType) {
            case MsgType.TYPE_CLICK_APPLAUD: {// 点赞通知
                ClickApplaudCountBean bean = ClickApplaudCountBean.parseServerMessage(message);
                if (bean != null) {
                    viewModel.clickApplaudLiveData.setValue(new ClickApplaudModel(ClickApplaudModel.TYPE_CLICK_APPLAUD, bean.applaudCount));
                }
            }
            break;
            case MsgType.TYPE_UPDATE_CLICK_APPLAUD_NUM: {// 点赞数
                ClickApplaudCountBean bean = ClickApplaudCountBean.parseServerMessage(message);
                if (bean != null) {
                    viewModel.detailResponse.applaudCount = Math.max(bean.applaudCount, viewModel.detailResponse.applaudCount);
                    viewModel.headerLiveData.postValue(null != viewModel.headerLiveData.getValue() ? viewModel.headerLiveData.getValue() : 0);
                }
            }
            break;
            case MsgType.TYPE_LUCKY_DRAW_PREPARE: {// 抽奖准备中
                LuckyDrawItemBean bean = LuckyDrawItemBean.parseServerMessage(message);
                if (bean != null) {
                    LuckyDrawModel luckyDrawModel = viewModel.luckyDrawLiveData.getValue();
                    LuckyDrawDetailResponse luckyDraw = luckyDrawModel != null ? luckyDrawModel.response : null;
                    if (luckyDraw == null) {
                        luckyDraw = new LuckyDrawDetailResponse();
                    }
                    luckyDraw.totalLuckyDrawingTime = bean.readyTime + bean.drawingTime;
                    luckyDraw.leftLuckyDrawingTime = bean.drawingTime;
                    luckyDraw.leftLuckyDrawReadTime = bean.readyTime;
                    luckyDraw.luckyDrawOrder = bean.luckyDrawOrder;
                    luckyDraw.joinUserNum = bean.drawingNum;
                    luckyDraw.status = LiveConstants.STATUS_PREPARING;
                    // 初始化一个真正的结束时间
                    luckyDraw.endTime = SystemClock.elapsedRealtime()
                            + luckyDraw.leftLuckyDrawReadTime * 1000
                            + luckyDraw.leftLuckyDrawingTime * 1000;
                    viewModel.luckyDrawLiveData.postValue(new LuckyDrawModel(luckyDraw));
                    viewModel.dialogManager.dismissLuckyDrawDraftFragment();// 隐藏草稿列表
                }
            }
            break;
            case MsgType.TYPE_LUCKY_DRAW_START: { // 开始抽奖
                LuckyDrawItemBean bean = LuckyDrawItemBean.parseServerMessage(message);
                if (bean != null) {
                    LuckyDrawModel luckyDrawModel = viewModel.luckyDrawLiveData.getValue();
                    LuckyDrawDetailResponse luckyDraw = luckyDrawModel != null ? luckyDrawModel.response : null;
                    if (luckyDraw == null || luckyDraw.status != LiveConstants.STATUS_DRAWING) {// 不在抽奖状态
                        if (luckyDraw == null) {
                            luckyDraw = new LuckyDrawDetailResponse();
                        }
                        luckyDraw.totalLuckyDrawingTime = bean.drawingTime;
                        luckyDraw.leftLuckyDrawingTime = bean.drawingTime;
                        luckyDraw.leftLuckyDrawReadTime = 0;
                        luckyDraw.luckyDrawOrder = bean.luckyDrawOrder;
                        luckyDraw.joinUserNum = bean.drawingNum;
                        luckyDraw.status = LiveConstants.STATUS_DRAWING;
                        // 初始化一个真正的结束时间
                        luckyDraw.endTime = SystemClock.elapsedRealtime()
                                + luckyDraw.leftLuckyDrawReadTime * 1000
                                + luckyDraw.leftLuckyDrawingTime * 1000;
                        viewModel.luckyDrawLiveData.postValue(new LuckyDrawModel(luckyDraw));
                    }
                    viewModel.dialogManager.dismissLuckyDrawDraftFragment();// 隐藏草稿列表
                }
            }
            break;
            case MsgType.TYPE_LUCKY_DRAW_LIST: { // 中奖结果
                LuckyDrawItemBean bean = LuckyDrawItemBean.parseServerMessage(message);
                if (bean != null) {
                    LuckyDrawModel luckyDrawModel = viewModel.luckyDrawLiveData.getValue();
                    LuckyDrawDetailResponse luckyDraw = luckyDrawModel != null ? luckyDrawModel.response : null;
                    if (luckyDraw != null) {
                        luckyDraw.luckyDrawOrder = bean.luckyDrawOrder;
                        luckyDraw.status = bean.luckyDrawState;
                        viewModel.luckyDrawLiveData.postValue(new LuckyDrawModel(luckyDraw));
                    }
                }
            }
            break;
            case MsgType.TYPE_LUCKY_DRAW_DRAFT_MODIFY: {// 抽奖草稿变更
                LuckyDrawDraftItemBean bean = LuckyDrawDraftItemBean.parseServerMessage(message);
                if (bean != null) {
                    LuckyDrawBDraftModel draftModel = viewModel.luckyDrawDraftLiveData.getValue();
                    if (draftModel == null) {
                        draftModel = new LuckyDrawBDraftModel(bean.newDraftNum > 0);
                    } else {
                        draftModel.existDraft = bean.newDraftNum > 0;
                    }
                    viewModel.luckyDrawDraftLiveData.postValue(draftModel);
                }
            }
            break;
            case MsgType.TYPE_NOTICE_ONLINE_CNT: {// 在线人数
                OnlineNumItemBean bean = OnlineNumItemBean.parseServerMessage(message);
                if (bean != null) {
                    viewModel.detailResponse.liveViewersCnt = bean.totalNum;
                    viewModel.detailResponse.liveViewersOnlineCnt = bean.onlineNum;
                    viewModel.headerLiveData.postValue(null != viewModel.headerLiveData.getValue() ? viewModel.headerLiveData.getValue() : 0);
                }
            }
            break;
            case MsgType.TYPE_RESUME:// 投递简历
                CommentItemBean resumeItemBean = ChatContractParser.parseServerMessage(message);
                if (resumeItemBean != null) {
                    /*由于简历类型的消息返回的msgId为空，这里采用这个字段用来给简历类型的消息增加一个唯一标识*/
                    resumeItemBean.resumeUuid = UUID.randomUUID().toString();

                    CommentModel model = CommentModel.getInstance();
                    model.addItem(resumeItemBean);
                    viewModel.commentLiveData.postValue(model);
                    // 投递简历， 收到简历数+1
                    viewModel.addResumeNum(resumeItemBean);
                }
                break;
            case MsgType.TYPE_CONTROL_TOP: {// 置顶广播
                CommentItemBean commentItemBean = ChatContractParser.parseServerMessage(message);
                if (commentItemBean != null) {
                    viewModel.topMessageLiveData.postValue(commentItemBean);
                }
            }
            break;
            // 1209.711 新置顶职位 开始录制
            case MsgType.TYPE_NOTIFY_REAL_START_EXPLAIN: {
                CommentItemBean commentItemBean = ChatContractParser.parseServerMessage(message);
                if (commentItemBean != null) {
                    viewModel.detailResponse.topEncryptJobId = commentItemBean.encryptJobId;
                    viewModel.topJobLiveData.postValue(commentItemBean.encryptJobId);
                }
            }
            break;
            // 1209.711 新置顶职位 结束录制
            case MsgType.TYPE_NOTIFY_REAL_END_EXPLAIN: {
                CommentItemBean commentItemBean = ChatContractParser.parseServerMessage(message);
                if (commentItemBean != null) {
                    viewModel.detailResponse.topEncryptJobId = "";
                    viewModel.topJobLiveData.postValue("");
                }
            }
            break;
            case MsgType.TYPE_GROUP_TOP: {
                CommentItemBean commentItemBean = ChatContractParser.parseServerMessage(message);
                if (commentItemBean != null) {
                    viewModel.detailResponse.topEncryptJobGroupId = commentItemBean.encryptJobGroupId;
                    viewModel.topJobLiveData.postValue(commentItemBean.encryptJobGroupId);
                }
            }
            break;
            case MsgType.TYPE_MARK_EXPLAIN_SWITCH: {
                MarkExplainSwitcherBean switcherBean = MarkExplainSwitcherBean.parseServerMessage(message);
                if (switcherBean != null) {
                    viewModel.detailResponse.markSwitchStatus = switcherBean.switchStatus;
                    ToastUtils.showText(String.format(Locale.getDefault(), "%s%s了标记讲解开关",
                            switcherBean.userRole == 2 ? "管理员" : "主播", switcherBean.switchStatus == 1 ? "打开" : "关闭"));
                }
            }
            break;
            case MsgType.TYPE_MARK_EXPLAIN_DIALOG: {// 其他端弹了首次确认弹窗，告知我
                viewModel.detailResponse.alreadyPopMarkDialog = 1;
            }
            break;
            case MsgType.TYPE_LIVE_START: {
                LiveStartItemBean bean = LiveStartItemBean.parseServerMessage(message);
                if (bean != null) {
                    // 只有主播端检测，发现开播不是app端，则立即退出
                    if (bean.clientType != 1) {
                        ToastUtils.showText("PC端已开播，请重新进入管理端");
                        viewModel.finishLiveData.postValue(new FinishModel(false));
                    }
                    //更新字幕开关
                    if (null != viewModel.detailResponse) {
                        viewModel.detailResponse.hideSubtitleSwitch = bean.hideSubtitleSwitch;
                        viewModel.detailResponse.subtitleStatus = bean.subtitleStatus;
                        viewModel.updateToolsItemDisable(viewModel.detailResponse.isSubtitleGone(), BOpenCloseSubtitleItem.class);
                        viewModel.updateToolsItemIconTitle(viewModel.getSubtitleStatusIcon(), viewModel.getSubtitleStatusTitle(), BOpenCloseSubtitleItem.class);
                        if (null != viewModel.dialogManager) {
                            viewModel.dialogManager.updateToolsFragment(viewModel.getEnableTools());
                        }
                    }
                }
            }
            break;
            case MsgType.TYPE_ROOM_CONTROL: {
                OpItemBean bean = OpItemBean.parseServerMessage(message);
                if (bean != null) {
                    /*这里使用liveData而没有直接调用，是为了防止：直播中状态，当app息屏或退出到后台时，
                    PC端把暂停状态恢复为直播中，APP接收到信令后也会在息屏或后台状态下继续推流的问题*/
                    viewModel.suspendAndContinueLiveData.postValue(bean);
                }
            }
            break;
            case MsgType.TYPE_FINISH_LIVE:
            case MsgType.TYPE_LIVE_END:
                LiveFinishBean liveFinishBean = LiveFinishBean.parseServerMessage(message);
                viewModel.finishLiveData.postValue(new FinishModel(true, liveFinishBean));
                break;
            case MsgType.TYPE_ANCHOR_NOTICE: {// B端收到系统通知
                AnchorNoticeBean bean = AnchorNoticeBean.parseServerMessage(message);
                if (bean != null) {
                    viewModel.anchorNoticeLiveData.postValue(bean);
                }
            }
            break;
            case MsgType.TYPE_FLOW_PURCHASE: {// 直播中流量购买
                AnchorFlowPurchaseBean bean = AnchorFlowPurchaseBean.parseServerMessage(message);
                if (bean != null) {
                    if (bean.type == 2) {
                        viewModel.anchorNoticeLiveData.postValue(AnchorFlowPurchaseBean.parse2Notice(bean));
                    } else {
                        viewModel.detailResponse.showAddFlowButton = 1;
                        viewModel.showFlowIconLiveData.postValue(true);
                    }
                }
            }
            break;
            case MsgType.TYPE_BOSS_EXPLAIN_FINISH: {// 求讲解录制服务端主动结束
                BossExplainFinishItemBean bean = BossExplainFinishItemBean.parseServerMessage(message);
                if (bean != null) {
                    ExplainForLivingItemBean explainItemBean = viewModel.curExplainLiveData.getValue();
                    if (explainItemBean != null && TextUtils.equals(bean.encryptExplainId, explainItemBean.encryptExplainId)
                            && explainItemBean.explainTime > 0) {
                        viewModel.curExplainLiveData.postValue(null);
                    }
                }
            }
            break;
            case MsgType.TYPE_BOSS_LIVE_CALL_CONNECT: {// 主播处理连麦信息
                TLog.info("ConnectCall", "msgType-73-into");
                BossLiveCallConnectItemBean bean = BossLiveCallConnectItemBean.parseServerMessage(message);
                if (bean != null) {
                    TLog.info("ConnectCall", "msgType-73-callonUserId:%s，handleClient:%s,type:%d", bean.callonUserId, bean.handleClient, bean.type);
                    viewModel.callConnectStateLiveData.postValue(bean);
                }
            }
            break;
            case MsgType.TYPE_SUBTITLE_STATUS: {// 字幕状态变化
                SubtitleStatusItemBean bean = SubtitleStatusItemBean.parseServerMessage(message);
                if (null != bean && null != viewModel.detailResponse) {
                    viewModel.detailResponse.subtitleStatus = bean.subtitleStatus;
                    viewModel.updateToolsItemIconTitle(viewModel.getSubtitleStatusIcon(), viewModel.getSubtitleStatusTitle(), BOpenCloseSubtitleItem.class);
                    if (null != viewModel.dialogManager) {
                        viewModel.dialogManager.updateToolsFragment(viewModel.getEnableTools());
                    }
                }
            }
            break;
            case MsgType.TYPE_B_UPDATE_RECOMMENDED_JOB: {// 1209.711 b端 推职位卡状态更新
                CommentItemBean itemBean2 = ChatContractParser.parseServerMessage(message);
                viewModel.recommendingJobBean = itemBean2;
                viewModel.checkShowRecommend();
            }
            break;
            //1209.711 通知主播开始录制讲解
            case MsgType.TYPE_NOTIFY_B_START_EXPLAIN: {
                BossLiveExplainItemBean bossLiveExplainItemBean = BossLiveExplainItemBean.parseServerMessage(message);
                if (null != bossLiveExplainItemBean) {
                    viewModel.startExplainByRemote(viewModel.getJobByExplained(bossLiveExplainItemBean.encryptJobId));
                }
            }
            break;
            //1209.711  通知主播结束录制讲解
            case MsgType.TYPE_NOTIFY_B_END_EXPLAIN: {
                BossLiveExplainItemBean bossLiveExplainItemBean = BossLiveExplainItemBean.parseServerMessage(message);
                if (null != bossLiveExplainItemBean) {
                    viewModel.endExplainByRemote(bossLiveExplainItemBean);
                }
            }
            //1226.711 b端 直播间加热
            case MsgType.TYPE_B_UPDATE_NOTICE_HEAT: {
                AnchorNoticeBean noticeBean = AnchorNoticeBean.parseServerMessage(message);
                if (null != noticeBean) {
                    viewModel.noticeHeatLiveData.postValue(noticeBean);
                }
            }
            break;
            //1312.711 个人直播间支持交换电话和微信
            case MsgType.TYPE_B_UPDATE_EXCHANGE_STATUS: {
                LiveGeekPostStatusBean statusBean = LiveGeekPostStatusBean.parseServerMessage(message);
                if (null != statusBean) {
                    viewModel.geekPostStatusLiveData.postValue(statusBean);
                }
            }
            break;
            default: {
                CommentItemBean itemBean = ChatContractParser.parseServerMessage(message);
                if (itemBean != null) {
                    itemBean = BossAtUtil.sortCommentItemBean(itemBean);

                    CommentModel model = CommentModel.getInstance();
                    model.addItem(itemBean);
                    viewModel.commentLiveData.postValue(model);
                }
            }
            break;
        }
    }
}
