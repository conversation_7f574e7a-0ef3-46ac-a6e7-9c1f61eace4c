package com.hpbr.bosszhipin.live.campus.audience.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.Observer;
import androidx.lifecycle.OnLifecycleEvent;

import com.bszp.kernel.utils.ObserverChange;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.bean.CommentItemBean;
import com.hpbr.bosszhipin.live.bean.SpeakVideoBean;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.AtJobListShowModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.ClickApplaudModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.view.LikeAnimView;
import com.hpbr.bosszhipin.live.campus.audience.model.ExplainJobPlayModel;
import com.hpbr.bosszhipin.live.campus.audience.model.ProxyBossJobModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.CommentModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.ExplainChapterModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.ExplainJobModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.GuideLiveModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.SendMessageModel;
import com.hpbr.bosszhipin.live.campus.audience.util.AudienceGiftWrapper;
import com.hpbr.bosszhipin.live.campus.audience.util.AudienceLayoutUtil;
import com.hpbr.bosszhipin.live.campus.audience.util.AudienceLuckyDrawWrapper;
import com.hpbr.bosszhipin.live.campus.audience.view.CommentView;
import com.hpbr.bosszhipin.live.campus.audience.view.DialogManager;
import com.hpbr.bosszhipin.live.campus.audience.view.RoomInfoView;
import com.hpbr.bosszhipin.live.campus.audience.viewmodel.AudienceViewModel;
import com.hpbr.bosszhipin.live.campus.audience.weight.ChatBottomFunctionView;
import com.hpbr.bosszhipin.live.campus.audience.weight.GuideJobCardView;
import com.hpbr.bosszhipin.live.campus.audience.weight.HotRecruitJobCardView;
import com.hpbr.bosszhipin.live.campus.audience.weight.QuickQuestionView;
import com.hpbr.bosszhipin.live.campus.audience.weight.TopJobCardView;
import com.hpbr.bosszhipin.live.campus.audience.weight.TopJobHighCardView;
import com.hpbr.bosszhipin.live.campus.bean.ExplainParamBean;
import com.hpbr.bosszhipin.live.campus.listener.CommentItemCallback;
import com.hpbr.bosszhipin.live.campus.widget.SpeakVideoListView;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.export.LiveService;
import com.hpbr.bosszhipin.live.export.LiveSilenceRecord;
import com.hpbr.bosszhipin.live.export.bean.ReOpenLiveAudienceParamBean;
import com.hpbr.bosszhipin.live.geek.audience.adapter.RecruitGiftAdapter;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.GiftModel;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.RecruitJobModel;
import com.hpbr.bosszhipin.live.geek.audience.popup.LuckyDrawResultTipPopup;
import com.hpbr.bosszhipin.live.model.PlayVideoModel;
import com.hpbr.bosszhipin.live.net.bean.AtCityPositionBean;
import com.hpbr.bosszhipin.live.net.bean.AtJobPositionBean;
import com.hpbr.bosszhipin.live.net.bean.GeekQuickQuestionQueryBean;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.bean.LiveRecordBean;
import com.hpbr.bosszhipin.live.net.response.GeekQuickQuestionQueryResponse;
import com.hpbr.bosszhipin.live.net.response.GeekRecommendJobCardResponse;
import com.hpbr.bosszhipin.live.net.response.GeekRecruitDetailResponse;
import com.hpbr.bosszhipin.live.util.ICommonCallback;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveCommonUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.live.util.MsgType;
import com.hpbr.bosszhipin.live.widget.applaud.OnDrawApplaudListener;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.contacts.util.ReportUtil;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.SystemUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class CommentLifecycleObserver implements LifecycleObserver, View.OnClickListener {
    private static final String TAG = "CommentLifecycle";

    private final FragmentActivity activity;
    private final AudienceViewModel mViewModel;

    private final AudienceLiveFragment fragment;
    private final DialogManager dialogManager;
    private final CommentView view;
    private final RoomInfoView roomInfoView;
    private final ChatBottomFunctionView chatBottomFunctionView;
    private final QuickQuestionView quickQuestionView;

    private final AudienceLuckyDrawWrapper luckyDrawWrapper;
    private final AudienceGiftWrapper giftWrapper;
    private Runnable bossNormalRoomRunnable;
    private Runnable runnableDismissRecommendLabelForJobList;

    public CommentLifecycleObserver(AudienceLiveFragment fragment, AudienceViewModel mViewModel, ConstraintLayout rootView, RoomInfoView roomInfoView) {
        this.fragment = fragment;
        this.activity = fragment.getActivity();
        this.mViewModel = mViewModel;
        this.dialogManager = mViewModel.dialogManager;
        this.roomInfoView = roomInfoView;
        view = new CommentView(rootView);
        quickQuestionView = view.commentDisplayView.quickQuestionView;
        chatBottomFunctionView = dialogManager.getChatBottomFunctionView();
        luckyDrawWrapper = new AudienceLuckyDrawWrapper(fragment, mViewModel, view, roomInfoView);
        giftWrapper = new AudienceGiftWrapper(fragment, mViewModel, view);

        initView();
        initObserver();
    }

    private void initView() {
        view.rewardLayout.setAdapter(new RecruitGiftAdapter(activity, "c"));
        view.tvCommentInput.setOnClickListener(this);
        view.ivFullShrinkBottomArea.setOnClickListener(this);
        view.ivGift.setOnClickListener(this);
        view.clBtnPosition.setOnClickListener(this);
        view.btnInvite.setOnClickListener(this);
        view.ivPositionCompanyColleague.setOnClickListener(this);
        view.clBackToLive.setOnClickListener(this);
        view.llPositionNotLiving.setOnClickListener(this);

        // 导播引导卡片
        view.commentDisplayView.guideJobCardView.setActionListener(new GuideJobCardView.IOnCardActionListener() {
            @Override
            public void onCardClick(LiveRecordBean topLiveRecord) {
                LiveAnalysisUtil.dotLiveListPageBClickEx(mViewModel.dataCenter.liveRecordId, topLiveRecord.liveRecordId);
                LiveService.openAudienceActivity(activity, topLiveRecord.liveRecordId, mViewModel.dataCenter.liveRecordId, activity.getIntent().getExtras(), 17);
            }

            @Override
            public void onCloseBtnClick(GuideLiveModel guideLiveModel) {
                guideLiveModel.isShowTopLive = true;
                guideLiveModel.topLiveRecord = null;
                mViewModel.dataCenter.guideLiveLiveData.postValue(guideLiveModel);
            }
        });

        view.likeAnimView.setOnLongClickShakeListener(new LikeAnimView.OnLongClickShakeListener() {
            @Override
            public void onShake() {
                /*点赞按钮长按触发振动*/
                SystemUtils.vibrator(36L);
            }
        });

        setCommentContainerOnClick(true);
        initApplaudClick();
    }

    private void initObserver() {
        luckyDrawWrapper.initObserver();
        giftWrapper.initObserver();

        mViewModel.dataCenter.positionPopLiveData.observe(fragment, new ObserverChange<Long>() {
            @Override
            public void onChangedValue(Long topApplyJobId) {
                showNormalTopPositionCard();
            }
        });
        mViewModel.dataCenter.positionHighPopLiveData.observe(fragment, new ObserverChange<String>() {
            @Override
            public void onChangedValue(String topGroupJobId) {
                showHighTopGroupCard();
            }
        });
        mViewModel.dataCenter.commentModelLiveData.observe(fragment, new Observer<CommentModel>() {
            @Override
            public void onChanged(CommentModel commentModel) {
                if (mViewModel.dataCenter.detailResponse.isLiving()) {
                    if (!LList.isEmpty(commentModel.itemList)) {
                        if (commentModel.isNeedClear) { //onResume 重新连接时 重新创建 初始化 IM消息 会重新推送
                            view.commentDisplayView.clearData();
                            // 解决上下滑动，划回来，礼物偶现不消失问题
                            view.rewardLayout.clearAllGift();
                            commentModel.isNeedClear = false;
                        }
                        GiftModel giftModel = mViewModel.dataCenter.giftLiveData.getValue();
                        boolean isHighRoom = mViewModel.dataCenter.detailResponse.isHighLevelRoom();
                        for (CommentItemBean itemBean : commentModel.itemList) {
                            view.commentDisplayView.updateMessage(itemBean, isHighRoom);
                            boolean autoDownload = null != mViewModel.dataCenter.additionalInfoResponse && null != mViewModel.dataCenter.additionalInfoResponse.giftDownload
                                    && mViewModel.dataCenter.additionalInfoResponse.giftDownload.autoDownload;
                            view.checkShowGift(activity, mViewModel.dataCenter.liveRecordId, itemBean, giftModel, autoDownload, mViewModel.dataCenter.isShowPkVoteView());
                        }
                        commentModel.itemList.clear();
                    }
                } else if (mViewModel.dataCenter.detailResponse.isFinishLive()) {
                    view.commentDisplayView.setEnableLoadMore(false);
                    view.commentDisplayView.updateMessage(commentModel.isNeedClear, commentModel.itemList);
                    if (!LList.isEmpty(commentModel.itemList)) {
                        commentModel.itemList.clear();
                    }
                }

                if (commentModel.hideUIList) {
                    view.commentDisplayView.setVisibility(View.GONE);
                    view.clInputControlContainer.setVisibility(View.GONE);
                    roomInfoView.topOperationPlaceLayout.setVisibility(View.GONE);
                } else {
                    view.commentDisplayView.setVisibility(mViewModel.dataCenter.isSpeakVideoMode() ? View.GONE : View.VISIBLE);// 切片播放时，不展示弹幕
                    if (mViewModel.dataCenter.detailResponse.isLiving()) {
                        view.clInputControlContainer.setVisibility(View.VISIBLE);
                    }
                    roomInfoView.topOperationPlaceLayout.setVisibility(View.VISIBLE);
                }
            }
        });
        mViewModel.dataCenter.sendCommentLiveData.observe(fragment, new Observer<SendMessageModel>() {
            @Override
            public void onChanged(SendMessageModel model) {
                sendMessage(model.type, model.content, "");
            }
        });

        mViewModel.dataCenter.interactLiveData.observe(fragment, new Observer<GeekRecruitDetailResponse>() {
            @Override
            public void onChanged(GeekRecruitDetailResponse response) {
                roomInfoView.checkShowInteract(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.liveRecordId);
            }
        });
        mViewModel.dataCenter.proxyBossJobListLiveData.observe(fragment, model -> {
            updateJobDeliverProxyBoss();
        });
        /*直播中点击职位「看讲解」，播放直播切片视频*/
        mViewModel.dataCenter.explainSliceVideoLiveData.observe(fragment, new Observer<ExplainJobPlayModel>() {
            @Override
            public void onChanged(ExplainJobPlayModel explainJobPlayModel) {
                if (explainJobPlayModel == null) return;
                ExplainParamBean explainParamBean = new ExplainParamBean();
                explainParamBean.liveRecordId = mViewModel.dataCenter.liveRecordId;
                explainParamBean.brandName = mViewModel.dataCenter.detailResponse != null ? mViewModel.dataCenter.detailResponse.brandName : "";
                explainParamBean.brandLogo = mViewModel.dataCenter.detailResponse != null ? mViewModel.dataCenter.detailResponse.brandLogo : "";
                explainParamBean.playBackUrl = explainJobPlayModel.playBackUrl;
                explainParamBean.jobInfoBean = explainJobPlayModel.jobInfoBean;
                explainParamBean.isPortraitStream = mViewModel.dataCenter.detailResponse != null && mViewModel.dataCenter.detailResponse.isPortraitStream();
                explainParamBean.isProxyBoss = mViewModel.dataCenter.shareDataCenter.isProxyBoss;

                ReOpenLiveAudienceParamBean reOpenLiveAudienceParamBean = new ReOpenLiveAudienceParamBean();
                reOpenLiveAudienceParamBean.liveRecordId = mViewModel.dataCenter.liveRecordId;
                reOpenLiveAudienceParamBean.isProxyBoss = mViewModel.dataCenter.isProxyBoss;
                reOpenLiveAudienceParamBean.source = mViewModel.dataCenter.source;
                reOpenLiveAudienceParamBean.listSource = mViewModel.dataCenter.listSource;
                reOpenLiveAudienceParamBean.powerType = mViewModel.dataCenter.shareDataCenter.powerType;
                reOpenLiveAudienceParamBean.targetSpecialTagBean = mViewModel.dataCenter.shareDataCenter.targetSpecialTagBean;
                reOpenLiveAudienceParamBean.encryptRecommendJobId = mViewModel.dataCenter.encryptRecommendJobId;

                if (null != mViewModel.dialogManager && null != activity) {
                    mViewModel.dataCenter.isSeePlayBackShowing = true;
                    View liveView = mViewModel.dataCenter.getLiveView();
                    liveView.setVisibility(View.GONE);
                    mViewModel.dataCenter.muteLocalAudio(true);
                    mViewModel.dialogManager.dismissJobBottomSheetDialog();
                    mViewModel.dialogManager.showLiveExplainBottomSheetDialog(AppUtil.isLandscape(activity), explainParamBean,
                            reOpenLiveAudienceParamBean, new Runnable() {
                                @Override
                                public void run() {
                                    mViewModel.dataCenter.isSeePlayBackShowing = false;
                                    liveView.setVisibility(View.VISIBLE);
                                    mViewModel.dataCenter.muteLocalAudio(LiveSilenceRecord.get().isOpenSilence());
                                }
                            });
                }
            }
        });
        // 回看讲解职位
        mViewModel.dataCenter.explainJobLiveData.observe(fragment, new Observer<ExplainJobModel>() {
            @Override
            public void onChanged(ExplainJobModel explainModel) {
                checkShowBackToLive(explainModel != null && explainModel.jobInfo != null);
            }
        });
        // 回看讲解章节
        mViewModel.dataCenter.explainChapterLiveData.observe(fragment, new Observer<ExplainChapterModel>() {
            @Override
            public void onChanged(ExplainChapterModel explainModel) {
                checkShowBackToLive(explainModel != null && explainModel.chapterBean != null);
            }
        });
        // 直播间引导卡片
        mViewModel.dataCenter.guideLiveLiveData.observe(fragment, new Observer<GuideLiveModel>() {
            @Override
            public void onChanged(GuideLiveModel guideLiveModel) {
                if (mViewModel.dataCenter.detailResponse == null || activity == null) return;
                if (mViewModel.dataCenter.detailResponse.isGuideLive() && mViewModel.dataCenter.detailResponse.isLiving()) {
                    if (guideLiveModel != null) {
                        view.topJobCardView.endTopJobAnimatorSet();// 防止隐藏动画未执行完
                        view.clTopHighJobContainer.endTopJobAnimatorSet();
                        if (guideLiveModel.topLiveRecord == null || !guideLiveModel.isShowTopLive) {
                            view.commentDisplayView.guideJobCardView.dismissGuideLiveCard(true);
                        } else {
                            view.commentDisplayView.guideJobCardView.showGuideLiveCard(view.clBtnPosition, view.clInputControlContainer, guideLiveModel);
                            LiveAnalysisUtil.dotLiveListPageBExpose(mViewModel.dataCenter.liveRecordId, guideLiveModel.topLiveRecord.liveRecordId);
                        }
                    }
                } else {
                    view.commentDisplayView.guideJobCardView.dismissGuideLiveCard(false);
                }
            }
        });
        // 直播切片播放下一个视频
        mViewModel.dataCenter.playNextVideoLiveData.observe(fragment, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean play) {
                view.speakVideoListView.playNextVideo();
            }
        });
        // 投递前/投递后预设问题
        mViewModel.dataCenter.questionResponseLiveData.observe(fragment, new Observer<GeekQuickQuestionQueryResponse>() {
            @Override
            public void onChanged(GeekQuickQuestionQueryResponse response) {
                if (response == null || LList.isEmpty(response.presetBarrageList))
                    return;
                GeekRecruitDetailResponse detailResponse = mViewModel.dataCenter.detailResponse;
                boolean landscape = null != mViewModel.dialogManager && mViewModel.dialogManager.landscape;
                quickQuestionView.showQuickQuestion(response.presetBarrageList, mViewModel.dataCenter.liveRecordId, view.tvCommentInput,
                        detailResponse.deliveredNum, landscape, detailResponse.isLiving(),
                        (adapter, view, position) -> {
                            clickQuickQuestionItemSendMessage(adapter, position, false);
                        });
            }
        });
        mViewModel.dataCenter.shareDataCenter.logoListShowingLiveData.observe(fragment, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                handleViewAlpha(aBoolean);
                dialogManager.setShowTop(aBoolean);
            }
        });
        // 更新点赞数
        mViewModel.dataCenter.clickApplaudLiveData.observe(fragment, new Observer<ClickApplaudModel>() {
            @Override
            public void onChanged(ClickApplaudModel clickApplaudModel) {
                view.tvApplauseNum.setText(StringUtil.formatAudienceApplaud(clickApplaudModel.applaudCount));
            }
        });
        mViewModel.dataCenter.showBossDeliverReplyLiveData.observe(fragment, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (dialogManager.showBossDeliverReplyPopup(view.btnMore)) {
                    /*埋点：牛人在APP端观看直播中弹出boss投递回复的提醒气泡*/
                    LiveAnalysisUtil.campusLivePopReply(mViewModel.dataCenter.liveRecordId);
                }
            }
        });
        ContactManager.getInstance().observeContacts(fragment, new Observer<List<ContactBean>>() {
            @Override
            public void onChanged(List<ContactBean> contactBeans) {
                /*查询BOSS回复投递*/
                mViewModel.dataCenter.checkBossDeliverReply();
            }
        });
        // 刚刚看过或与你匹配或你关注过
        mViewModel.dataCenter.recommendJobLiveData.observe(fragment, new Observer<GeekRecommendJobCardResponse>() {
            @Override
            public void onChanged(GeekRecommendJobCardResponse jobCardResponse) {
                if (null != jobCardResponse) {
                    GeekRecruitDetailResponse detailResponse = mViewModel.dataCenter.detailResponse;
                    if (mViewModel.dataCenter.detailResponse.isLiving()) {
                        //横屏不出卡
                        if (null != activity && AppUtil.isLandscape(activity)) return;

                        view.clHotJobCardView.showHotRecruitJobPopup(view.clBtnPosition, view.clInputControlContainer, jobCardResponse,
                                detailResponse.canDeliver, detailResponse.isBlueRoom(), mViewModel.dataCenter.liveRecordId, detailResponse.isOnlineNormalLevelRoom());
                        //刚刚看过或与你匹配或你关注过
                        view.clHotJobCardView.setActionListener(new HotRecruitJobCardView.IOnCardActionListener() {
                            @Override
                            public void onCardClick(JobInfoBean jobInfo) {
                                // 跳转到职位详情页
                                mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, jobInfo, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                                LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState,
                                        jobInfo.securityId, LiveAnalysisUtil.getSourceByLabelType(jobInfo.labelType),
                                        mViewModel.dataCenter.getStreamType(), jobInfo.bossId, jobInfo.encryptJobGroupId);
                            }

                            @Override
                            public void onPostBtnClick(JobInfoBean jobInfo) {
                                if (UserManager.isBossRole()) {
                                    ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
                                    return;
                                }
                                if (!mViewModel.dataCenter.detailResponse.canDeliver || jobInfo.delivered)
                                    return; // 当禁止投递和已投递过的状态时，则不允许操作
                                if (jobInfo.isAtsPostType()) {
                                    dialogManager.showAtsPostDialog(jobInfo.applyOnlineUrl);
                                } else {
                                    /*显示投递简历时的二次确认弹框*/
                                    showDeliverResumeConfirmDialog(mViewModel.dataCenter.detailResponse, jobInfo);
                                }
                                LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, view.clHotJobCardView.getBtnTitle(), view.clHotJobCardView.getCardType());
                            }

                            @Override
                            public void onClickChatButton(JobInfoBean jobInfo) {
                                if (jobInfo == null) return;
                                handleClickChatButtonEvent(jobInfo, "recommendJobLiveData");
                            }

                            @Override
                            public void onClickSendButton(JobInfoBean jobInfo, boolean isPhone) {
                                if(isPhone){
                                    mViewModel.dataCenter.sendPhone(jobInfo);
                                }else{
                                    mViewModel.dataCenter.sendWechat(jobInfo);
                                }
                            }

                            @Override
                            public void onDismiss() {
                                mViewModel.dataCenter.isHotJobShowing = false;
                                //检查在该卡显示期间，是否有正在讲解卡过来，有的话需要显示出来
                                String topJobGroupId = mViewModel.dataCenter.detailResponse.topJobGroupId;
                                long topApplyJobId = mViewModel.dataCenter.detailResponse.topApplyJobId;

                                if (topApplyJobId > 0) {
                                    showNormalTopPositionCard();
                                } else if (!TextUtils.isEmpty(topJobGroupId)) {
                                    showHighTopGroupCard();
                                }

                                //所有弹卡（讲解弹卡/推荐弹卡/职位卡片）消失时展示一次
                                view.isSetResAnimationPosition = false;
                                view.showPositionResAnimation();
                            }
                        });
                    }
                }

            }
        });
        //推职位
        mViewModel.dataCenter.recommendBossJobLiveData.observe(fragment, new Observer<CommentItemBean>() {
            @Override
            public void onChanged(CommentItemBean itemBean) {
                showRecommendBossJobCard(itemBean);
            }
        });
        //刚刚看过卡，投递后更新状态
        mViewModel.dataCenter.positionNotifyLiveData.observe(fragment, new Observer<JobInfoBean>() {
            @Override
            public void onChanged(JobInfoBean jobInfoBean) {
                GeekRecruitDetailResponse detailResponse = mViewModel.dataCenter.detailResponse;
                if (null != detailResponse && detailResponse.isLiving()) {
                    if (null != view.clHotJobCardView) {
                        view.clHotJobCardView.setTopJobData(jobInfoBean, detailResponse.canDeliver, detailResponse.isBlueRoom(),
                                detailResponse.isOnlineNormalLevelRoom(), mViewModel.dataCenter.liveRecordId);
                    }
                    if (null != view.topJobCardView) {
                        view.topJobCardView.setTopJobData(jobInfoBean, detailResponse.canDeliver, detailResponse.isBlueRoom(),
                                detailResponse.isOnlineNormalLevelRoom(), mViewModel.dataCenter.liveRecordId);
                    }
                } else if (null != detailResponse && detailResponse.isFinishLive()) {
                    if (null != view.speakVideoListView && null != jobInfoBean) {
                        view.speakVideoListView.getCardView().setTopJobLiveFinishData(jobInfoBean, detailResponse.canDeliver, detailResponse.isBlueRoom(),
                                detailResponse.isOnlineNormalLevelRoom(), mViewModel.dataCenter.liveRecordId);
                        view.speakVideoListView.notifyItem(jobInfoBean);
                    }
                }
            }
        });
    }

    public void initData() {
        checkShowCommentInput();
        view.commentDisplayView.initAdapter(mViewModel.dataCenter.getSceneType(), mViewModel.dataCenter.detailResponse.roomPowerLevel, new CommentItemCallback() {
            @Override
            public void onItemClick(int action, CommentItemBean commentItemBean, int position, View itemView) {
                if (!mViewModel.dataCenter.detailResponse.isLiving()) return;

                if (commentItemBean.msgType == MsgType.TYPE_RESUME && !commentItemBean.isMyselfJob) {// Boss自家职位不可点击
                    if (mViewModel.dataCenter.isSceneTypeBossAudienceOnline()) {/*BOSS作为观众 观看线上直播时，点击后提示「当前为BOSS身份，无法查看职位」*/
                        ToastUtils.showText("当前为BOSS身份，无法查看职位");
                        return;
                    }
                    // 跳转到职位详情页
                    if (!TextUtils.isEmpty(commentItemBean.jobSecurityId)) {
                        JobInfoBean jobInfoBean = new JobInfoBean();
                        jobInfoBean.securityId = commentItemBean.jobSecurityId;
                        jobInfoBean.encryptJobGroupId = commentItemBean.encryptJobGroupId;
                        jobInfoBean.encryptJobId = commentItemBean.encryptJobId;
                        jobInfoBean.bossId = commentItemBean.jobCreatorId;
                        mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, jobInfoBean, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                        LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState, jobInfoBean.securityId,
                                CampusConstant.ClickJumpJobDetailFrom.FROM_DELIVER_RESUME, mViewModel.dataCenter.getStreamType(), jobInfoBean.bossId, jobInfoBean.encryptJobGroupId);
                    }
                } else if (commentItemBean.msgType == MsgType.TYPE_BOSS_REQUEST_EXPLAIN || commentItemBean.msgType == MsgType.TYPE_BOSS_JOB_GROUP_REQUEST_EXPLAIN) {
                    if (mViewModel.dataCenter.isSceneTypeBossAudienceOnline()) {/*BOSS作为观众 观看线上直播时，点击后提示「当前为BOSS身份，无法查看职位」*/
                        ToastUtils.showText("当前为BOSS身份，无法查看职位");
                        return;
                    }
                    jump2JobPage(commentItemBean, CampusConstant.ClickJumpJobDetailFrom.FROM_BEG_EXPLAIN);
                }
            }

            @Override
            public void onItemChildClick(int action, CommentItemBean commentItemBean) {
                if (!mViewModel.dataCenter.detailResponse.isLiving()) return;

                if (action == CommentItemCallback.Action.ACTION_CLICK_BRAND) {
                    if (mViewModel.dataCenter.detailResponse.isHighLevelRoom()) {// 高级直播间
                        if (!mViewModel.dataCenter.isFromBrand && !TextUtils.isEmpty(commentItemBean.brandLandingUrl)) {
                            // 跳转到高级品牌页
                            new ZPManager(activity, commentItemBean.brandLandingUrl).handler();
                        }
                        fragment.doExitRoom();
                    } else {
                        // 跳转到普通品牌页
                        new ZPManager(activity, commentItemBean.brandLandingUrl).handler();
                    }
                } else if (action == CommentItemCallback.Action.ACTION_CLICK_BRAND_SUBSCRIBE) {
                    mViewModel.dataCenter.brandSubscribe(null, 0, 2);
                }
            }

            @Override
            public void onClickAtPosition(int action, CommentItemBean commentItemBean, AtJobPositionBean positionBean) {
                if (!mViewModel.dataCenter.detailResponse.isLiving()) return;

                LiveAnalysisUtil.livePageBAtCk(mViewModel.dataCenter.liveRecordId, positionBean.jobSecurityId);
                if (mViewModel.dataCenter.isSceneTypeBossAudienceOnline()) {/*BOSS作为观众 观看线上直播时，点击后提示「当前为BOSS身份，无法查看职位」*/
                    ToastUtils.showText("当前为BOSS身份，无法查看职位");
                    return;
                }
                if (positionBean.isAtGroup) {
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(mViewModel.dataCenter.getJobBottomSheetDialogHeight(),
                            RecruitJobModel.TAB_TYPE_POSITION, positionBean.encryptJobGroupId)
                            .setEncryptBrandId(commentItemBean.encryptBrandId)
                            .setClickJumpJobListFrom(CampusConstant.ClickJumpJobListFrom.FROM_AT_BRAND));
                } else {
                    commentItemBean.jobSecurityId = positionBean.jobSecurityId;
                    commentItemBean.encryptJobGroupId = positionBean.encryptJobGroupId;
                    commentItemBean.encryptJobId = positionBean.encryptApplyJobId;
                    jump2JobPage(commentItemBean, CampusConstant.ClickJumpJobDetailFrom.FROM_AT_JOB);
                }
            }

            @Override
            public void onClickCityLocation(int action, AtCityPositionBean positionBean) {
                if (!mViewModel.dataCenter.detailResponse.isLiving()) return;
                jumpToCityTab(positionBean.cityCode);
            }

            @Override
            public void onClickAddOne(int action, CommentItemBean commentItemBean, int position) {
                mViewModel.dataCenter.messageClickAddOne(commentItemBean, new ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        commentItemBean.echoState = 2;
                        if (view.commentDisplayView != null) {
                            view.commentDisplayView.notifyItemChanged(position);
                        }
                    }
                });
                if (action == CommentItemCallback.Action.ACTION_CLICK_REQUEST_EXPLAIN) {
                    LiveAnalysisUtil.dotAchievementPlusOneClick(mViewModel.dataCenter.liveRecordId, commentItemBean.problem, 1);
                } else {
                    LiveAnalysisUtil.dotAchievementPlusOneClick(mViewModel.dataCenter.liveRecordId, commentItemBean.msg, 2);
                }
            }

            // 跳转到职位详情页
            private void jump2JobPage(CommentItemBean itemBean, String from) {
                if (itemBean.msgType == MsgType.TYPE_BOSS_JOB_GROUP_REQUEST_EXPLAIN) {
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(mViewModel.dataCenter.getJobBottomSheetDialogHeight(), RecruitJobModel.TAB_TYPE_POSITION, itemBean.encryptJobGroupId)
                            .setClickJumpJobListFrom(CampusConstant.ClickJumpJobListFrom.FROM_BULLET_BRAND)
                            .setEncryptBrandId(itemBean.encryptBrandId));
                } else if (!TextUtils.isEmpty(itemBean.jobSecurityId)) {
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, CommentItemBean.parseJobInfoBean(itemBean), mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                    LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState,
                            itemBean.securityId, from, mViewModel.dataCenter.getStreamType(), itemBean.jobCreatorId, itemBean.encryptJobGroupId);
                }
            }

            /**
             * 跳转到职位列表弹层 并切换至 「城市」tab下
             */
            private void jumpToCityTab(String cityCode) {
                mViewModel.showJobBottomSheetDialog(new RecruitJobModel(mViewModel.dataCenter.getJobBottomSheetDialogHeight(), RecruitJobModel.TAB_TYPE_CITY, cityCode)
                        .setClickJumpJobListFrom(mViewModel.dataCenter.detailResponse.isBlueRoom() ? CampusConstant.ClickJumpJobListFrom.FROM_BLUE_ROOM
                                : CampusConstant.ClickJumpJobListFrom.FROM_CLICK_AT_CITY).setClickCityCode(cityCode));
            }

            @NonNull
            @Override
            public Context getContext() {
                return activity;
            }
        });
        mViewModel.dataCenter.revertComments();
        /*查询BOSS回复投递*/
        mViewModel.dataCenter.checkBossDeliverReply();

        if (mViewModel.dataCenter.isProxyBoss && mViewModel.dataCenter.detailResponse.isLiving()
                && !mViewModel.dataCenter.shareDataCenter.fromSmallWindow) {/*本场BOSS身份进入直播间 && 不是从小窗进入,弹出Toast提示*/
            ToastUtils.showText("以本场BOSS身份，与求职者弹幕互动吧");
        }

        //公司同事-第一次进去弹出投递牛人提示
        if (mViewModel.dataCenter.shareDataCenter.isThisLiveBossOrProxyBoss()) {
            if (null == bossNormalRoomRunnable) {
                bossNormalRoomRunnable = new Runnable() {
                    @Override
                    public void run() {
                        if (null != dialogManager) {
                            //更改输入框提示文字
                            view.tvCommentInput.setText("点击发送消息");
                            chatBottomFunctionView.setInputHint("发送消息吸引更多牛人投递");
                            //显示提示window
                            LiveAnalysisUtil.dotCampusBubblePopExpo(mViewModel.dataCenter.liveRecordId);
                            //如果显示过‘邀’提示，再显示职位提示
                            boolean isShowed = SpManager.get().user().getBoolean(CampusConstant.KEY_USER_SHOW_INVITE_HINT + "_" + mViewModel.dataCenter.liveRecordId, false);
                            if (mViewModel.dataCenter.isShowInviteBtn() && !isShowed) {
                                dialogManager.showInviteHintTipPopup(view.btnInvite, mViewModel.dataCenter.liveRecordId);
                            } else {
                                dialogManager.showCompanyColleagueDeliverHintTipPopup(view.clBtnPosition, mViewModel.dataCenter.isProxyBoss, mViewModel.dataCenter.liveRecordId);
                            }
                        }
                    }
                };
            }
            Utils.runOnUiThreadDelayed(bossNormalRoomRunnable, 200);
        }
    }

    public void dispatchLiveStatus() {
        handleLiveCommentContainerViewOrientation();
        AudienceLayoutUtil.handleLiveCommentStyle(activity, view.commentDisplayView.cl_container, chatBottomFunctionView, mViewModel.dataCenter.getScrnOrient());
        GeekRecruitDetailResponse geekResp = mViewModel.dataCenter.detailResponse;
        TLog.debug(TAG, "dispatchLiveStatus====%s", geekResp.liveState);
        switch (geekResp.liveState) {
            case LiveState.PUBLISHING: // 宣讲中
                // 企业宣传视频正在播放，注意非企业视频播放没有break，走processLiving()
                if (mViewModel.dataCenter.openingVideoPlaying) {
                    break;
                }
            case LiveState.PAUSED: // 暂停中
                processLiving();
                break;
            case LiveState.FINISH: // 已结束
                processFinish();
                break;
            case LiveState.DELAY: // 超时完成
                processFinishTimeout();
                break;
            default:
                break;
        }
        // 点击编辑框外部，可以消失
        chatBottomFunctionView.setOutAreaHide(geekResp.isPreLive() || geekResp.isDelay());
    }

    /**
     * 直播中
     */
    private void processLiving() {
        view.speakVideoListView.setVisibility(View.GONE);
        view.llPositionNotLiving.setVisibility(View.GONE);
        /*双击点赞区域*/
        view.applaudTouchView.setVisibility(View.VISIBLE);
        /*公司同事右侧职位图标*/
        int positionCnt = mViewModel.dataCenter.detailResponse.positionCnt;
        view.ivPositionCompanyColleague.setVisibility(mViewModel.dataCenter.shareDataCenter.isThisLiveBossOrProxyBoss() && positionCnt > 0 ? View.VISIBLE : View.GONE);
        /*邀按钮，显示逻辑*/
        boolean isShowInvite = mViewModel.dataCenter.isShowInviteBtn() && mViewModel.dataCenter.shareDataCenter.isThisLiveBossOrProxyBoss() && positionCnt > 0;
        view.btnInvite.setVisibility(isShowInvite ? View.VISIBLE : View.GONE);
        if (isShowInvite) {
            view.tvApplauseNum.setVisibility(View.GONE);
            view.ivApplause.setVisibility(View.GONE);
        }
        mViewModel.dataCenter.initComments();
        view.checkShowPositionLiving(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.isProxyBoss,
                mViewModel.dataCenter.shareDataCenter.isThisLiveBossNormalRoom(), mViewModel.dataCenter.isShowInviteBtn());
        view.checkShowInputControlContainer(activity, mViewModel.dataCenter.detailResponse, dialogManager.isShowSoft());
        /*直播需要显示点赞按钮*/
        view.tvApplauseNum.setText(StringUtil.formatAudienceApplaud(mViewModel.dataCenter.detailResponse.applaudCount));
        view.likeAnimView.startAutoLike();
        view.commentDisplayView.showEnterRoom();// 直播中直接显示出欢迎语，防止UI抖动
    }

    private void processFinish() {
        setCommentContainerOnClick(false);
        onDestroy();
        setPlayBack();
        view.checkShowPositionNotLiving(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.isProxyBoss);
        view.checkShowInputControlContainer(activity, mViewModel.dataCenter.detailResponse, dialogManager.isShowSoft());
        view.rewardLayout.setVisibility(View.GONE);
        view.likeAnimView.setVisibility(View.GONE);
        view.applaudTouchView.setVisibility(View.GONE);
        view.ivPositionCompanyColleague.setVisibility(View.GONE);
        view.topJobCardView.dismissTopJobPopup(false);
        view.clTopHighJobContainer.dismissTopJobPopup(false);
        view.clHotJobCardView.dismissTopJobPopup(false);
        view.commentDisplayView.quickQuestionView.cancelQuickQuestionTimerAndDismiss();
    }

    private void processFinishTimeout() {
        view.speakVideoListView.setVisibility(View.GONE);
        view.applaudTouchView.setVisibility(View.GONE);
        view.topJobCardView.dismissTopJobPopup(false);
        view.clTopHighJobContainer.dismissTopJobPopup(false);
        view.clHotJobCardView.dismissTopJobPopup(false);
        setCommentContainerOnClick(false);
        view.checkShowPositionNotLiving(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.isProxyBoss);
        view.checkShowInputControlContainer(activity, mViewModel.dataCenter.detailResponse, dialogManager.isShowSoft());
        onDestroy();
    }

    public void onConfigurationChanged(boolean landscape) {
        if (mViewModel.dataCenter.detailResponse == null) return;
        view.onConfigurationChanged(landscape);

        view.clCommentContainer.setVisibility(View.VISIBLE);
        if (landscape) {
            if (mViewModel.dataCenter.detailResponse.isLiving()) {
                view.clBtnPosition.setVisibility(View.GONE);
                view.btnInvite.setVisibility(View.GONE);
                view.clCommentInput.setVisibility(View.GONE);
            } else if (mViewModel.dataCenter.detailResponse.isFinishLive()) {
                view.llPositionNotLiving.setVisibility(View.GONE);
            }
            view.clHotJobCardView.dismissTopJobPopup(false);
            view.topJobCardView.dismissTopJobPopup(false);
            view.speakVideoListView.setVisibility(View.GONE);// 隐藏「直播切片」View
            view.clTopHighJobContainer.dismissTopJobPopup(false);
        } else {
            if (mViewModel.dataCenter.detailResponse.isLiving()) {
                view.checkShowPositionLiving(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.isProxyBoss,
                        mViewModel.dataCenter.shareDataCenter.isThisLiveBossNormalRoom(), mViewModel.dataCenter.isShowInviteBtn());
                checkShowCommentInput();
            } else if (mViewModel.dataCenter.detailResponse.isFinishLive()) {
                view.checkShowPositionNotLiving(activity, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.isProxyBoss);
                JobInfoBean jobCardLiveFinish = mViewModel.dataCenter.getJobCardLiveFinish();
                boolean isShowJobCard = null != jobCardLiveFinish && !mViewModel.dataCenter.detailResponse.outTimeLimit;
                view.speakVideoListView.setVisibility(mViewModel.dataCenter.isNeedShowSpeakVideoListView() || isShowJobCard ? View.VISIBLE : View.GONE);
            }
            view.checkShowInputControlContainer(activity, mViewModel.dataCenter.detailResponse, dialogManager.isShowSoft());
        }

        if (!AppUtil.isLandscape(activity) && mViewModel.dataCenter.detailResponse.isGuideLive()
                && mViewModel.dataCenter.guideLiveLiveData.getValue() != null
                && mViewModel.dataCenter.guideLiveLiveData.getValue().isShowTopLive
                && mViewModel.dataCenter.guideLiveLiveData.getValue().topLiveRecord != null
                && mViewModel.dataCenter.detailResponse.isLiving()) {
            view.commentDisplayView.guideJobCardView.setVisibility(View.VISIBLE);
        } else {
            view.commentDisplayView.guideJobCardView.setVisibility(View.GONE);
        }

        handleLiveCommentContainerViewOrientation();
        AudienceLayoutUtil.handleLiveCommentViewOrientation(activity, view.clCommentContainer, mViewModel.dataCenter.detailResponse, mViewModel.dataCenter.shareDataCenter.screenHeight);
        view.commentDisplayView.onConfigurationChanged(landscape);

        AudienceLayoutUtil.handleLiveCommentStyle(activity, view.commentDisplayView.cl_container, chatBottomFunctionView, mViewModel.dataCenter.getScrnOrient());

        // 横竖屏之后，都是默认显示所有 UI，所以默认重置一下标记，否则第一次按钮点击不起作用
        CommentModel commentModel = mViewModel.dataCenter.getCommentModel();
        commentModel.hideUIList = false;
        mViewModel.dataCenter.commentModelLiveData.postValue(commentModel);

        dialogManager.onConfigurationChanged(landscape);

        view.rewardLayout.updateLandscape(landscape);
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockClickEvent()) return;
        if (v == view.tvCommentInput) {
            if (mViewModel.dataCenter.detailResponse.isShutUp()) {
                ToastUtils.showText("观众禁言模式开启，暂时不能提问哦");
                LiveAnalysisUtil.dotCampusLivePageWordprohibited(mViewModel.dataCenter.liveRecordId);
                return;
            }
            dialogManager.showChatBottomView(0);
        } else if (v == view.ivFullShrinkBottomArea) {
            AppUtil.setRequestedOrientation(activity, false);
        } else if (v == view.ivGift) {
            mViewModel.dataCenter.showGiftDialogLiveData.setValue(true);
        } else if (v == view.clBtnPosition) {
            /*BOSS身份&&非直播学院非公司同事&&非代播*/
            if (mViewModel.dataCenter.isBossAndNotLiveCollegeNotCompanyColleague() && (UserManager.isBossRole() && !mViewModel.dataCenter.isProxyBoss)) {
                ToastUtils.showText("当前为BOSS身份，无法查看职位");
                return;
            }
            //同公司boss进普通直播间 「显示投递牛人样式，点击展开投递牛人列表」
            if (mViewModel.dataCenter.shareDataCenter.isThisLiveBossOrProxyBoss()) {
                dialogManager.showCompanyColleagueDeliverDialog();
                LiveAnalysisUtil.dotManageJobBossDeliverList(mViewModel.dataCenter.liveRecordId);
                return;
            }
            // 跳转到职位列表页
            GeekRecruitDetailResponse response = mViewModel.dataCenter.detailResponse;
            if (response.isGuideLive()) {
                LiveAnalysisUtil.liveTabChange(mViewModel.dataCenter.liveRecordId, response.startTime, "专栏直播",
                        response.liveState, "", mViewModel.dataCenter.getStreamType());
                dialogManager.showGuideBottomSheetDialog(mViewModel.dataCenter.getJobBottomSheetDialogHeight(), AppUtil.isLandscape(activity));
            } else {
                JobInfoBean jobInfoBeanWhenOnlyOneJob = mViewModel.dataCenter.getJobInfoBeanWhenOnlyOneJob();
                mViewModel.showJobBottomSheetDialog(new RecruitJobModel(null != jobInfoBeanWhenOnlyOneJob, jobInfoBeanWhenOnlyOneJob, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                LiveAnalysisUtil.liveTabChange(response.liveRecordId, response.startTime,
                        (response.commonConfig != null && response.commonConfig.showResumeSendBtn) ? "投递简历" : "职位列表",
                        response.liveState, "", mViewModel.dataCenter.getStreamType());
            }
        } else if (v == view.ivPositionCompanyColleague) {//公司同事进普通直播间-右侧职位图标「蓝色小包袱」
            /*代播*/
            if (mViewModel.dataCenter.isProxyBoss) {
                dialogManager.showProxyBossBoardDialog();//显示「代播BOSS」看板弹层
            } else {
                if (mViewModel.dataCenter.isBossAndNotLiveCollegeNotCompanyColleague()) {/*BOSS身份&&非直播学院非公司同事*/
                    ToastUtils.showText("当前为BOSS身份，无法查看职位");
                    return;
                }
                if (!mViewModel.dataCenter.shareDataCenter.isThisLiveBossNormalRoom()) {/*兜底*/
                    ToastUtils.showText("非公司同事，无法查看");
                    return;
                }
                // 跳转到职位列表页
                GeekRecruitDetailResponse response = mViewModel.dataCenter.detailResponse;
                if (response.isGuideLive()) {
                    LiveAnalysisUtil.liveTabChange(mViewModel.dataCenter.liveRecordId, response.startTime, "专栏直播",
                            response.liveState, "", mViewModel.dataCenter.getStreamType());
                    dialogManager.showGuideBottomSheetDialog(mViewModel.dataCenter.getJobBottomSheetDialogHeight(), AppUtil.isLandscape(activity));
                } else {
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(null, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                    LiveAnalysisUtil.liveTabChange(response.liveRecordId, response.startTime,
                            (response.commonConfig != null && response.commonConfig.showResumeSendBtn) ? "投递简历" : "职位列表",
                            response.liveState, "", mViewModel.dataCenter.getStreamType());
                }
            }

        } else if (v == view.clBackToLive) {// 回到直播中
            mViewModel.dataCenter.exitBackLiveMode();
        } else if (v == view.llPositionNotLiving) {
            if (mViewModel.dataCenter.isBossAndNotLiveCollegeNotCompanyColleague()) {/*BOSS身份&&非直播学院*/
                ToastUtils.showText("当前为BOSS身份，无法查看职位");
                return;
            }

            // 显示职位列表页
            if (mViewModel.dataCenter.detailResponse.isGuideLive()) {
                dialogManager.showGuideBottomSheetDialog(mViewModel.dataCenter.getJobBottomSheetDialogHeight(), AppUtil.isLandscape(activity));
            } else {
                JobInfoBean jobInfoBeanWhenOnlyOneJob = mViewModel.dataCenter.getJobInfoBeanWhenOnlyOneJobLiveFinish();
                mViewModel.showJobBottomSheetDialog(new RecruitJobModel(null != jobInfoBeanWhenOnlyOneJob, jobInfoBeanWhenOnlyOneJob, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));

                if (mViewModel.dataCenter.detailResponse.commonConfig == null) return;
                LiveAnalysisUtil.liveTabChange(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.startTime,
                        mViewModel.dataCenter.detailResponse.commonConfig.showResumeSendBtn ? "投递简历" : "职位列表",
                        mViewModel.dataCenter.detailResponse.liveState, "", mViewModel.dataCenter.getStreamType());
            }
        } else if (v == view.btnInvite) {
            dialogManager.showInviteDeliverBottomSheetDialog(AppUtil.isLandscape(activity), mViewModel.dataCenter.liveRecordId);
        }
    }

    private void showNormalTopPositionCard() {
        if (mViewModel.dataCenter.detailResponse == null) return;
        if (mViewModel.dataCenter.detailResponse.isLiving()) {
            if (mViewModel.dataCenter.isHotJobShowing) {//职位卡显示期间，来的置顶职位，职位卡消失需要显示出来
                TLog.debug(TAG, "hot card showing normal top card into");
            } else {
                //可能存在推荐卡展示，复用的同一个卡
                if (null != view.clHotJobCardView && view.clHotJobCardView.isVisible()) {
                    view.clHotJobCardView.dismissTopJobPopup();
                }
                checkShowTopJobCard(mViewModel.dataCenter.detailResponse);
            }
        } else {
            view.topJobCardView.dismissTopJobPopup();
        }
    }

    /**
     * 推职位卡
     */
    private void showRecommendBossJobCard(CommentItemBean itemBean) {
        if (mViewModel.dataCenter.detailResponse == null || null == itemBean) return;
        if (mViewModel.dataCenter.detailResponse.isLiving()) {
            //--1、弹卡--检查在该卡显示期间，是否有正在讲解卡过来，有的话需要显示出来
            String topJobGroupId = mViewModel.dataCenter.detailResponse.topJobGroupId;
            long topApplyJobId = mViewModel.dataCenter.detailResponse.topApplyJobId;
            if (mViewModel.dataCenter.isHotJobShowing || !TextUtils.isEmpty(topJobGroupId) || topApplyJobId > 0) {
                //有职位卡，讲解卡，推职位卡不显示
                TLog.debug(TAG, "hot card showing high top card into");
            } else {
                List<JobInfoBean> jobList = mViewModel.dataCenter.detailResponse.jobList;
                //横屏不出卡
                if (AppUtil.isLandscape(activity)) return;

                if (LList.getCount(jobList) > 0) {
                    JobInfoBean recommendJobBean = null;
                    for (JobInfoBean jobInfoBean : jobList) {
                        if (null == jobInfoBean) continue;
                        if (jobInfoBean.encryptJobId.equals(itemBean.encryptJobId)) {
                            recommendJobBean = jobInfoBean;
                        }
                    }
                    if (null != recommendJobBean) {
                        recommendJobBean.labelType = 4;
                        checkShowRecommendBossJobCard(itemBean, recommendJobBean);
                    }
                }
            }

            //--2、更新职位列表状态--如果没有正在讲解，职位列表中增加推职位角标
            if ((TextUtils.isEmpty(topJobGroupId) && topApplyJobId == 0)) {
                boolean isShowRecommend = false;
                // 高级直播间不做调整
                if (!mViewModel.dataCenter.detailResponse.isHighLevelRoom() && !LList.isEmpty(mViewModel.dataCenter.detailResponse.jobList)) {
                    for (JobInfoBean jobInfo : mViewModel.dataCenter.detailResponse.jobList) {
                        if (jobInfo == null) continue;
                        jobInfo.isRecommend = jobInfo.id == itemBean.jobId && itemBean.jobId > 0;
                        if (jobInfo.isRecommend) {
                            isShowRecommend = true;
                        }
                    }
                }
                mViewModel.dataCenter.positionLiveData.postValue(mViewModel.dataCenter.detailResponse);
                //清空之前延时
                if (null != runnableDismissRecommendLabelForJobList) {
                    Utils.removeHandlerCallbacks(runnableDismissRecommendLabelForJobList);
                    runnableDismissRecommendLabelForJobList = null;
                }
                //如果显示了推荐卡标签，倒计时结束后要，还原回来，太几麻烦了，后台不加消失的信令
                if (isShowRecommend) {
                    runnableDismissRecommendLabelForJobList = new Runnable() {
                        @Override
                        public void run() {
                            // 只还原这一个推职位，其他的别动，防止其他信令推职位，冲突
                            if (!mViewModel.dataCenter.detailResponse.isHighLevelRoom() && !LList.isEmpty(mViewModel.dataCenter.detailResponse.jobList)) {
                                for (JobInfoBean jobInfo : mViewModel.dataCenter.detailResponse.jobList) {
                                    if (jobInfo == null) continue;
                                    if (jobInfo.id == itemBean.jobId && itemBean.jobId > 0) {
                                        jobInfo.isRecommend = false;
                                    }
                                }
                            }
                            mViewModel.dataCenter.positionLiveData.postValue(mViewModel.dataCenter.detailResponse);
                        }
                    };
                    Utils.runOnUiThreadDelayed(runnableDismissRecommendLabelForJobList, itemBean.showTime * 1000);
                }
            }
        }
    }

    private void checkShowRecommendBossJobCard(CommentItemBean itemBean, JobInfoBean jobInfoBean) {
        GeekRecruitDetailResponse detailResponse = mViewModel.dataCenter.detailResponse;
        if (null != itemBean && !TextUtils.isEmpty(itemBean.encryptJobId) && mViewModel.dataCenter.detailResponse.isLiving()) {
            view.clHotJobCardView.showRecommendedJobPopup(view.clBtnPosition, view.clInputControlContainer, jobInfoBean,
                    detailResponse.canDeliver, detailResponse.isBlueRoom(), mViewModel.dataCenter.liveRecordId, itemBean,
                    detailResponse.isOnlineNormalLevelRoom());
            //推职位
            view.clHotJobCardView.setActionListener(new HotRecruitJobCardView.IOnCardActionListener() {
                @Override
                public void onCardClick(JobInfoBean jobInfo) {
                    // 跳转到职位详情页
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, jobInfo, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                    LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState,
                            jobInfo.securityId, LiveAnalysisUtil.getSourceByLabelType(jobInfo.labelType),
                            mViewModel.dataCenter.getStreamType(), jobInfo.bossId, jobInfo.encryptJobGroupId);
                }

                @Override
                public void onPostBtnClick(JobInfoBean jobInfo) {
                    if (UserManager.isBossRole()) {
                        ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
                        return;
                    }
                    if (!mViewModel.dataCenter.detailResponse.canDeliver || jobInfo.delivered)
                        return; // 当禁止投递和已投递过的状态时，则不允许操作
                    if (jobInfo.isAtsPostType()) {
                        dialogManager.showAtsPostDialog(jobInfo.applyOnlineUrl);
                    } else {
                        /*显示投递简历时的二次确认弹框*/
                        showDeliverResumeConfirmDialog(mViewModel.dataCenter.detailResponse, jobInfo);
                    }
                    LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, view.clHotJobCardView.getBtnTitle(), view.clHotJobCardView.getCardType());
                }

                @Override
                public void onClickChatButton(JobInfoBean jobInfo) {
                    if (jobInfoBean == null) return;
                    handleClickChatButtonEvent(jobInfoBean, "checkShowRecommendBossJobCard");
                }

                @Override
                public void onClickSendButton(JobInfoBean jobInfo, boolean isPhone) {
                    if(isPhone){
                        mViewModel.dataCenter.sendPhone(jobInfo);
                    }else{
                        mViewModel.dataCenter.sendWechat(jobInfo);
                    }
                }

                @Override
                public void onDismiss() {
                    mViewModel.dataCenter.recommendBossJobLiveData.setValue(null);

                    //所有弹卡（讲解弹卡/推荐弹卡/职位卡片）消失时展示一次
                    view.isSetResAnimationPosition = false;
                    view.showPositionResAnimation();
                }
            });
        } else {
            view.clHotJobCardView.dismissTopJobPopup();
        }
    }

    private void showHighTopGroupCard() {
        if (mViewModel.dataCenter.detailResponse == null) return;
        if (mViewModel.dataCenter.detailResponse.isLiving()) {
            if (mViewModel.dataCenter.isHotJobShowing) {//职位卡显示期间，来的置顶职位，职位卡消失需要显示出来
                TLog.debug(TAG, "hot card showing high top card into");
            } else {
                checkShowHighJobCard(mViewModel.dataCenter.detailResponse);
            }
        }
    }

    /**
     * 处理点击「去沟通」按钮事件
     *
     * @param jobInfo
     */
    protected final void handleClickChatButtonEvent(@NonNull JobInfoBean jobInfo, String pageSource) {
        LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, "去沟通", "1");
        // 去沟通，跳转到聊天页

        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setJobId(jobInfo.id);
        singleChatParam.setFriendId(jobInfo.bossId);
        singleChatParam.setSecurityId(jobInfo.securityId);
        singleChatParam.setChangePositionDesc(jobInfo.name);
        singleChatParam.setFromDZUser(false);
        SingleRouter.startChat(activity, singleChatParam);

        ReportUtil.reportNoJobId(0, pageSource, "setData");
    }

    private void clearDelayTopCard() {
        mViewModel.dataCenter.isHotJobShowing = false;
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    public void onResume() {
        if (UserManager.isGeekRole()) {/*BOSS身份不投简历，所以没有这一步操作*/
            // 从管理简历列表回来，要刷新简历状态
            mViewModel.dataCenter.getResumeList(null);
        }

        luckyDrawWrapper.checkShowLuckyDrawResultTips();
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void onDestroy() {
        if (view.likeAnimView.isRunning()) {
            view.likeAnimView.stopAutoLike();
        }
        if (view.commentDisplayView != null) {
            view.commentDisplayView.onDestroy();
        }
        if (view.rewardLayout != null) {
            view.rewardLayout.onDestroy();
        }
        if (view.applaudAnimView != null) {
            view.applaudAnimView.onDestroy();
        }
        if (roomInfoView.ldvLotteryDraw != null) {
            roomInfoView.ldvLotteryDraw.stopLottery();
        }

        if (null != bossNormalRoomRunnable) {
            Utils.removeHandlerCallbacks(bossNormalRoomRunnable);
            bossNormalRoomRunnable = null;
        }
        if (null != runnableDismissRecommendLabelForJobList) {
            Utils.removeHandlerCallbacks(runnableDismissRecommendLabelForJobList);
            runnableDismissRecommendLabelForJobList = null;
        }
        clearDelayTopCard();

        if (null != view.clHotJobCardView) {
            view.clHotJobCardView.onDestroy();
        }
    }

    /**
     * 初始化代播本场boss
     */
    public void updateJobDeliverProxyBoss() {
        if (mViewModel.dataCenter.isProxyBoss) {
            if (mViewModel.dataCenter.detailResponse.isLiving()) {
                view.ivGift.setVisibility(View.GONE);
                view.tvCommentInput.setText("点击发送消息");
                chatBottomFunctionView.setInputHint("发送消息吸引更多牛人投递");
                ProxyBossJobModel proxyBossJobModel = mViewModel.dataCenter.proxyBossJobListLiveData.getValue();
                view.updateJobDeliverInfo(mViewModel.dataCenter.isProxyBoss, proxyBossJobModel);
                checkBindAt(proxyBossJobModel != null ? LList.getCount(proxyBossJobModel.jobList) : 0);
            }
        }
    }

    /**
     * 根据是否有职位，检查是否显示at图标
     */
    public void checkBindAt(int jobCount) {
        chatBottomFunctionView.setHasJobAndAtJobBottomSheetListener(jobCount > 0, new ChatBottomFunctionView.OnAtJobBottomSheetListener() {
            @Override
            public void onShow(boolean inputLastAt) {
                dialogManager.showAtJobBottomSheetDialog(new AtJobListShowModel(inputLastAt))
                        .setOnItemClickListener(new AtJobListFragment.onItemClickAtJobListener() {
                            @Override
                            public void onItemOnclick(int position, JobInfoBean jobInfoBean, boolean inputLastAt) {
                                onJobItemClicked(jobInfoBean, inputLastAt);
                            }
                        });
            }
        });
    }

    /**
     * 点击预设弹幕并且发送
     *
     * @param adapter
     * @param position
     */
    private void clickQuickQuestionItemSendMessage(BaseQuickAdapter adapter, int position, boolean isFromKeyBoard) {
        // 隐藏软键盘，发送消息
        chatBottomFunctionView.stop(true);
        GeekQuickQuestionQueryBean question = (GeekQuickQuestionQueryBean) adapter.getItem(position);
        if (null != question) {
            sendMessage(0, question.speakContent, question.positionGroupJSON);
            if (isFromKeyBoard) {
                LiveAnalysisUtil.dotTopSetQuestionRvClick(question.showContent, position, mViewModel.dataCenter.liveRecordId, UserManager.isBossRole() ? "4" : (mViewModel.dataCenter.detailResponse.deliveredNum > 0 ? "2" : "1"));
            } else {
                LiveAnalysisUtil.dotTopSetQuestionClick(mViewModel.dataCenter.liveRecordId
                        , question.showContent, UserManager.isBossRole() ? "4" : (mViewModel.dataCenter.detailResponse.deliveredNum > 0 ? "2" : "1"));
            }
        }
        quickQuestionView.cancelQuickQuestionTimerAndDismiss();
    }

    public void onChatBottom(boolean isShow, int keyBoardHeight) {
        if (AppUtil.isLandscape(activity) || !mViewModel.dataCenter.detailResponse.isLiving())
            return;
        softWindowStateChange(isShow, keyBoardHeight);
    }

    private void onJobItemClicked(JobInfoBean jobInfoBean, boolean inputLastAt) {
        if (jobInfoBean != null && !TextUtils.isEmpty(jobInfoBean.name)) {
            if (chatBottomFunctionView.getAtJobListCount() >= 5) {
                ToastUtils.showText("每条消息最多@5次");
                return;
            }
            dialogManager.hideAtJobBottomSheetDialog();
            // 显示软键盘，填充@职位
            dialogManager.showChatBottomView(0);
            chatBottomFunctionView.onJobItemClicked(jobInfoBean, inputLastAt, false);
        }
    }

    /**
     * 设置回放数据
     */
    private void setPlayBack() {
        /*不可投递状态，清空职位卡，切片可以正常出*/
        if (null != mViewModel.dataCenter.detailResponse
                && mViewModel.dataCenter.detailResponse.outTimeLimit
                && null != mViewModel.dataCenter.liveExplainListResponse) {
            mViewModel.dataCenter.liveExplainListResponse.jobCard = null;
        }
        JobInfoBean jobCardBean = mViewModel.dataCenter.getJobCardLiveFinish();
        List<SpeakVideoBean> speakVideoBeanList = mViewModel.dataCenter.getSpeakList();
        if (null != jobCardBean || LList.getCount(speakVideoBeanList) > 0) {
            if (mViewModel.dataCenter.isNeedShowSpeakVideoListView()) {//「直播切片」列表数据>0条
                mViewModel.dataCenter.videoPlayBackMode = null != jobCardBean && !TextUtils.isEmpty(mViewModel.dataCenter.detailResponse.liveVideoUrl) ? CampusConstant.VideoPlayBackMode.MODE_COMPLETE_PLAYBACK : CampusConstant.VideoPlayBackMode.MODE_SPEAK_VIDEO;
            } else {
                mViewModel.dataCenter.videoPlayBackMode = CampusConstant.VideoPlayBackMode.MODE_COMPLETE_PLAYBACK;
            }
            SpeakVideoBean firstSpeakVideo = LList.getElement(speakVideoBeanList, 0);
            String targetExplainId = !TextUtils.isEmpty(mViewModel.dataCenter.speakExplainId) ? mViewModel.dataCenter.speakExplainId : (firstSpeakVideo != null ? firstSpeakVideo.encryptExplainId : "");
            //不管有没有切片，都显示切片view，推荐卡也在里面显示
            view.speakVideoListView.setIsShowDeleteIcon(false)
                    //极端case：有切片，没有回放，直接播放切片，不允许显示切换回放按钮
                    .setChangeModeButtonVisibility(LList.getCount(speakVideoBeanList) > 0 && !TextUtils.isEmpty(mViewModel.dataCenter.detailResponse.liveVideoUrl) ? View.VISIBLE : View.GONE)
                    .setVideoPlayBackMode(mViewModel.dataCenter.videoPlayBackMode)
                    .setCanDeliver(mViewModel.dataCenter.detailResponse.canDeliver)
                    .setOnlineNormalLevelRoom(mViewModel.dataCenter.detailResponse.isOnlineNormalLevelRoom())
                    .setUserIntoIdentity(true)
                    .setLiveRecordId(mViewModel.dataCenter.liveRecordId)
                    .setBlueRoom(mViewModel.dataCenter.detailResponse.isBlueRoom())
                    .setJobCardBean(jobCardBean)
                    .setData(speakVideoBeanList, targetExplainId, speakVideoPlayEventListener);
            view.speakVideoListView.setVisibility(View.VISIBLE);
        } else {
            view.speakVideoListView.setVisibility(View.GONE);
        }

        /*回放模式播放回放视频*/
        if (mViewModel.dataCenter.videoPlayBackMode == CampusConstant.VideoPlayBackMode.MODE_COMPLETE_PLAYBACK) {
            mViewModel.dataCenter.playVideoLiveData.setValue(new PlayVideoModel(mViewModel.dataCenter.detailResponse.liveVideoUrl));
        }
    }


    private void handleLiveCommentContainerViewOrientation() {
        if (dialogManager.isShowSoft()) return;
        AudienceLayoutUtil.handleLiveCommentContainerViewOrientation(activity, view.rootView, mViewModel.dataCenter.detailResponse);
    }

    /**
     * @param isShow         键盘显示因此  true 显示 false隐藏
     * @param keyBoardHeight 键盘高度
     */
    private void softWindowStateChange(boolean isShow, int keyBoardHeight) {
        if (AppUtil.isLandscape(activity)) return;

        if (mViewModel.dataCenter.detailResponse.isLiving()) {
            view.softWindowStateChangeLiving(isShow);
            //输入框上方显示的预设弹幕
            chatBottomFunctionView.showQuickQuestion(isShow, view.commentDisplayView.quickQuestionView.getCurrentShowQuestionList(), (adapter, view, position) -> {
                clickQuickQuestionItemSendMessage(adapter, position, true);
            });
            if (isShow) {
                view.clInputControlContainer.setVisibility(View.INVISIBLE);
                view.topJobCardView.dismissTopJobPopup(false);
                view.clTopHighJobContainer.dismissTopJobPopup(false);
            }

            AudienceLayoutUtil.handleLiveCommentStyle(activity, view.commentDisplayView.cl_container, chatBottomFunctionView, mViewModel.dataCenter.getScrnOrient());
        }
        if (isShow) {
            AudienceLayoutUtil.softWindowStateChange(view.rootView, keyBoardHeight);
        } else { // 所有有 输入显示 限制的 地方 都需要添加到这里来
            handleLiveCommentContainerViewOrientation();
            view.checkShowInputControlContainer(activity, mViewModel.dataCenter.detailResponse, dialogManager.isShowSoft());
        }
        chatBottomFunctionView.addOrRemoveGlobalLayoutListener(isShow);
    }

    /**
     * 发送互动消息
     */
    private void sendMessage(int type, String content, String positionGroupJSON) {
        if (mViewModel.dataCenter.detailResponse.isShutUp()) {
            ToastUtils.showText("观众禁言模式开启，暂时不能提问哦");
            LiveAnalysisUtil.dotCampusLivePageWordprohibited(mViewModel.dataCenter.liveRecordId);
            return;
        }
        if (TextUtils.isEmpty(content) || TextUtils.isEmpty(content.trim())) {
            ToastUtils.showText("发送内容不能为空");
            return;
        }

        if (type != 1) {// 普通发言
            view.commentDisplayView.setStatus(CampusConstant.STATUS_UPDATING);
            if (mViewModel.dataCenter.isProxyBoss || mViewModel.dataCenter.shareDataCenter.isCompanyColleague()) {
                mViewModel.dataCenter.bossSpeak(content, positionGroupJSON, chatBottomFunctionView.getAtJobPositions(), new ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        chatBottomFunctionView.stop(true);
                        chatBottomFunctionView.getAtJobPositions().clear();
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        if (code == 200010) {// 敏感词错误码
                            chatBottomFunctionView.setText("");
                            ToastUtils.showText("内容包含违禁字符，请修改后重试");
                        } else {
                            ToastUtils.showText(msg);
                        }
                    }
                });
            } else {
                mViewModel.dataCenter.speak(0, content,
                        new ICommonCallback() {
                            @Override
                            public void onSuccess() {
                                chatBottomFunctionView.stop(true);
                                mViewModel.dataCenter.isHasSendMsg = true;
                            }

                            @Override
                            public void onFail(int code, String msg) {
                                mViewModel.dataCenter.isHasSendMsg = true;
                                if (code == 200010) {// 敏感词错误码
                                    chatBottomFunctionView.setText("");
                                    ToastUtils.showText("内容包含违禁字符，请修改后重试");
                                } else {
                                    ToastUtils.showText(msg);
                                }
                            }
                        });
            }
        }
    }

    private void checkShowCommentInput() {
        if (mViewModel.dataCenter.detailResponse.isBossAndNotLiveCollege() && !mViewModel.dataCenter.isProxyBoss
                && !(UserManager.isBossRole() && mViewModel.dataCenter.shareDataCenter.isCompanyColleague())) {/*BOSS身份&&非直播学院 && 非本场同公司boss 不展示*/
            view.clCommentInput.setVisibility(View.INVISIBLE);
        } else {
            if (mViewModel.dataCenter.isProxyBoss) {
                if (UserManager.isBossRole()) {
                    view.tvCommentInput.setText("点击发送消息");
                    chatBottomFunctionView.setInputHint("发送消息吸引更多牛人投递");
                } else {
                    view.tvCommentInput.setText("发送消息，和牛人互动");
                }
            }
            if (UserManager.isBossRole() && mViewModel.dataCenter.shareDataCenter.powerType == -3) {
                view.ivGift.setVisibility(View.GONE);
                view.tvCommentInput.setText("点击发送消息");
                chatBottomFunctionView.setInputHint("发送消息吸引更多牛人投递");
            }
            view.clCommentInput.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 置顶职位气泡
     */
    private void checkShowTopJobCard(GeekRecruitDetailResponse response) {
        if (response.topApplyJobId > 0 && !response.isHighLevelRoom()) {
            if (!mViewModel.dataCenter.detailResponse.isLiving() || mViewModel.dataCenter.detailResponse.isBossAndNotLiveCollege()
                    || dialogManager.isTopJobPopupInterceptor() || mViewModel.dataCenter.isShowedTopPop
                    || mViewModel.dataCenter.isFirstIntoWhenShowJobPop) {
                return;
            }
            //横屏不出卡
            if (null != activity && AppUtil.isLandscape(activity)) return;

            if (response.deliverNumLimit > 0 && response.deliveredNum >= response.deliverNumLimit) {
                response.canDeliver = false;
            }
            mViewModel.dataCenter.isShowedTopPop = true;
            view.topJobCardView.showTopJobPopup(view.clBtnPosition, view.clInputControlContainer,
                    mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse);
            view.topJobCardView.setActionListener(new TopJobCardView.IOnCardActionListener() {
                @Override
                public void onCardClick(JobInfoBean jobInfo) {
                    // 跳转到职位详情页
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, jobInfo, mViewModel.dataCenter.getJobBottomSheetDialogHeight()));
                }

                @Override
                public void onPostBtnClick(JobInfoBean jobInfo) {
                    if (UserManager.isBossRole()) {
                        ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
                        return;
                    }
                    if (!mViewModel.dataCenter.detailResponse.canDeliver || jobInfo.delivered)
                        return; // 当禁止投递和已投递过的状态时，则不允许操作
                    if (jobInfo.isAtsPostType()) {
                        dialogManager.showAtsPostDialog(jobInfo.applyOnlineUrl);
                    } else {
                        /*显示投递简历时的二次确认弹框*/
                        showDeliverResumeConfirmDialog(mViewModel.dataCenter.detailResponse, jobInfo);
                    }
                    LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, view.topJobCardView.getBtnTitle(), "3");
                }

                @Override
                public void onClickChatButton(JobInfoBean jobInfo) {
                    if (jobInfo == null) return;
                    handleClickChatButtonEvent(jobInfo, "BaseNormalJobLIstPresenter");
                }

                @Override
                public void onClickSendButton(JobInfoBean jobInfo, boolean isPhone) {
                    if(isPhone){
                        mViewModel.dataCenter.sendPhone(jobInfo);
                    }else{
                        mViewModel.dataCenter.sendWechat(jobInfo);
                    }
                }

                @Override
                public void onDismiss() {
                    view.isSetResAnimationPosition = false;
                    view.showPositionResAnimation();
                }
            });
        } else {
            view.topJobCardView.dismissTopJobPopup();
        }
    }

    /**
     * 高级直播间职位气泡
     */
    private void checkShowHighJobCard(GeekRecruitDetailResponse response) {
        if (null != response) {
            if (TextUtils.isEmpty(response.topJobGroupId)) {
                view.clTopHighJobContainer.dismissTopJobPopup();
                return;
            }
            if (!mViewModel.dataCenter.detailResponse.isLiving() || mViewModel.dataCenter.detailResponse.isBossAndNotLiveCollege()
                    || dialogManager.isTopJobPopupInterceptor() || mViewModel.dataCenter.isShowedTopHighPop
                    || mViewModel.dataCenter.isFirstIntoWhenShowJobPop) {
                return;
            }
            //横屏不出卡
            if (null != activity && AppUtil.isLandscape(activity)) return;

            mViewModel.dataCenter.isShowedTopHighPop = true;
            view.clTopHighJobContainer.showTopJobHighPopup(view.clBtnPosition, view.clInputControlContainer, response);
            //处理按钮和卡片点击
            view.clTopHighJobContainer.setActionListener(new TopJobHighCardView.IOnCardActionListener() {
                @Override
                public void onCardClick() {
                    view.clTopHighJobContainer.dismissTopJobPopup();
                    // 跳转到职位列表页
                    int height = mViewModel.dataCenter.getJobBottomSheetDialogHeight();
                    int width = mViewModel.dataCenter.getJobBottomSheetDialogWidth();
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(null, Math.max(width, height)).setClickJumpJobListFrom(CampusConstant.ClickJumpJobListFrom.FROM_TOP_GROUP));
                }

                @Override
                public void onPostBtnClick() {
                    view.clTopHighJobContainer.dismissTopJobPopup();
                    // 跳转到职位列表页
                    int height = mViewModel.dataCenter.getJobBottomSheetDialogHeight();
                    int width = mViewModel.dataCenter.getJobBottomSheetDialogWidth();
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(null, Math.max(width, height)).setClickJumpJobListFrom(CampusConstant.ClickJumpJobListFrom.FROM_TOP_GROUP));
                }

                @Override
                public void onDismiss() {
                    view.isSetResAnimationPosition = false;
                    view.showPositionResAnimation();
                }
            });
        } else {
            view.clTopHighJobContainer.dismissTopJobPopup();
        }
    }

    /**
     * 显示投递简历时的二次确认弹框
     */
    private void showDeliverResumeConfirmDialog(GeekRecruitDetailResponse geekResp, @NonNull JobInfoBean jobInfoBean) {
        if (geekResp == null) return;
        if (geekResp.liveRoomType == 3) { // 试播
            dialogManager.showTestLiveDialogTips();
        } else {
            mViewModel.dataCenter.checkBlueAndDeliver(dialogManager, jobInfoBean, false);
        }
    }

    /**
     * 避免第一次出现闪一下的情况
     */
    private boolean isFirstHideTop = true;

    private void handleViewAlpha(boolean showTop) {
        view.handleViewAlpha(showTop, isFirstHideTop, roomInfoView);
        isFirstHideTop = false;
        if (!showTop) {
            if (luckyDrawWrapper.needShowLuckyDrawResultTips && System.currentTimeMillis() - mViewModel.dataCenter.pureModeDisplayTime < LuckyDrawResultTipPopup.DISPLAY_TIME - 1000) {
                view.rootView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        luckyDrawWrapper.realShowLuckyDrawResultTips(LuckyDrawResultTipPopup.DISPLAY_TIME
                                - (System.currentTimeMillis() - mViewModel.dataCenter.pureModeDisplayTime));
                        luckyDrawWrapper.needShowLuckyDrawResultTips = false;
                        mViewModel.dataCenter.pureModeDisplayTime = 0;
                    }
                }, 500);
            }
        }
    }

    private void checkShowBackToLive(boolean show) {
        if (show) {
            // 延迟一点时间显示恢复直播按钮，否则横屏立刻显示出来
            App.get().getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    view.clBackToLive.setVisibility(View.VISIBLE);// 回到直播中
                    view.ivGift.setVisibility(View.GONE);
                    view.clCommentInput.setVisibility(View.GONE);
                }
            }, 200);
        } else {
            view.clBackToLive.setVisibility(View.GONE);
            view.ivGift.setVisibility(mViewModel.dataCenter.shareDataCenter.isCompanyColleague() || mViewModel.dataCenter.isProxyBoss ? View.GONE : View.VISIBLE);
            checkShowCommentInput();
        }
    }

    /**
     * 布局层级问题
     * 设置点击屏幕 隐藏其他功能性按钮
     * 用于点击 屏幕 隐藏其他功能性按钮
     * 注意：竖屏推流时 不能 设置点击事件， 否则下方法按钮无法点击
     */
    private void setCommentContainerOnClick(boolean isClick) {
        if (isClick) {
            view.clCommentContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 1.点击隐藏输入法
                    dialogManager.getChatBottomFunctionView().onDispatchTouchOutArea();
                    // 2、横屏情况下 点击 隐藏工具按钮
                    if (mViewModel.dataCenter.detailResponse.isLandscapeStream()) {/*横向流*/
                        if (ActivityUtils.isValid(activity) && AppUtil.isLandscape(activity)) {
                            if (mViewModel.dataCenter.detailResponse.isLiving()) {
                                CommentModel commentModel = mViewModel.dataCenter.getCommentModel();
                                commentModel.hideUIList = !commentModel.hideUIList;
                                mViewModel.dataCenter.commentModelLiveData.postValue(commentModel);
                            }
                        }
                    }
                }
            });
        } else {
            view.clCommentContainer.setOnClickListener(null);
        }
        view.clCommentContainer.setClickable(isClick);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initApplaudClick() {
        view.ivApplause.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        view.likeAnimView.LongClickLikeStart();
                        view.ivApplause.startAnimation(AnimationUtils.loadAnimation(activity, R.anim.scale_anim));

                        /*调用点赞接口*/
                        mViewModel.dataCenter.applaud(1);
                        /*点赞数+1*/
                        if (mViewModel.dataCenter.detailResponse != null) {
                            view.tvApplauseNum.setText(StringUtil.formatAudienceApplaud(++mViewModel.dataCenter.detailResponse.applaudCount));
                        }
                        break;
                    case MotionEvent.ACTION_UP:
                        view.likeAnimView.LongClickLikeEnd();
                        break;
                }
                return true;
            }
        });
        view.applaudTouchView.setOnDrawApplaudListener(new OnDrawApplaudListener() {
            @Override
            public void onDrawApplaudAnim(int x, int y) {
                if (mViewModel.dataCenter.detailResponse.isLiving()) {
                    // 触发震动
                    SystemUtils.vibrator(36L);
                    // 调用点赞接口
                    mViewModel.dataCenter.applaud(2);
                    // 点赞数+1
                    view.tvApplauseNum.setText(StringUtil.formatAudienceApplaud(++mViewModel.dataCenter.detailResponse.applaudCount));
                    // 渲染一次双击点赞动画
                    view.applaudAnimView.addAnim(x, y);
                }
            }
        });
    }

    /**
     * 「讲解视频」列表事件回调
     */
    private final SpeakVideoListView.SpeakVideoPlayEventListener speakVideoPlayEventListener = new SpeakVideoListView.SpeakVideoPlayEventListener() {
        @Override
        public void playSpeakVideo(SpeakVideoBean speakVideoBean, boolean isLastItem) {
            if (speakVideoBean == null) return;
            mViewModel.dataCenter.getExplainPlayUrl(speakVideoBean.encryptExplainId, isLastItem);
        }

        @Override
        public void onChangeVideoPlayMode(@CampusConstant.VideoPlayBackMode int videoPlayBackMode) {
            mViewModel.dataCenter.videoPlayBackMode = videoPlayBackMode;
            if (mViewModel.dataCenter.videoPlayBackMode == CampusConstant.VideoPlayBackMode.MODE_COMPLETE_PLAYBACK) {
                String liveVideoPlayBackUrl = mViewModel.dataCenter.detailResponse != null ? mViewModel.dataCenter.detailResponse.liveVideoUrl : "";
                mViewModel.dataCenter.playVideoLiveData.setValue(new PlayVideoModel(liveVideoPlayBackUrl));
            } else {
                view.speakVideoListView.setPlaySpeakVideoBean(view.speakVideoListView.getFirstItemBean(), true);
            }
        }

        @Override
        public void setVideoPause() {
            mViewModel.dataCenter.pauseVideoLiveData.setValue(true);
        }

        @Override
        public void onOpenJobDetail(JobInfoBean jobInfoBean, boolean isExplain) {
            if (mViewModel.dataCenter.shareDataCenter.isCompanyColleague())
                return;
            if (null != jobInfoBean && LiveCommonUtil.showChatNowBtn(mViewModel.dataCenter.detailResponse) && jobInfoBean.open) {
                // 跳转至APP的职位详情页
                ParamBean paramBean = new ParamBean();
                paramBean.jobId = jobInfoBean.id;
                paramBean.userId = jobInfoBean.bossId;
                paramBean.jobName = jobInfoBean.name;
                paramBean.degreeName = jobInfoBean.graduateDegree;
                paramBean.experienceName = jobInfoBean.experienceName;
                paramBean.securityId = jobInfoBean.securityId;
                paramBean.localPageTag = getClass().getSimpleName();
//                BossJobActivity.startActivity(activity, paramBean);
                GeekPageRouter.jumpToBossJobActivity(activity, paramBean, false);

                LiveAnalysisUtil.dotLiveChatImmediate(jobInfoBean.id);
            } else {
                if (null != activity && null != jobInfoBean) {
                    int peekHeight = (int) (Math.max(ZPUIDisplayHelper.getScreenWidth(activity), ZPUIDisplayHelper.getScreenHeight(activity)) * 0.7);
                    mViewModel.showJobBottomSheetDialog(new RecruitJobModel(true, jobInfoBean, peekHeight));
                    if (isExplain) {
                        LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState,
                                jobInfoBean.securityId, CampusConstant.ClickJumpJobDetailFrom.FROM_EXPLAIN_CARD,
                                mViewModel.dataCenter.getStreamType(), jobInfoBean.bossId, jobInfoBean.encryptJobGroupId);
                    } else {
                        LiveAnalysisUtil.traceLookPositionClick(mViewModel.dataCenter.liveRecordId, mViewModel.dataCenter.detailResponse.liveState,
                                jobInfoBean.securityId, LiveAnalysisUtil.getSourceByLabelType(jobInfoBean.labelType),
                                mViewModel.dataCenter.getStreamType(), jobInfoBean.bossId, jobInfoBean.encryptJobGroupId);
                    }

                }
            }
        }

        @Override
        public void onClickChatButton(JobInfoBean jobInfo) {
            LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, "去沟通", "1");
            // 去沟通，跳转到聊天页

            SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
            singleChatParam.setJobId(jobInfo.id);
            singleChatParam.setFriendId(jobInfo.bossId);
            singleChatParam.setSecurityId(jobInfo.securityId);
            singleChatParam.setChangePositionDesc(jobInfo.name);
            singleChatParam.setFromDZUser(false);
            if (null != activity) {
                SingleRouter.startChat(activity, singleChatParam);
            }
        }

        @Override
        public void onClickDeliverButton(JobInfoBean jobInfo) {
            if (mViewModel.dataCenter.detailResponse == null || !mViewModel.dataCenter.detailResponse.canDeliver || jobInfo.delivered) { // 当禁止投递和已投递过的状态时，则不允许操作
                return;
            }
            if (UserManager.isBossRole()) { /*B身份只展示Toast*/
                ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
                return;
            }


            if (mViewModel.dataCenter.detailResponse.liveRoomType == CampusConstant.LiveRoomType.LIVE_ROOM_TYPE_TRY) { // 试播
                if (mViewModel.dialogManager != null) {
                    mViewModel.dialogManager.showTestLiveDialogTips();
                }
                return;
            }

            LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfo, jobInfo.blueCollarFlag ? "立即报名" : "投简历", "1");//埋点
            mViewModel.dataCenter.checkBlueAndDeliver(mViewModel.dialogManager, jobInfo, false);
        }

        @Override
        public void onClickSendButton(JobInfoBean jobInfo, boolean isPhone) {
            if(isPhone){
                mViewModel.dataCenter.sendPhone(jobInfo);
            }else{
                mViewModel.dataCenter.sendWechat(jobInfo);
            }
        }

        @Override
        public void onClickNetPostButton(JobInfoBean jobInfoBean) {
            if (mViewModel.dataCenter.detailResponse == null) { //则不允许操作
                return;
            }
            if (UserManager.isBossRole()) { /*B身份只展示Toast*/
                ToastUtils.showText("当前为BOSS身份，无法成功投递，仅为体验流程");
                return;
            }

            if (mViewModel.dataCenter.detailResponse.liveRoomType == CampusConstant.LiveRoomType.LIVE_ROOM_TYPE_TRY) { // 试播
                if (mViewModel.dialogManager != null) {
                    mViewModel.dialogManager.showTestLiveDialogTips();
                }
                return;
            }

            if (jobInfoBean.isAtsPostType()) {
                if (mViewModel.dialogManager != null) {
                    LiveAnalysisUtil.liveResumeSend(mViewModel.dataCenter.liveRecordId, jobInfoBean, "立即网申", "1");//埋点
                    mViewModel.dialogManager.showAtsPostDialog(jobInfoBean.applyOnlineUrl);
                }
            }
        }

        @Override
        public GeekRecruitDetailResponse getRecruitDetailResponse() {
            return mViewModel.dataCenter.detailResponse;
        }
    };
}
