package com.hpbr.bosszhipin.live.boss.live.fragment;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.viewpager.widget.ViewPager;

import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.boss.live.activity.SchoolLiveFinishActivity;
import com.hpbr.bosszhipin.live.boss.live.adapter.SchoolFinishViewPagerAdapter;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.LiveFinishViewModel;
import com.hpbr.bosszhipin.live.boss.live.widget.FinishLiveReportView;
import com.hpbr.bosszhipin.live.export.LiveAudience;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.export.LiveService;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.response.BossEndOverviewResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListOnlineResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveDetailForSchoolBatchResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.titlebar.ScaleTransitionPagerTitleView;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.buildins.UIUtil;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.GradualChangeColorLinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.badge.BadgePagerTitleView;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * 带牛人列表的内容
 * <p>
 * Created by Qu Zhiyong on 2023/2/23
 */
public class FinishWithGeekListFragment extends BaseFragment {
    private ConstraintLayout clTabSelector, clSelector;
    private MTextView tvLiveTitle, tvLiveStateDesc;
    private CoordinatorLayout coordinatorLayout;
    private ZPUIRoundButton btnLiveData;
    private MTextView tvSelector;
    private FinishLiveReportView liveReportView;
    private MagicIndicator magicIndicator;
    private CommonNavigator commonNavigator;
    private ImageView ivSelectorArrow;
    private ViewPager vpGeekList;
    private LiveFinishViewModel viewModel;
    private BossEndOverviewResponse overviewResponse;
    private List<String> titles;
    private List<BaseFragment> mFragments;

    public static FinishWithGeekListFragment getInstance(Bundle data) {
        FinishWithGeekListFragment f = new FinishWithGeekListFragment();
        f.setArguments(data);
        return f;
    }

    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.live_fragment_finish_with_geek_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        viewModel = LiveFinishViewModel.instance(getActivity());
        initViews(view);
        bindListener();
        initMvp();
        ReceiverUtils.register(activity, rejectActionReceiver, Constants.RECEIVER_FRIEND_REJECT_ACTION);
    }

    private void initViews(View view) {
        tvLiveTitle = view.findViewById(R.id.tv_live_title);
        tvLiveStateDesc = view.findViewById(R.id.tv_live_state_desc);
        coordinatorLayout = view.findViewById(R.id.coordinatorLayout);
        btnLiveData = view.findViewById(R.id.btn_live_data);
        clTabSelector = view.findViewById(R.id.cl_tab_selector);
        magicIndicator = view.findViewById(R.id.magic_indicator);
        clSelector = view.findViewById(R.id.cl_selector);
        tvSelector = view.findViewById(R.id.tv_selector);
        ivSelectorArrow = view.findViewById(R.id.iv_selector_arrow);
        vpGeekList = view.findViewById(R.id.view_pager);
        liveReportView = view.findViewById(R.id.view_live_report);
    }

    private void bindListener() {
        btnLiveData.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (overviewResponse == null) return;
                LiveAnalysisUtil.campusLivePopEndFunc(overviewResponse.recordId, "全部数据", viewModel.getUserIdentityForAnalysis());
                viewMoreDetail(overviewResponse.moreLiveReport);
            }
        });
        clSelector.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                viewModel.selectorFragmentShowLiveData.postValue(true);
            }
        });
        liveReportView.setActionListener(new FinishLiveReportView.IOnActionListener() {
            @Override
            public void onQuestionAnswerClicked() {
                viewModel.questionExplainFragmentLiveData.postValue(true);
                LiveAnalysisUtil.campusLivePopEndExplainFunc(overviewResponse.recordId, "讲解视频", viewModel.getUserIdentityForAnalysis(), overviewResponse.hasExplain);
            }

            @Override
            public void onIntentionClicked() {
                LiveAnalysisUtil.campusLivePopEndFunc(overviewResponse.recordId, "牛人榜", viewModel.getUserIdentityForAnalysis());
                /*显示「牛人榜」菜单*/
                viewModel.intentionFragmentShowLiveData.postValue(true);
            }

            @Override
            public void onSeeExplain() {
                LiveAnalysisUtil.campusLivePopEndFunc(overviewResponse.recordId, "观看回放", viewModel.getUserIdentityForAnalysis());
                if (viewModel.isThisLiveBoss) {
                    // 代播Boss || 同公司Boss，跳转到B看C页面，不展示职位按钮
                    if (null == getContext()) return;
                    LiveAudience.builder(getContext())
                            .setLiveRecordId(viewModel.liveRecordId)
                            .setIsProxyBoss(true)
                            .build()
                            .jump();
                } else {
                    LiveService.openBossPreviewActivity(getContext(), overviewResponse.recordId, viewModel.isExperienceLive, viewModel.isAdmin);
                }
            }

            @Override
            public void onLuckyDrawFeedbackClicked() {
                LiveAnalysisUtil.campusLivePopEndFunc(overviewResponse.recordId, "上传抽奖订单", viewModel.getUserIdentityForAnalysis());
                new ZPManager(activity, overviewResponse.luckyDrawTrackingNumberH5Url).handler();
            }

            @Override
            public void onLoveTalentRankClicked() {
                LiveAnalysisUtil.campusLivePopEndFunc(overviewResponse.recordId, "爱才企业榜", viewModel.getUserIdentityForAnalysis());
                new ZPManager(activity, overviewResponse.heatPowerH5Url).handler();
            }

            @Override
            public void onSeeMoreClicked() {
                LiveAnalysisUtil.dotApplyGeekListViewMore(overviewResponse.recordId);
            }
        });
    }

    private void initMvp() {
        viewModel.detailForSchoolLiveData.observe(this, new Observer<BossLiveDetailForSchoolBatchResponse>() {
            @Override
            public void onChanged(BossLiveDetailForSchoolBatchResponse batchResponse) {
                if (batchResponse == null) return;
                updateUi(batchResponse.bossEndOverviewResponse);
                if (viewModel.detailResp != null && viewModel.detailResp.isOnlineBzpType() && viewModel.detailResp.isNormalRoom()) {
                    BossGetDeliveryGeekListOnlineResponse geekListOnlineResponse = batchResponse.bossGetDeliveryGeekListOnlineResponse;
                    if ((batchResponse.bossEndOverviewResponse.roomLevel == 1 || viewModel.isThisLiveBoss)
                            && geekListOnlineResponse != null
                            && (!LList.isEmpty(geekListOnlineResponse.phoneGeekList) || !LList.isEmpty(geekListOnlineResponse.wxGeekList) || !LList.isEmpty(geekListOnlineResponse.resumeGeekList))
                    ) {
                        // 没有牛人列表
                        clTabSelector.setVisibility(View.VISIBLE);
                        clSelector.setVisibility(LList.getCount(geekListOnlineResponse.jobList) > 1 ? View.VISIBLE : View.GONE);// 职位数大于1条时筛选条才展示
                        setSelectorTitle(null);
                        if(LList.getCount(geekListOnlineResponse.jobList) > 1){
                            viewModel.addP4("职位筛选");
                        }
                        initGeekOnlineFragments();
                        initIndicator(true);
                    } else {
                        clTabSelector.setVisibility(View.GONE);
                        vpGeekList.setVisibility(View.GONE);
                    }
                } else {
                    BossGetDeliveryGeekListResponse geekListResponse = batchResponse.bossGetDeliveryGeekListResponse;
                    //[1012.802] 普通直播间 || 本场boss '显示'
                    if ((batchResponse.bossEndOverviewResponse.roomLevel == 1 || viewModel.isThisLiveBoss)
                            && geekListResponse != null
                            && (!LList.isEmpty(geekListResponse.geekList) || !LList.isEmpty(geekListResponse.rejectGeekList))
                    ) {
                        // 没有牛人列表
                        clTabSelector.setVisibility(View.VISIBLE);
                        clSelector.setVisibility(LList.getCount(geekListResponse.jobList) > 1 ? View.VISIBLE : View.GONE);// 职位数大于1条时筛选条才展示
                        setSelectorTitle(null);
                        if(LList.getCount(geekListResponse.jobList) > 1){
                            viewModel.addP4("职位筛选");
                        }
                        initGeekNoOnlineFragments();
                        initIndicator(false);
                    } else {
                        clTabSelector.setVisibility(View.GONE);
                        vpGeekList.setVisibility(View.GONE);
                    }
                }
                boolean showSeeExplain = overviewResponse.liveVideoFlag == 1 && !TextUtils.isEmpty(overviewResponse.liveVideoUrl) && overviewResponse.liveVideoState != 1;
                LiveAnalysisUtil.campusLivePopEnd(viewModel.liveRecordId, showSeeExplain ? 1 : 0, viewModel.getUserIdentityForAnalysis(), viewModel.getP4(), viewModel.geekList);
                viewModel.cleanP4();
            }
        });
        // 筛选条选中职位
        viewModel.selectedJobBeanLiveData.observe(this, selectedJobBean -> {
            setSelectorTitle(selectedJobBean != null ? selectedJobBean.jobName : null);
            // 筛选投递牛人列表
            if (viewModel.detailResp != null && viewModel.detailResp.isOnlineBzpType() && viewModel.detailResp.isNormalRoom()) {
                viewModel.getDeliveryGeekOnlineList(selectedJobBean != null ? selectedJobBean.encryptJobId : "");
            } else {
                viewModel.getDeliveryGeekList(selectedJobBean != null ? selectedJobBean.encryptJobId : "");
            }
        });
        // 所有讲解视频都被删除了
        LiveBus.with(ChannelConstants.LIVE_CAMPUS_BOSS_DELECT_ALL_SPEAK_VIDEOS, Boolean.class).observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean cleared) {
                if (cleared && null != viewModel.detailResp) {
                    boolean show = viewModel.detailResp.isOnlineBzpType() && viewModel.detailResp.publishLiveHighlight == 0 && viewModel.detailResp.autoPublishGet == 0 && viewModel.detailResp.hasLiveHighlight == 1;
                    if (show) {
                        viewModel.questionExplainFragmentLiveData.postValue(true);
                    }
                }
            }
        });
        viewModel.endOverRespLiveData.observe(getViewLifecycleOwner(), new Observer<BossEndOverviewResponse>() {
            @Override
            public void onChanged(BossEndOverviewResponse bossEndOverviewResponse) {
                liveReportView.setExplainViewData(bossEndOverviewResponse);
            }
        });
    }

    private void updateUi(BossEndOverviewResponse resp) {
        if (resp == null) return;
        this.overviewResponse = resp;
        coordinatorLayout.setVisibility(View.VISIBLE);

        if (resp.liveState == LiveState.DELAY) {// 超时结束
            tvLiveStateDesc.setText("请您下次预约直播后准时开播");
        } else {// 已结束
            if(resp.isOnlineNormalRoom()){
                tvLiveStateDesc.setText("请您下次预约直播后准时开播");
                String liveTime = "直播时长 ".concat(!TextUtils.isEmpty(resp.liveElapsedTime) ? resp.liveElapsedTime : "00:00:00");
                tvLiveStateDesc.setText(liveTime);
            }else{
                if (viewModel.isThisLiveBoss && resp.specialExposureNum > 0) {
                    tvLiveStateDesc.setTextColor(ContextCompat.getColor(activity, R.color.live_color_FFFFE8BB));
                    tvLiveStateDesc.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.live_ic_finish_proxy_boss_exposure, 0, 0, 0);
                    tvLiveStateDesc.setText(String.format(Locale.getDefault(), "本场专属流量：%d人", resp.specialExposureNum));
                    /*1204.713 本场 boss 身份，直播结束页面，「本场专属流量」字段隐藏（不要删除）*/
                    tvLiveStateDesc.setVisibility(View.GONE);
                }else {
                    if (resp.liveVideoFlag == 0) {// 无回放
                        tvLiveStateDesc.setText("本场直播暂无回放");
                    } else if (resp.liveVideoState == 1) {
                        tvLiveStateDesc.setText("回放生成中，稍后可观看");
                    } else if (resp.liveVideoFlag == 1 && !TextUtils.isEmpty(resp.liveVideoUrl)) {// 有回放
                        if (viewModel.isThisLiveBoss) {
                            tvLiveStateDesc.setText("以下为本场直播数据");
                        } else {
                            tvLiveStateDesc.setText("已为您生成回放");
                        }
                    }
                }
            }
        }

        //普通直播间跳转链接为空不显示按钮，高级直播间 (跳转链接为空 || 是本场boss[1012.802]) 不显示
        boolean viewMoreGone = TextUtils.isEmpty(resp.moreLiveReport) || (viewModel.isHighLiveRoom && viewModel.isThisLiveBoss);
        btnLiveData.setVisibility(viewMoreGone ? View.GONE : View.VISIBLE);
        if(!viewMoreGone){
            viewModel.addP4("全部数据");
        }

        tvLiveTitle.setText(resp.liveTitle);
        liveReportView.setData(resp, viewModel.isThisLiveBoss, viewModel.isExperienceLive, viewModel.isAnchor(),viewModel);
    }

    public void setPlaybackGenerating() {
        if (tvLiveStateDesc != null) {
            tvLiveStateDesc.setText("回放生成中，稍后可观看");
        }
    }

    private void initGeekNoOnlineFragments() {
        titles = new ArrayList<>();
        titles.add("投递牛人");
        mFragments = new ArrayList<>();
        mFragments.add(LiveFinishGeekListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_NONE));
        viewModel.addP4("投递牛人");
        SchoolFinishViewPagerAdapter mViewAdapter = new SchoolFinishViewPagerAdapter(getChildFragmentManager(), mFragments);
        vpGeekList.setAdapter(mViewAdapter);
    }

    private void initGeekOnlineFragments() {
        titles = new ArrayList<>();
        titles.add(LiveConstants.GeekDeliverName.TYPE_PHONE);
        titles.add(LiveConstants.GeekDeliverName.TYPE_WX);
        titles.add(LiveConstants.GeekDeliverName.TYPE_RESUME);
        mFragments = new ArrayList<>();
        mFragments.add(LiveFinishGeekListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_PHONE));
        mFragments.add(LiveFinishGeekListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_WX));
        mFragments.add(LiveFinishGeekListFragment.getInstance(LiveConstants.GeekDeliverType.TYPE_RESUME));
        viewModel.addP4(LiveConstants.GeekDeliverName.TYPE_PHONE);
        viewModel.addP4(LiveConstants.GeekDeliverName.TYPE_WX);
        viewModel.addP4(LiveConstants.GeekDeliverName.TYPE_RESUME);
        SchoolFinishViewPagerAdapter mViewAdapter = new SchoolFinishViewPagerAdapter(getChildFragmentManager(), mFragments);
        vpGeekList.setAdapter(mViewAdapter);
    }

    private void initIndicator(boolean isOnline) {
        commonNavigator = new CommonNavigator(activity);
        commonNavigator.setAdjustMode(false);
        commonNavigator.setSmoothScroll(false);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return titles.size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                BadgePagerTitleView badgePagerTitleView = new BadgePagerTitleView(context);
                ScaleTransitionPagerTitleView pagerTitleView = new ScaleTransitionPagerTitleView(context);
                pagerTitleView.setNormalColor(ContextCompat.getColor(context, R.color.color_FFFFFFFF));
                pagerTitleView.setSelectedColor(ContextCompat.getColor(context, R.color.color_FFFFFFFF));
                pagerTitleView.setMinScale(1f);
                pagerTitleView.setBothTextBold(false);
                pagerTitleView.setText(titles.get(index));
                pagerTitleView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                pagerTitleView.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        vpGeekList.setCurrentItem(index, false);
                        if (isOnline) {
                            LiveAnalysisUtil.reportLiveBossEndDeliverListClick(viewModel.liveRecordId, "0", titles.get(index), "", "");
                        }
                    }
                });
                badgePagerTitleView.setInnerPagerTitleView(pagerTitleView);
                return badgePagerTitleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                GradualChangeColorLinePagerIndicator indicator = new GradualChangeColorLinePagerIndicator(context);
                indicator.setGradualChangeColorStart(ContextCompat.getColor(context, R.color.color_FF0D9EA3));
                indicator.setGradualChangeColorEnd(ContextCompat.getColor(context, R.color.transparent));
                indicator.setMode(LinePagerIndicator.MODE_WRAP_CONTENT);
                indicator.setLineHeight(UIUtil.dip2px(context, 6));
                indicator.setRoundRadius(UIUtil.dip2px(context, 3));
                return indicator;
            }
        });
        magicIndicator.setNavigator(commonNavigator);
        LinearLayout titleContainer = commonNavigator.getTitleContainer(); // must after setNavigator
        titleContainer.setShowDividers(LinearLayout.SHOW_DIVIDER_MIDDLE);
        titleContainer.setDividerDrawable(new ColorDrawable() {
            @Override
            public int getIntrinsicWidth() {
                return UIUtil.dip2px(activity, 20);
            }
        });

        vpGeekList.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                magicIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                magicIndicator.onPageSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                magicIndicator.onPageScrollStateChanged(state);
            }
        });
    }

    /**
     * 设置筛选器的title
     *
     * @param title 为空展示"职位筛选"
     */
    private void setSelectorTitle(String title) {
        if (TextUtils.isEmpty(title)) {
            tvSelector.setText("职位筛选");
            tvSelector.setTextColor(ContextCompat.getColor(activity, R.color.color_99FFFFFF));
            ivSelectorArrow.setImageResource(R.mipmap.live_ic_school_finish_selector_arrow);
        } else {
            tvSelector.setText(title);
            tvSelector.setTextColor(ContextCompat.getColor(activity, R.color.color_FF2DB4B4));
            ivSelectorArrow.setImageResource(R.mipmap.bg_geek_all_tab_filter_select);
        }
    }

    /**
     * 查看更多数据
     */
    private void viewMoreDetail(String reportUrl) {
        if (!TextUtils.isEmpty(reportUrl)) {
            Intent intent = new Intent(activity, WebViewActivity.class);
            intent.putExtra(com.hpbr.bosszhipin.config.Constants.DATA_URL, reportUrl);
            AppUtil.startActivity(activity, intent);
        } else {
            ToastUtils.showText("本场直播的数据暂未生成");
        }
        LiveAnalysisUtil.campusLiveTryResultCk(viewModel.isExperienceLive, 2);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ReceiverUtils.unregister(activity, rejectActionReceiver);
        rejectActionReceiver = null;
    }

    /**
     * 牛人被设置不合适与取消不合适
     */
    private BroadcastReceiver rejectActionReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_FRIEND_REJECT_ACTION)) {
                if (viewModel == null || viewModel.deliveryGeekListObserver == null) return;
                boolean isReject = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
                long geekId = intent.getLongExtra(Constants.DATA_LONG2, 0L);

                BossGetDeliveryGeekListResponse geekListResponse = viewModel.deliveryGeekListObserver.getValue();
                if (geekListResponse != null) {
                    List<BossDeliveryGeekBean> geekList = geekListResponse.geekList;
                    List<BossDeliveryGeekBean> rejectGeekList = geekListResponse.rejectGeekList;

                    if (isReject) {// 被标记为不合适
                        if (!LList.isEmpty(geekList)) {
                            for (BossDeliveryGeekBean geekBean : geekList) {
                                if (geekBean == null) continue;
                                if (geekBean.geekId == geekId) {
                                    // 将牛人从投递列表挪到不合适列表
                                    geekList.remove(geekBean);
                                    rejectGeekList.add(0, geekBean);
                                    break;
                                }
                            }
                        }
                        // 清空栈中当前页面上面的所有activity
                        ForegroundUtils.get().finishUntilActivity(SchoolLiveFinishActivity.class);
                    } else {// 不合适牛人被标记为合适
                        if (!LList.isEmpty(rejectGeekList)) {
                            for (BossDeliveryGeekBean rejectGeekBean : rejectGeekList) {
                                if (rejectGeekBean == null) continue;
                                if (rejectGeekBean.geekId == geekId) {
                                    // 将牛人从不合适列表挪到投递列表
                                    rejectGeekList.remove(rejectGeekBean);
                                    geekList.add(0, rejectGeekBean);
                                    break;
                                }
                            }
                        }
                    }
                    viewModel.deliveryGeekListObserver.postValue(geekListResponse);
                }
            }
        }
    };
}
