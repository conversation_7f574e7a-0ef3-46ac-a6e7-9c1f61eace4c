package com.hpbr.bosszhipin.live.net.response;

import com.hpbr.bosszhipin.live.net.bean.MiniShareConfigBean;
import com.hpbr.bosszhipin.live.util.LiveState;

import net.bosszhipin.base.HttpResponse;

public class BossEndOverviewResponse extends HttpResponse {
    private static final long serialVersionUID = -2009199711129838891L;

    /**
     * boss id
     */
    public long bossId;
    /**
     * 直播房间ID，视频组开始直播时提供，可以为空
     */
    public String liveRoomId;
    /**
     * 宣讲标题
     */
    public String liveTitle;
    /**
     * 直播状态 0：待宣讲(未开始)  1：宣讲中  2：已结束	3：宣讲已过时(但未开始) 4：暂停
     */
    @LiveState.State
    public int liveState;
    /**
     * boss 端 加密的ID
     */
    public String recordId;
    /**
     * 查看人数，也就是累计人数，904版本以后直播中显示累计人数
     */
    public int liveViewersCnt;
    public int deliverWxCount;
    public int deliverPhoneCount;
    /**
     * 直播回放地址(编辑后的视频) 没有就是空
     */
    public String liveVideoUrl;
    /**
     * 结束直播后，是否保存了视频
     */
    public int liveVideoFlag;
    /**
     * 消耗时长，结束状态会有
     */
    public String liveElapsedTime;
    /**
     * 简历投递数量
     */
    public int resumeDeliverCnt;
    /**
     * 数据报告URL，用于查看更多数据报告使用
     */
    public String moreLiveReport;
    /**
     * 房间级别类型：1:普通；2:高级；
     */
    public int roomLevel;
    /**
     * 房间类型  1：测试  2：正式    3：试播  4:活动  5:体验;
     */
    public int liveRoomType;
    /**
     * -1:未知 1 app 2 地址播放 3 obs 4：PC
     */
    public int addressPlayUrlType;
    /**
     * 914 是否有直播回答
     */
    public boolean hasAnswers;
    // 使用反馈url
    public String feedbackUrl;
    /**
     * 1直播间、2直播学院(BOSS作为观众时会用到，用于区分BOSS是否是直播学院进来)
     * 3专场直播，4带播  5.课程直播  6.导播间
     */
    public int roomKind;
    public boolean canShare;// 是否可以分享
    public int canRecoverLive;// 可以恢复直播，0 不可以 1 可以
    public int hasExplain;// 是否有讲解录制，1 有，0 没有（直播结束后用）
    public int notExplainRecordJobNum;//1210 未讲解职位数量
    public int orderType;// 支付类型（只有1才算线上支付）;0: 未选择；1：线上支付；2：线上定制；3:线下支付；4:免费；
    public int bzpType;// 支付类型：0：无（高级直播间、体验、试播）；1:线上支付；2:线下支付；

    /**
     * 分享标题
     */
    public String shareTitle;
    /**
     * 分享描述
     */
    public String shareDesc;
    /**
     * 分享配图Url
     */
    public String shareImage;
    /**
     * 分享URL
     */
    public String shareUrl;
    /**
     * 小程序分享的配置信息
     */
    public MiniShareConfigBean miniAppShareConfig;

    public int completedAchievementNum;// 完成成就个数
    public String achievementH5Url;// 成就H5页面Url，若为空，则不显示成就入口

    /*@since 1010 打投心动榜展示地址*/
    public String showBrandIntention;

    /*@since 1014 爱才热力榜单H5页面Url*/
    public String heatPowerH5Url;

    public int goldAnchor;// 1-本场直播是否弹金牌主播
    public long goldAnchorExpireTime;// 金牌主播有效期时间戳，精确到0时刻
    public String goldAnchorWebp;// 弹窗动图地址
    public String goldAnchorBackground;// 弹窗动图地址

    public String luckyDrawTrackingNumberH5Url;// boss抽奖快递单号填写页面

    public int liveVideoState;// 0是未生成 1是生成中 2是已生成
    /**
     * 屏幕方向；1.横屏、2.竖屏
     */
    public int scrnOrient;

    public int specialExposureNum;// 专属曝光流量（本场BOSS使用）
    public int hasLiveHighlight;//是否有直播高光：0 没有，1 有
    public int publishLiveHighlight;//是否发布了直播高光：0 待发布，1 已发布
    public int autoPublishGet;// 后续是否自动将直播高光发布到有了社区：0 不自动发布，1 自动发布

    public boolean isOBSLive(){
        return addressPlayUrlType == 3;
    }

    public boolean isHasLiveHighlight(){
        return hasLiveHighlight == 1;
    }

    public boolean hasExplainOrHighlight(){
        return hasExplain == 1 || hasLiveHighlight == 1;
    }

    public boolean isNormalRoom() {
        return roomLevel == 1;
    }

    public boolean isOnlineBzpType() {
        return bzpType == 1;
    }

    public boolean isOnlineNormalRoom(){
        return isNormalRoom() && isOnlineBzpType();
    }

    @Override
    public String toString() {
        return "BossEndOverviewResponse{" +
                "bossId=" + bossId +
                ", liveRoomId='" + liveRoomId + '\'' +
                ", liveTitle='" + liveTitle + '\'' +
                ", liveState=" + liveState +
                ", recordId='" + recordId + '\'' +
                ", liveViewersCnt=" + liveViewersCnt +
                ", liveVideoUrl='" + liveVideoUrl + '\'' +
                ", liveVideoFlag=" + liveVideoFlag +
                ", liveElapsedTime='" + liveElapsedTime + '\'' +
                ", resumeDeliverCnt=" + resumeDeliverCnt +
                ", moreLiveReport='" + moreLiveReport + '\'' +
                ", roomLevel=" + roomLevel +
                ", liveRoomType=" + liveRoomType +
                ", addressPlayUrlType=" + addressPlayUrlType +
                ", hasAnswers=" + hasAnswers +
                ", feedbackUrl='" + feedbackUrl + '\'' +
                ", roomKind=" + roomKind +
                ", canShare=" + canShare +
                ", canRecoverLive=" + canRecoverLive +
                ", hasExplain=" + hasExplain +
                ", notExplainRecordJobNum=" + notExplainRecordJobNum +
                ", orderType=" + orderType +
                ", bzpType=" + bzpType +
                ", shareTitle='" + shareTitle + '\'' +
                ", shareDesc='" + shareDesc + '\'' +
                ", shareImage='" + shareImage + '\'' +
                ", shareUrl='" + shareUrl + '\'' +
                ", miniAppShareConfig=" + miniAppShareConfig +
                ", completedAchievementNum=" + completedAchievementNum +
                ", achievementH5Url='" + achievementH5Url + '\'' +
                ", showBrandIntention='" + showBrandIntention + '\'' +
                ", heatPowerH5Url='" + heatPowerH5Url + '\'' +
                ", goldAnchor=" + goldAnchor +
                ", goldAnchorExpireTime=" + goldAnchorExpireTime +
                ", goldAnchorWebp='" + goldAnchorWebp + '\'' +
                ", goldAnchorBackground='" + goldAnchorBackground + '\'' +
                ", luckyDrawTrackingNumberH5Url='" + luckyDrawTrackingNumberH5Url + '\'' +
                ", liveVideoState=" + liveVideoState +
                ", scrnOrient=" + scrnOrient +
                ", specialExposureNum=" + specialExposureNum +
                '}';
    }
}
