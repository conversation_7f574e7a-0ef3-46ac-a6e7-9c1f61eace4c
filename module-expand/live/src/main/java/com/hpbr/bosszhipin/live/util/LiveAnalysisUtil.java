package com.hpbr.bosszhipin.live.util;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.JsonObject;
import com.hpbr.bosszhipin.beauty.bean.BeautyConfigBean;
import com.hpbr.bosszhipin.beauty.bean.MakeupConfigBean;
import com.hpbr.bosszhipin.beauty.bean.PasterConfigBean;
import com.hpbr.bosszhipin.chat.export.bean.AiLiveGenPromptBean2;
import com.hpbr.bosszhipin.constant.CommonConstant;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.live.boss.util.TimeUtil;
import com.hpbr.bosszhipin.live.campus.audience.moduel.SpecialTopicModel;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.export.bean.AnchorUserBean;
import com.hpbr.bosszhipin.live.export.bean.BossTicketListBean;
import com.hpbr.bosszhipin.live.export.bean.ExposureTypeBean;
import com.hpbr.bosszhipin.live.export.bean.JobsBean;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.bean.BossInviteDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.bean.GeekQuickQuestionQueryBean;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.bean.LiveRecordItemBean;
import com.hpbr.bosszhipin.live.net.bean.LiveRecruitBannerBean;
import com.hpbr.bosszhipin.live.net.bean.RecommendLiveInfoBean;
import com.hpbr.bosszhipin.live.net.bean.SpecialLiveIconBean;
import com.hpbr.bosszhipin.live.net.response.GetLiveRecruitRecordsResponse.RecordsBean;
import com.hpbr.bosszhipin.live.net.response.LiveExposurePreviewResponse;
import com.hpbr.bosszhipin.live.net.response.LiveSpecialTagListResponse;
import com.hpbr.bosszhipin.live.net.response.RecordDetailForModifyResponse;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.net.bean.LiveInfoBean;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * 直播相关埋点
 */
public class LiveAnalysisUtil {

    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2953&wikiCount=1&wikiLink=https%3A%2F%2Fwiki.kanzhun-inc.com%2Fpages%2Fviewpage.action%3FpageId%3D62824363
     */
    public static void extensionCampusliveListpagebDetail(@NonNull RecordsBean recordsBean) {
        String liveStateStr = null; // 直播状态 0：待宣讲 1：宣讲中 2：已结束 3：宣讲未开始 4:暂停状态
        switch (recordsBean.liveState) {
            case 0:
                liveStateStr = "待宣讲";
                break;
            case 1:
                liveStateStr = "宣讲中";
                break;
            case 2:
                liveStateStr = "已结束";
                break;
            case 3:
                liveStateStr = "宣讲未开始";
                break;
            case 4:
                liveStateStr = "暂停状态";
                break;
        }

        String auditStateStr = null; // 审核状态：0-申请中，1-通过，2-驳回，3-失效
        switch (recordsBean.auditState) {
            case 0:
                auditStateStr = "申请中";
                break;
            case 1:
                auditStateStr = "通过";
                break;
            case 2:
                auditStateStr = "驳回";
                break;
            case 3:
                auditStateStr = "失效";
                break;
        }

        AnalyticsFactory.create().action("extension-campuslive-listpageb-detail")
                .param("p", recordsBean.liveTitle) // 直播标题
                .param("p2", recordsBean.recordId) // 直播id
                .param("p3", liveStateStr != null ? liveStateStr : "") // 直播状态
                .param("p5", recordsBean.startTime) // 直播时间
                .param("p6", auditStateStr != null ? auditStateStr : "") // 审核状态
                .param("p7", recordsBean.powerType == -3 ? 0 : 1) //1009新增 0直播创建人点击 1同事点击
                .build();
    }


    /**
     * 牛人在校招直播的直播间里，点击校招职位列表查看职位（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62817325）
     * p1:直播id
     * p2:直播状态
     * p3:职位id：jobSecurityId
     * p4:点击来源  @职位：at  ；求讲解: explain  ；投简历：resume  ；职位列表： list
     * p5:913视频流类型：1-实时流 2-回放流（预告及回放传空）
     */
    public static void traceLookPositionClick(
            String liveRecordId,
            int state,
            String jobSecurityId,
            String from,
            String streamType,
            long bossId,
            String encryptJobGroupId
    ) {
        AnalyticsFactory.create().action("extension-campuslive-job-detail")
                .param("p", liveRecordId)
                .param("p2", getLiveStateValue(state))
                .param("p3", jobSecurityId)
                .param("p4", from)
                .param("p5", streamType)
                .param("p6", bossId)
                .param("p7", encryptJobGroupId)
                .debug()
                .build();
    }

    public static String getSourceByLabelType(int labelType) {
        //1-刚刚看过，2-你关注过，3-与你匹配, 4-推荐卡(本地记录的状态)
        switch (labelType) {
            case 1:
                return CampusConstant.ClickJumpJobDetailFrom.FROM_NOW_SEE;
            case 2:
                return CampusConstant.ClickJumpJobDetailFrom.FROM_YOU_ATTENTION;
            case 3:
                return CampusConstant.ClickJumpJobDetailFrom.FROM_MATCH_JOB_CARD;
            case 4:
                return CampusConstant.ClickJumpJobDetailFrom.FROM_RECOMMEND_JOB_CARD;
        }
        return "";
    }

    /**
     * 牛人 and Boss
     * 用户在校招直播页，点击分享按钮（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62824363）
     * p1:直播id
     * p2:直播状态（「尚未开始」、「直播中」、「已结束」）
     */
    public static void traceLiveShareClick(String liveRecordId, int state) {
        AnalyticsFactory.create().action("extension-campuslive-livepageb-share")
                .param("p", liveRecordId)
                .param("p2", getLiveStateValue(state))
                .build();
    }


    /**
     * 牛人在直播界面点击「投简历」时，无简历时，在推弹出的引导窗口点击「去上传」（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62817325）
     * p1:直播id
     */
    public static void traceGoUploadResumeClick(String liveRecordId, String buttonText, JobInfoBean jobInfoBean) {
        AnalyticsFactory.create().action("extension-campuslive-noresume-click")
                .param("p", liveRecordId)
                .param("p2", buttonText)/*按钮字符串*/
                .param("p3", jobInfoBean != null ? jobInfoBean.securityId : "")/*jobId*/
                .build();
    }

    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2954&wikiCount=1&wikiLink=https%3A%2F%2Fwiki.kanzhun-inc.com%2Fpages%2Fviewpage.action%3FpageId%3D62824363
     * 1005.822高级直播间新增「节目单」按钮
     *
     * @param startTime  直播开始时间
     * @param tabName    tab 品牌信息、更多、公告、直播问答、职位、节目单、企业榜、直播详情
     * @param streamType 913视频流类型：1-实时流 2-回放流（预告及回放不传）
     */
    public static void liveTabChange(String recordId, long startTime, String tabName, @LiveState.State int state, String intentionValue, String streamType) {
        AnalyticsFactory.create().action("extension-campuslive-livepageb-tab")
                .param("p", recordId)
                .param("p2", startTime)
                .param("p3", tabName)
                .param("p4", intentionValue)/*	如p3为心动榜，传当时心动值，否则为空*/
                .param("p5", getLiveStateValue(state))
                .param("p6", streamType)
                .debug()
                .build();
    }

    /**
     * 牛人在直播界面点击「投简历」时，弹出无简历弹窗，初次预约弹去上传，投递弹去沟通
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2971&wikiCount=1&wikiLink=https%3A%2F%2Fwiki.kanzhun-inc.com%2Fpages%2Fviewpage.action%3FpageId%3D62817325
     *
     * @param btnTitle 按钮字符串：去上传/去沟通
     */
    public static void liveNoResume(String recordId, String btnTitle, JobInfoBean jobInfoBean) {
        AnalyticsFactory.create().action("extension-campuslive-noresume-push")
                .param("p", recordId)
                .param("p2", btnTitle)
                .param("p3", jobInfoBean != null ? jobInfoBean.encryptJobId : "")
                .param("p4", jobInfoBean != null ? jobInfoBean.bossId : 0)
                .build();
    }

    /**
     * 牛人在校招直播间的校招职位列表，点击卡片，在卡片详情底部点击"投递简历"
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2978&wikiCount=1&wikiLink=https%3A%2F%2Fwiki.kanzhun-inc.com%2Fpages%2Fviewpage.action%3FpageId%3D62817325
     * 1009.813 更新参数p5，区分点击按钮的位置
     *
     * @param btnTitle 按钮实际名称：投简历/去沟通
     */
    public static void liveResumeSend(String recordId, JobInfoBean jobInfoBean, String btnTitle, String clickSource) {
        if (jobInfoBean == null) return;
        liveResumeSend(recordId, jobInfoBean.securityId, btnTitle, clickSource);
    }

    public static void liveResumeSend(String recordId, String securityId, String btnTitle, String clickSource) {
        AnalyticsFactory.create().action("extension-campuslive-resume-send")
                .param("p", recordId)
                .param("p3", securityId)
                .param("p4", btnTitle)
                .param("p5", clickSource)
                .build();
    }

    private static String getLiveStateValue(@LiveState.State int state) {
        String value; // 直播状态（「尚未开始」、「直播中」、「已结束」）
        switch (state) {
            case LiveState.PUBLISHING:
            case LiveState.PAUSED:
                value = "直播中";
                break;
            case LiveState.WAIT:
                value = "尚未开始";
                break;
            case LiveState.FINISH:
            case LiveState.DELAY:
                value = "已结束";
                break;
            default: {
                value = "";
            }
        }
        return value;
    }

    /**
     * 牛人在直播间点击「投简历」时，对弹出的确认弹出点击（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62817325）
     * p1:直播id
     * p2:职位id
     * p3:点击位置（0-暂不投递，1-立即投递）
     */
    public static void traceSubmitResumeClick(String liveRecordId, JobInfoBean jobInfoBean, int action) {
        if (jobInfoBean == null) return;
        AnalyticsFactory.create().action("extension-campuslive-resume-click")
                .param("p", liveRecordId)
                .param("p2", jobInfoBean.securityId)
                .param("p3", action)
                .param("p4", jobInfoBean.bossId)
                .build();
    }

    /**
     * 牛人在校招直播界面的「问答」tab，对问答点击有用（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=62817325）
     * p1:直播id
     * p2:问答问题内容
     * p3:问答id
     */
    public static void traceGiveALikeClick(String liveRecordId, String question, long qaId) {
        AnalyticsFactory.create().action("extension-campuslive-qa-useful")
                .param("p", liveRecordId)
                .param("p2", question)
                .param("p3", qaId)
                .build();
    }

    /**
     * 牛人在校招直播列表页点击直播卡片（https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2961）
     * p1:直播id live_record_id
     * p2:直播状态（0-未开始，1-正在直播，2-已结束/回放）
     * p3:0-默认未进列表 3-点击气泡进入列表 4-icon进入列表
     */
    public static void listPageClick(String recordId, int liveState, int listSource, String topTabName, String subTabName, String date) {
        AnalyticsFactory.create().action("extension-campuslive-listpagec-click")
                .param("p", recordId)
                .param("p2", convertLiveState(liveState))
                .param("p3", listSource)
                .param("p4", topTabName)/*	对应顶部tab页面：「最近直播」、「往期回放」*/
                .param("p5", subTabName)/*对应三个分类tab：「推荐」、「全部」、「专场」或者 当无分类tab时，返回空*/
                .param("p6", date)/*对应日历tab：点击日历对应的日期*/
                .debug()
                .build();
    }

    /**
     * 直播列表曝光（https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=5703）
     * p1:曝光直播id，用英文逗号分隔
     * p2:对应曝光直播状态（「尚未开始」、「直播中」、「已结束」）与上面p1一一对应
     * p3:来源——3-气泡、4-icon
     */
    public static <T> void listPageExpose(List<T> liveItemList, int source, String topTabName, String subTabName) {
        if (LList.getCount(liveItemList) == 0) return;
        String recordIds = "";
        String liveStates = "";
        String dates = "";

        List<String> recordIdList = new ArrayList<>();
        List<String> liveStateList = new ArrayList<>();
        List<String> dateList = new ArrayList<>();//只有「全部」Tab下才埋日期

        for (T liveItemBean : liveItemList) {
            if (liveItemBean == null) continue;
            if (liveItemBean instanceof RecommendLiveInfoBean) {/*「推荐」列表*/
                RecommendLiveInfoBean recommendLiveInfoBean = (RecommendLiveInfoBean) liveItemBean;
                recordIdList.add(recommendLiveInfoBean.liveRecordId);
                liveStateList.add(convertLiveState(recommendLiveInfoBean.liveState));
            } else if (liveItemBean instanceof LiveInfoBean) {/*「全部」列表*/
                LiveInfoBean liveInfoBean = (LiveInfoBean) liveItemBean;
                recordIdList.add(liveInfoBean.liveRecordId);
                liveStateList.add(convertLiveState(liveInfoBean.liveState));
                dateList.add(DateUtil.getFormatDate(liveInfoBean.startTime));
            } else if (liveItemBean instanceof LiveRecordItemBean) {/*「往期回放」列表*/
                LiveRecordItemBean liveRecordItemBean = (LiveRecordItemBean) liveItemBean;
                recordIdList.add(liveRecordItemBean.liveRecordId);
                liveStateList.add(convertLiveState(liveRecordItemBean.liveState));
            }
        }

        if (!LList.isEmpty(recordIdList) && LList.getCount(recordIdList) == LList.getCount(liveStateList)) {
            recordIds = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, recordIdList);
            liveStates = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, liveStateList);
            if (LList.getCount(recordIdList) == LList.getCount(dateList)) {//只有「全部」Tab下才埋日期
                dates = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, dateList);
            }
        }

        AnalyticsFactory.create().action("extension-campuslive-listpagec-expose")
                .param("p", recordIds)/*	曝光直播id（看到的） or list */
                .param("p2", liveStates)/*对应曝光直播状态（「尚未开始」、「直播中」、「已结束」）*/
                .param("p3", source)/*来源——3-气泡、4-icon */
                .param("p4", topTabName)/*对应顶部tab页面：「最近直播」、「往期回放」*/
                .param("p5", subTabName)/*对应三个分类tab：「推荐」、「全部」、「专场」*/
                .param("p6", dates)/*	对应日历tab：当前曝光列表对应的日期*/
                .debug()
                .build();
    }

    /**
     * 回放部分支持跳至定位点观看，捕捉曝光情况
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=22753
     *
     * @param recordId 直播间id
     * @param jobIds   职位id组，多个以英文逗号分隔
     */
    public static void playExpose(String recordId, String jobIds) {
        AnalyticsFactory.create().action("extension-campuslive-livepageb-playexpose")
                .param("p", recordId)
                .param("p2", jobIds)
                .build();
    }

    /**
     * 回放部分支持跳至定位点观看，用户点击定点播放按钮
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=22754
     *
     * @param recordId 直播间id
     * @param jobId    职位id
     */
    public static void playClick(String recordId, String jobId) {
        AnalyticsFactory.create().action("extension-campuslive-livepageb-playclick")
                .param("p", recordId)
                .param("p2", jobId)
                .build();
    }

    /**
     * boss在APP端点击发布抽奖按钮
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=23852
     *
     * @param recordId 直播间id
     * @param p2       草稿id
     */
    public static void luckPublicClick(String recordId, String p2) {
        AnalyticsFactory.create().action("extension-campuslive-luck-pubck")
                .param("p", recordId)
                .param("p2", p2)
                .build();
    }

    /**
     * boss在APP直播中发布抽奖后点击确认发布
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=23853
     *
     * @param recordId 直播间id
     * @param p2       草稿id
     * @param p3       按钮：1-确认发布 0-返回
     */
    public static void luckPublicConfirmBtnClick(String recordId, String p2, int p3) {
        AnalyticsFactory.create().action("extension-campuslive-luck-pubconfirm")
                .param("p", recordId)
                .param("p2", p2)
                .param("p3", p3)
                .build();
    }

    /**
     * 埋点：B端雇主创建直播间，选择职位列表点击下一步
     *
     * @param isSocailType 是否是社招
     * @param selectedJobs
     */
    public static void dotCampuslivePricebossJobsetnext(int isSocailType, ArrayList<JobsBean> selectedJobs, BossTicketListBean ticketListBean) {
        List<String> connectJobIds = new ArrayList<>();
        if (LList.getCount(selectedJobs) > 0) {
            for (JobsBean jobsBean : selectedJobs) {
                if (jobsBean == null) continue;
                connectJobIds.add(jobsBean.jobId);
            }
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_PRICEBOSS_JOBSETNEXT)
                .param("p", isSocailType)/*招聘类型 1-校招 2-社招 4-ats 5-蓝领 6-白领*/
                .param("p2", StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, connectJobIds))/*职位id组，从上到下顺序，逗号隔开，字符串*/
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .param("p22", GlobalStatus.isHaveAvailableTicket() ? 1 : 0)/*判断该用户是否有可用的免费券（未失效 未使用），有返回1，没有返回0*/
                .build();
    }

    /**
     * 埋点：B端雇主点击立即支付
     */
    public static void dotCampusLivePriceBossBzbCk(String recordId, LiveExposurePreviewResponse liveExposurePreviewResponse, ExposureTypeBean exposureTypeBean,
                                                   int payScene, BossTicketListBean ticketListBean) {
        if (exposureTypeBean == null || liveExposurePreviewResponse == null) return;
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_PRICEBOSS_BZBCK)
                .param("p2", 0)/*是否为推荐曝光 0-否 1-是（推荐功能已下掉）*/
                .param("p3", String.format(Locale.getDefault(), "%s,%s",
                        exposureTypeBean.minRecommendNum, exposureTypeBean.maxRecommendNum))/*用户选择的预计观看人数（上限，下限）*/
                .param("p4", liveExposurePreviewResponse.expectedDeliverNumDesc)/*预计收获简历（上限，下限）数组*/
                .param("p5", StringUtil.getRMB(liveExposurePreviewResponse.originalAmount))/*原价（直豆）*/
                .param("p6", StringUtil.getRMB(liveExposurePreviewResponse.finallyAmount))/*内测价，916修改为：消耗直豆*/
                .param("p7", recordId)/*直播id*/
                .param("p9", String.valueOf(payScene))/*	支付类型 ：1 创建直播间*/
                .param("p10", exposureTypeBean.giftPersonNum)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .param("p22", GlobalStatus.isHaveAvailableTicket() ? 1 : 0)/*判断该用户是否有可用的免费券（未失效 未使用），有返回1，没有返回0*/
                .debug()
                .build();
    }

    /**
     * 埋点：B端雇主创建直播间支付成功
     */
    public static void dotCampusLivePriceBossBzbSuc(String recordId, LiveExposurePreviewResponse liveExposurePreviewResponse, ExposureTypeBean exposureTypeBean,
                                                    int payScene, BossTicketListBean ticketListBean,
                                                    String dateStr, String timeStr, String jobIds) {
        if (exposureTypeBean == null || liveExposurePreviewResponse == null) return;

        String industryName = "";
        BossInfoBean bossInfoBean = UserManager.getBossInfo();
        if (bossInfoBean != null && !LList.isEmpty(bossInfoBean.brandList) && bossInfoBean.brandList.get(0) != null) {
            industryName = bossInfoBean.brandList.get(0).industryName;
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_PRICEBOSS_BZBSUC)
                .param("p", recordId)/*	直播id*/
                .param("p3", 0)/*是否为推荐曝光 0-否 1-是（推荐功能已下掉）*/
                .param("p4", String.format(Locale.getDefault(), "%s,%s",
                        exposureTypeBean.minRecommendNum, exposureTypeBean.maxRecommendNum))/*预计观看人数，916：预计观看人数（上限，下限）*/
                .param("p5", liveExposurePreviewResponse.expectedDeliverNumDesc)/*预计简历数，916：预计简历（上限，下限）*/
                .param("p6", StringUtil.getRMB(liveExposurePreviewResponse.originalAmount))/*原价（直豆）*/
                .param("p7", StringUtil.getRMB(liveExposurePreviewResponse.finallyAmount))/*内测价，916修改为：消耗直豆*/
                .param("p9", String.valueOf(payScene))/*支付类型 ：1 创建直播间*/
                .param("p10", exposureTypeBean.giftPersonNum)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p16", dateStr)// 预约直播日期：日期，格式：××××-××-××
                .param("p17", timeStr)// 预约直播时间：时间点 格式：××:××
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .param("p19", jobIds)// 不加密jobId集合，以英文逗号分隔
                .param("p20", industryName)
                .param("p22", GlobalStatus.isHaveAvailableTicket() ? 1 : 0)/*判断该用户是否有可用的免费券（未失效 未使用），有返回1，没有返回0*/
                .debug()
                .build();
    }

    /**
     * 埋点：boss在APP端预约直播流程中点击左上角退出/回到前页
     */
    public static void dotCampusAppointmentExit(String recordId, @CampusConstant.CampusAppointmentExitType int exitType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_EXIT)
                .param("p", TextUtils.isEmpty(recordId) ? "" : recordId)/*	直播id*/
                .param("p2", String.valueOf(exitType))/*退出前位置1-基本信息界面2-可选功能界面3-报价界面*/
                .param("p18", GlobalStatus.bzpType)/*	bzpType// 支付类型 1线上(个人) 2线下(品牌)*/
                .build();
    }

    /**
     * 埋点：boss在APP端预约校招直播第一页面，点击"下一步"
     */
    public static void dotCampusAppointmentNext(RecordDetailForModifyResponse bean, long startTime, List<String> selectedJobs, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_NEXT)
                .param("p", bean != null ? bean.liveTitle : "") // 直播标题
                .param("p2", TimeUtil.uiFormat(startTime)) // 直播时间
                .param("p3", bean != null ? String.valueOf(bean.liveScenario) : "")//招聘类型 1-校招 2-社招（来源为选择职位页面招聘类型按钮）
                .param("p4", StringUtil.connectTextWithChar(",", selectedJobs)) // 招聘职位ID
                .param("p5", getP5ParamValue(bean)) // 直播封面 ：如果为预设传编号 1或2，自己上传则为url
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .debug()
                .build();
    }

    /**
     * 埋点：boss在APP端预约校招直播可选项界面，点击下一步成功跳转
     */
    public static void dotCampusAppointmentNextTop(RecordDetailForModifyResponse bean, BossTicketListBean ticketListBean) {
        if (null == bean) return;
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_NEXTTOP)
                .param("p", null != bean.recordId ? bean.recordId : "")/*	直播id*/
                .param("p2", bean.anchorUser != null ? bean.anchorUser.encryptUserId : "")/*选择同事：有传boss id，无则传空*/
                .param("p3", getEncryptAdminUserIds(bean))/*协助同事：有则传boss id，由于可选多个boss，按逗号隔开，存为字符串，无则传空*/
                .param("p4", bean.addressPlayUrlType == 3 ? "1" : "0")/*OBS开关 0-关 1-开*/
                .param("p5", bean.deliverNumLimit > 0 ? bean.deliverNumLimit : -1)/*投递限制 -1-不限制，其他传数字*/
                .param("p6", String.format(Locale.getDefault(), "%d-%d", bean.supportLuckyDraw, bean.luckyDrawPassCard))/*告知抽奖开关 0-关 1-开 1103.803更新，用"-"在原有字段后拼接是否包含笔试面试直通卡，0-没有，1-有；更新后字段如"0-0"，"1-0"，"1-1"*/
                .param("p7", bean.openingVideoInfo != null ? bean.openingVideoInfo.originVideoUrl : "")/*宣传视频：URL？*/
                .param("p8", null != bean.speakerName ? bean.speakerName : "")/*展示信息中的姓名*/
                .param("p9", null != bean.speakerDuty ? bean.speakerDuty : "")/*展示信息中的职位*/
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .build();
    }

    /**
     * boss在APP点击修改预约的直播
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24187
     *
     * @param recordId 直播ID
     */
    public static void campusLiveAppointmentFix(String recordId) {
        AnalyticsFactory.create().action("extension-campuslive-appointment-fix")
                .param("p", recordId)
                .build();
    }

    /**
     * 体验直播间，boss在APP体验直播间中体验时长，从开始直播到结束直播
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24195
     *
     * @param duration 时长/秒
     */
    public static void campusLiveTryDuration(boolean isExperienceLive, int duration) {
        if (!isExperienceLive) return;
        AnalyticsFactory.create().action("extension-campuslive-try-duration")
                .param("p", duration)
                .build();
    }

    /**
     * 体验直播间，boss在APP体验直播间结果界面点击按钮
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24184
     *
     * @param p 1-重新直播 2-查看更多数据 3-观看回放
     */
    public static void campusLiveTryResultCk(boolean isExperienceLive, int p) {
        if (!isExperienceLive) return;
        AnalyticsFactory.create().action("extension-campuslive-try-resultck")
                .param("p", p)
                .build();
    }

    /**
     * 体验直播间，boss在APP体验直播中对确认退出弹窗的选择
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24180
     *
     * @param p  横竖屏模式：0-横屏 1-竖屏
     * @param p2 横屏下：0-继续 1-滑动结束 4-暂停/竖屏下：0-取消/1-确定
     */
    public static void campusLiveTryExitConfirm(boolean isExperienceLive, int p, int p2) {
        if (!isExperienceLive) return;
        AnalyticsFactory.create().action("extension-campuslive-try-exitconfirm")
                .param("p", p)
                .param("p2", p2)
                .build();
    }

    /**
     * 体验直播间，boss在APP端点击退出体验直播
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24179
     */
    public static void campusLiveTryExitCk(boolean isExperienceLive) {
        if (!isExperienceLive) return;
        AnalyticsFactory.create().action("extension-campuslive-try-exitck")
                .build();
    }

    /**
     * 体验直播间，boss在APP端体验直播中点击右下功能区按钮
     * https://datastar.kanzhun-inc.com/dashboard/warlock/event/20?showdetail=24178
     *
     * @param p  icon名称：按904版顺序从左到右，1-暂停，2-美颜，3-镜头切换 4-更多/备注
     * @param p2 如点击icon为相机镜头切换，则：1:切换至后置，-1:切换回前置，否则传99
     */
    public static void campusLiveTryFuncCk(boolean isExperienceLive, int p, int p2) {
        if (!isExperienceLive) return;
        AnalyticsFactory.create().action("extension-campuslive-try-funcck")
                .param("p", p)
                .param("p2", p2)
                .build();
    }

    /**
     * 埋点：boss在APP创建直播间创建直播间
     *
     * @param source 来源：跳转链接中source参数，1:APP直播招聘icon，2:H5跳转，3:直播间内，5.免费流量包弹层
     * @param url    如来源为直播间内，传url协议
     */
    public static void dotCampusLiveAppointmentCreateCk(int source, String url) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_CREATECK)
                .param("p", source)
                .param("p2", url)
                .param("p22", GlobalStatus.isHaveAvailableTicket() ? 1 : 0)/*判断该用户是否有可用的免费券（未失效 未使用），有返回1，没有返回0*/
                .debug()
                .build();
    }

    /**
     * boss在APP端直播结束页面点击功能按钮
     *
     * @param p  直播id
     * @param p2 全部数据（直播数据详情），牛人榜，讲解视频，观看回放，上传抽奖订单
     * @param p3 身份 1-主播 2-管理端 3-本场其他boss
     */
    public static void campusLivePopEndFunc(String p, String p2, int p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_ENDFUNC)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)//身份 1-主播 2-管理端 3-本场其他boss
                .debug()
                .build();
    }

    public static void campusLivePopEndExplainFunc(String p, String p2, int p3, int hasExplainSlice) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_ENDFUNC)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)//身份 1-主播 2-管理端 3-本场其他boss
                .debug()
                .build();
    }


    /**
     * B端APP直播结束后弹出结束页面
     *
     * @param p  直播id
     * @param p2 0-未生成回放 1-已生成回放
     * @param p3 身份 1-主播 2-管理端 3-本场其他boss
     * @param p4 全部数据、牛人榜、 直播讲解、直播成就、观看回放、上传抽奖订单、电话投递、微信投递，简历投递、职位筛选
     */
    public static void campusLivePopEnd(String p, int p2, int p3, String p4, @Nullable List<BossDeliveryGeekBean> geekList) {
        String p5 = StringUtil.connectTextWithCharNew(StringUtil.SPLIT_CHAR_COMMA, geekList, bossDeliveryGeekBean -> bossDeliveryGeekBean.encryptGeekId);
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_END)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .param("p5", p5)
                .debug()
                .build();
    }

    /**
     * 牛人在APP端观看直播中弹出boss投递回复的提醒气泡
     *
     * @param p 直播id
     */
    public static void campusLivePopReply(String p) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_REPLY)
                .param("p", p)
                .build();
    }

    /**
     * boss在APP端直播中对职位或职位组进行置顶/取消置顶操作
     *
     * @param p  直播id
     * @param p2 职位/职位组id
     * @param p3 1-置顶 -1-取消置顶
     * @param p4 当时职位/职位组简历数
     * @param p5 端口身份：1-主播 2-管理者
     */
    public static void campusLiveTopPositionClick(String p, String p2, int p3, int p4, int p5) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_TOPPOSITION_CLICK)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .param("p5", p5)
                .build();
    }

    /**
     * boss在APP段直播中点击右下角圆形功能按钮
     *
     * @param p  直播id
     * @param p2 身份 1-主播 2-管理者
     * @param p3 按钮名称：主播端：职位、美颜、翻转、发布抽奖、抽奖结果、电脑端；管理者端：职位、抽奖、牛人排行榜、视频连线、关闭字幕、开启字幕
     */
    public static void campusLiveManageFuncCk(String p, int p2, String p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_FUNCCK)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)
                .build();
    }

    /**
     * 牛人在APP观看直播中对弹出的邀请投递窗口点击'投递'
     *
     * @param p  直播id
     * @param p2 职位id，如无匹配弹出列表则传0
     */
    public static void campusLivePopInviteCk(String p, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_INVITECK)
                .param("p", p)
                .param("p2", p2)
                .build();
    }

    /**
     * boss在APP端直播中在牛人卡片点击邀请投递蓝框
     *
     * @param p  直播id
     * @param p2 身份 1-主播 2-管理员
     * @param p3 geek id
     * @param p4 剩下邀请名额数
     * @param p5 后端返回的结果message内容，code=1报错时传，成功为空
     */
    public static void campusLiveManageInviteConfirm(String p, int p2, String p3, int p4, String p5) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_INVITE_CONFIRM)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .param("p5", p5)
                .build();
    }

    /**
     * boss在APP端直播中对弹幕进行操作
     *
     * @param p  直播id
     * @param p2 身份 1-主播 2-管理员
     * @param p3 弹幕id
     * @param p4 to_id。弹幕发送者id
     * @param p5 弹幕发送者身份：0-geek 1-boss
     * @param p6 按钮名称：置顶、禁言、邀请投递、回复、删除等，有什么传什么
     */
    public static void campusLiveManageCommentCk(String p, int p2, String p3, String p4, int p5, String p6) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_COMMENT_CK)
                .param("p", p)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .param("p5", p5)
                .param("p6", p6)
                .build();
    }

    /**
     * 获取协助同事id集合
     */
    private static String getEncryptAdminUserIds(RecordDetailForModifyResponse bean) {
        if (bean == null) return "";
        List<String> adminIds = new ArrayList<>();
        if (!LList.isEmpty(bean.adminUsers)) {
            for (AnchorUserBean user : bean.adminUsers) {
                if (user == null) continue;
                adminIds.add(user.encryptUserId);
            }
        }
        return StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, adminIds);
    }

    /**
     * 获取得到埋点 ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_NEXT 的p5参数的值
     * 1-自传封面，2-默认
     */
    private static String getP5ParamValue(RecordDetailForModifyResponse bean) {
        if (bean == null) return "";
        if (bean.preinstallPromotionalPictureIndex == 0) {// 从相册上传
            return "1";
        } else if (bean.preinstallPromotionalPictureIndex == -1) {/*非相册上传*/
            return "2";
        }
        return "";
    }

    /**
     * 将直播间的状态按规则转换成埋点所需的int类型
     * 0-未开始，1-正在直播，2-已结束/回放
     */
    private static String convertLiveState(@LiveState.State int liveState) {
        // 直播状态（0-未开始，1-正在直播，2-已结束/回放）
        String liveStateConvert = "尚未开始";
        if (liveState == LiveState.PAUSED || liveState == LiveState.PUBLISHING) {
            liveStateConvert = "直播中";
        } else if (liveState == LiveState.FINISH || liveState == LiveState.DELAY) {
            liveStateConvert = "已结束";
        }
        return liveStateConvert;
    }

    /**
     * boss在APP端点击话题库/弹幕热词下具体tab
     *
     * @param liveRecordId 直播id
     * @param mainTabName  主tab：弹幕热词，话题库
     * @param subTabName   点击的副tab名称
     */
    public static void dotCampusLiveManageTopicTab(String liveRecordId, String mainTabName, String subTabName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_TOPICTAB)
                .param("p", liveRecordId)
                .param("p3", mainTabName)
                .param("p4", subTabName)
                .debug()
                .build();
    }


    /**
     * APP B端对直播学院具体内容的点击
     *
     * @param id   内容id/直播id
     * @param type 类型：1-图文 2-视频 3-直播
     */
    public static void dotCampusCourseContentClick(String id, String type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_COURSE_CONTENTCLICK)
                .param("p", id)/*内容id/直播id*/
                .param("p2", type)/*类型：1-图文 2-视频 3-直播*/
                .debug()
                .build();
    }

    /**
     * Boss在app创建直播间过程中点击职位
     */
    public static void dotCampusAppointmentjobClick(String jobId, int clickScene, String toastContent, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_JOBCK)
                .param("p", jobId)/*职位id*/
                .param("p2", String.valueOf(clickScene))/*1：勾选/-1:取消勾选/0:置灰不可选*/
                .param("p3", toastContent)/*P2=0时，点击灰色职位，返回点击错误的原因toast的提示内容。*/
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .debug()
                .build();
    }

    /**
     * boss在app创建直播过程中弹出增加职位提示
     *
     * @param popType 弹框类型：1-牛人数量缺少/2-同类职位近期直播
     */
    public static void dotCampusAppointmentMoreJobPop(int popType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_MOREJOBPOP)
                .param("p", String.valueOf(popType))
                .debug()
                .build();
    }

    /**
     * boss在app创建直播过程中对弹出更多职位提示窗进行点击
     *
     * @param popType         弹框类型：1-牛人数量缺少/2-同类职位近期直播
     * @param clickButtonText 按钮字符串：910版本为"选择更多职位/不选了，继续"
     */
    public static void dotCampusAppointmentMoreJobPopClick(int popType, String clickButtonText) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_MOREJOBCK)
                .param("p", String.valueOf(popType))
                .param("p2", clickButtonText)
                .debug()
                .build();
    }

    /**
     * C在APP直播间内点击弹幕中@职位
     *
     * @param liveRecordId 直播id
     * @param jobId        职位id
     */
    public static void livePageBAtCk(String liveRecordId, String jobId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_ATCK)
                .param("p", liveRecordId)
                .param("p2", jobId)
                .build();
    }

    /**
     * C在APP直播间点击心动榜具体任务
     *
     * @param liveRecordId   直播id
     * @param operationTitle 操作标题文案
     */
    public static void dotCampusLiveHeartClick(String liveRecordId, String operationTitle) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_HEART_CLICK)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", operationTitle)/*操作标题文案*/
                .debug()
                .build();
    }

    /**
     * C在APP直播中点击切换心动榜tab
     *
     * @param liveRecordId 直播id
     * @param tabName      tab名称
     */
    public static void dotCampusLiveHeartRankCk(String liveRecordId, String tabName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_HEART_RANKCK)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", tabName)/*tab名称，仅在点击tab时埋，弹层弹出时不报*/
                .debug()
                .build();
    }

    /**
     * B端APP我的直播页面中，直播文章曝光
     *
     * @param liveRecordId 直播id
     * @param p2           加密文章id组，英文逗号隔开
     * @param p3           直播状态 0-预约 1-结束
     */
    public static void dotCampusLiveCourseLiveArticle(String liveRecordId, String p2, int p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_COURSE_LIVEARTICLE)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", p2)
                .param("p3", p3)
                .debug()
                .build();
    }

    /**
     * B端APP端我的直播界面中点击引导文章
     *
     * @param liveRecordId 直播id
     * @param p2           加密文章id
     * @param p3           直播状态 0-预约 1-结束
     */
    public static void dotCampusLiveCourseArticleCk(String liveRecordId, String p2, int p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_COURSE_ARTICLECK)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p3", p3)
                .debug()
                .build();
    }

    /**
     * C在APP直播间内点赞
     *
     * @param liveRecordId 直播id
     * @param type         点赞方式对应编号，1:右下角点赞图标，2:双击屏幕点赞
     */
    public static void dotCampusLivePageBThumbUp(String liveRecordId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_THUMBUP)
                .param("p", liveRecordId)
                .param("p2", type)
                .debug()
                .build();
    }

    /**
     * C端APP直播间快捷弹幕弹出
     *
     * @param liveRecordId 直播id
     * @param securityId   职位securityId
     * @param p3           卡片类型：1-讲解职位 2-与你匹配 3-热招职位卡片 4-置顶职位（高级直播间）5-刚刚看过 6-你关注过 7-推荐职位
     */
    public static void dotCampusLivePopJobCard(String liveRecordId, String securityId, int p3) {
        AnalyticsFactory param = AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_JOBCARD)
                .param("p", liveRecordId)
                .param("p2", securityId)
                .param("p3", p3);
        param.debug().build();
    }

    /**
     * C在APP进入直播第一次拉流成功30s后上报卡顿次数，直播中和回放打点
     *
     * @param liveRecordId 直播id
     * @param p2           直播状态 1-正式 2-回放
     * @param p3           卡顿次数
     */
    public static void dotCampusLiveEntryStall(String liveRecordId, int p2, int p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_ENTRY_STALL)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p3", p3)
                .debug()
                .build();
    }


    /**
     * 统计C从进入直播间到看到首帧画面的时间，待开播时进入不埋
     *
     * @param liveRecordId 直播id
     * @param p2           发起播放请求的时间戳 Long类型
     * @param p4           V组完成首帧渲染的时间戳 Long类型
     * @param p5           直播状态 1-直播 2-回放
     */
    public static void dotCampusLivePlayLoad(String liveRecordId, Long p2, Long p4, int p5) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_PLAY_LOAD)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p4", p4)
                .param("p5", p5)
                .debug()
                .build();
    }

    /**
     * C在APP观看直播3s及10s上报 时机：拉流成功停留3s及10s
     *
     * @param liveRecordId 直播id
     * @param p2           直播状态 1-正式 2-回放
     * @param p3           秒数：3或10
     */
    public static void dotCampusLiveEntry3S(String liveRecordId, int p2, int p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_ENTRY_3S)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p3", p3)
                .debug()
                .build();
    }

    /**
     * B/C 在APP直播间内职位列表弹层弹出 912高级直播间设置城市分类tab
     *
     * @param liveRecordId 直播id
     * @param liveState    直播状态
     * @param from         来源
     * @param tabName
     * @param cityCode
     */
    public static void dotCampusLivePopJobList(String liveRecordId, int liveState, String from, String tabName, String cityCode,
                                               String streamType, String jobGroupId, String encryptBrandId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_JOBLIST)
                .param("p", liveRecordId)
                .param("p2", convertLiveStateToDotParamValue(liveState))
                .param("p3", from)/*大蓝普通类型直播间中， p3=city*/
                .param("p4", tabName)/*如为高级直播间取展示tab名称：职位分类/城市分类*/
                .param("p5", cityCode)/*如高级直播间，点击城市弹出传该城市cityCode，否则不传*/
                .param("p6", streamType)/*913增加视频流类型：1-实时流 2-回放流（预告及回放不传）*/
                .param("p7", jobGroupId)/*	encrypt job group id*/
                .param("p8", encryptBrandId)/*encrypt brand id*/
                .debug()
                .build();
    }

    /**
     * C在直播间内点击关注直播品牌
     * 包括点击上方品牌旁关注按钮以及弹幕我也关注的点击 再次点击品牌旁关注变为取消关注也上报
     *
     * @param p2 encrypt直播品牌id
     * @param p3 点击位置：0-直播间 1-品牌页 2-弹幕
     * @param p4 关注状态：1-关注/0-取消
     */
    public static void dotCampusLiveBrandFollow(String liveRecordId, String p2, int p3, int p4) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_FOLLOW)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .build();
    }

    /**
     * C端APP高级直播品牌页TAB点击事件
     *
     * @param p2 tab实际字符串
     */
    public static void dotCampusLiveBrandPageTabCk(String brandId, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_BRANDPAGE_TABCK)
                .param("p", brandId)
                .param("p2", p2)
                .build();
    }

    /**
     * C在APP观看直播中点击【看讲解】按钮
     *
     * @param securityId
     * @param groupId    普通直播间不传，仅高级直播间
     */
    public static void dotCampusLiveExplainVm(String liveRecordId, String securityId, String groupId, int liveStateScene, String encryptJobId) {
        HashMap<String, String> param = new HashMap<>();
        param.put("p", liveRecordId);
        param.put("p2", securityId);
        param.put("p3", groupId);
        param.put("p4", String.valueOf(liveStateScene));/*直播状态 1-直播中 2-回放*/
        if (!TextUtils.isEmpty(encryptJobId)) {
            param.put("p5", encryptJobId);
        }
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_EXPLAIN_VM)
                .param(param)
                .debug()
                .build();
    }

    /**
     * C在APP直播中从观看讲解回放切回到直播中，包括主动切回以及播放完成切回，1005.822 新增观看节目单回放后，切回直播
     *
     * @param securityId 1005更新：若p4=2，传章节ID
     * @param groupId    普通直播间不传或传空，仅高级直播间
     * @param type       回放类型：1-职位讲解 2-节目单
     */
    public static void dotCampusLiveVideoLiveBack(String liveRecordId, String securityId, String groupId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_VIDEO_LIVEBACK)
                .param("p", liveRecordId)
                .param("p2", securityId)
                .param("p3", groupId)
                .param("p4", type)
                .build();
    }

    /**
     * C在APP观看回放直播中点击倍速
     *
     * @param speed 切换到的速度，例：从正常切换至1.5，传1.5
     * @param time  切换时刻的进度条时间 00:00:00
     */
    public static void dotCampusLiveVideoSpeed(String liveRecordId, String speed, String time) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_VIDEO_SPEED)
                .param("p", liveRecordId)
                .param("p2", speed)
                .param("p3", time)
                .build();
    }

    /**
     * C在APP观看回放时拖动进度条
     *
     * @param startTime  起始进度条时间 00:00:00
     * @param targetTime 落地进度条时间 00:00:00
     */
    public static void dotCampusLiveVideoProgress(String liveRecordId, String startTime, String targetTime) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_VIDEO_PROGRESS)
                .param("p", liveRecordId)
                .param("p2", startTime)
                .param("p3", targetTime)
                .build();
    }

    /**
     * C在APP观看回放直播时点击暂停/重新播放，进入直播间自动播放不上报
     *
     * @param action 动作：0-暂停 1-重新播放
     * @param time   进度条时间 00:00:00
     */
    public static void dotCampusLiveVideoPauseRePlay(String liveRecordId, int action, String time) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_VIDEO_PAUSE_REPLAY)
                .param("p", liveRecordId)
                .param("p2", action)
                .param("p3", time)
                .build();
    }

    /**
     * 牛人在直播间内点击组件
     */
    public static void dotCampusLivePageBModule(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_MODULE)
                .param("p", liveRecordId)
                .build();
    }

    /**
     * 询问在直播时，是否打开自动打点开关
     *
     * @param p2 是否是开播前首次询问（首次置顶职位（或品牌分类）时（或首次点击开关时））：1 是， 0 否 直播中的状态变更；
     * @param p3 操作人员身份 ：1 管理员端， 2 主播端 ；
     * @param p4 标记的状态 ；状态 ：0 不开启， 1 开启 ；
     */
    public static void dotCampusLivePopLiveTag(String liveRecordId, int p2, int p3, int p4) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_POP_LIVETAG)
                .param("p", liveRecordId)
                .param("p2", p2)
                .param("p3", p3)
                .param("p4", p4)
                .build();
    }

    /**
     * 把直播状态转换为埋点 所需要的参数值
     *
     * @param liveState 直播状态 0-预告 1-正式 2-回放4-暂停
     */
    private static String convertLiveStateToDotParamValue(int liveState) {
        String resultValue = "";
        switch (liveState) {
            case LiveState.PUBLISHING:
                resultValue = "1";
                break;
            case LiveState.PAUSED:
                resultValue = "4";
                break;
            case LiveState.WAIT:
                resultValue = "0";
                break;
            case LiveState.FINISH:
            case LiveState.DELAY:
                resultValue = "2";
                break;
        }
        return resultValue;
    }


    /**
     * 埋点：C在APP直播间对职位列表进行筛选
     *
     * @param liveRecordId
     */
    public static void dotCampusLiveJobFilter(String liveRecordId, int fitlerType, String filterLabelName, int filterOrCancel, int liveStatus) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_JOBFILTER)
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", String.valueOf(fitlerType))/*筛选字段类型：1-职类/2-城市*/
                .param("p3", filterLabelName)/*筛选字段字符串，例：产品*/
                .param("p4", String.valueOf(filterOrCancel))/*1-筛选 0-取消筛选*/
                .param("p5", String.valueOf(liveStatus))/*直播间状态：0-未开始；1-直播中；2-已结束*/
                .debug()
                .build();
    }

    /**
     * 埋点： B在APP创建直播流程中职位页面，接口返回是否允许创建直播
     */
    public static void dotCampusLiveApponitmentStartAllow(String encryptJobIds, boolean isAllowStart, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_STARTALLOW)
                .param("p", encryptJobIds)/*encrypt职位id组，英文逗号隔开*/
                .param("p2", isAllowStart ? "1" : "0")/*是否允许开播：1-是，0-否*/
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .debug()
                .build();
    }

    /**
     * 埋点： boss直播开始界面功能按键点击
     */
    public static void dotCampusStartFunctionClick(String liveRecordId, String buttonText, boolean hasRecoverLive, int liveSource) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_STARTFUNCTIONCLICK)
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", buttonText)/*按键字符串*/
                .param("p3", hasRecoverLive ? 1 : 0)/*是否误关闭恢复直播：1是，0否*/
                .param("p4", liveSource)/*开播方式：0-预约直播，1-即时开播*/
                .debug()
                .build();
    }

    /**
     * 埋点： B身份点击分享
     */
    public static void dotShareClick(String liveRecordId) {
        AnalyticsFactory.create().action("live-boss-share-click")
                .param("p", liveRecordId)/*encrypt直播id*/
                .debug()
                .build();
    }

    /**
     * 埋点： B身份点击分享弹层内部元素
     */
    public static void dotShareLayerClick(String liveRecordId, int type) {
        AnalyticsFactory.create().action("live-boss-sharelayer-click")
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", type)/*分享渠道：0-微信好友，1-朋友圈，2-海报*/
                .debug()
                .build();
    }

    /**
     * 埋点： boss直播开始界面功能按键点击
     */
    public static void dotCampusStartFunctionClick(String liveRecordId, String buttonText, boolean hasRecoverLive, int liveSource, List<JobsBean> selectedJobs) {
        // 当 p2 为「开始直播」或「选好了，去开播」时，处理职位 ID 列表
        String jobIds = "";
        if (LList.isNotEmpty(selectedJobs)) {
            jobIds = StringUtil.connectTextWithCharNew(",", selectedJobs, jobsBean -> jobsBean.jobId);
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_STARTFUNCTIONCLICK)
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", buttonText)/*按键字符串*/
                .param("p3", hasRecoverLive ? 1 : 0)/*是否误关闭恢复直播：1是，0否*/
                .param("p4", liveSource)/*开播方式：0-预约直播，1-即时开播*/
                .param("p5", jobIds)/*若p2为「开始直播」或「选好了，去开播」，记录不加密的职位id组，按照接口顺序，逗号隔开*/
                .debug()
                .build();
    }

    /**
     * 1017.815 app B在直播前/直播中美颜弹层里，点击按钮
     */
    public static void dotCampusBeautyClick(String liveRecordId, String buttonText, int state, int level) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_BEAUTYCK)
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", buttonText)/*点击的按钮文案：关闭/重置/美白/磨皮/大眼/瘦脸/V脸/窄脸/瘦颧骨/眼袋/去法令纹/鼻翼*/
                .param("p3", state)/*直播间状态：0-待开播，1-直播中*/
                .param("p4", level)/*此时该美颜功能调整条的数值，若p2=关闭/重置，固定传0*/
                .debug()
                .build();
    }

    /**
     * 埋点： C端APP端点击品牌&弹幕品牌跳转到品牌信息页面
     *
     * @param liveRecordId
     * @param brandId
     */
    public static void dotCampusClickBrand(String liveRecordId, String brandId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_CLICKBRAND)
                .param("p", liveRecordId)/*encrypt直播id*/
                .param("p2", brandId)/*encrypt品牌id*/
                .debug()
                .build();
    }

    /**
     * 点击流量包，试验一方案2，流量包选择
     *
     * @param p  选择的流量包数量
     * @param p2 是否是推荐的流量包 ： 1， 是 0 否
     */
    public static void dotCampusLiveAppointmentExpbClick(String p, int p2, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_EXPBCHFLOW)
                .param("p", p)
                .param("p2", p2)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .build();
    }

    /**
     * 点击查看详情， 试验一 ，方案2
     */
    public static void dotCampusLiveAppointmentExpbCkCd() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_APPOINTMENT_EXPBCKCD)
                .build();
    }

    /**
     * APP直播间内用户选择分享渠道
     */
    public static void dotCampusLiveShareChannel(String encryptId, String channel) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_LIVEPAGEB_SHARECHANNEL)
                .param("p", encryptId)/*encrypt直播id*/
                .param("p2", channel)/*分享渠道：1-微信好友 2-朋友圈 3-朋友圈海报*/
                .debug()
                .build();
    }

    /**
     * APP直播间内用户选择分享渠道
     */
    public static void dotCampusLiveManageStartShare(String encryptId, String channel, String state, String admin) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_MANAGE_START_SHARE)
                .param("p", encryptId)
                .param("p2", channel)
                .param("p3", state)
                .param("p4", admin)
                .build();
    }

    /**
     * 牛人或雇主在APP高级直播品牌页点击分享
     */
    public static void dotCampusLiveBrandPageShare(String encryptId, String brandId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_BRANDPAGE_SHARE)
                .param("p", encryptId)
                .param("p2", brandId)
                .build();
    }

    /**
     * 牛人或雇主在APP高级品牌直播页选择分享渠道
     */
    public static void dotCampusLiveBrandPageShareChannel(String encryptId, String brandId, String channel) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_BRANDPAGE_SHARE_CHANNEL)
                .param("p", encryptId)
                .param("p2", brandId)
                .param("p3", channel)
                .build();
    }

    /**
     * 取boss选择职位组和点击日期后的推荐时间点
     */
    public static void dotCampusLiveCreateLiveIdGroupDate(String jobIds, String date, String allTimeGroup, String recommendTimeGroup, String minPrice) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATELIVE_IDGROUPDATE)
                .param("p", jobIds)/*选择的职位id组，id英文逗号隔开*/
                .param("p2", date)/*选择的日期，格式××××-××-××*/
                .param("p3", allTimeGroup)/*全部的时间组，英文逗号隔开，××:××*/
                .param("p4", recommendTimeGroup)/*推荐的时间组，英文逗号隔开，××:××*/
                .param("p5", minPrice)/*该天最低价格，传显示的数字，不加单位符号，不显示价格则传空*/
                .debug()
                .build();
    }

    /**
     * 创建直播直播间选择时间段，点击保存后埋点
     */
    public static void dotCampusLiveCreateLiveDateTime(String selectedDate, String selectedTime, boolean isRecommendTime, String minPrice, @Nullable BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATELIVE_DATETIME)
                .param("p", selectedDate)/*日期，格式：××××-××-××*/
                .param("p2", selectedTime)/*日期，格式：××××-××-××*/
                .param("p3", isRecommendTime ? "1" : "0")/*是否推荐时间点：1是，0否*/
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .param("p22", GlobalStatus.isHaveAvailableTicket() ? 1 : 0)/*判断该用户是否有可用的免费券（未失效 未使用），有返回1，没有返回0*/
                .debug()
                .build();
    }

    /**
     * 选择直播场次页面弹窗
     *
     * @param ticketIds 可使用的权益id组，英文逗号隔开
     */
    public static void dotCampusLivePopChrai(String ticketIds) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_CHRAI)
                .param("p", ticketIds)
                .debug()
                .build();
    }

    /**
     * 用户创建直播间有过弹窗提示
     */
    public static void dotCampusCreateLivePrompt() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATE_LIVE_PROMPT)
                .build();
    }

    /**
     * 用户选择直播间弹窗提示后选择的按钮
     *
     * @param action 按钮名称：选择其他职位、选择其他日期
     */
    public static void dotCampusCreateLiveSelectionPrompt(String action) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATE_LIVE_SELECTION_PROMPT)
                .param("p", action)
                .build();
    }

    /**
     * 中奖用户在直播间中奖名单页面吸底位置处 点击填写地址
     *
     * @param order live_draw_order 第几轮奖品
     * @param name  lucky_draw_name 奖品内容
     */
    public static void dotCampusLivePagebAddressFill(String liveRecordId, int order, String name) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_PAGEB_ADDRESS_FILL)
                .param("p", liveRecordId)
                .param("p2", order)
                .param("p3", name)
                .build();
    }

    /**
     * C在APP直播间内更多tab，选中tab就埋「问答」「公告」「中奖名单」「回复我」
     *
     * @param tabName tab名称：1-问答 2-公告 3-中奖名单 4-回复我  5-课程纪要 6-投票 7-直播详情
     */
    public static void dotCampusLiveBoardQaClick(String liveRecordId, int tabName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BOARDQA_CLICK)
                .param("p", liveRecordId)
                .param("p2", tabName)
                .debug()
                .build();
    }

    /**
     * 直播横幅曝光
     */
    public static void dotCampusLiveListTopBannerExpose(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEC_TOPBANNEREXPOSE)
                .param("p", liveRecordId)/*直播id；曝光多个直播ID时，存长字符串，用分号区隔*/
                .debug()
                .build();
    }

    /**
     * 直播横幅点击
     */
    public static void dotCampusLiveListTopBannerClick(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEC_TOPBANNERCLICK)
                .param("p", liveRecordId)/*直播id*/
                .debug()
                .build();
    }

    /**
     * 此弹框场景为：不满足抽奖要求的提示弹框点击
     *
     * @param joinConditionType 1发弹幕，2投简历，3关注并抽奖
     */
    public static void dotCampusLiveLotteryClick(String liveRecordId, int joinConditionType) {
        int reason = 0;
        if (joinConditionType == 1) {
            reason = 2;
        } else if (joinConditionType == 2) {
            reason = 1;
        } else if (joinConditionType == 3) {
            reason = 3;
        } else {
            return;
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LOTTERY_CLICK)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", reason)/*弹出原因1-未投简历2-未发弹幕*/
                .debug()
                .build();
    }

    /**
     * 此弹框场景为：不满足抽奖要求的提示弹框曝光
     *
     * @param joinConditionType 1发弹幕，2投简历，3关注并抽奖
     */
    public static void dotCampusLiveLotteryExpose(String liveRecordId, int joinConditionType) {
        int reason = 0;
        if (joinConditionType == 1) {
            reason = 2;
        } else if (joinConditionType == 2) {
            reason = 1;
        } else if (joinConditionType == 3) {
            reason = 3;
        } else {
            return;
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LOTTERY_EXPOSE)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", reason)/*弹出原因1-未投简历2-未发弹幕*/
                .debug()
                .build();
    }

    /**
     * 牛人在代招直播间中奖名单页面点击查看图片可查看大图
     */
    public static void dotCampusLivePageViewPic(String liveRecordId, int count, String gifName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_PAGETAB_VIEWPIC)
                .param("p", liveRecordId)/*直播id*/
                .param("p2", count)/*第几轮*/
                .param("p3", gifName)/*礼物名称*/
                .debug()
                .build();
    }

    /**
     * 心动值功能下，心动榜内在排行榜底部依据当前排名，吸底展示对应文案
     */
    public static void dotCampusLiveHeartBottomClick(String liveRecordId, String text) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_BOTTOM_CLICK)
                .param("p", liveRecordId)
                .param("p3", text)
                .build();
    }

    /**
     * 心动值功能下，心动榜内在排行榜底部依据当前排名，吸底展示对应文案
     */
    public static void dotCampusLiveHeartBottomExpose(String liveRecordId, String text, int isShow) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_BOTTOM_EXPOSE)
                .param("p", liveRecordId)
                .param("p3", text)
                .param("p4", isShow)
                .build();
    }

    /**
     * B在APP个人直播间长页面创建流程过程中，点击「更多设置（可跳过）」
     */
    public static void dotCampusLiveAppointmentUnFoldOption(BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_UNFOLDOPTION)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .build();
    }

    /**
     * 品牌页，新增悬浮广告位（仅app）广告位在配置直播品牌时进行配置 C、B观众身份需分别配置，广告位点击
     *
     * @param encryptBrandId encrypt品牌id
     * @param url            广告位跳转链接url
     */
    public static void dotCampusLiveBrandPageAdClick(String encryptBrandId, String url) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BRANDPAGE_ADCLICK)
                .param("p", encryptBrandId)
                .param("p2", url)
                .build();
    }

    /**
     * 品牌页，新增悬浮广告位（仅app）广告位在配置直播品牌时进行配置 C、B观众身份需分别配置，广告位曝光
     *
     * @param encryptBrandId encrypt品牌id
     * @param url            广告位跳转链接url
     */
    public static void dotCampusLiveBrandPageAdExpose(String encryptBrandId, String url) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BRANDPAGE_ADEXPOSE)
                .param("p", encryptBrandId)
                .param("p2", url)
                .build();
    }

    /**
     * 用户在进入直播间后，职位列表会以默认弹出，或者用户点击相关按钮后弹出，希望记录用户关闭职位列表时间(用户在职位列表中进行，职位详情查看，投递什么的都算作职位列表内停留)
     *
     * @param source joblist的来源，区分为default和other就行
     */
    public static void dotCampusLivePopJobListClose(String recordId, String source) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_JOBLISTCLOSE)
                .param("p", recordId)
                .param("p2", source)
                .build();
    }

    /**
     * 高级直播间 职位列表 在"职位"-全部职位-"全部"的前提下 滑屏职位曝光
     *
     * @param jobIds 曝光的job_id列表 英文逗号隔开
     */
    public static void dotCampusLivePageBAdvLiveJobList(String recordId, String jobIds) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVEPAGEB_ADVLIVEJOBLIST)
                .param("p", recordId)
                .param("p2", jobIds)
                .build();
    }

    /**
     * 牛人进入直播间1min后未关注，弹出关注提示弹窗
     *
     * @param dialogTitle 弹窗对应文案
     */
    public static void dotCampusLivePopFollowPop(String recordId, String dialogTitle) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_FOLLOWPOP)
                .param("p", recordId)
                .param("p2", dialogTitle)
                .build();
    }


    /**
     * B端APP端职位选择页面弹出
     */
    public static void dotCampusLiveAppointmentJobPagePop(int source, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_JOBPAGEPOP)
                .param("p", source)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .build();
    }

    /**
     * 课程直播间禁言模式下，点击弹幕输入框弹出禁言toast（B C端都上报）
     */
    public static void dotCampusLivePageWordprohibited(String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVEPAGEB_WORDPROHIBITED)
                .param("p", recordId)
                .build();
    }

    /**
     * APP端B端直播中主播端强提醒曝光
     *
     * @param content 提醒文案
     * @param level   优先级数字
     */
    public static void dotCampusLivePopStrongBNotice(String recordId, String content, int level) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_STRONGBNOTICE)
                .param("p", recordId)
                .param("p2", content)
                .param("p3", level)
                .build();
    }

    /**
     * APP端B端直播中主播端弱提醒曝光，其中倒计时提醒只上报首次弹出
     *
     * @param content 提醒文案
     */
    public static void dotCampusLivePopWeakBNotice(String recordId, String content) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_WEAKBNOTICE)
                .param("p", recordId)
                .param("p2", content)
                .build();
    }

    /**
     * 点击直播封面 换一张 按钮
     */
    public static void dotCampusLiveCreateLiveReplaceCover() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATELIVE_REPLACECOVER)
                .build();
    }

    /**
     * 直播结束后本场投递牛人列表点击中-"查看更多"按钮点击
     */
    public static void dotApplyGeekListViewMore(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVE_END_APPLY_GEEK_LIST_VIEW_MORE)
                .param("p", liveRecordId)
                .build();
    }

    /**
     * 直播结束后本场投递牛人列表曝光
     *
     * @param geekIdList   投递牛人加密geek_id list
     * @param btnTitleList 每个牛人卡片内按钮（"继续沟通"都有所以只埋"查看附件简历"，有"查看附件简历"时埋1）list，与geek_id list对应
     * @param selector     职位筛选，(当筛选为某职位下时传某职位加密的job_id，无选择的时候传空字符串)
     * @param identity     身份：1-主播，2-管理员，3-本场其他boss
     * @param tabName      tab名称
     */
    public static void dotApplyGeekListExpose(String liveRecordId, String geekIdList, String btnTitleList, String selector, int identity, String tabName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVE_END_APPLY_GEEK_LIST_EXPOSE)
                .param("p", liveRecordId)
                .param("p2", geekIdList)
                .param("p3", btnTitleList)
                .param("p4", selector)
                .param("p5", identity)//身份：1-主播，2-管理员，3-本场其他boss
                .build();
    }

    /**
     * 品牌直播间，校招信息「保存」
     *
     * @param liveRecordId   直播id，如无为空
     * @param jsonSchoolInfo 传输给接口的json串，包括保存的「招聘对象」、「招聘流程」、「起止时间」等信息
     */
    public static void dotSaveAppointmentSchoolInfo(String liveRecordId, String jsonSchoolInfo, BossTicketListBean ticketListBean) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_SCHOOL_INFO)
                .param("p", liveRecordId)
                .param("p2", jsonSchoolInfo)
                .param("p11", ticketListBean != null && !TextUtils.isEmpty(ticketListBean.ticketId) ? 1 : 0)/*是否消耗权益：1-是 0-否*/
                .param("p12", ticketListBean != null ? ticketListBean.type : 0)/*p11=1 时存在，消耗权益类型 ： 1 免费，2 付费 ；*/
                .param("p13", ticketListBean != null ? String.valueOf(ticketListBean.liveScenario) : "")/*权益直播类型：1-校招 2-社招 0-不限*/
                .param("p14", ticketListBean != null ? ticketListBean.ticketId : "")/*如消耗权益则传权益id，否则传空*/
                .param("p18", GlobalStatus.bzpType)// 支付类型 1线上(个人) 2线下(品牌)
                .build();
    }


    /**
     * 品牌直播间，校招信息「保存」
     *
     * @param liveRecordId 直播id，如无为空
     * @param ids          预约的加密直播id list，英文逗号、无空格
     */
    public static void dotLiveListSubscribeAll(String liveRecordId, String ids) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVE_LIST_SUBSCRIBE_ALL)
                .param("p", liveRecordId)
                .param("p2", ids)
                .debug()
                .build();
    }

    /**
     * 品牌直播间，校招信息「保存」
     *
     * @param liveRecordId 直播id，如无为空
     * @param url          广告url
     */
    public static void dotLiveListPageBAdvertiseClick(String liveRecordId, String url, String type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIST_PAGEB_ADVERTISE_CLICK)
                .param("p", liveRecordId)
                .param("p2", url)
                .param("p3", type)
                .debug()
                .build();
    }

    /**
     * 品牌直播间，校招信息「保存」
     *
     * @param liveRecordId        直播id，如无为空
     * @param state               状态
     * @param currentLiveRecordId 当前点击的直播间id
     */
    public static void dotLivePopLiveListClick(String liveRecordId, String state, String currentLiveRecordId, String curState) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_LIVE_LIST_CLICK)
                .param("p", liveRecordId)
                .param("p2", state)
                .param("p3", currentLiveRecordId)
                .param("p4", curState)
                .debug()
                .build();
    }

    /**
     * 品牌直播间，校招信息「保存」
     *
     * @param liveRecordId 直播id，如无为空
     * @param url          广告url
     */
    public static void dotLiveListPageBAdvertise(String liveRecordId, String url, String type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIST_PAGEB_ADVERTISE)
                .param("p", liveRecordId)
                .param("p2", url)
                .param("p3", type)
                .debug()
                .build();
    }

    /**
     * 专栏直播间弹出主播列表--专栏页点击
     *
     * @param liveRecordId 加密直播id
     * @param columnId     专栏id
     * @param state        当前直播状态 0-待开播；1-直播中；2-已结束
     * @param type         跳转类型 1-H5专栏；2-原生品牌主页
     */
    public static void dotLiveListZLClick(String liveRecordId, String columnId, String state, String type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_LIVE_LIST_ZL_CLICK)
                .param("p", liveRecordId)
                .param("p2", columnId)
                .param("p3", state)
                .param("p4", type)
                .debug()
                .build();
    }

    /**
     * 专栏直播间弹出主播列表--专栏页点击
     *
     * @param liveRecordId    加密直播id
     * @param curLiveRecordId 当前点击直播间
     */
    public static void dotLiveListPageBClickEx(String liveRecordId, String curLiveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIST_PAGEB_CLICK_EX)
                .param("p", liveRecordId)
                .param("p2", curLiveRecordId)
                .debug()
                .build();
    }

    /**
     * 专栏直播间弹出主播列表--专栏页曝光
     *
     * @param liveRecordId    加密直播id
     * @param curLiveRecordId 当前点击直播间
     */
    public static void dotLiveListPageBExpose(String liveRecordId, String curLiveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIST_PAGEB_EXPOSE)
                .param("p", liveRecordId)
                .param("p2", curLiveRecordId)
                .debug()
                .build();
    }

    /**
     * 专栏直播间弹出主播列表--专栏页点击
     *
     * @param liveRecordId 加密直播id
     */
    public static void dotLiveListExpose(String liveRecordId, String columnId, String state, String curLiveRecordId, int show) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVE_LIST_EXPOSE)
                .param("p", liveRecordId)
                .param("p2", columnId)
                .param("p3", state)
                .param("p4", curLiveRecordId)
                .param("p5", show)
                .debug()
                .build();
    }

    /**
     * 学生端直播列表「直播专栏」曝光
     *
     * @param columnTitles 专栏标题。分号区隔的字符串
     */
    public static void dotLiveListColumnExpose(String columnTitles) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEC_COLUMN_EXPOSE)
                .param("p", columnTitles)/*专栏标题。分号区隔的字符串*/
                .debug()
                .build();
    }

    /**
     * 学生端直播列表直播专栏，点击「查看全部」
     */
    public static void dotLiveColumnCheckMoreClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEC_COLUMN_DETAILCLICK)
                .debug()
                .build();
    }

    /**
     * 学生端直播列表or直播专栏详细页，点击卡片/"进入专栏"按钮
     */
    public static void dotLiveColumnEntryClick(int type, String columnTitle) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEC_COLUMN_ENTRYCLICK)
                .param("p", String.valueOf(type))/*1-主列表页点击 2-专栏详细页面点击*/
                .param("p2", columnTitle)/*专栏文字标题*/
                .debug()
                .build();
    }


    /**
     * 直播录制的讲解视频展示
     *
     * @param questionIds 逗号形式连接
     */
    public static void dotLiveExplainVideoShow(String questionIds) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_EXPLAINVIDEO_SHOW)
                .param("p", questionIds)
                .build();
    }

    /**
     * 直播间b端页面求讲解页面点击开始讲解
     *
     * @param title      识别的问题/职位名称
     * @param questionId question_id
     * @param from       1：弹窗点击 2：讲解请求页面点击
     */
    public static void dotLiveExplainStartClick(String title, String questionId, int from) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_EXPLAIN_START_CLICK)
                .param("p", title)
                .param("p2", questionId)
                .param("p3", from)
                .build();
    }

    /**
     * 直播间b端页面求讲解页面展示
     *
     * @param title      识别的问题/职位名称
     * @param questionId question_id
     */
    public static void dotLiveExplainToastShow(String title, String questionId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_EXPLAIN_TOAST_SHOW)
                .param("p", title)
                .param("p2", questionId)
                .build();
    }

    /**
     * 直播录制的讲解视频播放（bc观看均打点）
     *
     * @param title      识别的问题/职位名称
     * @param questionId question_id
     */
    public static void dotLiveExplainVideoPlay(String title, String questionId, String jobId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_EXPLAINVIDEO_PLAY)
                .param("p", title)
                .param("p2", questionId)
                .param("p3", jobId)
                .debug()
                .build();
    }

    /**
     * 直播录制的讲解视频点击删除
     *
     * @param questionId question_id
     */
    public static void dotLiveExplainVideoDelete(String questionId, String jobId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_EXPLAINVIDEO_DELETED)
                .param("p", questionId)
                .param("p2", jobId)
                .debug()
                .build();
    }

    /**
     * B在app直播间点击下方「直播互动榜」
     */
    public static void dotLiveManageInteractClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_MANAGE_INTERACT_CLICK)
                .build();
    }

    /**
     * B在直播间直播互动榜的浮层里，点击tab
     *
     * @param tabName tab的文字内容
     */
    public static void dotLiveManageInteractTabClick(String tabName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_MANAGE_INTERACT_TABCLICK)
                .param("p", tabName)
                .build();
    }

    /**
     * 点击小窗关闭按钮
     *
     * @param recordId
     */
    public static void dotLiveSmallWindowQuit(String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_WINDOWQUIT_GEEK)
                .param("p", recordId)
                .debug()
                .build();
    }

    /**
     * 直播间点击职位列表悬浮层"立刻沟通"按钮
     *
     * @param jobId 不加密
     */
    public static void dotLiveChatImmediate(long jobId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHAT_IMMEDIATE)
                .param("p", jobId)
                .build();
    }

    /**
     * 您一共中奖X轮 「查看全部」按钮的曝光
     */
    public static void dotLiveLotteryAllMessageExpo() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LOTTERY_ALL_MESSAGE_EXPO)
                .build();
    }

    /**
     * 您一共中奖X轮 「查看全部」按钮的点击
     */
    public static void dotLiveLotteryAllMessage() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LOTTERY_ALL_MESSAGE)
                .build();
    }

    /**
     * 「选择直播场次」入口点击后，在展开后的页面中，保存按钮的点击
     */
    public static void dotLiveChooseCouponSave(String ticketId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHOOSE_COUPON_SAVE)
                .param("p", ticketId)
                .build();
    }

    /**
     * 「选择直播场次」入口点击后，在展开后的页面中，场次的点击
     *
     * @param selected 点击后的状态 选中：1 取消选中：0
     */
    public static void dotLiveChooseCouponClick(String ticketId, int selected) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHOOSE_COUPON_CLICK)
                .param("p", ticketId)
                .param("p2", selected)
                .build();
    }

    /**
     * 「选择直播场次」入口的点击
     */
    public static void dotLiveChooseEntryClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHOOSE_ENTRY_CLICK)
                .param("p", GlobalStatus.bzpType)
                .build();
    }

    public static void pointChangeLive(String preRecordId, String recordId, int position, int curLogoPosition,
                                       SpecialTopicModel specialIcon, LiveSpecialTagListResponse specialTagListResponse,
                                       LiveSpecialTagListResponse.TagBean targetSpecialTagBean) {
        if (preRecordId == null || recordId == null || specialIcon == null || specialTagListResponse == null
                || LList.isEmpty(specialTagListResponse.tagList)) return;

        List<SpecialLiveIconBean> liveList = specialIcon.getLiveList();
        if (LList.getCount(liveList) <= 1) return;
        SpecialLiveIconBean currentSpecialTopicBean = null;
        SpecialLiveIconBean preSpecialTopicBean = null;
        for (SpecialLiveIconBean specialLiveIconBean : liveList) {
            if (LText.equal(preRecordId, specialLiveIconBean.liveRecordId)) {
                preSpecialTopicBean = specialLiveIconBean;
            }
            if (LText.equal(recordId, specialLiveIconBean.liveRecordId)) {
                currentSpecialTopicBean = specialLiveIconBean;
            }
        }

        if (currentSpecialTopicBean == null) return;
        if (preSpecialTopicBean == null) return;

        LiveSpecialTagListResponse.TagBean tagBean = targetSpecialTagBean != null ? targetSpecialTagBean
                : LList.getElement(specialTagListResponse.tagList, 0);
        if (tagBean == null) return;

        LiveAnalysisUtil.dotSlideLiveChange(currentSpecialTopicBean, preSpecialTopicBean, tagBean.aggregatedTagName, position > curLogoPosition ? 1 : 2);
    }

    /**
     * app专场直播间内，上下滑动切换直播间
     *
     * @param currentSpecialTopicBean 所在直播间
     * @param preSpecialTopicBean     去向直播间
     * @param tagName                 所属的聚合标签文案，字符串
     * @param direction               滑动方向：1-向上滑；2-向下滑
     */
    private static void dotSlideLiveChange(SpecialLiveIconBean currentSpecialTopicBean, SpecialLiveIconBean preSpecialTopicBean, String tagName, int direction) {
        if (currentSpecialTopicBean == null || preSpecialTopicBean == null) return;
        int currentLiveState = 0;
        if (currentSpecialTopicBean.isLiving()) {
            currentLiveState = 1;
        } else if (currentSpecialTopicBean.isFinished()) {
            currentLiveState = 2;
        }

        int preLiveState = 0;
        if (preSpecialTopicBean.isLiving()) {
            preLiveState = 1;
        } else if (preSpecialTopicBean.isFinished()) {
            preLiveState = 2;
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVEPAGE_B_APRCIALSILIDE)
                .param("p", currentSpecialTopicBean.liveRecordId)
                .param("p2", preSpecialTopicBean.liveRecordId)
                .param("p3", tagName)
                .param("p4", direction)
                .param("p5", currentLiveState)
                .param("p6", preLiveState)
                .debug()
                .build();
    }

    /**
     * app专场直播间中，右侧边栏内，更多直播间曝光
     *
     * @param recordId 所在直播间加密ID
     */
    public static void dotClickMoreLiveRoom(String recordId, String tags, String tagName, String livingStr, String endStr, String startStr) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVEPAGE_B_MORE_POP)
                .param("p", recordId)
                .param("p2", tags)
                .param("p3", tagName)
                .param("p4", livingStr)
                .param("p5", endStr)
                .param("p6", startStr)
                .debug()
                .build();
    }

    /**
     * app专场直播间中，顶部其他直播间icon曝光
     *
     * @param recordId 所在直播间加密ID
     */
    public static void dotSpecialTopIcon(String recordId, String liveStrs) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVEPAGE_B_SPECIAL_TOP_ICON)
                .param("p", recordId)
                .param("p2", liveStrs)
                .debug()
                .build();
    }

    /**
     * 进入直播间自动弹出「专栏详情」的曝光
     *
     * @param recordId 导播live_id
     */
    public static void dotGuideJobListExpo(String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_GUIDE_JOBLIST_EXPO)
                .param("p", recordId)
                .debug()
                .build();
    }

    /**
     * 搜索框前面的【搜职位】按钮的点击
     *
     * @param beforeClickTypeName
     */
    public static void dotJobSearchSwitchType(String beforeClickTypeName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SEARCH_SWITCH_TYPE)
                .param("p", beforeClickTypeName)/*点击前的标签，返回name    e.g.搜职位*/
                .debug()
                .build();
    }

    /**
     * 搜索框前面的【搜职位】按钮的切换情况
     *
     * @param beforeClickTypeName
     * @param afterClickTypeName
     */
    public static void dotJobSearchSwitchTypeChoose(String beforeClickTypeName, String afterClickTypeName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SEARCH_SWITCH_TYPE_CHOOSE)
                .param("p", beforeClickTypeName)/*点击前的标签，返回name    e.g.搜职位*/
                .param("p2", afterClickTypeName)/*点击的切换按钮文案，返回name  e.g. 搜职位/搜boss*/
                .debug()
                .build();
    }

    /**
     * 【本页全选】按钮的点击
     */
    public static void dotChooseJobAll(int beforeClickButtonState) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHOOSE_JOB_ALL)
                .param("p", String.valueOf(beforeClickButtonState))/*点击前「全选」按钮的状态：全选：1 未全选：0*/
                .debug()
                .build();
    }

    /**
     * 点击求讲解后的问题点击
     */
    public static void dotAskExplainQuestionClick(String question, String jobId, String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ASK_EXPLAIN_QUESTION_CLICK)
                .param("p", question)// 问题内容
                .param("p2", jobId)// 问题中包含的职位加密job_id
                .param("p3", recordId)// live_id
                .build();
    }

    /**
     * 1106.807 app 观众在直播间中，预设弹幕点击
     */
    public static void dotTopSetQuestionClick(String recordId, String question, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SETQUESTION_CK)
                .param("p", recordId)// 直播加密ID
                .param("p2", p2)// 预设弹幕类型：1-投递前，2-投递后 3-管理员 4-职位发布人
                .param("p3", question)// 点击的预设弹幕文案
                .build();
    }

    /**
     * 1106.807 app 观众在直播间中，预设弹幕曝光
     */
    public static void dotTopSetQuestionExposure(String recordId, boolean deliverAfter, List<GeekQuickQuestionQueryBean> presetBarrageList, String p2) {
        StringBuilder builder = new StringBuilder();
        for (GeekQuickQuestionQueryBean quickQuestionQueryBean : presetBarrageList) {
            builder.append(quickQuestionQueryBean.showContent).append("&&");
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SETQUESTION_POP)
                .param("p", recordId)// 直播加密ID
                .param("p2", !TextUtils.isEmpty(p2) ? p2 : (deliverAfter ? "2" : "1"))// 预设弹幕类型：1-投递前，2-投递后 3-管理员 4-职位发布人
                .param("p3", builder.toString())// 本次展示的所有弹幕文案，字符串，&& 分隔
                .debug()
                .build();
    }

    /**
     * 新增B端输入框上方预设常用语点击
     */
    public static void dotTopSetQuestionRvClick(String content, int position, String liveId, String p4) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_TOP_SET_QUESTION_CLICK)
                .param("p", content)// 点击的问题内容
                .param("p2", position)// 问题位置(0/1/2)
                .param("p3", liveId)// liveId
                .param("p4", p4)// 预设弹幕类型：1-投递前，2-投递后 3-管理员 4-职位发布人
                .debug()
                .build();
    }

    /**
     * C/B在直播间「节目单」浮层，点击 预约/已预约/看回放 按钮
     */
    public static void dotProgrammeFloatCk(String recordId, int state, String chapterId, int btn) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_PROGRAMME_FLOATCK)
                .param("p", recordId)// 点击的问题内容
                .param("p2", state)// 直播间状态：0-未开始 1-直播中 2-直播结束
                .param("p3", chapterId)// 点击按钮对应的章节ID
                .param("p4", btn)// 按钮状态：1-预约 2-已预约 3-看回放
                .build();
    }

    /**
     * 直播成就系统 弹幕中+1按钮的点击
     */
    public static void dotAchievementPlusOneClick(String recordId, String msg, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_ACHIEVEMENT_PLUS_ONE_CLICK)
                .param("p", recordId)// 直播加密ID
                .param("p2", msg)// 弹幕，例如："您好，讲一下hr的职位"
                .param("p3", type)// 类型：求讲解：1，其他弹幕：2
                .build();
    }

    /**
     * B端直播学院（视频）内容详情页，点击分享后选择分享渠道
     */
    public static void dotCourseContentShareChannel(String contentId, int channelType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.ACTION_EXTENSION_CAMPUSLIVE_COURSE_CONTENT_SHARE_CHANNEL)
                .param("p", contentId)// 内容ID
                .param("p3", String.valueOf(channelType))// 分享渠道选择：1-微信好友；2-朋友圈；3-复制链接
                .debug()
                .build();
    }

    /**
     * B端直播学院（视频）内容页面，点击 有帮助/没帮助
     */
    public static void dotCourseContentHelpClick(String contentId, int buttonType, int operationType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_CONTENTHELPFULCK)
                .param("p", contentId)// 内容ID
                .param("p3", String.valueOf(buttonType))// 具体按钮：0-没帮助；1-有帮助
                .param("p4", String.valueOf(operationType))//点击类型：0-取消；1-点亮按钮
                .debug()
                .build();
    }

    /**
     * B端直播创建流程中，点击底部优惠详情
     */
    public static void dotPriceBossDiscountDetailClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_PRICEBOSS_DISCOUNT_DETAIL_CLICK)
                .debug()
                .build();
    }

    /**
     * B端直播创建流程中，优惠横幅曝光
     */
    public static void dotCampusLiveAlreadyAppintmentWindowClick(int clickType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ALREADY_APPOINTMENT_WINDOW_CLICK)
                .param("p", String.valueOf(clickType))// 点击类型：取消选择，返回0   ；继续选择，返回1
                .debug()
                .build();
    }

    /**
     * B端直播创建流程中，优惠横幅曝光
     */
    public static void dotCampusLiveAlreadyAppintmentWindowExpose() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ALREADY_APPOINTMENT_WINDOW_EXPO)
                .debug()
                .build();
    }

    /**
     * B端直播创建流程中，优惠横幅曝光
     */
    public static void dotShareGo(int shareWhere, int shareScene, String contentId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_SHARE_GO)
                .param("p", String.valueOf(shareWhere))/*2: 微信好友（808） 3: 微信朋友圈 （709）4: 短信　 5：保存图片（709） 6：复制链接（707）7: QQ好友(717)*/
                .param("p2", shareScene)/*分享出口 职位发布并分享: jd_save我的职位列表: job_list职位详情页: self_jd 招聘周报: report公司主页: company微 JD: vjdboss 勋章: medal牛人简历: cvboss 联盟盟帖：union 影响力页面：vjdboss （708） 非工作时间场景:nigt_share （708）， 主动开聊：chat_achievement （708）,
                面试达成：interview_achievement（708） medal (709)，周榜卡片（813）：weekly_card；16:牛人开聊多，17双聊简历面试 11 深夜工作¶20:牛人简历¶21:直播学院文章（1006更新）*/
                .param("p6", contentId)
                .debug()
                .build();
    }

    /**
     * B端直播学院banner位曝光
     *
     * @param url
     * @param bannerId
     * @param position
     */
    public static void dotLiveCourseBannerShow(String url, String bannerId, int position) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_BANNERSHOW)
                .param("p", url)
                .param("p2", bannerId)
                .param("p3", String.valueOf(position))
                .debug()
                .build();
    }

    /**
     * B端直播学院banner位点击
     *
     * @param url
     * @param bannerId
     * @param position
     */
    public static void dotLiveCourseBannerClick(String url, String bannerId, int position) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_BANNER_CLICK)
                .param("p", url)
                .param("p2", bannerId)
                .param("p3", String.valueOf(position))
                .debug()
                .build();
    }

    /**
     * 直播创建-选择职位流程增加扩桶策略的弹窗曝光
     */
    public static void dotLiveJobExpandExpo() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_JOB_EXPAND_EXPO)
                .debug()
                .build();
    }

    /**
     * 直播创建-选择职位流程增加扩桶策略的弹窗- 扩充流量 点击
     */
    public static void dotLiveJobExpandClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_JOB_EXPAND_CLICK)
                .debug()
                .build();
    }

    /**
     * 1007.803 app B端用户查看直播学院内视频内容时，上/下 滑动切换视频
     */
    public static void dotCourseContentSlide(String currentContentId, String topicTitle, String slideToContentId, int slideDirection) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_CONTENT_SLIDE)
                .param("p", currentContentId)/*当前内容ID*/
                .param("p2", topicTitle)/*当前分类名称，字符串*/
                .param("p3", slideToContentId)/*去向内容ID*/
                .param("p4", String.valueOf(slideDirection))/*滑动方向：1-向上滑；2-向下滑*/
                .debug()
                .build();
    }

    /**
     * 1007.803 app B端用户查看直播学院内视频内容时，上/下 滑动切换视频
     */
    public static void dotCourseContentClick(String currentContentId, String topicTitle, String clickContentId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_CONTENT_CLICK)
                .param("p", currentContentId)/*当前内容ID*/
                .param("p2", topicTitle)/*当前分类名称，字符串*/
                .param("p3", clickContentId)/*点击的内容ID*/
                .debug()
                .build();
    }

    /**
     * 1007.803 app B端用户查看直播学院内视频内容时，上/下 滑动切换视频
     */
    public static void dotCourseContentDownBannerClick(String currentContentId, String topicTitle) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_COURSE_CONTENTDOWNBANNERCK)
                .param("p", currentContentId)/*当前内容ID*/
                .param("p2", topicTitle)/*当前分类名称，字符串*/
                .debug()
                .build();
    }

    /**
     * B端用户「我的直播」列表里，代播报名场次的卡片点击
     */
    public static void dotLiveListPageBInsteadCardCk(String id, int status) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEB_INSTEADCARDCK)
                .param("p", id)// 代播场次ID
                .param("p2", status)// 报名状态：1-审核中；2-待超管授权；3-待填写企业信息；4-报名完成待开播；5-取消报名
                .build();
    }

    /**
     * B端用户「我的直播」列表里，代播报名场次的卡片曝光
     */
    public static void dotLiveListPageBInsteadCardPop(String id, int status) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LISTPAGEB_INSTEADCARDPOP)
                .param("p", id)// 代播场次ID
                .param("p2", status)// 报名状态：1-审核中；2-待超管授权；3-待填写企业信息；4-报名完成待开播；5-取消报名
                .build();
    }

    /**
     * B端用户在观看回放页面的 回放开关弹层 内，点击开关按钮
     */
    public static void dotLiveReplayManageSwitchCk(String recordId, int switchStatus, int role) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_REPLAYMANAGE_SWITCHCK)
                .param("p", recordId)// 直播加密ID
                .param("p2", switchStatus)// 本次点击：0-关闭回放；1-开启回放
                .param("p3", role)// 身份：1-主播；2-管理员
                .build();
    }

    /**
     * B端用户在观看回放页面，点击 职位/回放开关 按钮
     */
    public static void dotLiveReplayManageTabCk(String recordId, int type, int role) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_REPLAYMANAGE_TABCK)
                .param("p", recordId)// 直播加密ID
                .param("p2", type)// 按钮类型：1-职位；2-回放开关
                .param("p3", role)// 身份：1-主播；2-管理员
                .build();
    }

    /**
     * B对用户进入直播间后（直播或回放），3s，10s，1分钟，2分钟，3分钟，4分钟，5分钟，6分钟，7分钟，8分钟，9分钟，10分钟，分别打点
     */
    public static void dotLiveRecruitSpot(String recordId, int type, String timeDesc) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_LIVE_RECRUIT_SPOT)
                .param("p", recordId)// 直播id
                .param("p2", type)// 直播类型（0:直播，1:回放）
                .param("p3", timeDesc)// 时间节点（0:3秒，1:10秒，2:1分钟，3:2分钟，4:3分钟，5:4分钟，6:5分钟，7:6分钟，8:7分钟，9:8分钟，10:9分钟，11:10分钟）
                .debug()
                .build();
    }

    /**
     *
     */
    public static void doLiveVoteTabExpo(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUS_LIVE_BOARD_QA_VOTE_TAB_POP)
                .param("p", liveRecordId)
                .build();
    }


    /**
     * 1008.812 app 主播在点击 挂断按钮 的确认弹窗里，点击 取消/确认
     */
    public static void dotLiveClickCallHangUpButtonSpot(String recordId, String callOnUserId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_ENDCHECK)
                .param("p", recordId)// 直播id
                .param("p2", callOnUserId)// 连线者的uid
                .param("p3", type)// 具体按钮：0-取消；1-挂断
                .debug()
                .build();
    }

    /**
     * 1008.812 app 主播成功连麦后，点击 静音/挂断
     */
    public static void dotLiveVoiceMuteSpot(String recordId, String callOnUserId, int type, int voiceType) {
        if (1 == type) {
            AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_FUC)
                    .param("p", recordId)// 直播id
                    .param("p2", callOnUserId)// 连线者的uid
                    .param("p3", type)// 具体按钮：1-静音；2-挂断
                    .param("p4", voiceType)// 若p3=1，传本次点击的效果：0-静音；1-恢复声音
                    .debug()
                    .build();
        } else {
            AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_FUC)
                    .param("p", recordId)// 直播id
                    .param("p2", callOnUserId)// 连线者的uid
                    .param("p3", type)// 具体按钮：1-静音；2-挂断
                    .debug()
                    .build();
        }
    }

    /**
     * 1008.812 app 主播在接收到视频连线的请求浮层内，点击 立即连线/拒绝
     */
    public static void dotLiveVerifyConnectSpot(String recordId, String callOnUserId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_PERMISSION)
                .param("p", recordId)// 直播id
                .param("p2", callOnUserId)// 连线者的uid
                .param("p3", type)// 点击的按钮：0-拒绝；1-立即连线
                .debug()
                .build();
    }

    /**
     * 1008.812 app 主播在点击视频连线后的浮层内，点击「去微信粘贴给好友」
     */
    public static void dotLiveInviteConnectToShareWxSpot(String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_COPY)
                .param("p", recordId)// 直播id
                .debug()
                .build();
    }

    /**
     * 1008.812 app 主播在视频连线浮层内，点击「连线注意须知」
     */
    public static void dotLiveClickOnConnectionNoticeSpot(String recordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_VIDEOLINK_NOTICE)
                .param("p", recordId)// 直播id
                .debug()
                .build();
    }

    /**
     * 1009.811 app boss在创建成功后的页面里，点击页面内按钮
     */
    public static void dotLiveBuySuccessSpot(String recordId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CREATESUC_BUTTONCK)
                .param("p", recordId)// 直播id
                .param("p2", type)// 点击的按钮类型：1-「查看更多」，2-视频
                .debug()
                .build();
    }

    /**
     * 1009.802 app 观众在直播间职位列表的弹层里，「与你匹配」标签曝光
     *
     * @param liveRecordId
     * @param jobId
     * @param reason
     */
    public static void dotLiveJobListMatchPop(String liveRecordId, String jobId, int reason) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_JOBLIST_MATCH_POP)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", jobId)// 对应职位id
                .param("p3", reason)//曝光原因：1-专题直播间算法，0-其他原因
                .debug()
                .build();
    }


    /**
     * boss直播中点击「投递列表」按钮
     */
    public static void dotManageDeliverList(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_MANAGE_DELIVERLIST)
                .param("p", liveRecordId)// 加密直播id
                .debug()
                .build();
    }

    /**
     * 1103.813 app 本场boss在直播中的直播间内，点击「投递牛人」按钮
     */
    public static void dotManageJobBossDeliverList(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_MANAGE_JOB_BOSS_DELIVERLIST)
                .param("p", liveRecordId)// 加密直播id
                .debug()
                .build();
    }

    /**
     * 直播学院-立即预约的点击
     */
    public static void dotCampusLiveEntryReserveClick(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ENTRY_RESERVE_CLICK)
                .param("p", liveRecordId)// 加密直播id
                .debug()
                .build();
    }

    public static void doStopExplainAction(String liveRecordId, String jobId, String type, String duration, String identity) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_STOP_EXPLAIN_CLICK)
                .param("p", liveRecordId)
                .param("p2", jobId)
                .param("p3", type)
                .param("p4", duration)
                .param("p5", identity)//身份：1-主播，2-管理员
                .debug()
                .build();
    }

    /**
     * @param explainRecordStatus 0-开始录制讲解，1-录制中，2-重新讲解
     */
    public static void doStartExplainAction(String liveRecordId, String jobId, String type, String clickSource, int explainRecordStatus) {
        HashMap<String, String> params = new HashMap<>();
        params.put("p", liveRecordId);
        params.put("p2", jobId);
        params.put("p3", type);
        params.put("p4", clickSource);//1-职位列表，2-弹幕，3-竖屏职位卡片
        if (!TextUtils.equals(clickSource, "2")) {
            String p5 = "";
            switch (explainRecordStatus) {
                case 0:
                    p5 = "开始讲解";
                    break;
                case 1:
                    p5 = "完成录制";
                    break;
                case 2:
                    p5 = "重新讲解";
                    break;
                default:
                    break;
            }
            params.put("p5", p5);//若p4=1或3，具体按钮类型：1-开始讲解，2-重新讲解，3-完成录制
        }
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_START_EXPLAIN_CLICK)
                .param(params)
                .debug()
                .build();
    }


    /**
     * 1010.804 app 观众在心动值-心动职位弹层里，点击 职位卡片/排名按钮
     *
     * @param liveRecordId
     * @param onTheList
     */
    public static void dotCampusHeartJobRank(String liveRecordId, String securityId, String onTheList) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_JOB_RANK)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", securityId)/*加密职位ID*/
                .param("p4", onTheList)/*点击时，观众是否上榜：0-没上榜，1-上榜了*/
                .debug()
                .build();
    }

    /**
     * 1010.804 app 职位发布者b身份进入代播直播间，点击右侧 打榜看板
     *
     * @param liveRecordId
     */
    public static void dotCampusHeartBillBoardJobCk(String liveRecordId, String encryptJobId, String deliverNum, String hitNum) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_BILL_BOARD_JOB_CK)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", encryptJobId)/*加密职位ID*/
                .param("p3", deliverNum)/*点击时的投递人数*/
                .param("p4", hitNum)/*点击时的投递人数*/
                .debug()
                .build();
    }

    /**
     * 1010.804 app 职位发布者b身份打榜看板-职位-打榜牛人列表里，点击牛人卡片
     *
     * @param liveRecordId
     */
    public static void dotCampusHeartBillBoardGeekCk(String liveRecordId, String encryptJobId, String encryptGeekId, String hitScore) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_BILL_BOARD_GEEK_CK)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", encryptJobId)/*加密职位ID*/
                .param("p3", encryptGeekId)/*加密牛人ID*/
                .param("p4", hitScore)/*牛人打榜的心动值*/
                .debug()
                .build();
    }

    /**
     * 1010.804 app 职位发布者b身份打榜看板-职位-打榜牛人的卡片里，点击底部「继续沟通」
     *
     * @param liveRecordId
     */
    public static void dotCampusHeartBillBoardGeekContinue(String liveRecordId, String encryptJobId, String encryptGeekId,
                                                           String source) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_HEART_BILL_BOARD_GEEK_CONTINUE)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", encryptJobId)/*加密职位ID*/
                .param("p3", encryptGeekId)/*加密牛人ID*/
                .param("p4", source)/*曝光来源：投递牛人列表：1职位列表：2*/
                .debug()
                .build();
    }

    /**
     * 1011.803 直播间顶部直播间 icon区域 打开/关闭
     *
     * @param liveRecordId
     */
    public static void dotCampusSpecialTopBarck(String liveRecordId, int type, String livingNum, String way) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SPECIAL_TOP_BARCK)
                .param("p", liveRecordId)// 加密直播id
                .param("p2", type)/*本次操作效果：1-展开icon区域，0-关闭icon区域*/
                .param("p3", livingNum)/*传点击时文案中的 直播数量*/
                .param("p4", way)/*记录关闭方式 1-6s自动上滑，2-手动触摸屏幕关闭*/
                .debug()
                .build();
    }


    /**
     * 1014.811 app 直播创建成功页面，banner点击
     */
    public static void dotCampusLiveFinishBannerClick(String liveId, String bannerId, int position) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_SUCBANNERCK)
                .param("p", liveId)// 加密直播ID
                .param("p2", bannerId)// banner id
                .param("p3", position)// banner 位置
                .build();
    }

    /**
     * 1014.811 app 直播创建成功页面，banner曝光
     */
    public static void dotCampusLiveFinishBannerExposure(String liveId, List<LiveRecruitBannerBean> bannerList) {
        if (LList.isEmpty(bannerList)) return;
        StringBuilder builder = new StringBuilder();
        int count = LList.getCount(bannerList);
        for (int i = 0; i < count; i++) {
            LiveRecruitBannerBean element = LList.getElement(bannerList, i);
            if (null != element) {
                builder.append(element.encryptId);
                if (i < count - 1) {
                    builder.append(",");
                }
            }
        }
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_APPOINTMENT_SUCBANNERPOP)
                .param("p", liveId)// 加密直播ID
                .param("p2", builder.toString())// banner id
                .build();
    }

    /**
     * 1015.812 app b在直播中，"涨流量"icon/横幅 点击
     */
    public static void dotCampusLiveAddFlowCk(String liveRecordId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ADDFLOWCK)
                .param("p", liveRecordId)// 加密直播ID
                .param("p2", type)// 曝光类型：1-icon，2-横幅
                .build();
    }

    /**
     * 1015.812 app b在直播中，"涨流量"icon/横幅曝光
     */
    public static void dotCampusLiveAddFlowPop(String liveRecordId, int type) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ADDFLOWPOP)
                .param("p", liveRecordId)// 加密直播ID
                .param("p2", type)// 曝光类型：1-icon，2-横幅
                .build();
    }


    /**
     * 1016.814 app 主播/管理员 在直播管理台「关闭字幕」弹窗里，点击按钮
     */
    public static void dotCampusLiveSubtitleBtnClick(String liveId, String userType, String actionType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_MANAGE_CAPTIONTOASTCK)
                .param("p", liveId)//加密直播ID
                .param("p2", userType)// boss角色：1-主播，2-管理员
                .param("p3", actionType)// 点击的按钮：1-确认关闭，0-取消
                .build();
    }

    /**
     * 1016.814 app 主播/管理员 在直播管理台，「关闭字幕」按钮曝光
     */
    public static void dotCampusLiveSubtitleBtnExpose(String liveId, String userType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_MANAGE_CAPTIONPOP)
                .param("p", liveId)//加密直播ID
                .param("p2", userType)// boss角色：1-主播，2-管理员
                .build();
    }

    /**
     * 1016.804 app C端用户在新的有了「直播」tab-直播日程内，点击日期（首次进入也记录点击）
     */
    public static void dotCampusGetLiveTabCalendarDataCk(List<LiveInfoBean> liveInfoBeanList) {
        List<JsonObject> jsons = new ArrayList<>();
        JsonObject object;
        for (int i = 0; i < LList.getCount(liveInfoBeanList); i++) {
            LiveInfoBean item = LList.getElement(liveInfoBeanList, i);
            object = new JsonObject();
            object.addProperty("liveid", item.liveRecordId);
            object.addProperty("location", i + 1);
            if (item.liveState == LiveState.WAIT) {
                object.addProperty("status", "0");
            } else if (item.liveState == LiveState.PAUSED || item.liveState == LiveState.PUBLISHING) {
                object.addProperty("status", "1");
            } else {
                object.addProperty("status", "2");
            }
            jsons.add(object);
        }

        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_GET_LIVE_TAB_CALENDAR_DATE_CK)
                .param("p2", GsonUtils.toJson(jsons))//加密直播ID
                .debug()
                .build();
    }


    /**
     * 1016.804 app C端用户在新的有了「直播」tab-直播日程内，点击日期（首次进入也记录点击）
     */
    @SuppressLint("SimpleDateFormat")
    public static void dotCampusGetLiveTabCalendarLiveCk(LiveInfoBean liveRecordItemBean, int location) {
        if (liveRecordItemBean == null) return;
        String state = "";
        if (liveRecordItemBean.liveState == LiveState.WAIT) {
            state = "0";
        } else if (liveRecordItemBean.liveState == LiveState.PAUSED || liveRecordItemBean.liveState == LiveState.PUBLISHING) {
            state = "1";
        } else {
            state = "2";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_GET_LIVE_TAB_CALENDAR_LIVE_CK)
                .param("p", liveRecordItemBean.liveRecordId)//点击的直播间加密ID
                .param("p2", location)//列表内位置（从1开始）
                .param("p3", state)//点击时直播状态：0-未开始，1-直播中，2-已结束
                .param("p4", sdf.format(new Date(liveRecordItemBean.startTime)))//对应的日期，"yyyy-mm-dd"
                .debug()
                .build();
    }

    /**
     * 1017.811 app 创建流程选择职位页面，右上角「？」icon点击
     */
    public static void dotCampusLiveCreateTipBtnClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_CHOOSE_JOB_QA)
                .build();
    }

    /**
     * 1019.801 app 大蓝类型普通直播间，点击职位列表左侧城市
     */
    public static void dotCampusLiveClickGroupCityBlueRoom(String liveId, @LiveState.State int state, String cityCode) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_JOBLISTCK)
                .param("p", liveId)//加密直播ID
                .param("p2", getLiveState(state))//直播状态：0-待开始，1-直播中，2-已结束
                .param("p3", cityCode)//点击的城市code，点「全部职位」固定传 -1
                .build();
    }

    /**
     * 1102.802 app 观众在直播间内，问答抽奖弹层唤醒
     */
    public static void dotCampusLiveQuestionLotteryPop(String liveId, String prizeName) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_QUESTIONLOTTERY_POP)
                .param("p", liveId)// 加密直播ID
                .param("p2", prizeName)// 奖品名称，字符串
                .build();
    }

    /**
     * 1102.802 app 观众在直播间内，问答抽奖弹层内点击（答完题后的无效点击不记录）
     */
    public static void dotCampusLiveQuestionLotteryCk(String liveId, String prizeName, int correct) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_QUESTIONLOTTERY_CK)
                .param("p", liveId)// 加密直播ID
                .param("p2", prizeName)// 奖品名称，字符串
                .param("p3", correct)// 本次点击是否正确：1-正确，0-错误
                .build();
    }

    /**
     * 1103.810 app 用户在直播-我的账户页面，点击「购买直通卡」
     */
    public static void dotCampusLiveAccountPurchase(String cardCountRemain) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ACCOUNT_PURCHASE)
                .param("p", cardCountRemain)// 点击时剩余直通卡数量
                .build();
    }

    /**
     * 1103.810 app 用户在直播直通卡购买页面，点击 「立即购买」
     */
    public static void dotCampusLiveBluePurchaseCk(int cardCount, int zhiDouCount) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BLUEPURCHASE_CK)
                .param("p", cardCount)// 选择的直通卡数量
                .param("p2", zhiDouCount)// 需要支付的直豆数量
                .build();
    }

    /**
     * 1103.810 app 用户在直播直通卡购买页面，点击「立即购买」后支付成功
     */
    public static void dotCampusLiveBluePurchaseDone(int cardCount, int zhiDouCount) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BLUEPURCHASE_DONE)
                .param("p", cardCount)// 选择的直通卡数量
                .param("p2", zhiDouCount)// 需要支付的直豆数量
                .build();
    }

    /**
     * 1103.801 app 用户在直播-我的账户页面，点击扣费记录
     */
    public static void dotCampusLiveBlueAccountCastRecord(String liveId, int count) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ACCOUNT_HISCK)
                .param("p", liveId)// 加密直播ID
                .param("p2", count)// 消耗的数量
                .build();
    }


    /**
     * 1103.801 app 我的直播页面，点击右上角 个人中心 浮层内的「我的账户」按钮
     */
    public static void dotCampusLiveBlueAccountClick() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_SELFCENTER_MYACCOUNT)
                .build();
    }

    /**
     * 1103.810 app 用户在直播直通卡购买页面，点击 我的客服 跳转客服相关功能
     */
    public static void dotCampusLiveBlueCardGetService() {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BLUECARDGET_SERVICE)
                .build();
    }

    /**
     * 此处主动过滤了牛人身份
     *
     * @param liveRecordId 加密直播ID
     * @param state        直播间状态
     * @param title        点击的按钮文案名称，如"与你匹配"、"全部职位"、"22.无忧传媒"
     * @param isTop        点击时，该按钮是否处于讲解置顶状态：0-未置顶，1-置顶
     */
    public static void dotCampusLiveJobListLeftTabCk(String liveRecordId, @LiveState.State int state, String title, boolean isTop) {
        if (UserManager.isGeekRole()) {
            AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_JOBLIST_LEFTTABCK)
                    .param("p", liveRecordId)
                    .param("p2", getLiveState(state))
                    .param("p3", title)
                    .param("p4", isTop ? 1 : 0)
                    .build();
        }
    }

    /**
     * 1105.805 app 观众在高级直播间的 直播详情 tab下，点击「查看全部」
     *
     * @param position 点击的位置：1-本场招聘企业，2-本场上播boss
     */
    public static void dotCampusLiveInsteadRoomAllDetail(String liveRecordId, @LiveState.State int state, int position) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_INSTEADROOM_ALLDETAIL)
                .param("p", liveRecordId)
                .param("p2", getLiveState(state))
                .param("p3", position)
                .build();
    }

    /**
     * 1103.801 app 用户创建直播间中，选择了"蓝领"职位后，点击「立即预约」（新创建/编辑已创建直播间 都上报）
     */
    public static void dotCampusLiveBluePay(List<String> jobIds, long time, String title, String liveId) {
        String subscribeDate = DateUtil.formatterDate(time, "yyyy-MM-dd");
        String subscribeTime = DateUtil.formatterDate(time, "hh-mm");
        StringBuilder sb = new StringBuilder();
        int count = LList.getCount(jobIds);
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                String jobId = LList.getElement(jobIds, i);
                if (!TextUtils.isEmpty(jobId)) {
                    sb.append(jobId);
                    sb.append(",");
                }
            }
        }
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_BLUE_APPOINT)
                .param("p", sb.toString())// 选择的职位ID组，逗号分隔，字符串
                .param("p2", subscribeDate)// 预约直播日期，"yyyy-mm-dd"
                .param("p3", subscribeTime)// 预约直播的时间，"hh-mm"
                .param("p4", title)// 直播间标题文案
                .param("p5", liveId)// 编辑已创建的直播间时，传直播间加密ID；新创建时传空
                .build();
    }

    /**
     * 1106.804 app 主播在编辑回放视频页面 返回弹窗 内，点击 确定/取消
     */
    public static void dotCampusLiveReplayEditBackToastCk(String liveRecordId, int btnType, String p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_REPLAYEDIT_BACKTOASTCK)
                .param("p", liveRecordId)
                .param("p2", btnType)// 点击的按钮：0-取消，1-确定
                .param("p3", p3)// 1-完整回放，2-切片视频
                .build();
    }

    /**
     * 1106.804 app 主播在编辑回放视频页面，点击 保存/返回
     */
    public static void dotCampusLiveReplayEditDone(String liveRecordId, int btnType, String p3) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_REPLAYEDIT_DONE)
                .param("p", liveRecordId)
                .param("p2", btnType)// 点击的按钮：0-返回，1-保存
                .param("p3", p3)// 1-完整回放，2-切片视频
                .build();
    }

    /**
     * 1106.804 app 主播在观看回放页面，点击「编辑回放」
     */
    public static void dotCampusLiveReplayEdit(String liveRecordIdOrJobId, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_REPLAYEDIT)
                .param("p", liveRecordIdOrJobId)//直播ID/职位ID
                .param("p2", liveRecordIdOrJobId)//1-完整回放，2-切片视频
                .build();
    }

    /**
     * 1106.801 app 主播在直播结束录制讲解的过程中，点击 取消/完成录制
     */
    public static void dotCampusLiveReExplainCk(String liveRecordId, String encryptJobId, int btnType, int timeSeconds) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_RECEXPLAIN_INGCK)
                .param("p", liveRecordId)
                .param("p2", encryptJobId)// 讲解的职位加密ID
                .param("p3", btnType)// 点击的按钮类型：0-取消，1-完成录制
                .param("p4", timeSeconds)// 点击时已录制时长（秒）
                .build();
    }

    /**
     * 1106.801 app 主播在直播结束录制讲解页面的职位列表里，点击 讲解职位/重新讲解
     */
    public static void dotCampusLiveReExplainJobCk(String liveRecordId, String encryptJobId, int btnType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_RECEXPLAIN_JOBCK)
                .param("p", liveRecordId)
                .param("p2", encryptJobId)// 讲解的职位加密ID
                .param("p3", btnType)// 点击的按钮类型：1-讲解职位，2-重新讲解
                .build();
    }

    /**
     * 1106.801 app 主播在直播结束录制讲解页面，点击「在招职位」按钮
     */
    public static void dotCampusLiveReExplainJobList(String liveRecordId, int hasExplainSlice) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_RECEXPLAIN_JOBLIST)
                .param("p", liveRecordId)
                .param("p2", hasExplainSlice)// 点击时是否有已经录制的切片：1-有，0-没有
                .build();
    }

    /**
     * 1106.801 app 主播在直播结束讲解视频弹层内，点击 录制更多视频/录制讲解
     */
    public static void dotCampusLiveReExplain(String liveRecordId, int btnType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_RECEXPLAIN)
                .param("p", liveRecordId)
                .param("p2", btnType)// 按钮类型：1-录制更多视频，2-录制讲解
                .build();
    }


    private static String getLiveState(@LiveState.State int state) {
        String value; // 直播状态（「尚未开始」、「直播中」、「已结束」）
        switch (state) {
            case LiveState.PUBLISHING:
            case LiveState.PAUSED:
                value = "1";
                break;
            case LiveState.WAIT:
                value = "0";
                break;
            case LiveState.FINISH:
            case LiveState.DELAY:
                value = "2";
                break;
            default: {
                value = "";
            }
        }
        return value;
    }

    /**
     * 1106.803 app 选择直播清晰度弹层内，点击 具体清晰度
     */
    public static void dotCampusLiveResolutionClick(String liveRecordId, String clickType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_DEFINITIONCK)
                .param("p", liveRecordId)
                .param("p2", clickType)//点击的按钮类型：0-「X」，1-蓝光1080p，2-超清720p
                .build();
    }

    /**
     * 1114.63 app 直播间内，静音设置提示弹窗点击
     *
     * @param clickType 点击按钮：1-静音播放，0-取消
     */
    public static void dotCampusLiveRoomSilentToastCk(String liveRecordId, String clickType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ROOM_SILENT_TOAST_CK)
                .param("p", liveRecordId)
                .param("p2", clickType)
                .debug()
                .build();
    }

    /**
     * 1114.63 app 直播间内，静音设置提示弹窗点击
     *
     * @param clickType 点击按钮：1-去设置，0-取消
     */
    public static void dotCampusLiveWindowPlaySetCk(String liveRecordId, String clickType) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_WINDOW_PLAY_SET_TOAST_CK)
                .param("p", liveRecordId)
                .param("p2", clickType)
                .debug()
                .build();
    }

    /**
     * 1114.63 app 直播间内，静音设置提示弹窗点击
     */
    public static void dotCampusLiveRoomSilentToastPop(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_ROOM_SILENT_TOAST_POP)
                .param("p", liveRecordId)
                .debug()
                .build();
    }

    /**
     * 1114.63 app 关闭小窗时，关闭小窗播放弹窗设置弹窗曝光
     */
    public static void dotCampusWindowPlaySetToastPop(String liveRecordId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_WINDOW_PLAY_SET_TOAST_POP)
                .param("p", liveRecordId)
                .debug()
                .build();
    }

    /**
     * 【1117.71】直播中「投递牛人」列表曝光
     */
    public static void dotCampusGeekListExpose(String liveRecordId, List<BossDeliveryGeekBean> deliveryGeekList, int source) {
        if (LList.getCount(deliveryGeekList) > 0) {
            StringBuilder builder = new StringBuilder();
            for (BossDeliveryGeekBean deliveryGeekBean : deliveryGeekList) {
                if (builder.length() > 0) {
                    builder.append(",");
                }
                builder.append(deliveryGeekBean.encryptGeekId);
            }
            AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_MANAGE_GEEKLISTEXPOSE)
                    .param("p", liveRecordId)
                    .param("p2", builder.toString())
                    .param("p3", source)//曝光来源： 投递牛人列表：1 职位列表：2 (V1126)
                    .debug()
                    .build();
        }
    }

    /**
     * 1118.71 app B在直播招聘主页面，各种点击行为
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=220627784
     */
    public static void dotCampusManageClick(String type, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.CAMPUSLIVE_EXTENSION_BMAIN_CK)
                .param("p", type)//点击类型：1-顶部banner，2-自播模块，3-代播模块，4-直播记录/查看全部，5-名企直播间，6-直播学院，7-个人账号，8-直播记录中点击修改按钮
                .param("p2", p2)//若p=1，传banner链接；若p=4/5，传直播间加密ID（点 查看全部 传空），p=8传点击修改按钮对应的直播间加密ID
                .debug()
                .build();
    }

    /**
     * 1215.711：重做个人权限boss的直播招聘页面，此埋点记录该页面各模块，boss的主动点击行为
     */
    public static void dotCampusManageStartNowClick(String type, String p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.CAMPUSLIVE_BOSS_LIVE_BMAIN_CLICK)
                .param("p", type)//点击位置，上报数字，对应关系： 1-banner位 2-直播记录 3-职位上的去直播按钮 4-名企直播 5-直播学院 6-平台代播 7-预约直播 8-底部立即开播按钮，9-开播奖励的去完成按钮 10-在播同行 11-优秀案例
                .param("p2", p2)//若p = 1，传banner链接 若p = 4，传加密直播间id
                .debug()
                .build();
    }

    /**
     * 1126.712【直播】安卓新增设备权限索取及安全中心权限管理页面权限收集展示迭代
     */
    public static void dotCampusPhoneStateClick(long bossId, int p2) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.CAMPUSLIVE_EXTENSION_LIVE_BLUE_TOOTH_POP)
                .param("p", bossId)
                .param("p2", p2)//=1允许；=2不允许
                .debug()
                .build();
    }

    /**
     * Boss点击 直播职位列表中的某一个职位
     */
    public static void dotCampusProxyBossClickJob(String liveId, String jobId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_POP_JOBCLICK)
                .param("p", liveId)
                .param("p2", jobId)//=1允许；=2不允许
                .debug()
                .build();
    }

    /**
     * boss进入直播间 引导气泡「快来看看投递简历的牛人吧~」的曝光
     */
    public static void dotCampusBubblePopExpo(String liveId) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_RESUME_BUBBLE_EXPO)
                .param("p", liveId)
                .debug()
                .build();
    }

    /**
     * 牛人被邀约
     */
    public static void campusLiveGeekInviteDeliver(String job_id, String expect_id) {
        AnalyticsFactory.create().action("extension-campuslive-livepage-geekinvited-app")
                .param("p", job_id)
                .param("p2", expect_id)
                .param("p3", UserManager.getUID())
                .debug()
                .build();
    }

    /**
     * Boss邀约牛人投递
     */
    public static void campusLiveBossInviteDeliver(long job_id, long expect_id, long geek_id) {
        AnalyticsFactory.create().action("extension-campuslive-livepage-geekinvite-app")
                .param("p", job_id)
                .param("p2", expect_id)
                .param("p3", geek_id)
                .debug()
                .build();
    }

    /**
     * geek点击退出直播间
     * 、点击当前直播间右上角的"X"
     * 、点击「更多直播」里的其他直播间离开当前直播间
     * 、上下滑方式离开当前直播间
     * 、点击小窗上的"X"
     * 、退到后台
     * 、物理返回键离开直播间（仅安卓）
     * 、被其他音视频打断
     */
    public static void campusLiveGeekExitRoom(String liveId, long time) {
        AnalyticsFactory.create().action(LiveAnalyticsAction.EXTENSION_CAMPUSLIVE_QUIT_CLICK)
                .param("p", liveId)//liveid
                .param("p2", String.valueOf(time))//点击x时观看时长
                .debug()
                .build();
    }

    /**
     * 直播投递列表曝光
     */
    public static void campusLiveInviteDeliverListExp(List<BossInviteDeliveryGeekBean> deliveryGeekBeanList) {
        if (LList.getCount(deliveryGeekBeanList) == 0) return;
        StringBuilder geekBuilder = new StringBuilder();
        StringBuilder expectBuilder = new StringBuilder();
        StringBuilder positionBuilder = new StringBuilder();
        for (int i = 0; i < deliveryGeekBeanList.size(); i++) {
            if (geekBuilder.length() > 0) {
                geekBuilder.append(",");
                expectBuilder.append(",");
                positionBuilder.append(",");
            }
            BossInviteDeliveryGeekBean deliveryGeekBean = deliveryGeekBeanList.get(i);
            geekBuilder.append(deliveryGeekBean.geekId);
            expectBuilder.append(deliveryGeekBean.expectId);
            positionBuilder.append(i + 1);
        }
        AnalyticsFactory.create().action("extension-campuslive-livepage-geeklist-app")
                .param("p", geekBuilder.toString())
                .param("p2", expectBuilder.toString())
                .param("p3", positionBuilder.toString())
                .debug()
                .build();
    }

    /**
     * 美颜曝光
     */
    public static void doLiveBeautyShowExp(String liveId, int state, int type, List<BeautyConfigBean> list) {
        AnalyticsFactory param = AnalyticsFactory.create().action("extension-campuslive-companyexp-beautyshow")
                .param("p", liveId)
                .param("p2", String.valueOf(type));
        if (LList.getCount(list) > 0) {
            boolean isDefault = true;
            for (int i = 0; i < list.size(); i++) {
                BeautyConfigBean localBean = list.get(i);
                if (localBean.curValue != localBean.defaultValue) {
                    isDefault = false;
                }
                if (TextUtils.equals(CommonConstant.BEAUTY_NAME_WHITENESS, localBean.name)) {
                    param.param("p3", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_BEAUTY, localBean.name)) {
                    param.param("p4", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_EYESCALE, localBean.name)) {
                    param.param("p5", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_FACESLIM, localBean.name)) {
                    param.param("p6", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_FACEV, localBean.name)) {
                    param.param("p7", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_THIN_NASAL_WINGS, localBean.name)) {
                    param.param("p14", String.valueOf(localBean.curValue));
                }
                param.param("p13", isDefault ? 0 : 1);
            }
        }
        param.param("p8", state);
        param.debug().build();

    }

    /**
     * 特效贴纸曝光
     */
    public static void doLivePasterShowExp(String liveId, int state) {
        AnalyticsFactory param = AnalyticsFactory.create().action("extension-campuslive-companyexp-specialshow")
                .param("p", liveId);
        param.param("p3", state);
        param.debug().build();
    }

    /**
     * 特效贴纸点击
     */
    public static void doLivePasterClickExp(String liveId, int state, List<PasterConfigBean> list) {
        AnalyticsFactory param = AnalyticsFactory.create().action("extension-campuslive-companyexp-specialclick")
                .param("p", liveId);
        if (!LList.isEmpty(list)) {
            for (PasterConfigBean configBean : list) {
                if (configBean != null && configBean.isSelected) {
                    param.param("p2", configBean.name);
                }
            }
        }
        param.param("p3", state);
        param.debug().build();

    }

    /**
     * 美妆点击
     */
    public static void doLiveMakeUpTwoTabClick(String liveId, String twoTabName, int state) {
        AnalyticsFactory param = AnalyticsFactory.create().action("extension-campuslive-company-beautyclick")
                .param("p", liveId)
                .param("p2", twoTabName)
                .param("p11", state);
        param.debug().build();
    }

    /**
     * 美颜点击
     */
    public static void doLiveBeautyClickExp(String liveId, int state, int oneTabType, int twoTabType, List<BeautyConfigBean> beautyList, List<MakeupConfigBean> makeupList) {
        AnalyticsFactory param = AnalyticsFactory.create().action("extension-campuslive-companyexp-beautyclick")
                .param("p", liveId);

        // -1是美妆就不传了
        if (twoTabType != -1) {
            param.param("p2", twoTabType);
        }

        if (!LList.isEmpty(beautyList) && LList.getCount(beautyList) > 0) {
            boolean isDefault = true;
            for (int i = 0; i < beautyList.size(); i++) {
                BeautyConfigBean localBean = beautyList.get(i);
                if (localBean.curValue != localBean.defaultValue) {
                    isDefault = false;
                }
                if (TextUtils.equals(CommonConstant.BEAUTY_NAME_WHITENESS, localBean.name)) {
                    param.param("p3", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_BEAUTY, localBean.name)) {
                    param.param("p4", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_EYESCALE, localBean.name)) {
                    param.param("p5", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_FACESLIM, localBean.name)) {
                    param.param("p6", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_FACEV, localBean.name)) {
                    param.param("p7", String.valueOf(localBean.curValue));
                } else if (TextUtils.equals(CommonConstant.BEAUTY_NAME_THIN_NASAL_WINGS, localBean.name)) {
                    param.param("p14", String.valueOf(localBean.curValue));
                }
                param.param("p13", isDefault ? 0 : 1);
            }
        }
        param.param("p11", state);
        param.debug().build();

    }

    /**
     * 有了社区-创作学院-视频播放时长
     */
    public static void doLiveCollegeVideoPlayTime(String fromId, long playTime, long totalTime) {
        AnalyticsFactory.create().action("get-pgcacadamy-video-duration")
                .param("p", fromId)
                .param("p2", playTime)
                .param("p3", totalTime)
                .debug().build();

    }

    /**
     * APP/小程序直播专场列表点击，原需求1302.711：<a href="https://zhishu.zhipin.com/wiki/3gDzt09pcD9">...</a>
     */
    public static void doLiveMoreLiveClick(String liveId, String tagName, int position, String p4, String matchPositionId) {
        AnalyticsFactory.create().action("geek-live-morelive-click")
                .param("p", liveId)//所在直播间id
                .param("p2", tagName)//当前所在的聚合标签文案
                .param("p3", position)//点击位置：0-标签，1-直播间，2-查看更多直播
                .param("p4", p4)//若p3 = 0，上报点击的标签文案；若p3 = 1，上报点击的直播间id
                .param("p5", matchPositionId)//若p3 = 1，记录返回的匹配职位，无匹配职位时返空
                .debug().build();
    }

    /**
     * APP/小程序直播专场列表曝光，进入列表和每次切换标签时均上报 原需求1302.711：<a href="https://zhishu.zhipin.com/wiki/3gDzt09pcD9">...</a>
     */
    public static void doLiveMoreLiveShow(String liveId, String tags, String tagName, String liveIds) {
        AnalyticsFactory.create().action("geek-live-morelive-show")
                .param("p", liveId)//所在直播间id
                .param("p2", tags)//返回的所有聚合标签，逗号分隔
                .param("p3", tagName)//当前所在的聚合标签文案
                .param("p4", liveIds)//当前所在的聚合标签内，曝光的直播间id，逗号分隔
                .debug().build();
    }

    /**
     * 直播中 AI提词器展开/收起按钮的点击
     */
    public static void doLiveAIBtnExpendClick(String liveId, String type, String p3) {
        AnalyticsFactory.create().action("live-boss-AI-teleprompts-entry-click")
                .param("p", liveId)//所在直播间id
                .param("p2", type)//点击内容：0-展开，1-收起
                .param("p3", p3)//提词板的类型：0-暖场话术，1-职位介绍
                .debug().build();
    }

    /**
     * 直播-B端-AI提词tab-点击
     */
    public static void doLiveAITabClick(String liveId, String tabName,  String sessionId, String tabType, String state) {
        AnalyticsFactory.create().action("live-boss-AI-teleprompts-tab-click")
                .param("p", liveId)//所在直播间id
                .param("p2", tabName)//点击的tab名称
                .param("p3", sessionId)//点击的tab对应的session id
                .param("p4", tabType)//点击的tab对应的type
                .param("p5", state)//生成状态： 0-生成中 1-存在异常 2-生成完毕
                .debug().build();
    }

    /**
     * 直播-B端-虚拟背景-点击
     */
    public static void doLiveAIVirtualClick(String liveId, String btnText, int p3) {
        AnalyticsFactory.create().action("live-boss-virtual-click")
                .param("p", liveId)//所在直播间id
                .param("p2", btnText)//点击的按钮名称，上报文字
                .param("p3", p3)//直播间状态：0-待开播，1-直播中
                .debug().build();
    }

    /**
     * 直播-B端-特效tab-点击
     */
    public static void doLiveAISpecialTabClick(String liveId, String btnText) {
        AnalyticsFactory.create().action("live-boss-special-tab-click")
                .param("p", liveId)//所在直播间id
                .param("p2", btnText)//点击的按钮名称，上报文字
                .debug().build();
    }

    /**
     * 直播-B端-生成页面-停留时长
     * @param duration
     * @param target  离开去向：startlive-开播;close-关闭页面
     */
    public static void doLiveGenerateTimeDuration(long duration, String target) {
        AnalyticsFactory.create()
                .action("live-boss-generate-timeDuration")
                .param("p", String.format("%.2f", duration / 1000.0f))
                .param("p2", target)
                .debug()
                .build();
    }

    /**
     * 直播-B端-生成页开播按钮-点击
     * @param json
     */
    public static void reportLiveGenerateLiveClick(List<AiLiveGenPromptBean2> json) {
        AnalyticsFactory.create()
                .action("live-boss-generate-live-click")
                .param("p", GsonUtils.toJson(json))
                .debug()
                .build();
    }

    /**
     * 直播-B端-切换职位类型-点击
     */
    public static void reportLiveJobTypeClick(String p) {
        AnalyticsFactory.create()
                .action("live-boss-switch-jobtype-click")
                .param("p", p)//点击tab：0-普通职位；1-代招职位
                .debug()
                .build();
    }

    /**
     * 直播-B端-高光按钮-点击
     */
    public static void reportLiveHighlightClick(String p) {
        AnalyticsFactory.create()
                .action("live-boss-highlight-click")
                .param("p", p)//点击后按钮状态：0-关闭，1-开启
                .debug()
                .build();
    }

    /**
     * 直播-B端-讲解弹层-曝光
     */
    public static void reportLiveExplainLayerShow(String liveId) {
        AnalyticsFactory.create()
                .action("live-boss-explain-layer-show")
                .param("p", liveId)//加密直播间id
                .debug()
                .build();
    }

    /**
     * 牛人在个人直播间发起请求时，请求弹层的曝光
     */
    public static void reportLiveSendPhoneChatLayerShow(String liveId, String p2) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-request-layer-show")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//加密职位id
                .debug()
                .build();
    }

    /**
     * 牛人在个人直播间发起请求时，请求弹层的点击
     */
    public static void reportLiveSendPhoneChatLayerClick(String liveId, String p2, String p3) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-request-layer-click")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//点击行为类型：0-关闭，1-发电话，2-发微信，3-发简历
                .param("p3", p3)//加密职位id
                .debug()
                .build();
    }

    /**
     * 牛人点击发电话/微信后，电话/微信弹窗的曝光
     */
    public static void reportLiveExChangeWindowShow(String liveId, String p2, String p3) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-request-window-show")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//加密职位id
                .param("p3", p3)//弹窗类型：1-发电话，2-发微信
                .debug()
                .build();
    }

    /**
     * 牛人点击发电话/微信后，在电话/微信弹窗内的点击
     */
    public static void reportLiveExChangeWindowClick(String liveId, String p2, String p3, String p4) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-request-window-click")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//弹窗类型：1-发电话，2-发微信
                .param("p3", p3)//点击行为类型：0-取消，1-确定
                .param("p4", p4)//加密职位id
                .debug()
                .build();
    }

    /**
     * 直播-C端-无微信弹窗-曝光
     */
    public static void reportLiveNotWeChatShow(String liveId, String p2) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-nowechat-window-show")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//加密职位id
                .debug()
                .build();
    }

    /**
     * 直播-C端-无微信弹窗-点击
     */
    public static void reportLiveNotWeChatClick(String liveId, String p2) {
        if (TextUtils.isEmpty(liveId)) return;
        AnalyticsFactory.create()
                .action("live-geek-nowechat-window-click")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//加密职位id
                .debug()
                .build();
    }

    /**
     * 直播-B端-讲解弹层-点击
     */
    public static void reportLiveExplainLayerClick(String liveId, String p2, String p3, String p4, String p5) {
        AnalyticsFactory.create()
                .action("live-boss-explain-layer-click")
                .param("p", liveId)//加密直播间id
                .param("p2", p2)//点击内容 0-切换tab 1-讲解视频 2-删除按钮 3-自动推广开关 4-发布按钮 5-关闭
                .param("p3", p3)//若p2=0，记录点击的tab名称
                .param("p4", p4)//若p2=1或2，记录点击/删除的切片id
                .param("p5", p5)//若p2=3，记录点击后的按钮状态，0-关闭，1-开启
                .debug()
                .build();
    }

    /**
     * 直播-B端-播中投递列表-点击
     */
    public static void reportLiveBossingDeliverListClick(String liveId, String type, String tabName, String geekId, String position) {
        AnalyticsFactory.create()
                .action("live-boss-ing-deliverlist-click")
                .param("p", liveId)//加密直播间id
                .param("p2", type)//点击行为类型：0-切换tab，1-牛人卡片，2-关闭
                .param("p3", tabName)//若p2 = 0，上报点击的tab名称（点击当前所在tab也上报）
                .param("p4", geekId)//若p2 = 1，上报加密牛人id
                .param("p5", position)//若p2 = 1，上报点击位置：0-非按钮，1-同意交换按钮
                .debug()
                .build();
    }

    /**
     * 直播-B端-牛人详情-点击
     */
    public static void reportLiveBossingGeekDetailClick(String liveId, String geekId, String type) {
        AnalyticsFactory.create()
                .action("live-boss-ing-geekdetail-click")
                .param("p", liveId)//加密直播间id
                .param("p2", geekId)//加密牛人id
                .param("p3", type)//点击行为类型：0-返回，1-同意交换，2-关闭
                .debug()
                .build();
    }

    /**
     * 直播-B端-播后投递列表-点击
     */
    public static void reportLiveBossEndDeliverListClick(String liveId, String type, String tabName, String geekId, String position) {
        AnalyticsFactory.create()
                .action("live-boss-end-deliverlist-click")
                .param("p", liveId)//加密直播间id
                .param("p2", type)//点击行为类型：0-切换tab，1-牛人卡片
                .param("p3", tabName)//若p2 = 0，上报点击的tab名称（点击当前所在tab也上报）
                .param("p4", geekId)//若p2 = 1，上报加密牛人id
                .param("p5", position)//若p2 = 1，上报点击位置：0-非按钮，1-按钮
                .debug()
                .build();
    }
}
