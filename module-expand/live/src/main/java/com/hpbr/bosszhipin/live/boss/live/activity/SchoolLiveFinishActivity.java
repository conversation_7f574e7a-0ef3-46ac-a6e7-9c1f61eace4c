package com.hpbr.bosszhipin.live.boss.live.activity;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseLiveActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.boss.live.fragment.FinishWithGeekListFragment;
import com.hpbr.bosszhipin.live.boss.live.fragment.FinishWithoutGeekListFragment;
import com.hpbr.bosszhipin.live.boss.live.fragment.LiveFinishGeekSelectorFragment;
import com.hpbr.bosszhipin.live.boss.live.fragment.QuestionAndExplainFragment;
import com.hpbr.bosszhipin.live.boss.live.fragment.RealTimeIntentionFragment;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.LiveFinishViewModel;
import com.hpbr.bosszhipin.live.boss.live.widget.BossAnchorGoldMedalGifView;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.LiveHighlightTipsFragment;
import com.hpbr.bosszhipin.live.export.Constants;
import com.hpbr.bosszhipin.live.export.IntentKey;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.export.LiveService;
import com.hpbr.bosszhipin.live.export.WindowIntercept2;
import com.hpbr.bosszhipin.live.net.response.BossEndOverviewResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListOnlineResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveDetailForSchoolBatchResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.window.FloatWindowManager;
import com.monch.lbase.util.LList;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.twl.ui.ToastUtils;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

@RouterUri(path = Constants.PATH_SCHOOL_LIVE_FINISH, interceptors = WindowIntercept2.class)
public class SchoolLiveFinishActivity extends BaseLiveActivity implements View.OnClickListener {

    private AppTitleView titleView;
    private ConstraintLayout clRoot, clRecoverTitle;
    private ZPUIRoundButton btnExperience, btnBottomAction;
    private View viewBgGlobal;
    private BossAnchorGoldMedalGifView goldMedalGifView;

    private LiveFinishViewModel viewModel;
    private BossEndOverviewResponse overviewResponse;

    private FinishWithGeekListFragment withGeekListFragment;
    private FinishWithoutGeekListFragment withoutGeekListFragment;

    private String liveRecordId;
    private String endLiveReason;// 直播结束原因

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        /*设置状态栏透明*/
        makeStatusBarTransparent();
        setContentView(R.layout.live_activity_school_finish);

        /*关闭悬浮小窗*/
        FloatWindowManager.getInstance().resetWindow();

        initViews();
        bindListener();
        initMvp();
        if (null != viewModel && !TextUtils.isEmpty(liveRecordId)) {
            viewModel.requestRecordDetailForSchool(liveRecordId);
        }
    }

    private void initViews() {
        titleView = findViewById(R.id.title_view);
        titleView.setBackClickListener();
        titleView.setDividerInvisible();
        titleView.changeStyle(AppTitleView.SKIN_CUSTOM, Color.TRANSPARENT);
        clRoot = findViewById(R.id.cl_root);
        viewBgGlobal = findViewById(R.id.view_bg_global);
        clRecoverTitle = findViewById(R.id.cl_recover_title);
        btnExperience = findViewById(R.id.btn_experience);
        btnBottomAction = findViewById(R.id.btn_bottom_action);
        goldMedalGifView = findViewById(R.id.gold_medal_view);
    }

    private void bindListener() {
        viewBgGlobal.setOnClickListener(this);
        btnExperience.setOnClickListener(this);
    }

    private void initMvp() {
        if (getIntent() == null) return;
        liveRecordId = getIntent().getStringExtra(IntentKey.INTENT_LIVE_RECORD_ID);
        endLiveReason = getIntent().getStringExtra(IntentKey.INTENT_LIVE_END_REASON);

        if (!TextUtils.isEmpty(endLiveReason)) {
            showLiveEndReasonDialog(endLiveReason);
        }

        viewModel = LiveFinishViewModel.instance(this);
        viewModel.needFitSystemWindow = true;
        viewModel.isAdmin = getIntent().getBooleanExtra(IntentKey.INTENT_LIVE_IS_ADMIN, false);

        viewModel.detailForSchoolLiveData.observe(this, new Observer<BossLiveDetailForSchoolBatchResponse>() {
            @Override
            public void onChanged(BossLiveDetailForSchoolBatchResponse batchResponse) {
                if (batchResponse == null || batchResponse.bossEndOverviewResponse == null) return;
                updateUi(batchResponse.bossEndOverviewResponse);
                //1114.61【直播】个人直播间权益调整@刘睿@闫莹莹@汪亚东 @鲁斌@王鹏
                //后续要剔除金牌主播相关代码
                dealGoldMedalGif(batchResponse.bossEndOverviewResponse);

                if (viewModel.detailResp != null && viewModel.detailResp.isOnlineBzpType() && viewModel.detailResp.isNormalRoom()) {
                    BossGetDeliveryGeekListOnlineResponse geekListOnlineResponse = batchResponse.bossGetDeliveryGeekListOnlineResponse;
                    if ((batchResponse.bossEndOverviewResponse.roomLevel == 1 || viewModel.isThisLiveBoss)
                            && geekListOnlineResponse != null
                            && (!LList.isEmpty(geekListOnlineResponse.phoneGeekList) || !LList.isEmpty(geekListOnlineResponse.wxGeekList) || !LList.isEmpty(geekListOnlineResponse.resumeGeekList))
                    ) {
                        // 没有牛人列表
                        withGeekListFragment = FinishWithGeekListFragment.getInstance(null);
                        getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fl_content_container, withGeekListFragment)
                                .commitAllowingStateLoss();
                        viewModel.deliveryGeekListOnlineObserver.postValue(geekListOnlineResponse);
                    } else {
                        withoutGeekListFragment = FinishWithoutGeekListFragment.getInstance(null);
                        getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fl_content_container, withoutGeekListFragment)
                                .commitAllowingStateLoss();
                    }
                } else {
                    BossGetDeliveryGeekListResponse geekListResponse = batchResponse.bossGetDeliveryGeekListResponse;
                    //[1012.802] 普通直播间 || 本场boss '显示'
                    if ((batchResponse.bossEndOverviewResponse.roomLevel == 1 || viewModel.isThisLiveBoss)
                            && geekListResponse != null
                            && (!LList.isEmpty(geekListResponse.geekList) || !LList.isEmpty(geekListResponse.rejectGeekList))
                    ) {
                        // 没有牛人列表
                        withGeekListFragment = FinishWithGeekListFragment.getInstance(null);
                        getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fl_content_container, withGeekListFragment)
                                .commitAllowingStateLoss();
                        viewModel.deliveryGeekListObserver.postValue(geekListResponse);
                    } else {
                        withoutGeekListFragment = FinishWithoutGeekListFragment.getInstance(null);
                        getSupportFragmentManager().beginTransaction()
                                .replace(R.id.fl_content_container, withoutGeekListFragment)
                                .commitAllowingStateLoss();
                    }
                }
                checkShowHighlightTips(batchResponse.bossEndOverviewResponse);
            }
        });
        // 问答和视频讲解
        viewModel.questionExplainFragmentLiveData.observe(this, show -> {
            if (show) {
                showBottomFragment(QuestionAndExplainFragment.getInstance(null), QuestionAndExplainFragment.TAG, 0.7f);
                LiveAnalysisUtil.reportLiveExplainLayerShow(liveRecordId);
            } else {
                hideBottomFragment(QuestionAndExplainFragment.TAG);
            }
            viewModel.questionExplainFragmentShowing = show;
        });
        // 牛人榜
        viewModel.intentionFragmentShowLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isShow) {
                if (isShow) {
                    Bundle bundle = new Bundle();
                    bundle.putInt(IntentKey.INTENT_DATA_INT, LiveConstants.FROM_BOSS_LIVE_FINISH_ACTIVITY);
                    bundle.putBoolean(IntentKey.KEY_IS_NEED_TOP_ROUND, true);
                    showBottomFragment(RealTimeIntentionFragment.getInstance(bundle), RealTimeIntentionFragment.TAG, 0.7f);
                } else {
                    hideBottomFragment(RealTimeIntentionFragment.TAG);
                }
                viewModel.intentionFragmentShowing = isShow;
            }
        });
        // 职位筛选页的显示与隐藏
        viewModel.selectorFragmentShowLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean show) {
                if (show) {
                    showBottomFragment(LiveFinishGeekSelectorFragment.getInstance(null), LiveFinishGeekSelectorFragment.TAG, 1f);
                } else {
                    hideBottomFragment(LiveFinishGeekSelectorFragment.TAG);
                }
                viewModel.selectorFragmentShowing = show;
            }
        });
        viewModel.createExperienceLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String recordId) {
                if (!TextUtils.isEmpty(recordId)) {
                    LiveService.openAnchorActivity(SchoolLiveFinishActivity.this, recordId, true, false);
                    AppUtil.finishActivity(SchoolLiveFinishActivity.this);
                } else {
                    ToastUtils.showText("数据异常，请重试");
                }
            }
        });
        // 恢复直播
        viewModel.recoverLiveLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean success) {
                // 跳转到直播页
                LiveService.openAnchorActivity(SchoolLiveFinishActivity.this, viewModel.liveRecordId, viewModel.isExperienceLive);
                AppUtil.finishActivity(SchoolLiveFinishActivity.this);
            }
        });
        viewModel.mLoading.observe(this, loadingStr -> {
            if (loadingStr != null) {
                showProgressDialog(loadingStr);
            } else {
                dismissProgressDialog();
            }
        });
        LiveBus.with(ChannelConstants.LIVE_CAMPUS_BOSS_PLAYBACK_VIDEO_MODIFIED, Boolean.class).observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean cleared) {
                if (overviewResponse != null) {
                    overviewResponse.liveVideoState = 1;
                    overviewResponse.liveVideoUrl = null;

                    if (!viewModel.showRecoverBtn(overviewResponse)) {// 如果是恢复直播，则不隐藏按钮
                        btnBottomAction.setVisibility(View.GONE);
                    }
                    if (withGeekListFragment != null) {
                        withGeekListFragment.setPlaybackGenerating();
                    }
                    if (withoutGeekListFragment != null) {
                        withoutGeekListFragment.setPlaybackGenerating();
                    }
                }
            }
        });

        //直播高光提示弹框
        viewModel.showHighlightTipsLiveData.observe(this, aBoolean -> {
            viewModel.isHighlightShowing = true;
            hideBottomFragment(QuestionAndExplainFragment.TAG);
            viewModel.questionExplainFragmentShowing = false;

            Bundle bundle = new Bundle();
            bundle.putBoolean(IntentKey.KEY_LIVE_FINISH_SOURCE, true);
            LiveHighlightTipsFragment liveHighlightTipsFragment = LiveHighlightTipsFragment.newInstance(bundle);
            showBottomFragment(liveHighlightTipsFragment ,LiveHighlightTipsFragment.TAG, 0.7f);
            liveHighlightTipsFragment.setOnDismissListener(this::hideHighlightTipsFragment);
        });
    }

    private void hideHighlightTipsFragment(){
        hideBottomFragment(LiveHighlightTipsFragment.TAG);
        showBottomFragment(QuestionAndExplainFragment.getInstance(null), QuestionAndExplainFragment.TAG, 0.7f);
        LiveAnalysisUtil.reportLiveExplainLayerShow(liveRecordId);
        viewModel.questionExplainFragmentShowing = true;
        viewModel.isHighlightShowing = false;
    }

    private void checkShowHighlightTips(BossEndOverviewResponse endOverResp) {
        if (null == endOverResp) return;
        if (endOverResp.isOnlineBzpType() && endOverResp.publishLiveHighlight == 0 && endOverResp.autoPublishGet == 0 && endOverResp.hasLiveHighlight == 1) {
            showBottomFragment(QuestionAndExplainFragment.getInstance(null), QuestionAndExplainFragment.TAG, 0.7f);
            LiveAnalysisUtil.reportLiveExplainLayerShow(liveRecordId);
            viewModel.questionExplainFragmentShowing = true;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (null != viewModel && !TextUtils.isEmpty(liveRecordId)) {
            viewModel.requestEndOver(liveRecordId);
        }
    }

    /**
     * 金牌主播动画弹框
     */
    private Runnable runnableGoldMedal;

    private void dealGoldMedalGif(BossEndOverviewResponse response) {
        if (null == response) return;
        if (null == runnableGoldMedal) {
            runnableGoldMedal = new Runnable() {
                @Override
                public void run() {
                    if (response.goldAnchor == 1) {
                        String bgUrl = response.goldAnchorBackground;
                        String gifUrl = response.goldAnchorWebp;
                        long time = response.goldAnchorExpireTime;
                        goldMedalGifView.startShowAnim(gifUrl, bgUrl, time);
                    }
                }
            };
        }
        App.get().getMainHandler().postDelayed(runnableGoldMedal, 200);
    }

    private void updateUi(@NonNull BossEndOverviewResponse resp) {
        if (resp == null) return;
        this.overviewResponse = resp;
        viewModel.isHighLevel = (resp.roomLevel == 2);

        if (resp.liveState == LiveState.DELAY) {// 超时结束
            titleView.setTitle("直播超时结束");
        } else {// 已结束
            titleView.setTitle("直播已结束");

            if (viewModel.showRecoverBtn(resp)) {
                btnBottomAction.setText("恢复直播");
                btnBottomAction.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        // 恢复直播
                        viewModel.recoverLive();
                    }
                });
                btnBottomAction.setVisibility(View.VISIBLE);
                clRecoverTitle.setVisibility(View.VISIBLE);

            } else if (resp.liveVideoFlag == 0) {// 无回放
            } else if (resp.liveVideoFlag == 1 && !TextUtils.isEmpty(resp.liveVideoUrl) && resp.liveVideoState != 1) {
                // 有回放，回放按钮不在底部显示了，在上面网格布局中常驻
                btnBottomAction.setVisibility(View.GONE);
            } else {
                btnBottomAction.setVisibility(View.GONE);
            }

            if (!viewModel.isAdmin && viewModel.isExperienceLive && !viewModel.isThisLiveBoss) {// 需要展示重新体验
                if (btnBottomAction.getVisibility() == View.VISIBLE) {
                    btnExperience.setVisibility(View.VISIBLE);
                } else {// 如果只有一个重新体验按钮，宽度充满，样式改变
                    btnBottomAction.setText("重新体验");
                    btnBottomAction.setOnClickListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            viewModel.createExperienceRecordDetail();
                        }
                    });
                    btnBottomAction.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (overviewResponse == null) return;
        if (ClickProtectedUtil.blockClickEvent()) {
            return;
        }

        int id = v.getId();
        if (id == R.id.view_bg_global) {/*点击「全局的背景」*/
            hideAllFragments();
        } else if (id == R.id.btn_experience) {// 重新体验
            viewModel.createExperienceRecordDetail();
        }
    }

    private void showBottomFragment(Fragment fragment, String tag, float heightPercent) {
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(clRoot);
        constraintSet.constrainPercentHeight(R.id.fl_container, heightPercent);
        constraintSet.applyTo(clRoot);

        viewBgGlobal.setVisibility(View.VISIBLE);

        getSupportFragmentManager().beginTransaction()
                .setCustomAnimations(R.anim.live_recruit_bottom_in, R.anim.live_recruit_bottom_out)
                .replace(R.id.fl_container, fragment, tag)
                .commitAllowingStateLoss();
    }

    private void hideBottomFragment(String tag) {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(tag);
        if (fragment != null) {
            fm.beginTransaction()
                    .setCustomAnimations(R.anim.live_recruit_bottom_in, R.anim.live_recruit_bottom_out)
                    .remove(fragment)
                    .commitAllowingStateLoss();
        }
        viewBgGlobal.setVisibility(View.GONE);

        if (TextUtils.equals(QuestionAndExplainFragment.TAG, tag)) {
            LiveAnalysisUtil.reportLiveExplainLayerClick(liveRecordId, "5", "", "", "");
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        if(viewModel.isHighlightShowing){
            hideHighlightTipsFragment();
            return;
        }
        if (viewModel.questionExplainFragmentShowing) {
            viewModel.questionExplainFragmentLiveData.postValue(false);
            return;
        }
        if (viewModel.intentionFragmentShowing) {
            viewModel.intentionFragmentShowLiveData.postValue(false);
            return;
        }
        if (viewModel.posterFragmentShowing) {
            viewModel.posterFragmentLiveData.postValue(false);
            return;
        }
        if (viewModel.selectorFragmentShowing) {
            viewModel.selectorFragmentShowLiveData.postValue(false);
            return;
        }
        if (goldMedalGifView.isShowing()) {
            goldMedalGifView.closeGoldMedalView();
            return;
        }
        AppUtil.finishActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        goldMedalGifView.closeGoldMedalView();
        if (null != runnableGoldMedal) {
            App.get().getMainHandler().removeCallbacks(runnableGoldMedal);
            runnableGoldMedal = null;
        }
    }

    private void hideAllFragments() {
        if (viewModel.questionExplainFragmentShowing) {
            viewModel.questionExplainFragmentLiveData.postValue(false);
        }
        if (viewModel.intentionFragmentShowing) {
            viewModel.intentionFragmentShowLiveData.postValue(false);
        }
        if (viewModel.posterFragmentShowing) {
            viewModel.posterFragmentLiveData.postValue(false);
        }
        if (viewModel.selectorFragmentShowing) {
            viewModel.selectorFragmentShowLiveData.postValue(false);
        }

        if(viewModel.isHighlightShowing){
            hideHighlightTipsFragment();
        }
    }

    private void showLiveEndReasonDialog(@NonNull String endLiveReason) {
        DialogUtils d = new DialogUtils.Builder(this)
                .setSingleButton()
                .setTitle("直播已被下线")
                .setDesc(endLiveReason)
                .setPositiveAction("我知道了")
                .build();
        d.show();
    }

    @Override
    protected boolean shouldUserDarkMode() {
        return false;
    }

}
