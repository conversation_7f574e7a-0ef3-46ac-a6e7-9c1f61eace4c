package com.hpbr.bosszhipin.live.bean;

import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.twl.utils.GsonUtils;

/**
 * @author: 冯智健
 * @date: 2025年06月20日 14:10
 * @description:
 */
public class LiveGeekPostStatusBean extends BaseMessageBean {
    private static final long serialVersionUID = -2225130477651869833L;
    public int exchangeState;// 0boss未处理 1已经交换 2拒绝 微信和电话用
    public String encryptGeekId;
    @LiveConstants.GeekDeliverType
    public int deliverType;// 投递类型
    public static LiveGeekPostStatusBean parseServerMessage(String args) {
        try {
            return GsonUtils.fromJson(args, LiveGeekPostStatusBean.class);
        } catch (Exception e) {
        }
        return null;
    }
}
