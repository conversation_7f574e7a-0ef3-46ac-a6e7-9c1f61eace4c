package com.hpbr.bosszhipin.live.net.bean;

import com.hpbr.bosszhipin.live.export.LiveConstants;

import net.bosszhipin.api.bean.BaseServerBean;

public class BossDeliveryGeekBean extends BaseServerBean {
    private static final long serialVersionUID = 1L;
    public String encryptDeliveryId;// 加密的投递id
    public String encryptGeekId;// 加密牛人ID
    public long geekId;// 不加密牛人ID
    public String geekName;// 姓名
    public String geekWorkYear;// 工作经验
    public String geekDegree;// 最高学历
    public String salary;// 期望薪资
    public String geekAvatar;// 头像
    public String lastWorkDesc;// 最近一份工作经历公司及职位
    public String lastWorkTime;// 最近一份工作时长
    public long jobId;// 投递的职位id
    public String jobName;// 投递的职位名称
    public long bossId;// 投递的职位发布人id
    public String securityId;// 职位securityId
    public int highIntention;// 是否高意向;1是0否
    public int hasResume;// 是否有附件简历;1是0否
    public long expectId;// 牛人期望id
    public String encryptExpectId;// 加密的牛人期望id
    public long usingIntention; // 贡献心动值
    public long intentionId;
    public int gender;// 性别 0：女 1：男
    public int geekType;// 0 职场人  1 学生 「同公司boss【geek/delivery/list/jobCreator】使用」
    public String encryptJobId;// 加密的投递的职位id 「同公司boss【geek/delivery/list/jobCreator】使用」
    public long recommendScore;// 推荐分 「同公司boss【geek/delivery/list/jobCreator】使用」
    public int inviteState;// 邀请投递状态，0未邀请 1已经邀请 2已经投递
    public int invite;// 是否是邀请投递 1是
    public String exchangeUrl;// 交换协议 微信和电话用
    public String msgId;// 交换消息id 微信和电话用
    public int exchangeState;// 0boss未处理 1已经交换 2拒绝 微信和电话用
    @LiveConstants.GeekDeliverType
    public int deliverType = LiveConstants.GeekDeliverType.TYPE_NONE;// 投递类型(本地字段)
}