package com.hpbr.bosszhipin.live.boss.live.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.boss.live.adapter.SchoolFinishGeekListAdapter;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.LiveFinishViewModel;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListOnlineResponse;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.ui.decorator.AppDividerDecorator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;

public class LiveFinishGeekListFragment extends BaseFragment {
    public static final String TAG = "LiveFinishGeekListFragment";
    private SchoolFinishGeekListAdapter adapter;
    private ZPUIStateLayoutManager stateLayoutManager;
    private LiveFinishViewModel viewModel;
    @LiveConstants.GeekDeliverType
    private int deliverType = LiveConstants.GeekDeliverType.TYPE_NONE;

    public static LiveFinishGeekListFragment getInstance(@LiveConstants.GeekDeliverType int type) {
        LiveFinishGeekListFragment f = new LiveFinishGeekListFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.DATA_INT, type);
        f.setArguments(bundle);
        return f;
    }

    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.live_fragment_finish_geek_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (getArguments() != null) {
            deliverType = getArguments().getInt(Constants.DATA_INT, LiveConstants.GeekDeliverType.TYPE_PHONE);
        }
        viewModel = LiveFinishViewModel.instance(getActivity());
        RecyclerView rvList = view.findViewById(R.id.rv_list);
        stateLayoutManager = new ZPUIStateLayoutManager(activity, rvList);
        adapter = new SchoolFinishGeekListAdapter(null);
        AppDividerDecorator decorator = new AppDividerDecorator();
        decorator.setDividerColor(ContextCompat.getColor(activity, R.color.transparent));
        decorator.setDividerHeight(Scale.dip2px(activity, 10f));
        rvList.addItemDecoration(decorator);

        View emptyView = LayoutInflater.from(activity).inflate(R.layout.zpui_state_empty_default_layout, null);
        // 缺省页手动设置为深色模式
        ImageView ivTop = emptyView.findViewById(R.id.iv_top);
        ivTop.setImageResource(R.mipmap.ic_empty_page_dark);
        TextView tvTitle = emptyView.findViewById(R.id.tv_title);
        tvTitle.setTextColor(ContextCompat.getColor(activity, R.color.color_FF5E5E5E));
        stateLayoutManager.getEmptyLayout().setCustomView(emptyView);
        adapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                // 跳转到牛人详情页
                BossDeliveryGeekBean geekBean = (BossDeliveryGeekBean) adapter.getItem(position);
                if (geekBean == null) return;
                ParamBean paramBean = new ParamBean();
                paramBean.userId = geekBean.geekId;
                paramBean.jobId = geekBean.jobId;
                paramBean.operation = ParamBean.OPERATION_REFUSE_STATUS;
                paramBean.geekAvatar = geekBean.geekAvatar;
                paramBean.geekName = geekBean.geekName;
                paramBean.securityId = geekBean.securityId;
                paramBean.expectId = geekBean.expectId;
                paramBean.from = ParamBean.FROM_LIVE_DELIVERY_CLOSED_LOOP;
                BossPageRouter.jumpResumeActivity(activity, paramBean);
                viewModel.viewDeliveryGeek(geekBean.geekId);
                if (deliverType != LiveConstants.GeekDeliverType.TYPE_NONE) {
                    LiveAnalysisUtil.reportLiveBossEndDeliverListClick(viewModel.liveRecordId, "1", "", geekBean.encryptGeekId, "0");
                }
            }
        });
        adapter.setOnItemChildClickListener((adapter, view1, position) -> {
            if (ClickProtectedUtil.blockClickEvent()) return;
            BossDeliveryGeekBean geekBean = (BossDeliveryGeekBean) adapter.getItem(position);
            if (geekBean == null) return;
            if (view1.getId() == R.id.btn_continue_chat) {
                SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
                singleChatParam.setJobId(geekBean.jobId);
                singleChatParam.setFriendId(geekBean.geekId);
                singleChatParam.setSecurityId(geekBean.securityId);
                singleChatParam.setExpectId(geekBean.expectId);
                singleChatParam.setChangePositionDesc(geekBean.jobName);
                singleChatParam.setFromDZUser(false);
                SingleRouter.startChat(activity, singleChatParam);
                if (deliverType != LiveConstants.GeekDeliverType.TYPE_NONE) {
                    LiveAnalysisUtil.reportLiveBossEndDeliverListClick(viewModel.liveRecordId, "1", "", geekBean.encryptGeekId, "1");
                }
            }
        });
        rvList.setAdapter(adapter);
        if (viewModel.detailResp != null && viewModel.detailResp.isOnlineBzpType() && viewModel.detailResp.isNormalRoom()) {
            viewModel.deliveryGeekListOnlineObserver.observe(this, new Observer<BossGetDeliveryGeekListOnlineResponse>() {
                @Override
                public void onChanged(BossGetDeliveryGeekListOnlineResponse response) {
                    if (adapter != null) {
                        if (response == null || (LList.isEmpty(response.resumeGeekList) && LList.isEmpty(response.wxGeekList) && LList.isEmpty(response.phoneGeekList))) {
                            adapter.setNewData(null);
                            stateLayoutManager.showEmptyScene();
                        } else {
                            if (deliverType == LiveConstants.GeekDeliverType.TYPE_RESUME) {
                                adapter.setNewData(response.resumeGeekList);
                                stateLayoutManager.dismiss();
                                dotGeekListOnlineExpose(response.resumeGeekList, response.encryptJobId, LiveConstants.GeekDeliverName.TYPE_RESUME);
                            } else if (deliverType == LiveConstants.GeekDeliverType.TYPE_WX) {
                                adapter.setNewData(response.wxGeekList);
                                stateLayoutManager.dismiss();
                                dotGeekListOnlineExpose(response.wxGeekList, response.encryptJobId, LiveConstants.GeekDeliverName.TYPE_WX);
                            } else if (deliverType == LiveConstants.GeekDeliverType.TYPE_PHONE) {
                                adapter.setNewData(response.phoneGeekList);
                                stateLayoutManager.dismiss();
                                dotGeekListOnlineExpose(response.phoneGeekList, response.encryptJobId, LiveConstants.GeekDeliverName.TYPE_PHONE);
                            } else {
                                adapter.setNewData(null);
                                stateLayoutManager.showEmptyScene();
                            }
                        }
                    }
                }
            });
        } else {
            viewModel.deliveryGeekListObserver.observe(this, new Observer<BossGetDeliveryGeekListResponse>() {
                @Override
                public void onChanged(BossGetDeliveryGeekListResponse response) {
                    if (adapter != null) {
                        if (response == null || LList.isEmpty(response.geekList)) {
                            adapter.setNewData(null);
                            stateLayoutManager.showEmptyScene();
                        } else {
                            adapter.setNewData(response.geekList);
                            stateLayoutManager.dismiss();
                        }
                    }
                }
            });
        }
    }

    /**
     * 列表曝光埋点
     */
    private void dotGeekListOnlineExpose(List<BossDeliveryGeekBean> geekBeanList, String encryptJobId, String tabName) {
        if (LList.isEmpty(geekBeanList)) return;
        List<String> geekIdList = new ArrayList<>();
        List<String> btnTitleList = new ArrayList<>();
        for (BossDeliveryGeekBean bean : geekBeanList) {
            if (bean == null) continue;
            geekIdList.add(String.valueOf(bean.geekId));
            btnTitleList.add(String.valueOf(bean.hasResume));
        }
        LiveAnalysisUtil.dotApplyGeekListExpose(
                viewModel.liveRecordId,
                StringUtil.connectTextWithChar(",", geekIdList),
                StringUtil.connectTextWithChar(",", btnTitleList),
                encryptJobId,
                viewModel.getUserIdentityForAnalysis(),
                tabName
        );
    }
}
