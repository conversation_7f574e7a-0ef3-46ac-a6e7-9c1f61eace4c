package com.hpbr.bosszhipin.live.campus.anchor.adpater;

import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseViewHolder;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;

import java.util.List;
import java.util.Locale;

public class AnchorGeekPostListAdapter extends BaseRvAdapter<BossDeliveryGeekBean, BaseViewHolder> {

    public AnchorGeekPostListAdapter(List<BossDeliveryGeekBean> data) {
        super(R.layout.live_item_anchor_geek_post_list, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, BossDeliveryGeekBean bean) {
        if (bean == null) return;
        helper.setImageResource(R.id.iv_gender, ViewCommon.getGenderIcon(bean.gender));
        MTextView tvGeekName = helper.getView(R.id.tv_geek_name);
        tvGeekName.setText(bean.geekName);
        SimpleDraweeView sdvAvatar = helper.getView(R.id.sdv_avatar);
        sdvAvatar.setImageURI(bean.geekAvatar);

        MTextView tvTitleLabel = helper.getView(R.id.tv_title_label);
        tvTitleLabel.setVisibility(bean.invite==1 ? View.VISIBLE : View.GONE);

        MTextView tvSubTitleDesc = helper.getView(R.id.tv_sub_title_desc);
        String subTitle = StringUtil.connectTextWithChar("｜", bean.geekWorkYear, bean.geekDegree, bean.salary);
        tvSubTitleDesc.setText(subTitle, View.INVISIBLE);

        MTextView tvWorkDesc = helper.getView(R.id.tv_work_desc);
        tvWorkDesc.setText(bean.lastWorkDesc, View.INVISIBLE);
        MTextView tvWorkTime = helper.getView(R.id.tv_work_time);
        tvWorkTime.setText(bean.lastWorkTime, View.INVISIBLE);

        MTextView tvPositionDesc = helper.getView(R.id.tv_position_desc);
        if (!TextUtils.isEmpty(bean.jobName)) {
            tvPositionDesc.setText(String.format(Locale.getDefault(), "投递职位：%s", bean.jobName));
        } else {
            tvPositionDesc.setVisibility(View.GONE);
        }

        Button btnChange = helper.getView(R.id.btn_change);
        if (bean.deliverType == LiveConstants.GeekDeliverType.TYPE_PHONE || bean.deliverType == LiveConstants.GeekDeliverType.TYPE_WX) {
            if (bean.exchangeState == 0) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(true);
                btnChange.setText("同意交换");
            } else if (bean.exchangeState == 1) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(false);
                btnChange.setText("已交换");
            } else if (bean.exchangeState == 2) {
                btnChange.setVisibility(View.VISIBLE);
                btnChange.setEnabled(false);
                btnChange.setText("已拒绝");
            } else {
                btnChange.setVisibility(View.GONE);
            }
        } else {
            btnChange.setVisibility(View.GONE);
        }
    }
}
