package com.hpbr.bosszhipin.live.campus.audience.datacenter;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.CountDownTimer;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.arch.core.util.Function;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.Transformations;

import com.basedata.network.response.Get1004CityResponse;
import com.bszp.kernel.utils.SingleLiveEvent;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseApplication;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.live.bean.BaseMessageBean;
import com.hpbr.bosszhipin.live.bean.CommentItemBean;
import com.hpbr.bosszhipin.live.bean.InviteResumeItemBean;
import com.hpbr.bosszhipin.live.bean.LiveExplainJobBean;
import com.hpbr.bosszhipin.live.bean.LiveVoteBean;
import com.hpbr.bosszhipin.live.bean.SpeakVideoBean;
import com.hpbr.bosszhipin.live.boss.live.helper.GeekLiveDetailBatchRequestHelper;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.ClickApplaudModel;
import com.hpbr.bosszhipin.live.campus.audience.fragment.AudienceConfirmRegisterFragment;
import com.hpbr.bosszhipin.live.campus.audience.manager.BarrageMessageDispatcher;
import com.hpbr.bosszhipin.live.campus.audience.manager.LiveAudienceMsgDispatcher;
import com.hpbr.bosszhipin.live.campus.audience.model.ConfirmRegisterModel;
import com.hpbr.bosszhipin.live.campus.audience.model.DeliverSuccessModel;
import com.hpbr.bosszhipin.live.campus.audience.model.DeliverSuccessShowRecommendedModel;
import com.hpbr.bosszhipin.live.campus.audience.model.ExplainJobPlayModel;
import com.hpbr.bosszhipin.live.campus.audience.model.GeekDeliveryListModel;
import com.hpbr.bosszhipin.live.campus.audience.model.IntentionHitOptionModel;
import com.hpbr.bosszhipin.live.campus.audience.model.JobDeliveredListModel;
import com.hpbr.bosszhipin.live.campus.audience.model.ProxyBossJobModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.AnnouncementModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.AppointmentHighCompanyInfoModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.CommentModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.CourseSummaryModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.ExplainChapterModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.ExplainJobModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.GuideLiveModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.LuckyDrawCModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.MenuTabModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.SendMessageModel;
import com.hpbr.bosszhipin.live.campus.audience.moduel.SpecialChangeModel;
import com.hpbr.bosszhipin.live.campus.audience.view.DialogManager;
import com.hpbr.bosszhipin.live.campus.audience.viewmodel.AudienceDetailCallBack;
import com.hpbr.bosszhipin.live.campus.audience.weight.LivePlaceHolderView;
import com.hpbr.bosszhipin.live.campus.base.BaseDataCenter;
import com.hpbr.bosszhipin.live.campus.bean.VoteInfoBean;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.export.IntentKey;
import com.hpbr.bosszhipin.live.export.LiveSilenceRecord;
import com.hpbr.bosszhipin.live.export.LiveUrlConfig;
import com.hpbr.bosszhipin.live.geek.audience.bean.VoteCastOption;
import com.hpbr.bosszhipin.live.geek.audience.bean.VoteOptionBean;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.GiftModel;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.PosterShareModel;
import com.hpbr.bosszhipin.live.geek.audience.utils.AudienceUtils;
import com.hpbr.bosszhipin.live.model.LiveVoteInfoModel;
import com.hpbr.bosszhipin.live.model.PlayVideoModel;
import com.hpbr.bosszhipin.live.model.VoteTimerCountDownFinishModel;
import com.hpbr.bosszhipin.live.model.VoteTimerCountDownModel;
import com.hpbr.bosszhipin.live.net.bean.AtJobContentBean;
import com.hpbr.bosszhipin.live.net.bean.AtJobPositionBean;
import com.hpbr.bosszhipin.live.net.bean.BarrageBean;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.bean.FilterItemBean;
import com.hpbr.bosszhipin.live.net.bean.GiftBean;
import com.hpbr.bosszhipin.live.net.bean.IntentionScoreBean;
import com.hpbr.bosszhipin.live.net.bean.JobGroupBean;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.bean.LiveCVotePkBean;
import com.hpbr.bosszhipin.live.net.bean.LiveCVotePkOptionBean;
import com.hpbr.bosszhipin.live.net.bean.LiveProgrammeChapterBean;
import com.hpbr.bosszhipin.live.net.bean.LiveRecordBean;
import com.hpbr.bosszhipin.live.net.bean.LuckyDrawInfoBean;
import com.hpbr.bosszhipin.live.net.bean.RecommendJobListBean;
import com.hpbr.bosszhipin.live.net.bean.RecruitTopMessageBean;
import com.hpbr.bosszhipin.live.net.request.AudienceJobListBatchRequest;
import com.hpbr.bosszhipin.live.net.request.BossGetCampusLiveJobDetailRequest;
import com.hpbr.bosszhipin.live.net.request.BossSpeakRequest;
import com.hpbr.bosszhipin.live.net.request.CompanyHitGroupListRequest;
import com.hpbr.bosszhipin.live.net.request.EnterRoomRequest;
import com.hpbr.bosszhipin.live.net.request.GeekBarrageListRequest;
import com.hpbr.bosszhipin.live.net.request.GeekBrandSubscribeRequest;
import com.hpbr.bosszhipin.live.net.request.GeekGetJobDetailBatchRequest;
import com.hpbr.bosszhipin.live.net.request.GeekGetJobStatusRequest;
import com.hpbr.bosszhipin.live.net.request.GeekLiveSubscribeRequest;
import com.hpbr.bosszhipin.live.net.request.GeekLiveUnsubscribeRequest;
import com.hpbr.bosszhipin.live.net.request.GeekLuckyDrawRequest;
import com.hpbr.bosszhipin.live.net.request.GeekPlayBackQueryRequest;
import com.hpbr.bosszhipin.live.net.request.LiveBrandHomeRequest;
import com.hpbr.bosszhipin.live.net.request.LiveBrandPastLiveRequest;
import com.hpbr.bosszhipin.live.net.request.LiveExplainListRequest;
import com.hpbr.bosszhipin.live.net.request.LiveGiftListRequest;
import com.hpbr.bosszhipin.live.net.request.LiveGuideActivityColumnRequest;
import com.hpbr.bosszhipin.live.net.request.LiveLuckyDrawQuestionQueryRequest;
import com.hpbr.bosszhipin.live.net.request.LiveRecommendJobListRequest;
import com.hpbr.bosszhipin.live.net.request.LiveRecruitV2JobListRequest;
import com.hpbr.bosszhipin.live.net.request.LiveVoteListResponse;
import com.hpbr.bosszhipin.live.net.request.LiveVoteRequest;
import com.hpbr.bosszhipin.live.net.request.SharePosterQueryRequest;
import com.hpbr.bosszhipin.live.net.response.AudienceJobListBatchResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetDeliveryDetailsResponse;
import com.hpbr.bosszhipin.live.net.response.BossSpeakResponse;
import com.hpbr.bosszhipin.live.net.response.CompanyHitGroupListResponse;
import com.hpbr.bosszhipin.live.net.response.EmptyResponse;
import com.hpbr.bosszhipin.live.net.response.GeekBarrageListResponse;
import com.hpbr.bosszhipin.live.net.response.GeekDeliveryListResponse;
import com.hpbr.bosszhipin.live.net.response.GeekGetJobDetailBatchResponse;
import com.hpbr.bosszhipin.live.net.response.GeekHistoryMessagesResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLiveDetailBatchResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLiveGiftListResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLiveJobDeliverResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLiveSubscribeResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLiveUnsubscribeResponse;
import com.hpbr.bosszhipin.live.net.response.GeekLuckyDrawResponse;
import com.hpbr.bosszhipin.live.net.response.GeekOnlineResumeResponse;
import com.hpbr.bosszhipin.live.net.response.GeekPlayBackQueryResponse;
import com.hpbr.bosszhipin.live.net.response.GeekQuestionQueryResponse;
import com.hpbr.bosszhipin.live.net.response.GeekQuickQuestionQueryResponse;
import com.hpbr.bosszhipin.live.net.response.GeekRecommendJobCardResponse;
import com.hpbr.bosszhipin.live.net.response.GeekRecruitDetailResponse;
import com.hpbr.bosszhipin.live.net.response.GeekSpecialColumnLiveRecordListResponse;
import com.hpbr.bosszhipin.live.net.response.JobScoreOptionResponse;
import com.hpbr.bosszhipin.live.net.response.LiveAdditionalInfoResponse;
import com.hpbr.bosszhipin.live.net.response.LiveBrandHomeResponse;
import com.hpbr.bosszhipin.live.net.response.LiveBrandPastListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveExplainListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveExplainUrlQueryResponse;
import com.hpbr.bosszhipin.live.net.response.LiveGuideActivityColumnResponse;
import com.hpbr.bosszhipin.live.net.response.LiveJobDeliveredResponse;
import com.hpbr.bosszhipin.live.net.response.LiveJobOptionListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveLuckyDrawQuestionQueryResponse;
import com.hpbr.bosszhipin.live.net.response.LivePlaybackQueryResponse;
import com.hpbr.bosszhipin.live.net.response.LiveRecruitRecommendJobListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveRecruitRegisterResponse;
import com.hpbr.bosszhipin.live.net.response.LiveRecruitScoreListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveRecruitV2JobListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveSpecialSessionTagListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveSpecialTagListResponse;
import com.hpbr.bosszhipin.live.net.response.LiveVoteResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitCheckPreDeliverResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitDetailTabResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitExchangePreCheckResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitHeartBeatResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitTopMessageResponse;
import com.hpbr.bosszhipin.live.net.response.SharePosterQueryResponse;
import com.hpbr.bosszhipin.live.util.GlobalStatus;
import com.hpbr.bosszhipin.live.util.ICommonCallback;
import com.hpbr.bosszhipin.live.util.IExchangePreCheckCallback;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveCommonUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.live.util.LiveTimerUtil;
import com.hpbr.bosszhipin.live.util.MsgType;
import com.hpbr.bosszhipin.live.video.CampusRTCEngineHelper;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.player.BZPlayerHelper;
import com.hpbr.bosszhipin.player.ZPEventName;
import com.hpbr.bosszhipin.player.ZPViewRenderer;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.window.FloatWindowManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.sdk.nebulartc.constant.NebulaRtcDef;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GetBrandInfoRequest;
import net.bosszhipin.api.GetBrandInfoResponse;
import net.bosszhipin.api.GetJobDetailRequest;
import net.bosszhipin.api.GetJobQueryBannerRequest;
import net.bosszhipin.api.ResumeListResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.CustomApiRequestWithParamCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created by Qu Zhiyong on 2022/10/13
 */
public class AudienceDataCenter extends BaseDataCenter {
    private static final String TAG = "AudienceDataCenter";

    public ShareDataCenter shareDataCenter;

    public CampusRTCEngineHelper engineHelper;
    // 直播View
    @SuppressLint("StaticFieldLeak")
    public LivePlaceHolderView livePlaceHolderView;
    // 回放视频View
    @SuppressLint("StaticFieldLeak")
    public ZPViewRenderer zpViewRenderer;
    public BZPlayerHelper playerHelper;

    /* ---- activity入参 ---- */
    public String liveRecordId;
    public int source;// 进入直播间来源，0-默认未进直播间，1-小秘书，2-push，3-列表，7-h5分享页
    public int listSource;// 进入列表来源，0默认未进入列表，3-点击气泡进入列表，4-icon进入列表
    public boolean isProxyBoss;// 是否代播本场boss
    public int powerType;// 权限类型 -3: 同公司boss
    public LiveSpecialTagListResponse.TagBean targetSpecialTagBean;// 专题直播，点击右侧侧滑菜单上的直播间时，传进来的专题标签
    public String encryptRecommendJobId;// 点击专题直播侧边栏上的某场直播传进来的加密推荐职位id
    public int from;// 前一步从哪来的 1-PUSH，2-F1Banner，3-JD页，4-专题
    public boolean isFromBrand;// 是否是品牌页跳转过来的
    public boolean isFirstIntoRoomCloseJobWindow = true;//进入直播间默认弹起职位弹层，第一次关闭
    /* ---- activity入参 ---- */

    public MutableLiveData<GeekRecruitDetailResponse> mainLiveData = new MutableLiveData<>();
    public MutableLiveData<GeekRecruitDetailResponse> responseLiveData = new MutableLiveData<>();
    public MediatorLiveData<Integer> liveStatusLiveData = new MediatorLiveData<>();
    public MutableLiveData<GeekRecruitDetailResponse> roomInfoLiveData = new MutableLiveData<>();
    // 直播前tab初始化
    public MutableLiveData<GeekRecruitDetailResponse> appointmentTabLiveData = new MutableLiveData<>();
    // 切换直播间/直播间状态切换
    public MutableLiveData<Integer> changeDisplayFragmentLiveData = new SingleLiveEvent<>();
    // 切换PPT
    public MutableLiveData<String> changePPTLiveData = new MutableLiveData<>();
    // 网络质量变化
    public MutableLiveData<Integer> networkQuality = new MutableLiveData<>();
    // 直播间订阅
    public MutableLiveData<GeekRecruitDetailResponse> subscribeLiveData = new SingleLiveEvent<>();
    // 直播间列表订阅状态刷新
    public MutableLiveData<Boolean> refreshSubscribeLiveData = new SingleLiveEvent<>();
    // 互动组件数据
    public MutableLiveData<GeekRecruitDetailResponse> interactLiveData = new MutableLiveData<>();
    // 互动列表数据
    public MutableLiveData<CommentModel> commentModelLiveData = new MutableLiveData<>();
    // 工具栏浮层MenusFragment中tab变化
    public MutableLiveData<MenuTabModel> menuTabModelLiveData = new MutableLiveData<>(new MenuTabModel());
    // 只用于更新单个数据
    public MutableLiveData<JobInfoBean> positionNotifyLiveData = new MutableLiveData<>();
    // 章节时间打点
    public MutableLiveData<LiveProgrammeChapterBean> chapterMarkLiveData = new SingleLiveEvent<>();
    // 职位组时间打点
    public MutableLiveData<JobGroupBean> jobGroupMarkLiveData = new SingleLiveEvent<>();
    // 公告数据
    public MutableLiveData<AnnouncementModel> announcementLiveData = new MutableLiveData<>();
    // 「回复我」数据
    public MutableLiveData<List<String>> replyMeLiveData = new MutableLiveData<>();
    // 带播boss职位列表数据
    public MutableLiveData<ProxyBossJobModel> proxyBossJobListLiveData = new MutableLiveData<>();
    // 发送消息
    public MutableLiveData<SendMessageModel> sendCommentLiveData = new SingleLiveEvent<>();
    // 礼物列表数据
    public MutableLiveData<GiftModel> giftLiveData = new SingleLiveEvent<>();
    // 显示礼物弹框
    public MutableLiveData<Boolean> showGiftDialogLiveData = new SingleLiveEvent<>();
    // 显示「心动值+」的弹框
    public MutableLiveData<IntentionScoreBean> showIntentionValuePlusLiveData = new SingleLiveEvent<>();
    // 获取「心动榜」列表数据成功
    public MutableLiveData<LiveRecruitScoreListResponse> getIntentionListLiveData = new SingleLiveEvent<>();
    // 意向值打榜选项列表数据
    public MutableLiveData<IntentionHitOptionModel> intentionHitOptionLiveData = new SingleLiveEvent<>();
    // 公司打投选项列表数据
    public MutableLiveData<IntentionHitOptionModel> companyHitOptionLiveData = new SingleLiveEvent<>();
    // 抽奖数据
    public MutableLiveData<LuckyDrawCModel> luckyDrawLiveData = new SingleLiveEvent<>();
    // 抽奖准备中倒计时，用于同步小宝箱倒计时和点击后浮层的倒计时
    public MutableLiveData<Integer> luckyDrawPrepareLiveData = new MutableLiveData<>();
    // 朋友圈海报数据
    public MutableLiveData<PosterShareModel> posterShareLiveData = new MutableLiveData<>();
    // 回看职位
    public MutableLiveData<ExplainJobModel> explainJobLiveData = new MutableLiveData<>();
    // 回看章节
    public MutableLiveData<ExplainChapterModel> explainChapterLiveData = new MutableLiveData<>();
    // 直播中看「讲解切片」视频
    public MutableLiveData<ExplainJobPlayModel> explainSliceVideoLiveData = new SingleLiveEvent<>();
    // 置顶章节
    public MutableLiveData<LiveProgrammeChapterBean> topChapterLiveData = new SingleLiveEvent<>();
    // 专题内有改变的直播间
    public MutableLiveData<SpecialChangeModel> specialChangeLiveData = new SingleLiveEvent<>();
    // 心动榜弹窗 tab切换
    public MutableLiveData<Integer> intentionTabChange = new SingleLiveEvent<>();
    // 关注品牌气泡
    public MutableLiveData<String> attentionPopLiveData = new SingleLiveEvent<>();
    // 课程纪要
    public MutableLiveData<CourseSummaryModel> courseSummaryLiveData = new MutableLiveData<>();
    //导播间
    public MutableLiveData<GuideLiveModel> guideLiveLiveData = new MutableLiveData<>();
    // 播放视频
    public MutableLiveData<PlayVideoModel> playVideoLiveData = new MutableLiveData<>();
    // 直播切片播放下一个视频
    public MutableLiveData<Boolean> playNextVideoLiveData = new MutableLiveData<>();
    // 直播切片暂停播放
    public MutableLiveData<Boolean> pauseVideoLiveData = new MutableLiveData<>();
    // 高级直播间公司介绍
    public MutableLiveData<AppointmentHighCompanyInfoModel> highCompanyLiveData = new SingleLiveEvent<>();
    // 高级直播间分页加载
    public MutableLiveData<LiveBrandPastListResponse> highCompanyMoreLiveData = new SingleLiveEvent<>();
    // 普通直播间公司介绍
    public MutableLiveData<GetBrandInfoResponse> normalCompanyLiveData = new SingleLiveEvent<>();
    // 直播前公司简介列表，分页加载错误
    public MutableLiveData<ErrorReason> companyErrorLiveData = new SingleLiveEvent<>();
    //求讲解
    public MutableLiveData<GeekQuestionQueryResponse> explainQuestionResponseLiveData = new SingleLiveEvent<>();
    //预设弹幕「投递前/投递后预设问题」
    public MutableLiveData<GeekQuickQuestionQueryResponse> questionResponseLiveData = new SingleLiveEvent<>();
    // 直播附加信息
    public MutableLiveData<LiveAdditionalInfoResponse> additionalInfoLiveData = new MutableLiveData<>();
    // 开始投票信息渲染
    public MutableLiveData<VoteInfoBean> beginVoteInfoLiveData = new MutableLiveData<>();
    // 更新投票
    public MutableLiveData<LiveVoteInfoModel> updateVoteCountLiveData = new MutableLiveData<>();
    //pk新增投票更新投票列表
    public MutableLiveData<LiveVoteListResponse.VoteBean> addPkVoteLiveData = new MutableLiveData<>();
    // 投票本地倒计时
    public MutableLiveData<VoteTimerCountDownModel> voteTimerLiveData = new SingleLiveEvent<>();
    // 投票本地倒计时结束
    public MutableLiveData<VoteTimerCountDownFinishModel> voteTimerFinishLiveData = new SingleLiveEvent<>();
    // 隐藏职位弹层筛选的项列表的PopupWindow
    public MutableLiveData<Boolean> dismissJobFilterPopupWindow = new SingleLiveEvent<>();
    // 导播间活动专栏
    public MutableLiveData<LiveGuideActivityColumnResponse> guideActivityColumn = new SingleLiveEvent<>();
    // 接收到讲解切片已生成
    public MutableLiveData<LiveExplainJobBean> explainCreatedLiveData = new MutableLiveData<>();
    // 本场投递列表
    public SingleLiveEvent<GeekDeliveryListModel> deliverListLiveData = new SingleLiveEvent<>();
    // 本场投递列表「同公司boss」
    public SingleLiveEvent<GeekDeliveryListModel> deliverCompanyColleagueListLiveData = new SingleLiveEvent<>();
    // 获取已投递职位列表数据
    public MutableLiveData<JobDeliveredListModel> jobDeliveredListModelLiveData = new SingleLiveEvent<>();
    // 点赞数 每5s同步一次
    public MutableLiveData<ClickApplaudModel> clickApplaudLiveData = new SingleLiveEvent<>();
    // 显示boss回复我气泡
    public MutableLiveData<Boolean> showBossDeliverReplyLiveData = new SingleLiveEvent<>();
    // 投票列表
    public MutableLiveData<LiveVoteListResponse> voteList = new MutableLiveData<>();
    // 刷新直播间横竖屏布局
    public MutableLiveData<Boolean> refreshScreenOrientationLiveData = new MutableLiveData<>();
    // 邀请投递
    public MutableLiveData<InviteResumeItemBean> inviteResumeLiveData = new SingleLiveEvent<>();
    // 牛人在线简历
    public SingleLiveEvent<GeekOnlineResumeResponse> geekResumeLiveData = new SingleLiveEvent<>();
    // 触发投递简历
    public MutableLiveData<JobInfoBean> deliverLiveData = new SingleLiveEvent<>();
    // 投递简历异常
    public MutableLiveData<GeekLiveJobDeliverResponse> deliverErrorLiveData = new SingleLiveEvent<>();
    // 投递简历成功（多个地方监听）
    public MutableLiveData<DeliverSuccessModel> deliverSuccessLiveData = new MutableLiveData<>();
    public MutableLiveData<DeliverSuccessModel> sendSuccessLiveData = new MutableLiveData<>();
    public MutableLiveData<DeliverSuccessModel> sendSuccessExplainLiveData = new MutableLiveData<>();
    public MutableLiveData<DeliverSuccessShowRecommendedModel> deliverSuccessRecommendedSingleLiveData = new SingleLiveEvent<>();
    // 一键投递后刷新职位列表
    public MutableLiveData<List<RecommendJobListBean>> positionListUpdateLiveData = new MutableLiveData<>();
    // 职位列表数据
    public MutableLiveData<GeekRecruitDetailResponse> positionLiveData = new MutableLiveData<>();
    public MutableLiveData<LiveRecruitRecommendJobListResponse> recommendTagJobListSheetLiveData = new MutableLiveData<>();
    // 置顶职位气泡
    public LiveData<Long> positionPopLiveData = Transformations.map(positionLiveData, new Function<GeekRecruitDetailResponse, Long>() {
        @Override
        public Long apply(GeekRecruitDetailResponse response) {
            return response.topApplyJobId;
        }
    });
    // 置顶职位气泡数据-高级直播间
    public LiveData<String> positionHighPopLiveData = Transformations.map(positionLiveData, new Function<GeekRecruitDetailResponse, String>() {
        @Override
        public String apply(GeekRecruitDetailResponse response) {
            return response.topJobGroupId;
        }
    });
    // 直播详情Tab信息
    public MutableLiveData<RecruitDetailTabResponse> recruitDetailTabLiveData = new SingleLiveEvent<>();
    // 更新pk投票
    public MutableLiveData<LiveAdditionalInfoResponse> pkVoteLiveData = new MutableLiveData<>();
    // 刚刚看过或与你匹配或你关注过
    public MutableLiveData<GeekRecommendJobCardResponse> recommendJobLiveData = new MutableLiveData<>();
    //推职位
    public MutableLiveData<CommentItemBean> recommendBossJobLiveData = new MutableLiveData<>();
    // 播放视频
    public MutableLiveData<LiveExplainUrlQueryResponse> playVideoLiveData2 = new MutableLiveData<>();
    public MutableLiveData<LiveExplainListResponse> explainListLiveData = new SingleLiveEvent<>();
    //报名信息查询
    public MutableLiveData<LiveRecruitRegisterResponse> recruitRegisterLiveData = new MutableLiveData<>();
    //城市信息
    public MutableLiveData<Get1004CityResponse> recruitRegisterCityLiveData = new MutableLiveData<>();
    //确认报名弹框关闭，需要把职位弹框或者详情弹层一起关闭
    public MutableLiveData<Boolean> closeJobPopLiveData = new MutableLiveData<>();
    //刷新更多按钮
    public MutableLiveData<Boolean> refreshBtnMoreLiveData = new SingleLiveEvent<>();

    //职位卡是否在显示状态中 「刚刚看过或与你匹配或你关注过」
    public boolean isHotJobShowing = false;
    /*信令处理工具类*/
    private final LiveAudienceMsgDispatcher msgDispatcher = new LiveAudienceMsgDispatcher(this);

    /*直播回放弹幕处理工具类*/
    public final BarrageMessageDispatcher barrageDispatcher = new BarrageMessageDispatcher();

    /*投票气泡计时*/
    private CountDownTimer votePopTimer;

    // 视频流开始渲染计时
    private ScheduledFuture<?> scheduledStreamFuture;
    // 直播开始计时
    private ScheduledFuture<?> scheduledLiveFuture;
    // 对用户进入直播间后（直播或回放），用于埋点
    private ScheduledFuture<?> scheduledEnterLiveFuture;
    public LiveTimerUtil timerUtil = new LiveTimerUtil();

    public GeekRecruitDetailResponse detailResponse;// 直播间详情
    public LiveJobOptionListResponse liveJobOptionListResponse;// 职位筛选项列表数据
    /*「直播切片」列表数据，直播中不再取直播切片列表数据（为了防止直播结束后，自动播放切片视频）*/
    public LiveExplainListResponse liveExplainListResponse;
    public LiveSpecialTagListResponse specialTagListResponse;
    public LiveSpecialSessionTagListResponse specialSessionTagListResponse;
    public LiveAdditionalInfoResponse additionalInfoResponse;// 直播附加信息

    // 简历列表数据
    public ResumeListResponse resumeListResponse;
    // 互动列表所有数据
    public CommentModel commentModel = new CommentModel();
    // 答题抽奖 问题&选项
    public LiveLuckyDrawQuestionQueryResponse luckyDrawQuestionResponse;

    // 职位列表fragment第一次可见
    public boolean normalJobListFirstLoad;
    // 企业宣传视频正在播放
    public boolean openingVideoPlaying;
    // PPT在主窗口还是小窗口
    public boolean pptInMainWindow = true;
    // 详情接口数据返回的时间
    public long requestDetailSuccessTimestamp;
    // 纯净模式显示的时间
    public long pureModeDisplayTime;
    // 视频方向
    public boolean videoLandScape;
    // 视频方向是否已设置过，兼容连麦和小窗进入
    private boolean videoLandScapeHasSet;

    // 置顶职位高级直播间、置顶职位普通直播间是否已经显示过，为了防止重复出卡
    public boolean isShowedTopHighPop = false;
    public boolean isShowedTopPop = false;
    public boolean isFirstIntoWhenShowJobPop = false;
    // 是否已经发送过消息
    public boolean isHasSendMsg = false;
    //「直播切片」讲解id
    public String speakExplainId;
    // 正在回看中的职位
    public JobInfoBean explainingJob;
    // 正在回看中的章节
    public LiveProgrammeChapterBean explainingChapter;
    /*视频播放模式，默认模式是播放完整回放视频*/
    @CampusConstant.VideoPlayBackMode
    public int videoPlayBackMode = CampusConstant.VideoPlayBackMode.MODE_COMPLETE_PLAYBACK;

    public int disableSmallWindow;//是否禁止使用小窗
    private Runnable downLoadGifMaxRunnable;
    public boolean isVotedPk = false;//本地字段 记录是否已经投票过了
    public List<LiveVoteListResponse.VoteBean> voteListByFrag = new ArrayList<>();//c投票列表中所有投票数组，为了区分pk投票信令中是否存在新增

    public boolean needToastAutoSilence = false;
    public boolean isLiveToFinish = false;//是否是在直播中到直播结束
    public boolean isSeePlayBackShowing = false;//是否在看回放
    public boolean isFirstDeliverSuccess = true;
    public boolean isPositionJobCardDeliver = false;

    public ConfirmRegisterModel confirmRegisterModel;//蓝领报名使用，临时
    public LuckyDrawCModel luckyDrawCModel;//c抽奖弹窗使用，临时
    public AnnouncementModel announcementModel;//c公告弹窗使用，临时
    public List<String> replyMeList = new ArrayList<>();//c回复我的使用，临时

    public boolean initData() {
        if (shareDataCenter.intent == null) return false;
        liveRecordId = shareDataCenter.intent.getStringExtra(IntentKey.INTENT_LIVE_RECORD_ID);
        source = shareDataCenter.intent.getIntExtra(IntentKey.INTENT_LIVE_SOURCE, 0);
        listSource = shareDataCenter.intent.getIntExtra(IntentKey.INTENT_LIVE_LIST_SOURCE, 0);
        isProxyBoss = shareDataCenter.intent.getBooleanExtra(IntentKey.INTENT_LIVE_IS_PROXY_BOSS, false);
        encryptRecommendJobId = shareDataCenter.intent.getStringExtra(IntentKey.INTENT_LIVE_ENCRYPT_RECOMMEND_JOBID);
        from = shareDataCenter.intent.getIntExtra(IntentKey.INTENT_LIVE_LIST_FROM, 0);
        isFromBrand = shareDataCenter.intent.getBooleanExtra(IntentKey.INTENT_LIVE_IS_FROM_BRAND, false);
        speakExplainId = shareDataCenter.intent.getStringExtra(IntentKey.INTENT_LIVE_ENCRYPT_EXPLAIN_ID);
        disableSmallWindow = shareDataCenter.intent.getIntExtra(IntentKey.INTENT_LIVE_DISABLE_SMALL_WINDOW, 0);
        int muteVoice = shareDataCenter.intent.getIntExtra(IntentKey.INTENT_LIVE_MUTE_VOICE, 0);
        if (muteVoice == 1 && !UserManager.isBossRole()) {
            LiveSilenceRecord.get().forceChangeSilence(true);
            needToastAutoSilence = true;
        }
        return !TextUtils.isEmpty(liveRecordId);
    }

    public void resetMuteSilence() {
        if (shareDataCenter.intent == null) return;
        if (!needToastAutoSilence) return;
        shareDataCenter.intent.putExtra(IntentKey.INTENT_LIVE_MUTE_VOICE, 0);
        needToastAutoSilence = false;
    }

    public void onCreate(String liveRecordId) {
        this.liveRecordId = liveRecordId;
        requestDetail();

        liveStatusLiveData.removeSource(responseLiveData);
        liveStatusLiveData.addSource(responseLiveData, new Observer<GeekRecruitDetailResponse>() {
            @Override
            public void onChanged(GeekRecruitDetailResponse response) {
                liveStatusLiveData.setValue(response.liveState);
            }
        });
    }

    public void enterRoom() {
        TLog.info(TAG, "joinRoom liveRecordId = %s", liveRecordId);
        if (engineHelper != null && !engineHelper.isJoinRoom()) {
            engineHelper.enterRoom();
        }
    }

    /**
     * 获取直播详情
     */
    public void requestDetail() {
        if (shareDataCenter.fromSmallWindow || shareDataCenter.currentDataCenter == null) {
            // 首次网络请求使用ShareDataCenter中的response，屏蔽多余的第二次请求
            initResp(null);
            return;
        }
        encryptRecommendJobId = shareDataCenter.getCurrentEncryptRecommendJobId();
        int formByAdditional = 0;
        if (UserManager.isGeekRole()) {
            formByAdditional = shareDataCenter.isFirstRequest() ? 0 : 1;
            shareDataCenter.requestCountAdd();
        }
        new GeekLiveDetailBatchRequestHelper()
                .setEncryptRecommendJobId(encryptRecommendJobId)
                .setFrom(from)
                .setFormByAdditional(formByAdditional)
                .requestLiveDetail(liveRecordId, source, listSource, shareDataCenter.liveRecordId, new AudienceDetailCallBack<GeekLiveDetailBatchResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(GeekLiveDetailBatchResponse resp) {
                        hideProgress();
                        initResp(resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        hideProgress();
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
    }

    private void initResp(GeekLiveDetailBatchResponse resp) {
        if (resp != null) {
            detailResponse = UserManager.isGeekRole() ? resp.recruitDetailResponse : resp.bossAudienceRecruitDetailResponse;
            if (detailResponse == null) return;

            liveJobOptionListResponse = resp.liveJobOptionListResponse;
            liveExplainListResponse = !detailResponse.isLiving() ? resp.liveExplainListResponse : null;
            additionalInfoResponse = UserManager.isGeekRole() ? resp.liveAdditionalInfoResponse : resp.bossAudienceLiveAdditionalInfoResponse;

            if (detailResponse.isHighLevelRoom()) {// 高级直播间

                /*1015高级直播间职位列表-刚进入直播间弹热招职位使用*/
                if (null != resp.liveRecruitV2JobListResponse) {
                    detailResponse.liveRecruitV2JobListResponse = resp.liveRecruitV2JobListResponse;
                }
                /*1209.715【直播】代播直播间职位列表调整 增加推荐职位列表*/
                detailResponse.recommendJobListResponse = resp.recommendJobListResponse;
            }

            /*抽奖信息在直播附加信息返回，不在直播间详情中返回，需要重新赋值*/
            if (null != additionalInfoResponse && null != additionalInfoResponse.luckyDrawInfo) {
                detailResponse.luckyDrawInfo = additionalInfoResponse.luckyDrawInfo;
            }
            //普通直播间，正在讲解id赋值
            if (detailResponse.isNormalLevelRoom() && null != additionalInfoResponse) {
                detailResponse.topApplyJobId = additionalInfoResponse.explainRecordJobId;
            }
            specialTagListResponse = UserManager.isGeekRole() ? resp.liveSpecialTagListResponse : resp.bossLiveSpecialTagListResponse;

            if (detailResponse.isBigBlueRoom()) {
                specialSessionTagListResponse = resp.liveSpecialSessionTagListResponse;
            }
        } else {
            detailResponse = shareDataCenter.detailResponse;
            liveJobOptionListResponse = shareDataCenter.liveJobOptionListResponse;
            liveExplainListResponse = shareDataCenter.liveExplainListResponse;
            specialTagListResponse = shareDataCenter.specialTagListResponse;
            specialSessionTagListResponse = shareDataCenter.specialSessionTagListResponse;
            additionalInfoResponse = shareDataCenter.additionalInfoResponse;
        }

        if (detailResponse == null) {
            ToastUtils.showText("数据异常，请退出后重试");
            return;
        }
        /*上下滑动，需要用此更新直播间ID，否则退出小窗，重新进来，是上次的直播间id*/
        liveRecordId = detailResponse.liveRecordId;

        if (!videoLandScapeHasSet) {
            videoLandScapeHasSet = true;
            videoLandScape = detailResponse.isLandscapeStream();
        }

        requestDetailSuccessTimestamp = System.currentTimeMillis();

        pkVoteLiveData.postValue(additionalInfoResponse);

        mainLiveData.postValue(detailResponse);
        additionalInfoLiveData.postValue(additionalInfoResponse);

        shareDataCenter.onCreateLive(this);
    }

    public void initLiveData() {
        // 抽奖状态
        if (detailResponse.luckyDrawInfo != null && detailResponse.luckyDrawInfo.isLuckyPreparingOrDrawing()) {
            int showType = detailResponse.luckyDrawInfo.leftLuckyDrawTime > 0 && detailResponse.luckyDrawInfo.isAutoShowBigBox && detailResponse.luckyDrawInfo.isLuckyDrawing()
                    ? LuckyDrawCModel.TYPE_POP : LuckyDrawCModel.TYPE_BOX;
            luckyDrawCModel = new LuckyDrawCModel(showType, detailResponse.luckyDrawInfo);
            luckyDrawLiveData.postValue(luckyDrawCModel);
        }
        // 房间信息
        roomInfoLiveData.postValue(this.detailResponse);
        // 更新公告
        announcementModel = new AnnouncementModel(detailResponse.noticeContent, detailResponse.noticeContentImageList);
        announcementLiveData.postValue(announcementModel);
        // 广告位
        interactLiveData.postValue(detailResponse);
        // 置顶章节
        if (additionalInfoResponse != null && !TextUtils.isEmpty(additionalInfoResponse.encryptMarkExplainProgrammeChapterId)) {
            LiveProgrammeChapterBean topChapterBean = LiveCommonUtil.findChapter(additionalInfoResponse, additionalInfoResponse.encryptMarkExplainProgrammeChapterId);
            topChapterLiveData.postValue(topChapterBean);
        }
        // 职位信息
        positionLiveData.postValue(detailResponse);
        //直播前tab
        appointmentTabLiveData.postValue(this.detailResponse);

        // -1代表从接口传过来的
        showIntentionValuePlusLiveData.postValue(new IntentionScoreBean(-1, detailResponse.sumScore));

        handleShowMore(false);
    }

    public void requestOtherData() {
        if (!detailResponse.isFinishLive()) {
            // 置顶消息
            requestTopMessage();
            // 代播Boss获取简历投递情况
            if (isProxyBoss) {
                getDeliveryDetails(null);
            }

            if (detailResponse.isPreLive()) {
                heartBeat();
            }

            // 牛人身份 && 普通直播间 && 不是代播boss
            if (UserManager.isGeekRole() && !detailResponse.isHighLevelRoom() && !isProxyBoss) {
                queryQuestion();
            }
        }

        /*请求礼物列表数据*/
        getGiftList();
    }

    /**
     * peerId playMode = 2时  不是 真正的 peerId
     */
    public void initEngine(@NonNull Runnable runnable) {
        TLog.info(TAG, "asyncInitSDK init liveRecordId = %s liveRoomId = %s", detailResponse.liveRecordId, detailResponse.liveRoomId);
        if (engineHelper != null) {
            if (TextUtils.equals(detailResponse.liveRoomId, engineHelper.getLiveRoomId()) && TextUtils.equals(detailResponse.nebulaId, engineHelper.getNebulaId())) {
                TLog.debug(TAG, "asyncInitSDK save liveRoomId");
                if (runnable != null) {
                    runnable.run();
                }
                return;
            } else { //退出房间 但是不销毁 RTC#SDK 防止销毁是异步的， 销毁不匹配
                leaveSimpleRoom();
            }
        }
        showProgress();
        if (detailResponse.nebulaSdkInfo != null && detailResponse.nebulaSdkInfo.rtc != null) {
            engineHelper = new CampusRTCEngineHelper(App.get(), detailResponse.nebulaSdkInfo,
                    detailResponse.liveRoomId, detailResponse.anchorUser.userId, detailResponse.playModel);
            engineHelper.setListener(new CampusRTCEngineHelper.CampusRTCListener(engineHelper) {
                @Override
                public void onEnterRoom(String selfId, long result) {
                    hideProgress();
                    if (result >= 0) {
                        if (engineHelper != null) {
                            engineHelper.initIMSDK();
                        }
                        if (runnable != null) {
                            runnable.run();
                        }
                        enterRoom(1);
                    } else {
                        ToastUtils.showText("当前网络环境较差，请退出后重新进入");
                    }
                }

                @Override
                public void onUserVideoAvailable(String peerId, boolean available) {
                    if (available) {
                        startStreamTimer();
                        getLivePlaceHolderView().renderLiveView(getLiveView());
                        notifyLiveState(LiveState.PUBLISHING);
                    } else {
                        notifyLiveState(LiveState.PAUSED);
                    }
                    responseLiveData.postValue(detailResponse);
                }

                @Override
                public void onFirstVideoFrame(String userId, int streamType, int width, int height) {
                    super.onFirstVideoFrame(userId, streamType, width, height);
                    boolean mVideoLandScape = width > height;
                    if (videoLandScape != detailResponse.isLandscapeStream()
                            || videoLandScape != mVideoLandScape) {
                        videoLandScape = mVideoLandScape;// 视频流方向
                        responseLiveData.postValue(detailResponse);
                    }
                    // 1214.972 统计观众从进入直播间到看到首帧画面的时间，直播中首帧渲染埋点，已打点验证，回放不会再经过这里
                    if (detailResponse != null) {
                        if (detailResponse.isLiving()) {
                            LiveAnalysisUtil.dotCampusLivePlayLoad(liveRecordId, requestDetailSuccessTimestamp, System.currentTimeMillis(), 1);
                        }
                    }

                }

                @Override
                public void onRemoteUserLeaveRoom(String peerId) {
                    notifyLiveState(LiveState.PAUSED);
                }

                @Override
                public void onNetworkQuality(NebulaRtcDef.NebulaRtcQuality rtcQuality) {
                    super.onNetworkQuality(rtcQuality);
                    networkQuality.postValue(rtcQuality.quality);
                    if (rtcQuality.quality >= NebulaRtcDef.NEBULA_RTC_NETWORK_QUALITY_VBAD) {
                        if (detailResponse.liveState == LiveState.PUBLISHING) {
                            ToastUtils.showText("网络状况存在波动");
                        }
                        getLivePlaceHolderView().showLoading();
                    } else if (rtcQuality.quality >= NebulaRtcDef.NEBULA_RTC_NETWORK_QUALITY_BAD) {
                        if (detailResponse.liveState == LiveState.PUBLISHING) {
                            ToastUtils.showText("网络状况存在波动");
                        }
                        getLivePlaceHolderView().hideLoading();
                    } else {
                        getLivePlaceHolderView().hideLoading();
                    }
                }

                @Override
                public void onCustomMessage(String groupId, String senderId, int type, String message) {
                    BaseMessageBean baseBean = BaseMessageBean.parseServerMessage(message);
                    if (baseBean == null || engineHelper == null || !TextUtils.equals(engineHelper.getLiveRoomId(), baseBean.liveRoomId)) {
                        TLog.error(TAG, "onCustomMessage 消息串了 recruitDetailResponse.liveRoomId = %s, liveRoomId =%s",
                                engineHelper != null ? engineHelper.getLiveRoomId() : "null", baseBean == null ? "null" : baseBean.liveRoomId);
                        return;
                    }
                    super.onCustomMessage(groupId, senderId, type, message);
                    msgDispatcher.dispatcherMessage(baseBean.msgType, message);
                }

                @Override
                public void onError(int errCode, String errMsg) {
                    ApmAnalyzer.create().action("CampusLive", "onError").p2("errCode=" + errCode + " errMsg=" + errMsg).report();
                    ToastUtils.showText(errMsg);
                }

                @Override
                public void onIMError(int errCode, String errMsg) {
                    ToastUtils.showText("加载失败，请退出后重新进入直播间");
                }
            });
            engineHelper.startInitSDK();
            enterRoom();
        } else {
            Utils.runOnUiThreadDelayed(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showText("加载失败，请退出后重新进入直播间");
                }
            }, 50);
        }
    }

    public void requestGuideLiveList() {
        SimpleApiRequest.GET(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_COLUMN_LIST : LiveUrlConfig.URL_BOSS_RECRUIT_COLUMN_LIST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("encryptLiveSpecialColumnRecordId", detailResponse.encryptLiveSpecialColumnRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekSpecialColumnLiveRecordListResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekSpecialColumnLiveRecordListResponse> data) {
                        performGuideLiveData(data.resp);
                    }
                }).execute();
    }

    public void requestSpecialBatchSubscribe(String liveRecordIds) {
        if (TextUtils.isEmpty(liveRecordIds)) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_RECRUIT_BATCH_SUBSCRIBE)
                .addParam("liveRecordIds", liveRecordIds)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekSpecialColumnLiveRecordListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekSpecialColumnLiveRecordListResponse> data) {
                        GuideLiveModel guideLiveModel = guideLiveLiveData.getValue();
                        if (guideLiveModel != null) {
                            LiveCommonUtil.updateSubscribed(liveRecordIds, guideLiveModel.liveRecordList);
                            guideLiveLiveData.setValue(guideLiveModel);
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 进出直播间
     *
     * @param type 1代表进入；0代表出去
     */
    public void enterRoom(int type) {
        EnterRoomRequest request = new EnterRoomRequest(null, false);
        request.type = type;
        request.liveRecordId = liveRecordId;
        request.execute();
    }

    public void sendWechat(JobInfoBean jobInfoBean) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_ZPGEE_SENDWECHAT)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .addParam("groupId", jobInfoBean.encryptJobGroupId)
                .addParam("source", source)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekLiveJobDeliverResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekLiveJobDeliverResponse> data) {
                        if (data.resp == null) return;
                        if (data.resp.dialog == null) { // 投递成功
                            sendSuccessLiveData.postValue(new DeliverSuccessModel(jobInfoBean, data.resp));
                            //讲解视频自己使用
                            sendSuccessExplainLiveData.postValue(new DeliverSuccessModel(jobInfoBean, data.resp));
                            ToastUtils.showText("发送成功");
                            //刷新职位卡
                            updateJobInfo(jobInfoBean);
                        } else {
                            deliverErrorLiveData.postValue(data.resp);
                        }
                    }
                })
                .execute();
    }

    public void sendPhone(JobInfoBean jobInfoBean) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_ZPGEE_SENDPHONE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .addParam("groupId", jobInfoBean.encryptJobGroupId)
                .addParam("source", source)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekLiveJobDeliverResponse>() {

                    @Override
                    public void onSuccess(ApiData<GeekLiveJobDeliverResponse> data) {
                        if (data.resp == null) return;
                        if (data.resp.dialog == null) { // 投递成功
                            sendSuccessLiveData.postValue(new DeliverSuccessModel(jobInfoBean, data.resp));
                            //讲解视频自己使用
                            sendSuccessExplainLiveData.postValue(new DeliverSuccessModel(jobInfoBean, data.resp));
                            ToastUtils.showText("发送成功");
                            //刷新职位卡
                            updateJobInfo(jobInfoBean);
                        } else {
                            deliverErrorLiveData.postValue(data.resp);
                        }
                    }
                })
                .execute();
    }


    /**
     * 投递职位
     */
    public void deliver(JobInfoBean jobInfoBean, long resumeId, boolean isShowingJobBottomSheetDialog) {
        if (jobInfoBean == null) return;
        String source = LiveAnalysisUtil.getSourceByLabelType(jobInfoBean.labelType);
        if (jobInfoBean.isExplain) {
            source = CampusConstant.ClickJumpJobDetailFrom.FROM_EXPLAIN_CARD;
        }
        //蓝领如果选择了报名信息，优先级最高，如果没选走历史逻辑
        if (detailResponse.isBigBlueRoom() && null != confirmRegisterModel) {
            source = CampusConstant.ClickJumpJobDetailFrom.FROM_BLUE_COLLAR_LIVE;
        }

        SimpleApiRequest request = SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_DELIVER)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .addParam("resumeId", resumeId)
                .addParam("groupId", jobInfoBean.encryptJobGroupId)
                .addParam("source", source);

        if (detailResponse.isBigBlueRoom() && null != confirmRegisterModel) {
            if (confirmRegisterModel.selectProvinceCode > 0) {
                request.addParam("provinceCode", confirmRegisterModel.selectProvinceCode);
            }
            if (confirmRegisterModel.selectCityCode > 0) {
                request.addParam("cityCode", confirmRegisterModel.selectCityCode);
            }
            //选择了不限的话，传-1
            request.addParam("districtCode", confirmRegisterModel.selectDistrictCode);
        }
        request.setRequestCallback(new SimpleCommonApiRequestCallback<GeekLiveJobDeliverResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void handleInChildThread(ApiData<GeekLiveJobDeliverResponse> data) {
                super.handleInChildThread(data);
                if (data.resp == null) return;
                if (data.resp.dialog == null) { // 投递成功
                    jobInfoBean.delivered = true;
                    updateJobInfo(jobInfoBean);

                    if (detailResponse.deliverNumLimit > 0 &&
                            detailResponse.deliveredNum >= detailResponse.deliverNumLimit) {
                        detailResponse.canDeliver = false;
                    } else {
                        detailResponse.canDeliver = true;
                    }
                    detailResponse.deliveredNum += 1;

                    positionLiveData.postValue(detailResponse);
                    //蓝领如果选择了报名信息，把职位弹层也关闭了
                    if (detailResponse.isBigBlueRoom() && null != confirmRegisterModel) {
                        closeJobPopLiveData.postValue(true);
                    }

                    GiftModel model = giftLiveData.getValue();
                    if (model != null && !LList.isEmpty(model.giftList)) {
                        for (GiftBean bean : model.giftList) {
                            if (bean != null && bean.unlockConditions == GiftBean.UNLOCK_SEND_RESUME) {
                                bean.lock = false;
                                if (!TextUtils.isEmpty(liveRecordId)) {
                                    /*更新当前礼物状态为已解锁状态*/
                                    SpManager.get().user().edit().putBoolean(CampusConstant.KEY_SP_GIFT_IS_UNLOCK + liveRecordId + "_" + bean.giftId, true).apply();
                                }
                                break;
                            }
                        }
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<GeekLiveJobDeliverResponse> data) {
                if (data.resp == null) return;
                if (data.resp.dialog == null) { // 投递成功
                    deliverSuccessLiveData.postValue(new DeliverSuccessModel(jobInfoBean, data.resp));

                    //代播投递成功页逻辑
                    if (isFirstDeliverSuccess && detailResponse.isProxyLiveRoom() && isShowingJobBottomSheetDialog) {
                        //第一次投递成功显示
                        isFirstDeliverSuccess = false;
                        getDeliverSuccessRecommendedList(jobInfoBean, () -> ToastUtils.showText(detailResponse.isBigBlueRoom() ? "报名成功" : "已成功将简历投至对方邮箱", 1500, Gravity.CENTER));
                    } else {
                        ToastUtils.showText(detailResponse.isBigBlueRoom() ? "报名成功" : "已成功将简历投至对方邮箱", 1500, Gravity.CENTER);
                        //投递后，如果还在投递成功列表，则通过这里进行数据管理，关闭投递成功后，deliverSuccessRecommendedSingleLiveData会被重置
                        DeliverSuccessShowRecommendedModel model = deliverSuccessRecommendedSingleLiveData.getValue();
                        if (null != model && null != model.response && LList.isNotEmpty(model.response.dataList)) {
                            for (JobInfoBean bean : model.response.dataList) {
                                if (null == bean) continue;
                                if (TextUtils.equals(bean.jobId, jobInfoBean.jobId)) {
                                    bean.delivered = true;
                                }
                            }
                            model.showWindow = false;
                            deliverSuccessRecommendedSingleLiveData.postValue(model);
                        }
                    }
                } else {
                    deliverErrorLiveData.postValue(data.resp);
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
                confirmRegisterModel = null;
            }
        }).execute();
    }

    /**
     * 获取推荐列表
     */
    public void getDeliverSuccessRecommendedList(JobInfoBean jobInfoBean, Runnable failRunnable) {
        if (null == jobInfoBean) return;
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_RCMD_JOB_RESUME)
                .addParam("enLiveId", liveRecordId)
                .addParam("enJobId", jobInfoBean.encryptJobId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveRecruitV2JobListResponse>() {
                    @Override
                    public void onSuccess(ApiData<LiveRecruitV2JobListResponse> data) {
                        if (data.resp == null || LList.isEmpty(data.resp.dataList)) {
                            if(null!=failRunnable){
                                failRunnable.run();
                            }
                            return;
                        }
                        deliverSuccessRecommendedSingleLiveData.postValue(new DeliverSuccessShowRecommendedModel(true, data.resp));
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        if(null!=failRunnable){
                            failRunnable.run();
                        }
                    }
                }).execute();
    }

    /**
     * 获取简历列表
     */
    public void getResumeList(ICommonCallback callback) {
        SimpleApiRequest.GET(GeekUrlConfig.URL_GET_RESUME_LIST)
                .addParam("entrance", 1)
                .setRequestCallback(new SimpleCommonApiRequestCallback<ResumeListResponse>() {
                    @Override
                    public void onSuccess(ApiData<ResumeListResponse> data) {
                        if (data.resp == null) return;
                        resumeListResponse = data.resp;

                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }
                }).execute();
    }

    /**
     * 投递前校验蓝领，蓝领弹框确认再投
     */
    public void checkBlueAndDeliver(DialogManager dialogManager, JobInfoBean jobInfoBean, boolean isJobListInto) {
        if (detailResponse.isBigBlueRoom() && null != dialogManager) {
            dialogManager.showConfirmRegisterFragment(new AudienceConfirmRegisterFragment.OnSaveClickBack() {
                @Override
                public void onClick() {
                    //1216.711 需求，蓝领要求直接投，不校验简历是否违规，和产品确认过
                    deliver(jobInfoBean, 0, dialogManager.isShowingJobBottomSheetDialog());
                }
            }, jobInfoBean, isJobListInto);
        } else {
            getResumeList(new ICommonCallback() {
                @Override
                public void onSuccess() {
                    deliverLiveData.postValue(jobInfoBean);
                }
            });
        }
    }

    /**
     * 【校招直播】查询职位卡片
     */
    public void requestRecommendJobCardInfo(SimpleCommonApiRequestCallback<GeekRecommendJobCardResponse> callback) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_LIVE_JOB_CARD_QUERY)
                .addParam("jobId", encryptRecommendJobId)
                .addParam("source", source)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(callback).execute();
    }

    /**
     * 职位列表中推荐tab
     */
    public void requestRecommendJobList() {
        if (UserManager.isBossRole()) return;
        LiveRecommendJobListRequest recommendJobListRequest = new LiveRecommendJobListRequest(new SimpleApiRequestCallback<LiveRecruitRecommendJobListResponse>() {
            @Override
            public void onSuccess(ApiData<LiveRecruitRecommendJobListResponse> data) {
                super.onSuccess(data);
                if (null != data && null != data.resp && LList.getCount(data.resp.jobList) > 0) {
                    recommendTagJobListSheetLiveData.postValue(data.resp);
                } else {
                    recommendTagJobListSheetLiveData.postValue(detailResponse.recommendJobListResponse);
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                recommendTagJobListSheetLiveData.postValue(detailResponse.recommendJobListResponse);
            }
        });
        recommendJobListRequest.liveRecordId = liveRecordId;
        HttpExecutor.execute(recommendJobListRequest);
    }


    /**
     * 发送消息
     */
    public void speak(int messageType, String content, ICommonCallback callback) {
        SimpleApiRequest.POST(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_MESSAGE_SEND : LiveUrlConfig.URL_BOSS_AUDIENCE_MESSAGE_SEND)
                .addParam("liveRecordId", liveRecordId)
                .addParam("liveRoomId", detailResponse.liveRoomId)
                .addParam("messageType", messageType)
                .addParam("content", content)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<EmptyResponse> data) {
                        super.handleInChildThread(data);
                        /*本场直播间评论过*/
                        SpManager.get().user().edit().putBoolean(CampusConstant.KEY_SP_PREFIX_COMMENT + liveRecordId, true).apply();

                        GiftModel model = giftLiveData.getValue();
                        if (model != null && !LList.isEmpty(model.giftList)) {
                            for (GiftBean bean : model.giftList) {
                                if (bean != null && bean.unlockConditions == GiftBean.UNLOCK_ASK) {
                                    bean.lock = false;
                                    /*更新当前礼物状态为已解锁状态*/
                                    SpManager.get().user().edit().putBoolean(CampusConstant.KEY_SP_GIFT_IS_UNLOCK + liveRecordId + "_" + bean.giftId, true).apply();
                                    break;
                                }
                            }
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }
                }).execute();
    }

    public void bossSpeak(String content, String positionGroupJSON, List<AtJobPositionBean> atJobPositions, ICommonCallback callback) {
        BossSpeakRequest bossSpeakRequest = new BossSpeakRequest(new SimpleApiRequestCallback<BossSpeakResponse>() {
            @Override
            public void onSuccess(ApiData<BossSpeakResponse> data) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
        bossSpeakRequest.recordId = liveRecordId;
        GeekRecruitDetailResponse geekResp = detailResponse;
        if (geekResp != null) {
            bossSpeakRequest.liveRoomId = geekResp.liveRoomId;
        }

        if (!LList.isEmpty(atJobPositions)) {// 有@的职位
            AtJobContentBean atJobContentBean = LiveCommonUtil.transformAtJob(content, atJobPositions);
            bossSpeakRequest.curContent = atJobContentBean.content;
            bossSpeakRequest.atJobPositions = atJobContentBean.atJobJsonStr;
        } else {
            if (!TextUtils.isEmpty(positionGroupJSON)) {
                bossSpeakRequest.atJobGroupPositions = positionGroupJSON;
            }
            bossSpeakRequest.curContent = content;
        }

        bossSpeakRequest.curContentType = 0;
        bossSpeakRequest.curUserName = "";
        bossSpeakRequest.execute();
    }

    /**
     * 品牌关注/取消关注
     *
     * @param source 来源，0直播间，1品牌页，2弹幕
     */
    public void toggleBrandSubscribe(int source) {
        if (detailResponse == null || detailResponse.commonConfig == null) return;
        // 关注类型：0关注（默认），1取消关注
        int flag = detailResponse.commonConfig.alreadyFocusBrand ? 1 : 0;

        brandSubscribe(null, flag, source);
    }

    /**
     * 品牌关注/取消关注
     *
     * @param flag 关注类型：0关注（默认），1取消关注
     */
    public void brandSubscribe(ICommonCallback callback, int flag, int source) {
        if (detailResponse == null || detailResponse.commonConfig == null) return;
        GeekBrandSubscribeRequest request = new GeekBrandSubscribeRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                detailResponse.commonConfig.alreadyFocusBrand = flag == 0;
                if (flag == 0) {
                    ToastUtils.showText("关注成功");
                    attentionPopLiveData.postValue(null);
                } else {
                    ToastUtils.showText("已取消关注");
                }
                // 刷新头部关注按钮
                roomInfoLiveData.postValue(detailResponse);

                Intent intent = new Intent();
                intent.setAction(CampusConstant.ACTION_BRAND_SUBSCRIBE);
                intent.putExtra(CampusConstant.DATA_BRAND_SUBSCRIBE, detailResponse.commonConfig.alreadyFocusBrand);
                ReceiverUtils.sendBroadcast(App.getAppContext(), intent);

                if (callback != null) {
                    callback.onSuccess();
                }
                LiveAnalysisUtil.dotCampusLiveBrandFollow(liveRecordId, detailResponse.securityId, source, flag == 0 ? 1 : 0);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.securityId = detailResponse.securityId;
        request.flag = flag;
        request.source = source;
        request.execute();
    }

    /**
     * 获取礼物列表
     */
    public void getGiftList() {
        LiveGiftListRequest request = new LiveGiftListRequest(new SimpleCommonApiRequestCallback<GeekLiveGiftListResponse>() {
            @Override
            public void onSuccess(ApiData<GeekLiveGiftListResponse> data) {
                if (data.resp == null) return;
                if (!LList.isEmpty(data.resp.giftList)) {
                    GiftModel giftModel = new GiftModel();
                    giftModel.giftList.clear();
                    for (GiftBean bean : data.resp.giftList) {
                        if (bean == null) continue;
                        bean.countdown = 0;//默认冷却时间都为0，只有点击了才赋值冷却时间
                        bean.stayCountdown = bean.unlockSeconds;
                        /*更新解锁状态*/
                        if (bean.unlockConditions == GiftBean.UNLOCK_NO_CONDITION) {/*无解锁条件*/
                            bean.lock = false;/*已解锁状态*/
                        } else {/*有解锁条件*/
                            bean.lock = !SpManager.get().user().getBoolean(CampusConstant.KEY_SP_GIFT_IS_UNLOCK + liveRecordId + "_" + bean.giftId, false);/*根据本地存储的状态来判断是否已经解锁过*/
                        }

                        /*冷却时间处理*/
                        LiveCommonUtil.handleCoolDownSeconds(liveRecordId, bean);
                        /*缓存霸屏动图到本地目录（后面展示动图的时候，直接从本地取）*/
                        if (!TextUtils.isEmpty(bean.gifUrl)) {
                            //判断本地是否有缓存,屏蔽下载，直接去拿缓存
                            File cFileByImageUrl = AudienceUtils.getCFileByImageUrl(bean.gifUrl, AudienceUtils.MOTION_GRAPH_CHILD_DIR, false);
                            if (null == cFileByImageUrl) {
                                //本地没有缓存，说明需要去下载
                                if (null == downLoadGifMaxRunnable) {
                                    downLoadGifMaxRunnable = new Runnable() {
                                        @Override
                                        public void run() {
                                            //过了60s，允许去下载
                                            if (null != additionalInfoResponse && null != additionalInfoResponse.giftDownload) {
                                                additionalInfoResponse.giftDownload.autoDownload = true;
                                            }
                                            AudienceUtils.getCFileByImageUrl(bean.gifUrl, AudienceUtils.MOTION_GRAPH_CHILD_DIR, true);
                                        }
                                    };
                                }
                                Utils.runOnUiThreadDelayed(downLoadGifMaxRunnable, null != additionalInfoResponse && null != additionalInfoResponse.giftDownload ? additionalInfoResponse.giftDownload.downloadDelay : 0);
                            }
                        }
                        giftModel.giftList.add(bean);
                    }
                    giftLiveData.setValue(giftModel);
                }
            }
        });
        if (UserManager.isGeekRole()) {
            request.type = 0;
        } else {
            request.liveRecordId = liveRecordId;
        }
        request.execute();
    }

    public void messageSendGift(String content, int sendCount, ICommonCallback callback) {
        SimpleApiRequest.POST(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_MESSAGE_SEND : LiveUrlConfig.URL_BOSS_AUDIENCE_MESSAGE_SEND)
                .addParam("liveRecordId", liveRecordId)
                .addParam("liveRoomId", detailResponse.liveRoomId)
                .addParam("messageType", 1)
                .addParam("content", content)
                .addParam("count", sendCount)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }
                }).execute();
    }

    public void messageSendGift(GiftBean giftBean) {
        messageSendGift(String.valueOf(giftBean.giftId),
                giftBean.giftCount + giftBean.getTheSendGiftSize(), new ICommonCallback() {
                    @Override
                    public void onFail(int code, String msg) {
                    }

                    @Override
                    public void onSuccess() {
                        /** 冷却时间处理 */
                        SpManager.get().user().edit().putLong(CampusConstant.KEY_SP_PREFIX_COOL_DOWN_SECONDS
                                + liveRecordId + "_" + giftBean.giftId, System.currentTimeMillis()).apply();
                    }
                });
    }

    /**
     * 获取置顶消息
     */
    public void requestTopMessage() {
        SimpleApiRequest.GET(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_TOP_MESSAGE : LiveUrlConfig.URL_BOSS_AUDIENCE_RECRUIT_TOP_MESSAGE)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<RecruitTopMessageResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<RecruitTopMessageResponse> data) {
                        super.handleInChildThread(data);
                        if (data.resp == null || data.resp.realContent == null) return;

                        RecruitTopMessageBean realContent = data.resp.realContent;
                        CommentItemBean commentItemBean = new CommentItemBean(MsgType.TYPE_CONTROL_TOP);
                        commentItemBean.msgId = realContent.msgId;
                        commentItemBean.top = realContent.top;
                        commentItemBean.msgSenderId = realContent.msgSenderId;
                        commentItemBean.msgSenderName = realContent.msgSenderName;
                        commentItemBean.avatarUrl = realContent.msgSenderTiny;
                        commentItemBean.msg = realContent.msg;
                        commentItemBean.curContentType = realContent.curContentType;
                        commentItemBean.timeOutSpaceV2 = realContent.remainingTimeV2;
                        commentModel.addItem(commentItemBean);
                        commentModelLiveData.postValue(commentModel);
                    }
                }).execute();
    }

    /**
     * 获取简历投递情况
     */
    public void getDeliveryDetails(ICommonCallback callback) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GET_BOSS_LIVE_GET_DELIVERY_DETAILS)
                .addParam("encryptLiveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<BossLiveGetDeliveryDetailsResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<BossLiveGetDeliveryDetailsResponse> data) {
                        if (data.resp == null) return;
                        proxyBossJobListLiveData.postValue(new ProxyBossJobModel(data.resp.intention, data.resp.jobList));
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        if (callback != null) {
                            callback.onComplete();
                        }
                        hideProgress();
                    }
                }).execute();
    }

    private boolean destroyed;

    /**
     * 获取直播心跳状态
     */
    public void heartBeat() {
        SimpleApiRequest.GET(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_HEARTBEAT : LiveUrlConfig.URL_BOSS_AUDIENCE_RECRUIT_HEARTBEAT)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<RecruitHeartBeatResponse>() {
                    @Override
                    public void onSuccess(ApiData<RecruitHeartBeatResponse> data) {
                        if (data.resp == null || destroyed) return;
                        detailResponse.liveState = data.resp.liveState;
                        detailResponse.scrnOrient = data.resp.scrnOrient;
                        // 控制心跳间隔至少1s
                        if (data.resp.liveStateHeartbeat <= 0 || (data.resp.liveState != LiveState.WAIT && data.resp.liveState != LiveState.DELAY)) {
                            responseLiveData.postValue(detailResponse);
                        } else {
                            startHeartBeatTime(data.resp.liveStateHeartbeat);
                        }
                    }
                }).execute();
    }

    private void startHeartBeatTime(long liveStateHeartbeat) {
        Utils.removeHandlerCallbacks(this::heartBeat);
        if (liveStateHeartbeat > 0) {
            Utils.runOnUiThreadDelayed(this::heartBeat, liveStateHeartbeat);
        }
    }

    /**
     * 查询分享海报信息
     */
    public void querySharePoster() {
        SharePosterQueryRequest request = new SharePosterQueryRequest(isProxyBoss, new ApiRequestCallback<SharePosterQueryResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<SharePosterQueryResponse> data) {
                posterShareLiveData.postValue(new PosterShareModel(data.resp));
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                posterShareLiveData.postValue(new PosterShareModel(null));
            }
        });

        if (UserManager.isGeekRole()) {
            request.liveRecordId = liveRecordId;
        } else {
            request.encryptLiveRecordId = liveRecordId;
        }
        request.execute();
    }

    private AudienceJobListBatchRequest audienceJobListBatchRequest;

    /**
     * 请求职位列表数据
     */
    public void requestJobListByGroup(int page, long positionCode, long cityCode,
                                      long experienceCode, long degreeCode,
                                      JobGroupBean groupBean,
                                      @Nullable ApiRequestCallback<AudienceJobListBatchResponse> callback) {
        if (audienceJobListBatchRequest != null) audienceJobListBatchRequest.cancelRequest();
        WeakReference<ApiRequestCallback<AudienceJobListBatchResponse>> callbackWeakReference = new WeakReference<>(callback);
        audienceJobListBatchRequest = new AudienceJobListBatchRequest(new SimpleCommonApiRequestCallback<AudienceJobListBatchResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                if (page == 1) showProgress();
            }

            @Override
            public void onSuccess(ApiData<AudienceJobListBatchResponse> data) {
                super.onSuccess(data);
                if (callbackWeakReference.get() != null) {
                    callbackWeakReference.get().onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
                if (callbackWeakReference.get() != null) {
                    callbackWeakReference.get().onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callbackWeakReference.get() != null) {
                    callbackWeakReference.get().onFailed(reason);
                }
            }
        });
        /*职位列表数据*/
        LiveRecruitV2JobListRequest liveRecruitV2JobListRequest = new LiveRecruitV2JobListRequest();
        liveRecruitV2JobListRequest.liveRecordId = liveRecordId;
        liveRecruitV2JobListRequest.page = page;

        if (groupBean.isTypeAllGroup()) {
            liveRecruitV2JobListRequest.positionCode = positionCode;
            liveRecruitV2JobListRequest.cityCode = cityCode;
            liveRecruitV2JobListRequest.experienceCode = experienceCode;
            liveRecruitV2JobListRequest.degreeCode = degreeCode;
        } else {
            liveRecruitV2JobListRequest.positionCode = FilterItemBean.TYPE_ALL_CODE;
            liveRecruitV2JobListRequest.cityCode = LText.getLong(groupBean.groupId);
            liveRecruitV2JobListRequest.experienceCode = FilterItemBean.TYPE_ALL_CODE;
            liveRecruitV2JobListRequest.degreeCode = FilterItemBean.TYPE_ALL_CODE;
        }

        liveRecruitV2JobListRequest.encryptRecommendJobId = encryptRecommendJobId;
        liveRecruitV2JobListRequest.extendedJobGroupType = groupBean.extendedJobGroupType;
        liveRecruitV2JobListRequest.extendedJobGroupL2Type = groupBean.extendedJobGroupL2Type;
        liveRecruitV2JobListRequest.groupId = groupBean.groupId;
        liveRecruitV2JobListRequest.from = from;
        audienceJobListBatchRequest.liveRecruitV2JobListRequest = liveRecruitV2JobListRequest;
        audienceJobListBatchRequest.execute();
    }

    /**
     * 求讲解
     */
    public void requestPositionExplain(JobInfoBean jobInfo, String question, ICommonCallback callback) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_POSITION_EXPLAIN)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfo.securityId)
                .addParam("groupId", jobInfo.encryptJobGroupId)
                .addParam("question", question)// 求讲解问题
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }
                }).execute();
    }

    /**
     * 职位组求讲解
     */
    public void requestJobGroupExplain(String encryptJobGroupId, ICommonCallback callback) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_GROUP_EXPLAIN)
                .addParam("liveRecordId", liveRecordId)
                .addParam("groupId", encryptJobGroupId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }
                }).execute();
    }

    /**
     * 点赞
     *
     * @param type 点赞方式 1:右下角点赞图标，2:双击屏幕点赞
     */
    public void applaud(int type) {
        SimpleApiRequest.POST(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_APPLAUD : LiveUrlConfig.URL_BOSS_AUDIENCE_APPLAUD)
                .addParam("liveRecordId", liveRecordId)
                .execute();
        LiveAnalysisUtil.dotCampusLivePageBThumbUp(liveRecordId, type);
    }

    /**
     * 查询心动榜数据
     */
    public void getIntentionList() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_SCORE_LIST)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveRecruitScoreListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LiveRecruitScoreListResponse> data) {
                        getIntentionListLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 观看时长上报
     *
     * @param duration 观看时长，2，5，10，20 单位：分钟
     */
    public void reportDuration(long duration) {
        if (UserManager.isBossRole()) return;/*BOSS身份不上报*/
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_DURATION_REPORT)
                .addParam("liveRecordId", liveRecordId)
                .addParam("duration", duration)
                .execute();
    }

    /**
     * @param subscribed true 订阅 false 取消订阅
     */
    public void bossSubscribe(boolean subscribed) {
        SimpleApiRequest.POST(subscribed ? LiveUrlConfig.URL_BOSS_SUBSCRIBE_LIVE : LiveUrlConfig.URL_BOSS_UNSUBSCRIBE_LIVE)
                .addParam("encryptLiveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        if (subscribed) {
                            detailResponse.subscribed = true;
                            detailResponse.subscribeUserCnt++;
                            subscribeLiveData.postValue(detailResponse);
                            ToastUtils.showText("预约成功");
                        } else {
                            detailResponse.subscribed = false;
                            detailResponse.subscribeUserCnt--;
                            subscribeLiveData.setValue(detailResponse);
                            ToastUtils.showText("取消预约");
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    public void subscribe(boolean toast) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_SUBSCRIBE_LIVE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("source", source)
                .addParam("listSource", listSource)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekLiveSubscribeResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekLiveSubscribeResponse> data) {
                        detailResponse.subscribed = true;
                        detailResponse.subscribeUserCnt++;
                        subscribeLiveData.postValue(detailResponse);
                        if (toast) {
                            ToastUtils.showText("预约成功");
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 取消订阅预约
     */
    public void unSubscribe(boolean toast) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_UNSUBSCRIBE_LIVE)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekLiveUnsubscribeResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekLiveUnsubscribeResponse> data) {
                        detailResponse.subscribed = false;
                        detailResponse.subscribeUserCnt--;
                        subscribeLiveData.setValue(detailResponse);
                        if (toast) {
                            ToastUtils.showText("取消预约");
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取职位详情，带投递和订阅状态，batch接口
     */
    public void getJobDetailWithStatus(JobInfoBean jobInfoBean, ApiRequestCallback<GeekGetJobDetailBatchResponse> callback) {
        if (jobInfoBean == null) return;
        GeekGetJobDetailBatchRequest batchRequest = new GeekGetJobDetailBatchRequest(callback);
        if (UserManager.isGeekRole()) {
            GetJobDetailRequest jobDetailRequest = new GetJobDetailRequest();
            jobDetailRequest.securityId = jobInfoBean.securityId;
            batchRequest.getJobDetailRequest = jobDetailRequest;

            /*职位收藏状态、投递简历状态、订阅状态信息接口请求*/
            GeekGetJobStatusRequest jobStatusRequest = new GeekGetJobStatusRequest();
            jobStatusRequest.liveRecordId = liveRecordId;
            jobStatusRequest.securityId = jobInfoBean.securityId;
            batchRequest.getJobStatusRequest = jobStatusRequest;

            /*增加海螺优条展示*/
            GetJobQueryBannerRequest jobQueryBannerRequest = new GetJobQueryBannerRequest();
            jobQueryBannerRequest.securityId = jobInfoBean.securityId;
            batchRequest.jobQueryBannerRequest = jobQueryBannerRequest;
        } else {
            BossGetCampusLiveJobDetailRequest bossGetCampusLiveJobDetailRequest = new BossGetCampusLiveJobDetailRequest();
            bossGetCampusLiveJobDetailRequest.liveRecordId = liveRecordId;
            bossGetCampusLiveJobDetailRequest.jobId = jobInfoBean.encryptJobId;
            batchRequest.bossGetCampusLiveJobDetailRequest = bossGetCampusLiveJobDetailRequest;
        }

        batchRequest.execute();
    }

    /**
     * 收藏职位
     */
    public void interest(JobInfoBean jobInfoBean, ICommonCallback callback) {
        if (jobInfoBean == null) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_INTEREST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void handleInChildThread(ApiData<EmptyResponse> data) {
                        jobInfoBean.interested = true;
                        updateJobInfo(jobInfoBean);
                        positionLiveData.postValue(detailResponse);

                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 取消收藏职位
     */
    public void unInterest(JobInfoBean jobInfoBean, ICommonCallback callback) {
        if (jobInfoBean == null) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_UNINTEREST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void handleInChildThread(ApiData<EmptyResponse> data) {
                        jobInfoBean.interested = false;
                        updateJobInfo(jobInfoBean);
                        positionLiveData.postValue(detailResponse);
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 订阅职位
     */
    public void jobSubscribe(JobInfoBean jobInfoBean, ICommonCallback callback) {
        if (jobInfoBean == null) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_SUBSCRIBE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void handleInChildThread(ApiData<EmptyResponse> data) {
                        jobInfoBean.subscribed = true;
                        updateJobInfo(jobInfoBean);
                        positionLiveData.postValue(detailResponse);
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 取消订阅职位
     */
    public void jobUnSubscribe(JobInfoBean jobInfoBean, ICommonCallback callback) {
        if (jobInfoBean == null) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_UNSUBSCRIBE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", jobInfoBean.securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void handleInChildThread(ApiData<EmptyResponse> data) {
                        jobInfoBean.subscribed = false;
                        updateJobInfo(jobInfoBean);
                        positionLiveData.postValue(detailResponse);
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    public void requestHighCompanyInfo() {
        LiveBrandHomeRequest request = new LiveBrandHomeRequest(new SimpleCommonApiRequestCallback<LiveBrandHomeResponse>() {
            @Override
            public void onSuccess(ApiData<LiveBrandHomeResponse> data) {
                highCompanyLiveData.setValue(new AppointmentHighCompanyInfoModel(data.resp));
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                highCompanyLiveData.setValue(new AppointmentHighCompanyInfoModel(reason));
            }
        });
        request.securityId = detailResponse.securityId;
        HttpExecutor.execute(request);
    }

    public void requestNormalCompanyInfo() {
        GetBrandInfoRequest request = new GetBrandInfoRequest(new SimpleApiRequestCallback<GetBrandInfoResponse>() {
            @Override
            public void onSuccess(ApiData<GetBrandInfoResponse> data) {
                normalCompanyLiveData.setValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                companyErrorLiveData.setValue(reason);
            }
        });

        if (UserManager.isBossRole() && String.valueOf(UserManager.getBrandId()).equals(detailResponse.brandId)) {
            request.brandId = "";
        } else {
            request.brandId = String.valueOf(detailResponse.brandId);
        }
        request.lid = "";
        request.sf = "";
        request.topicId = "";
        request.securityId = null != detailResponse.securityId ? detailResponse.securityId : "";
        request.execute();
    }

    public void requestHighCompanyPastList(String lastLiveRecordId) {
        LiveBrandPastLiveRequest request = new LiveBrandPastLiveRequest(new SimpleCommonApiRequestCallback<LiveBrandPastListResponse>() {
            @Override
            public void onSuccess(ApiData<LiveBrandPastListResponse> data) {
                highCompanyMoreLiveData.setValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                companyErrorLiveData.setValue(reason);
            }
        });
        request.securityId = detailResponse.securityId;
        request.lastLiveRecordId = lastLiveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 预约直播间
     */
    public void subscribe(String liveRecordId, ICommonCallback callback) {
        GeekLiveSubscribeRequest request = new GeekLiveSubscribeRequest(new SimpleApiRequestCallback<GeekLiveSubscribeResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GeekLiveSubscribeResponse> data) {
                ToastUtils.showText("预约成功");
                if (TextUtils.equals(liveRecordId, detailResponse.liveRecordId)) {// 如果是当前直播间
                    detailResponse.subscribed = true;
                    detailResponse.subscribeUserCnt++;
                    subscribeLiveData.postValue(detailResponse);
                }

                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 取消预约直播间
     */
    public void unSubscribe(String liveRecordId, ICommonCallback callback) {
        GeekLiveUnsubscribeRequest request = new GeekLiveUnsubscribeRequest(new SimpleApiRequestCallback<GeekLiveUnsubscribeResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GeekLiveUnsubscribeResponse> data) {
                ToastUtils.showText("已取消预约");
                if (TextUtils.equals(liveRecordId, detailResponse.liveRecordId)) {// 如果是当前直播间
                    detailResponse.subscribed = false;
                    detailResponse.subscribeUserCnt--;
                    subscribeLiveData.postValue(detailResponse);
                }

                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取导播间活动专栏数据
     * 1006.803 直播优化 - 导播间添加活动专栏
     */
    public void getActivityColumn() {
        LiveGuideActivityColumnRequest request = new LiveGuideActivityColumnRequest(new SimpleApiRequestCallback<LiveGuideActivityColumnResponse>() {
            @Override
            public void onSuccess(ApiData<LiveGuideActivityColumnResponse> data) {
                super.onSuccess(data);
                guideActivityColumn.postValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                guideActivityColumn.postValue(null);
            }
        });
        request.specialColumnRecordId = detailResponse.encryptLiveSpecialColumnRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 弹出抽奖大宝箱
     */
    public void showLuckyDrawPop() {
        LuckyDrawCModel drawLiveDataValue = luckyDrawLiveData.getValue();
        if (drawLiveDataValue != null) {
            drawLiveDataValue.setShowType(LuckyDrawCModel.TYPE_POP);
            luckyDrawLiveData.postValue(drawLiveDataValue);
        }
    }

    public void requestLotteryDraw() {
        requestLotteryDraw(null, null);
    }

    /**
     * 参与抽奖
     *
     * @param luckyDrawOptionId 用户选中的答题抽奖选项id（答题抽奖才有）
     */
    public void requestLotteryDraw(String luckyDrawOptionId, ICommonCallback callback) {
        GeekLuckyDrawRequest geekLuckyDrawRequest = new GeekLuckyDrawRequest(isProxyBoss, new SimpleApiRequestCallback<GeekLuckyDrawResponse>() {
            @Override
            public void onSuccess(ApiData<GeekLuckyDrawResponse> data) {
                if (data.resp == null) return;
                if (data.resp.result) {
                    if (!TextUtils.isEmpty(luckyDrawOptionId)) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    } else {
                        LuckyDrawCModel model = luckyDrawLiveData.getValue();
                        if (model != null) {
                            model.luckyDrawInfo.canLuckyDraw = false;
                            model.luckyDrawInfo.userDrawStatus = 2;
                            model.luckyDrawInfo.participated = true;
                            luckyDrawLiveData.postValue(model);
                        }
                    }
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (reason.getErrCode() == 200001) {
                    // 隐藏大宝箱，显示小宝箱，弹出错误弹框
                    LuckyDrawCModel model = luckyDrawLiveData.getValue();
                    if (model != null) {
                        model.setShowType(LuckyDrawCModel.TYPE_ERROR_DIALOG);
                        luckyDrawLiveData.postValue(model);
                    }
                } else {
                    ToastUtils.showText(reason.getErrReason());
                }
            }
        });
        LuckyDrawCModel drawLiveDataValue = luckyDrawLiveData.getValue();
        if (null != drawLiveDataValue && null != drawLiveDataValue.getLuckyDrawInfo()) {
            LuckyDrawInfoBean luckyDrawInfo = drawLiveDataValue.getLuckyDrawInfo();
            geekLuckyDrawRequest.realLuckyDrawOrder = luckyDrawInfo.realLuckyDrawOrder;
        }
        geekLuckyDrawRequest.liveRecordId = liveRecordId;
        geekLuckyDrawRequest.luckyDrawOptionId = luckyDrawOptionId;
        geekLuckyDrawRequest.execute();
    }

    /**
     * 获取标记讲解的播放地址
     *
     * @param jobInfoBean
     * @param securityId  职位或直播间加密ID（获取职位回放地址传职位加密ID，获取高级直播间分组回放地址传直播间加密ID）
     * @param groupId     高级直播间加密分组ID
     */
    public void queryPlayBackUrl(JobInfoBean jobInfoBean, String securityId, String groupId) {
        GeekPlayBackQueryRequest request = new GeekPlayBackQueryRequest(new SimpleCommonApiRequestCallback<GeekPlayBackQueryResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GeekPlayBackQueryResponse> data) {
                if (data.resp == null) return;
                if (TextUtils.isEmpty(data.resp.playBackUrl)) {
                    ToastUtils.showText("出了点小问题，请稍后再试");
                } else {
                    hideProgress();
                    ExplainJobModel explainJobModel = new ExplainJobModel(jobInfoBean);
                    explainJobModel.playBackUrl = data.resp.playBackUrl;
                    explainJobLiveData.postValue(explainJobModel);
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.securityId = securityId == null ? jobInfoBean.securityId : securityId;
        request.groupId = groupId;
        request.execute();
        /*埋点：C在APP观看直播中点击【看讲解】按钮*/
        LiveAnalysisUtil.dotCampusLiveExplainVm(liveRecordId, securityId, groupId, detailResponse.isFinishLive() ? 2 : 1, "");
    }

    /**
     * 获取章节的播放地址
     */
    public void queryChapterPlayBackUrl(String programmeChapterId, ICommonCallback callback) {
        SimpleApiRequest.GET(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_CHAPTER_PLAYBACK_QUERY : LiveUrlConfig.URL_BOSS_GEEK_RECRUIT_CHAPTER_PLAYBACK_QUERY)
                .addParam("liveRecordId", liveRecordId)
                .addParam("programmeChapterId", programmeChapterId)// 加密节目单章节ID
                .setRequestCallback(new SimpleCommonApiRequestCallback<LivePlaybackQueryResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LivePlaybackQueryResponse> data) {
                        if (data.resp == null) return;
                        if (TextUtils.isEmpty(data.resp.playUrl)) {
                            ToastUtils.showText("出了点小问题，请稍后再试");
                        } else {
                            if (callback != null) {
                                callback.onSuccess();
                            }
                            LiveProgrammeChapterBean chapterBean = LiveCommonUtil.findChapter(additionalInfoResponse, programmeChapterId);
                            if (chapterBean == null) return;
                            ExplainChapterModel explainChapterModel = new ExplainChapterModel(chapterBean);
                            explainChapterModel.playBackUrl = data.resp.playUrl;
                            explainChapterLiveData.postValue(explainChapterModel);
                        }
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    private GeekBarrageListRequest geekBarrageListRequest;

    /**
     * 拉取弹幕消息
     *
     * @param encryptDialogId 加密弹幕ID
     * @param relativeTimeMs  弹幕发送的时间戳 (单位：ms)
     */
    public void loadMessage(String encryptDialogId, long relativeTimeMs, boolean isNeedClear) {
        if (isNeedClear) {
            clearBarrageUiList();
            barrageDispatcher.clearQueue();
        }
        BarrageBean wholeLiveLastBarrage = barrageDispatcher.getWholeLiveLastBarrage();

        if (wholeLiveLastBarrage != null && relativeTimeMs > wholeLiveLastBarrage.relativeTimeMs) {
            //如果要请求的时间 大于 整个直播间的最后一条弹幕的时间，那么就没必要请求了，直接return（因为已经是最后一条弹幕了，大于最后一条弹幕的时间后肯定就没有弹幕了）
            return;
        }

        if (geekBarrageListRequest != null) {
            geekBarrageListRequest.cancelRequest();
        }
        geekBarrageListRequest = new GeekBarrageListRequest(new SimpleCommonApiRequestCallback<GeekBarrageListResponse>() {
            @Override
            public void onSuccess(ApiData<GeekBarrageListResponse> data) {
                if (data.resp == null) return;
                if (LList.getCount(data.resp.records) > 0) {
                    if (isNeedClear) {
                        barrageDispatcher.clearQueue();
                        clearBarrageUiList();
                    }
                    barrageDispatcher.dispatchBarrage(data.resp.records, data.resp.hasMore);
                }
            }
        });
        if (UserManager.isGeekRole()) {
            geekBarrageListRequest.liveRecordId = liveRecordId;
        } else {
            geekBarrageListRequest.encryptRecordId = liveRecordId;
        }
        geekBarrageListRequest.encryptDialogId = encryptDialogId;
        geekBarrageListRequest.relativeTimeMs = relativeTimeMs;
        geekBarrageListRequest.execute();

        barrageDispatcher.setLastRequestTime(SystemClock.elapsedRealtime());
    }

    /**
     * 清空弹幕列表数据
     */
    private void clearBarrageUiList() {
        commentModel.isNeedClear = true;
        commentModel.itemList = new CopyOnWriteArrayList<>();
        commentModel.allCommentList = new CopyOnWriteArrayList<>();
        commentModelLiveData.setValue(commentModel);
    }

    /**
     * 进入正在直播的直播间，获取历史弹幕
     */
    public void getHistoryMessages() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_HISTORY_MESSAGES)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekHistoryMessagesResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<GeekHistoryMessagesResponse> data) {
                        super.handleInChildThread(data);
                        if (data.resp == null) return;
                        if (LList.getCount(data.resp.msgList) > 0) {
                            List<CommentItemBean> comments = new ArrayList<>();
                            for (CommentItemBean itemBean : data.resp.msgList) {
                                if (itemBean == null) continue;
                                comments.add(msgDispatcher.preTransform(itemBean));
                            }
                            postCommentModel(comments);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                }).execute();
    }

    /**
     * 向commentModelLiveData post CommentItemBean列表
     */
    private void postCommentModel(List<CommentItemBean> comments) {
        if (LList.isEmpty(comments)) return;
        for (CommentItemBean itemBean : comments) {
            if (itemBean == null) continue;
            if (itemBean.msgType != MsgType.TYPE_COME_IN || itemBean.msgSenderId == UserManager.getUID()) {// 过滤掉除自己外XXX进来了
                commentModel.allCommentList.add(itemBean);
            }

            if (itemBean.msgType == MsgType.TYPE_GIFT && commentModel.hideUIList) {// 隐藏互动列表时接到礼物消息，不展示礼物动画
                itemBean.shown = true;
            }
            commentModel.addItem(itemBean);
        }
        commentModelLiveData.postValue(commentModel);
    }

    /**
     * 获取某条「直播切片」讲解视频的播放地址
     */
    public void getExplainPlayUrl(String explainId, boolean isLastItem) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_EXPLAIN_PLAY_URL_QUERY)
                .addParam("liveRecordId", liveRecordId)
                .addParam("explainId", explainId)/*视频讲解ID*/
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveExplainUrlQueryResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LiveExplainUrlQueryResponse> data) {
                        if (data.resp == null) return;
                        playVideoLiveData.setValue(new PlayVideoModel(data.resp.playUrl, isLastItem));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                        shareDataCenter.intent.putExtra(IntentKey.INTENT_LIVE_ENCRYPT_EXPLAIN_ID, "");
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        playVideoLiveData.setValue(new PlayVideoModel(CampusConstant.PLAY_ERROR_URL, isLastItem));
                    }
                }).execute();
    }

    /**
     * 获取某条「直播切片」讲解视频的播放地址
     */
    public void getExplainPlayUrl(JobInfoBean jobInfoBean, String explainId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_EXPLAIN_PLAY_URL_QUERY)
                .addParam("liveRecordId", liveRecordId)
                .addParam("explainId", explainId)/*视频讲解ID*/
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveExplainUrlQueryResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LiveExplainUrlQueryResponse> data) {
                        if (data.resp == null) return;
                        if (TextUtils.isEmpty(data.resp.playUrl)) {
                            ToastUtils.showText("出了点小问题，请稍后再试");
                        } else {
                            hideProgress();
                            explainSliceVideoLiveData.postValue(new ExplainJobPlayModel(jobInfoBean, data.resp.playUrl));
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 查询直播间预设问题
     */
    public void queryQuestion() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_RECRUIT_QUESTION_QUERY)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekQuestionQueryResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<GeekQuestionQueryResponse> data) {
                        super.handleInChildThread(data);
                        explainQuestionResponseLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                }).execute();
    }


    /**
     * 查询直播间预设问题
     */
    public void queryQuickQuestion() {
        SimpleApiRequest.GET(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_QUICK_QUESTION_QUERY : LiveUrlConfig.URL_GEEK_RECRUIT_QUICK_QUESTION_QUERY_B)
                .addParam(UserManager.isGeekRole() ? "liveRecordId" : "recordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekQuickQuestionQueryResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<GeekQuickQuestionQueryResponse> data) {
                        super.handleInChildThread(data);
                        questionResponseLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {

                    }

                }).execute();
    }

    public List<String> getExplainQuestionList() {
        GeekQuestionQueryResponse response = explainQuestionResponseLiveData.getValue();
        return response != null ? response.explainQuestionList : null;
    }

    /**
     * 节目单章节 预约/取消预约
     *
     * @param programmeChapterId 加密节目单章节ID
     * @param type               类型，默认0，0预约，1取消预约
     */
    public void chapterSubscribe(String programmeChapterId, int type, ICommonCallback callback) {
        SimpleApiRequest.POST(UserManager.isGeekRole() ? LiveUrlConfig.URL_GEEK_RECRUIT_CHAPTER_SUBSCRIBE : LiveUrlConfig.URL_BOSS_GEEK_RECRUIT_CHAPTER_SUBSCRIBE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("programmeChapterId", programmeChapterId)
                .addParam("type", type)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 点击弹幕+1
     */
    public void messageClickAddOne(CommentItemBean commentItemBean, ICommonCallback callback) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_MESSAGE_CLICK)
                .addParam("liveRecordId", liveRecordId)
                .addParam("id", commentItemBean.msgType == MsgType.TYPE_USER_COMMENT
                        ? commentItemBean.encryptMsgId : commentItemBean.encryptExplainId)
                .addParam("type", commentItemBean.msgType == MsgType.TYPE_USER_COMMENT ? 2 : 1)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    private LiveVoteRequest liveVoteRequest;

    /**
     * 直播间触发投票动作
     */
    public void liveVote(String encryptVoteIdParam, String encryptVoteOptionIdParam) {
        if (TextUtils.isEmpty(encryptVoteIdParam) || TextUtils.isEmpty(encryptVoteOptionIdParam))
            return;
        if (liveVoteRequest != null) liveVoteRequest.cancelRequest();
        CustomApiRequestWithParamCallback callback = new CustomApiRequestWithParamCallback<LiveVoteResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<LiveVoteResponse> data) {
                String encryptVoteId = this.paramString_1;
                String encryptVoteOptionId = this.paramString_2;

                VoteInfoBean voteInfoBean = beginVoteInfoLiveData.getValue();
                LiveVoteBean voteBean = new LiveVoteBean();
                voteBean.encryptVoteId = encryptVoteId;
                voteBean.voteOptions = new ArrayList<>();

                if (voteInfoBean != null && LList.getCount(voteInfoBean.voteOptions) > 0 && TextUtils.equals(voteInfoBean.encryptVoteId, encryptVoteId)) {
                    for (VoteOptionBean voteOptionBean : voteInfoBean.voteOptions) {
                        if (voteOptionBean == null) continue;
                        if (TextUtils.equals(encryptVoteOptionId, voteOptionBean.encryptVoteOptionId)) {
                            voteOptionBean.voted = 1;
                            voteOptionBean.optionVoteCount++;
                        }
                        VoteCastOption voteCastOption = new VoteCastOption();
                        voteCastOption.encryptVoteOptionId = voteOptionBean.encryptVoteOptionId;
                        voteCastOption.optionVoteCount = voteOptionBean.optionVoteCount;
                        voteBean.voteOptions.add(voteCastOption);
                    }
                }
                updateVoteCountLiveData.postValue(new LiveVoteInfoModel(LiveVoteInfoModel.EVENT_TYPE_VOTE_REQUEST, voteBean, encryptVoteOptionId));
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        };
        callback.paramString_1 = encryptVoteIdParam;
        callback.paramString_2 = encryptVoteOptionIdParam;

        liveVoteRequest = new LiveVoteRequest(callback);
        liveVoteRequest.liveRecordId = liveRecordId;
        liveVoteRequest.encryptVoteId = encryptVoteIdParam;
        liveVoteRequest.encryptVoteOptionId = encryptVoteOptionIdParam;
        HttpExecutor.execute(liveVoteRequest);
    }

    public void getVoteList(String liveRecordId) {
        SimpleApiRequest.GET(UserManager.isBossRole() ? LiveUrlConfig.URL_BOSS_GET_VOTE_LIST : LiveUrlConfig.URL_GEEK_GET_VOTE_LIST)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveVoteListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LiveVoteListResponse> data) {
                        voteList.postValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        voteList.postValue(null);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取本场投递列表
     */
    public void getGeekDeliveryList(int page, String encryptJobId, ICommonCallback callback) {
        long devoteScore = -1;
        long offsetId = -1;
        if (page != 1 && null != deliverListLiveData.getValue()) {
            GeekDeliveryListModel geekDeliveryListModel = deliverListLiveData.getValue();
            if (null != geekDeliveryListModel && null != geekDeliveryListModel.response) {
                List<BossDeliveryGeekBean> deliveryGeekList = geekDeliveryListModel.response.deliveryGeekList;
                int count = LList.getCount(deliveryGeekList);
                if (count > 0) {
                    BossDeliveryGeekBean geekBean = LList.getElement(deliveryGeekList, count - 1);
                    if (null != geekBean) {
                        devoteScore = geekBean.usingIntention;
                        offsetId = geekBean.intentionId;
                    }
                }
            }
        }
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_PROXY_GEEK_DELIVERY_LIST)
                .addParam("page", page)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("encryptJobId", encryptJobId)
                .addParam("devoteScore", devoteScore)//默认-1，首次可不传，之后为最后一条记录的usingIntention
                .addParam("offsetId", offsetId)//默认-1，首次可不传，之后为最后一条记录的intentionId
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekDeliveryListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        if (page == 1) showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekDeliveryListResponse> data) {
                        super.onSuccess(data);
                        deliverListLiveData.postValue(new GeekDeliveryListModel(page, data.resp));
                        if (null != data.resp) {
                            LiveAnalysisUtil.dotCampusGeekListExpose(liveRecordId, data.resp.deliveryGeekList, 2);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        deliverListLiveData.postValue(new GeekDeliveryListModel(page, null));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                        if (callback != null) {
                            callback.onComplete();
                        }
                    }
                }).execute();
    }

    /**
     * 获取本场投递列表「同公司boss」
     */
    public void getCompanyColleagueGeekDeliveryList(int page, ICommonCallback callback) {
        String encryptDeliveryId = "";
        long recommendScore = 0;
        if (page != 1 && null != deliverCompanyColleagueListLiveData.getValue()) {
            GeekDeliveryListModel geekDeliveryListModel = deliverCompanyColleagueListLiveData.getValue();
            if (null != geekDeliveryListModel && null != geekDeliveryListModel.response) {
                List<BossDeliveryGeekBean> deliveryGeekList = geekDeliveryListModel.response.deliveryGeekList;
                int count = LList.getCount(deliveryGeekList);
                if (count > 0) {
                    BossDeliveryGeekBean geekBean = LList.getElement(deliveryGeekList, count - 1);
                    if (null != geekBean) {
                        encryptDeliveryId = geekBean.encryptDeliveryId;
                        recommendScore = geekBean.recommendScore;
                    }
                }
            }
        }
        SimpleApiRequest simpleApiRequest = SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_COMPANY_COLLEAGUE_GEEK_DELIVERY)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("encryptDeliveryId", encryptDeliveryId)//加密的投递id（如果不传，默认返回第一页数据；如果传且非空，返回小于该id的那一页数据）
                .setRequestCallback(new SimpleCommonApiRequestCallback<GeekDeliveryListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        if (page == 1) showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekDeliveryListResponse> data) {
                        super.onSuccess(data);
                        deliverCompanyColleagueListLiveData.postValue(new GeekDeliveryListModel(page, data.resp));
                        if (null != data.resp) {
                            LiveAnalysisUtil.dotCampusGeekListExpose(liveRecordId, data.resp.deliveryGeekList, 1);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        deliverCompanyColleagueListLiveData.postValue(new GeekDeliveryListModel(page, null));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                        if (callback != null) {
                            callback.onComplete();
                        }
                    }
                });
        if (recommendScore > 0 && page > 1) {
            simpleApiRequest.addParam("recommendScore", recommendScore);
        }
        simpleApiRequest.execute();
    }

    /**
     * 获取牛人在线简历
     *
     * @param encryptGeekId 加密的牛人id
     * @param encryptGeekId 加密的期望id
     */
    public void getGeekOnlineResume(String encryptGeekId, String encryptExpectId, String encryptJobId, ICommonCallback callback) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_GEEK_ONLINE_RESUME_V2)
                .addParam("encryptGeekId", encryptGeekId)
                .addParam("encryptExpectId", encryptExpectId)
                .addParam("encryptLiveId", liveRecordId)
                .addParam("encryptJobId", encryptJobId)
                .setRequestCallback(new SimpleApiRequestCallback<GeekOnlineResumeResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekOnlineResumeResponse> data) {
                        geekResumeLiveData.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (callback != null) {
                            callback.onFail(reason.getErrCode(), reason.getErrReason());
                        }
                    }
                }).execute();
    }

    /**
     * 查询已投递职位列表数据
     */
    public void getJobDeliveredList(int page, ICommonCallback callback) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_JOB_DELIVERED_LIST)
                .addParam("page", page)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveJobDeliveredResponse>() {
                    @Override
                    public void onSuccess(ApiData<LiveJobDeliveredResponse> data) {
                        super.onSuccess(data);
                        jobDeliveredListModelLiveData.setValue(new JobDeliveredListModel(page, data.resp));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        if (callback != null) {
                            callback.onComplete();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        jobDeliveredListModelLiveData.setValue(new JobDeliveredListModel(page, null));
                    }
                }).execute();
    }

    /**
     * 获取代播直播间 牛人意向打投「排行榜」
     */
    public void getProxyLiveGeekIntentionRank(String securityId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_JOB_RANK_LIST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveRecruitScoreListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<LiveRecruitScoreListResponse> data) {
                        super.onSuccess(data);
                        getIntentionListLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取意向值打榜选项列表数据
     */
    public void getJobScoreOptionList(String securityId, String jobName, boolean isNeedRefreshIntentionRank) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_JOB_SCORE_OPTION_LIST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<JobScoreOptionResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<JobScoreOptionResponse> data) {
                        super.onSuccess(data);
                        intentionHitOptionLiveData.postValue(new IntentionHitOptionModel(data.resp, jobName, securityId, isNeedRefreshIntentionRank));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取公司打投选项列表数据
     */
    public void getCompanyScoreOptionList(String securityId, String companyName) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_JOB_SCORE_OPTION_LIST)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<JobScoreOptionResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<JobScoreOptionResponse> data) {
                        super.onSuccess(data);
                        companyHitOptionLiveData.postValue(new IntentionHitOptionModel(data.resp, companyName, securityId, false));
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 对职位打投
     */
    public void jobScoreDevote(String securityId, int score, boolean isNeedRefreshIntentionRank, ICommonCallback callback) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_JOB_SCORE_DEVOTE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)/*加密ID*/
                .addParam("score", score)/*打投分数*/
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                        ToastUtils.showText("心动值贡献成功");
                        if (isNeedRefreshIntentionRank) {
                            getProxyLiveGeekIntentionRank(securityId);/*查询心动榜数据*/
                        }

                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }


    /**
     * 对公司打投
     */
    public void companyScoreDevote(String securityId, int score, ICommonCallback callback) {
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_PROXY_COMPANY_HIT_DEVOTE)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)/*加密ID*/
                .addParam("score", score)/*打投分数*/
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                        ToastUtils.showText("心动值贡献成功");
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取分组职位
     * 信令84推过来更新火热企业
     */

    public void requestHotCompanyHitData() {
        CompanyHitGroupListRequest geekRecruitDetailRequest = new CompanyHitGroupListRequest(new SimpleCommonApiRequestCallback<CompanyHitGroupListResponse>() {
            @Override
            public void onSuccess(ApiData<CompanyHitGroupListResponse> data) {
                if (null == data) return;
                if (null == data.resp) return;
                int count = LList.getCount(detailResponse.jobGroupList);

                if (count > 0) {
                    String selectGroupId = null;
                    List<JobGroupBean> jobGroupList = new ArrayList<JobGroupBean>();
                    for (int i = 0; i < count; i++) {
                        JobGroupBean element = LList.getElement(detailResponse.jobGroupList, i);
                        if (null != element) {//匹配职位和全部职位重新插上
                            if (element.selected) {
                                selectGroupId = element.groupId;
                            }
                        }
                    }
                    if (LList.isNotEmpty(data.resp.jobGroupList)) {
                        jobGroupList.addAll(data.resp.jobGroupList);
                    }

                    //置顶职位&选中状态重新赋值
                    int count1 = LList.getCount(jobGroupList);
                    for (int i = 0; i < count1; i++) {
                        JobGroupBean element = LList.getElement(jobGroupList, i);
                        if (null != element) {
                            element.isTop = TextUtils.equals(detailResponse.topJobGroupId, element.groupId);
                            element.selected = TextUtils.equals(selectGroupId, element.groupId);
                        }
                    }
                    detailResponse.jobGroupList = jobGroupList;
                    positionLiveData.postValue(detailResponse);
                }
            }
        });
        geekRecruitDetailRequest.liveRecordId = liveRecordId;
        geekRecruitDetailRequest.execute();
    }

    /**
     * 获取答题抽奖 问题&选项
     */
    public void getLuckyDrawQuestion(int realLuckyDrawOrder, ICommonCallback callback) {
        LiveLuckyDrawQuestionQueryRequest request = new LiveLuckyDrawQuestionQueryRequest(shareDataCenter.isProxyBoss, new SimpleCommonApiRequestCallback<LiveLuckyDrawQuestionQueryResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<LiveLuckyDrawQuestionQueryResponse> data) {
                super.onSuccess(data);
                luckyDrawQuestionResponse = data.resp;
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        request.realLuckyDrawOrder = realLuckyDrawOrder;
        HttpExecutor.execute(request);
    }

    public void getDetailTabInfo() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_RECRUIT_DETAIL_TAB_QUERY)
                .addParam("liveRecordId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<RecruitDetailTabResponse>() {
                    @Override
                    public void onSuccess(ApiData<RecruitDetailTabResponse> data) {
                        super.onSuccess(data);
                        recruitDetailTabLiveData.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        recruitDetailTabLiveData.setValue(null);
                    }
                }).execute();
    }

    public void exchangePreCheck(String securityId, IExchangePreCheckCallback callback) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_GEEK_EXCHANGE_PRECHECK_QUERY)
                .addParam("securityId", securityId)
                .addParam("type", 3)// 1电话 2微信 3简历
                .setRequestCallback(new SimpleCommonApiRequestCallback<RecruitExchangePreCheckResponse>() {
                    @Override
                    public void onSuccess(ApiData<RecruitExchangePreCheckResponse> data) {
                        if (callback != null) {
                            if (data.resp != null && data.resp.status == 1 && data.resp.secureExchange != null) {
                                // 直播不展示头图，强制设置为空
                                data.resp.secureExchange.setPictureUrl(null);
                                callback.onSuccess(data.resp.secureExchange);
                            } else {
                                callback.onFail();
                            }
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (callback != null) {
                            callback.onFail();
                        }
                    }
                }).execute();
    }

    /**
     * 如果发送过消息，不触发显示预设弹幕
     */
    public void shouldQueryQuestion() {
        if (isHasSendMsg) return;
        queryQuickQuestion();
    }

    public void playVideo(String playTag, String playUrl, boolean isLooper, BZPlayerHelper.CallBack callBack, int mode, Runnable runnable) {
        if (TextUtils.isEmpty(playUrl)) return;
        if (playerHelper != null && zpViewRenderer != null) {
            // 重复播放 liveData数据 防止重复
            if (TextUtils.equals(playTag, (String) zpViewRenderer.getTag()) && playerHelper.isSameSource(playUrl) && (playerHelper.isPlaying() || playerHelper.isPaused())) {
                if (runnable != null) runnable.run();
                playerHelper.resume();
                TLog.info(TAG, "playVideo  skip");
                return;
            }
            releasePlayerHelper();
        }
        if (zpViewRenderer == null) {
            zpViewRenderer = new ZPViewRenderer(BaseApplication.getApplication());
            zpViewRenderer.setTag(playTag);
            zpViewRenderer.setKeepScreenOn(true);
        }

        playerHelper = new BZPlayerHelper(BaseApplication.getApplication(), callBack, BZPlayerHelper.PlayConfig
                .obj()
                .setUserId(UserManager.getUID())
                .setEventName(ZPEventName.ZP_LIVE));
        playerHelper.setPlayView(zpViewRenderer, mode);
        playerHelper.setPlayUrl(playUrl);
        playerHelper.setLooper(isLooper);

        openingVideoPlaying = TextUtils.equals(playUrl, detailResponse.openingVideoUrl);
    }

    /**
     * 设置播放器的播放速度
     */
    public void setPlaySpeed(float playSpeed) {
        if (playerHelper != null) playerHelper.speed(playSpeed);
    }

    public void releasePlayerHelper() {
        openingVideoPlaying = false;
        if (playerHelper != null) {
            playerHelper.setCallBack(null);
            playerHelper.stop();
            playerHelper.onDestroy();
            playerHelper = null;
        }
        if (zpViewRenderer != null) {
            AudienceUtils.removeFromParent(zpViewRenderer);
            zpViewRenderer.setKeepScreenOn(false);
            zpViewRenderer.release();
            zpViewRenderer = null;
        }
    }

    public String getStreamType() {
        String streamType = "";// 直播还是回放讲解
        if (detailResponse.isLiving()) {
            streamType = isBackLive() ? "2" : "1";
        }
        return streamType;
    }

    public void setMenuTabDot(int tabIndex, int dot) {
        MenuTabModel modelLiveDataValue = menuTabModelLiveData.getValue();
        MenuTabModel.MenuTab menuTab = modelLiveDataValue.getTab(tabIndex);
        if (menuTab != null) {
            //防止重复下发
            if (menuTab.tabDot == dot) {
                return;
            }
        }
        modelLiveDataValue.createTabDot(tabIndex, dot);
        menuTabModelLiveData.postValue(modelLiveDataValue);
    }

    public int netBadCount; // 已结束状态，回放缓存次数

    /**
     * 1、直播开始  开始视频流渲染 计时
     * 2、直播结束  回放视频 计时
     */
    public void startStreamTimer() {
        if (scheduledStreamFuture == null) {
            scheduledStreamFuture = AppThreadFactory.POOL.scheduleAtFixedRate(new Runnable() {
                long seconds;

                @Override
                public void run() {
                    seconds++;
                    if (seconds == 3) {// 3s
                        LiveAnalysisUtil.dotCampusLiveEntry3S(liveRecordId, detailResponse.isLiving() ? 1 : 2, 3);
                    } else if (seconds == 10) {// 10s
                        LiveAnalysisUtil.dotCampusLiveEntry3S(liveRecordId, detailResponse.isLiving() ? 1 : 2, 10);
                    } else if (seconds == 30) {// 30s
                        LiveAnalysisUtil.dotCampusLiveEntryStall(liveRecordId, detailResponse.isLiving() ? 1 : 2, netBadCount);
                    } else {
                        if (seconds > 30) {//*超过30秒后，就不用计时了*/
                            cancelStreamTimer();
                        }
                    }
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    private void cancelStreamTimer() {
        if (scheduledStreamFuture != null) {
            scheduledStreamFuture.cancel(true);
            scheduledStreamFuture = null;
        }
    }

    /**
     * 直播开始 开始计时
     */
    public void startLiveTimer() {
        if (scheduledLiveFuture == null) {
            scheduledLiveFuture = AppThreadFactory.POOL.scheduleAtFixedRate(new Runnable() {
                long seconds;

                @Override
                public void run() {
                    seconds++;
                    if (seconds > 0 && seconds % 60 == 0) {/*时长上报：@since 915调整为 整1分钟就上报一次*/
                        long minutes = seconds / 60;
                        reportDuration(minutes);

                        if (minutes == 1) {// 观看时长达到1分钟
                            if (detailResponse != null && detailResponse.commonConfig != null
                                    && detailResponse.commonConfig.canFocusBrand
                                    && !detailResponse.commonConfig.alreadyFocusBrand // 当前观众未关注当前品牌
                                    && !FloatWindowManager.getInstance().isFloatWindowShow() // 不是浮动窗状态
                            ) {
                                attentionPopLiveData.postValue(detailResponse.liveBrandGuideFollowTip);
                            }
                        }
                    }
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    private void cancelLiveTimer() {
        if (scheduledLiveFuture != null) {
            scheduledLiveFuture.cancel(true);
            scheduledLiveFuture = null;
        }
    }

    /**
     * 直播间变为直播中状态后计时（直播结束后需要继续计时）
     */
    public void startEnterLiveTimer() {
        if (scheduledEnterLiveFuture == null) {
            scheduledEnterLiveFuture = AppThreadFactory.POOL.scheduleAtFixedRate(new Runnable() {
                long seconds;

                @Override
                public void run() {
                    seconds++;
                    /*时间节点（0:3秒，1:10秒，2:1分钟，3:2分钟，4:3分钟，5:4分钟，6:5分钟，7:6分钟，8:7分钟，9:8分钟，10:9分钟，11:10分钟）*/
                    String timeDesc = null;
                    if (seconds == 3) {//3秒
                        timeDesc = "3s";
                    } else if (seconds == 10) {//10秒
                        timeDesc = "10s";
                    } else if (seconds == 20) {//20秒
                        timeDesc = "20s";
                    } else if (seconds == 30) {//30秒
                        timeDesc = "30s";
                    } else if (seconds == 40) {//40秒
                        timeDesc = "40s";
                    } else if (seconds == 50) {//40秒s
                        timeDesc = "50s";
                    } else if (seconds > 0 && seconds % 60 == 0) {
                        int minutes = (int) (seconds / 60);
                        timeDesc = minutes + "min";
                    }
                    if (!TextUtils.isEmpty(timeDesc)) {
                        LiveAnalysisUtil.dotLiveRecruitSpot(liveRecordId, detailResponse.isLiving() ? 0 : 1, timeDesc);//埋点
                    }
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    /**
     * 取消「直播间变为直播中状态后的计时」
     */
    public void cancelEnterLiveTimer() {
        if (scheduledEnterLiveFuture != null) {
            scheduledEnterLiveFuture.cancel(true);
            scheduledEnterLiveFuture = null;
        }
    }

    /**
     * 从第一次进入时恢复数据
     */
    public void revertComments() {
        int count = LList.getCount(commentModel.allCommentList);
        if (count > 500) {// 防止超过1000条，会循环执行删除列表
            commentModel.allCommentList.subList(count - 500, count);
        } else {
            commentModel.initItems(commentModel.allCommentList);
        }
        commentModelLiveData.postValue(commentModel);
    }

    /**
     * 初始化安全文案
     */
    public void initComments() {
        if (!commentModel.initComment) {
            commentModel.initComment = true;
            CommentItemBean itemBean = new CommentItemBean(MsgType.TYPE_COME_IN);
            itemBean.msgSenderId = UserManager.getUID();
            // 手动添加一条自己进入直播间的消息
            commentModel.addItem(itemBean);
            commentModel.allCommentList.add(itemBean);
            if (!TextUtils.isEmpty(detailResponse.welcomeMsg)) { // 欢迎语
                CommentItemBean welcomeBean = new CommentItemBean(MsgType.TYPE_SAFETY_NOTICE);
                welcomeBean.msg = detailResponse.welcomeMsg;
                commentModel.addItem(welcomeBean);

                commentModel.allCommentList.add(welcomeBean);
            }
            if (!TextUtils.isEmpty(detailResponse.warnMsg)) { // 安全文案
                CommentItemBean warnBean = new CommentItemBean(MsgType.TYPE_SAFETY_NOTICE);
                warnBean.msg = detailResponse.warnMsg;
                commentModel.addItem(warnBean);
                commentModel.allCommentList.add(warnBean);
            }
            commentModelLiveData.postValue(commentModel);

            getHistoryMessages();
        }
    }

    public boolean isPlaySucceed() {
        return engineHelper != null && engineHelper.isPlaySucceed();
    }

    public View getLiveView() {
        return engineHelper.getLiveView();
    }

    public LivePlaceHolderView getLivePlaceHolderView() {
        if (livePlaceHolderView == null) {
            livePlaceHolderView = new LivePlaceHolderView(BaseApplication.getApplication());
        }
        return livePlaceHolderView;
    }

    public void leaveRTCRoom() {
        if (engineHelper != null) {
            engineHelper.leaveRoom(false, false);
        }
    }

    public CommentModel getCommentModel() {
        return commentModel;
    }

    /**
     * 减少重复状态发送
     */
    public void notifyLiveState(@LiveState.State int liveState) {
        if (detailResponse.liveState == LiveState.FINISH) {
            TLog.debug(TAG, "invalid old liveState = %d  , liveState = %d ", detailResponse.liveState, liveState);
            return;
        }
        if (detailResponse.liveState != liveState) {
            detailResponse.liveState = liveState;
            responseLiveData.postValue(detailResponse);
            roomInfoLiveData.postValue(detailResponse);
        } else {
            TLog.debug(TAG, "same liveState = %d ", liveState);
        }
    }

    /**
     * 直播，静音与恢复静音
     */
    public void muteLocalAudio(boolean isOpenMute) {
        TLog.info(TAG, "muteLocalAudio：isOpenMute=%d", isOpenMute ? 1 : 0);
        if (engineHelper != null) {
            engineHelper.muteLocalAudio(isOpenMute);
        }
    }

    public void startPlay() {
        TLog.info(TAG, "startPlay liveRecordId = %s", liveRecordId);
        if (engineHelper != null) {
            engineHelper.play();
        }
    }

    public void stopPlay() {
        TLog.info(TAG, "stopPlay liveRecordId = %s", liveRecordId);
        if (engineHelper != null) {
            engineHelper.releasePlayer();
        }
    }

    public void setPlayUrl(String playUrl) {
        if (engineHelper != null) {
            engineHelper.setPlayUrl(playUrl);
        }
    }

    public void reStartPlay() {
        TLog.info(TAG, "reStartPlay liveRecordId = %s", liveRecordId);
        if (engineHelper != null) {
            engineHelper.rePlay();
        }
    }

    public void setPlaySucceed(boolean playSucceed) {
        TLog.info(TAG, "setPlaySucceed liveRecordId = %s", liveRecordId);
        if (engineHelper != null) {
            engineHelper.setPlaySucceed(playSucceed);
        }
    }

    public void leaveSimpleRoom() {
        if (engineHelper != null) {
            engineHelper.leaveRoom(false, true);
            engineHelper = null;
        }
    }

    /**
     * 回放模式
     */
    public boolean isBackLive() {
        return explainingJob != null || explainingChapter != null;
    }

    /**
     * 退出回放模式
     */
    public void exitBackLiveMode() {
        if (isBackLive()) {
            if (explainingJob != null) {
                if (detailResponse.isHighLevelRoom()) {
                    LiveAnalysisUtil.dotCampusLiveVideoLiveBack(liveRecordId, detailResponse.securityId,
                            explainingJob.encryptJobGroupId, 1);
                } else {
                    LiveAnalysisUtil.dotCampusLiveVideoLiveBack(liveRecordId, explainingJob.securityId, "", 1);
                }
                explainJobLiveData.setValue(null);
            } else if (explainingChapter != null) {
                if (detailResponse.isHighLevelRoom()) {
                    LiveAnalysisUtil.dotCampusLiveVideoLiveBack(liveRecordId, explainingChapter.encryptProgrammeChapterId, "", 2);
                }
                explainChapterLiveData.setValue(null);
            }
        }
    }

    /**
     * 更新 其他数据源的 职位信息
     */
    public void updateJobInfo(JobInfoBean jobInfoBean) {
        if (jobInfoBean == null) return;
        if (detailResponse.jobList != null) {
            int indexOf = detailResponse.jobList.indexOf(jobInfoBean);
            if (indexOf > -1) {
                JobInfoBean infoBean = detailResponse.jobList.get(indexOf);
                if (infoBean != null) { // 因为 isTop是本地临时字段
                    jobInfoBean.isTop = infoBean.isTop;
                    jobInfoBean.encryptExplainId = infoBean.encryptExplainId;
                    jobInfoBean.markTime = infoBean.markTime;
                    jobInfoBean.relation = "1";
                    jobInfoBean.deliveredByOnline = true;
                    detailResponse.jobList.set(indexOf, jobInfoBean);
                }
            }
        }
        positionNotifyLiveData.postValue(jobInfoBean);
    }

    /**
     * 通过信令86一更新pk投票
     */
    public void updatePkVoteByMsg(LiveCVotePkBean pkBean) {
        //状态 1进行中 2结束 3停止
        if (null == additionalInfoResponse.liveVoteBO) {
            additionalInfoResponse.liveVoteBO = new LiveCVotePkBean();
        }
        //更新已投状态
        if (pkBean.state == 2) {
            isVotedPk = false;
        }
        /*下发这些信令不更新，已经投票过了 && 停止状态 ）*/
        boolean notRefresh = isVotedPk && (pkBean.state == 3);
        if (!notRefresh) {
            /*别人投票，更新的时候，信令没有给voted，需要手动赋过去*/
            if (pkBean.state == 1 && pkBean.encryptVoteId.equals(additionalInfoResponse.liveVoteBO.encryptVoteId)) {
                LiveCVotePkOptionBean pkOptionBean1 = LList.getElement(additionalInfoResponse.liveVoteBO.voteOptions, 0);
                LiveCVotePkOptionBean pkOptionBean2 = LList.getElement(additionalInfoResponse.liveVoteBO.voteOptions, 1);
                int voted1 = null != pkOptionBean1 ? pkOptionBean1.voted : 0;
                int voted2 = null != pkOptionBean2 ? pkOptionBean2.voted : 0;
                if ((voted1 + voted2) > 0) {
                    if (null != LList.getElement(additionalInfoResponse.liveVoteBO.voteOptions, 0)) {
                        pkBean.voteOptions.get(0).voted = voted1;
                    }
                    if (null != LList.getElement(additionalInfoResponse.liveVoteBO.voteOptions, 1)) {
                        pkBean.voteOptions.get(1).voted = voted2;
                    }
                }
            }
            additionalInfoResponse.liveVoteBO = pkBean;
            pkVoteLiveData.postValue(additionalInfoResponse);
        }
    }

    /**
     * 是否有节目单
     */
    public boolean hasProgrammeChapter() {
        return additionalInfoResponse != null && !LList.isEmpty(additionalInfoResponse.liveProgramme);
    }

    /**
     * 获取「直播切片」列表数据
     */
    @Nullable
    public List<SpeakVideoBean> getSpeakList() {
        return liveExplainListResponse != null ? liveExplainListResponse.list : null;
    }

    /**
     * 直播结束是否显示推荐卡
     */
    public JobInfoBean getJobCardLiveFinish() {
        if (null == liveExplainListResponse) return null;
//        JobInfoBean jobInfoBean = new JobInfoBean();
//        jobInfoBean.open = true;
//        jobInfoBean.name = "fsdf";
//        jobInfoBean.salary = "fsdf";
//        jobInfoBean.address = "fsdf";
//        jobInfoBean.graduateDegree = "fsdf";
//        jobInfoBean.jobId = "fsdfsd";
//        liveExplainListResponse.jobCard = jobInfoBean;
        return liveExplainListResponse.jobCard;
    }

    /**
     * 是否需要显示「直播切片」列表View
     */
    public boolean isNeedShowSpeakVideoListView() {
        return LList.getCount(getSpeakList()) > 0;
    }

    /**
     * 是否显示投票
     */
    public boolean isShowVoteView() {
        return UserManager.isGeekRole() || detailResponse.isLiveCollege();// C端（仅C端观众），直播学院为B端观众
    }

    /**
     * 是否显示直播详情tab
     */
    public boolean showLiveDetailTab() {
        return additionalInfoResponse != null && additionalInfoResponse.showLiveDetailTab && UserManager.isGeekRole();
    }

    /**
     * 是否展示查看更多
     * forceShow : 投票的时候，强制显示更多，因为投票的显示隐藏逻辑和信令给的数据没关系
     */
    public void handleShowMore(boolean forceShow) {
        refreshBtnMoreLiveData.postValue(forceShow || checkShowMore());
    }

    private boolean checkShowMore() {
        //直播间详情
        boolean showLiveDetailTab = showLiveDetailTab();
        //公告
        boolean showAnnouncement = false;
        if (null != announcementModel) {
            showAnnouncement = null != detailResponse && !detailResponse.isPreLive() && (!TextUtils.isEmpty(announcementModel.announcement) || !LList.isEmpty(announcementModel.noticeContentImageList));
        } else {
            showAnnouncement = null != detailResponse && !TextUtils.isEmpty(detailResponse.noticeContent);
        }

        //中奖名单
        boolean showLuckyDrawInfo = false;
        if (null != luckyDrawCModel) {
            LuckyDrawInfoBean luckyDrawInfo = luckyDrawCModel.getLuckyDrawInfo();
            showLuckyDrawInfo = luckyDrawInfo != null && !TextUtils.isEmpty(luckyDrawInfo.noticeContentJumpH5);
        } else {
            showLuckyDrawInfo = null != detailResponse && null != detailResponse.luckyDrawInfo && !TextUtils.isEmpty(detailResponse.luckyDrawInfo.luckyDrawName);
        }

        //回复我
        List<ContactBean> contactList = AudienceUtils.getContactList(replyMeList);
        boolean showReplyMe = LList.isNotEmpty(contactList);

        //投票
        boolean showVote = additionalInfoResponse != null && (additionalInfoResponse.hasVote || isShowPkVoteView()) && isShowVoteView();

        TLog.debug(TAG, "showLiveDetailTab:" + showLiveDetailTab + ",showAnnouncement:" + showAnnouncement + ",showLuckyDrawInfo:" + showLuckyDrawInfo + ",showReplyMe:" + showReplyMe + ",showVote:" + showVote);
        return showLiveDetailTab || showAnnouncement || showLuckyDrawInfo || showReplyMe || showVote;
    }

    /**
     * 是否展示pk投票布局
     * 直播中 && pk投票类型 && 牛人（b进c不显示） && 投票状态是（进行中或者停止）
     * state 状态 1进行中 2结束 3停止
     */
    public boolean isShowPkVoteView() {
        return UserManager.isGeekRole() && additionalInfoResponse != null && null != additionalInfoResponse.liveVoteBO
                && additionalInfoResponse.liveVoteBO.type == 1 && null != detailResponse && detailResponse.isLiving()
                && (additionalInfoResponse.liveVoteBO.state == 1 || additionalInfoResponse.liveVoteBO.state == 3);
    }

    /**
     * 是否是代播报名人
     */
    public boolean isProxyRegister() {
        return null != additionalInfoResponse && additionalInfoResponse.proxyRegister;
    }

    /**
     * 是否显示邀请按钮
     */
    public boolean isShowInviteBtn() {
        return null != detailResponse && detailResponse.isInviteResume();
    }

    /**
     * 查询BOSS回复投递
     */
    public void checkBossDeliverReply() {
        if (!detailResponse.isLiving()) {/*只有直播中或者暂停状态，才进行查询*/
            return;
        }
        App.getRemoteExecutor().execute(new Runnable() {
            @Override
            public void run() {
                List<Long> deliveredBossIdList = new ArrayList<>(detailResponse.deliveredBossIdList);
                if (LList.getCount(deliveredBossIdList) > 0) {
                    List<Long> contactWithEachOther = ContactManager.getInstance().filterContactWithEachOther(deliveredBossIdList);
                    if (LList.getCount(contactWithEachOther) > 0) {/*是否有双聊的boss*/
                        /*显示「回复我」tab*/
                        replyMeList = LiveCommonUtil.getDeliveredBossIds(deliveredBossIdList);
                        replyMeLiveData.postValue(replyMeList);
                        handleShowMore(false);
                        /*查询未读消息数*/
                        int noReadMessageCount = ContactManager.getInstance().getListNoneReadCount(deliveredBossIdList, ContactBean.FROM_BOSS);
                        MenuTabModel modelLiveDataValue = menuTabModelLiveData.getValue();
                        if (modelLiveDataValue != null) {
                            modelLiveDataValue.createTabDot(MenuTabModel.ORDER_REPLY_ME, noReadMessageCount);
                        }
                        menuTabModelLiveData.postValue(modelLiveDataValue);

                        String key = UserManager.getUID() + "_" + liveRecordId;
                        if (!GlobalStatus.liveBossDeliverReplySet.contains(key)) {/*判断本场直播是否已经显示过该气泡，如果未显示过才显示*/
                            /*显示Boss回复投递简历的气泡*/
                            showBossDeliverReplyLiveData.postValue(true);
                            /*记录本场直播显示过该气泡*/
                            GlobalStatus.liveBossDeliverReplySet.add(key);
                        }
                    }
                }
            }
        });
    }

    /**
     * BOSS身份&&非直播学院&&非公司同事
     */
    public boolean isBossAndNotLiveCollegeNotCompanyColleague() {
        return UserManager.isBossRole() && !detailResponse.isLiveCollege() && !shareDataCenter.isCompanyColleague();
    }

    public int getSceneType() {
        return UserManager.isBossRole() ? (detailResponse.isLiveCollege() ? CampusConstant.CommentAdapterUseScene.SCENE_TYPE_BOSS_AUDIENCE_LIVE_COLLEGE :
                CampusConstant.CommentAdapterUseScene.SCENE_TYPE_BOSS_AUDIENCE_ONLINE) :
                CampusConstant.CommentAdapterUseScene.SCENE_TYPE_GEEK_AUDIENCE;
    }

    public boolean isSceneTypeBossAudienceOnline() {
        return getSceneType() == CampusConstant.CommentAdapterUseScene.SCENE_TYPE_BOSS_AUDIENCE_ONLINE;
    }

    /**
     * 是否是可以分享海报的代播BOSS
     * 未报名的代播BOSS不可分享
     */
    public boolean isProxyBossAndCanSharePoster() {
        int canShareProxyPoster = additionalInfoResponse != null ? additionalInfoResponse.canShareProxyPoster : 0;
        return isProxyBoss && canShareProxyPoster == 1;
    }

    public String getJobGroupTopTip() {
        return additionalInfoResponse != null ? additionalInfoResponse.jobGroupTopTips : null;
    }

    /**
     * 更新播放器的进度
     */
    public void updatePlayerSeekTo(int seekBarProgress) {
        if (playerHelper == null) return;
        long total = playerHelper.getDuration();
        long pos = total * seekBarProgress / 100;
        playerHelper.seekTo(pos);
    }

    public int getScrnOrient() {
        int scrnOrient = detailResponse.scrnOrient;
        int liveState = detailResponse.liveState;
        if (liveState == LiveState.DELAY ||/*超时结束（展示横向样式）*/
                /*（已结束&&无回放&&不需要显示「直播切片」View（注：如果有直播切片时会直接播放直播切片，此时不能写死横向样式））（展示横向样式）*/
                (liveState == LiveState.FINISH && TextUtils.isEmpty(detailResponse.liveVideoUrl) && !isNeedShowSpeakVideoListView())) {
            scrnOrient = CampusConstant.CampusStreamOrientation.STREAM_ORIENTATION_LANDSCAPE;
        } else if (detailResponse.isLiving()) {
            if (detailResponse.hasPPTMode() && pptInMainWindow) { //有PPT && PPT在主屏
                scrnOrient = CampusConstant.CampusStreamOrientation.STREAM_ORIENTATION_LANDSCAPE;
            }
        }
        return scrnOrient;
    }

    /**
     * 职位列表浮层高度
     */
    public int getJobBottomSheetDialogHeight() {
        return (int) (shareDataCenter.screenHeight * 0.7);
    }

    /**
     * 职位列表浮层宽度
     */
    public int getJobBottomSheetDialogWidth() {
        return (int) (shareDataCenter.screenWidth * 0.7);
    }

    public boolean isSpeakVideoMode() {
        return videoPlayBackMode == CampusConstant.VideoPlayBackMode.MODE_SPEAK_VIDEO;
    }

    /**
     * 开始投票倒计时
     *
     * @param duration 单位秒
     */
    public void startVotePopTimer(@CampusConstant.VoteCountDownType int type, int duration, String encryptVoteId) {
        cancelVotPopTimer();
        votePopTimer = new CountDownTimer(duration * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int seconds = (int) (millisUntilFinished / 1000);

                VoteInfoBean voteInfoBean = beginVoteInfoLiveData.getValue();
                if (voteInfoBean != null && TextUtils.equals(voteInfoBean.encryptVoteId, encryptVoteId)) {
                    voteInfoBean.remainingTime = (type == CampusConstant.VoteCountDownType.COUNT_DOWN_TYPE_VOTING) ? seconds : 0;
                    voteInfoBean.closeViewTime = (type == CampusConstant.VoteCountDownType.COUNT_DOWN_TYPE_VOTED) ? seconds : CampusConstant.VOTE_CLOSE_TIME;
                }
                voteTimerLiveData.setValue(new VoteTimerCountDownModel(type, millisUntilFinished, encryptVoteId));
            }

            @Override
            public void onFinish() {
                cancelVotPopTimer();

                if (type == CampusConstant.VoteCountDownType.COUNT_DOWN_TYPE_VOTED) {
                    VoteInfoBean voteInfoBean = beginVoteInfoLiveData.getValue();
                    if (voteInfoBean != null && TextUtils.equals(voteInfoBean.encryptVoteId, encryptVoteId)) {
                        beginVoteInfoLiveData.setValue(null);
                    }

                    LiveVoteInfoModel liveVoteInfoModel = updateVoteCountLiveData.getValue();
                    if (liveVoteInfoModel != null && liveVoteInfoModel.voteBean != null && TextUtils.equals(liveVoteInfoModel.voteBean.encryptVoteId, encryptVoteId)) {
                        updateVoteCountLiveData.setValue(null);
                    }
                }
                voteTimerFinishLiveData.setValue(new VoteTimerCountDownFinishModel(type, encryptVoteId));
            }
        }.start();
    }

    public void cancelVotPopTimer() {
        if (votePopTimer != null) {
            votePopTimer.cancel();
            votePopTimer = null;
        }
    }

    /**
     * 刷新礼物列表状态
     */
    public void updateGift(List<GiftBean> giftList) {
        for (int i = 0; i < giftList.size(); i++) {
            GiftBean giftBean = giftList.get(i);
            if (giftBean == null) break;

            if (giftBean.lock && giftBean.unlockConditions == GiftBean.UNLOCK_STAY) {
                // 上锁，停留倒计时
                if (giftBean.stayCountdown > 0) {
                    giftBean.stayCountdown--;
                } else {
                    giftBean.lock = false;

                    if (!TextUtils.isEmpty(liveRecordId)) {
                        /*更新当前礼物状态为已解锁状态*/
                        SpManager.get().user().edit().putBoolean(CampusConstant.KEY_SP_GIFT_IS_UNLOCK + liveRecordId + "_" + giftBean.giftId, true).apply();
                    }
                }
            } else if (!giftBean.lock && giftBean.countdown > 0) {
                // 解锁，冷却倒计时
                giftBean.countdown--;
            }
        }
    }

    private void performGuideLiveData(GeekSpecialColumnLiveRecordListResponse resp) {
        if (resp == null) return;
        List<LiveRecordBean> liveRecordList = resp.liveRecordList;
        LiveRecordBean topLiveRecord = LiveCommonUtil.findTopLiveRecord(liveRecordList);
        detailResponse.liveCnt = LList.getCount(liveRecordList);
        guideLiveLiveData.setValue(new GuideLiveModel(topLiveRecord, liveRecordList));
        responseLiveData.setValue(detailResponse);
    }

    public void destroy() {
        TLog.info(TAG, "destroy====%s", hashCode());
        destroyed = true;
        shareDataCenter.fromSmallWindow = false;
        explainingJob = null;
        explainingChapter = null;
        explainChapterLiveData.setValue(null);
        deliverSuccessLiveData.setValue(null);
        Utils.removeHandlerCallbacks(this::heartBeat);
        releasePlayerHelper();
        cancelLiveTimer();
        cancelStreamTimer();
        cancelEnterLiveTimer();
        cancelVotPopTimer();
        timerUtil.onDestroy();
        // 由于重新连接 IM  评论相关重新发送一次， 防止重复
        commentModel.reset();
        // 退出职位回放讲解
        exitBackLiveMode();
        if (livePlaceHolderView != null) {
            livePlaceHolderView.renderLiveView(null);
            livePlaceHolderView = null;
        }
        if (engineHelper != null) {
            enterRoom(0);
            engineHelper.leaveRoom();
            engineHelper.setListener(null);
            engineHelper = null;
        }
        SpManager.get().user().edit().putLong(CampusConstant.KEY_SP_OTHER_MAX_SCREEN_GIFT_IS_HAVE_SHOW_IN_CURRENT_LIVE + liveRecordId, 0).apply();
        if (null != downLoadGifMaxRunnable) {
            Utils.removeHandlerCallbacks(downLoadGifMaxRunnable);
        }
        instance = null;
    }

    public void checkPreDeliver(String securityId, String encryptJobGroupId, ZPFunction.Fun1<Integer> function) {
        SimpleApiRequest request = SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_LIVE_DELIVER_PRE_CHECK)
                .addParam("liveRecordId", liveRecordId)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<RecruitCheckPreDeliverResponse>() {
                    @Override
                    public void onSuccess(ApiData<RecruitCheckPreDeliverResponse> data) {
                        super.onSuccess(data);
                        int deliveredCount = null != data && null != data.resp ? data.resp.deliveredCount : 0;
                        if (null != function) {
                            function.call(deliveredCount);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (null != function) {
                            function.call(0);
                        }
                    }
                });
        if (!TextUtils.isEmpty(encryptJobGroupId)) {
            request.addParam("groupId", encryptJobGroupId);
        }
        request.execute();
    }


    public JobInfoBean getJobInfoBeanWhenOnlyOneJob() {
        boolean live1208713 = DataStarGray.getInstance().getLive1208_713();
        if (LList.getCount(detailResponse.jobList) == 1 && live1208713) {
            return LList.getElement(detailResponse.jobList, 0);
        }
        return null;
    }

    public JobInfoBean getJobInfoBeanWhenOnlyOneJobLiveFinish() {
        boolean live1208713 = DataStarGray.getInstance().getLive1208_713();
        if (null != detailResponse && LList.getCount(detailResponse.jobList) == 1 && live1208713 && detailResponse.canDeliver) {
            return LList.getElement(detailResponse.jobList, 0);
        }
        return null;
    }

    /**
     * 获取某条「直播切片」讲解视频的播放地址
     */
    public void getExplainPlayUrlSlice(String explainId, String liveRecordId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_GEEK_LIVE_RECRUIT_EXPLAIN_PLAY_URL_QUERY)
                .addParam("liveRecordId", liveRecordId)
                .addParam("explainId", explainId)/*视频讲解ID*/
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveExplainUrlQueryResponse>() {

                    @Override
                    public void onSuccess(ApiData<LiveExplainUrlQueryResponse> data) {
                        if (data.resp == null) return;
                        playVideoLiveData2.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                    }
                }).execute();
    }

    public void requestExplainList(String liveRecordId) {
        LiveExplainListRequest liveExplainListRequest = new LiveExplainListRequest(new SimpleApiRequestCallback<LiveExplainListResponse>() {
            @Override
            public void onSuccess(ApiData<LiveExplainListResponse> data) {
                super.onSuccess(data);
                explainListLiveData.postValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                if (null != reason.getErrReason()) {
                    ToastUtils.showText(reason.getErrReason());
                }
                explainListLiveData.postValue(null);
            }
        });
        liveExplainListRequest.liveRecordId = liveRecordId;
        HttpExecutor.execute(liveExplainListRequest);
    }

    /**
     * 报名信息
     */
    public void requestRegisterInfo(@Nullable JobInfoBean jobInfoBean) {
        if (null == jobInfoBean) return;
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_RECRUIT_REGISTER_INFO)
                .addParam("securityId", jobInfoBean.securityId)
                .addParam("groupId", jobInfoBean.encryptJobGroupId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<LiveRecruitRegisterResponse>() {
                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        recruitRegisterLiveData.postValue(null);
                    }

                    @Override
                    public void onSuccess(ApiData<LiveRecruitRegisterResponse> data) {
                        super.onSuccess(data);
                        recruitRegisterLiveData.postValue(data.resp);
                    }
                }).execute();
    }

    /**
     * 城市信息
     */
    public void requestCity() {
        SimpleApiRequest.GET(URLConfig.URL_CONFIG_CITY)
                .setRequestCallback(new SimpleCommonApiRequestCallback<Get1004CityResponse>() {
                    @Override
                    public void onSuccess(ApiData<Get1004CityResponse> data) {
                        recruitRegisterCityLiveData.postValue(null != data ? data.resp : null);
                    }
                }).execute();
    }

    /**
     * 是否是中介职位
     */
    public boolean isAgentJobType() {
        return null != additionalInfoResponse && additionalInfoResponse.liveJobType == 1;
    }

    public boolean isOnlineNormalLevelRoom() {
        return null != detailResponse && detailResponse.isOnlineRoom() && detailResponse.isNormalLevelRoom();
    }

    private static AudienceDataCenter instance;

    private AudienceDataCenter() {
        shareDataCenter = ShareDataCenter.getInstance();
        initData();
    }

    public static synchronized AudienceDataCenter getInstance() {
        if (instance == null) {
            instance = new AudienceDataCenter();
        }
        return instance;
    }

}
