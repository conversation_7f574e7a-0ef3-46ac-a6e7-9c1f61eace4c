package com.hpbr.bosszhipin.live.boss.live.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.boss.live.adapter.SchoolFinishGeekListAdapter;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.LiveFinishViewModel;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryGeekBean;
import com.hpbr.bosszhipin.live.net.bean.BossDeliveryJobBean;
import com.hpbr.bosszhipin.live.net.response.BossGetDeliveryGeekListResponse;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.ui.decorator.AppDividerDecorator;

import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;

public class LiveFinishGeekRejectListFragment extends BaseFragment {
    public static final String TAG = "LiveFinishGeekRejectListFragment";

    private RecyclerView rvList;
    private SchoolFinishGeekListAdapter adapter;

    private ZPUIStateLayoutManager stateLayoutManager;

    private LiveFinishViewModel viewModel;

    public static LiveFinishGeekRejectListFragment getInstance(Bundle data) {
        LiveFinishGeekRejectListFragment f = new LiveFinishGeekRejectListFragment();
        f.setArguments(data);
        return f;
    }

    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.live_fragment_finish_geek_reject_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        viewModel = LiveFinishViewModel.instance(getActivity());

        rvList = view.findViewById(R.id.rv_list);
        stateLayoutManager = new ZPUIStateLayoutManager(activity, rvList);

        adapter = new SchoolFinishGeekListAdapter(null);
        AppDividerDecorator decorator = new AppDividerDecorator();
        decorator.setDividerColor(ContextCompat.getColor(activity, R.color.transparent));
        decorator.setDividerHeight(Scale.dip2px(activity, 10f));
        rvList.addItemDecoration(decorator);

        View emptyView = LayoutInflater.from(activity).inflate(R.layout.zpui_state_empty_default_layout, null);
        // 缺省页手动设置为深色模式
        ImageView iv_top = emptyView.findViewById(R.id.iv_top);
        iv_top.setImageResource(R.mipmap.ic_empty_page_dark);
        TextView tvTitle = emptyView.findViewById(R.id.tv_title);
        tvTitle.setTextColor(ContextCompat.getColor(activity, R.color.color_FF5E5E5E));
        stateLayoutManager.getEmptyLayout().setCustomView(emptyView);

        adapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                // 跳转到牛人详情页
                BossDeliveryGeekBean geekBean = (BossDeliveryGeekBean) adapter.getItem(position);
                if (geekBean == null) return;
                ParamBean paramBean = new ParamBean();
                paramBean.userId = geekBean.geekId;
                paramBean.jobId = geekBean.jobId;
                paramBean.operation = ParamBean.OPERATION_REFUSE_STATUS;
                paramBean.geekAvatar = geekBean.geekAvatar;
                paramBean.geekName = geekBean.geekName;
                paramBean.securityId = geekBean.securityId;
                paramBean.expectId = geekBean.expectId;
                paramBean.from = ParamBean.FROM_LIVE_DELIVERY_CLOSED_LOOP;
                BossPageRouter.jumpResumeActivity(activity, paramBean);
                viewModel.viewDeliveryGeek(geekBean.geekId);
            }
        });
        adapter.setOnItemChildClickListener((adapter, view1, position) -> {
            if (ClickProtectedUtil.blockClickEvent()) return;
            BossDeliveryGeekBean geekBean = (BossDeliveryGeekBean) adapter.getItem(position);
            if (geekBean == null) return;
            if (view1.getId() == R.id.btn_continue_chat) {// 继续沟通
                SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
                singleChatParam.setJobId(geekBean.jobId);
                singleChatParam.setFriendId(geekBean.geekId);
                singleChatParam.setSecurityId(geekBean.securityId);
                singleChatParam.setExpectId(geekBean.expectId);
                singleChatParam.setChangePositionDesc(geekBean.jobName);
                singleChatParam.setFromDZUser(false);
                SingleRouter.startChat(activity, singleChatParam);
            }
        });
        rvList.setAdapter(adapter);
        viewModel.deliveryGeekListObserver.observe(getViewLifecycleOwner(), new Observer<BossGetDeliveryGeekListResponse>() {
            @Override
            public void onChanged(BossGetDeliveryGeekListResponse response) {
                if (adapter != null) {
                    if (response == null || LList.isEmpty(response.rejectGeekList)) {
                        adapter.setNewData(null);
                        stateLayoutManager.showEmptyScene();
                    } else {
                        adapter.setNewData(response.rejectGeekList);
                        stateLayoutManager.dismiss();
                    }
                }
            }
        });
    }

}
