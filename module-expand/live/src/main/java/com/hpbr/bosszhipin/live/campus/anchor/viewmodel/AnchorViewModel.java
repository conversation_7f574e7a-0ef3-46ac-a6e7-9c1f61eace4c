package com.hpbr.bosszhipin.live.campus.anchor.viewmodel;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.SystemClock;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.bszp.kernel.utils.SingleLiveEvent;
import com.google.zxing.WriterException;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.beauty.bean.PasterConfigBean;
import com.hpbr.bosszhipin.chat.airecruit.itf.IAiLiveGenPromptView;
import com.hpbr.bosszhipin.chat.export.bean.AICommonSceneBundleBean;
import com.hpbr.bosszhipin.chat.export.bean.AiLiveGenPromptTaskBean;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.constant.CommonConstant;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.live.R;
import com.hpbr.bosszhipin.live.bean.AnchorNoticeBean;
import com.hpbr.bosszhipin.live.bean.BaseMessageBean;
import com.hpbr.bosszhipin.live.bean.BossLiveCallConnectItemBean;
import com.hpbr.bosszhipin.live.bean.BossLiveExplainItemBean;
import com.hpbr.bosszhipin.live.bean.CommentItemBean;
import com.hpbr.bosszhipin.live.bean.LiveGeekPostStatusBean;
import com.hpbr.bosszhipin.live.bean.OpItemBean;
import com.hpbr.bosszhipin.live.bean.StrongAlertBean;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.AbsSettingItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorBeautyItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorCameraSwitchItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorComputerItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorFrameMirrorItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorLotteryDraftItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorLotteryResultItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.AnchorPasterItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BCallConnectItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BHighlightItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BIntentionItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BMarkExplainItem;
import com.hpbr.bosszhipin.live.boss.live.dialog.setting.item.BOpenCloseSubtitleItem;
import com.hpbr.bosszhipin.live.boss.live.helper.BossLiveDetailBatchRequestHelper;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.BossJobListModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.ClickApplaudModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.ExplainListModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.HotWordCategoryModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.LuckyDrawBDraftModel;
import com.hpbr.bosszhipin.live.boss.live.mvp.model.LuckyDrawModel;
import com.hpbr.bosszhipin.live.boss.live.viewmodel.BPosterShareQueryRequest;
import com.hpbr.bosszhipin.live.campus.anchor.fragment.AnchorPasterFragment;
import com.hpbr.bosszhipin.live.campus.anchor.model.FinishModel;
import com.hpbr.bosszhipin.live.campus.anchor.view.DialogManager;
import com.hpbr.bosszhipin.live.campus.audience.interfaces.JobExplainType;
import com.hpbr.bosszhipin.live.campus.bean.BossPreLiveStartNowParams;
import com.hpbr.bosszhipin.live.common.SdkInfo;
import com.hpbr.bosszhipin.live.constant.CampusConstant;
import com.hpbr.bosszhipin.live.export.IntentKey;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.export.LiveUrlConfig;
import com.hpbr.bosszhipin.live.export.bean.JobsBean;
import com.hpbr.bosszhipin.live.export.bean.LiveNebulaSdkInfoBean;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.CommentModel;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.MaxScreenModel;
import com.hpbr.bosszhipin.live.geek.audience.mvp.model.PosterShareModel;
import com.hpbr.bosszhipin.live.net.bean.BossDraftBean;
import com.hpbr.bosszhipin.live.net.bean.BossJobGroupBean;
import com.hpbr.bosszhipin.live.net.bean.BossPauseTipBean;
import com.hpbr.bosszhipin.live.net.bean.ExplainForLivingItemBean;
import com.hpbr.bosszhipin.live.net.bean.JobInfoBean;
import com.hpbr.bosszhipin.live.net.bean.RecruitTopMessageBean;
import com.hpbr.bosszhipin.live.net.request.AdminQrCodeRefreshRequest;
import com.hpbr.bosszhipin.live.net.request.BossAdminVerifyCodeRefreshRequest;
import com.hpbr.bosszhipin.live.net.request.BossBlockDialogRequest;
import com.hpbr.bosszhipin.live.net.request.BossChangeMarkSwitchRequest;
import com.hpbr.bosszhipin.live.net.request.BossConfirmPauseTipRequest;
import com.hpbr.bosszhipin.live.net.request.BossDeleteDialogRequest;
import com.hpbr.bosszhipin.live.net.request.BossExplainListRequest;
import com.hpbr.bosszhipin.live.net.request.BossIntentionAwardListRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveCallConnectActionRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveCallConnectKillRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveCallConnectVolumeMuteRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveGetAllDraftRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveGetDeliveryDetailsRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveGetPasterConfigRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveGiftListRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveLuckyDrawPublishRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveParamBeautySwitchRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveParamSwitchRequest;
import com.hpbr.bosszhipin.live.net.request.BossLiveSubtitleSwitchRequest;
import com.hpbr.bosszhipin.live.net.request.BossRecruitTopMessageRequest;
import com.hpbr.bosszhipin.live.net.request.BossReportMarkDialogRequest;
import com.hpbr.bosszhipin.live.net.request.BossTopLiveJobGroupRequest;
import com.hpbr.bosszhipin.live.net.request.ContinueLiveRequest;
import com.hpbr.bosszhipin.live.net.request.EndExplainRequest;
import com.hpbr.bosszhipin.live.net.request.EndLiveRequest;
import com.hpbr.bosszhipin.live.net.request.IntentionRankListRequest;
import com.hpbr.bosszhipin.live.net.request.LiveEndExplainResponse;
import com.hpbr.bosszhipin.live.net.request.LiveStartExplainResponse;
import com.hpbr.bosszhipin.live.net.request.LuckyDrawDetailRequest;
import com.hpbr.bosszhipin.live.net.request.LuckyDrawWinnerRequest;
import com.hpbr.bosszhipin.live.net.request.PlaceTopRequest;
import com.hpbr.bosszhipin.live.net.request.StartExplainRequest;
import com.hpbr.bosszhipin.live.net.request.StartLiveRequest;
import com.hpbr.bosszhipin.live.net.request.SuspendLiveRequest;
import com.hpbr.bosszhipin.live.net.response.AdminQrCodeRefreshResponse;
import com.hpbr.bosszhipin.live.net.response.BossAdminVerifyCodeRefreshResponse;
import com.hpbr.bosszhipin.live.net.response.BossBlockDialogResponse;
import com.hpbr.bosszhipin.live.net.response.BossExplainListResponse;
import com.hpbr.bosszhipin.live.net.response.BossHighlightInfoResponse;
import com.hpbr.bosszhipin.live.net.response.BossIntentionAwardListResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveBeautyMultiConfigResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveCallConnectInviteResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveCallConnectVolumeMuteResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveDetailBatchResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetAllDraftResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetDeliveryDetailsResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetJobGroupDeliveryDetailsResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGetPasterConfigResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveGiftListResposne;
import com.hpbr.bosszhipin.live.net.response.BossLiveGuideInfoResponse;
import com.hpbr.bosszhipin.live.net.response.BossLiveLuckyDrawPublishResponse;
import com.hpbr.bosszhipin.live.net.response.BossLivePreAIScriptResponse;
import com.hpbr.bosszhipin.live.net.response.BossRecruitDetailResponse;
import com.hpbr.bosszhipin.live.net.response.EmptyResponse;
import com.hpbr.bosszhipin.live.net.response.GeekDeliveryListResponse;
import com.hpbr.bosszhipin.live.net.response.GeekOnlineResumeResponse;
import com.hpbr.bosszhipin.live.net.response.IntentionRankListResponse;
import com.hpbr.bosszhipin.live.net.response.InviteResumeInfoResponse;
import com.hpbr.bosszhipin.live.net.response.InviteResumeResponse;
import com.hpbr.bosszhipin.live.net.response.LuckyDrawDetailResponse;
import com.hpbr.bosszhipin.live.net.response.LuckyDrawWinnerResponse;
import com.hpbr.bosszhipin.live.net.response.PlaceTopResponse;
import com.hpbr.bosszhipin.live.net.response.RecruitTopMessageResponse;
import com.hpbr.bosszhipin.live.net.response.SharePosterQueryResponse;
import com.hpbr.bosszhipin.live.net.response.TopicLibResponse;
import com.hpbr.bosszhipin.live.util.ICommonCallback;
import com.hpbr.bosszhipin.live.util.LiveAnalysisUtil;
import com.hpbr.bosszhipin.live.util.LiveState;
import com.hpbr.bosszhipin.live.util.MsgType;
import com.hpbr.bosszhipin.live.video.CampusRTCEngineHelper;
import com.hpbr.bosszhipin.utils.BeautyUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.zxing.encoding.EncodingHandler;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.sdk.nebulartc.constant.NebulaRtcDef;
import com.sdk.nebulartc.view.NebulaRtcView;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.ResultResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class AnchorViewModel extends BaseViewModel {
    private static final String TAG = "AnchorViewModel";

    public String liveRecordId;
    public boolean isExperienceLive;// 是否是"体验直播间"
    public boolean isTrailLive;// 是否是"试播直播间"

    // 网络请求后只赋值一次
    public MutableLiveData<BossRecruitDetailResponse> responseLiveData = new MutableLiveData<>();
    // 主数据
    public MutableLiveData<BossRecruitDetailResponse> mainLiveData = new MutableLiveData<>();
    // 直播间状态变化
    public MediatorLiveData<Integer> liveStatusLiveData = new MediatorLiveData<>();
    // 关闭页面
    public MutableLiveData<FinishModel> finishLiveData = new SingleLiveEvent<>();
    // 错误弹框
    public MutableLiveData<Boolean> errorFinishLiveData = new SingleLiveEvent<>();
    // 横竖屏切换
    public MutableLiveData<Boolean> landscapeLiveData = new SingleLiveEvent<>();
    // 收到直播暂停或继续的信令
    public final MutableLiveData<OpItemBean> suspendAndContinueLiveData = new SingleLiveEvent<>();
    // 直播头部数据，直播时长(秒)
    public MutableLiveData<Integer> headerLiveData = new SingleLiveEvent<>();
    // 直播头部数据，讲解职位时长(秒)
    public MutableLiveData<Integer> explainHeaderLiveData = new SingleLiveEvent<>();
    // 互动列表数据
    public MutableLiveData<CommentModel> commentLiveData = new MutableLiveData<>();
    // 礼物列表
    public MutableLiveData<BossLiveGiftListResposne> giftListLiveData = new SingleLiveEvent<>();
    // 霸屏动画
    public MutableLiveData<MaxScreenModel> commentGiftModelLiveData = new SingleLiveEvent<>();
    // 系统通知
    public MutableLiveData<AnchorNoticeBean> anchorNoticeLiveData = new MutableLiveData<>();
    // 强提醒
    public final MutableLiveData<StrongAlertBean> strongAlertLiveData = new SingleLiveEvent<>();
    // 网络状态
    public MutableLiveData<Integer> netQualityLiveData = new SingleLiveEvent<>();
    // 显示/隐藏直播前除画面外其他view（直播中只处理弹幕和职位列表按钮和功能按钮）
    public MutableLiveData<Boolean> hideBeautyUILiveData = new MutableLiveData<>();
    // 职位列表数据
    public MutableLiveData<BossJobListModel> positionLiveData = new MutableLiveData<>();
    // 置顶职位/职位组
    public MutableLiveData<String> topJobLiveData = new MutableLiveData<>();
    // 置顶消息
    public MutableLiveData<CommentItemBean> topMessageLiveData = new SingleLiveEvent<>();
    // 朋友圈海报
    public MutableLiveData<PosterShareModel> posterShareLiveData = new MutableLiveData<>();
    // 是否需要显示标记讲解图标
    public MutableLiveData<Boolean> markExplainIconLiveData = new MutableLiveData<>();
    // 刷新手机端管理二维码
    public MutableLiveData<Bitmap> refreshQrCodeLiveData = new MutableLiveData<>();
    // 刷新电脑端管理验证码
    public MutableLiveData<String> refreshVerifyCodeLiveData = new MutableLiveData<>();
    // 点赞
    public MutableLiveData<ClickApplaudModel> clickApplaudLiveData = new MutableLiveData<>();
    // 直播奖励列表
    public MutableLiveData<BossIntentionAwardListResponse> intentionAwardListLiveData = new MutableLiveData<>();
    // 心动榜
    public MutableLiveData<IntentionRankListResponse> intentionRankLiveData = new SingleLiveEvent<>();
    // 邀请投递，显示牛人卡片
    public InviteResumeInfoResponse inviteResumeInfoResponse;
    // 弹幕热词分类列表
    public MutableLiveData<HotWordCategoryModel> hotWordsLiveData = new MutableLiveData<>();
    // 求讲解列表
    public MutableLiveData<ExplainListModel> askExplainListLiveData = new MutableLiveData<>();
    // 抽奖状态
    public MutableLiveData<LuckyDrawModel> luckyDrawLiveData = new MutableLiveData<>();
    // 抽奖草稿列表
    public MutableLiveData<LuckyDrawBDraftModel> luckyDrawDraftLiveData = new SingleLiveEvent<>();
    // 抽奖中奖名单
    public MutableLiveData<LuckyDrawWinnerResponse> luckyDrawWinnerLiveData = new MutableLiveData<>();
    /*流量支付成功*/
    public final MutableLiveData<Boolean> livePaySuccessLiveData = new SingleLiveEvent<>();
    // 当前正在讲解的请求
    public MutableLiveData<ExplainForLivingItemBean> curExplainLiveData = new MutableLiveData<>();
    // 邀请连麦链接密码
    public MutableLiveData<BossLiveCallConnectInviteResponse> callConnectInviteLiveData = new MutableLiveData<>();
    // 视频连线中
    public MutableLiveData<BossRecruitDetailResponse> callConnectLiveData = new MutableLiveData<>();
    // 连线状态 0请求连麦 1挂断连麦 2暂停挂麦 3连麦成功
    public MutableLiveData<BossLiveCallConnectItemBean> callConnectStateLiveData = new MutableLiveData<>();
    // 连线网络状态
    public MutableLiveData<NebulaRtcDef.NebulaRtcQuality> otherNetWorkLiveData = new MutableLiveData<>();
    // 连麦 0静音/1恢复
    public MutableLiveData<Integer> volumeStateLive = new MutableLiveData<>();
    // 本场投递列表
    public MutableLiveData<GeekDeliveryListResponse> deliveryListLiveData = new MutableLiveData<>();
    // 本场投递更多列表
    public MutableLiveData<GeekDeliveryListResponse> moreDeliveryListLiveData = new MutableLiveData<>();
    // 牛人在线简历
    public SingleLiveEvent<GeekOnlineResumeResponse> geekResumeLiveData = new SingleLiveEvent<>();
    // 牛人在线简历loading
    public MutableLiveData<Boolean> resumeLoadingLiveData = new MutableLiveData<>();
    // 牛人在线简历failed
    public MutableLiveData<String> resumeFailedLiveData = new MutableLiveData<>();
    // 显示/隐藏流量包icon
    public MutableLiveData<Boolean> showFlowIconLiveData = new SingleLiveEvent<>();
    //通过信令开启讲解，pc或者其他管理员发起
    public MutableLiveData<JobInfoBean> startShowExplainAfterLoadingLiveData = new MutableLiveData<>();
    //通过信令关闭讲解，pc或者其他管理员发起
    public MutableLiveData<BossLiveExplainItemBean> endExplainLiveData = new MutableLiveData<>();
    //加热通知
    public MutableLiveData<AnchorNoticeBean> noticeHeatLiveData = new MutableLiveData<>();
    //个人直播间牛人投递状态更新
    public MutableLiveData<LiveGeekPostStatusBean> geekPostStatusLiveData = new MutableLiveData<>();

    public CampusRTCEngineHelper engineHelper;
    public BossRecruitDetailResponse detailResponse;
    public BossLiveGuideInfoResponse guideInfoResponse;
    public DialogManager dialogManager;

    public long volumeCheckStartTime;// 声音检查的时间戳
    public long liveStartTimestamp;// 开始直播的时间点

    private int lastVolume;// 声音强弱回调，上一次声音大小
    public Bitmap explainSuccessSnapShot;//1009.813 讲解切片成功弹窗中切片第一帧截图
    public int mirrorBeforeSwitchCamera;//摄像头切换之前镜像
    public Runnable refreshNormalList10s;
    public CommentItemBean recommendingJobBean;//正在推职位的id 本地字段
    public boolean isStartNowPreLive = false;//立即开播-直播前 「只有在直播前页面生效，两个页面共用的该model，跳转到AnchorActivity后，该字段失效，用下面的字段判断」
    public BossLiveBeautyMultiConfigResponse beautyResponsePreLiveStartNow;//立即开播-直播前 使用，其他地方无效
    public boolean isBtnStartNowClickedSource = false;//立即开播-直播前，开始直播按钮是否被点击
    public List<JobsBean> selectedJobs;//立即开播-直播前，选择的职位
    public long selectJobTypeCode;//立即开播-直播前，选择的职位tab
    public int liveScenarioPreLive;//立即开播-直播前

    public int liveJobType = 0;// 招聘职位类型：0 普通职位招聘，1 中介职位招聘 1306.711立即开播使用
    public boolean isHighlightSwitchOn = true;// 是否高亮
    public BossHighlightInfoResponse highlightInfoResponse;
    public BossLivePreAIScriptResponse aiScriptResponse;
    public String selectVirtualName;//选中的虚拟背景
    public List<String> oldSelectJobsId;

    public boolean initData(Intent intent) {
        if (intent == null) return false;
        liveRecordId = intent.getStringExtra(IntentKey.INTENT_LIVE_RECORD_ID);
        isExperienceLive = intent.getBooleanExtra(IntentKey.INTENT_LIVE_IS_EXPERIENCE, false);
        initPreLiveStartNowParamsBefore(intent);
        return !TextUtils.isEmpty(liveRecordId);
    }

    public void initAiGenExplain(Activity activity, @Nullable Runnable agreeRunnable, @Nullable Runnable cancelRunnable) {
        if (isLandscape()) {
            if (null != cancelRunnable) {
                cancelRunnable.run();
            }
            return;
        }
        AiLiveGenPromptTaskBean taskBean = new AiLiveGenPromptTaskBean();
        taskBean.encryptLiveId = liveRecordId;
        //如果职位变了，重新生成职位和暖场数据
        List<String> selectJobsId = getSelectJobsId();
        checkPositionChangeThenResetAIInit(selectJobsId);
        oldSelectJobsId = selectJobsId;
        taskBean.encJobIds = selectJobsId;
        AICommonSceneBundleBean aiCommonSceneBundleBean = new AICommonSceneBundleBean();
        if (aiScriptResponse != null) {
            aiCommonSceneBundleBean.reqTimeout = aiScriptResponse.timeoutSeconds;
            aiCommonSceneBundleBean.bizCode = aiScriptResponse.bizCode;
            AnchorAIScriptDataCenter.getInstance().initAiGenExplain(activity, aiCommonSceneBundleBean, taskBean, agreeRunnable, cancelRunnable, new Runnable() {
                @Override
                public void run() {
                    requestBindLiveSessionId();
                }
            });
        }
    }

    /**
     * 如果是立即开播进来的，需要设置一些默认参数
     */
    public void initPreLiveStartNowParamsAfter(Intent intent){
        if (intent == null) return;
        Serializable sStartNowParams = intent.getSerializableExtra(IntentKey.KEY_LIVE_PRE_START_NOW_PARAMS);
        if(null==sStartNowParams)return;
        if (sStartNowParams instanceof BossPreLiveStartNowParams) {
            BossPreLiveStartNowParams startNowParams = (BossPreLiveStartNowParams) sStartNowParams;
            if (LList.getCount(startNowParams.pasterConfigs) > 0) {
                BeautyUtil.instance().setLiveBeautyPasterList(startNowParams.pasterConfigs);
            }
            if (LList.getCount(startNowParams.virtualConfigs) > 0) {
                BeautyUtil.instance().setVirtualBgAdapterData(startNowParams.virtualConfigs);
            }
            if(LList.getCount(startNowParams.makeUpList)>0){
                BeautyUtil.instance().setLiveBeautyMultiMakeUpList(startNowParams.makeUpList);
            }
            if(startNowParams.camara == 1 && null!=engineHelper){
                engineHelper.switchCamera();
                //更新镜像状态
               updateToolFrameMirrorStatus(engineHelper.isFrontCamera());
            }
        }
    }

    public void initPreLiveStartNowParamsBefore(Intent intent){
        if (intent == null) return;
        Serializable sStartNowParams = intent.getSerializableExtra(IntentKey.KEY_LIVE_PRE_START_NOW_PARAMS);
        if(null==sStartNowParams)return;
        if (sStartNowParams instanceof BossPreLiveStartNowParams) {
            BossPreLiveStartNowParams startNowParams = (BossPreLiveStartNowParams) sStartNowParams;
            aiScriptResponse = startNowParams.aiScriptResponse;
        }
    }

    /**
     * 请求直播记录详情
     */
    public void requestRecordDetail(Activity activity) {
        new BossLiveDetailBatchRequestHelper()
                .setUseScene(BossLiveDetailBatchRequestHelper.RequestLiveRecordBatchScene.B_LIVE)
                .requestRecordDetail(liveRecordId, isExperienceLive, new SimpleCommonApiRequestCallback<BossLiveDetailBatchResponse>() {
                    @Override
                    public void onSuccess(ApiData<BossLiveDetailBatchResponse> data) {
                        if (data == null || data.resp == null) return;
                        detailResponse = isExperienceLive ? data.resp.bossExperienceRecruitDetailResponse : data.resp.bossRecruitDetailResponse;
                        if (detailResponse == null || !detailResponse.isSuccess()) {
                            onFailed(new ErrorReason(ErrorReason.CODE_DEFAULT, ErrorReason.ERROR_SERVER_FAILED));
                            finishLiveData.postValue(new FinishModel(false));
                            return;
                        }
                        handleResponse(data.resp, activity);
                    }
                });
    }

    public void handleResponse(@NonNull BossLiveDetailBatchResponse batchResponse, Activity activity) {
        BossLiveGetDeliveryDetailsResponse detailsResponse = batchResponse.bossLiveGetDeliveryDetailsResponse;
        BossLiveGetJobGroupDeliveryDetailsResponse groupDetailsResponse = batchResponse.bossLiveGetJobGroupDeliveryDetailsResponse;
        guideInfoResponse = batchResponse.bossLiveGuideInfoResponse;
        detailResponse = isExperienceLive ? batchResponse.bossExperienceRecruitDetailResponse : batchResponse.bossRecruitDetailResponse;
        highlightInfoResponse = batchResponse.highlightInfoResponse;

        isExperienceLive = detailResponse.liveRoomType == 5;// 服务端要求以接口返回的数据为准，更新是否是体验直播间
        if (detailResponse.roomLevel == 2) {// 高级直播间
            detailResponse.applyJobInfos = null;
            detailResponse.jobGroupInfos = (groupDetailsResponse != null ? groupDetailsResponse.jobGroupList : null);
            detailResponse.jobCount = BossLiveDetailBatchRequestHelper.getJobGroupAllJobNums(detailResponse.jobGroupInfos);
        } else {// 普通直播间
            detailResponse.applyJobInfos = (detailsResponse != null ? detailsResponse.jobList : null);
            detailResponse.jobGroupInfos = null;
            detailResponse.jobCount = LList.getCount(detailResponse.applyJobInfos);
        }

        // 品牌直播间默认竖屏
        if (detailResponse.bzpType == 2 && detailResponse.rollbackStatus != 1
                && !detailResponse.showFlowPacket && detailResponse.liveState == LiveState.WAIT) {
            //1214.972【内容】【直播】美颜改版&特效贴纸，将线下直播间改为默认竖屏
            detailResponse.scrnOrient = 2;
        }

        // AI提示词信息,直播中使用，直播前是通过立即开播直播前传递过来的
        if (null != batchResponse.aiScriptResponse) {
            aiScriptResponse = batchResponse.aiScriptResponse;
            // 如果是立即开播直播间 并且 显示AI提示词，并且iAiGenExplainWarmUpPresenter为空，则重新初始化AI
            if (batchResponse.aiScriptResponse.showEntrance == 1 && detailResponse.isStartNowLive()) {
                TLog.info(TAG, "to living start init ai");
                initAiGenExplain(activity, null,null);
            }
        }

        if (detailResponse.liveState == LiveState.DELAY || detailResponse.liveState == LiveState.FINISH // 直播已结束
                || (detailResponse.liveState == LiveState.PAUSED && detailResponse.suspendTimeRemaining <= 0)) {// 暂停超时结束
            finishLiveData.postValue(new FinishModel(true));
        } else {
            initEngine();
        }
    }

    public void requestNormalJobList(String encryptJobId, Runnable runnable) {
        BossLiveGetDeliveryDetailsRequest bossLiveGetDeliveryDetailsRequest = new BossLiveGetDeliveryDetailsRequest(new SimpleCommonApiRequestCallback<BossLiveGetDeliveryDetailsResponse>() {
            @Override
            public void onSuccess(ApiData<BossLiveGetDeliveryDetailsResponse> data) {
                super.onSuccess(data);
                if (null == detailResponse || null == data) return;
                if (detailResponse.roomLevel == 1) {// 普通直播间
                    detailResponse.applyJobInfos = (data.resp != null ? data.resp.jobList : null);
                    detailResponse.jobGroupInfos = null;
                    detailResponse.jobCount = LList.getCount(detailResponse.applyJobInfos);
                    boolean isShowRecommend = checkShowRecommend();
                    if (!isShowRecommend) {
                        BossJobListModel bossJobListModel = new BossJobListModel(detailResponse);
                        bossJobListModel.encryptJobId = encryptJobId;
                        positionLiveData.postValue(bossJobListModel);
                    }
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                if (null != runnable) {
                    runnable.run();
                }
            }
        });
        bossLiveGetDeliveryDetailsRequest.encryptLiveRecordId = liveRecordId;
        HttpExecutor.execute(bossLiveGetDeliveryDetailsRequest);
    }

    /**
     * 热推标签展示
     */
    public boolean checkShowRecommend() {
        if (null == detailResponse) return false;
        //先清空推职位标志
        if (LList.getCount(detailResponse.applyJobInfos) > 0) {
            for (JobInfoBean jobInfoBean : detailResponse.applyJobInfos) {
                if (null == jobInfoBean) continue;
                jobInfoBean.isRecommend = false;
            }
        }
        if (null == recommendingJobBean || recommendingJobBean.showTime == 0) return false;

        List<JobInfoBean> applyJobInfo = detailResponse.applyJobInfos;
        if (LList.getCount(applyJobInfo) == 0) return false;
        boolean isExplaining = false;
        for (JobInfoBean jobInfoBean : applyJobInfo) {
            if (jobInfoBean.explainRecordStatus == JobExplainType.TYPE_RECORDING) {
                isExplaining = true;
            }
        }
        //正在讲解 > 推职位 ,有正在讲解不显示推职位
        if (isExplaining) return false;

        //先清空延时，如果都是推职位，则不会设置延时
        if (null != refreshNormalList10s) {
            Utils.removeHandlerCallbacks(refreshNormalList10s);
            refreshNormalList10s = null;
        }

        //10s后重置推职位状态
        refreshNormalList10s = new Runnable() {
            @Override
            public void run() {
                if (null == recommendingJobBean) return;
                recommendingJobBean.showTime = 0;
                recommendingJobBean = null;
                requestNormalJobList("", null);
            }
        };
        Utils.runOnUiThreadDelayed(refreshNormalList10s, recommendingJobBean.showTime * 1000);

        //更新职位列表，显示推职位标
        for (JobInfoBean jobInfoBean : applyJobInfo) {
            if (null != jobInfoBean && jobInfoBean.id == recommendingJobBean.jobId && recommendingJobBean.jobId > 0) {
                jobInfoBean.isRecommend = true;
            } else if (null != jobInfoBean) {
                jobInfoBean.isRecommend = false;
            }
        }
        positionLiveData.postValue(new BossJobListModel(detailResponse));
        return true;
    }

    /**
     * 开始推荐职位
     */
    public void requestStartRecommendJob(String encryptJobId, SimpleCommonApiRequestCallback<BossLiveGetDeliveryDetailsResponse> callback) {
        if (null != callback) {
            SimpleApiRequest.POST(LiveUrlConfig.URL_GEEK_LIVE_NOTIFY_PUSH_JOB)
                    .addParam("encryptJobId", encryptJobId)
                    .addParam("encryptLiveRecordId", liveRecordId)
                    .setRequestCallback(callback).execute();
        }
    }

    public void initEngine() {
        TLog.info(TAG, "initEngine liveRecordId = %s", detailResponse.recordId);
        if (isStartNowPreLive) {
            LiveNebulaSdkInfoBean liveNebulaSdkInfoBean = new LiveNebulaSdkInfoBean();
            SdkInfo.NebulaRtcInfo rtcInfo = new SdkInfo.NebulaRtcInfo();
            rtcInfo.userId = String.valueOf(UserManager.getUID());
            liveNebulaSdkInfoBean.rtc = rtcInfo;
            detailResponse.nebulaSdkInfo = liveNebulaSdkInfoBean;
        }
        if (!isStartNowPreLive && (detailResponse.nebulaSdkInfo == null || detailResponse.nebulaSdkInfo.rtc == null)) {
            errorFinishLiveData.postValue(true);
            return;
        }
        showProgress();
        engineHelper = new CampusRTCEngineHelper(App.get(), detailResponse.nebulaSdkInfo,
                detailResponse.liveRoomId, String.valueOf(UserManager.getUID()), detailResponse.playModel);
        engineHelper.setNotNebulaInit(isStartNowPreLive);
        engineHelper.setListener(new CampusRTCEngineHelper.CampusRTCListener(engineHelper) {
            @Override
            public void onEnterRoom(String selfId, long result) {
                super.onEnterRoom(selfId, result);
                hideProgress();
                if (result >= 0) {
                    if (engineHelper != null && !isStartNowPreLive) {
                        engineHelper.initIMSDK();
                    }
                    responseLiveData.postValue(detailResponse);
                } else {
                    errorFinishLiveData.postValue(true);
                }
            }

            @Override
            public void onSwitchRole(int errCode, boolean anchor) {
                super.onSwitchRole(errCode, anchor);
                if (errCode == 0) {
                    if (anchor) {
                        notifyLiveState(LiveState.PUBLISHING);
                    } else {
                        notifyLiveState(LiveState.PAUSED);
                    }
                } else {
                    if (isPublishing()) {// 当前正在直播，但switchRole推流失败
                        ToastUtils.showText("当前网络异常，调试网络后点击恢复直播");
                        suspendLive(null);
                        notifyLiveState(LiveState.PAUSED);
                    }
                }
            }

            @Override
            public void onForceOffline() {
                super.onForceOffline();
                TLog.info(TAG, "onForceOffline");
                // IM被踢下线，直接退出页面
                doExitRoom("开播失败，请重新进入管理端");
            }

            @Override
            public void onUserAudioAvailable(String userId, boolean available) {
                if (isCallConnectRole(userId)) {
                    //连线人的声音
                    volumeStateLive.postValue(available ? 1 : 0);
                }
            }

            @Override
            public void onUserVideoAvailable(String userId, boolean available) {
                super.onUserVideoAvailable(userId, available);
                if (isCallConnectRole(userId)) { //解决连麦中重启App之后显示层级问题
                    engineHelper.setSurfaceViewOnTop(getOtherLiveView(userId));
                }
            }

            @Override
            public void onCustomMessage(String groupId, String senderId, int type, String message) {
                BaseMessageBean baseBean = BaseMessageBean.parseServerMessage(message);
                if (baseBean == null || engineHelper == null || !TextUtils.equals(engineHelper.getLiveRoomId(), baseBean.liveRoomId)) {
                    TLog.error(TAG, "onCustomMessage 消息串了 recruitDetailResponse.liveRoomId = %s, liveRoomId =%s",
                            engineHelper != null ? engineHelper.getLiveRoomId() : "null", baseBean == null ? "null" : baseBean.liveRoomId);
                    return;
                }
                super.onCustomMessage(groupId, senderId, type, message);
                AnchorMessageDispatcher.dispatcherMessage(AnchorViewModel.this, baseBean.msgType, message);
            }

            @Override
            public void onNetworkQuality(NebulaRtcDef.NebulaRtcQuality rtcQuality) {
                super.onNetworkQuality(rtcQuality);
                if (TextUtils.isEmpty(rtcQuality.userId) || isSelf(rtcQuality.userId)) {
                    netQualityLiveData.postValue(rtcQuality.quality);
                } else {
                    otherNetWorkLiveData.setValue(rtcQuality);
                }
            }

            @Override
            public void onUserVoiceVolume(ArrayList<NebulaRtcDef.NebulaRtcVolumeInfo> userVolumes, int totalVolume) {
                super.onUserVoiceVolume(userVolumes, totalVolume);
                if (!canShowStrongAlert()) return;

                StrongAlertBean alertBean = strongAlertLiveData.getValue();
                if (alertBean != null && alertBean.priority < StrongAlertBean.PRIORITY_P3) {// 当前强提醒等级高于声音级别
                    volumeCheckStartTime = 0;// 清空声音检测的时间
                    return;
                }

                NebulaRtcDef.NebulaRtcVolumeInfo selfVolumeInfo = null;
                if (!LList.isEmpty(userVolumes)) {
                    for (NebulaRtcDef.NebulaRtcVolumeInfo info : userVolumes) {
                        // userId为空时表示自己的音量，做了兼容如果userId==当前UID也表示自己的音量
                        if (TextUtils.isEmpty(info.userId) || TextUtils.equals(String.valueOf(UserManager.getUID()), info.userId)) {
                            selfVolumeInfo = info;
                            break;
                        }
                    }
                }

                int volume = selfVolumeInfo != null ? selfVolumeInfo.volume : 0;
                // 第一次符合条件需要显示强提醒
                if ((volume < CampusConstant.MIN_VOLUME_RATE || volume > CampusConstant.MAX_VOLUME_RATE) && volumeCheckStartTime == 0) {
                    volumeCheckStartTime = System.currentTimeMillis();
                }
                if (volume < CampusConstant.MIN_VOLUME_RATE) {
                    if (lastVolume > CampusConstant.MAX_VOLUME_RATE) {// 上一次声音大，这一次声音小，重置检测时间为当前时间
                        volumeCheckStartTime = System.currentTimeMillis();
                        checkDismissVolumeAlert(alertBean);
                    } else if (lastVolume < CampusConstant.MIN_VOLUME_RATE && (System.currentTimeMillis() - volumeCheckStartTime) > CampusConstant.VOLUME_SPAN_DURATION) {// 上一次和这一次声音都小于阈值，且时间超过15s
                        strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P3, "声音过小，请提高音量"));
                    }
                    lastVolume = volume;
                } else if (volume > CampusConstant.MAX_VOLUME_RATE) {
                    if (lastVolume > CampusConstant.MAX_VOLUME_RATE) {// 上一次声音小，这一次声音大，重置检测时间为当前时间
                        volumeCheckStartTime = System.currentTimeMillis();
                        checkDismissVolumeAlert(alertBean);
                    } else if (lastVolume > CampusConstant.MAX_VOLUME_RATE && (System.currentTimeMillis() - volumeCheckStartTime) > CampusConstant.VOLUME_SPAN_DURATION) {// 上一次和这一次声音都小大于阈值，且时间超过15s
                        strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P3, "声音过大，请降低音量"));
                    }
                    lastVolume = volume;
                } else {
                    volumeCheckStartTime = 0;// 重置声音检测的时间
                    lastVolume = 0;
                    checkDismissVolumeAlert(alertBean);
                }
            }

            @Override
            public void onFaceDetected(List<NebulaRtcDef.NebulaRtcFaceInfo> faceInfoList) {
                super.onFaceDetected(faceInfoList);
                if (!canShowStrongAlert()) return;
                if (!LList.isEmpty(faceInfoList)) {
                    NebulaRtcDef.NebulaRtcFaceInfo largestFaceInfo = null;
                    for (NebulaRtcDef.NebulaRtcFaceInfo faceInfo : faceInfoList) {
                        if (faceInfo == null) continue;
                        if (largestFaceInfo == null || largestFaceInfo.faceRatio < faceInfo.faceRatio) {
                            largestFaceInfo = faceInfo;
                        }
                    }

                    if (largestFaceInfo == null) return;

                    StrongAlertBean alertBean = strongAlertLiveData.getValue();
                    if (alertBean != null && alertBean.priority < StrongAlertBean.PRIORITY_P1) {// 当前强提醒等级高于人脸级别
                        return;
                    }
                    //人脸面积 / 屏幕面积 在 5%~30% 范围内时消失
                    if (largestFaceInfo.faceRatio > 0.3f) {
                        strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P1, "请远离摄像头"));
                    } else if (largestFaceInfo.faceRatio < 0.05f) {
                        strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P1, "请靠近摄像头"));
                    } else {
                        // 如果有人脸提醒，则隐藏
                        if (alertBean != null && alertBean.priority == StrongAlertBean.PRIORITY_P1) {
                            strongAlertLiveData.postValue(null);
                        }
                    }
                }
            }

            @Override
            public void onBrightnessDetected(int brightness) {
                super.onBrightnessDetected(brightness);
                if (!canShowStrongAlert()) return;

                StrongAlertBean alertBean = strongAlertLiveData.getValue();
                if (alertBean != null && alertBean.priority < StrongAlertBean.PRIORITY_P4) {// 当前强提醒等级高于画面明暗级别
                    return;
                }

                if (brightness > 65) {// 画面太亮
                    strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P4, "请降低室内亮度"));
                } else if (brightness < 35) {// 画面太暗
                    strongAlertLiveData.postValue(new StrongAlertBean(StrongAlertBean.PRIORITY_P4, "请调高室内亮度"));
                } else {
                    // 如果有画面明暗提醒，则隐藏
                    if (alertBean != null && alertBean.priority == StrongAlertBean.PRIORITY_P4) {
                        strongAlertLiveData.postValue(null);
                    }
                }
            }

            @Override
            public void onError(int errCode, String errMsg) {
                errorFinishLiveData.postValue(true);
            }

            @Override
            public void onIMError(int errCode, String errMsg) {
                errorFinishLiveData.postValue(true);
            }

            @Override
            public void onSwitchCameraFinish(boolean isFrontCamera) {
                clickSwitchCamera();
            }

            @Override
            public void onRefuseBluePermission() {
                needAddBluePermissionSysComment();
            }
        });
        engineHelper.startInitSDK();
        // 加入房间
        if (!engineHelper.isJoinRoom() && !isStartNowPreLive) {
            engineHelper.enterRoom();
            handleVirtualBgSelectForResp();
        }
        //立即开播，直接去调用startLocalPreview,通过responseLiveData去刷新
        if (isStartNowPreLive) {
            hideProgress();
            responseLiveData.postValue(detailResponse);
        }
    }

    /**
     * 是否是连线人
     *
     * @param userId
     * @return
     */
    public boolean isCallConnectRole(String userId) {
        return detailResponse.callonUserId != null && TextUtils.equals(detailResponse.callonUserId, userId);
    }

    private void doExitRoom(String cs) {
        ToastUtils.showText(cs);
        finishLiveData.postValue(new FinishModel(false));
    }

    public void initResponse() {
        isTrailLive = detailResponse.liveRoomType == 3;

        if (detailResponse.scrnOrient == 2) {// 一进入页面即竖屏直播状态
            engineHelper.setVideoEncoderResolutionMode(NebulaRtcDef.NEBULA_RTC_VIDEO_RESOLUTION_MODE_PORTRAIT);
        }

        liveStatusLiveData.removeSource(mainLiveData);
        liveStatusLiveData.addSource(mainLiveData, new Observer<BossRecruitDetailResponse>() {
            @Override
            public void onChanged(BossRecruitDetailResponse response) {
                liveStatusLiveData.setValue(response.liveState);
            }
        });
        // 横竖屏
        landscapeLiveData.postValue(detailResponse.scrnOrient == 1);
        // 职位列表
        positionLiveData.postValue(new BossJobListModel(detailResponse));
        // 置顶职位/职位组
        topJobLiveData.postValue(detailResponse.roomLevel == 1 ? detailResponse.topEncryptJobId : detailResponse.topEncryptJobGroupId);

        // 当前正在录制的讲解（录制过程中崩溃会走）
        if (detailResponse.curExplain != null) {
            // 延迟1000ms，防止横竖屏切换与录制讲解同时执行时UI异常
            App.get().getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    postExplainAndSnapshot(detailResponse.curExplain);
                }
            }, 1000);
        }

        if (isLiving()) {
            if (detailResponse.pauseTipInfo != null) {// 有暂停强提醒弹框
                BossPauseTipBean pauseTipInfo = detailResponse.pauseTipInfo;
                AnchorNoticeBean noticeBean = new AnchorNoticeBean();
                noticeBean.msg = pauseTipInfo.msgContent;
                noticeBean.isPause = true;
                noticeBean.popUp = true;
                noticeBean.endRemainingTime = pauseTipInfo.endRemainingTime;
                noticeBean.readRemainingTime = pauseTipInfo.readRemainingTime;
                anchorNoticeLiveData.postValue(noticeBean);
            }

            if (detailResponse.showAddFlowButton == 1) {// 购买流量入口
                showFlowIconLiveData.postValue(true);
            }
        }
        initTools();
        requestOtherData();
    }

    public boolean started;// 是否已调用startPublish

    public void startPublish() {
        if (!started) {
            started = true;
            TLog.info(TAG, "startPublish");
            // 自研 必须要 先 startLocalPreview 后 switchRole
            engineHelper.startLocalPreview();
            setFrameMirror();
            engineHelper.switchRole(NebulaRtcDef.NEBULA_RTC_ROLE_CODE_ANCHOR);// 推流

            startLive();
            startLiveTimer();
        }
    }

    public void startOtherRemoteView(String uid) {
        if (engineHelper != null) {
            engineHelper.startOtherRemoteView(uid);
        }
    }

    public void stopOtherRemoteView() {
        if (engineHelper != null) {
            engineHelper.stopOtherRemoteView();
        }
    }

    public boolean isUserForbiddenVoice(String uid) {
        if (engineHelper != null) {
            return engineHelper.isUserForbiddenVoice(uid);
        }
        return true;
    }

    /**
     * 开始直播，在首次开播时需要确保接口调用成功
     */
    public void startLive() {
        StartLiveRequest request = new StartLiveRequest(new SimpleCommonApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                notifyLiveState(LiveState.PUBLISHING);
                volumeCheckStartTime = System.currentTimeMillis();
                liveStartTimestamp = System.currentTimeMillis();
                // 直播中，审核通过，且有职位时才显示标记讲解开关
                markExplainIconLiveData.postValue(detailResponse.roomLevel == 2 && detailResponse.auditState == CampusConstant.CampusLiveAuditState.PASS
                        && detailResponse.jobCount > 0);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                doExitRoom(reason.getErrReason());
            }
        });
        request.recordId = detailResponse.recordId;
        request.scrnOrient = isLandscape() ? 1 : 2;
        request.resolution = detailResponse.resolution;
        request.virtualBackground = selectVirtualName;
        HttpExecutor.execute(request);
    }

    /**
     * 暂停直播
     * 为了和IOS保持一致，主动暂停不需要等接口成功，24信令需要等接口成功
     *
     * @param callback 为null表示不用等接口成功继续执行后续流程，不为null表示需要等接口成功再执行后续流程
     */
    public void suspendLive(ICommonCallback callback) {
        if (callback != null) {
            SuspendLiveRequest request = new SuspendLiveRequest(new SimpleCommonApiRequestCallback<HttpResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    showProgress();
                }

                @Override
                public void onSuccess(ApiData<HttpResponse> data) {
                    // 恢复暂停剩余时间，默认暂停1小时则超时结束
                    detailResponse.suspendTimeRemaining = detailResponse.suspendTimeLength;
                    if (callback != null) {
                        callback.onSuccess();
                    }
                }

                @Override
                public void onComplete() {
                    super.onComplete();
                    hideProgress();
                }
            });
            request.recordId = detailResponse.recordId;
            request.liveRoomId = detailResponse.liveRoomId;
            request.callonUserId = detailResponse.callonUserId;
            HttpExecutor.execute(request);
        } else {
            // 恢复暂停剩余时间，默认暂停1小时则超时结束
            detailResponse.suspendTimeRemaining = detailResponse.suspendTimeLength > 0
                    ? detailResponse.suspendTimeLength : detailResponse.suspendTimeLength;

            SuspendLiveRequest request = new SuspendLiveRequest(null);
            request.recordId = detailResponse.recordId;
            request.liveRoomId = detailResponse.liveRoomId;
            request.callonUserId = detailResponse.callonUserId;
            HttpExecutor.execute(request);
        }
    }

    public boolean pauseLastMinute;// 暂停倒计时最后1分钟

    public void continueLive() {
        // 继续直播，接口调用成功之后才执行后续步骤
        continueLive(new ICommonCallback() {
            @Override
            public void onSuccess() {
                if (pauseLastMinute) {
                    ToastUtils.showText("当前距自动结束时间太短，可能导致继续失败，建议您重新预约直播");
                    return;
                }

                if (engineHelper != null) {
                    if (started) {// 开播过
                        engineHelper.resumeAnchor();
                    } else {// 进入页面即暂停状态
                        startPublish();
                    }
                }

                notifyLiveState(LiveState.PUBLISHING);
            }
        });
    }

    /**
     * 继续直播，在首次开播时需要确保接口调用成功
     */
    public void continueLive(ICommonCallback callback) {
        ContinueLiveRequest request = new ContinueLiveRequest(new SimpleCommonApiRequestCallback<HttpResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.recordId = detailResponse.recordId;
        request.scrnOrient = isLandscape() ? 1 : 2;
        HttpExecutor.execute(request);
    }

    /**
     * 结束直播
     */
    public void endLive(ApiRequestCallback<HttpResponse> callback) {
        EndLiveRequest request = new EndLiveRequest(callback);
        request.recordId = detailResponse.recordId;
        request.liveRoomId = detailResponse.liveRoomId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取直播礼物列表
     */
    public void getGiftList() {
        BossLiveGiftListRequest request = new BossLiveGiftListRequest(liveRecordId, new SimpleCommonApiRequestCallback<BossLiveGiftListResposne>() {
            @Override
            public void onSuccess(ApiData<BossLiveGiftListResposne> data) {
                if (data == null || data.resp == null) return;
                giftListLiveData.postValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * 查询分享海报信息
     */
    public void querySharePoster() {
        BPosterShareQueryRequest request = new BPosterShareQueryRequest(new ApiRequestCallback<SharePosterQueryResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<SharePosterQueryResponse> data) {
                posterShareLiveData.postValue(new PosterShareModel(data.resp));
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                posterShareLiveData.postValue(new PosterShareModel(null));
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.execute();
    }

    private void requestTopMessage() {
        BossRecruitTopMessageRequest request = new BossRecruitTopMessageRequest(new SimpleCommonApiRequestCallback<RecruitTopMessageResponse>() {
            @Override
            public void handleInChildThread(ApiData<RecruitTopMessageResponse> data) {
                super.handleInChildThread(data);
                if (data == null || data.resp == null || data.resp.realContent == null) return;

                RecruitTopMessageBean realContent = data.resp.realContent;
                CommentItemBean commentItemBean = new CommentItemBean(MsgType.TYPE_CONTROL_TOP);
                commentItemBean.msgId = realContent.msgId;
                commentItemBean.top = realContent.top;
                commentItemBean.msgSenderId = realContent.msgSenderId;
                commentItemBean.msgSenderName = realContent.msgSenderName;
                commentItemBean.avatarUrl = realContent.msgSenderTiny;
                commentItemBean.msg = realContent.msg;
                commentItemBean.curContentType = realContent.curContentType;
                commentItemBean.timeOutSpaceV2 = realContent.remainingTimeV2;
                topMessageLiveData.postValue(commentItemBean);
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        request.recordId = TextUtils.isEmpty(liveRecordId) ? "" : liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取抽奖状态详情
     */
    public void getLuckyDrawDetail() {
        LuckyDrawDetailRequest luckyDrawDetailRequest = new LuckyDrawDetailRequest(new SimpleCommonApiRequestCallback<LuckyDrawDetailResponse>() {

            @Override
            public void onSuccess(ApiData<LuckyDrawDetailResponse> data) {
                if (data.resp == null) return;
                // 初始化一个真正的结束时间
                data.resp.endTime = SystemClock.elapsedRealtime()
                        + data.resp.leftLuckyDrawReadTime * 1000
                        + data.resp.leftLuckyDrawingTime * 1000;
                luckyDrawLiveData.postValue(new LuckyDrawModel(data.resp, true));
                luckyDrawDraftLiveData.postValue(new LuckyDrawBDraftModel(data.resp.existDraft));
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        luckyDrawDetailRequest.recordId = liveRecordId;
        HttpExecutor.execute(luckyDrawDetailRequest);
    }

    /**
     * 获取求讲解列表
     */
    public void getExplainList() {
        BossExplainListRequest request = new BossExplainListRequest(new SimpleCommonApiRequestCallback<BossExplainListResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossExplainListResponse> data) {
                askExplainListLiveData.postValue(new ExplainListModel(data.resp != null ? data.resp.explainList : null));
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 确认暂停提示信息
     */
    public void confirmPauseTip(ICommonCallback callback) {
        BossConfirmPauseTipRequest request = new BossConfirmPauseTipRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取手机端管理二维码
     */
    public void getAdminQrCode(Activity activity) {
        AdminQrCodeRefreshRequest request = new AdminQrCodeRefreshRequest(new SimpleCommonApiRequestCallback<AdminQrCodeRefreshResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void handleInChildThread(ApiData<AdminQrCodeRefreshResponse> data) {
                super.handleInChildThread(data);
                if (data.resp != null && !TextUtils.isEmpty(data.resp.protocolContent)) {
                    try {
                        Bitmap qrCodeBitmap = EncodingHandler.createQRCode(data.resp.protocolContent,
                                ZPUIDisplayHelper.dp2px(activity, 144));
                        refreshQrCodeLiveData.postValue(qrCodeBitmap);
                    } catch (WriterException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 刷新管理员验证码
     */
    public void refreshAdminVerifyCode(int onlyIfAbsent) {
        BossAdminVerifyCodeRefreshRequest request = new BossAdminVerifyCodeRefreshRequest(
                new SimpleCommonApiRequestCallback<BossAdminVerifyCodeRefreshResponse>() {

                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<BossAdminVerifyCodeRefreshResponse> data) {
                        if (data == null || data.resp == null) return;
                        refreshVerifyCodeLiveData.postValue(data.resp.adminVerifyCode);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                });
        request.encryptLiveRecordId = liveRecordId;
        request.onlyIfAbsent = onlyIfAbsent;
        HttpExecutor.execute(request);
    }

    /**
     * 更改打点标记开关状态
     *
     * @param switchStatus 0 关闭，1 开启
     */
    public void changeMarkSwitch(int switchStatus, boolean first, ICommonCallback callback) {
        BossChangeMarkSwitchRequest request = new BossChangeMarkSwitchRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
                if (callback != null) {
                    callback.onComplete();
                }
            }
        });
        request.liveRecordId = liveRecordId;
        request.switchStatus = switchStatus;
        HttpExecutor.execute(request);

        LiveAnalysisUtil.dotCampusLivePopLiveTag(liveRecordId, first ? 1 : 0, 2, switchStatus);
    }

    /**
     * 上报第一次显示打点确认开关dialog
     */
    public void reportMarkDialog(ICommonCallback callback) {
        BossReportMarkDialogRequest request = new BossReportMarkDialogRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    public void checkRequestTop(String topId) {
        requestTopJobGroup(topId);
    }


    /**
     * 置顶职位组
     */
    public void requestTopJobGroup(String encryptJobGroupId) {
        BossTopLiveJobGroupRequest bossTopLiveJobGroupRequest = new BossTopLiveJobGroupRequest(new SimpleApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                topJobLiveData.postValue(encryptJobGroupId);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        bossTopLiveJobGroupRequest.encryptLiveRecordId = liveRecordId;
        bossTopLiveJobGroupRequest.encryptJobGroupId = encryptJobGroupId;
        bossTopLiveJobGroupRequest.execute();
    }

    /**
     * 获取心动值直播奖励
     */
    public void getIntentionAwardList() {
        BossIntentionAwardListRequest request = new BossIntentionAwardListRequest(new SimpleCommonApiRequestCallback<BossIntentionAwardListResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossIntentionAwardListResponse> data) {
                intentionAwardListLiveData.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 获取「牛人心动榜」数据
     */
    public void getIntentionRankList() {
        IntentionRankListRequest request = new IntentionRankListRequest(new SimpleCommonApiRequestCallback<IntentionRankListResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<IntentionRankListResponse> data) {
                if (data == null || data.resp == null) return;
                intentionRankLiveData.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }
        });
        request.liveRecordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    /**
     * 显示牛人卡片
     */
    public void showGeekInfo(String encryptGeekId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_INVITE_RESUME_GEEKINFO)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("encryptGeekId", encryptGeekId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<InviteResumeInfoResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<InviteResumeInfoResponse> data) {
                        inviteResumeInfoResponse = data.resp;
                        dialogManager.showResumeFragment();
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 邀请投递
     */
    public void inviteDeliver() {
        if (inviteResumeInfoResponse == null || inviteResumeInfoResponse.userInfo == null) return;
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_INVITE_RESUME_GEEK)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("encryptGeekId", inviteResumeInfoResponse.userInfo.encryptGeekId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<InviteResumeResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<InviteResumeResponse> data) {
                        ToastUtils.showText("邀请成功");
                        dialogManager.dismissResumeFragment();
                        LiveAnalysisUtil.campusLiveManageInviteConfirm(liveRecordId, 1,
                                inviteResumeInfoResponse.userInfo.encryptGeekId,
                                inviteResumeInfoResponse.inviteResumeAvailableNum,
                                "");
                        inviteResumeInfoResponse = null;
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        LiveAnalysisUtil.campusLiveManageInviteConfirm(liveRecordId, 1,
                                inviteResumeInfoResponse.userInfo.encryptGeekId,
                                inviteResumeInfoResponse.inviteResumeAvailableNum,
                                reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 【1008-新增】直播连麦-获取连麦验证信息
     */
    public void getCallConnectLinkInfo() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_INVITE_CALL_INFO)
                .addParam("liveId", liveRecordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<BossLiveCallConnectInviteResponse>() {
                    @Override
                    public void onStart() {
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<BossLiveCallConnectInviteResponse> data) {
                        if (null != data.resp) {
                            callConnectInviteLiveData.postValue(data.resp);
                            dialogManager.showInviteCallConnectFragment();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        ToastUtils.showText(reason.getErrReason());
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 【1008-新增】直播连麦-接受/拒绝连麦
     * 确认连线弹框使用
     *
     * @param type 0拒绝 1接受
     */
    public void requestCallConnectAction(String type, String callonUserId) {
        BossLiveCallConnectActionRequest request = new BossLiveCallConnectActionRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                dialogManager.dismissVerifyCallConnectFragment();
                confirmCallConnect("1".equals(type) ? callonUserId : null);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveId = liveRecordId;
        request.callonUserId = callonUserId;
        request.type = type;
        HttpExecutor.execute(request);
    }

    /**
     * 确认连线
     *
     * @param callonUserId
     */
    public void confirmCallConnect(String callonUserId) {
        //连线声音默认值
        if (callonUserId != null) {
            volumeStateLive.setValue(isUserForbiddenVoice(callonUserId) ? 0 : 1);
        }
        if (null != detailResponse) {
            detailResponse.callonUserId = callonUserId;
            callConnectLiveData.setValue(detailResponse);
        }
    }

    /**
     * 【1008-新增】直播连麦-连麦踢人
     */
    public void requestKillCallConnect() {
        BossLiveCallConnectKillRequest request = new BossLiveCallConnectKillRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                if (null != mainLiveData.getValue()) {
                    mainLiveData.getValue().callonUserId = null;
                    callConnectLiveData.setValue(mainLiveData.getValue());
                } else {//兜底结束连麦
                    BossRecruitDetailResponse bossRecruitDetailResponse = new BossRecruitDetailResponse();
                    bossRecruitDetailResponse.callonUserId = null;
                    callConnectLiveData.setValue(bossRecruitDetailResponse);
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveId = liveRecordId;
        request.callonUserId = null != mainLiveData.getValue() ? mainLiveData.getValue().callonUserId : "";
        HttpExecutor.execute(request);
    }

    /**
     * 【1008-新增】直播连麦-静音
     *
     * @param state 0静音 1恢复
     */
    public void requestVolumeMuteCallConnect(int state) {
        BossLiveCallConnectVolumeMuteRequest request = new BossLiveCallConnectVolumeMuteRequest(new SimpleCommonApiRequestCallback<BossLiveCallConnectVolumeMuteResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.liveId = liveRecordId;
        request.state = state;
        request.callonUserId = detailResponse.callonUserId;
        HttpExecutor.execute(request);
    }

    public void getLuckyDrawDraft() {
        BossLiveGetAllDraftRequest request = new BossLiveGetAllDraftRequest(new SimpleCommonApiRequestCallback<BossLiveGetAllDraftResponse>() {

            @Override
            public void onSuccess(ApiData<BossLiveGetAllDraftResponse> data) {
                luckyDrawDraftLiveData.postValue(new LuckyDrawBDraftModel(data.resp));
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                luckyDrawDraftLiveData.postValue(null);
            }
        });
        request.recordId = liveRecordId;
        HttpExecutor.execute(request);
    }

    public void publishDraft(BossDraftBean draft) {
        BossLiveLuckyDrawPublishRequest request = new BossLiveLuckyDrawPublishRequest(new SimpleCommonApiRequestCallback<BossLiveLuckyDrawPublishResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossLiveLuckyDrawPublishResponse> data) {
                ToastUtils.showText("发布成功");
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.recordId = liveRecordId;
        request.luckyDrawName = draft.luckyDrawName;
        request.luckyUserNum = draft.luckyUserNum;
        request.joinCondition = draft.joinCondition;
        request.readyTime = draft.readyTime;
        request.draftId = draft.draftId;
        request.dialogCommand = draft.dialogCommand;
        request.bzlStaffJoin = draft.bzlStaffJoin;
        request.expendIntentionNum = draft.expendIntentionNum;
        HttpExecutor.execute(request);
    }

    /**
     * 获取抽奖中奖名单
     */
    public void getLuckyDrawWinner() {
        LuckyDrawWinnerRequest luckyDrawWinnerRequest = new LuckyDrawWinnerRequest(new SimpleCommonApiRequestCallback<LuckyDrawWinnerResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<LuckyDrawWinnerResponse> data) {
                luckyDrawWinnerLiveData.postValue(data.resp);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        luckyDrawWinnerRequest.recordId = liveRecordId;
        HttpExecutor.execute(luckyDrawWinnerRequest);
    }

    /**
     * 置顶/取消置顶消息
     */
    public void placeTop(long msgId, int flag) {
        PlaceTopRequest placeTopRequest = new PlaceTopRequest(new SimpleCommonApiRequestCallback<PlaceTopResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<PlaceTopResponse> data) {
                if (data != null && data.resp != null) {
                    if (data.resp.result == 1) {
                        if (flag == 1) {// 取消置顶不弹toast，可能是服务端触发
                            ToastUtils.showText("置顶成功");
                        }
                    }
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        placeTopRequest.liveRoomId = detailResponse.liveRoomId;
        placeTopRequest.recordId = liveRecordId;
        placeTopRequest.dialogId = msgId;
        placeTopRequest.flag = flag;
        HttpExecutor.execute(placeTopRequest);
    }

    /**
     * 删除消息
     */
    public void delMessage(long msgId) {
        BossDeleteDialogRequest desChatRequest = new BossDeleteDialogRequest(new SimpleCommonApiRequestCallback<ResultResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

        });
        desChatRequest.encryptLiveRecordId = liveRecordId;
        desChatRequest.dialogId = msgId;
        HttpExecutor.execute(desChatRequest);
    }

    /**
     * 禁言
     */
    public void forbidChat(long geekId, long msgId) {
        BossBlockDialogRequest request = new BossBlockDialogRequest(new SimpleCommonApiRequestCallback<BossBlockDialogResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossBlockDialogResponse> data) {
                if (data.resp != null && data.resp.result) {
                    ToastUtils.showText("禁言成功");
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText("禁言失败：" + reason.getErrReason());
            }
        });
        request.recordId = liveRecordId;
        request.liveRoomId = detailResponse.liveRoomId;
        request.dialogId = String.valueOf(msgId);
        request.geekId = String.valueOf(geekId);
        HttpExecutor.execute(request);
    }

    /**
     * 话题库
     */
    public void getTopicWords() {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_TOPIC_WORDS)
                .addParam("recordId", liveRecordId)
                .setRequestCallback(new SimpleApiRequestCallback<TopicLibResponse>() {
                    @Override
                    public void onSuccess(ApiData<TopicLibResponse> data) {
                        if (data.resp == null) return;

                        HotWordCategoryModel hotWordCategoryModel = hotWordsLiveData.getValue();
                        if (hotWordCategoryModel == null) {
                            hotWordCategoryModel = new HotWordCategoryModel();
                        }
                        hotWordCategoryModel.setTopicLibBeans(data.resp.topicLibrary);
                        hotWordsLiveData.postValue(hotWordCategoryModel);
                    }
                }).execute();

    }

    /**
     * 讲解是否已经标记不再提醒
     *
     * @return
     */
    public boolean hasNotRemindExplain() {
        return SpManager.get().user().getBoolean(CampusConstant.KEY_EXPLAIN_NOT_REMIND, false);
    }

    /**
     * 通过信令开启直播，pc或者其他管理员发起
     *
     * @param jobInfoBean 职位
     */
    public void startExplainByRemote(JobInfoBean jobInfoBean) {
        if (null == jobInfoBean) return;
        startShowExplainAfterLoadingLiveData.postValue(jobInfoBean);
    }

    public void endExplainByRemote(BossLiveExplainItemBean jobInfoBean) {
        if (null == jobInfoBean) return;
        endExplainLiveData.postValue(jobInfoBean);
    }

    public void startExplainWrapper(JobInfoBean jobInfoBean) {
        if (jobInfoBean == null) return;
        startExplain(jobInfoBean.encryptJobId, response -> {
            setJobExplained(jobInfoBean.encryptJobId);
            //有正在讲解，清空推荐职位
            recommendingJobBean = null;
            if (null != refreshNormalList10s) {
                Utils.removeHandlerCallbacks(refreshNormalList10s);
                refreshNormalList10s = null;
            }

            ExplainForLivingItemBean bean = new ExplainForLivingItemBean();
            bean.encryptJobId = jobInfoBean.encryptJobId;
            bean.encryptExplainId = response.encryptExplainId;
            bean.desc = jobInfoBean.name;
            bean.salary = jobInfoBean.salary;
            // 最长录制时间为5分钟
            bean.maxTime = response.maxTime == 0 ? 5 * 60 : response.maxTime;
            bean.triggerStart = true;
            postExplainAndSnapshot(bean);
        });
    }

    public JobInfoBean getJobByExplained(String encryptJobId) {
        //7c096070c9632f6f1nV53dq9EFVYyoq9
        if (!LList.isEmpty(detailResponse.applyJobInfos)) {
            for (JobInfoBean jobInfoBean : detailResponse.applyJobInfos) {
                if (TextUtils.equals(jobInfoBean.encryptJobId, encryptJobId)) {
                    return jobInfoBean;
                }
            }
        }
        return null;
    }

    private void setJobExplained(String encryptJobId) {
        if (!LList.isEmpty(detailResponse.applyJobInfos)) {
            for (JobInfoBean jobInfoBean : detailResponse.applyJobInfos) {
                if (TextUtils.equals(jobInfoBean.encryptJobId, encryptJobId)) {
                    jobInfoBean.explainRecordStatus = JobExplainType.TYPE_RECORDING;
                    break;
                }
            }
        }
    }

    private void postExplainAndSnapshot(ExplainForLivingItemBean bean) {
        curExplainLiveData.postValue(bean);
        if (engineHelper != null) {
            engineHelper.snapshotVideo("", 0, bitmap -> explainSuccessSnapShot = bitmap);
        }
    }

    /**
     * 开始求讲解录制
     */
    public void startExplain(String explainTargetId, StartExplainCallback callback) {
        if (hasNotRemindExplain()) {
            realStartExplain(explainTargetId, callback);
        } else {
            dialogManager.showRecordExplainFragment(() -> realStartExplain(explainTargetId, callback));
        }
    }

    public void realStartExplain(String explainTargetId, StartExplainCallback callback) {
        StartExplainRequest request = new StartExplainRequest(new SimpleApiRequestCallback<LiveStartExplainResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<LiveStartExplainResponse> data) {
                if (callback != null) {
                    callback.onSuccess(data.resp);
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.explainTargetId = explainTargetId;
        request.type = "2";
        HttpExecutor.execute(request);
    }

    public interface StartExplainCallback {
        void onSuccess(LiveStartExplainResponse response);
    }

    public interface EndExplainCallback {
        void onDone(String msg);
    }


    /**
     * 结束求讲解录制
     */
    public void endExplain(String encryptExplainId, int endType, EndExplainCallback callback) {
        EndExplainRequest request = new EndExplainRequest(new SimpleCommonApiRequestCallback<LiveEndExplainResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<LiveEndExplainResponse> data) {
                if (callback != null && data.resp != null) {
                    callback.onDone(data.resp.msg);
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onDone(reason.getErrReason());
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.encryptExplainId = encryptExplainId;
        request.type = endType;
        HttpExecutor.execute(request);
    }

    /**
     * 获取本场投递列表
     *
     * @param deliveryId 最后一个加密的投递id，用于分页
     */
    public void getGeekDeliveryList(String deliveryId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_GEEK_DELIVERY_LIST)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("encryptDeliveryId", deliveryId)
                .setRequestCallback(new SimpleApiRequestCallback<GeekDeliveryListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekDeliveryListResponse> data) {
                        if (!TextUtils.isEmpty(deliveryId)) {
                            moreDeliveryListLiveData.setValue(data.resp);
                        } else {
                            deliveryListLiveData.setValue(data.resp);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                        if (!TextUtils.isEmpty(deliveryId)) {
                            moreDeliveryListLiveData.setValue(null);
                        } else {
                            deliveryListLiveData.setValue(null);
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取直播中牛人投递列表
     * @param deliverType 投递类型：1简历 2微信 3电话
     * @param encryptDeliveryId 加密的投递id（分页参数，如果不传，默认返回第一页数据；如果传且非空，返回小于该id的那一页数据）
     */
    public void getGeekDeliveryListOnline(@LiveConstants.GeekDeliverType int deliverType, String encryptDeliveryId) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_GEEK_DELIVERY_LIST_ONLINE)
                .addParam("encryptLiveRecordId", liveRecordId)
                .addParam("deliverType", deliverType)
                .addParam("encryptDeliveryId", encryptDeliveryId)
                .setRequestCallback(new SimpleApiRequestCallback<GeekDeliveryListResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<GeekDeliveryListResponse> data) {
                        if (!TextUtils.isEmpty(encryptDeliveryId)) {
                            moreDeliveryListLiveData.setValue(data.resp);
                        } else {
                            deliveryListLiveData.setValue(data.resp);
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                        if (!TextUtils.isEmpty(encryptDeliveryId)) {
                            moreDeliveryListLiveData.setValue(null);
                        } else {
                            deliveryListLiveData.setValue(null);
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 获取牛人在线简历
     *
     * @param encryptGeekId 加密的牛人id
     * @param encryptGeekId 加密的期望id
     */
    public void getGeekOnlineResume(String encryptGeekId, String encryptExpectId, String encryptLiveId, int source) {
        resumeLoadingLiveData.setValue(true);
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_GEEK_ONLINE_RESUME)
                .addParam("encryptGeekId", encryptGeekId)
                .addParam("encryptExpectId", encryptExpectId)
                .addParam("encryptLiveId", encryptLiveId)
                .addParam("source", source)//0职位牛人投递列表 1邀请投递列表
                .setRequestCallback(new SimpleApiRequestCallback<GeekOnlineResumeResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekOnlineResumeResponse> data) {
                        geekResumeLiveData.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        resumeFailedLiveData.setValue(reason.getErrReason());
                    }
                }).execute();
    }

    /**
     * 获取牛人在线简历
     *
     * @param encryptGeekId 加密的牛人id
     * @param encryptGeekId 加密的期望id
     */
    public void getGeekOnlineResumeV2(String encryptGeekId, String encryptExpectId, String encryptJobId, int source) {
        SimpleApiRequest.GET(LiveUrlConfig.URL_BOSS_LIVE_GEEK_ONLINE_RESUME_V2)
                .addParam("encryptGeekId", encryptGeekId)
                .addParam("encryptExpectId", encryptExpectId)
                .addParam("encryptLiveId", liveRecordId)
                .addParam("encryptJobId", encryptJobId)
                .addParam("source", source)
                .setRequestCallback(new SimpleApiRequestCallback<GeekOnlineResumeResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekOnlineResumeResponse> data) {
                        geekResumeLiveData.setValue(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        resumeFailedLiveData.setValue(reason.getErrReason());

                    }
                }).execute();
    }

    /**
     * 【1016-新增】直播字幕开关
     *
     * @param state 1打开 0关闭
     */
    public void requestSubtitleSwitch(int state) {
        BossLiveSubtitleSwitchRequest request = new BossLiveSubtitleSwitchRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                ToastUtils.showText(state == 1 ? "已开启字幕" : "已关闭字幕");
                if (null != dialogManager) {
                    dialogManager.hideSubtitleSwitchFragment();
                    if (state == 0) {
                        dialogManager.hideToolsFragment();
                    }
                    //更新字幕开关
                    if (null != detailResponse) {
                        detailResponse.subtitleStatus = state;
                        updateToolsItemDisable(detailResponse.isSubtitleGone(), BOpenCloseSubtitleItem.class);
                        updateToolsItemIconTitle(getSubtitleStatusIcon(), getSubtitleStatusTitle(), BOpenCloseSubtitleItem.class);
                        dialogManager.updateToolsFragment(getEnableTools());
                    }
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.encryptRecordId = liveRecordId;
        request.status = state;
        HttpExecutor.execute(request);
    }

    private void requestOtherData() {
        // 请求置顶消息
        requestTopMessage();
        // 获取抽奖信息
        getLuckyDrawDetail();
        // 求讲解
        getExplainList();

        if (!isExperienceLive) {// 非体验直播间
            // 获取直播礼物列表
            getGiftList();
        }
    }


    /**
     * 点击切换摄像头操作逻辑
     * 如果切到后置摄像头，镜像强制为0
     * 切回来前置，需要恢复镜像状态
     */
    public void clickSwitchCamera() {
        if (engineHelper.isFrontCamera()) {
            //后置摄像头，切换到前置摄像头
            detailResponse.mirror = mirrorBeforeSwitchCamera;
            //恢复前置摄像头之前的镜像状态
            engineHelper.setLocalMirror(detailResponse.mirror);
            engineHelper.setRemoteMirror(detailResponse.mirror);
            //视频镜像的时候顺便镜像一下贴纸
            engineHelper.setPasterMirror(detailResponse.mirror);
        } else {
            mirrorBeforeSwitchCamera = detailResponse.mirror;
            //前置摄像头，切换到后置摄像头
            detailResponse.mirror = 0;
        }
        requestLiveParamSwitch(detailResponse.mirror, engineHelper.isFrontCamera() ? 0 : 1, "");
    }

    /**
     * 点击 关闭/开启 画面镜像
     */
    public void clickFrameMirrorTool() {
        if (!engineHelper.isFrontCamera()) return;

        int mirror = detailResponse.mirror;
        int mirror2 = Math.abs(mirror - 1);
        //后台记录状态，不能依据接口成功来判断是否镜像成功
        requestLiveParamSwitch(mirror2, -1, "");
        //v组sdk镜像
        engineHelper.setLocalMirror(mirror2);
        engineHelper.setRemoteMirror(mirror2);
        //视频镜像的时候顺便镜像一下贴纸
        engineHelper.setPasterMirror(mirror2);

        detailResponse.mirror = mirror2;
        String hintText = mirror2 == 1 ? "打开画面镜像" : "关闭画面镜像";
        ToastUtils.showText(hintText);
    }

    /**
     * 直播参数切换
     */
    public void requestLiveParamSwitch(int mirror, int camera, String beauty) {
        if (null == detailResponse || TextUtils.isEmpty(detailResponse.nebulaId)) return;
        BossLiveParamSwitchRequest request = new BossLiveParamSwitchRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.nebulaId = detailResponse.nebulaId;
        if (mirror != -1) {
            request.mirror = mirror;
        }
        if (camera != -1) {
            request.camera = camera;
        }

        request.characters = detailResponse.subtitleStatus;
        if (!TextUtils.isEmpty(beauty)) {
            request.beauty = beauty;
        }
        request.openBeauty = BeautyUtil.instance().isLiveBeautyOpening() ? 1 : 0;
        HttpExecutor.execute(request);
    }

    /**
     * 直播参数切换
     */
    public void requestLiveBeautyParamSwitch(String beauty) {
        BossLiveParamBeautySwitchRequest request = new BossLiveParamBeautySwitchRequest(new SimpleCommonApiRequestCallback<EmptyResponse>() {
            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                hideProgress();
            }
        });
        request.encryptLiveRecordId = liveRecordId;
        request.beauty = beauty;
        HttpExecutor.execute(request);
    }


    public void initSafetyWords() {
        if (!TextUtils.isEmpty(detailResponse.warnMsg)) { // 安全文案
            CommentModel commentModel = CommentModel.getInstance();
            CommentItemBean warnBean = new CommentItemBean(MsgType.TYPE_SAFETY_NOTICE);
            warnBean.msg = detailResponse.warnMsg;
            commentModel.addItem(warnBean);
            commentLiveData.postValue(commentModel);
        }
    }

    /**
     * 如果没有蓝牙权限，进入B直播需要增加系统消息，排在第一位
     */
    public void needAddBluePermissionSysComment() {
        //直播间评论区系统消息
        CommentModel commentModel = CommentModel.getInstance();
        CommentItemBean warnBean = new CommentItemBean(MsgType.TYPE_SAFETY_NOTICE);
        warnBean.msg = "本次直播暂不支持使用蓝牙设备，如需使用请在手机 设置-> 应用 -> 权限管理中，允许BOSS直聘使用附近设备。";
        commentModel.addItem(warnBean);
        commentLiveData.postValue(commentModel);
    }

    public void showMarkExplainConfirmFirstFragment(String topJobId) {
        dialogManager.showMarkExplainConfirmFirstFragment(topJobId);

        // 调用接口告知服务端，弹出了首次确认框
        reportMarkDialog(new ICommonCallback() {
            @Override
            public void onSuccess() {
                detailResponse.alreadyPopMarkDialog = 1;
            }

            @Override
            public void onFail(int code, String msg) {
                checkRequestTop(topJobId);
                dialogManager.dismissMarkConfirmFirstDialog();
            }
        });
    }

    public NebulaRtcView getLiveView() {
        if (engineHelper != null) {
            return engineHelper.getLiveView(String.valueOf(UserManager.getUID()));
        }
        return null;
    }

    public NebulaRtcView getOtherLiveView(String uid) {
        if (uid == null) {
            return null;
        }
        if (engineHelper != null) {
            return engineHelper.getLiveView(uid);
        }
        return null;
    }

    public boolean isLiving() {
        return detailResponse != null && (detailResponse.liveState == LiveState.PUBLISHING || detailResponse.liveState == LiveState.PAUSED);
    }

    public boolean isPublishing() {
        return detailResponse != null && detailResponse.liveState == LiveState.PUBLISHING;
    }

    public boolean isPaused() {
        return detailResponse != null && detailResponse.liveState == LiveState.PAUSED;
    }

    public boolean isPreLive() {
        return detailResponse != null && detailResponse.liveState == LiveState.WAIT;
    }

    public boolean isFinishLive() {
        return detailResponse != null && (detailResponse.liveState == LiveState.FINISH || detailResponse.liveState == LiveState.DELAY);
    }

    public boolean isLandscape() {
        return detailResponse != null && detailResponse.scrnOrient == 1;
    }

    public void setLandscape(boolean landscape) {
        if (detailResponse != null) {
            detailResponse.scrnOrient = landscape ? 1 : 2;
        }
    }

    /**
     * 是否是线上普通直播间
     */
    public boolean onlineNormalRoom() {
        return detailResponse != null && detailResponse.bzpType == 1 && detailResponse.roomLevel == 1;
    }

    /**
     * 是否是普通直播间
     */
    public boolean isNormalRoom() {
        return detailResponse != null && detailResponse.roomLevel == 1;
    }

    /**
     * 是否隐藏字幕开关
     */
    private boolean isHideSubtitle() {
        return null != detailResponse && detailResponse.hideSubtitleSwitch == 1;
    }


    public void pauseAnchor() {
        TLog.info(TAG, "pauseAnchor");
        if (engineHelper != null && engineHelper.hasPublishing()) {
            engineHelper.pauseAnchor();
        }
        notifyLiveState(LiveState.PAUSED);
    }

    /**
     * 刷新直播状态
     */
    public void notifyLiveState(@LiveState.State int liveState) {
        if (detailResponse.liveState == LiveState.FINISH) {
            TLog.info(TAG, "invalid old liveState=%d, liveState=%d", detailResponse.liveState, liveState);
            return;
        }
        if (detailResponse.liveState != liveState) {// 减少重复状态发送
            detailResponse.liveState = liveState;
            mainLiveData.postValue(detailResponse);
        } else {
            TLog.info(TAG, "same liveState =%d", liveState);
        }
    }

    // 直播开始 计时
    private ScheduledFuture<?> liveTimerFuture;

    /**
     * 直播开始 开始计时
     */
    public void startLiveTimer() {
        if (liveTimerFuture == null) {
            liveTimerFuture = AppThreadFactory.POOL.scheduleAtFixedRate(new Runnable() {
                int seconds;

                @Override
                public void run() {
                    headerLiveData.postValue(seconds++);
                }
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    public void cancelLiveTimer() {
        if (liveTimerFuture != null) {
            liveTimerFuture.cancel(true);
            liveTimerFuture = null;
        }
    }

    /**
     * 是否允许显示强提醒
     */
    public boolean canShowStrongAlert() {
        // 直播中 && 开播了 && (普通直播间 || 试播直播间 || 体验直播间)
        return isPublishing() && liveStartTimestamp > 0// 防止一进入直播间既直播中状态，闪强提醒
                && ((detailResponse != null && detailResponse.roomLevel == 1) || isTrailLive || isExperienceLive);
    }

    /**
     * 录制讲解卡片是否正在显示
     */
    public boolean isExplainCardShowing() {
        return curExplainLiveData.getValue() != null;
    }

    private void checkDismissVolumeAlert(StrongAlertBean alertBean) {
        // 如果有声音提醒，则隐藏
        if (alertBean != null && alertBean.priority == StrongAlertBean.PRIORITY_P3) {
            strongAlertLiveData.postValue(null);
        }
    }

    /**
     * 工具栏item原始数据，包含所有设置选项
     */
    private final List<AbsSettingItem> originToolsList = new ArrayList<>();

    /**
     * 获取工具栏当前展示在界面上的选项
     */
    public List<AbsSettingItem> getEnableTools() {
        List<AbsSettingItem> currentToolsList = new ArrayList<>();
        // 过滤禁用的筛选项
        for (int i = 0; i < originToolsList.size(); i++) {
            AbsSettingItem settingItem = originToolsList.get(i);
            if (null != settingItem && !settingItem.disable()) {
                currentToolsList.add(settingItem);
            }
        }
        return currentToolsList;
    }

    /**
     * 更新工具栏item可用状态
     */
    public void updateToolsItemDisable(boolean isDisable, Class<?>... clazzArray) {
        for (Class<?> clazz : clazzArray) {
            AbsSettingItem settingItem = null;
            for (AbsSettingItem item : originToolsList) {
                if (item.getClass() == clazz) {
                    settingItem = item;
                }
            }
            if (null != settingItem) {
                settingItem.setDisable(isDisable);
            }
        }
    }

    /**
     * 更新工具栏item图标、标题
     */
    public void updateToolsItemIconTitle(int logoResId, String title, Class<?>... clazzArray) {
        for (Class<?> clazz : clazzArray) {
            AbsSettingItem settingItem = null;
            for (AbsSettingItem item : originToolsList) {
                if (item.getClass() == clazz) {
                    settingItem = item;
                }
            }
            if (null != settingItem) {
                settingItem.setLogoResId(logoResId);
                settingItem.setTitle(title);
            }
        }
    }

    /**
     * 更新画面镜像状态
     */
    public void updateToolFrameMirrorStatus(boolean isFrontCamera) {
        for (AbsSettingItem item : originToolsList) {
            if (item.getClass() == AnchorFrameMirrorItem.class) {
                AnchorFrameMirrorItem settingItem = (AnchorFrameMirrorItem) item;
                settingItem.setFrontCamera(isFrontCamera);
            }
        }
    }

    /**
     * 获取工具栏item可用状态
     */
    public boolean isToolsItemVisibleEnable(Class<?>... clazzArray) {
        AbsSettingItem settingItem = null;
        for (Class<?> clazz : clazzArray) {
            for (AbsSettingItem item : originToolsList) {
                if (item.getClass() == clazz) {
                    settingItem = item;
                }
            }
        }
        return null != settingItem;
    }

    /**
     * 获取弹幕开关是否开启
     */
    public boolean isSubtitleStatusOpen() {
        return detailResponse != null && detailResponse.isSubtitleStatusOpen();
    }


    /**
     * 获取弹幕图标
     */
    public int getSubtitleStatusIcon() {
        return detailResponse != null && detailResponse.isSubtitleStatusOpen() ?
                R.mipmap.live_ic_close_subtitle : R.mipmap.live_ic_open_subtitle;
    }

    /**
     * 获取弹幕标题
     */
    public String getSubtitleStatusTitle() {
        return detailResponse != null && detailResponse.isSubtitleStatusOpen() ? "关闭字幕" : "开启字幕";
    }

    /**
     * 初始化镜像，如果强杀应用再进入，需要设置后台返回的镜像状态
     */
    public void setFrameMirror() {
        mirrorBeforeSwitchCamera = detailResponse.mirror;
        engineHelper.setRemoteMirror(detailResponse.mirror);
        engineHelper.setLocalMirror(detailResponse.mirror);
        //视频镜像的时候顺便镜像一下贴纸
        engineHelper.setPasterMirror(detailResponse.mirror);
    }

    /**
     * 是否显示邀请按钮
     */
    public boolean isShowInviteBtn() {
        return null != detailResponse && detailResponse.isInviteResume();
    }


    /**
     * 初始化工具栏所有item
     */
    private void initTools() {
        // 美颜
        AnchorBeautyItem beautyItem = new AnchorBeautyItem();
        //特效
        AnchorPasterItem pasterItem = new AnchorPasterItem();
        // 旋转镜头
        AnchorCameraSwitchItem cameraSwitchItem = new AnchorCameraSwitchItem();
        // 发布抽奖
        AnchorLotteryDraftItem lotteryItem = new AnchorLotteryDraftItem(true);
        // 抽奖结果
        AnchorLotteryResultItem lotteryResultItem = new AnchorLotteryResultItem(true);
        // 电脑端
        AnchorComputerItem computerItem = new AnchorComputerItem();
        // 直播高光
        BHighlightItem highlightItem = new BHighlightItem();
        // 标记讲解
        BMarkExplainItem markExplainItem = new BMarkExplainItem();
        // 牛人心动榜
        BIntentionItem intentionItem = new BIntentionItem(!isNormalRoom() || !isLiving());
        // 视频连线
        BCallConnectItem callConnectItem = new BCallConnectItem(true);
        // 开启/关闭字幕
        BOpenCloseSubtitleItem openCloseSubtitleItem = new BOpenCloseSubtitleItem(isHideSubtitle(), getSubtitleStatusIcon(), getSubtitleStatusTitle());
        //镜像按钮
        AnchorFrameMirrorItem frameMirrorItem = new AnchorFrameMirrorItem(!detailResponse.showMirror, engineHelper.isFrontCamera());
        originToolsList.clear();
        Collections.addAll(originToolsList, beautyItem, pasterItem, cameraSwitchItem, lotteryItem, lotteryResultItem);
        if (null != detailResponse && detailResponse.isNormalLevel() && detailResponse.isOnlineBzpType()) {
            Collections.addAll(originToolsList, highlightItem);
        }else{
            Collections.addAll(originToolsList, computerItem);
        }
        Collections.addAll(originToolsList, markExplainItem, intentionItem, callConnectItem, openCloseSubtitleItem, frameMirrorItem);
    }

    public void addResumeNum(CommentItemBean resumeItemBean) {
        BossJobListModel value = positionLiveData.getValue();
        if (value != null) {
            if (!TextUtils.isEmpty(resumeItemBean.encryptJobGroupId) && !LList.isEmpty(value.detail.jobGroupInfos)) {
                for (BossJobGroupBean jobGroupInfo : value.detail.jobGroupInfos) {
                    if (TextUtils.equals(resumeItemBean.encryptJobGroupId, jobGroupInfo.encryptJobGroupId)) {
                        jobGroupInfo.allDeliverNum++;
                        positionLiveData.postValue(value);
                        break;
                    }
                }
            } else if (!TextUtils.isEmpty(resumeItemBean.encryptJobId) && !LList.isEmpty(value.detail.applyJobInfos)) {
                for (JobInfoBean jobInfoBean : value.detail.applyJobInfos) {
                    if (TextUtils.equals(resumeItemBean.encryptJobId, jobInfoBean.encryptJobId)) {
                        jobInfoBean.allDeliverNum++;
                        positionLiveData.postValue(value);
                        break;
                    }
                }
            }

        }
    }

    public void setDialogManager(DialogManager dialogManager) {
        this.dialogManager = dialogManager;
    }

    /**
     * 更新高亮开关状态
     */
    public void updateHighlightSwitchStatus(Runnable runnable) {
        if (TextUtils.isEmpty(liveRecordId)) {
            if (null != runnable) {
                runnable.run();
            }
            return;
        }
        SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_HIGHLIGHT_SWITCH)
                .addParam("encryptLiveId", liveRecordId)
                .addParam("enableRecordHighlight", isHighlightSwitchOn ? 1 : 0)
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onComplete() {
                        if (null != runnable) {
                            runnable.run();
                        }
                    }
                })
                .execute();
    }

    /**
     * 如果 warmSessionId 和 jobIntroSessionId 都不存在的话，无需绑定，此时不要调接口。接口内部会拦截
     */
    public void requestBindLiveSessionId() {
        String warmUpSessionId = AnchorAIScriptDataCenter.getInstance().warmUpSessionId;
        String jobIntroSessionId = AnchorAIScriptDataCenter.getInstance().jobIntroSessionId;
        if (LText.empty(liveRecordId) || (LText.empty(warmUpSessionId) && LText.empty(jobIntroSessionId))) {
            return;
        }
        SimpleApiRequest request = SimpleApiRequest.POST(LiveUrlConfig.URL_BOSS_LIVE_BIND_SESSION)
                .addParam("encryptLiveId", liveRecordId);
        if(!TextUtils.isEmpty(warmUpSessionId)){
            request.addParam("encryptWarmSessionId", warmUpSessionId);
        }
        if(!TextUtils.isEmpty(jobIntroSessionId)){
            request.addParam("encryptJobIntroSessionId", jobIntroSessionId);
        }
        request.execute();
    }

    public List<String> getSelectJobsId() {
        List<String> selectedJobsId = new ArrayList<>();
        if (null != detailResponse && LList.isNotEmpty(detailResponse.applyJobInfos)) {
            for (JobInfoBean jobInfoBean : detailResponse.applyJobInfos) {
                if (null == jobInfoBean) continue;
                selectedJobsId.add(jobInfoBean.encryptJobId);
            }
            return selectedJobsId;
        }
        if (LList.isNotEmpty(selectedJobs)) {
            for (JobsBean jobsBean : selectedJobs) {
                if (null == jobsBean) continue;
                selectedJobsId.add(jobsBean.encryptJobId);
            }
        }
        return selectedJobsId;
    }

    public List<JobsBean> getSelectJobs() {
        List<JobsBean> selectedJobsTemp = new ArrayList<>();
        if (null != detailResponse && LList.isNotEmpty(detailResponse.applyJobInfos)) {
            for (JobInfoBean jobInfoBean : detailResponse.applyJobInfos) {
                if (null == jobInfoBean) continue;
                JobsBean jobsBean = new JobsBean();
                jobsBean.encryptJobId = jobInfoBean.encryptJobId;
                jobsBean.jobName = jobInfoBean.name;
                selectedJobsTemp.add(jobsBean);
            }
            return selectedJobsTemp;
        }
        return selectedJobs;
    }

    public int getLiveScenario() {
        if (null != detailResponse && detailResponse.liveScenario > 0) {
            return detailResponse.liveScenario;
        } else {
            return liveScenarioPreLive;
        }
    }

    public boolean isShowAIScript() {
        return null != aiScriptResponse && aiScriptResponse.showEntrance == 1;
    }


    /**
     * 处理虚拟背景选择-根据响应数据
     */
    private void handleVirtualBgSelectForResp() {
        // 检查必要条件
        if (null == detailResponse || TextUtils.isEmpty(detailResponse.virtualBackground)) {
            return;
        }

        if (!detailResponse.isLiving()) {
            return;
        }
        selectVirtualName = detailResponse.virtualBackground;
        handleVirtualBgSelectForResp(selectVirtualName);
    }

    public void handleVirtualBgSelectForResp(String virtualName) {
        if (TextUtils.isEmpty(virtualName)) {
            return;
        }
        TLog.info(TAG, "处理虚拟背景: %s" , virtualName);
        selectVirtualName = virtualName;
        // 已有配置数据的情况
        if (!LList.isEmpty(BeautyUtil.instance().getLiveBeautyPasterConfig())) {
            findAndApplyVirtualBackground();
        } else {
            // 需要请求配置数据的情况
            requestAndApplyVirtualBackground();
        }
    }
    
    /**
     * 查找并应用虚拟背景
     */
    private void findAndApplyVirtualBackground() {
        PasterConfigBean selectedItem = findMatchingVirtualBackground();
        BeautyUtil.instance().updateVirtualBgSelect(selectVirtualName);
        if (selectedItem != null) {
            loadAndApplyVirtualBackground(selectedItem);
        } else {
            TLog.info(TAG, "未找到匹配的虚拟背景: %s" , selectVirtualName);
        }
    }
    
    /**
     * 查找匹配的虚拟背景配置
     */
    private PasterConfigBean findMatchingVirtualBackground() {
        List<PasterConfigBean> pasterItems = BeautyUtil.instance().getVirtualBgAdapterData();
        if (LList.isEmpty(pasterItems)) {
            return null;
        }
        
        for (PasterConfigBean pasterItem : pasterItems) {
            if (pasterItem != null && TextUtils.equals(pasterItem.name, selectVirtualName)) {
                return pasterItem;
            }
        }
        return null;
    }

    public String findLandscapePath(boolean isLandscape, List<String> pathList) {
        if (LList.isEmpty(pathList)) return "";
        for (String path : pathList) {
            if (TextUtils.isEmpty(path)) continue;
            if (isLandscape && path.contains("landscape")) {
                return path;
            }
            if (!isLandscape && path.contains("portrait")) {
                return path;
            }
        }
        return "";
    }
    
    /**
     * 加载并应用虚拟背景
     */
    private void loadAndApplyVirtualBackground(PasterConfigBean item) {
        if (item == null || engineHelper == null) {
            return;
        }

        if(TextUtils.equals(item.name, AnchorPasterFragment.TAB_VIRTUAL_BG_NAME)){
            engineHelper.switchMaskBg(1,"");
            return;
        }

        BeautyUtil.instance().loadPasterFiles(CommonConstant.DIR_LIVE_BOSS_CACHE, item, new BeautyUtil.OnLoadResultListener() {
            @Override
            public void OnPasterResult(List<String> pathList) {
                if (null == engineHelper) {
                    return;
                }
                if (LList.isEmpty(pathList)) {
                    selectVirtualName = "";
                    engineHelper.switchMaskBg(0, "");
                    return;
                }
                engineHelper.switchMaskBg(2, findLandscapePath(isLandscape(), pathList));
            }

            @Override
            public void OnMakeUpResult(String openImagePath, String closeImagePath) {
                //ignore
            }

            @Override
            public void OnFailed() {
                TLog.error(TAG, "加载虚拟背景失败");
            }

            @Override
            public void OnLoading() {
                //ignore
            }
        });
    }
    
    /**
     * 请求并应用虚拟背景配置
     */
    private void requestAndApplyVirtualBackground() {
        new BossLiveGetPasterConfigRequest(new SimpleApiRequestCallback<BossLiveGetPasterConfigResponse>() {
            @Override
            public void onSuccess(ApiData<BossLiveGetPasterConfigResponse> data) {
                if (null != data && null != data.resp && !LList.isEmpty(data.resp.backgroundList)) {
                    BeautyUtil.instance().setVirtualBgAdapterData(data.resp.backgroundList);
                    findAndApplyVirtualBackground();
                }
                if (null != data && null != data.resp && !LList.isEmpty(data.resp.pasterList)) {
                    BeautyUtil.instance().setLiveBeautyPasterList(data.resp.pasterList);
                }
            }
            
            @Override
            public void onFailed(ErrorReason reason) {
                TLog.error(TAG, "请求虚拟背景配置失败: %s" , reason.getErrReason());
            }
        }).execute();
    }

    public boolean checkPositionChange(List<String> newSelectJobsId) {
        if (LList.isEmpty(newSelectJobsId) || LList.isEmpty(oldSelectJobsId)) return false;

        List<String> newSelectedJobIds = new ArrayList<>(newSelectJobsId);

        List<String> oldSelectedJobIds = new ArrayList<>(oldSelectJobsId);

        Collections.sort(newSelectedJobIds);
        Collections.sort(oldSelectedJobIds);

        boolean equals = !newSelectedJobIds.equals(oldSelectedJobIds);
        if (equals) {
            TLog.info(TAG, "checkPositionChange true");
        }
        return equals;
    }

    public void checkPositionChangeThenResetAIInit(List<String> selectJobsId) {
        if (!checkPositionChange(selectJobsId)) return;
        AnchorAIScriptDataCenter.getInstance().clear();
    }

    public void startAiGenWarmUpList() {
        IAiLiveGenPromptView iAiGenExplainWarmUpListView = AnchorAIScriptDataCenter.getInstance().iAiGenExplainWarmUpListView;
        if (iAiGenExplainWarmUpListView == null) {
            return;
        }
        List<AiLiveGenPromptTaskBean> taskBeans = new ArrayList<>();
        for (int type = 1; type < 5; type++) {
            AiLiveGenPromptTaskBean bean = new AiLiveGenPromptTaskBean();
            bean.title = getAiLiveGenPromptTitle(type);
            bean.type = type;
            bean.encryptLiveId = liveRecordId;
            bean.liveScenario = getLiveScenario();
            bean.encJobIds = getSelectJobsId();
            taskBeans.add(bean);
        }
        iAiGenExplainWarmUpListView.submitTaskList(taskBeans);
    }

    public void startJobIntroList() {
        IAiLiveGenPromptView iAiGenExplainJobIntroListView = AnchorAIScriptDataCenter.getInstance().iAiGenExplainJobIntroListView;
        if (iAiGenExplainJobIntroListView == null) {
            return;
        }
        if (LList.isEmpty(getSelectJobs())) {
            return;
        }
        List<AiLiveGenPromptTaskBean> taskBeans = new ArrayList<>();
        for (JobsBean selectJob : getSelectJobs()) {
            AiLiveGenPromptTaskBean bean = new AiLiveGenPromptTaskBean();
            bean.title = selectJob.jobName;
            bean.type = 5;
            bean.encryptLiveId = liveRecordId;
            bean.liveScenario = getLiveScenario();
            bean.encJobId = selectJob.encryptJobId;
            taskBeans.add(bean);
        }
        iAiGenExplainJobIntroListView.submitTaskList(taskBeans);
    }

    public String getAiLiveGenPromptTitle(int type) {
        switch (type) {
            case 1: return "开场语";
            case 2: return "讲公司";
            case 3: return "互动";
            case 4: return "结束语";
            case 5: return "讲职位";
            default: return "";
        }
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        //立即开播直播前共用
        if (isStartNowPreLive) {
            if (engineHelper != null) {
                engineHelper.setListener(null);
                engineHelper = null;
            }
            return;
        }
        if (engineHelper != null) {
            engineHelper.leaveRoom();
            engineHelper.setListener(null);
            engineHelper = null;
        }
        if (liveStartTimestamp > 0) {
            LiveAnalysisUtil.campusLiveTryDuration(isExperienceLive, (int) ((System.currentTimeMillis() - liveStartTimestamp) / 1000));
        }
        cancelLiveTimer();
        dialogManager = null;
        SpManager.get().user().edit().putLong(CampusConstant.KEY_SP_OTHER_MAX_SCREEN_GIFT_IS_HAVE_SHOW_IN_CURRENT_LIVE + liveRecordId, 0).apply();
        if (null != refreshNormalList10s) {
            Utils.removeHandlerCallbacks(refreshNormalList10s);
        }
    }

    public AnchorViewModel(@NonNull Application application) {
        super(application);
    }

}
