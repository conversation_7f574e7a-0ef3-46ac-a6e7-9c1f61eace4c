<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    android:focusable="true"
    app:layout_behavior="@string/bottom_sheet_behavior">

    <View
        android:id="@+id/view_top_area"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone" />

    <View
        android:id="@+id/view_left_area"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintWidth_percent="0.5" />

    <zpui.lib.ui.shadow.layout.ZPUIConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/view_top_area"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/view_left_area"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/color_FFFFFFFF">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton
                android:id="@+id/zp_btn"
                android:layout_width="38dp"
                android:layout_height="5dp"
                android:layout_marginTop="5dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:zpui_backgroundColor="@color/live_color_FFCCCCCC"
                app:zpui_radius="3dp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginTop="10dp">

                <ImageView
                    android:id="@+id/iv_title_back"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:paddingLeft="20dp"
                    android:paddingRight="6dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:src="@mipmap/live_ic_recruit_job_back"/>

                <com.hpbr.bosszhipin.views.MTextView
                    android:id="@+id/tv_title"
                    app:layout_constraintLeft_toRightOf="@+id/iv_title_back"
                    app:layout_constraintTop_toTopOf="@+id/iv_title_back"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_title_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:layout_marginBottom="3dp"
                    android:text="牛人详情"
                    android:textColor="@color/color_FF141414"
                    android:textSize="@dimen/text_d2" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:src="@drawable/live_ic_close"
                app:tint="@color/color_balck"
                app:layout_constraintBottom_toBottomOf="@+id/cl_back"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/cl_back" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_top"
            app:layout_constraintBottom_toTopOf="@+id/btn_change"
            android:layout_marginBottom="15dp"
            app:layout_goneMarginBottom="0dp"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/live_inter_resume_item_basic_info" />

        <zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton
            android:id="@+id/btn_change"
            style="@style/zpui_button_green_gray"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="15dp"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="同意交换"/>

    </zpui.lib.ui.shadow.layout.ZPUIConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>