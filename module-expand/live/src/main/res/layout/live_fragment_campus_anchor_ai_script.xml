<?xml version="1.0" encoding="utf-8"?><!-- 主体圆角卡片 -->
<zpui.lib.ui.shadow.layout.ZPUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ai_panel_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_66000000"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:zpui_radius="8dp"
    app:zpui_shadowElevation="8dp">

    <!-- 左侧Tab -->
    <LinearLayout
        android:id="@+id/ll_tab"
        android:layout_width="72dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="12dp"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/tv_ai_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:src="@mipmap/live_ic_ai_hint_title" />

        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="@android:color/transparent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Tab项示例，选中态加粗高亮，左侧指示条 -->
                <RelativeLayout
                    android:id="@+id/rl_tab_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <View
                        android:id="@+id/indicator_tab_1"
                        android:layout_width="5dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:background="@color/color_BOSS6"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tab_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="开场语"
                        android:textColor="@color/color_W_Text_01"
                        android:textSize="15dp"
                        android:textStyle="normal" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_tab_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <View
                        android:id="@+id/indicator_tab_2"
                        android:layout_width="5dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:background="@color/color_BOSS6"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tab_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="讲公司"
                        android:textColor="@color/color_W_Text_01"
                        android:textSize="15dp"
                        android:textStyle="normal" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_tab_3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <View
                        android:id="@+id/indicator_tab_3"
                        android:layout_width="5dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:background="@color/color_BOSS6"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tab_3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="讲职位"
                        android:textColor="@color/color_W_Text_01"
                        android:textSize="15dp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_tab_4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <View
                        android:id="@+id/indicator_tab_4"
                        android:layout_width="5dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:background="@color/color_BOSS6"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tab_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="互动"
                        android:textColor="@color/color_W_Text_01"
                        android:textSize="15dp"
                        android:textStyle="normal" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_tab_5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <View
                        android:id="@+id/indicator_tab_5"
                        android:layout_width="5dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:background="@color/color_BOSS6"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tab_5"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="结束语"
                        android:textColor="@color/color_W_Text_01"
                        android:textSize="15dp"
                        android:textStyle="normal" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>

    <View
        android:id="@+id/view_mid_line"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginTop="41dp"
        android:layout_marginBottom="12dp"
        android:background="@color/color_33FFFFFF"
        app:layout_constraintBottom_toBottomOf="@+id/ll_tab"
        app:layout_constraintStart_toEndOf="@+id/ll_tab"
        app:layout_constraintTop_toTopOf="@+id/ll_tab" />

    <!-- 右侧内容区 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/view_mid_line"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 收起按钮 -->
        <ImageView
            android:id="@+id/iv_collapse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:src="@mipmap/live_ic_ai_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- ViewPager+Fragment 内容区 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</zpui.lib.ui.shadow.layout.ZPUIConstraintLayout>