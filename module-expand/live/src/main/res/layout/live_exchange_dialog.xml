<?xml version="1.0" encoding="utf-8"?><!-- 交换信息对话框布局 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    tools:background="@color/color_FF141414">

    <!-- 对话框主体内容 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="295dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_12_corner_dialog_style">

        <!-- 头部图片 -->

        <zpui.lib.ui.shadow.layout.ZPUIFrameLayout
            android:id="@+id/flHeadImageView"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:background="@drawable/bg_12_corner_dialog_style"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:zpui_radius="12dp">

            <ImageView
                android:id="@+id/mHeadImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-2dp"
                android:src="@mipmap/live_bg_exwexin_top_banner" />
        </zpui.lib.ui.shadow.layout.ZPUIFrameLayout>

        <!-- 交换图标 -->
        <ImageView
            android:id="@+id/mExchangeIcon"
            android:layout_width="38dp"
            android:layout_height="38dp"
            app:layout_constraintBottom_toBottomOf="@+id/flHeadImageView"
            app:layout_constraintHorizontal_bias="0.27"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/flHeadImageView"
            app:layout_constraintVertical_bias="0.7"
            tools:src="@mipmap/icon_wechat" />

        <!-- 交换信息容器 -->
        <LinearLayout
            android:layout_width="118dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/mExchangeIcon"
            app:layout_constraintLeft_toRightOf="@+id/mExchangeIcon"
            app:layout_constraintTop_toTopOf="@+id/mExchangeIcon">

            <!-- 交换标题 -->
            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/mExchangeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_FF292929_FFD2D2D6"
                android:textSize="13dp"
                tools:text="Eve*****十多个十多个示范岗" />

            <!-- 交换描述 -->
            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/mDescText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/color_FF858585_FF818185"
                android:textSize="11dp"
                android:visibility="gone"
                tools:text="更新于 更新于2022-09-08" />

            <!-- 加密提示容器 -->
            <LinearLayout
                android:id="@+id/ll_encrypt_tip_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/chat_ic_encrypt" />

                <com.hpbr.bosszhipin.views.MTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="号码已隐藏"
                    android:textColor="@color/color_B5"
                    android:textSize="11dp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 对话框标题 -->
        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/mTitleView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="20dp"
            android:textColor="@color/color_FF141414_FFE6E6EB"
            android:textSize="18dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/flHeadImageView"
            tools:text="确认与对方交换微信吗？" />

        <!-- 对话框描述 -->
        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/mDescView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="20dp"
            android:drawablePadding="5dp"
            android:lineSpacingExtra="3dp"
            android:textColor="@color/color_FF5E5E5E_FFFFFFFF"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mTitleView"
            tools:text="若对方以任何形式向您收取费用，请请留下证据并及时举报。" />

        <!-- 提示文本 -->
        <com.hpbr.bosszhipin.views.MTextView
            android:id="@+id/mTipView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="1dp"
            android:layout_marginRight="20dp"
            android:drawablePadding="5dp"
            android:lineSpacingExtra="3dp"
            android:textColor="@color/app_green"
            android:textSize="14dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mDescView"
            tools:text="若对方以任何形式向您收取费用，请请留下证据并及时举报。"
            tools:visibility="visible" />

        <!-- 分割线 -->
        <View
            android:id="@+id/mDividerView"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="25dp"
            android:background="@color/app_divider1"
            app:layout_constraintTop_toBottomOf="@+id/mTipView" />

        <!-- 底部按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/mDividerView">

            <!-- 取消按钮 -->
            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/mCancelView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="取消"
                android:textColor="@color/color_FFB8B8B8_FF6D6D70"
                android:textSize="16dp" />

            <!-- 按钮分割线 -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/app_divider1" />

            <!-- 确认按钮 -->
            <com.hpbr.bosszhipin.views.MTextView
                android:id="@+id/mSureView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="确认"
                android:textColor="@color/color_FF0D9EA3"
                android:textSize="16dp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>