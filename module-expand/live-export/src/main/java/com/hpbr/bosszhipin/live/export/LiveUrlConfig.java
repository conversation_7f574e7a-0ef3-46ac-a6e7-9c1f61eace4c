package com.hpbr.bosszhipin.live.export;

import com.hpbr.bosszhipin.config.URLConfig;

/**
 * Author: zhouyou
 * Date: 2019-12-12
 */
public class LiveUrlConfig extends URLConfig {


    /**
     * 【802-新增】获取职位查询条件
     * http://api.weizhipin.com/project/30/interface/api/76232
     */
    public static final String URL_ZPBOSS_APP_LIVE_RECRUIT_GET_JOB_QUERY_INIT_DATA = buildUrl("zpboss/app/liveRecruit/getJobQueryInitData");

    /**
     * 【802-新增】获取直播/管理员用户列表接口
     * http://api.kanzhun-inc.com/project/30/interface/api/76692
     */
    public static final String URL_ZPBOSS_APP_LIVE_RECRUIT_GET_LIVE_ADMIN_LIST = buildUrl("zpboss/app/liveRecruit/getLiveAdminList");

    /**
     * 【901-新增】线上宣讲首页V2（901以后使用）
     * http://api.kanzhun-inc.com/project/30/interface/api/148556
     */
    public static final String URL_BOSS_LIVE_RECRUIT_RECORDS = buildUrl("zpboss/app/liveRecruit/recordsV2");

    /**
     * 【903-新增】直播学院-顶部Banner
     * https://api.weizhipin.com/project/30/interface/api/297945
     */
    public static final String URL_BOSS_LIVE_COLLEGE_BANNER_LIST = buildUrl("zpboss/app/liveRecruit/college/bannerList");

    /**
     * 【903-新增】直播学院-获取话题列表
     * http://api.kanzhun-inc.com/project/30/interface/api/158678
     */
    public static final String URL_BOSS_LIVE_COLLEGE_TOPIC_LIST = buildUrl("zpboss/app/liveRecruit/college/topicList");

    /**
     * 【1006-新增】直播学院-获取话题内容详情
     * https://api.weizhipin.com/project/30/interface/api/158692
     */
    public static final String URL_BOSS_LIVE_COLLEGE_GET_TOPIC_CONTENT_DETAIL = buildUrl("zpboss/app/liveRecruit/college/getTopicContentDetail");

    /**
     * 【1006-新增】直播学院-有无帮助点击
     * https://api.weizhipin.com/project/30/interface/api/297837
     */
    public static final String URL_BOSS_LIVE_COLLEGE_GOOD_EVALUATE = buildUrl("zpboss/app/liveRecruit/college/good/evaluate");

    /**
     * 【903-新增】直播学院-获取某话题下内容列表
     * http://api.kanzhun-inc.com/project/30/interface/api/158685
     */
    public static final String URL_BOSS_LIVE_TOPIC_CONTENT_LIST = buildUrl("zpboss/app/liveRecruit/college/topicContentList");

    /**
     * 【908-新增】直播学院-直播列表
     * http://api.kanzhun-inc.com/project/30/interface/api/187892
     */
    public static final String URL_BOSS_LIVE_TEACHING_LIST = buildUrl("zpboss/app/liveRecruit/college/liveList");

    /**
     * 【914-新增】直播间记录不存在时的曝光量订单预览V2
     * http://api.weizhipin.com/project/30/interface/api/207889
     */
    public static final String URL_BOSS_LIVERECRUIT_ORDER_EXPOSURE_PREVIEW_FOR_NOT_EXIST = buildUrl("zpboss/app/liveRecruit/order/exposure/previewV2ForLiveRecordNotExists");

    /**
     * 【906-新增】线上宣讲预约-草稿是否可以修改
     * http://api.kanzhun-inc.com/project/30/interface/api/179324
     */
    public static final String URL_BOSS_LIVERECRUIT_DRAF_CANMODIFY = buildUrl("zpboss/app/liveRecruit/draft/canModify");

    /**
     * 【919-新建】个人直播间保存
     * http://api.weizhipin.com/project/30/interface/api/218144
     */
    public static final String URL_BOSS_MAINTENANCE_PERSONAL_SAVE = buildUrl("zpboss/app/liveRecruit/record/maintenance/personal/save");
    /**
     * 【919-新建】品牌直播间保存
     * http://api.weizhipin.com/project/30/interface/api/218148
     */
    public static final String URL_BOSS_MAINTENANCE_BRAND_SAVE = buildUrl("zpboss/app/liveRecruit/record/maintenance/brand/save");

    /**
     * 【919-新增】个人直播间编辑修改信息保存
     * http://api.weizhipin.com/project/30/interface/api/218154
     */
    public static final String URL_BOSS_DRAFT_MAINTENANCE_PERSONAL_SAVE = buildUrl("zpboss/app/liveRecruit/draft/record/maintenance/personal/save");

    /**
     * 【919-新增】品牌直播间编辑修改信息保存
     * http://api.weizhipin.com/project/30/interface/api/218162
     */
    public static final String URL_BOSS_DRAFT_MAINTENANCE_BRAND_SAVE = buildUrl("zpboss/app/liveRecruit/draft/record/maintenance/brand/save");

    /**
     * 【802-新增】职位查询列表
     * http://api.kanzhun-inc.com/project/30/interface/api/76252
     */
    public static final String URL_ZPBOSS_APP_LIVE_RECRUIT_GET_LIVE_JOBS_V_2 = buildUrl("zpboss/app/liveRecruit/getLiveJobsV2");


    /**
     * 【913-新增】职位-检查选中职位给出提示V2
     * http://api.weizhipin.com/project/30/interface/api/206887
     */
    public static final String URL_ZPBOSS_APP_LIVERECRUIT_JOB_SELECTEDJOB_CHECKV2 = buildUrl("zpboss/app/liveRecruit/job/selectedJob/checkV2");

    /**
     * 【1006-新增】在某日期已经有直播的职位
     * https://api.weizhipin.com/project/30/interface/api/299763
     */
    public static final String URL_ZPBOSS_CHECK_SAME_DAY_LIVE_JOB = buildUrl("zpboss/app/liveRecruit/checkSameDayLiveJob");

    /**
     * boss 预约详情
     */
    public static final String URL_GET_BOSS_LIVE_RECRUIT_DETAIL = buildUrl("zpboss/app/liveRecruit/recordDetail");

    /**
     * 909 获取简历投递情况接口
     * http://api.kanzhun-inc.com/project/30/interface/api/183272
     */
    public static final String URL_GET_BOSS_LIVE_GET_DELIVERY_DETAILS = buildUrl("zpboss/app/liveRecruit/getDeliveryDetails");

    /**
     * 909 获取职位组简历投递情况接口
     * http://api.kanzhun-inc.com/project/30/interface/api/183531
     */
    public static final String URL_GET_BOSS_LIVE_JOB_GROUP_DELIVERY_DETAILS = buildUrl("zpboss/app/liveRecruit/getJobGroupDeliveryDetails");

    /**
     * 1002 【校招直播】查询讲解列表
     * https://api.weizhipin.com/project/30/interface/api/247230
     */
    public static final String URL_GET_BOSS_LIVE_EXPLAIN_LIST_FOR_END = buildUrl("zpboss/app/liveRecruit/explain/listForEnd");

    /**
     * 【1002-新增】删除求讲解录制
     * https://api.weizhipin.com/project/30/interface/api/246807
     */
    public static final String URL_GET_BOSS_LIVE_EXPLAIN_DEL = buildUrl("zpboss/app/liveRecruit/explain/del");

    /**
     * 【1002-新增】根据讲解id查询讲解视频url
     * https://api.weizhipin.com/project/30/interface/api/246663
     */
    public static final String URL_GET_BOSS_LIVE_EXPLAIN_GET_VIDEO_URL = buildUrl("zpboss/app/liveRecruit/explain/getVideoUrl");

    /**
     * 牛人 预约详情
     */
    public static final String URL_GET_GEEK_LIVE_RECRUIT_DETAIL = buildUrl("zplive/audience/geek/live/detail");

    /**
     * http://api.kanzhun-inc.com/project/30/interface/api/186989
     * 【908-新增】B端观众获取直播详情
     */
    public static final String URL_BOSS_AUDIENCE_LIVE_RECRUIT_DETAIL = buildUrl("zpboss/app/liveRecruit/audience/detail/query");

    /**
     * 1004 【校招直播】查询专场直播间的标签列表
     * https://api.weizhipin.com/project/30/interface/api/268200
     */
    public static final String URL_BOSS_LIVE_RECRUIT_SPECIAL_TAG_LIST = buildUrl("zpboss/app/liveRecruit/audience/special/subject/aggregatedTag/list");

    /**
     * 【1004-新增】直播专题聚合-聚合标签下直播间列表
     * https://api.weizhipin.com/project/30/interface/api/268209
     */
    public static final String URL_BOSS_LIVE_RECRUIT_SPECIAL_LIVE_CARD_LIST = buildUrl("zpboss/app/liveRecruit/audience/special/subject/aggregatedTag/liveList");

    /**
     * 1009
     * 直播专题聚合-聚合标签下直播间列表
     * https://api.weizhipin.com/project/30/interface/api/358219
     */
    public static final String URL_BOSS_LIVE_RECRUIT_SPECIAL_SUBJECT_ENTERROOM_LIVELIST = buildUrl("zpboss/app/liveRecruit/audience/special/subject/aggregatedTag/enterRoom/liveList");

    /**
     * 【校招直播】预约直播
     * http://************:8088/project/30/interface/api/52407
     */
    public static final String URL_GEEK_SUBSCRIBE_LIVE = buildUrl("zpgeek/app/live/recruit/subscribe");

    /**
     * 【校招直播】取消预约直播
     * http://************:8088/project/30/interface/api/52412
     */
    public static final String URL_GEEK_UNSUBSCRIBE_LIVE = buildUrl("zpgeek/app/live/recruit/unsubscribe");

    /**
     * 【校招直播】直播职位 - 感兴趣
     * http://api.kanzhun-inc.com/project/30/interface/api/52472
     */
    public static final String URL_GEEK_JOB_INTEREST = buildUrl("zpgeek/app/live/recruit/job/interest");

    /**
     * 【校招直播】直播职位 - 取消感兴趣
     * http://api.kanzhun-inc.com/project/30/interface/api/52477
     */
    public static final String URL_GEEK_JOB_UNINTEREST = buildUrl("zpgeek/app/live/recruit/job/uninterest");

    /**
     * 【校招直播】直播职位 - 投递简历
     * http://api.kanzhun-inc.com/project/30/interface/api/52707
     */
    public static final String URL_GEEK_JOB_DELIVER = buildUrl("zpgeek/app/live/recruit/job/deliver");

    /**
     * 【校招直播】直播道具礼物列表
     * http://api.kanzhun-inc.com/project/30/interface/api/52837
     */
    public static final String URL_GEEK_GIFT_LIST = buildUrl("zpgeek/app/live/recruit/gift/list");

    /**
     * 【908-新增】直播道具礼物列表(BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187038
     */
    public static final String URL_BOSS_AUDIENCE_GIFT_LIST = buildUrl("zpboss/app/liveRecruit/audience/gift/list");

    /**
     * 【校招直播】发送消息
     * http://api.kanzhun-inc.com/project/30/interface/api/52817
     */
    public static final String URL_GEEK_MESSAGE_SEND = buildUrl("zpgeek/app/live/recruit/message/send");

    /**
     * 908【校招直播】发送消息(BOSS 观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187080
     */
    public static final String URL_BOSS_AUDIENCE_MESSAGE_SEND = buildUrl("zpboss/app/liveRecruit/audience/message/send");

    /**
     * 【校招直播】BARRAGE LIST
     * https://api.weizhipin.com/project/30/interface/api/376451
     */
    public static final String URL_GEEK_BARRAGE_LIST = buildUrl("zpgeek/app/live/recruit/v2/barrage/list");

    /**
     * 【校招直播】历史弹幕
     */
    public static final String URL_GEEK_HISTORY_MESSAGES = buildUrl("zpgeek/app/live/recruit/barrage/history/query");

    /**
     * 【1011】获取直播同步弹幕列表（观众）
     * https://api.weizhipin.com/project/30/interface/api/377579
     */
    public static final String URL_BOSS_AUDIENCE_BARRAGE_LIST = buildUrl("zpboss/app/liveRecruit/audience/synchronizeBarrage/list");

    /**
     * 910  查询我的心动值
     * http://api.weizhipin.com/project/30/interface/api/204609
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SCORE_QUERY = buildUrl("zpgeek/app/live/recruit/score/query");

    /**
     * 910  查询心动榜
     * http://api.weizhipin.com/project/30/interface/api/204610
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SCORE_LIST = buildUrl("zpgeek/app/live/recruit/score/list");

    /**
     * 910  观看时长上报
     * http://api.weizhipin.com/project/30/interface/api/204611
     */
    public static final String URL_GEEK_LIVE_RECRUIT_DURATION_REPORT = buildUrl("zpgeek/app/live/recruit/duration/report");

    /**
     * 910  中奖名单
     * http://api.weizhipin.com/project/30/interface/api/204522
     */
    public static final String URL_GEEK_LIVE_RECRUIT_DRAW_NOTICE_QUERY = buildUrl("zpgeek/app/live/recruit/draw/notice/query");

    /**
     * 910  中奖名单  （BOSS作为观众）
     * http://api.weizhipin.com/project/30/interface/api/205004
     */
    public static final String URL_BOSS_AUDIENCE_LIVE_RECRUIT_DRAW_NOTICE_QUERY = buildUrl("zpboss/app/liveRecruit/audience/lucky/draw/notice/query");

    /**
     * 【校招直播】获取直播同步弹幕列表
     * https://api.weizhipin.com/project/30/interface/api/377587
     */
    public static final String URL_BOSS_BARRAGE_LIST = buildUrl("zpboss/app/liveRecruit/synchronizeBarrage/list");
    /**
     * 【校招直播】消息置顶
     * http://api.kanzhun-inc.com/project/30/interface/api/51032
     */
    public static final String URL_BOSS_MESSAGE_PLACE_TOP = buildUrl("zpboss/app/liveRecruit/topDialog");

    /**
     * 908【校招直播】查询分组内职位列表 (BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187857
     */
    public static final String URL_BOSS_AUDIENCE_RECRUIT_GROUP_JOB = buildUrl("zpboss/app/liveRecruit/audience/group/job/listV2");
    /**
     * 【校招直播】去抽奖
     */
    public static final String URL_GEEK_LIVE_LUCKY_DRAW = buildUrl("zpgeek/app/live/recruit/lucky/draw");
    /**
     * 908【校招直播】去抽奖 (BOSS 观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187185
     */
    public static final String URL_BOSS_AUDIENCE_LIVE_LUCKY_DRAW = buildUrl("zpboss/app/liveRecruit/audience/lucky/draw");
    /**
     * 911【校招直播】去抽奖 (代播BOSS)
     * http://api.kanzhun-inc.com/project/30/interface/api/187185
     */
    public static final String URL_BOSS_PROXY_LIVE_LUCKY_DRAW = buildUrl("zpboss/app/liveRecruit/proxy/lucky/draw");
    /**
     * 902【校招直播】获取体验直播间详情
     * http://api.kanzhun-inc.com/project/30/interface/api/154611
     */
    public static final String URL_BOSS_LIVE_EXPERIENCE_GET_LIVE_RECORD_DETAIL = buildUrl("zpboss/app/liveRecruit/bossExperience/getLiveRecordDetail");
    /**
     * 902【校招直播】刷新管理员验证码
     * http://api.kanzhun-inc.com/project/30/interface/api/154660
     */
    public static final String URL_BOSS_LIVE_ADMINVERIFYCODE_REFRESH = buildUrl("zpboss/app/liveRecruit/adminVerifyCode/refresh");
    /**
     * 902【校招直播】创建并获取体验直播间详情
     * http://api.kanzhun-inc.com/project/30/interface/api/154632
     */
    public static final String URL_BOSS_LIVE_CREATE_AND_GET_LIVE_RECORD_DETAIL = buildUrl("zpboss/app/liveRecruit/bossExperience/createAndGetLiveRecordDetail");
    /**
     * 906【906-新增】获取直播礼物列表
     * http://api.kanzhun-inc.com/project/30/interface/api/177504
     */
    public static final String URL_BOSS_LIVE_LIVERECRUIT_LIVEGIFT_LIST = buildUrl("zpboss/app/liveRecruit/liveGift/list");

    /**
     * 【 910-新增】试用直播间可复制的目标直播记录集合
     * http://api.weizhipin.com/project/30/interface/api/199888
     */
    public static final String URL_BOSS_LIVE_GET_TRIAL_LIVE_LIST = buildUrl("zpboss/app/liveRecruit/trialLive/targetLiveRecords");

    /**
     * 【910-新增】创建并获取试播直播间详情
     * http://api.weizhipin.com/project/30/interface/api/199897
     */
    public static final String URL_BOSS_LIVE_CREATE_TRIAL_LIVE_DETAIL = buildUrl("zpboss/app/liveRecruit/trialLive/createAndGetLiveRecordDetail");

    /**
     * 【910-新增】职位-检查选中职位给出提示
     * http://api.weizhipin.com/project/30/interface/api/200347
     */
    public static final String URL_BOSS_LIVE_RECRUIT_SELECT_JOB_CHECK = buildUrl("zpboss/app/liveRecruit/job/selectedJob/check");

    /**
     * 912【校招直播】查询直播间职位城市列表
     * http://api.weizhipin.com/project/30/interface/api/206054
     */
    public static final String URL_GEEK_LIVE_RECRUIT_CITY_LIST = buildUrl("zpgeek/app/live/recruit/city/list");

    /**
     * 1002 【校招直播】查询讲解列表
     * https://api.weizhipin.com/project/30/interface/api/247230
     */
    public static final String URL_GEEK_LIVE_RECRUIT_EXPLAIN_LIST = buildUrl("zplive/audience/geek/live/explain/list");

    /**
     * 1004 【校招直播】查询直播间专题的标签列表
     * https://api.weizhipin.com/project/30/interface/api/265356
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SPECIAL_TAG_LIST = buildUrl("zpgeek/app/live/recruit/special/tag/list");

    /**
     * 1004 【校招直播】查询专题标签下直播列表
     * https://api.weizhipin.com/project/30/interface/api/266796
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SPECIAL_LIVE_CARD_LIST = buildUrl("zpgeek/app/live/recruit/special/livecard/list");

    /**
     * 【校招直播】直播职位列表
     * https://api.weizhipin.com/project/30/interface/api/351131
     */
    public static final String URL_GEEK_LIVE_RECRUIT_V2_JOB_LIST = buildUrl("zplive/audience/geek/job/list");

    /**
     * 【校招直播】职位列表筛选项
     * https://api.weizhipin.com/project/30/interface/api/351139
     */
    public static final String URL_GEEK_LIVE_RECRUIT_JOB_OPTION_LIST = buildUrl("zpgeek/app/live/recruit/job/option/list");

    /**
     * 【校招直播】查询视频讲解地址
     * https://api.weizhipin.com/project/30/interface/api/247239
     */
    public static final String URL_GEEK_LIVE_RECRUIT_EXPLAIN_PLAY_URL_QUERY = buildUrl("zpgeek/app/live/recruit/explain/playurl/query");

    /**
     * 1009
     * 查询专场直播品牌列表
     * https://api.weizhipin.com/project/30/interface/api/358395
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SPECIAL_SUBJECT_ENTERROOM_LIVELIST = buildUrl("zpgeek/app/live/recruit/special/brand/list");



    /*=====================================================================================================*/

    /**
     * 开始直播
     * http://api.kanzhun-inc.com/project/30/interface/api/144381
     */
    public static final String URL_BOSS_RECRUIT_START_LIVE = buildUrl("zpboss/app/liveRecruit/startLive");

    /**
     * 【1001-新增】继续直播
     * https://api.weizhipin.com/project/30/interface/api/226206
     */
    public static final String URL_BOSS_RECRUIT_CONTINUE_LIVE = buildUrl("zpboss/app/liveRecruit/continueLive");

    /**
     * 暂停宣讲
     * http://api.kanzhun-inc.com/project/30/interface/api/144383
     */
    public static final String URL_BOSS_RECRUIT_SUSPEND_LIVE = buildUrl("zpboss/app/liveRecruit/suspendLive");

    /**
     * 结束宣讲
     * http://api.kanzhun-inc.com/project/30/interface/api/144384
     */
    public static final String URL_BOSS_RECRUIT_END_LIVE = buildUrl("zpboss/app/liveRecruit/endLive");

    /**
     * 【校招直播】进出直播间 C端调用
     * http://api.kanzhun-inc.com/project/30/interface/api/100629
     */
    public static final String URL_BOSS_RECRUIT_ROOM_ENTER = buildUrl("zpgeek/app/live/recruit/room/enter");

    /**
     * 908【校招直播】进出直播间 (BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187255
     */
    public static final String URL_BOSS_AUDIENCE_RECRUIT_ROOM_ENTER = buildUrl("zpboss/app/liveRecruit/audience/room/enter");

    /**
     * 910【校招直播】进出直播间 (管理员)
     * http://api.weizhipin.com/project/30/interface/api/204261
     */
    public static final String URL_BOSS_ADMIN_RECRUIT_ROOM_ENTER = buildUrl("zpboss/app/liveRecruit/room/enter");

    /**
     * 【校招直播】获取置顶消息 C端调用
     * http://api.kanzhun-inc.com/project/30/interface/api/102078
     */
    public static final String URL_GEEK_RECRUIT_TOP_MESSAGE = buildUrl("zpgeek/app/live/recruit/topmessage/query");

    /**
     * 【校招直播】获取置顶消息 C端调用 (BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187269
     */
    public static final String URL_BOSS_AUDIENCE_RECRUIT_TOP_MESSAGE = buildUrl("zpboss/app/liveRecruit/audience/topmessage/query");

    /**
     * 【校招直播】获取置顶消息 B端调用
     * http://api.kanzhun-inc.com/project/30/interface/api/101896
     */
    public static final String URL_BOSS_RECRUIT_TOP_MESSAGE = buildUrl("zpboss/app/liveRecruit/getTopDialog");

    /**
     * 【校招直播】获取直播心跳状态 C端调用
     * http://api.kanzhun-inc.com/project/30/interface/api/105298
     */
    public static final String URL_GEEK_RECRUIT_HEARTBEAT = buildUrl("zpgeek/app/live/recruit/heartbeat/query");

    /**
     * 908【校招直播】获取直播心跳状态 (BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187871
     */
    public static final String URL_BOSS_AUDIENCE_RECRUIT_HEARTBEAT = buildUrl("zpboss/app/liveRecruit/audience/heartbeat/query");

    /**
     * 【1008-新增】获取某职位组的职位列表V2(分页版)
     * https://api.weizhipin.com/project/30/interface/api/353371
     */
    public static final String URL_BOSS_RECRUIT_GET_JOB_LIST = buildUrl("zpboss/app/liveRecruit/getLiveJobsByJobGroupIdV2");
    /**
     * 【校招直播】最近直播-Banner
     * http://api.weizhipin.com/project/30/interface/api/210252
     */
    public static final String URL_GEEK_RECRUIT_HOME_BANNER = buildUrl("zpgeek/app/live/recruit/home/<USER>/query");

    /**
     * 【校招直播】最近直播-推荐
     * http://api.weizhipin.com/project/30/interface/api/210260
     */
    public static final String URL_GEEK_RECRUIT_HOME_RECOMMEND = buildUrl("zpgeek/app/live/recruit/home/<USER>");

    /**
     * 【校招直播】最近直播-全部
     * http://api.weizhipin.com/project/30/interface/api/210268
     */
    public static final String URL_GEEK_RECRUIT_HOME_LIST = buildUrl("zpgeek/app/live/recruit/home/<USER>");

    /**
     * 【校招直播】校招直播列表V3
     * http://api.weizhipin.com/project/30/interface/api/117744
     */
    public static final String URL_GEEK_RECRUIT_V3_LIST = buildUrl("zpgeek/app/live/recruit/v3/list");

    /**
     * 【1002-新增】直播专栏列表
     * https://api.weizhipin.com/project/30/interface/api/233640
     */
    public static final String URL_GEEK_LIVE_SPECIAL_COLUMN_LIST = buildUrl("zpgeek/app/live/recruit/column/card/list");
    /**
     * https://api.weizhipin.com/project/30/interface/api/376435
     */
    public static final String URL_CERTIFICATION_USERVIDEO_GETSIGNINFO = buildUrl("certification/userVideo/getSignInfo");
    /**
     * sdk初始化成功之后-锁定客服
     * http://api.kanzhun-inc.com/project/30/interface/api/144802
     */
    public static final String URL_CERTIFICATION_USERVIDEO_LOCK = buildUrl("certification/userVideo/lock");
    /**
     * sdk初始化失败之后-释放客服
     * http://api.kanzhun-inc.com/project/30/interface/api/144802
     */
    public static final String URL_CERTIFICATION_USERVIDEO_RELEASE = buildUrl("certification/userVideo/release");
    /**
     * 821-视频排队-入会准备接口
     * http://api.kanzhun-inc.com/project/30/interface/api/148250
     */
    public static final String URL_CERTIFICATION_USERVIDEO_PREPARE = buildUrl("certification/nUserVideo/prepare");
    /**
     * 821-视频排队-入会回调
     * http://api.kanzhun-inc.com/project/30/interface/api/148251
     */
    public static final String URL_CERTIFICATION_USERVIDEO_NOTIFY = buildUrl("certification/nUserVideo/notify");
    /**
     * 821-视频排队-用户心跳接口
     * http://api.kanzhun-inc.com/project/30/interface/api/151545
     */
    public static final String URL_CERTIFICATION_USERVIDEO_HEALTHCHECK = buildUrl("certification/nUserVideo/healthCheck");

    /**
     * 【校招直播】Boss获取抽奖详情
     * http://api.kanzhun-inc.com/project/30/interface/api/95267
     */
    public static final String URL_BOSS_GET_LUCKY_DRAW_DETAIL = buildUrl("zpboss/app/liveRecruit/luckyDrawDetail");
    /**
     * 【校招直播】Boss获取抽奖获奖名单
     * http://api.kanzhun-inc.com/project/30/interface/api/95274
     */
    public static final String URL_BOSS_GET_LUCKY_DRAW_WINNER = buildUrl("zpboss/app/liveRecruit/publishWinner");


    /*-----------------------直播分享-------------------------------*/
    /**
     * 【校招直播】查询分享海报信息
     * http://api.kanzhun-inc.com/project/30/interface/api/147391
     */
    public static final String URL_GEEK_SHARE_POSTER_QUERY = buildUrl("zpgeek/app/live/recruit/share/poster/query");//C身份作为观众时调用

    /**
     * https://api.weizhipin.com/project/30/interface/api/187885
     */
    public static final String URL_BOSS_AS_AUDIENCE_SHARE_POSTER_QUERY = buildUrl("zpboss/app/liveRecruit/audience/share/poster/query");//非代播BOSS的B身份作为观众时调用

    /**
     * https://api.weizhipin.com/project/30/interface/api/376571
     */
    public static final String URL_PROXY_BOSS_AUDIENCE_SHARE_POSTER_QUERY = buildUrl("zpboss/app/liveRecruit/proxy/register/share/poster/query");//代播BOSS 作为观众时调用

    /**
     * https://api.weizhipin.com/project/30/interface/api/179667
     */
    public static final String URL_BOSS_SHARE_POSTER_QUERY = buildUrl("zpboss/app/liveRecruit/share/poster/query");//B身份 管理员和主播 调用


    /*------------------------------------------------------*/

    /**
     * 【校招直播】职位订阅
     * http://api.kanzhun-inc.com/project/30/interface/api/147968
     */
    public static final String URL_GEEK_JOB_SUBSCRIBE = buildUrl("zpgeek/app/live/recruit/job/subscribe");
    /**
     * 【校招直播】职位取消订阅
     * http://api.kanzhun-inc.com/project/30/interface/api/147969
     */
    public static final String URL_GEEK_JOB_UNSUBSCRIBE = buildUrl("zpgeek/app/live/recruit/job/unsubscribe");

    /**
     * 【901-新增】线上宣讲直播间-编辑用详情
     * http://api.weizhipin.com/project/30/interface/api/148587
     */
    public static final String URL_BOSS_RECORD_DETAIL_FOR_MODIFY = buildUrl("zpboss/app/liveRecruit/recordDetailForModify");

    /**
     * 【914-新增】线上宣讲直播间-直播间记录不存在时
     * http://api.weizhipin.com/project/30/interface/api/207891
     */
    public static final String URL_BOSS_DETAIL_FOR_RECORD_NOT_EXIST = buildUrl("zpboss/app/liveRecruit/quotation/detailV2ForLiveRecordNotExists");
    /**
     * 【1222-新增】线上支付权限预约直播流程底部提示
     * <a href="https://api.weizhipin.com/project/30/interface/api/727687">...</a>
     */
    public static final String URL_BOSS_ONLINE_BOTTOM_TIPS = buildUrl("zpboss/app/liveRecruit/scheduled/process/bottomTips");

    /**
     * 【903-新增】获取已保存的所有抽奖草稿
     * http://api.kanzhun-inc.com/project/30/interface/api/157369
     */
    public static final String URL_BOSS_GET_ALL_DRAFT = buildUrl("zpboss/app/liveRecruit/luckyDraw/getAllDraft");

    /**
     * 【903-新增】发布抽奖
     * http://api.kanzhun-inc.com/project/30/interface/api/157418
     */
    public static final String URL_BOSS_LUCKY_DRAW_PUBLISH = buildUrl("zpboss/app/liveRecruit/luckyDraw/publish");

    /**
     * 【904-新增】直播间点赞
     * http://api.kanzhun-inc.com/project/30/interface/api/164383
     */
    public static final String URL_GEEK_APPLAUD = buildUrl("zpgeek/app/live/recruit/applaud");

    /**
     * 【908-新增】直播间点赞(BOSS观众)
     * http://api.kanzhun-inc.com/project/30/interface/api/187101
     */
    public static final String URL_BOSS_AUDIENCE_APPLAUD = buildUrl("zpboss/app/liveRecruit/audience/applaud");
    /**
     * 【校招直播】查询直播间指定职位状态
     */
    public static final String URL_GEEK_JOB_STATUS_REQUEST = buildUrl("zpgeek/app/live/recruit/job/status/query");
    /**
     * 908 【校招直播】查询职位详情（BOSS观众）
     * http://api.kanzhun-inc.com/project/30/interface/api/189488
     */
    public static final String URL_BOSS_AUDIENCE_GET_JOB_DETAIL = buildUrl("zpboss/app/liveRecruit/audience/getJobDetail");
    /**
     * 【906-新增】品牌视频列表
     * http://api.kanzhun-inc.com/project/30/interface/api/178071
     */
    public static final String URL_BOSS_BRAND_VIDEO_LIST = buildUrl("zpboss/app/liveRecruit/brandVideo/list");

    /**
     * 【907-新增】职位组置顶
     * http://api.kanzhun-inc.com/project/30/interface/api/183601
     */
    public static final String URL_BOSS_TOP_LIVE_JOB_GROUP = buildUrl("zpboss/app/liveRecruit/topLiveJobGroup");
    /**
     * 【907-新增】新增或修改抽奖草稿
     * http://api.kanzhun-inc.com/project/30/interface/api/183377
     */
    public static final String URL_BOSS_LUCKY_DRAW_SAVE_DRAFT = buildUrl("zpboss/app/liveRecruit/luckyDraw/saveDraft");
    /**
     * 【907-新增】删除消息
     * http://api.kanzhun-inc.com/project/30/interface/api/183433
     */
    public static final String URL_BOSS_DELETE_DIALOG = buildUrl("zpboss/app/liveRecruit/deleteDialog");
    /**
     * 【907-新增】禁言
     * http://api.kanzhun-inc.com/project/30/interface/api/183440
     */
    public static final String URL_BOSS_BLOCK_DIALOG = buildUrl("zpboss/app/liveRecruit/blockDialog");
    /**
     * 【907-新增】发言
     * http://api.kanzhun-inc.com/project/30/interface/api/50872
     */
    public static final String URL_BOSS_SPEAK = buildUrl("zpboss/app/liveRecruit/speak");
    /**
     * 908.511
     * 【校招直播】求讲解职位
     */
    public static final String URL_GEEK_POSITION_EXPLAIN = buildUrl("zpgeek/app/live/recruit/position/explain");
    /**
     * 【908新增】获取直播话题库
     * http://api.kanzhun-inc.com/project/30/interface/api/188340
     */
    public static final String URL_BOSS_TOPIC_WORDS = buildUrl("zpboss/app/liveRecruit/topicLibrary/list");
    /**
     * 【908-新增】邀约投递某牛人
     * http://api.kanzhun-inc.com/project/30/interface/api/187528
     */
    public static final String URL_BOSS_INVITE_RESUME_GEEK = buildUrl("zpboss/app/liveRecruit/inviteResume/geek/invite");
    /**
     * 【908-新增】邀约牛人的信息
     * http://api.kanzhun-inc.com/project/30/interface/api/187969
     */
    public static final String URL_BOSS_INVITE_RESUME_GEEKINFO = buildUrl("zpboss/app/liveRecruit/inviteResume/geek/inviteResumeInfo");
    /**
     * 【908-新增】【外部授权】刷新管理员二维码
     * http://api.kanzhun-inc.com/project/30/interface/api/187864
     */
    public static final String URL_ADMIN_QR_CODE_REFRESH = buildUrl("zpboss/app/liveRecruit/extraAdmin/adminQRCode/refresh");
    /**
     * 【910-新增】被驳回的直播间退款
     * http://api.weizhipin.com/project/30/interface/api/199825
     */
    public static final String URL_LIVE_RECRUIT_REFUND = buildUrl("zpboss/app/liveRecruit/refund");

    /**
     * 【911-新增】直播牛人心动榜
     * http://api.weizhipin.com/project/30/interface/api/204717
     */
    public static final String URL_BOSS_LIVE_INTENTION_RANK_LIST = buildUrl("zpboss/app/liveRecruit/intention/rankList");

    /**
     * 【911-新增】直播广场
     * http://api.weizhipin.com/project/30/interface/api/204804
     */
    public static final String URL_BOSS_APP_LIVE_RECRUIT_PLAZA = buildUrl("zpboss/app/liveRecruit/plaza");

    /**
     * 【911-新增】代播往期回放
     * http://api.weizhipin.com/project/30/interface/api/204988
     */
    public static final String URL_BOSS_PROXY_PAST_LIVE_LIST = buildUrl("zpboss/app/liveRecruit/proxy/pastLiveList");

    /**
     * 【912新增】确认暂停提示信息
     * http://api.weizhipin.com/project/30/interface/api/206152
     */
    public static final String URL_BOSS_CONFIRM_PAUSE_TIP = buildUrl("zpboss/app/liveRecruit/confirmPauseTip");
    /**
     * 【912新增】求讲解列表
     * http://api.weizhipin.com/project/30/interface/api/205692
     */
    public static final String URL_BOSS_EXPLAIN_LIST = buildUrl("zpboss/app/liveRecruit/explainList");
    /**
     * 【校招直播】分享上报
     * http://api.weizhipin.com/project/30/interface/api/206451
     */
    public static final String URL_GEEK_SHARE_REPORT = buildUrl("zpgeek/app/live/recruit/share/report");
    /**
     * 【913-新增】心动值直播奖励
     * http://api.weizhipin.com/project/30/interface/api/206842
     */
    public static final String URL_BOSS_INTENTION_AWARD_LIST = buildUrl("zpboss/app/liveRecruit/intention/awardList");
    /**
     * 【校招直播】直播间关注品牌
     * http://api.weizhipin.com/project/30/interface/api/206873
     */
    public static final String URL_GEEK_BRAND_SUBSCRIBE = buildUrl("zpgeek/app/live/recruit/brand/subscribe");
    /**
     * 【913-新增】高级直播间品牌页
     * http://api.weizhipin.com/project/30/interface/api/206997
     */
    public static final String URL_BOSS_BRAND_HOME = buildUrl("zpboss/app/liveRecruit/brand/home");
    /**
     * 【校招直播】高级直播间品牌页
     * http://api.weizhipin.com/project/30/interface/api/207178
     */
    public static final String URL_GEEK_BRAND_HOME = buildUrl("zpgeek/app/live/recruit/brand/query");

    /**
     * 【校招直播】查询品牌下直播回放列表
     * http://api.weizhipin.com/project/30/interface/api/207179
     */
    public static final String URL_GEEK_BRAND_PAST_LIVE_LIST = buildUrl("zpgeek/app/live/recruit/brand/pastlive/list");
    /**
     * 【校招直播】查询品牌下直播回放列表
     * http://api.weizhipin.com/project/30/interface/api/207008
     */
    public static final String URL_BOSS_BRAND_PAST_LIVE_LIST = buildUrl("zpboss/app/liveRecruit/brand/pastLiveList");
    /**
     * 【913-新增-APP】上报打点弹窗
     * http://api.weizhipin.com/project/30/interface/api/207198
     */
    public static final String URL_BOSS_REPORT_MARK_DIALOG = buildUrl("zpboss/app/liveRecruit/reportMarkDialog");
    /**
     * 【913-新增-App】更改打点标记开关状态
     * http://api.weizhipin.com/project/30/interface/api/207240
     */
    public static final String URL_BOSS_CHANGE_MARK_SWITCH = buildUrl("zpboss/app/liveRecruit/changeMarkSwitch");
    /**
     * 【校招直播】查询直播回放地址
     * http://api.weizhipin.com/project/30/interface/api/207195
     */
    public static final String URL_GEEK_PLAYBACK_QUERY = buildUrl("zpgeek/app/live/recruit/playback/query");

    /**
     * 【914-新增】求讲解职位分组
     * http://api.weizhipin.com/project/30/interface/api/207941
     */
    public static final String URL_GEEK_JOB_GROUP_EXPLAIN = buildUrl("zpgeek/app/live/recruit/job/group/explain");

    /**
     * 【校招直播】查询直播品牌分享信息
     * http://api.weizhipin.com/project/30/interface/api/207936
     */
    public static final String URL_GEEK_LIVE_RECRUIT_SHARE_QUERY = buildUrl("zpgeek/app/live/recruit/brand/share/poster/query");

    /**
     * 【914-新增】高级直播间品牌分享海报数据
     * http://api.weizhipin.com/project/30/interface/api/207934
     */
    public static final String URL_BOSS_LIVE_RECRUIT_SHARE_QUERY = buildUrl("zpboss/app/liveRecruit/brand/posterShareInfo");

    /**
     * 【915-新增】获取直播抵扣券列表
     * http://api.weizhipin.com/project/30/interface/api/208428
     */
    public static final String URL_BOSS_LIVE_RECRUIT_TICKET_LIST = buildUrl("zpboss/app/liveRecruit/ticketList");

    /**
     * 【915-新增】校招直播获取推荐开播时间
     * http://api.weizhipin.com/project/30/interface/api/208464
     */
    public static final String URL_BOSS_GET_RECOMMEND_OPEN_TIME = buildUrl("zpboss/app/liveRecruit/getRecommendOpenTime");

    /**
     * 【916-新增】直播间恢复直播接口
     * http://api.weizhipin.com/project/30/interface/api/209053
     */
    public static final String URL_BOSS_RECOVER_LIVE = buildUrl("zpboss/app/liveRecruit/recoverLive");

    /**
     * 【1006-新增】获取职位不合适的预约开播日期
     * https://api.weizhipin.com/project/30/interface/api/298674
     */
    public static final String URL_BOSS_GET_JOB_UNSUITABLE_LIVE_START_DATES = buildUrl("zpboss/app/liveRecruit/getJobUnsuitableLiveStartDates");

    /**
     * 【917-新增】删除抽奖草稿
     * http://api.weizhipin.com/project/30/interface/api/214052
     */
    public static final String URL_BOSS_LIVE_RECRUIT_LUCKDRAW_DEL_DRAF = buildUrl("zpboss/app/liveRecruit/luckyDraw/delDraft");

    /**
     * 【917-新增】直播类型列表
     * http://api.weizhipin.com/project/30/interface/api/213900
     */
    public static final String URL_BOSS_LIVE_RECRUIT_LIVE_TYPE_LIST = buildUrl("zpboss/app/liveRecruit/liveTypeList");

    /**
     * 【921-新增】直播攻略信息
     * https://api.weizhipin.com/project/30/interface/api/220824
     */
    public static final String URL_BOSS_LIVE_RECRUIT_NOTE_INFO = buildUrl("zpboss/app/liveRecruit/noteInfo");

    /**
     * 【1001-新增】直播间投递牛人查询(最多100条)
     * https://api.weizhipin.com/project/30/interface/api/226683
     */
    public static final String URL_BOSS_DELIVERY_GEEK_LIST = buildUrl("zpboss/app/liveRecruit/deliveryGeekList");
    /**
     * 【【1312-新增】个人直播间直播结束页投递牛人查询(最多100条)
     * https://api.weizhipin.com/project/30/interface/api/780892
     */
    public static final String URL_BOSS_DELIVERY_GEEK_ONELINE_LIST = buildUrl("zpboss/app/liveRecruit/deliveryGeekList/byOnlineLiveEnd");
    /**
     * 【1001-新增】【校招直播】查询专栏列表
     * https://api.weizhipin.com/project/30/interface/api/227871
     */
    public static final String URL_GEEK_RECRUIT_COLUMN_LIST = buildUrl("zpgeek/app/live/recruit/column/list");
    /**
     * 【1001-新增】B端观众获取直播专栏的直播间列表
     * https://api.weizhipin.com/project/30/interface/api/227844
     */
    public static final String URL_BOSS_RECRUIT_COLUMN_LIST = buildUrl("zpboss/app/liveRecruit/audience/specialColumn/liveRecord/list");
    /**
     * 【1001-新增】【校招直播】批量预约直播间
     * https://api.weizhipin.com/project/30/interface/api/227889
     */
    public static final String URL_GEEK_RECRUIT_BATCH_SUBSCRIBE = buildUrl("zpgeek/app/live/recruit/batchsubscribe");
    /**
     * 【1002-新增】直播结束求讲解录制列表
     * https://api.weizhipin.com/project/30/interface/api/246564
     */
    public static final String URL_BOSS_RECRUIT_EXPLAIN_FOR_END = buildUrl("zpboss/app/liveRecruit/explain/listForEnd");
    /**
     * 【1107-新增】职位列表-用于直播后切片录制
     * https://api.weizhipin.com/project/30/interface/api/527005
     */
    public static final String URL_BOSS_RECRUIT_JOB_LIST_BY_EXPLAIN = buildUrl("zpboss/app/liveRecruit/jobListByExplain");
    /**
     * 【1002-新增】开始求讲解录制
     * https://api.weizhipin.com/project/30/interface/api/246501
     */
    public static final String URL_BOSS_RECRUIT_EXPLAIN_START = buildUrl("zpboss/app/liveRecruit/explain/start");
    /**
     * 【1002-新增】结束求讲解录制
     * https://api.weizhipin.com/project/30/interface/api/246510
     */
    public static final String URL_BOSS_RECRUIT_EXPLAIN_END = buildUrl("zpboss/app/liveRecruit/explain/end");
    /**
     * 【1107-新增】直播讲解切片录制-开始录制讲解
     * https://api.weizhipin.com/project/30/interface/api/527501
     */
    public static final String URL_BOSS_RECRUIT_LIVE_END_EXPLAIN_START = buildUrl("zpboss/app/liveRecruit/liveEnd/startExplain");
    /**
     * 【1107-新增】直播讲解切片录制-完成录制讲解
     * https://api.weizhipin.com/project/30/interface/api/527549
     */
    public static final String URL_BOSS_RECRUIT_LIVE_END_EXPLAIN_END = buildUrl("zpboss/app/liveRecruit/liveEnd/explainFinish");
    /**
     * 【1107-新增】直播讲解切片录制-取消录制讲解
     * https://api.weizhipin.com/project/30/interface/api/527557
     */
    public static final String URL_BOSS_RECRUIT_LIVE_END_EXPLAIN_CANCEL = buildUrl("zpboss/app/liveRecruit/liveEnd/explainCancel");

    /**
     * 【校招直播】查询直播间预设问题
     * https://api.weizhipin.com/project/30/interface/api/283356
     */
    public static final String URL_GEEK_RECRUIT_QUESTION_QUERY = buildUrl("zpgeek/app/live/recruit/question/query");
    /**
     * 【1005-新增】直播间结束概览
     * https://api.weizhipin.com/project/30/interface/api/285588
     */
    public static final String URL_BOSS_RECRUIT_END_OVERVIEW = buildUrl("zpboss/app/liveRecruit/endOverview");
    /**
     * 【校招直播】直播间附加信息
     * https://api.weizhipin.com/project/30/interface/api/285921
     */
    public static final String URL_GEEK_RECRUIT_ADDITIONAL_QUERY = buildUrl("zplive/audience/geek/live/additional/info");
    /**
     * 1008
     * 【校招直播】投票
     * https://api.weizhipin.com/project/30/interface/api/325179
     */
    public static final String URL_GEEK_RECRUIT_VOTE = buildUrl("zpgeek/app/live/recruit/vote");
    /**
     * 【校招直播】直播间附加信息
     * https://api.weizhipin.com/project/30/interface/api/285480
     */
    public static final String URL_BOSS_GEEK_RECRUIT_ADDITIONAL_QUERY = buildUrl("zpboss/app/liveRecruit/audience/additionalInfo/query");
    /**
     * 1008
     * 【校招直播】B端观众进行投票
     * https://api.weizhipin.com/project/30/interface/api/324675
     */
    public static final String URL_BOSS_AUDIENCE_VOTE_CAST = buildUrl("zpboss/app/liveRecruit/audience/vote/cast");
    /**
     * 【校招直播】预约&取消预约节目单
     * https://api.weizhipin.com/project/30/interface/api/285939
     */
    public static final String URL_GEEK_RECRUIT_CHAPTER_SUBSCRIBE = buildUrl("zpgeek/app/live/recruit/chapter/subscribe");
    /**
     * 【校招直播】预约&取消预约节目单
     * https://api.weizhipin.com/project/30/interface/api/285570
     */
    public static final String URL_BOSS_GEEK_RECRUIT_CHAPTER_SUBSCRIBE = buildUrl("zpboss/app/liveRecruit/audience/programmeChapter/reserve");
    /**
     * 【校招直播】查询直播节目单回放地址
     * https://api.weizhipin.com/project/30/interface/api/285975
     */
    public static final String URL_GEEK_RECRUIT_CHAPTER_PLAYBACK_QUERY = buildUrl("zpgeek/app/live/recruit/chapter/playback/query");
    /**
     * 【校招直播】查询直播节目单回放地址
     * https://api.weizhipin.com/project/30/interface/api/285525
     */
    public static final String URL_BOSS_GEEK_RECRUIT_CHAPTER_PLAYBACK_QUERY = buildUrl("zpboss/app/liveRecruit/audience/livingMark/playUrl");

    /**
     * 【1005-新增】线上权限用户我的直播页面banner列表
     */
    public static final String URL_BOSS_LIVE_RECRUIT_BANNER_LIST = buildUrl("zpboss/app/liveRecruit/myBannerList");

    /**
     * 【校招直播】点击弹幕消息
     * https://api.weizhipin.com/project/30/interface/api/296352
     */
    public static final String URL_GEEK_LIVE_RECRUIT_MESSAGE_CLICK = buildUrl("zpgeek/app/live/recruit/message/click");

    /**
     * BOSS 预约直播
     */
    public static final String URL_BOSS_SUBSCRIBE_LIVE = buildUrl("zpboss/app/liveRecruit/audience/subscribe");
    public static final String URL_BOSS_UNSUBSCRIBE_LIVE = buildUrl("zpboss/app/liveRecruit/audience/unSubscribe");

    /**
     * 【1007-新增】设置回放视频是否对牛人可见
     * https://api.weizhipin.com/project/30/interface/api/304848
     */
    public static final String URL_BOSS_UPDATE_LIVE_VIDEO_FLAG = buildUrl("zpboss/app/liveRecruit/updateLiveVideoFlag");
    /**
     * 1006.803 导播直播间待开播页面的活动专栏 - Boss身份
     * https://api.weizhipin.com/project/30/interface/api/297117
     */
    public static final String URL_BOSS_GUIDE_ACTIVITY_COLUMN = buildUrl("zpboss/app/liveRecruit/audience/activitySpecialColumn");
    /**
     * 1006.803 导播直播间待开播页面的活动专栏 - geek身份
     * https://api.weizhipin.com/project/30/interface/api/297225
     */
    public static final String URL_GEEK_GUIDE_ACTIVITY_COLUMN = buildUrl("zpgeek/app/live/recruit/column/activity/query");

    /**
     * 网络通话转人工
     * https://api.weizhipin.com/project/30/interface/api/307386
     */
    public static final String URL_KEFU_AUDIO_TRANSFER_CUSTOM = buildUrl("kefu/app/audio/transferCustom");
    /**
     * 创建智慧石会话
     * https://api.weizhipin.com/project/30/interface/api/309870
     */
    public static final String URL_KEFU_AUDIO_SESSION_CREATE = buildUrl("kefu/app/audio/session/create");
    /**
     * 网络通话心跳
     * https://api.weizhipin.com/project/30/interface/api/309906
     */
    public static final String URL_KEFU_AUDIO_HEARTBEAT = buildUrl("kefu/app/audio/heartbeat");
    /**
     * 客服进线信息查询
     * https://api.weizhipin.com/project/30/interface/api/311202
     */
    public static final String URL_KEFU_AUDIO_SEAT_INFO = buildUrl("kefu/app/audio/querySeatInfo");
    /**
     * 网络通话事件回调
     * https://api.weizhipin.com/project/30/interface/api/309825
     */
    public static final String URL_KEFU_AUDIO_EVENT_CALLBACK = buildUrl("kefu/app/audio/eventCallback");
    /**
     * 【1007-新增】查看直播间投递牛人
     * https://api.weizhipin.com/project/30/interface/api/312417
     */
    public static final String URL_BOSS_VIEW_DELIVERY_GEEK = buildUrl("zpboss/app/liveRecruit/viewDeliveryGeek");
    /**
     * 【1008-新增】直播连麦-获取连麦验证信息
     * https://api.weizhipin.com/project/30/interface/api/322560
     */
    public static final String URL_BOSS_LIVE_INVITE_CALL_INFO = buildUrl("zpboss/app/liveRecruit/callon/info");
    /**
     * 1008.801 B端观众直播间往期投票
     */
    public static final String URL_BOSS_GET_VOTE_LIST = buildUrl("zpboss/app/liveRecruit/audience/vote/history");

    /**
     * 1008.801 c端观众直播间往期投票
     */
    public static final String URL_GEEK_GET_VOTE_LIST = buildUrl("zpgeek/app/live/recruit/voted/list");
    /**
     * 【1008-新增】直播连麦-连麦操作
     * https://api.weizhipin.com/project/30/interface/api/322218
     */
    public static final String URL_BOSS_LIVE_CALL_CONNECT_ACTION = buildUrl("zpboss/app/liveRecruit/callon");
    /**
     * 【1008-新增】直播连麦-连麦踢人
     * https://api.weizhipin.com/project/30/interface/api/322605
     */
    public static final String URL_BOSS_LIVE_KILL_CALL_CONNECT = buildUrl("zpboss/app/liveRecruit/callon/kicking");
    /**
     * 【1008-新增】直播连麦-静音
     * https://api.weizhipin.com/project/30/interface/api/322614
     */
    public static final String URL_BOSS_LIVE_CALL_CONNECT_VOLUME_MUTE = buildUrl("zpboss/app/liveRecruit/callon/mute");
    /**
     * 【1009-新增】线上支付权限用户优惠信息
     * https://api.weizhipin.com/project/30/interface/api/334467
     */
    public static final String URL_BOSS_LIVE_ONLINE_USER_COUPON = buildUrl("zpboss/app/liveRecruit/online/user/discount");
    /**
     * 【1009-新增】直播中牛人投递列表
     * https://api.weizhipin.com/project/30/interface/api/336078
     */
    public static final String URL_BOSS_LIVE_GEEK_DELIVERY_LIST = buildUrl("zpboss/app/liveRecruit/geek/delivery/list");
    
    /**
     * 直播中牛人投递列表个人直播
     * https://api.weizhipin.com/project/30/interface/api/780535
     */
    public static final String URL_BOSS_LIVE_GEEK_DELIVERY_LIST_ONLINE = buildUrl("zpboss/app/liveRecruit/geek/delivery/list/onlinePay");
    /**
     * 【1009-新增】牛人在线简历
     * https://api.weizhipin.com/project/30/interface/api/337227
     */
    public static final String URL_BOSS_LIVE_GEEK_ONLINE_RESUME = buildUrl("zpboss/app/liveRecruit/geek/online/resume");
    /**
     * 【1010-新增】本场boss-直播中牛人投递列表
     * https://api.weizhipin.com/project/30/interface/api/359091
     */
    public static final String URL_BOSS_PROXY_GEEK_DELIVERY_LIST = buildUrl("zpboss/app/liveRecruit/proxy/geek/delivery/listV2");
    /**
     * 【1015-新增】直播中三档流量价格展示
     * https://api.weizhipin.com/project/30/interface/api/443685
     */
    public static final String URL_BOSS_LIVE_FLOW_SHOW_LIST = buildUrl("zpboss/app/liveRecruit/online/flow/show");


    /**
     * 【校招直播】查询已投递职位
     * https://api.weizhipin.com/project/30/interface/api/359475
     */
    public static final String URL_GEEK_LIVE_JOB_DELIVERED_LIST = buildUrl("zpgeek/app/live/recruit/job/delivered/list");

    /**
     * 【校招直播】查询职位分数排行列表
     * https://api.weizhipin.com/project/30/interface/api/359483
     */
    public static final String URL_GEEK_LIVE_JOB_RANK_LIST = buildUrl("zpgeek/app/live/recruit/brand/rank/list");

    /**
     * 【校招直播】查询职位可贡献分数值
     * https://api.weizhipin.com/project/30/interface/api/359515
     */
    public static final String URL_GEEK_JOB_SCORE_OPTION_LIST = buildUrl("zpgeek/app/live/recruit/job/score/option/list");

    /**
     * 【校招直播】对职位打投
     * https://api.weizhipin.com/project/30/interface/api/359507
     */
    public static final String URL_GEEK_JOB_SCORE_DEVOTE = buildUrl("zpgeek/app/live/recruit/job/score/devote");

    /**
     * 【1010-新增】牛人在线简历V2
     * https://api.weizhipin.com/project/30/interface/api/370115
     */
    public static final String URL_BOSS_LIVE_GEEK_ONLINE_RESUME_V2 = buildUrl("zpboss/app/liveRecruit/geek/online/resumeV2");

    /**
     * 【1016-新增】直播字幕开关
     * https://api.weizhipin.com/project/30/interface/api/453226
     */
    public static final String URL_BOSS_LIVE_SUBTITLE_SWITCH = buildUrl("zpboss/app/liveRecruit/subtitleSwitch");

    /**
     * 【校招直播】打投品牌
     * https://api.weizhipin.com/project/30/interface/api/453093
     */
    public static final String URL_BOSS_LIVE_PROXY_COMPANY_HIT_DEVOTE = buildUrl("zpgeek/app/live/recruit/brand/devote");

    /**
     * 【校招直播】打投-查询分组列表
     * https://api.weizhipin.com/project/30/interface/api/456096
     */
    public static final String URL_BOSS_LIVE_COMPANY_HIT_GROUP_LIST = buildUrl("zpgeek/app/live/recruit/group/list");

    /**
     * 【校招直播】打投-查询分组列表 b身份
     * https://api.weizhipin.com/project/30/interface/api/456964
     */
    public static final String URL_BOSS_LIVE_B_COMPANY_HIT_GROUP_LIST = buildUrl("zpboss/app/liveRecruit/audience/jobGroup/list");

    /**
     * 【1017-新增】直播切片引导信息
     * https://api.weizhipin.com/project/30/interface/api/473755
     */
    public static final String URL_BOSS_RECRUIT_GUIDE_INFO_LIST = buildUrl("zpboss/app/liveRecruit/explain/guideInfoList");

    /**
     * 【1017-新增】获取美颜默认配置信息
     * https://api.weizhipin.com/project/30/interface/api/486673
     */
    public static final String URL_BOSS_RECRUIT_BEAUTY_DEFAULT_CONFIG = buildUrl("zpboss/app/liveRecruit/beauty/defaultConfig");

    /**
     * 【1017-新增】获取贴纸默认配置信息
     * https://api.weizhipin.com/mock/30/api/zpboss/app/liveRecruit/beauty/paster/defaultConfig
     */
    public static final String URL_BOSS_RECRUIT_PASTER_DEFAULT_CONFIG = buildUrl("zpboss/app/liveRecruit/beauty/paster/defaultConfig");

    /**
     * 【校招直播】查询抽奖答题信息
     * https://api.weizhipin.com/project/30/interface/api/509386
     */
    public static final String URL_GEEK_RECRUIT_DRAW_QUESTION_QUERY = buildUrl("zpgeek/app/live/recruit/draw/question/query");

    /**
     * 【校招直播】查询抽奖答题信息
     * https://api.weizhipin.com/project/30/interface/api/509368
     */
    public static final String URL_BOSS_RECRUIT_DRAW_QUESTION_QUERY = buildUrl("zpboss/app/liveRecruit/audience/lucky/draw/question/info");

    /**
     * 【校招直播】查询抽奖答题信息
     * https://api.weizhipin.com/project/30/interface/api/509374
     */
    public static final String URL_BOSS_PROXY_RECRUIT_DRAW_QUESTION_QUERY = buildUrl("zpboss/app/liveRecruit/proxy/lucky/draw/question/info");

    /**
     * 【1103-新增】付费直播直通卡三档价格展示
     * https://api.weizhipin.com/project/30/interface/api/512482
     */
    public static final String URL_BOSS_PASS_THROUGH_CARD_PRICE_SHOW = buildUrl("zpboss/app/liveRecruit/passThrough/card/price/show");
    /**
     * 【1103-新增】直播中牛人投递列表-职位创建者可看
     * https://api.weizhipin.com/project/30/interface/api/512722
     */
    public static final String URL_BOSS_COMPANY_COLLEAGUE_GEEK_DELIVERY = buildUrl("zpboss/app/liveRecruit/geek/delivery/list/jobCreator");
    /**
     * 【1204-新增】推荐邀请牛人列表
     */
    public static final String URL_BOSS_INVITE_DELIVER_GEEK_LIST = buildUrl("zpboss/app/liveRecruit/inviteResume/rcmdGeekList");
    /**
     * 【1204-新增】邀请牛人投递
     */
    public static final String URL_BOSS_INVITE_DELIVER = buildUrl("zpboss/app/liveRecruit/inviteResume/invite");
    /**
     * 【1204-新增】推荐邀请牛人职位筛选列表
     */
    public static final String URL_BOSS_INVITE_DELIVER_CHANGE_JOBS = buildUrl("zpboss/app/liveRecruit/inviteResume/filterJob");

    /**
     * 【1103-新增】直播我的蓝领账户
     * https://api.weizhipin.com/project/30/interface/api/514186
     */
    public static final String URL_BOSS_BLUE_COLLAR_ACCOUNT = buildUrl("zpboss/app/liveRecruit/blue/collar/account");
    /**
     * 【校招直播】查询直播间直播详情Tab信息
     * https://api.weizhipin.com/project/30/interface/api/521126
     */
    public static final String URL_GEEK_RECRUIT_DETAIL_TAB_QUERY = buildUrl("zpgeek/app/live/recruit/detail/tab/query");

    /**
     * 【校招直播】直播详情Tab信息-查询直播招聘企业列表
     * https://api.weizhipin.com/project/30/interface/api/521497
     */
    public static final String URL_GEEK_RECRUIT_DETAIL_BRAND_LIST = buildUrl("zpgeek/app/live/recruit/detail/tab/brand/list");

    /**
     * 【校招直播】直播详情Tab信息-查询直播招聘公司全称列表
     * https://api.weizhipin.com/project/30/interface/api/703529
     */
    public static final String URL_GEEK_RECRUIT_DETAIL_COMPANY_LIST = buildUrl("zpgeek/app/live/recruit/detail/tab/company/list");

    /**
     * 直播详情Tab信息-查询直播BOSS列表
     * https://api.weizhipin.com/project/30/interface/api/521499
     */
    public static final String URL_GEEK_RECRUIT_DETAIL_BOSS_LIST = buildUrl("zpgeek/app/live/recruit/detail/tab/boss/list");
    /**
     * 【校招直播】查询预设弹幕
     * https://api.weizhipin.com/project/30/interface/api/526573
     */
    public static final String URL_GEEK_RECRUIT_QUICK_QUESTION_QUERY = buildUrl("zpgeek/app/live/recruit/barrage/preset/query");
    public static final String URL_GEEK_RECRUIT_QUICK_QUESTION_QUERY_B = buildUrl("zpboss/app/liveRecruit/presetDiaLog");
    /**
     * 【1017-扩展】直播视频参数切换切换
     * https://api.weizhipin.com/project/30/interface/api/526277
     */
    public static final String URL_BOSS_LIVE_TOOLS_SWITCH = buildUrl("zpboss/app/liveRecruit/nebulaParam/switch");
    /**
     * 【1208-新增】设定直播app美颜参数
     * https://api.weizhipin.com/project/30/interface/api/700234
     */
    public static final String URL_BOSS_LIVE_TOOLS_SWITCH_BEAUTY = buildUrl("zpboss/app/liveRecruit/nebulaParam/switch/beauty");

    /**
     * 【1106-新增】直播回放地址修改
     * https://api.weizhipin.com/project/30/interface/api/526365
     */
    public static final String URL_BOSS_RECRUIT_PLAYBACK_VIDEO_MODIFIED = buildUrl("zpboss/app/liveRecruit/livePlayBackVideo/modified");
    /**
     * 【1211-711】obs直播后编辑讲解
     */
    public static final String URL_BOSS_RECRUIT_PLAYBACK_VIDEO_MODIFIED_OBS = buildUrl("zpboss/app/liveRecruit/obs/explain/endLiveExplain");

    /**
     * 【1107-新增】直播讲解切片录制-获取sdk信息
     * https://api.weizhipin.com/project/30/interface/api/527493
     */
    public static final String URL_BOSS_LIVE_END_EXPLAIN_NEBULA_SDK = buildUrl("zpboss/app/liveRecruit/liveEnd/explainNebulaSDK");

    /**
     * 【1107-新增】直播视频参数获取-用于直播后切片录制
     * https://api.weizhipin.com/project/30/interface/api/526997
     */
    public static final String URL_BOSS_LIVE_END_EXPLAIN_NEBULA_PARAM = buildUrl("zpboss/app/liveRecruit/nebulaParam");

    /**
     * 【1107-新增】【校招直播】投递简历前校验
     * https://api.weizhipin.com/project/30/interface/api/531055
     */
    public static final String URL_GEEK_LIVE_DELIVER_PRE_CHECK = buildUrl("zpgeek/app/live/recruit/job/deliver/precheck");

    /**
     * 【1215-新增】直播首页入口-线上权限
     */
    public static final String URL_BOSS_LIVE_MANAGE_HOME_PAGE_ONLINE = buildUrl("zpboss/app/liveRecruit/home/<USER>");
    /**
     * 【1215-新增】直播首页推荐职位-线上权限
     */
    public static final String URL_BOSS_LIVE_MANAGE_RCMD_JOB = buildUrl("zpboss/app/liveRecruit/home/<USER>");

    /**
     * 【1123-新增】直播创建检查
     * https://api.weizhipin.com/project/30/interface/api/642509
     */
    public static final String URL_BOSS_LIVE_MANAGE_BTN_CLICK_CHECK = buildUrl("zpboss/app/liveRecruit/home/<USER>/check");


    /**
     * 【校招直播】查询职位卡片
     * https://api.weizhipin.com/project/30/interface/api/663150
     */
    public static final String URL_LIVE_JOB_CARD_QUERY = buildUrl("zplive/audience/geek/job/card/query");
    /**
     * 【校招直播】推荐职位列表
     * https://api.weizhipin.com/project/30/interface/api/351147
     */
    public static final String URL_GEEK_LIVE_RECOMMEND_JOB_LIST = buildUrl("zpgeek/app/live/recruit/exclusive/job/list");
    /**
     * 【1210-新增】通知主播结束录制讲解
     */
    public static final String URL_GEEK_LIVE_NOTIFY_ANCHOR_START_EXPLAIN = buildUrl("zpboss/app/liveRecruit/explain/notify/anchor/start");
    /**
     * 【1211-711】obs直播讲解开始讲解
     */
    public static final String URL_GEEK_LIVE_OBS_START_EXPLAIN = buildUrl("zpboss/app/liveRecruit/obs/explain/start");
    /**
     * 【1210-新增】通知主播结束录制讲解
     */
    public static final String URL_GEEK_LIVE_NOTIFY_ANCHOR_END_EXPLAIN = buildUrl("zpboss/app/liveRecruit/explain/notify/anchor/end");
    /**
     * 【1211-711】obs直播讲解完成讲解
     */
    public static final String URL_GEEK_LIVE_OBS_END_EXPLAIN = buildUrl("zpboss/app/liveRecruit/obs/explain/finish");
    /**
     * 【1210-新增】推职位
     */
    public static final String URL_GEEK_LIVE_NOTIFY_PUSH_JOB = buildUrl("zpboss/app/liveRecruit/pushJob");
    /**
     * 【1215-新增】即时开播-预览页基础信息
     */
    public static final String URL_BOSS_LIVE_PRE_COVER_INFO = buildUrl("zpboss/app/liveRecruit/instantLive/preview/page/info");
    /**
     * 【1215-新增】即时开播-创建个人直播间
     */
    public static final String URL_BOSS_LIVE_CREATE_LIVE = buildUrl("zpboss/app/liveRecruit/instantLive/personal/createLive");
    /**
     * 【1215-新增】即时开播-职位筛选项
     */
    public static final String URL_BOSS_LIVE_FILTER_OPTIONS = buildUrl("zpboss/app/liveRecruit/instantLive/job/filterOptions");
    /**
     * 【1215-新增】即时开播-职位列表
     */
    public static final String URL_BOSS_LIVE_PRE_JOB_LIST = buildUrl("zpboss/app/liveRecruit/instantLive/job/list");
    /**
     * 【1215-713 新增】直播学院滑动到底部，规则中心链接
     */
    public static final String URL_LIVE_RULE_CENTER = URLConfig.getWebHost() + "h5-live/rule-center?type=live";
    /**
     * 【app-直播】用户报名信息
     */
    public static final String URL_BOSS_LIVE_RECRUIT_REGISTER_INFO = buildUrl("zpgeek/app/live/recruit/user/info");
    /**
     * 【1223-新增】直播预约-管理员主播权限开通
     */
    public static final String URL_BOSS_LIVE_RECRUIT_CREATE_USER_CHECK = buildUrl("zpboss/app/liveRecruit/create/userCheck");
    /**
     * 【1302-新增】投递后的相似职位推荐
     */
    public static final String URL_GEEK_RCMD_JOB_RESUME= buildUrl("zplive/audience/geek/job/rcmd/jobByResume");

    public static final String URL_MEIYAN_CONFIG = buildUrl("zpinterview/meiyan/getConfig");

    public static final String URL_MEIYAN_SAVE_CONFIG = buildUrl("zpinterview/meiyan/saveConfig");

    /**
     * 【1302-新增】获取普通直播(蓝领)专场标签列表
     * https://api.weizhipin.com/project/30/interface/api/743719
     */
    public static final String URL_GEEK_NORMAL_SPECIAL_SESSION_TAGS = buildUrl("zplive/audience/geek/normal/special/session/tags");

    /**
     * 【1302-新增】获取普通直播(蓝领)专场下直播列表
     * https://api.weizhipin.com/project/30/interface/api/743737
     */
    public static final String URL_GEEK_NORMAL_SPECIAL_SESSION_TAG_LIVES = buildUrl("zplive/audience/geek/normal/special/session/tag/lives");

    /**
     * 【host】高光封面基本信息查询
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_INFO = buildUrl("zplive/host/highlight/enable/simpleinfo");
    
    /**
     * 【host】更新高光状态开关
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_SWITCH = buildUrl("zplive/host/highlight/enable/record");
    /**
     * 【1307-新增】查询直播高光列表
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_LIST= buildUrl("zplive/host/highlight/list");
    /**
     * 【1307-新增】直播高光按钮信息
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_BTN_INFO= buildUrl("zplive/host/highlight/btn/info");
    /**
     * 【1307-新增】发布直播间下直播高光视频
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_PUBLISH= buildUrl("zplive/host/highlight/publish");
    /**
     * 【1307-新增】直播高光视频后续自动发布到社区
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_AUTO_PUBLISH= buildUrl("zplive/host/highlight/after/auto/publish");
    /**
     * 【1307-新增】删除某个直播高光视频
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_DELETE= buildUrl("zplive/host/highlight/delete");
    /**
     * 【1307-新增】获取高光视频的播放地址
     */
    public static final String URL_BOSS_LIVE_HIGHLIGHT_PLAY_URL= buildUrl("zplive/host/highlight/play/url");
    /**
     * 【1309-新增】直播间AI脚本入口
     */
    public static final String URL_BOSS_PRE_LIVE_AI_SCRIPT= buildUrl("zplive/host/ai-script/entrance");
    /**
     * 【1309-新增】直播AI脚本绑定会话和直播间
     */
    public static final String URL_BOSS_LIVE_BIND_SESSION= buildUrl("zplive/host/ai-script/bind-live");
    /**
     * 【1310-新增】直播中修改虚拟背景
     */
    public static final String URL_BOSS_LIVE_VIRTUALBACKGROUND_UPDATE= buildUrl("zpboss/app/liveRecruit/virtualBackground/update");
    /**
     * 【校招直播】发送微信号
     */
    public static final String URL_ZPGEE_SENDWECHAT = buildUrl("zpgeek/app/live/recruit/job/sendWechat");
    /**
     * 【校招直播】发送电话号
     */
    public static final String URL_ZPGEE_SENDPHONE = buildUrl("zpgeek/app/live/recruit/job/sendPhone");
}
