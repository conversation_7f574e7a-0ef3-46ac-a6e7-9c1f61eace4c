package com.hpbr.bosszhipin.chat.single.activity;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Pair;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.chat.R;
import com.hpbr.bosszhipin.chat.adapter.ChatAdapter2;
import com.hpbr.bosszhipin.chat.adapter.itf.IAdapterCallBack;
import com.hpbr.bosszhipin.chat.adapter.itf.IRefreshAdapterCallBack;
import com.hpbr.bosszhipin.chat.adapter.itf.ISendUserCallBack;
import com.hpbr.bosszhipin.chat.banner.BossEnter;
import com.hpbr.bosszhipin.chat.banner.toptips.ChatTopStatusView;
import com.hpbr.bosszhipin.chat.banner.toptips.IUserEnter;
import com.hpbr.bosszhipin.chat.constant.ChatConstant;
import com.hpbr.bosszhipin.chat.dialog.ChoosePositionDialog2;
import com.hpbr.bosszhipin.chat.dialog.QuestionNaireManager;
import com.hpbr.bosszhipin.chat.entity.AIGeneratedResultEntity;
import com.hpbr.bosszhipin.chat.exchange.ExchangePhoneManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeRequireResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeSendResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeWeChatManager;
import com.hpbr.bosszhipin.chat.export.constant.SingleConstant;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.chat.export.routerservice.IChatNlpTransferService;
import com.hpbr.bosszhipin.chat.nlp.BossInterviewExpressGuideDialog;
import com.hpbr.bosszhipin.chat.nlp.ChatNLPTransfer;
import com.hpbr.bosszhipin.chat.nlp.DailyGuideBean;
import com.hpbr.bosszhipin.chat.nlp.DispatchSuggestCallBack;
import com.hpbr.bosszhipin.chat.nlp.IChatNLPObserver;
import com.hpbr.bosszhipin.chat.nlp.NLPContainerLayout;
import com.hpbr.bosszhipin.chat.nlp.NLPListBean;
import com.hpbr.bosszhipin.chat.nlp.OnSuggestCallBack;
import com.hpbr.bosszhipin.chat.nlp.view.ChatSuggestView;
import com.hpbr.bosszhipin.chat.nlp.view.NormalView;
import com.hpbr.bosszhipin.chat.single.BossBlockCheck;
import com.hpbr.bosszhipin.chat.single.ChatCommon;
import com.hpbr.bosszhipin.chat.single.dialog.ChatEditGreetingWordDialog;
import com.hpbr.bosszhipin.chat.single.listener.AIChatPanelListener;
import com.hpbr.bosszhipin.chat.single.listener.IChatCommon;
import com.hpbr.bosszhipin.chat.single.listener.OnBindAdapterCallBack;
import com.hpbr.bosszhipin.chat.single.presenter.chat.ChatBottomFuncPresenter;
import com.hpbr.bosszhipin.chat.single.presenter.chat.IChatBottomFuncView;
import com.hpbr.bosszhipin.chat.single.presenter.chat.exchange2.BossRoleChatBossUserPresenter2;
import com.hpbr.bosszhipin.chat.single.presenter.chat.exchange2.GeekRoleChatBossUserPresenter2;
import com.hpbr.bosszhipin.chat.utils.AIHelpPanelUtils;
import com.hpbr.bosszhipin.chat.utils.AudioCovertTextUtils;
import com.hpbr.bosszhipin.chat.utils.ChatAnalysisUtil;
import com.hpbr.bosszhipin.chat.utils.ChatBlockManager;
import com.hpbr.bosszhipin.chat.utils.ExchangeMessage;
import com.hpbr.bosszhipin.chat.utils.SingleChatOnlineUtil;
import com.hpbr.bosszhipin.chat.utils.UploadChatManager;
import com.hpbr.bosszhipin.chat.view.AIAssistChatView;
import com.hpbr.bosszhipin.chat.view.AIAssistantView;
import com.hpbr.bosszhipin.chat.view.AIHelpChatPanel;
import com.hpbr.bosszhipin.chat.view.ChatQuoteMsgLayout;
import com.hpbr.bosszhipin.chat.view.NLPDispatchLayout;
import com.hpbr.bosszhipin.common.SwitchCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.ChatWechatDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.VirtualPhoneManage;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.config.GrayStageManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.NLPSuggestBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.event.analytics.AnalyticsApp;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.interviews.export.InterviewsRouter;
import com.hpbr.bosszhipin.listener.Callback;
import com.hpbr.bosszhipin.listener.OnZPUIChatGifLongClickListener;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.block.utils.OnlineRemindUtils;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.module.contacts.adapter.listener.OnMessageSendCallBack;
import com.hpbr.bosszhipin.module.contacts.constant.IntentParamConstant;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.NewQuickReplyBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.QuickReplyManager;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.exchange.BossUnfitChangeManager;
import com.hpbr.bosszhipin.module.contacts.exchange.RejectHttpManager;
import com.hpbr.bosszhipin.module.contacts.exchange.UserRejectManager;
import com.hpbr.bosszhipin.module.contacts.manager.CommonWordManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.contacts.service.ChatBeanFactory;
import com.hpbr.bosszhipin.module.contacts.service.ChatSendCallback;
import com.hpbr.bosszhipin.module.contacts.sounds.SoundFile;
import com.hpbr.bosszhipin.module.contacts.util.ChatUtils;
import com.hpbr.bosszhipin.module.contacts.util.SendMessageUtil;
import com.hpbr.bosszhipin.module.interview.api.GetInterviewDetailResponse;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCreateIntentBean;
import com.hpbr.bosszhipin.module.interview.entity.InterviewParams;
import com.hpbr.bosszhipin.module.interview.utils.InterviewCommonUtils;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.fragment.contacts.F2ContactMarkLabelManager;
import com.hpbr.bosszhipin.module.main.fragment.contacts.viewholder.GeeKQuestionResearchManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.AIDirectChatManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.module.my.activity.boss.brand.bean.BrandInfoBean;
import com.hpbr.bosszhipin.module.my.activity.boss.dialog.UploadHeadDialog;
import com.hpbr.bosszhipin.module.my.activity.boss.location.SelectWorkLocationActivity;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.protocol.ProtocolConstants;
import com.hpbr.bosszhipin.utils.CloneUtils;
import com.hpbr.bosszhipin.utils.LiveDataBus;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.hpbr.bosszhipin.utils.screenshot.IScreenShotCallback;
import com.hpbr.bosszhipin.utils.screenshot.ScreenShotSetting;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.ContactExchangeChatView;
import com.hpbr.bosszhipin.views.MPoupWindow;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.MessageSlideView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.chatbottom2.commonwords.CommonWordsUIBean;
import com.hpbr.bosszhipin.views.chatbottom2.depends.single.BossChatMoreParam;
import com.hpbr.bosszhipin.views.chatbottom2.depends.single.BossUserLayout;
import com.hpbr.bosszhipin.views.chatbottom2.emotion.ExtendEmotion;
import com.hpbr.bosszhipin.views.chatbottom2.emotion.data.ChatEmotionDataManager;
import com.hpbr.bosszhipin.views.exchange.depend2.BossZPConversationExchangeContainer2;
import com.hpbr.bosszhipin.views.exchange.icon.OnExchangeCallBack;
import com.hpbr.bosszhipin.views.exchange.icon.boss2.WeiChatConversationIconView;
import com.hpbr.bosszhipin.views.exchange.utils.ExchangeHelper;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.signer.Signer;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.AIAssistInfo;
import net.bosszhipin.api.AIDirectChatReasonRequest;
import net.bosszhipin.api.AIDirectChatReasonResponse;
import net.bosszhipin.api.BossGreetingGuideBean;
import net.bosszhipin.api.CheckAgentAvatarRequest;
import net.bosszhipin.api.CheckAgentAvatarResponse;
import net.bosszhipin.api.ResultStringResponse;
import net.bosszhipin.api.UnlockChatRequest;
import net.bosszhipin.api.UnlockChatResponse;
import net.bosszhipin.api.bean.AIHelpChatParam;
import net.bosszhipin.api.bean.AgreeExchangeBean;
import net.bosszhipin.api.bean.ChatPendingTaskBean;
import net.bosszhipin.api.bean.JobSwitchBean;
import net.bosszhipin.api.bean.NLPSuggestResultBean;
import net.bosszhipin.api.bean.SendMsgExtraBean;
import net.bosszhipin.api.bean.ServerVipItemBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

import message.handler.MSGSender;
import message.handler.MessageTargetInfo;
import message.handler.MessageUtils;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;
import message.server.connect.ConnectStatus;
import zpui.lib.ui.popup.ZPUIPopupList;
import zpui.lib.ui.refreshlayout.ZPUIRefreshLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class ChatRoomActivity extends ChatBaseActivity implements IChatCommon,
        IChatNLPObserver, OnBindAdapterCallBack {


    public static final String TAG = "ChatRoomActivity";

    // 默认标题栏内容
    private String title = "";
    // 数据源
    //外面传入的friendId用于加好友,可能是0,加完好友后从obtainFriendId()方法获得联系人
    private long tempFriendId;
    private long jobId;
    private String from;
    private BossUserLayout bottomFunctionView;

    private MessageSlideView slideView;
    private ChatBlockManager chatBlockManager;
    private FrameLayout flBottom;
    private NLPDispatchLayout mNlpView;
    private View mPayCoverView;
    private String securityId;

    private ScreenShotSetting screenShotSetting;
    private ChatCommon common;

    private ChatQuoteMsgLayout chatQuoteMsgView;
    private AppTitleView titleView;
    // 聊天列表控件
    private RecyclerView recyclerView;
    private ZPUIRefreshLayout mRefreshLayout;
    private BossZPConversationExchangeContainer2 bossUserExchangeContainer;
    private ChatTopStatusView tipStatusView;
    private int appBarLayoutCollapseCount; //用于过滤自动appBarLayout展开和收缩的
    private SimpleDraweeView imgChatBj;
    private String defaultInputText;
    private AIAssistantView aiAssistantView;
    private AIAssistantView.AIAssistantParam aiAssistantParam;
    private AIAssistChatView aiAssistChatView;
    private AIHelpChatPanel aiHelpChatPanel;

    private ChatBean newMessage; // 用与记录在AI生成过程中是否收到新消息，如有生成完毕后再次生成
    private NLPListBean curNlpListBean; // 用与记录NLP消息展示时，又收到了新NLP消息，如果是则丢弃新的
    private ChatPendingTaskBean aiHelpChatPendingTask; // 临时保存任务。AI帮聊窗口必须在定位权限被授予后执行
    private boolean hasReportAIPanelShow;
    private boolean autoShowKeyBoard;
    private View aiDirectReasonPopupView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F));
        //注册监听NLP接收器
        IChatNlpTransferService nlpTransferService = SingleRouter.getNlpTransferService();
        if (nlpTransferService != null) {
            nlpTransferService.register(this);
        }
    }

    @Override
    protected void onCreateView(Bundle savedInstanceState) {
        common = new ChatCommon();

        Intent intent = getIntent();
        initParams(intent);
        setContentView(R.layout.chat_room_activity);

        if (checkFriendIdSecurityIdEmpty()) return;

        //创建好友关系
        common.init(this);
        common.create(tempFriendId);
        common.initSwipeRefreshHelper(mRefreshLayout, recyclerView, bottomFunctionView);
        common.setOnShowExchangeGuideCallback(exchangeGuideBean -> {
            if (UserManager.isGeekRole() || exchangeGuideBean == null || LText.empty(exchangeGuideBean.tip)) {
                return;
            }

            bossUserExchangeContainer.showBossGuideTip(this, 3, exchangeGuideBean.tip);
            ChatAnalysisUtil.statsInterviewPopShow(common.getFriendId());
        });

        initScreenShot();
        checkAgentAvatar();
        initEventListener();
    }

    private void initEventListener() {
        /*刷新聊天页顶部在线绿点以及异常信息标签*/
        LiveDataBus.multi(ChannelConstants.REFRESH_CHAT_ONLINE_AND_EXCEPTION_TIP_LABEL, Object.class).observe(this, obj -> {
            refreshOnlineIconAndExceptionTipLabel(null);
        });
        /*刷新聊天页副标题*/
        LiveDataBus.multi(ChannelConstants.REFRESH_CHAT_SUB_TITLE, Object.class).observe(this, obj -> {
            obtainFriendId(contactBean -> {
                /*副标题*/
                titleView.setSubTitle(getSubTitleContent(contactBean, contactBean.note, contactBean.getLabelsCollection()));
            });
        });

        registerVoiceObserver();
    }

    private void registerVoiceObserver() {

        String friendKey = common.getFriendId() + "_" + common.getFriendSource();
        LiveDataBus.multi(ChannelConstants.SUCCESS_DOWNLOAD_AUDIO_FILE, String.class).observe(this, url -> {
            Pair<String, ChatBean> paramPair;
            paramPair = AudioCovertTextUtils.getInstance().mReceiveAudioMsgMap.get(SoundFile.getInstance().getFileName(String.valueOf(url)));

            if (paramPair == null) {
                paramPair = AudioCovertTextUtils.getInstance().mOnClickAudioMsgMap.get(SoundFile.getInstance().getFileName(String.valueOf(url)));
            }

            String fileParent = PathUtils.getCacheDirChildPathExternalFirst("sound");
            File fileSound = FileUtils.getFileByPath(fileParent, SoundFile.getInstance().getFileName((String) url));

            if (paramPair != null && paramPair.first.startsWith(friendKey) && paramPair.second != null) {
                AudioCovertTextUtils.getInstance().convertAudioToText(new AudioCovertTextUtils.AudioCovertTextParam(paramPair.first, fileSound.getPath(), paramPair.second.msgId, paramPair.second));
                common.soundFirstTrans(paramPair.second.msgId, false);
            }

        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_COVERT_RESULT).observe(this, obj -> {
            if (chatAdapter2 == null || LList.isEmpty(chatAdapter2.getData())) return;
            Pair<String, AudioCovertTextUtils.AudioCovertTextParam> param = (Pair<String, AudioCovertTextUtils.AudioCovertTextParam>) obj;
            AudioCovertTextUtils.AudioCovertTextParam audioCovertTextParam = param.second;
            String text = param.first;
            if (audioCovertTextParam.getFriendKey().startsWith(friendKey)) {
                common.updateAudioMessage(audioCovertTextParam.getMid(), text, param.second.isNeedUpdate());
            }
        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_FIRST_PLAY).observe(this, mid -> {
            common.soundFirstPlay((Long) mid);
        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_FIRST_TRANS).observe(this, param -> {
            Pair<Long, Boolean> paramPair = (Pair<Long, Boolean>) param;
            common.soundFirstTrans(paramPair.first, paramPair.second);
        });

    }

    @Override
    protected void onRestart() {
        super.onRestart();
        tipStatusView.postInvalidateUI();
        tipStatusView.onRestart();
        refreshExchangeView();
        fixPageReloadMessage();
    }

    /**
     * 快速处理 是否显示过 键盘
     */
    private boolean hasShowFastHandlerKeyBoard;


    /**
     * 点击搜索建议词的nlp内容
     */
    private String nlpSuggestValue;

    private final OnSuggestCallBack onSuggestCallBack = new OnSuggestCallBack() {


        @Override
        public void sendLocationToGeek(long id, String type, String label) {//发
            obtainFriendId(contactBean -> {
                // 送位置到牛人
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_LOCATION_SEND)
                        .param("p", "" + contactBean.friendId)
                        .param("p2", label)
                        .build();
                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                NLPContainerLayout.sendNLPLocationAction(contactBean.friendId, id, getJobId());
            });

        }

        @Override
        public void exchangePhone(long id, String type, String label, @Nullable NLPSuggestResultBean nlpSuggestResultBean) {//交换电话
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (onAgreeExchange(LText.getInt(type)) >= 0) {
                return;
            }

            onExchangePhoneListener(nlpSuggestResultBean);
        }

        @Override
        public void exchangeWeiChat(long id, String type, String label, @Nullable NLPSuggestResultBean nlpSuggestResultBean) {//交换微信
            noticeCleanNLPMessage(id, type, true);
            if (onAgreeExchange(LText.getInt(type)) >= 0) {
                return;
            }
            onExchangeWeChatListener(nlpSuggestResultBean);
        }

        @Override
        public void sendResume(long id, String type, String label) {//发送简历
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    //隐藏NLP消息
                    noticeCleanNLPMessage(id, type, true);
                    if (onAgreeExchange(LText.getInt(type)) >= 0) {
                        return;
                    }

                    ExchangeSendResumeManager sendResumeManager = new ExchangeSendResumeManager(ChatRoomActivity.this, contactBean);
                    sendResumeManager.setCallBack(ChatRoomActivity.this::refreshExchangeView);
                    sendResumeManager.onChatSendResumeListener();
                }
            });

        }

        @Override
        public void requestResume(long id, String type, String label) {//请求简历

            obtainFriendId(contactBean -> {

                if (!contactBean.isContactEachOther()) {
                    ToastUtils.showText("双方回复之后才能使用");
                    return;
                }

                //Boss付费阻断
                if (UserManager.isBossRole() && contactBean.isTechGeekBlock) {
                    ChatBlockManager chatBlockManager = getChatBlockManager();
                    if (chatBlockManager != null) {
                        chatBlockManager.showPayDialog();
                    }
                    return;
                }

                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                if (onAgreeExchange(LText.getInt(type)) >= 0) {
                    return;
                }

                ExchangeRequireResumeManager exchangeRequireResumeManager = new ExchangeRequireResumeManager(ChatRoomActivity.this, contactBean);
                exchangeRequireResumeManager.setCallBack(() -> refreshExchangeView());
                exchangeRequireResumeManager.onRequireResumeListener();
            });
        }

        @Override
        public void interview(long defaultTime, long id, String type, String label) {//约面试
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    //隐藏NLP消息
                    noticeCleanNLPMessage(id, type, true);
                    if (UserManager.isBossRole()) {
                        checkInterviewJump(defaultTime);
                    } else {
                        ApmAnalyzer.create().action("action_temp", ApmAnalyticsAction.ACTION_INTERVIEW_CREATE_BY_GEEK)
                                .p2("ChatNewActivity").report();
                        InterviewsRouter.startGeekCreateInterview(ChatRoomActivity.this, InterviewCommonUtils.createContactBean(contactBean));
                    }
                }
            });
        }

        @Override
        public void sendText(String text, long id, String type, String label) {//发送文本
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    //隐藏NLP消息
                    noticeCleanNLPMessage(id, type, true);
                    if (!TextUtils.isEmpty(text)) {
                        common.sendTextMessage(text, false);
                    }
                    if (UserManager.isBossRole()) {
                        int action;//1-常用语追聊，2-常用语回聊
                        if (contactBean.isIHaveSendMsgToFriend()) {
                            action = 1;
                        } else {
                            action = 2;
                        }
                        AnalyticsFactory
                                .create()
                                .action(AnalyticsAction.ACTION_NLP_CHAT_CLICK)
                                .param("p", action)
                                .buildSync();
                    }
                }
            });
        }

        @Override
        public void protocolJump(String protocol, long id, String type, String label) {//协议跳转

            //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157170378
            if (!LText.empty(protocol)) {
                Map<String, String> params = ZPManager.UrlHandler.getParams(protocol);
                String resident = params.get("resident");
                if (LText.equal(params.get("type"), "getStartChatSuggest")) {
                    String suggestType = params.get("suggestType");
                    String securityId = params.get("securityId");

                    if (LText.empty(securityId)) {
                        securityId = common.getSecurityId();
                    }

                    //发送lp的action
                    noticeCleanNLPMessage(id, type, resident);

                    SimpleApiRequest request = SimpleApiRequest.GET(ChatUrlConfig.URL_FAST_REPLY_SUGGEST_TEMPLATE);
                    request.addParam("suggestType", suggestType);
                    request.addParam("securityId", securityId);
                    request.setCallback(new ApiRequestCallback<ResultStringResponse>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            showProgressDialogNoFocus();
                        }

                        @Override
                        public void onSuccess(ApiData<ResultStringResponse> data) {
                            ResultStringResponse resp = data.resp;
                            String result = resp.result;
                            if (!LText.empty(result)) {
                                bottomFunctionView.setEditInputText(result);
                                bottomFunctionView.setShowKeyboard();
                            }
                        }

                        @Override
                        public void onComplete() {
                            dismissProgressDialog();
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    HttpExecutor.execute(request);
                } else if (LText.equal(params.get("type"), ProtocolConstants.Interview.TYPE_EDIT_INTERVIEWREMIND_SUGGEST)) {
                    obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                        @Override
                        public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                            new BossInterviewExpressGuideDialog(contactBean, new BossInterviewExpressGuideDialog.OnSendCallBack() {
                                @Override
                                public void onSendListener(String text) {
                                    noticeCleanNLPMessage(id, type, resident);
                                }
                            }).show(params.get("content"));
                        }
                    });
                } else {
                    noticeCleanNLPMessage(id, type, "");
                    new ZPManager(ChatRoomActivity.this, protocol).handler();
                }

            }

        }

        @Override
        public void onCloseNlpListener(long id, String suggestions, String label, String resident) {
            //点击关闭NLP按钮,通知服务区
            onDeleteNlpMessage(id, suggestions, resident);
            //隐藏nlp控件
            mNlpView.setNlpGone();
        }

        ////追聊  发送  *#14#  可以测试
        @Override
        public void onDaily(long id, String type, String label, String value) {
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false);
            }
        }

        //回聊  发送  *#15#  可以测试
        @Override
        public void backChat(long id, String type, String label, String value) {
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false);
            }
        }

        @Override
        public void changeJob(long id, String suggestions, String label, String value) {
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    //隐藏NLP消息
                    noticeCleanNLPMessage(id, suggestions, true);
                    ChoosePositionDialog2.checkJob(contactBean, new ChoosePositionDialog2.OnLoadJobBeanCallBack() {
                        @Override
                        public void onLoadJobBeanListener(List<JobSwitchBean> jobSwitchBeans, String atsNotify) {
                            ChoosePositionDialog2 dialog = new ChoosePositionDialog2(ChatRoomActivity.this);
                            dialog.setOnItemSelectCallBack(jobSwitchBean -> common.sendJobCard(jobSwitchBean));
                            dialog.show(jobSwitchBeans, contactBean.jobId);
                        }
                    });
                }
            });
        }

        @Override
        public void dailyGuide(long id, String type, String label, String value, @Nullable DailyGuideBean dailyGuideBean) {
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false, dailyGuideBean);
            }
        }

        @Override
        public void soundCall(long id, String type, String label, String value) {
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    if (contactBean.isContactEachOther()) {
                        noticeCleanNLPMessage(id, type, true);
                        ChatBottomFuncPresenter.onNlpAudioListener(contactBean);
                    } else {
                        ToastUtils.showText("双方回复之后才能使用");
                    }
                }
            });
        }

        @Override
        public void onSuggestClickListener(long id, String value) {

            obtainFriendId(contactBean -> {

                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_CHAT_DOUBLE_CHAT_LABEL_CLICK)
                        .param("p", contactBean.friendId)
                        .param("p2", jobId)
                        .param("p3", contactBean.jobIntentId)
                        .param("p4", value)
                        .param("p5", bottomFunctionView.getInputEditText())
                        .build();

            });


            bottomFunctionView.setEditInputText(value);
            nlpSuggestValue = value;

        }

        @Override
        public void livingSuggest(long id, String type, String label, String value) {
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false);
            }
        }

        @Override
        public void sendVideoGreatText(String content) {
            if (!TextUtils.isEmpty(content)) {
                bottomFunctionView.setEditInputText(content);
                bottomFunctionView.setShowKeyboard();
            }
        }

        @Override
        public void sendChatEmotionText(String content) {
            common.sendTextMessage(content, false);
        }

        @Override
        public void acceptExchangePhone(long reqMsgId) {
            common.onAcceptExchangePhoneOrWx(reqMsgId);
        }

        @Override
        public void acceptExchangeWeChat(long reqMsgId) {
            common.onAcceptExchangePhoneOrWx(reqMsgId);
        }

        @Override
        public void sendVerbalInterviewToGeek(String protocol) {
            new ZPManager(ChatRoomActivity.this, protocol).handler();
        }

        @Override
        public void onClickChatNLP(long msgId, String type, String content, boolean localHideNLP, int resident) {

            obtainFriendId(contactBean -> {

                //通知服务器点击了NLP
                MSGSender.sendCleanTAGNLPMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, msgId, type, ContactBean.FROM_BOSS, String.valueOf(resident));

                //把文本放到输入框
                if (!TextUtils.isEmpty(content)) {
                    bottomFunctionView.setEditInputText(content);
                    bottomFunctionView.setShowKeyboard();
                }

                //是否隐藏nlp消息
                if (localHideNLP) {
                    ChatNLPTransfer.sendHideNLPMessage(contactBean.friendId, contactBean.friendSource, ChatSuggestView.TAG);
                }

            });


        }
    };

    private long getJobId() {
        obtainFriendId(contactBean -> jobId = contactBean.jobId);
        return jobId;
    }


    //面试请求接口 决定跳转到那个界面
    private void checkInterviewJump(long defaultTime) {

        obtainFriendId(contactBean -> {
            InterviewsRouter.getInterviewDetailByBoss(0, "", contactBean.securityId, "6", new ApiRequestCallback<GetInterviewDetailResponse>() {

                @Override
                public void onStart() {
                    super.onStart();
                }

                @Override
                public void onSuccess(ApiData<GetInterviewDetailResponse> data) {
                    GetInterviewDetailResponse resp = data.resp;
                    if (resp.bizCode == 100156) {
                        String applyAddtion = null;
                        String applyTip = null;
                        if (resp.geekApplyInfo != null) {
                            applyAddtion = resp.geekApplyInfo.applyAddition;
                            applyTip = resp.geekApplyInfo.applyTip;
                        }
                        InterviewCreateIntentBean bean = new InterviewCreateIntentBean(InterviewCommonUtils.createContactBean(contactBean), 2);
                        bean.setDefaultTimeLong(defaultTime).setApplyAddition(applyAddtion).setApplyTip(applyTip);
                        InterviewsRouter.tryCreateInterview(ChatRoomActivity.this, bean);
                    } else {
                        InterviewParams params = new InterviewParams();
                        params.interviewId = resp.interviewDetail.interviewId;
                        params.encryptInterviewId = resp.interviewDetail.encryptInterviewId;
                        params.securityId = resp.interviewDetail.securityId;
                        params.isFinishActivityWhenStartChat = true;
                        params.from = InterviewParams.FROM_OTHER;
                        params.apiFrom = "6";
                        InterviewsRouter.startInterviewDetailActivity(ChatRoomActivity.this, params);
                    }
                }

                @Override
                public void onComplete() {
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
        });

    }


    /**
     * 截图分享监听器注册
     */
    private void initScreenShot() {
        if (UserManager.isGeekRole()) return;
        screenShotSetting = new ScreenShotSetting(this);
        screenShotSetting.setAction(0);
        screenShotSetting.setScreenShotCallback(new IScreenShotCallback() {
            @Override
            public void screenShot(String path, Uri uri) {

                obtainFriendId(contactBean -> {
                    screenShotSetting.setParams(String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendId), String.valueOf(contactBean.jobIntentId), contactBean.securityId);
                });
            }
        });

        getLifecycle().addObserver(screenShotSetting);
    }


    //发送nlp出席消息
    private void sendNLPPresenterMessage(String scene) {
        AppThreadFactory.POOL.execute(() -> {
            //发送最后一个聊天消息id,没有就传0
            obtainFriendId(contactBean -> {
                MessageDao messageDao = MessageDaoFactory.getMessageDao();
                long id = messageDao.queryFriendChatMaxMessageId(UserManager.getUID(), UserManager.getUserRole().get(), contactBean.friendId);

                MSGSender.sendMessageSuggest(System.currentTimeMillis(),
                        UserManager.getUID(), scene,
                        contactBean.friendId, id, ContactBean.FROM_BOSS);

            });
        });
    }

    //获得好友friendId
    private void obtainFriendId(@NonNull ChatCommon.OnFriendIdCallBack callBack) {
        if (common != null) {
            common.obtainContactBean(callBack);
        }
    }

    /**
     * 情况nlp消息
     *
     * @param msgId
     * @param type
     */
    private void noticeCleanNLPMessage(long msgId, String type, String resident) {
        obtainFriendId(contactBean -> MSGSender.sendCleanTAGNLPMessageSuggest(System.currentTimeMillis(),
                UserManager.getUID(), contactBean.friendId,
                msgId, type, ContactBean.FROM_BOSS, resident));
    }

    /**
     * 通知服务器清空NLP消息
     */
    private void noticeCleanNLPMessage(long msgId, String type, boolean fromNLP) {

        obtainFriendId(contactBean -> {
            if (fromNLP) {
                MSGSender.sendCleanTAGNLPMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, msgId, type, ContactBean.FROM_BOSS, "");
            } else {
                MSGSender.sendCleanExchangeNLPMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, type, ContactBean.FROM_BOSS);
            }
        });


    }

    /**
     * 用户点击 删除NLP按钮
     */
    private void onDeleteNlpMessage(long msgId, String suggestions, String resident) {
        obtainFriendId(contactBean -> MSGSender.sendDeleteMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, msgId, suggestions, resident, ContactBean.FROM_BOSS));
    }


    //检测好友id与securityId是否为空
    private boolean checkFriendIdSecurityIdEmpty() {
        if (tempFriendId <= 0 && LText.empty(securityId)) {
            ToastUtils.showText("数据异常");
            AppUtil.finishActivity(this);
            return true;
        }
        return false;
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        long oldFriendId = tempFriendId;
        // 1107.42 重置intent前检测 是否需要重置
        if (!checkIgnoreResetIntent(intent)) {
            setIntent(intent);
            initParams(intent);
        }


        if (checkFriendIdSecurityIdEmpty()) return;

        if (oldFriendId != tempFriendId) { //处理聊天界面不同聊天对象的重入问题 add by datian
            common.reset();
            common.create(tempFriendId);
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=187612681
                    String scene = BossEnter.canSendNlpScene(contactBean) ? "1" : "0";
                    sendNLPPresenterMessage(scene);
                }
            });
        } else {
            common.onNewIntent(intent);
            obtainFriendId(contactBean -> tipStatusView.userEnter(ChatRoomActivity.this, false, contactBean, securityId, new Callback<IUserEnter>() {
                @Override
                public void call(IUserEnter iUserEnter) {
                    if (iUserEnter.bossEnterResponse != null) {
                        common.bossLimitCheckUtils.setBossChatPluginBlock(iUserEnter.bossEnterResponse.bossChatPluginBlockSwitch, iUserEnter.bossEnterResponse.bossChatPluginBlockThreshold, iUserEnter.bossEnterResponse.bossChatPluginBlockTips);
                        setAIChatHelperView(iUserEnter.bossEnterResponse.chatHelperActionStatus);

                        // 处理guideQuick引导餐饮Boss快速达成
                        GrayStageManager.getInstance().setGuideQuickExchange(iUserEnter.bossEnterResponse.guideQuick);
                    }
                    boolean isOnline = iUserEnter.isOnline;
                    int onlineStyle = iUserEnter.onlineStyle;
                    if (isOnline) {
                        if (onlineStyle == 2) {
                            titleView.setTitleRightIcon(R.mipmap.chat_icon_online, 6);
                        } else {
                            titleView.setTitleLeftIcon(R.drawable.bg_67cc35_dot);
                        }
                        SingleChatOnlineUtil.getInstance().registerOnLine(ChatRoomActivity.this, contactBean.friendId, contactBean.friendSource, true, onlineStyle);
                    } else {
                        SingleChatOnlineUtil.getInstance().removeUserOnLine(contactBean.friendId, contactBean.friendSource);
                    }

                    setChatBackground(iUserEnter.chatBgImg);

                    if (iUserEnter.refreshExchange) {
                        refreshExchangeView();
                    }
                }
            }));

        }
        startCheckIsContacted();

        obtainFriendId(contactBean -> {
            // 购买VIP成功
            boolean isGeekVipPurchaseSuccess = intent.getBooleanExtra(SingleConstant.CHAT_BOOLEAN_VIP_PURCHASE_SUCCESS, false);
            if (isGeekVipPurchaseSuccess) {
                String source = intent.getStringExtra(SingleConstant.CHAT_STRING_SOURCE);
                OnlineRemindUtils onlineRemindUtils = new OnlineRemindUtils(ChatRoomActivity.this,
                        contactBean.securityId, source);
                onlineRemindUtils.setJobIdString(String.valueOf(getJobId()));
                onlineRemindUtils.onlineRemindSetup();
            }
        });

    }

    /**
     * 聊天背景
     *
     * @param chatBgImg
     */
    private void setChatBackground(String chatBgImg) {
        if (!TextUtils.isEmpty(chatBgImg)) {
            imgChatBj.setImageURI(chatBgImg);
            imgChatBj.setVisibility(View.VISIBLE);
        } else {
            imgChatBj.setVisibility(View.GONE);
        }
    }

    private boolean checkIgnoreResetIntent(Intent intent) {
        // 1107.42 从牛人添加面试日程页面跳回到聊天页面 不需要重新设置intent
        boolean fromGeekInterviewArrange = tempFriendId == intent.getLongExtra(SingleConstant.CHAT_LONG_FRIEND_ID, -1) && !intent.getBooleanExtra(SingleConstant.CHAT_BOOLEAN_RESET_INTENT, true);
        return fromGeekInterviewArrange;
    }

    private void initParams(Intent intent) {
        tempFriendId = intent.getLongExtra(SingleConstant.CHAT_LONG_FRIEND_ID, -1);
        jobId = intent.getLongExtra(SingleConstant.CHAT_LONG_JOB_ID, 0);
        long jobIntentId = intent.getLongExtra(SingleConstant.CHAT_LONG_EXPECT_ID, 0);
        String lid = intent.getStringExtra(SingleConstant.CHAT_STRING_LID);
        from = intent.getStringExtra(SingleConstant.CHAT_STRING_FROM);
        long jobPositionId = intent.getLongExtra(SingleConstant.CHAT_LONG_JOB_POSITION, 0);
        int jobCityCode = intent.getIntExtra(SingleConstant.CHAT_INT_JOB_CITY_CODE, 0);
        String backProtocol = intent.getStringExtra(SingleConstant.CHAT_STRING_BACK_PROTOCOL);
        String changePositionDesc = intent.getStringExtra(SingleConstant.CHAT_STRING_CHANGE_POSITION_DESC);
        String jobAddressId = intent.getStringExtra(SingleConstant.CHAT_STRING_JOB_ADDRESS_ID);
        securityId = intent.getStringExtra(SingleConstant.CHAT_STRING_SECURITY_ID);
        String url = intent.getStringExtra(SingleConstant.CHAT_STRING_URL);
        int similarPosition = intent.getIntExtra(SingleConstant.CHAT_INT_SIMILAR_POSITION, 0);
        long smoothToTargetId = intent.getLongExtra(SingleConstant.CHAT_LONG_SMOOTH_TO_TARGET_MESSAGE_ID, 0);
        String lightChatText = intent.getStringExtra(SingleConstant.CHAT_STRING_LIGHT_CHAT_TEXT);
        int entrance = intent.getIntExtra(SingleConstant.CHAT_INT_ENTRANCE, 0);
        String greet = intent.getStringExtra(SingleConstant.CHAT_STRING_GREET);
        String contentInEditText = intent.getStringExtra(SingleConstant.CHAT_STRING_CONTENT_IN_EDIT_TEXT);
        int inviteType = intent.getIntExtra(SingleConstant.CHAT_INT_INVITE_TYPE, -1);
        String addFriendSendText = intent.getStringExtra(SingleConstant.CHAT_STRING_ADD_FRIEND_SEND_TEXT);
        defaultInputText = intent.getStringExtra(SingleConstant.CHAT_DEFAULT_INPUT_TEXT);

        int applyJobDirectly = intent.getIntExtra(SingleConstant.CHAT_STRING_APPLY_DIRECT, 0);
        // 1225.66【B/C】口语测试提效：通过口语测试打开聊天界面时，新增 languageId 界面入参
        long speakTestLanguageId = intent.getLongExtra(SingleConstant.CHAT_SPEAK_TEST_LANGUAGE_ID, 0L);
        //1302.110 【BC】蓝领C快速达成路径尝试 一键报名灰度 对照组为0，实验组为1-3
        int startChatProcessExpGroup = intent.getIntExtra(SingleConstant.CHAT_ONE_SIGN_GRAY_TYPE, 0);
        // 1308.170【招聘者】新增AI面试考察重点
        autoShowKeyBoard = intent.getBooleanExtra(SingleConstant.CHAT_AUTO_SHOW_KEYBOARD, false);
        //1308.165【B&C】AI帮你推
        boolean fromAiRecommend = intent.getBooleanExtra(SingleConstant.CHAT_BOOLEAN_FROM_AI_RECOMMEND, false);

        common.setJobId(jobId);
        common.setJobAddressId(jobAddressId);
        common.setJobIntentId(jobIntentId);
        common.setAddFriendSecurityId(securityId);
        common.setLid(lid);
        common.setFrom(from);
        common.setJobPositionId(jobPositionId);
        common.setJobCityCode(jobCityCode);
        common.setBackProtocol(backProtocol);
        common.setSmoothToTargetMessageId(smoothToTargetId);
        common.setChangePositionDesc(changePositionDesc);
        common.setUrl(url);
        common.setSimilarPosition(similarPosition);
        common.setLightChatText(lightChatText);
        common.setEntrance(entrance);
        common.setGreet(greet);
        common.setChatContentInEditText(contentInEditText);
        common.setInviteType(inviteType);
        common.setAddFriendSendText(addFriendSendText);
        common.setApplyDirect(applyJobDirectly);
        common.setSpeakTestLanguageId(speakTestLanguageId);
        common.setStartChatProcessExpGroup(startChatProcessExpGroup);
        common.setFromAiRecommend(fromAiRecommend);
        //如果有未读消息,每次关闭时候,发送广播,f2提示使用新招呼功能
        initSendGreeting();
    }


    //如果有未读消息,每次关闭时候,发送广播,f2提示使用新招呼功能
    private void initSendGreeting() {
        isSendShowNewGreetingBroadcast = false;
        obtainFriendId(contactBean -> {
            if (contactBean.noneReadCount > 0) {
                isSendShowNewGreetingBroadcast = true;
            }
        });

    }

    //是否发送 新招呼广播
    private boolean isSendShowNewGreetingBroadcast;

    //发送 新招呼广播
    private void sendShowNewGreetingBroadcast() {
        if (isSendShowNewGreetingBroadcast) {
            List<ContactBean> contactBeans = F2ContactHelper.getInstance().getFastHandleContact();
            if (contactBeans != null) {
                for (ContactBean contactBean : contactBeans) {
                    if (contactBean.noneReadCount > 0) {
                        //还有单聊未读消息 推荐快速处理
                        ContactManager.sendNewGreetingBroadcast(this);
                        break;
                    }
                }
            }
        }
    }

    private boolean hasShowPhoneDialog;

    private long resumeTime;// 执行onResume的时间，用于记录展示时长，单位ms

    @Override
    protected void onResume() {
        super.onResume();
        resumeTime = SystemClock.elapsedRealtime();
        common.resume();
        SecurityFrameworkManager.getInstance().check();

        obtainFriendId(contactBean -> {
            if (UserManager.isGeekRole()) {
                if (hasShowPhoneDialog) return;
                VirtualPhoneManage.VirtualPhoneBean extendValue = VirtualPhoneManage.VirtualCacheBean.getInstance().getExtendValue(contactBean.friendId, ContactBean.FROM_BOSS);
                if (extendValue != null) {
                    long endTime = extendValue.endTime;
                    if (System.currentTimeMillis() > endTime) {
                        VirtualPhoneManage.VirtualCacheBean.getInstance().removeData(contactBean.friendId, ContactBean.FROM_BOSS);
                        return;
                    }

                    ViewGroup mListParentView = findViewById(R.id.mListParentView);
                    hasShowPhoneDialog = true;

                    VirtualPhoneManage.VirtualDialog dialog = new VirtualPhoneManage.VirtualDialog();
                    dialog.setIcon(extendValue.icon);
                    dialog.setTitle(extendValue.title);
                    dialog.setSecurityId(extendValue.securityId);
                    dialog.setFriendId(contactBean.friendId);
                    dialog.setRefuseButtonText(extendValue.refuseButtonText);
                    dialog.setConfirmButtonText(extendValue.confirmButtonText);
                    dialog.setRootView(mListParentView);
                    dialog.checkShowDialog();
                }
            }
        });

        ChatBottomFuncPresenter.resume();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //折叠屏适配
        obtainFriendId(contactBean -> {
            if (bossUserExchangeContainer != null) {
                bossUserExchangeContainer.refreshContainer(contactBean);
            }
        });

    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        //处理返回协议
        if (common.keyDown(keyCode, event)) {
            return true;
        }
        //处理"小调查"
        if (QuestionNaireManager.getInstance().onBackPressListener()) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        common.activityResult(this, requestCode, resultCode, data);
    }

    @Override
    protected void onPause() {
        super.onPause();
        long durationTime = SystemClock.elapsedRealtime() - resumeTime;
        AnalyticsApp.appPageDwellTime(5, durationTime);
        obtainFriendId(contactBean -> {
            GeeKQuestionResearchManager.getInstance().removeContactQuestionAskCard(contactBean);
            F2ContactMarkLabelManager.getInstance().remove(contactBean.friendId, contactBean.friendSource, F2ContactMarkLabelManager.MarkSPManage.KEY_AUTO_REPLY);
            contactBean.messageExchangeIcon = ContactExchangeChatView.NONE;
            contactBean.isPlatFromResearch = false;
            contactBean.improveMessageExposure = false;
            contactBean.ats = false;
            contactBean.directCallStatus = 0;
            contactBean.recommendReason = "";

            String inputText = bottomFunctionView.getInputEditText();
            long inputQuoteMsgId = chatQuoteMsgView.getQuoteMsgId();
            refreshDraftInfo(contactBean, inputText, inputQuoteMsgId);

            common.pause();
        });

    }

    //页面走了 onStop 回来之后
    private long lastPageStopTime;

    @Override
    protected void onStop() {
        super.onStop();
        lastPageStopTime = SystemClock.elapsedRealtime();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Utils.removeHandlerCallbacks(refreshPageMessage);
        obtainFriendId(contactBean -> {

            try {


                //刷新最后一个聊天消息
                MessageUtils.finishRefreshLastChatText(contactBean, chatAdapter2.getData());

                if (tipStatusView != null) {
                    tipStatusView.unRegister();
                }

                //检测是否发新招呼广播
                sendShowNewGreetingBroadcast();
                common.destroy();
                IChatNlpTransferService nlpTransferService = SingleRouter.getNlpTransferService();
                if (nlpTransferService != null) {
                    nlpTransferService.unRegister(this);
                }
                //清空表情
                ChatEmotionDataManager.getInstance().clearEmotion();

                ChatBottomFuncPresenter.destroy();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        screenShotSetting = null;


    }

    @Override
    protected String getTitleText() {
        return title;
    }

    @Override
    protected TextView getTitleView() {
        if (titleView != null) {
            return titleView.getTitleTextView();
        }
        return null;
    }

    @Override
    public View initConvertView() {
        return getWindow().getDecorView();
    }

    @Override
    protected int getStatusBarColor() {
        return ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F);
    }

    @Override
    public void initTitle(ContactBean contact) {
        try {
            titleView = findViewById(R.id.title_view);
            titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F));
            titleView.setBackClickListener(v -> {

                AppUtil.hideSoftInput(ChatRoomActivity.this);

                ChatAnalysisUtil.dotF2DetailClickBack(contact != null ? String.valueOf(contact.friendId) : "");/*埋点*/
                //1307.80【C】缓解部分招聘者超期会话资源占用问题
                if (common.showOverdueBossDialog()) {
                    return;
                }
                if (!QuestionNaireManager.getInstance().onBackPressListener()) {
                    AppUtil.finishActivity(ChatRoomActivity.this);
                }
            });

            if (contact == null) return;
            initTitleValue(contact);
            titleView.changeAutoStyle(ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F));
            titleView.setAction2ClickListener(R.mipmap.ic_action_more_function_black, new OnTitleMoreButtonClickListener());
            titleView.setDividerInvisible();
            titleView.getTvSubTitle().setMaxWidth(ZPUIDisplayHelper.dp2px(this, 170));/*按UI设计图，设置副标题最大宽度为170dp*/
            titleView.getTvSubTitle().setEllipsize(TextUtils.TruncateAt.END);
            titleView.getIvAction1().setImageTintList(null);
            titleView.getTitleTextView().setOnClickListener(new OnClickNoFastListener() {/*标题点击*/
                @Override
                public void onNoFastClick(View v) {
                    if (contact == null) return;
                    SingleRouter.startSettingChatNew(ChatRoomActivity.this, contact, null, from);
                    ChatAnalysisUtil.dotF2DetailHeadClick(String.valueOf(contact.friendId), !TextUtils.isEmpty(contact.note));/*埋点*/
                }
            });
            titleView.getTvSubTitle().setOnClickListener(new OnClickNoFastListener() {/*副标题点击*/
                @Override
                public void onNoFastClick(View v) {
                    if (contact == null) return;
                    SingleRouter.startSettingChatNew(ChatRoomActivity.this, contact, null, from);
                    ChatAnalysisUtil.dotF2DetailHeadClick(String.valueOf(contact.friendId), !TextUtils.isEmpty(contact.note));/*埋点*/
                }
            });

            if (UserManager.isBossRole()) {
                titleView.setAction1ClickListener(contact.isStar ? R.mipmap.chat_ic_collect_yes : R.mipmap.ic_action_favor_off_black, v -> {
                    ChatAnalysisUtil.dotChatClickInterest(contact.friendId, contact.isStar ? 2 : 1);/*埋点*/
                    performCollection(contact);
                }, false);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 执行收藏
     *
     * @param mContactBean
     */
    private void performCollection(ContactBean mContactBean) {
        BossUnfitChangeManager.getInstance().onStartChangeListener(UserRejectManager.PAGE_TYPE_CHAT_PAGE, mContactBean, new RejectHttpManager.OnLockUnLockCallBack() {

            @Override
            public void onSuccess(ContactBean contactBean) {
                if (contactBean == null) return;
                titleView.getIvAction1().setImageResource(contactBean.isStar ? R.mipmap.chat_ic_collect_yes : R.mipmap.ic_action_favor_off_black);
            }

            @Override
            public void onFailed(ContactBean contactBean) {

            }
        });
    }


    /**
     * 815.82.BOSS测评  邀请答题
     * 切换 职位需要刷新
     */

    @Override
    public void initBackButton(int otherNoneReadCountString) {
        TextView tvBackText = titleView.getTvBackText();
        tvBackText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        if (otherNoneReadCountString > 0) {
            tvBackText.setVisibility(View.VISIBLE);
            tvBackText.setBackgroundResource(R.drawable.bg_12_corner_color_f0f0f0);
            tvBackText.setPadding(ZPUIDisplayHelper.dp2px(this, 8), ZPUIDisplayHelper.dp2px(this, 2), ZPUIDisplayHelper.dp2px(this, 8), ZPUIDisplayHelper.dp2px(this, 2));
            tvBackText.setText(String.valueOf(otherNoneReadCountString));
            tvBackText.setTextColor(ContextCompat.getColor(this, R.color.text_c13));
        } else {
            tvBackText.setVisibility(View.GONE);
        }
    }


    /**
     * 刷新交换微信和交换电话的状态
     */
    private void refreshExchangeView() {
        if (isFinishing()) return;
        obtainFriendId(contactBean -> bossUserExchangeContainer.refreshContainer(contactBean));
    }


    @Override
    public void initView() {
        slideView = findViewById(R.id.slide_view);
        mPayCoverView = findViewById(R.id.mPayCoverView);
        recyclerView = findViewById(R.id.mRecycleView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        mRefreshLayout = findViewById(R.id.mSwipRefreshLayout);

        bossUserExchangeContainer = findViewById(R.id.mTempExchangeLayout);

        tipStatusView = findViewById(R.id.ll_top_tips);
        slideView.setOnSlideClickCallBack(common);
        bottomFunctionView = findViewById(R.id.chat_functions);
        mNlpView = findViewById(R.id.mNlpView);

        flBottom = findViewById(R.id.fl_bottom);
        chatQuoteMsgView = findViewById(R.id.chatReplyView);

        imgChatBj = findViewById(R.id.img_chat_bj);
        aiAssistantView = findViewById(R.id.ai_assistant_view);
        aiAssistChatView = findViewById(R.id.ai_assist_chat_view);
        aiDirectReasonPopupView = findViewById(R.id.view_direct_reason);

        aiAssistChatView.setOnClickListener(v -> {
            openAIHelpChatPanel();
            AIHelpPanelUtils.clearCloseAIPanelNum();
            ChatAnalysisUtil.statsChatAIReplyClick(common.getFriendId(), common.getJobId(), 0,0, 7, "");
        });
    }


    //点击个更多
    private void onMoreClickBgAction() {
        obtainFriendId(contactBean -> {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_PLUS)
                    .secId(contactBean.securityId)
                    .param("p", String.valueOf(contactBean.friendId))
                    .build();


            //公司卡片是否存在
            if (UserManager.isBossRole() && ChatBottomFuncPresenter.isShowCompanyCard()) {
                long brandId = 0;
                BossInfoBean bossInfo = UserManager.getBossInfo();
                if (bossInfo != null) {
                    BrandInfoBean brandInfoBean = LList.getElement(bossInfo.brandList, 0);
                    if (brandInfoBean != null) {
                        brandId = brandInfoBean.brandId;
                    }
                }

                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_BRANDCARD_ICON_POP)
                        .param("p", contactBean.friendId)
                        .param("p2", contactBean.jobId)
                        .param("p3", contactBean.jobIntentId)
                        .param("p3", brandId)
                        .build();
            }


        });

    }


    private void onTextChangeListener(String text) {
        if (mNlpView != null) {
            mNlpView.onInputTextHideNLP(text);
            obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    mNlpView.getChatSuggest(text, contactBean.securityId, contactBean.friendId, contactBean.friendSource);
                }
            });

        }
    }


    private void sendTextMessageListener(String text, boolean isCommonPhrase) {


        obtainFriendId(contactBean -> {

            common.bossLimitCheckUtils.checkLimit(contactBean, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
                @Override
                public void onBlockResult(String blockTip) {
                    if (blockTip != null) {
                        common.sendFailedTextMessage(text, blockTip);
                    } else {
                        boolean sendSuggestNlp = !LText.empty(nlpSuggestValue);

                        ChatBean quoteChatBean = chatQuoteMsgView.getQuoteChatBean();

                        long quoteId = quoteChatBean != null ? quoteChatBean.msgId : 0;

                        ContactKeyManager.getInstance().onInputSendMsgListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                if (sendSuggestNlp) {
                                    common.sendNlpSuggestTextMessage(text, quoteId, quoteChatBean);
                                } else if (isCommonPhrase) {
                                    common.sendTextMessageWithBizType(text, SendMessageUtil.COMMON_PHRASES_TYPE);
                                } else {
                                    common.sendTextMessage(text, CommonWordManager.getInstance().checkWeatherHasSet(text), quoteId, quoteChatBean, null, com.hpbr.bosszhipin.chat.utils.ChatUtils.createExtraBean(chooseAIGeneratedText, text));
                                    // AI生成内容填充到输入框并被手动发出后，清除记录的文案。该文案用以判断是否是AI生成内容还是用户输入内容。AI生成内容需传专属bizCode
                                    clearAIGeneratedText();
                                }
                                //发送消息出去之后,隐藏引用消息
                                chatQuoteMsgView.hideReplyView();
                            }

                            @Override
                            public void onCheckBlockUrlListener(String url) {
                                common.sendFailedTextMessage(text);
                            }

                            @Override
                            public void onCheckBlockToastListener(String msg) {
                                common.sendFailedTextMessage(text);
                            }
                        });
                        if (!LText.empty(nlpSuggestValue)) {

                            AnalyticsFactory
                                    .create()
                                    .action(AnalyticsAction.ACTION_CHAT_DOUBLE_CHAT_LABEL_PUSH)
                                    .param("p", nlpSuggestValue)
                                    .param("p2", text)
                                    .param("p3", LText.equal(nlpSuggestValue, text) ? 1 : 0)
                                    .build();
                            nlpSuggestValue = "";
                        }
                    }
                }
            });


        });

    }

    private void sendOtherEmotionListener(ExtendEmotion extendEmotion) {

        obtainFriendId(contact -> {

            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_STICKER).secId(contact.securityId)
                    .param("p", "" + contact.friendId).param("p2", extendEmotion.getItemEmotionId() + "")
                    .param("p3", "0")
                    .param("p4", "0")
                    .param("p5", String.valueOf(contact.jobId))
                    .param("p6", String.valueOf(contact.jobIntentId))
                    .param("p7", String.valueOf(extendEmotion.getStaticDrawableUrl()))
                    .param("p8", extendEmotion.getPackageId() == ChatEmotionDataManager.EMOTION_FAVOURITE_ID ? 0 : 1)//图片是否自定义 0 ：自定义 1：非自定义
                    .param("p9", String.valueOf(extendEmotion.getDynamicDrawableUrl()))
                    .param("p10", extendEmotion.getPackageName())
                    .build();


            common.bossLimitCheckUtils.checkLimit(contact, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
                @Override
                public void onBlockResult(String blockTip) {
                    if (blockTip != null) {
                        long packageId = extendEmotion.getPackageId();
                        long itemEmotionId = extendEmotion.getItemEmotionId();
                        String gifName = extendEmotion.getGifName();
                        String encSid = extendEmotion.getEncSid();

                        ChatImageInfoBean originImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(extendEmotion.getDynamicDrawableUrl(), extendEmotion.getOrgEmotionWidth(), extendEmotion.getOrgEmotionHeight());
                        ChatImageInfoBean tinyImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(extendEmotion.getDynamicDrawableUrl(), extendEmotion.getTinyEmotionWidth(), extendEmotion.getTinyEmotionHeight());
                        common.sendFailedGif(encSid,packageId, itemEmotionId, originImage, tinyImage, gifName, blockTip);
                    } else {

                        ContactKeyManager.getInstance().onEmotionSendListener(contact, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                ChatImageInfoBean originImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(extendEmotion.getDynamicDrawableUrl(), extendEmotion.getOrgEmotionWidth(), extendEmotion.getOrgEmotionHeight());
                                ChatImageInfoBean tinyImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(extendEmotion.getDynamicDrawableUrl(), extendEmotion.getTinyEmotionWidth(), extendEmotion.getTinyEmotionHeight());
                                ChatBean bean = common.getChatSendCommon().sendGifMessage(MessageTargetInfo.fromContactBean(contact), extendEmotion.getEncSid(), extendEmotion.getPackageId(), extendEmotion.getItemEmotionId(), originImage, tinyImage, extendEmotion.getGifName(), new ChatSendCallback() {
                                    @Override
                                    public void onSaveLocation(MessageTargetInfo targetInfo, ChatBean sendChatBean) {
                                        super.onSaveLocation(targetInfo, sendChatBean);
                                    }

                                    @Override
                                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                                        if (success) {
                                            onSendChatCallback(null);
                                        }

                                        if (isFinishing()) {
                                            return;
                                        }

                                        refreshShowData();


                                    }

                                });

                                if (bean == null) {
                                    T.ss("发送消息失败");
                                    return;
                                }

                                common.addData(bean);
                                refreshShowData();
                                common.scrollToBottom(false);
                            }

                            @Override
                            public void onCheckBlockUrlListener(String url) {

                                saveFailedGifListener(extendEmotion, contact);
                            }

                            @Override
                            public void onCheckBlockToastListener(String msg) {
                                super.onCheckBlockToastListener(msg);
                                saveFailedGifListener(extendEmotion, contact);
                            }
                        });

                    }
                }
            });


        });

    }


    private List<CommonWordsUIBean> getChatCommonList() {
        List<CommonWordsUIBean> result = new ArrayList<>();
        List<NewQuickReplyBean> commonList = QuickReplyManager.getCommonList();
        if (commonList != null) {
            for (NewQuickReplyBean newQuickReplyBean : commonList) {
                result.add(new CommonWordsUIBean(newQuickReplyBean.content, newQuickReplyBean.fastReplyId, false));
            }
        }
        return result;
    }


    /**
     * 刷新NLP建议列表
     */
    @Override
    public void refreshNlpSuggest(NLPListBean nlpListBean) {
        if (common == null) return;
        common.obtainContactBean(contactBean -> {
            if (nlpListBean != null
                    && nlpListBean.getFriendId() == contactBean.friendId
                    && nlpListBean.getFriendSource() == ContactBean.FROM_BOSS) {//Boss的NLP

                // 展示AI帮聊窗口时，在AI窗口展示特定类型NLP消息
                if (AIHelpPanelUtils.isAIHelpPanelFuncEnable() && aiHelpChatPanel != null && !bottomFunctionView.hideAIHelpChatPanelAndBubble()) {
                    if (mNlpView != null) {
                        mNlpView.checkRemoveNlpAction(common.getFriendId(), common.getFriendSource());
                    }
                    if (aiHelpChatPanel.isIgnoreHandleNlpType(nlpListBean)) {
                        return;
                    }

                    // 当前正在展示NLP消息，丢弃后续的
                    if (curNlpListBean != null) {
                        return;
                    }

                    // 气泡模式收到需要处理的NLP消息先打开窗口
                    if (bottomFunctionView.isShowAIHelpChatBubble()) {
                        openAIHelpChatPanel();
                    }

                    aiHelpChatPanel.showNlpView(nlpListBean);
                    // 是AI帮聊需要处理的NLP气泡类型，记录一下，用以拦截后续NLP消息
                    curNlpListBean = nlpListBean;
                    return;
                }

                if (aiDirectReasonPopupView.getVisibility() == View.VISIBLE) {
                    return;
                }
                if (mNlpView != null) {
                    mNlpView.showNLP(common.filterNLPListBean(nlpListBean));
                }
                nlPBgAction(nlpListBean);
            }
        });
    }


    private void nlPBgAction(NLPListBean nlpListBean) {
        if (!LList.isEmpty(nlpListBean.getNlpList())) {

            obtainFriendId(contactBean -> {

                List<String> labels = new ArrayList<>();
                List<String> types = new ArrayList<>();
                for (NLPSuggestBean nlpBean : nlpListBean.getNlpList()) {
                    if (nlpBean == null) {
                        continue;
                    }
                    types.add(String.valueOf(nlpBean.type));
                    //https://jira.kanzhun-inc.com/browse/BSA-19975?filter=-1
                    labels.add(nlpBean.label);
                }
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_NLP_SHOW)
                        .param("p", String.valueOf(contactBean.friendId))
                        .param("p2", StringUtil.connectTextWithChar(",", labels))
                        .param("p3", StringUtil.connectTextWithChar(",", types))
                        .param("p4", String.valueOf(nlpListBean.getBusinessSceneType()))
                        .param("p5", String.valueOf(getJobId()))
                        .param("p7", NormalView.getBgActionP7(nlpListBean))
                        .param("p8", 0)
                        .build();

            });

        }
    }


    //点击gif失败处理保存消息到数据库
    private void saveFailedGifListener(ExtendEmotion item, ContactBean contact) {
        ChatImageInfoBean originImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(item.getStaticDrawableUrl(), item.getOrgEmotionWidth(), item.getOrgEmotionHeight());
        ChatImageInfoBean tinyImage = ChatBeanFactory.getInstance().getDefaultChatImageInfoBean(item.getDynamicDrawableUrl(), item.getTinyEmotionWidth(), item.getTinyEmotionHeight());

        ChatBean sendChatBean = ChatBeanFactory
                .getInstance().createGif(MessageTargetInfo.fromContactBean(contact), item.getEncSid(), item.getPackageId(), item.getItemEmotionId(), originImage, tinyImage, item.getGifName());

        ChatUtils.addContactKey2Message(sendChatBean);

        AppThreadFactory.POOL.execute(() -> {

            //更新最后一个消息的文案
            contact.lastChatText = MessageUtils.getSingleChatLastText(sendChatBean, contact, false);
            contact.lastChatStatus = 2;
            contact.lastChatTime = System.currentTimeMillis();
            contact.updateTime = contact.lastChatTime;
            ContactManager.getInstance().insertOrUpdateAllField(contact, UserManager.getUserRole().get());

            //保存一个失败的消息到数据库
            MessageDaoFactory.getMessageDao().saveChat(sendChatBean);
            //同步刷新UI
            App.get().getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    common.addData(sendChatBean);
                    refreshShowData();
                    common.scrollToBottom(false);
                }
            });
        });
    }

    @Override
    public void checkShowNewAvAudioMore(Activity activity, boolean isShowVideoIcon, long friendId) {
        bottomFunctionView.checkShowNewAvAudioMore(activity, isShowVideoIcon, friendId, frinedId -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_BUBBLE_EXCHANGE_TEL).param("p", String.valueOf(friendId)).buildSync());
    }

    @Override
    public String getChatFrom() {
        return from;
    }

    /**
     * recycleView滑动回掉
     */
    @Override
    public void onBindAdapterItem(long id) {
        //检测是否消失电梯
        MessageSlideView.checkWeatherHideSlideView(slideView, id);
    }

    @Override
    public void onReEditRevocationText(ChatBean chatBean, String text) {

    }


    @Override
    public void initTitleValue(ContactBean contactBean) {
        if (contactBean == null) return;
        title = contactBean.friendName;
        final int connectStatus = MSGManager.get().getStatus();
        /*标题*/
        titleView.getTitleTextView().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f);
        titleView.getTitleTextView().setMaxWidth((int) (ZPUIDisplayHelper.getScreenWidth(this) * 0.45));
        titleView.setTitle(connectStatus == ConnectStatus.SUCCESS ? (UserManager.isBossRole() && contactBean.teamMemberSize > 1 ?
                String.format(Locale.getDefault(), "%s 等%s人", title, contactBean.teamMemberSize) : title) : "未连接");/*1105.85 BOSS 看牛人 如果是组队，那么展示「牛人名字 等n人」*/
        /*「在线」绿点与异常提示标签*/
        refreshOnlineIconAndExceptionTipLabel(contactBean);
        /*副标题*/
        titleView.setSubTitle(getSubTitleContent(contactBean, contactBean.note, contactBean.getLabelsCollection()));
    }

    /**
     * 获取副标题要展示的文案
     *
     * @param contactBean
     * @param remarkContent
     * @param labelList
     * @return
     */
    private String getSubTitleContent(@NonNull ContactBean contactBean, String remarkContent, List<String> labelList) {
        if (!TextUtils.isEmpty(remarkContent) || LList.getCount(labelList) > 0) {/* 需要同时展示 标签+ 备注，用「、」分割*/
            String connectLables = null;
            if (LList.getCount(labelList) > 0) {
                List<String> subLabels = LList.getCount(labelList) > 3 ? LList.getSubList(labelList, 0, 3) : labelList;
                connectLables = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA2, subLabels);
            }
            if (!TextUtils.isEmpty(connectLables)) {
                return TextUtils.isEmpty(remarkContent) ? connectLables : StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA2, connectLables, remarkContent);
            } else {
                return remarkContent;
            }
        } else {/*否则，展示（C 看 B：任职公司 · 职位  ；B 看 C：当前在招的职位名）*/

            if (UserManager.isBossRole()) {
                return contactBean.bossJobPosition;
            } else {
                if (contactBean.jobSource == 4 || contactBean.jobSource == 13 || !contactBean.isRecruiter()) {
                    return StringUtil.connectTextWithChar(" · ", contactBean.bossCompanyName, contactBean.geekPositionName);
                } else {
                    return contactBean.geekPositionName;
                }
            }
        }
    }

    @Override
    protected void setTitleText(String text, int connectState) {
        super.setTitleText(text, connectState);
        refreshOnlineIconAndExceptionTipLabel(null);
    }

    /**
     * 刷新「在线」绿点与异常提示标签
     */
    private void refreshOnlineIconAndExceptionTipLabel(@Nullable ContactBean mContactBean) {
        int connectState = MSGManager.get().getStatus();
        /*根据长链接的状态来控制 标题左侧 在线状态绿点的显示与隐藏*/
        if (connectState == ChatConstant.ChatConnectState.CONNECT_ING || connectState == ChatConstant.ChatConnectState.CONNECT_NO) {/*显示 「未连接 」和 「连接中」时，不显示异常状态标签 和 在线标签（显示优先级：未连接和连接中>异常状态>在线状态）*/
            if (titleView != null) {
                titleView.getTitleTextView().setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                titleView.getTvTitleRightLabel().setVisibility(View.GONE);
            }
        } else if (connectState == ChatConstant.ChatConnectState.CONNECT_SUCCESS) {
            if (mContactBean != null) {
                renderTitleLabelWhenConnectSuccess(mContactBean);
            } else {
                obtainFriendId(this::renderTitleLabelWhenConnectSuccess);
            }
        }
    }

    /**
     * 当连接成功状态下时渲染「在线」绿点与异常提示标签
     *
     * @param contactBean
     */
    private void renderTitleLabelWhenConnectSuccess(@NonNull ContactBean contactBean) {
        String exceptionTip = getTitleExceptionTip(contactBean);
        if (!TextUtils.isEmpty(exceptionTip)) {/*有异常提示信息，优先显示异常提示标签*/
            initTitleLabel(exceptionTip);/*标题右侧的标签*/
            titleView.getTitleTextView().setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        } else {/*否则，再判断是否显示在线的绿点*/
            titleView.getTvTitleRightLabel().setVisibility(View.GONE);
            boolean isOnLine = SingleChatOnlineUtil.getInstance().isOneLine(contactBean.friendId, contactBean.friendSource);
            int onlineStyle = SingleChatOnlineUtil.getInstance().getOnlineStyle();
            if (isOnLine) {
                if (onlineStyle == 2) {
                    titleView.setTitleRightIcon(R.mipmap.chat_icon_online, 6);
                } else {
                    titleView.setTitleLeftIcon(R.drawable.bg_67cc35_dot);
                }
            } else {
                titleView.getTitleTextView().setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            }
        }
    }

    private final ChatBottomFuncPresenter ChatBottomFuncPresenter = new ChatBottomFuncPresenter();


    private final IChatBottomFuncView iChatBottomFuncView = new IChatBottomFuncView() {

        @Override
        public void refreshCommonWordsListener() {
            bottomFunctionView.refreshCommonWords(getChatCommonList());
        }

        @Override
        public void onCommonWordsItemClickListener(CommonWordsUIBean item) {
            bottomFunctionView.onCommonWordTextListener(item.commonWordText);
            bottomFunctionView.setShowKeyboard();
            ChatUtils.isClickCommonReply = true;

            obtainFriendId(contactBean -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_QUICK_REPLY)
                    .param("p2", String.valueOf(contactBean.friendId))
                    .param("p3", Signer.getPassword(item.commonWordText))
                    .param("p4", "1")
                    .param("p5", item.commonWordId)
                    .build());

        }

        @Override
        public void onCommonWordsItemLongClickListener(MotionEvent event, CommonWordsUIBean item) {
            if (UserManager.isGeekRole()) {
                ZPUIPopupList.Builder builder = new ZPUIPopupList.Builder(ChatRoomActivity.this);
                builder.addItem("编辑", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        obtainFriendId(contactBean -> {
                            AnalyticsFactory
                                    .create()
                                    .action(AnalyticsAction.ACTION_QUICK_REPLY_PAGE_ACTION)
                                    .param("p", contactBean.friendId)// 聊天页面对方ID
                                    .param("p2", "2")// 1-长按出现"编辑"气泡 2-点击编辑气泡 3-点击添加 4-点击修改 5-点击管理¶ 6-上拉滑动至全屏 7-全屏收起
                                    .buildSync();


                            SingleRouter.geekJumpEditCommonWord(ChatRoomActivity.this, item.commonWordId, contactBean.friendId, ContactBean.FROM_BOSS);
                        });
                    }
                });
                builder.setRawAxis(event.getRawX(), event.getRawY()).build().show();


                obtainFriendId(contactBean -> AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_QUICK_REPLY_PAGE_ACTION)
                        .param("p", contactBean.friendId)// 聊天页面对方ID
                        .param("p2", "1")// 1-长按出现"编辑"气泡 2-点击编辑气泡 3-点击添加 4-点击修改 5-点击管理¶ 6-上拉滑动至全屏 7-全屏收起
                        .buildSync());

            }
        }

        @Override
        public void onCommonWordsSendListener(String text) {

            /*需要消耗权益*/
            sendTextMessageListener(text, true);

            //common.sendTextMessage(text, false);

            if (bottomFunctionView != null) {
                bottomFunctionView.resetAllView();
            }
        }

        @Override
        public void uploadImageFile(Uri fileUri, ContactBean contact) {


            common.bossLimitCheckUtils.checkLimit(contact, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
                @Override
                public void onBlockResult(String blockTip) {
                    if (blockTip != null) {
                        UploadChatManager.uploadQuickChatImageNoSend(ChatRoomActivity.this, fileUri, contact, (url, tinyUrl) ->
                                new SendMessageUtil().sendFailedImageMessage(contact, MessageTargetInfo.fromContactBean(contact), url,
                                        0, 0, tinyUrl, 0, 0,
                                        chatBean -> {

                                            common.addData(chatBean);
                                            common.addData(common.createBossLimitReasonText(blockTip));
                                            refreshShowData();
                                            common.scrollToBottom(false);
                                        }));
                    } else {
                        ContactKeyManager.getInstance().onPhotoSendListener(contact, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                uploadPhoto(fileUri, contact);
                            }
                        });
                    }
                }
            });

        }

        @Override
        public void uploadGalleryImageFile(List<Uri> fileUris, ContactBean contact) {

            common.bossLimitCheckUtils.checkLimit(contact, common.refreshBossTempCheckLimitList(), (blockTip) -> {
                if (blockTip != null) {
                    Uri element = fileUris.remove(0);
                    UploadChatManager.uploadQuickChatImageNoSend(ChatRoomActivity.this, element, contact, (url, tinyUrl) ->
                            new SendMessageUtil().sendFailedImageMessage(contact, MessageTargetInfo.fromContactBean(contact), url,
                                    0, 0, tinyUrl, 0, 0,
                                    chatBean -> {

                                        common.addData(chatBean);
                                        common.addData(common.createBossLimitReasonText(blockTip));
                                        refreshShowData();
                                        common.scrollToBottom(false);
                                    }));

                } else {

                    ContactKeyManager.getInstance().onPhotoSendListener(contact, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                        @Override
                        public void onCheckNoBlockListener() {
                            uploadgalleryImages(fileUris, contact);
                        }
                    });

                }

            });


        }

        @Override
        public void sendChatTextMessage(String text) {
            common.obtainContactBean(contactBean -> common.bossLimitCheckUtils.checkLimit(contactBean, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
                @Override
                public void onBlockResult(String blockTip) {
                    if (blockTip != null) {
                        common.sendFailedTextMessage(text, blockTip);
                    } else {
                        ContactKeyManager.getInstance().onSendAudioListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                common.sendTextMessage(text, false);
                            }
                        });
                    }
                }
            }));

        }

        @Override
        public void sendAudioFile(String audioFile, int duration) {
            common.obtainContactBean(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    common.bossLimitCheckUtils.checkLimit(contactBean, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {


                        @Override
                        public void onBlockResult(String blockTip) {
                            if (blockTip != null) {
                                common.sendFailedAudioFile(audioFile, duration, blockTip);
                            } else {
                                ContactKeyManager.getInstance().onSendAudioListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                                    @Override
                                    public void onCheckNoBlockListener() {
                                        common.onRecordSoundCompleteCallback(audioFile, duration);
                                    }
                                });
                            }
                        }
                    });


                }
            });


        }

        @Override
        public void sendChangeJobCard(JobSwitchBean jobSwitchBean) {
            common.sendJobCard(jobSwitchBean);
        }

        @Override
        public void sendLocationListener(JobBean jobBean) {
            if (jobBean.isMultiAddress()) {//职位存在多地址
                MultiAddressActivity.startForB(ChatRoomActivity.this, String.valueOf(getJobId()), String.valueOf(jobBean.locationIndex), REQ_SEND_LOCATION);
            } else {
                JobBean cloneJobBean = CloneUtils.cloneObject(jobBean);//里面会修改职位的经纬度
                if (cloneJobBean != null) {
                    Intent intent = SelectWorkLocationActivity.createIntent(ChatRoomActivity.this, cloneJobBean, true, "发送", PermissionConstants.SCENE_CHAT_SEND_LOCATION);
                    intent.putExtra("from", SelectWorkLocationActivity.SELECT_WORK_CHAT_SOURCE);
                    AppUtil.startActivityForResult(ChatRoomActivity.this, intent, REQ_SEND_LOCATION, ActivityAnimType.UP_GLIDE);
                }
            }
        }

        @Override
        public void onMessageNoticeListener() {
            common.smsItemPreUse();
        }


        @Override
        public void onRefreshChatMoreView() {
            bottomFunctionView.refreshChatMore();
            ChatBottomFuncPresenter.getChatMoreParams();
            // 更多设置中问意向接口请求完，需要刷新提示条，可能展示权益提醒
            tipStatusView.setChatEntranceData(ChatBottomFuncPresenter.getHunterChatEntranceResponse());
            refreshHeadTips();
        }

        @Override
        public void onShowPopWindowListener(List<BossChatMoreParam.WindowChatMoreTip> typeList) {

            for (BossChatMoreParam.WindowChatMoreTip type : typeList) {
                obtainFriendId(contactBean -> {
                    //好友名称
                    String friendName = contactBean.friendName;
                    String tipText = type.getTipText();
                    boolean autoDismiss = type.isAutoDismiss();
                    String resultTipText = String.format(tipText, friendName);
                    showMorePopWindowDialog(resultTipText, autoDismiss, type::setHasShow);
                });
                break;
            }

        }

        @Override
        public void onInputAssociateEmotionClickListener(ExtendEmotion extendEmotion) {
            sendOtherEmotionListener(extendEmotion);
            bottomFunctionView.setEditInputText("");
        }

        @Override
        public void onExtendEmotionClickListener(ExtendEmotion extendEmotion, boolean isFavourite) {
            sendOtherEmotionListener(extendEmotion);
        }
    };

    private void showMorePopWindowDialog(String titleText, boolean autoDismiss, PopupWindow.OnDismissListener dismissListener) {
        // AI帮聊开启时隐藏引导气泡
        if (AIHelpPanelUtils.isAIHelpPanelFuncEnable()) {
            return;
        }
        App.get().getMainHandler().postDelayed(() -> {
            if (!ActivityUtils.isValid(ChatRoomActivity.this)) {
                return;
            }

            View view = View.inflate(ChatRoomActivity.this, R.layout.chat_back_research_item, null);
            MTextView mTextView = view.findViewById(R.id.guide_bubble_tv);


            mTextView.setText(titleText);

            int[] location = new int[2];
            bottomFunctionView.getLocationOnScreen(location);

            int measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            view.measure(measureSpec, measureSpec);
            int x = location[1] - view.getMeasuredWidth();
            int y = location[1] - view.getMeasuredHeight();

            MPoupWindow popupWindow = new MPoupWindow(view, WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            popupWindow.setBackgroundDrawable(new BitmapDrawable());
            popupWindow.setOutsideTouchable(true);
            popupWindow.setFocusable(true);

            if (ActivityUtils.isValid(this)) {
                popupWindow.showAtLocation(getWindow().getDecorView(),
                        Gravity.NO_GRAVITY, x,
                        y);
            }


            popupWindow.setOnDismissListener(dismissListener);

            if (autoDismiss) {
                App.get().getMainHandler().postDelayed(() -> {
                    if (!ActivityUtils.isValid(ChatRoomActivity.this)) {
                        return;
                    }
                    if (popupWindow.isShowing()) {
                        popupWindow.dismiss();
                    }
                }, 5000);
            }


        }, 1000);
    }


    /**
     * @param contact 没有联系人---> "加好友" 接口成功后会回掉
     *                有联系人-----> 直接会回掉
     */
    @Override
    public void initContactInfo(final ContactBean contact) {

        //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=187612681
        String scene = BossEnter.canSendNlpScene(contact) ? "1" : "0";

        // 加载用户双方是否已经开聊
        startFirstCheckContact(contact);
        //初始化emotion
        initBottomView(contact);
        // 初始化AI帮聊窗口
        initAIHelpChatPanel();
        //初始化顶部交换电话/微信
        initTopExchange(contact);
        //初始化顶部黄条状态
        initChatTopStatus(contact);
        //初始化nlpView
        initNlpView(contact);

        //发送nlp出席消息
        sendNLPPresenterMessage(scene);
        //刷新滑梯
        slideView.refreshSlideView(contact.noneReadCount, contact.friendId, false);

    }

    /**
     * 初始化标题右侧的标签
     *
     * @param titleExceptionTip
     */
    private void initTitleLabel(String titleExceptionTip) {
        if (TextUtils.isEmpty(titleExceptionTip)) {
            titleView.getTvTitleRightLabel().setVisibility(View.GONE);
        } else {
            titleView.getTvTitleRightLabel().setPadding(ZPUIDisplayHelper.dp2px(this, 3), ZPUIDisplayHelper.dp2px(this, 2), ZPUIDisplayHelper.dp2px(this, 3), ZPUIDisplayHelper.dp2px(this, 2));
            titleView.getTvTitleRightLabel().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
            titleView.getTvTitleRightLabel().setText(titleExceptionTip);
            titleView.getTvTitleRightLabel().setTextColor(ContextCompat.getColor(this, R.color.color_Y6));
            titleView.getTvTitleRightLabel().setBackgroundResource(R.drawable.bg_2_corner_color_ffe9e1);
            titleView.getTvTitleRightLabel().setVisibility(View.VISIBLE);
        }
    }

    @NonNull
    private String getTitleExceptionTip(ContactBean contactBean) {
        String titleLabel = "";
        if (contactBean.isFreeze || contactBean.isPreFreeze) {/*已冻结 || 预冻结*/
            titleLabel = "已冻结";
        } else if (contactBean.isBlack) {/*已拉黑*/
            titleLabel = "已拉黑";
        } else if (contactBean.isReject) {/*不感兴趣/不合适*/
            titleLabel = UserManager.isBossRole() ? "不合适" : "不感兴趣";
        }
        return titleLabel;
    }

    private void initNlpView(ContactBean contact) {
        //NLP控件
        mNlpView.setJobId(getJobId());
        mNlpView.setCallBack(new DispatchSuggestCallBack(contact.friendId, ContactBean.FROM_BOSS, onSuggestCallBack));
        mNlpView.setHasInitRegister();
    }

    private void initChatTopStatus(ContactBean contact) {
        //聊天黄条
        tipStatusView.setChatCommon(common);
        tipStatusView.setiChatCommon(this);
        tipStatusView.setFriendId(contact.friendId);
        tipStatusView.setFriendSource(ContactBean.FROM_BOSS);
        tipStatusView.register(true);
        tipStatusView.userEnter(ChatRoomActivity.this, true, contact, securityId, new Callback<IUserEnter>() {
            @Override
            public void call(IUserEnter iUserEnter) {
                // 检查是否需要调用AI直聊推荐理由接口
                long aiDirectChatReasonInterval = 0;
                if (iUserEnter.bossEnterResponse != null) {
                    common.bossLimitCheckUtils.setBossChatPluginBlock(iUserEnter.bossEnterResponse.bossChatPluginBlockSwitch, iUserEnter.bossEnterResponse.bossChatPluginBlockThreshold, iUserEnter.bossEnterResponse.bossChatPluginBlockTips);
                    setAIChatHelperView(iUserEnter.bossEnterResponse.chatHelperActionStatus);

                    // 处理guideQuick引导餐饮Boss快速达成
                    GrayStageManager.getInstance().setGuideQuickExchange(iUserEnter.bossEnterResponse.guideQuick);
                    // 1308.165:【B&C】AI帮你推 卡片背景色 style 适配
                    aiDirectChatReasonInterval = iUserEnter.bossEnterResponse.aiDirectChatReasonInterval;
                }

                if (iUserEnter.geekEnterResponse != null) {
                    common.setOverdueBossInfo(iUserEnter.geekEnterResponse.overdueBossInfo);
                    // 1308.165:【B&C】AI帮你推 卡片背景色 style 适配
                    aiDirectChatReasonInterval = iUserEnter.geekEnterResponse.aiDirectChatReasonInterval;
                }

                requestAIDirectChatReasonDelay(aiDirectChatReasonInterval);

                boolean isOnline = iUserEnter.isOnline;
                int onlineStyle = iUserEnter.onlineStyle;
                if (isOnline) {
                    if (onlineStyle == 2) {
                        titleView.setTitleRightIcon(R.mipmap.chat_icon_online, 6);
                    } else {
                        titleView.setTitleLeftIcon(R.drawable.bg_67cc35_dot);
                    }
                    SingleChatOnlineUtil.getInstance().registerOnLine(ChatRoomActivity.this,
                            contact.friendId, contact.friendSource, true, onlineStyle);
                } else {
                    SingleChatOnlineUtil.getInstance().removeUserOnLine(contact.friendId, contact.friendSource);
                }
                setChatBackground(iUserEnter.chatBgImg);

                if (iUserEnter.refreshExchange) {
                    refreshExchangeView();
                }

                if (iUserEnter.botherConfig != null && iUserEnter.botherConfig.botherDetect == 1) {
                    App.get().getMainHandler().postDelayed(() -> {
                        common.setBotherMaxNum(iUserEnter.botherConfig.continuousMsgNum);
                        common.checkIsBother();
                    }, 200);
                }

                //1223.613 展示交换引导
                if (iUserEnter.geekExchangeTip != null && !TextUtils.isEmpty(iUserEnter.geekExchangeTip.copyWriting)) {
                    bossUserExchangeContainer.showGuideTip(iUserEnter.geekExchangeTip.requestType, iUserEnter.geekExchangeTip.copyWriting);
                    ChatAnalysisUtil.statsExchangeGuidePop(iUserEnter.geekExchangeTip.requestType);
                }

                //1224.112【招聘者】用户换公司后自动打招呼场景
                if (iUserEnter.bossEnterResponse != null
                        && iUserEnter.bossEnterResponse.greetingGuide != null
                        && iUserEnter.bossEnterResponse.greetingGuide.customTemplateId > 0) {
                    if (common.isHasAgreeLocation()) {
                        checkShowEditGreet(iUserEnter.bossEnterResponse.greetingGuide);
                    } else {
                        common.setBossGreetingGuideBean(iUserEnter.bossEnterResponse.greetingGuide);
                    }
                }

            }
        });
    }

    @Override
    public boolean checkShowEditGreet(BossGreetingGuideBean bossGreetingGuide) {
        if (bossGreetingGuide != null
                && bossGreetingGuide.customTemplateId > 0) {
            new ChatEditGreetingWordDialog().showDialog(ChatRoomActivity.this, bossGreetingGuide, common.getFriendId(), common.getFriendSource(), common.getJobId());
            common.setBossGreetingGuideBean(null);
            return true;
        }
        return false;
    }

    @Override
    public boolean showEditBossPosition() {
        if (UserManager.isBossRole() && DataStarGray.getInstance().getBossTitleSuggestGuideGroup() == 2) {
            return BossPageRouter.showEditBossPositionDialog(this);
        }
        return false;
    }

    @Override
    public void setAIChatHelperView(int status) {
        GrayStageManager.getInstance().setChatHelperActionStatus(status);
        if (status <= 0) {
            aiAssistantView.setVisibility(View.GONE);

            if (ChatUtils.noBossLocationPermission(this)) {
                setNoBossLocationPermission(true);
                if (aiHelpChatPendingTask == null) {
                    aiHelpChatPendingTask = new ChatPendingTaskBean(ChatPendingTaskBean.TASK_AI_HELP_CHAT, this::setBossAiHelpChatPanel);
                }
                common.getPendingTaskAfterLocationPermissionGrant().add(aiHelpChatPendingTask);
            } else {
                setNoBossLocationPermission(false);
                setBossAiHelpChatPanel();
            }
        } else {
            if (aiAssistantView.getStatus() != status) {
                ChatAnalysisUtil.statsAIChatInteractionExpo(common.getFriendId(), common.getJobId(), common.getJobIntentId(), status);
            }
            // ai标识和nlp横条互斥
            aiAssistantView.setVisibility(View.VISIBLE);
            if (aiAssistantParam == null) {
                aiAssistantParam = new AIAssistantView.AIAssistantParam(common.getSecurityId(), common.getFriendId(), common.getJobId(), common.getJobIntentId());
            }
            aiAssistantView.setData(this, status, aiAssistantParam);
        }
    }

    private void initTopExchange(ContactBean contact) {
        bossUserExchangeContainer.setAnalyCallBack(new WeiChatConversationIconView.AnalyCallBack() {
            @Override
            public void onWetChatAnalyListener(long p, String p2) {
                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_EXCHANGE_WECHAT_CLICK)
                        .param("p", p)
                        .param("p2", p2)
                        .build();
            }

            @Override
            public void onPhoneAnalyListener(long p, String p2) {
                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_EXCHANGE_MOBILE_CLICK)
                        .param("p", p)
                        .param("p2", p2)
                        .build();
            }

            @Override
            public void onInterview() {
                common.obtainContactBean(mContact -> {
                    AnalyticsFactory factory = AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_CHAT_INTERVIEW_CLICK)
                            .param("p", String.valueOf(mContact.friendId))
                            .param("p2", String.valueOf(mContact.jobId))
                            .param("p3", String.valueOf(mContact.jobIntentId))
                            .secId(mContact.securityId)
                            .param("p4", String.valueOf(2));
                    if (!TextUtils.isEmpty(mContact.currentInterviewProtocol)) {
                        Map<String, String> params = ZPManager.UrlHandler
                                .getParams(mContact.currentInterviewProtocol);
                        if (!params.isEmpty() && params.containsKey("interviewid")) {
                            factory.param("p3", params.get("interviewid"));
                        }
                        factory.param("p5", "");
                    }
                    factory.build();
                });

            }

            @Override
            public void onResumeListener(long p, String p7) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_RESUME_SEND_CLICK)
                        .param("p", p)
                        .param("p7", p7)
                        .buildSync();
            }

            @Override
            public void onBossPhoneWeChatListener(long p, String p2) {
                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_EXCHANGE_CONTACT_CLICK)
                        .param("p", p)
                        .param("p2", p2)
                        .build();
            }
        });

        bossUserExchangeContainer.initViewAndCallBack(contact, getExchangeHelperWithCallBack(contact));
    }

    private void initBottomView(ContactBean contact) {

        common.registerQuoteMessage(contact.quoteMessageId, chatBean -> chatQuoteMsgView.setQuoteMsgInfo(chatBean));


        ChatBottomFuncPresenter.setiChatBottomFuncView(iChatBottomFuncView);
        ChatBottomFuncPresenter.setActivity(this);
        ChatBottomFuncPresenter.setFriendId(contact.friendId);
        ChatBottomFuncPresenter.setFriendSource(ContactBean.FROM_BOSS);
        ChatBottomFuncPresenter.setSecurityId(contact.securityId);

        bottomFunctionView.setChatMoreContainerCallBack(new BossUserLayout.OnChatMoreContainerCallBack() {
            @Override
            public void onChatrMoreVisibleListener(boolean messageNotice, boolean showGeekHunterIntent) {
                if (messageNotice) {
                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_BLOCK_MSG_EXPOSURE).build();
                }

                if (showGeekHunterIntent) {
                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_HUNTER_APP_CHAT_EXPOSE)
                            .param("p", contact.jobId)
                            .param("p2", contact.friendId)
                            .build();
                }
            }
        });
        //搜索出来gif显示在nlp上面
        bottomFunctionView.setGifAboveView(mNlpView);
        //常用语参数
        bottomFunctionView.setCommonWordsParam(ChatBottomFuncPresenter.getCommonWordsParams());
        bottomFunctionView.setCallback(dy -> {
            recyclerView.scrollToPosition(chatAdapter2.getItemCount() - 1);
            bottomFunctionView.requestLayout();
        });
        //更多参数
        bottomFunctionView.setMoreParams(ChatBottomFuncPresenter.getChatMoreParams(bottomFunctionView.getToolsLayout(), mNlpView));
        //表情参数
        bottomFunctionView.setEmotionParams(ChatBottomFuncPresenter.getEmotionParams());
        bottomFunctionView.setInputTextStateChangeCallBack(isShow -> {
            appBarLayoutCollapseCount = 0;
            if (isShow) {
                common.scrollToBottom(false);
            }
        });
        //发送文本
        bottomFunctionView.setOnSendMessageClickCallBack(message -> {
            sendTextMessageListener(message, false);
        });
        //NLP监控输入框文本变化
        bottomFunctionView.addTextChangeCallBack(ChatRoomActivity.this::onTextChangeListener);
        //点击更多埋点
        bottomFunctionView.setOnMoreClickListener(v -> onMoreClickBgAction());

        bottomFunctionView.setOnEmotionClickListener(v -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_EMOTION_MENU_CLICK).buildSync());
        //推荐表情
        bottomFunctionView.setOnSuggestEmotionCallBack(emotion -> {
            sendOtherEmotionListener(emotion);
            bottomFunctionView.setEditInputText("");
        });
        bottomFunctionView.setSecurityId(contact.securityId);
        bottomFunctionView.setIsBoss(UserManager.isBossRole()); // 控制发送按钮展示样式
        setNoBossLocationPermission(false);
        //初始化view
        bottomFunctionView.initBottomLayout();

        bottomFunctionView.observerEmotion(this);

        if (!LText.empty(defaultInputText)) {
            bottomFunctionView.setEditInputText(defaultInputText);
        } else {
            //初始化草稿
            String roughDraft = contact.RoughDraft;
            bottomFunctionView.setEditInputText(ChatUtils.getContactDraft(roughDraft));
            //有草稿情况,显示键盘
            if (!LText.empty(roughDraft) || contact.quoteMessageId > 0) {
                showKeyBoard();
            }
        }

        if (autoShowKeyBoard) {
            showKeyBoard();
        }
    }

    private void showKeyBoard() {
        boolean isAndroid13 = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R;
        //Android13设备在该位置 会显示键盘该住了输入框：需要延时800显示键盘就没问题
        App.get().getMainHandler().postDelayed(() -> {
            // AI助聊开启时，有引用或草稿不弹键盘
            if (!AIHelpPanelUtils.isAIHelpPanelFuncEnable()) {
                bottomFunctionView.setShowKeyboard();
            }
        }, isAndroid13 ? 800 : 200);
    }

    private String chooseAIGeneratedText;
    private void initAIHelpChatPanel() {
        if (!AIHelpPanelUtils.isAIHelpPanelFuncEnable()) {
            setShowAIHelpChatPanel(AIHelpPanelUtils.TYPE_NONE);
            return;
        }

        setShowAIHelpChatPanel(AIHelpPanelUtils.TYPE_PANEL);
        AIAssistInfo aiAssistInfo = GrayStageManager.getInstance().getAiAssistInfo();
        AIHelpChatParam aiHelpChatParam = new AIHelpChatParam();
        aiHelpChatParam.bizCode = aiAssistInfo != null ? aiAssistInfo.bizCode : "";
        aiHelpChatParam.contactBean = common.getContactBean();
        aiHelpChatParam.onSuggestCallBack = onSuggestCallBack;
        aiHelpChatPanel = new AIHelpChatPanel(this, aiHelpChatParam, new AIChatPanelListener() {
            @Override
            public void onSendContentDirectly(AIGeneratedResultEntity entity) {
                if (entity == null || LText.empty(entity.getContent())) {
                    return;
                }

                SendMsgExtraBean extraBean = new SendMsgExtraBean();
                extraBean.bizCode = AIHelpPanelUtils.AI_MSG_BIZ_CODE;
                common.sendTextMessage(entity.getContent(), extraBean);
                onAfterHandleAIMsg();
                ChatAnalysisUtil.statsChatAIReplyClick(common.getFriendId(), common.getJobId(), 1, entity.getType(), 2, entity.getContent());
            }

            @Override
            public void onFillContent(AIGeneratedResultEntity entity) {
                if (entity == null || LText.empty(entity.getContent())) {
                    return;
                }

                chooseAIGeneratedText = entity.getContent();
                if (bottomFunctionView != null) {
                    bottomFunctionView.setEditInputText(entity.getContent());
                    bottomFunctionView.setShowKeyboard();
                }
                onAfterHandleAIMsg();
                ChatAnalysisUtil.statsChatAIReplyClick(common.getFriendId(), common.getJobId(), 1, entity.getType(), 1, entity.getContent());
            }

            @Override
            public void onClosePanel(int scene) {
                int type = AIHelpPanelUtils.TYPE_BUBBLE;
                showAIAssistChatView(type);
                bottomFunctionView.resetAllView();
                curNlpListBean = null;
            }

            @Override
            public void onNlpClick(NLPListBean nlpListBean) {
                curNlpListBean = null;
                // 到达关闭阈值，处理完NLP自动关闭
                if (AIHelpPanelUtils.isReachLimit()) {
                    showAIAssistChatView(AIHelpPanelUtils.TYPE_BUBBLE);
                    bottomFunctionView.resetAllView();
                    return;
                }
                onAfterHandleAIMsg(true);
            }

            @Override
            public void onRefreshClick() {
                curNlpListBean = null;
            }
        });

        bottomFunctionView.setAiHelpChatPanelView(aiHelpChatPanel);
        // 牛人没有智能助手，直接打开AI帮聊
        if (UserManager.isGeekRole()) {
            setAIHelpChatPanelView();
        }
    }

    private void setBossAiHelpChatPanel() {
        setAIHelpChatPanelView();
        common.setIgnoreSetAIHelpChatPanel(true);
    }

    private void setAIHelpChatPanelView() {
        if (!AIHelpPanelUtils.isAIHelpPanelFuncEnable()) {
            setShowAIHelpChatPanel(AIHelpPanelUtils.TYPE_NONE);
            return;
        }

        // 达到关闭限制，默认展示气泡入口
        if (AIHelpPanelUtils.isReachLimit()) {
            showAIAssistChatView(AIHelpPanelUtils.TYPE_BUBBLE);
            return;
        }

        if (common.isIgnoreSetAIHelpChatPanel()) {
            return;
        }

        com.hpbr.bosszhipin.chat.utils.ChatUtils.showAIHelpChatGuideDialog(this, () -> {
            if (bottomFunctionView != null) {
                int type = common.getAiHelpChatType();
                setShowAIHelpChatPanel(type);
                bottomFunctionView.initBottomLayout();
                startAIGenerate();

                if (!hasReportAIPanelShow) {
                    common.reportAIHelpChatPanelShow();
                    hasReportAIPanelShow = true;
                }
            }
        });
    }

    private void openAIHelpChatPanel() {
        if (bottomFunctionView == null) {
            return;
        }

        showAIAssistChatView(AIHelpPanelUtils.TYPE_PANEL);
        bottomFunctionView.resetAllView();
        startAIGenerate();
    }

    private void startAIGenerate() {
        // 首次生成清除标记，全新生成
        newMessage = null;
        curNlpListBean = null;

        if (aiHelpChatPanel != null) {
            aiHelpChatPanel.startAIGenerate();
        }
    }

    private void setAiAssistChatView(int type) {
        if (!AIHelpPanelUtils.isAIHelpPanelFuncEnable()) {
            aiAssistChatView.setVisibility(View.GONE);
            setShowAIHelpChatPanel(AIHelpPanelUtils.TYPE_NONE);
            return;
        }

        if (aiAssistChatView != null) {
            aiAssistChatView.setVisibility(type == AIHelpPanelUtils.TYPE_BUBBLE ? View.VISIBLE : View.GONE);
        }
    }

    private void submitByNewMsg(ChatBean bean) {
        // 新消息只计算用户发送的文本消息
        if (!com.hpbr.bosszhipin.chat.utils.ChatUtils.isValidChatMessage(bean) || bean.message.messageBody.type != 1 || bean.message.messageBody.templateId != 1) {
            return;
        }

        if (aiHelpChatPanel == null || common.getAiHelpChatType() != AIHelpPanelUtils.TYPE_PANEL) {
            return;
        }

        // 当前展示nlp气泡或loading且无sessionid，不触发自动生成。等待处理后再请求
        AIHelpChatPanel.PanelStateType curStateType = aiHelpChatPanel.getCurrentStateType();
        if (curStateType == AIHelpChatPanel.PanelStateType.NLP_COMPATIBLE || (curStateType == AIHelpChatPanel.PanelStateType.LOADING && LText.empty(aiHelpChatPanel.getSessionId()))) {
            newMessage = bean;
            return;
        }

        // loading状态，先stop在submit
        if (curStateType == AIHelpChatPanel.PanelStateType.LOADING) {
            aiHelpChatPanel.stopAIGenerate();
            aiHelpChatPanel.startAIGenerate();
            return;
        }
        aiHelpChatPanel.submit(2);
    }

    private void onAfterHandleAIMsg() {
        onAfterHandleAIMsg(false);
    }

    private void onAfterHandleAIMsg(boolean needClearCurState) {
        if (newMessage == null) {
            aiHelpChatPanel.showEmptyView();
            return;
        }

        // 发送消息后，如果在这之前又收到了新消息，则继续生成
        aiHelpChatPanel.submit(2, needClearCurState);
        newMessage = null;
    }

    private void showAIAssistChatView(int type) {
        setShowAIHelpChatPanel(type);
        setAiAssistChatView(type);
    }

    private void setShowAIHelpChatPanel(int type) {
        if (bottomFunctionView != null) {
            bottomFunctionView.setAIHelpChatType(type);
        }
        common.setAiHelpChatType(type);
    }

    private void refreshBottomHint(ContactBean contactBean) {
        String hint = "回复消息";
        if (contactBean != null) {
            if (contactBean.isContactEachOther()) {
                hint = "回复消息";
            } else if (contactBean.isInitiativeConservation()) {
                hint = UserManager.isGeekRole() ? "介绍自己回复率更高哦~" : "介绍更多信息~";
            } else if (contactBean.isPassiveConservation()) {
                hint = "和Ta聊一聊~";
            }
        }
        bottomFunctionView.setInputHintText(hint);
    }


    //创建业务处理层与回掉
    private OnExchangeCallBack getExchangeHelperWithCallBack(ContactBean contactBean) {

        if (UserManager.isBossRole()) {

            return new BossRoleChatBossUserPresenter2(contactBean,
                    from,
                    this,
                    mNlpView,
                    new BossRoleChatBossUserPresenter2.BossRoleChatBossUserPresenterCallBack() {

                        @Override
                        public void setEncryptBlockBagId(String entryId) {
                            common.setEncryptBlockBagId(entryId);
                        }

                        @Override
                        public void refreshExchangeContainer() {
                            refreshExchangeView();
                        }

                        @Override
                        public void onNewChatBean(ChatBean chatBean) {
                            common.addData(chatBean);
                            refreshShowData();
                        }

                        @Override
                        public void smoothToBottom() {
                            common.scrollToBottom(false);
                        }

                        @Override
                        public void checkShowNewAvAudioMore() {
                            bottomFunctionView.checkShowNewAvAudioMore(ChatRoomActivity.this, SwitchCommon.isShowVideoIcon(), contactBean.friendId, frinedId -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_BUBBLE_EXCHANGE_TEL).param("p", String.valueOf(contactBean.friendId)).buildSync());
                        }

                        @Override
                        public void onClickInterviewMessage() {
                            common.onClickInterviewMessage(null);
                        }

                        @Override
                        public void onRecordNoticeListener() {
                            obtainFriendId(contactBean -> ChatBottomFuncPresenter.onRecordNoticeListener(contactBean, false));
                        }

                        @Override
                        public int onAgreeExchangeDirectly(int dialogType) {
                            return onAgreeExchange(dialogType, false);
                        }

                        @Override
                        public void onExchangePhoneWxWithSingleChat() {
                            refreshExchangeView();
                        }
                    });
        } else {

            return new GeekRoleChatBossUserPresenter2(contactBean,
                    from,
                    this,
                    mNlpView,
                    new GeekRoleChatBossUserPresenter2.OnGeekRoleChatBossPresenterCallBack() {

                        @Override
                        public void checkShowNewAvAudioMore() {
                            bottomFunctionView.checkShowNewAvAudioMore(ChatRoomActivity.this, SwitchCommon.isShowVideoIcon(), contactBean.friendId, frinedId -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_BUBBLE_EXCHANGE_TEL).param("p", String.valueOf(contactBean.friendId)).buildSync());
                        }

                        @Override
                        public void refreshExchangeContainer() {
                            refreshExchangeView();
                        }

                        @Override
                        public void addNewChatBean(ChatBean chatBean) {
                            common.addData(chatBean);
                            refreshShowData();
                        }

                        @Override
                        public void smoothToBottom() {
                            common.scrollToBottom(false);
                        }

                        @Override
                        public int onAgreesExchangeDirectly(int dialogType) {
                            return onAgreeExchange(dialogType, false);
                        }
                    });
        }


    }


    private void uploadgalleryImages(List<Uri> fileUris, ContactBean contact) {
        //上传图片
        UploadChatManager.uploadQuickChatImages(ChatRoomActivity.this, fileUris, contact, ContactBean.FROM_BOSS, new OnMessageSendCallBack() {

            @Override
            public void onMsgStartSendListener(ChatBean itemBean) {
                common.addData(itemBean);
                refreshShowData();
                common.scrollToBottom(false);

                //递归上传图片
                if (!LList.isEmpty(fileUris)) {
                    UploadChatManager.uploadQuickChatImages(ChatRoomActivity.this, fileUris, contact, ContactBean.FROM_BOSS, this);
                }
            }

            @Override
            public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                if (BossBlockCheck.checkMaxLimitAddText(contact, common.refreshBossTempCheckLimitList())) {
                    common.addData(common.createBossLimitStartNoticeText());
                }
                //通知查看是否双聊
                onSendChatCallback(null);
                //通知刷新列表
                refreshShowData();
            }

        });
    }

    /**
     * 上传图片
     *
     * @param fileUri
     * @param contact
     */
    private void uploadPhoto(Uri fileUri, ContactBean contact) {
        UploadChatManager.uploadQuickChatImage(ChatRoomActivity.this, fileUri, contact, ContactBean.FROM_BOSS, new OnMessageSendCallBack() {
            @Override
            public void onMsgStartSendListener(ChatBean contactBean) {
                common.addData(contactBean);
                refreshShowData();
                common.scrollToBottom(false);
            }

            @Override
            public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                if (BossBlockCheck.checkMaxLimitAddText(contact, common.refreshBossTempCheckLimitList())) {
                    common.addData(common.createBossLimitStartNoticeText());
                    refreshShowData();
                    common.scrollToBottom(false);
                }
            }
        });
    }

    @Override
    public void finishThis() {
        AppUtil.finishActivity(this);
    }

    @Override
    public void initTopCommon(boolean show) {
        refreshExchangeView();
    }

    private boolean hasScrolled;


    private ChatAdapter2 chatAdapter2;

    @Override
    public void refreshShowData() {

        refreshExchangeView();
        common.refreshTopStatusView();

        obtainFriendId(contact -> {

            List<ChatBean> data = common.getMessageData2();

            if (data == null) return;

            if (chatAdapter2 == null) {
                chatAdapter2 = new ChatAdapter2(new IAdapterCallBack.SingleChatImp(common,
                        bottomFunctionView, new IRefreshAdapterCallBack() {
                    @Override
                    public void onRefreshAdapterListener() {
                        refreshShowData();
                    }

                    @Override
                    public void onReplyListener(ChatBean bean) {
                        chatQuoteMsgView.setQuoteMsgInfo(bean);
                    }

                    @Override
                    public void onAddGifEmotionListener(ChatBean bean) {
                        OnZPUIChatGifLongClickListener.executeFavourite(ChatRoomActivity.this, bean.message.messageBody.gifImageBean,bean.msgId);
                    }
                }), new ISendUserCallBack.ChatRoomUserCallBack());
                recyclerView.setAdapter(chatAdapter2);
            }
            chatAdapter2.setNewData(data);


            if (UserManager.isBossRole()) {
                //检测是否有加密的消息,控制【常用语】【输入框】【+】是否可以点击
                boolean hasEncryptMessage = common.hasEncryptMessage();
                if (hasEncryptMessage) {
                    mPayCoverView.setVisibility(View.VISIBLE);
                    mPayCoverView.setOnClickListener(v -> {

                        checkDecryMessage();

                        ApmAnalyzer.create()
                                .action("LastChatText", "isShowPayCover")
                                .p3("hasEncryptMessage")
                                .p4("ChatRoomActivity")
                                .report();

                    });
                    return;
                }

                //技术类牛人开聊收费
                if (contact.isTechGeekBlock) {
                    mPayCoverView.setVisibility(View.VISIBLE);
                    bottomFunctionView.setInputHintText("需使用道具回复");
                    mPayCoverView.setOnClickListener(v -> {

                        ApmAnalyzer.create()
                                .action("LastChatText", "isShowPayCover")
                                .p3("isTechGeekBlock")
                                .p4("ChatRoomActivity")
                                .report();

                        checkShowChatBlock();
                    });
                    return;
                }

                mPayCoverView.setVisibility(View.GONE);
                if (chatBlockManager != null) {
                    chatBlockManager.hideBottomView();
                }
            }

            //从快速处理进来显示键盘
            checkIsFromFastHandler();

        });


    }

    private void checkDecryMessage() {
        obtainFriendId(contactBean -> {
            UnlockChatRequest request = new UnlockChatRequest(new ApiRequestCallback<UnlockChatResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    showProgressDialog("加载中");
                }

                @Override
                public void onSuccess(ApiData<UnlockChatResponse> data) {
                    final UnlockChatResponse resp = data.resp;
                    if (resp.block && resp.page != null) {
                        ParamBean paramBean = new ParamBean();
                        paramBean.userId = contactBean.friendId;
                        BusinessPageRouter.jumpToChatLimitActivity(ChatRoomActivity.this, paramBean, true, resp.page);
                    } else {
                        ToastUtils.showText(resp.vipChatToast);
                    }
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    T.ss(reason.getErrReason());
                }
            });
            request.securityId = contactBean.securityId;
            HttpExecutor.execute(request);
        });
    }


    public @Nullable
    ChatBlockManager getChatBlockManager() {
        obtainFriendId(contactBean -> {
            if (chatBlockManager == null) {
                chatBlockManager = new ChatBlockManager(ChatRoomActivity.this, contactBean.securityId, new ChatBlockManager.OnPayCallback() {
                    @Override
                    public void onPayListener(ServerVipItemBean bean) {
                        common.setEncryptBlockBagId(bean.encryptBlockBagId);
                    }
                });
                chatBlockManager.setGeekId(contactBean.friendId);
                chatBlockManager.setJobId(getJobId());
            }
        });
        return chatBlockManager;
    }


    @Override
    public void checkShowChatBlock() {
        obtainFriendId(contactBean -> {
            ChatBlockManager chatBlockManager = getChatBlockManager();
            if (chatBlockManager != null) {
                chatBlockManager.showPayDialog();
            }
        });
    }


    /**
     * 跟TA互换电话，已经存在电话情况，本地弹出临时卡片
     */
    @Override
    public void OnPhoneContactCallBack(long friendId, int friendSource) {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                if (contactBean.friendId == friendId && contactBean.friendSource == friendSource) {
                    onExchangePhoneListener();
                }
            }
        });
    }

    //交换电话
    private void onExchangePhoneListener() {
        onExchangePhoneListener(null);
    }

    private void onExchangePhoneListener(@Nullable NLPSuggestResultBean nlpSuggestResultBean) {

        obtainFriendId(contactBean -> {
            ExchangePhoneManager exchangePhoneManager = new ExchangePhoneManager(ChatRoomActivity.this, contactBean);
            exchangePhoneManager.setScene(nlpSuggestResultBean != null ? nlpSuggestResultBean.scene : 0);
            exchangePhoneManager.setCallBack(new ExchangePhoneManager.OnExchangePhoneCallBack() {
                @Override
                public void onSendLocalPhoneMessage(ChatBean chatBean) {
                    if ("******".equals(contactBean.friendPhone)) {
                        sendExchangeGrayTextMessage(1);
                    } else {
                        common.addData(chatBean);
                        refreshShowData();
                        common.scrollToBottom(false);
                    }
                }

                @Override
                public void onRefreshExchangeView() {
                    refreshShowData();
                }
            });
            exchangePhoneManager.onExchangePhoneListener();
        });

    }


    /**
     * 7.07 简聊，获取详情页的简聊文字后，放置在聊天页的输入框中，并弹出键盘
     *
     * @param text
     */
    @Override
    public void setContentInEditText(String text) {
        bottomFunctionView.setEditInputText(text);
        //弹起键盘
        App.get().getMainHandler().postDelayed(() -> bottomFunctionView.setShowKeyboard(), 200);
    }

    @Override
    public void onNewChatCallback(ChatBean bean) {
        startCheckIsContacted();
        submitByNewMsg(bean);
    }

    @Override
    public void onSendChatCallback(ChatBean bean) {
        startCheckIsContacted();
        tipStatusView.setMessageListener();
    }

    @Override
    public void refreshHeadTips() {
        tipStatusView.postInvalidateUI();
    }

    @Override
    public void showKeyBoard(long friendId) {

    }


    /**
     * 快速处理弹出键盘
     */
    private void checkIsFromFastHandler() {
        if (IntentParamConstant.FROM_FAST_HANDLE_CHAT.equals(from) && !hasShowFastHandlerKeyBoard && !common.hasEncryptMessage()) {
            hasShowFastHandlerKeyBoard = true;
            //弹起键盘
            App.get().getMainHandler().postDelayed(() -> bottomFunctionView.setShowKeyboard(), 200);
        }
    }


    private boolean onDialogViewButtonIntercept;

    @Override
    public boolean onDialogViewButtonClickIntercept(final ChatBean chatBean, ChatDialogBean
            dialogBean, final int index) {
        if (dialogBean.type == 1
                && index == 1
                && showSettingWechatDialog(chatBean, index)) {
            // 这里意为对话框为微信对话框，点击同意按键，同时还未设置微信号码
            return true;
        }

        onDialogViewButtonIntercept = false;

        obtainFriendId(contact -> {
            if (UserManager.getUserRole() == ROLE.GEEK
                    && dialogBean.type == 8) {
                // 这里是牛人点击拒绝Boss的牛炸开聊，并且牛人当前未拒绝过Boss
                if (!contact.isReject && index == 2) {
                    // 拒绝
                    DialogUtils d = new DialogUtils.Builder(ChatRoomActivity.this)
                            .setDoubleButton()
                            .setTitle(R.string.string_reject_boss_position_dialog_title)
                            .setDesc(R.string.string_reject_boss_position_dialog_desc)
                            .setNegativeAction(R.string.string_think_twice)
                            .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    chatDialogOnReject(chatBean, index);
                                }
                            })
                            .build();
                    d.show();
                    onDialogViewButtonIntercept = true;
                } else if (contact.isReject && index == 1) { // 接受
                    chatDialogOnReject(chatBean, index);
                    onDialogViewButtonIntercept = true;
                }
            }

        });

        return onDialogViewButtonIntercept;
    }

    private void chatDialogOnReject(final ChatBean chatBean, final int index) {

        obtainFriendId(contactBean -> UserRejectManager.getInstance().changeRejectListener(contactBean, UserRejectManager.PAGE_TYPE_CHAT_PAGE, new RejectHttpManager.OnLockUnLockCallBack() {

            @Override
            public void onSuccess(ContactBean contactBean) {
                common.resume();
                common.onDialogViewButtonClickCallback(chatBean, index);
            }

            @Override
            public void onFailed(ContactBean contactBean) {

            }
        }));

    }

    private void startFirstCheckContact(ContactBean contactData) {
        startCheckIsContact(contactData, () -> {
            //使用到双聊标注 需要获取完是否双聊之后才执行
            refreshBottomHint(contactData);
        });
    }

    private void startCheckIsContact(ContactBean contactData, Runnable runnable) {
        //从数据库获取是否双聊
        //检测收否已经收到 电话交换助手 卡片，收到默认开启双聊，可以随便发送卡片信息
        ScheduledExecutorService execute = AppThreadFactory.POOL;
        if (!execute.isShutdown() && contactData.friendId > 0 && !contactData.isContactEachOther()) {
            execute.execute(() -> {

                /*检测双聊*/
                checkContactIsContactWithEachOther(contactData);

                App.get().getMainHandler().post(() -> {
                    //牛人第一次达成双聊天,需要请求接口出发NLP
                    ChatBottomFuncPresenter.sendGeekNlpGuide();
                    refreshExchangeView();
                    refreshBottomHint(contactData);
                    if (runnable != null) {
                        runnable.run();
                    }
                    //双聊变化 收缩状态
                    if (contactData.isContactEachOther()) {
                        appBarLayoutCollapseCount = 0;
                    }
                });
                tipStatusView.refreshContactWithEachOther(contactData);
            });
        } else if (runnable != null) {
            App.get().getMainHandler().post(runnable);
        }
    }

    /**
     * 开启一个线程检测双方是否已经开聊
     */
    @Override
    public void startCheckIsContacted() {
        obtainFriendId(contact -> startCheckIsContact(contact, null));
    }

    @Override
    public void onAgreeExchangeTip(ChatBean chatBean) {
        if (common != null) {
            common.onDialogViewButtonClickCallback(chatBean, 1);
        }
    }

    //交换微信
    private void onExchangeWeChatListener(@Nullable NLPSuggestResultBean nlpSuggestResultBean) {
        obtainFriendId(contactBean -> {
            ExchangeWeChatManager exchangeWeChatManager = new ExchangeWeChatManager(ChatRoomActivity.this, contactBean);
            exchangeWeChatManager.setScene(nlpSuggestResultBean != null ? nlpSuggestResultBean.scene : 0);
            exchangeWeChatManager.setCallBack(new ExchangeWeChatManager.OnExchangeWeChatCallBack() {
                @Override
                public void onSendLocalWeChatListener(ChatBean chatBean) {

                    if (!LText.empty(contactBean.friendWxNumber)) {
                        //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=136553351
                        if ("******".equals(contactBean.friendWxNumber)) {
                            sendExchangeGrayTextMessage(2);
                        } else {
                            sendWechatNumberTextMessage(false);
                        }
                    }

                }

                @Override
                public void onRefreshExchangeViewListener() {
                    refreshExchangeView();
                }
            });
            exchangeWeChatManager.onExchangeWeChatListener();
        });
    }


    //检测当前用户是否设置了微信
    private boolean checkMySelfHasSetWeChat() {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return false;
        if (UserManager.getUserRole() == ROLE.GEEK) {
            GeekInfoBean geekInfoBean = loginUser.geekInfo;
            if (geekInfoBean != null) {
                return !LText.empty(geekInfoBean.weixin);
            }
        }
        BossInfoBean bossInfoBean = loginUser.bossInfo;
        return bossInfoBean != null && !LText.empty(bossInfoBean.weixin);
    }

    // ************* 发送交换微信相关方法 ****************

    /**
     * 判断是否需要弹出设置微信的对话框
     *
     * @param bean  实例
     * @param index 点击下标
     * @return true：需要设置微信号 false：不需要设置微信号
     */
    private boolean showSettingWechatDialog(final ChatBean bean, final int index) {
        //自己没有设置微信&&弹出设置微信对话框
        if (!checkMySelfHasSetWeChat()) {
            // 弹出对话框
            ChatWechatDialog dialog = new ChatWechatDialog(this, new ChatWechatDialog.IWechatClickListener() {
                @Override
                public void setWechat(String input) {
                    common.onDialogViewButtonClickCallback(bean, index);
                }
            });
            dialog.show();
            return true;
        }
        return false;
    }

    /**
     * @param exchange 1 交换电话 2 交换微信
     */
    private void sendExchangeGrayTextMessage(int exchange) {
        obtainFriendId(contact -> {
            ChatBean bean = null;
            if (exchange == 1) {
                bean = ExchangeMessage.createPhoneGray(contact);
            } else if (exchange == 2) {
                bean = ExchangeMessage.createWxGray(contact);
            }
            if (bean != null) {
                common.addData(bean);
                refreshShowData();
                common.scrollToBottom(false);
            } else {
                ToastUtils.showText("发送消息失败");
            }
        });

    }

    /**
     * 发送一条交换微信号消息
     */
    private void sendWechatNumberTextMessage(final boolean fromNLP) {
        obtainFriendId(contact -> {
            ChatBean bean = common.getChatSendCommon().sendExchangeWechatMessage(MessageTargetInfo.fromContactBean(contact), ContactBean.FROM_BOSS, new ChatSendCallback() {
                @Override
                public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                    refreshShowData();
                    noticeCleanNLPMessage(mNlpView.getCurrentNlpMsgId(), "4", fromNLP);
                }
            });
            if (bean != null) {
                common.addData(bean);
                refreshShowData();
                common.scrollToBottom(true, true);
            } else {
                ToastUtils.showText("发送消息失败");
            }
        });
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }


    /**
     * 右上解点击事件实现类
     */
    private class OnTitleMoreButtonClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {

            obtainFriendId(contactBean -> {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_WINDOW_MORE).secId(contactBean.securityId).param("p", String.valueOf(contactBean.friendId)).debug().build();
                SingleRouter.startSettingChatNew(ChatRoomActivity.this, contactBean, null, from);
            });

        }
    }

    /**
     * 代招且头像不真实，弹窗 false不弹
     */
    public void checkAgentAvatar() {
        CheckAgentAvatarRequest request = new CheckAgentAvatarRequest(new ApiRequestCallback<CheckAgentAvatarResponse>() {
            @Override
            public void onSuccess(ApiData<CheckAgentAvatarResponse> data) {
                if (data.resp.recruit) {// 代招且头像不真实，弹窗 false不弹
                    openImmediatelyUploadDialog();
                }
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * 打开立即上传弹窗
     */
    private void openImmediatelyUploadDialog() {
        UploadHeadDialog dialog = new UploadHeadDialog(this);
        dialog.setUploadDialogCallBack(new UploadHeadDialog.UploadDialogCallBack() {
            @Override
            public void onOk() {
                AnalyticsFactory.create()
                        .action(AnalyticsAction.ACTION_CHANGE_REALME_PICTURE_CLICK)
                        .param("p2", "1")
                        .build();
                BossPageRouter.jumpToBossEditInfoActivity(ChatRoomActivity.this, true, "2");
            }

            @Override
            public void onCancel() {

            }
        });
        dialog.shows();
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_CHANGE_REALME_PICTURE)
                .param("p2", "1")
                .build();
    }

    private int onAgreeExchange(int dialogType){
        return onAgreeExchange(dialogType, true);
    }

    private int onAgreeExchange(int dialogType, boolean fromNlp) {
        if (fromNlp) {
            dialogType = ExchangeMessage.mapNlpType2DialogType(dialogType);
        }

        // 先清除数据标记
        ExchangeHelper.getInstance().clearFlags();

        // 优先级 1305.114【BC】聊天页面--增加接受请求的快捷入口 > 1304.921【B&C】聊天交换错位问题解决
        if (onAgreeWaitReceiveExchange(dialogType) > 0) {
            return dialogType;
        } else if (onAgreeExchangeDirectly(dialogType) > 0) {
            return dialogType;
        } else {
            return ChatConstant.NONE;
        }
    }

    private int onAgreeWaitReceiveExchange(int dialogType) {
        if (ExchangeHelper.getInstance().isWaitReceiveStatus(dialogType, common.getContactBean())) {
            ChatBean targetChatBean = ExchangeHelper.getInstance().getTargetChatBean();
            if (targetChatBean != null) {
                ExchangeHelper.getInstance().setIgnoreConfirm(false);
                ExchangeHelper.getInstance().setSource(ExchangeHelper.Source.EXCHANGE_BUTTON);
                common.onDialogViewButtonClickCallback(targetChatBean, 1);
                return dialogType;
            }
        }

        return ChatConstant.NONE;
    }

    private int onAgreeExchangeDirectly(int dialogType) {
        if (!DataStarGray.getInstance().isOpenAgreeExchangeDirectly()) {
            return ChatConstant.NONE;
        }

        AgreeExchangeBean targetWxPhoneResumeDialogBean = ExchangeMessage.getTargetExchangeChatBean(common.getMessageData2(), dialogType);
        if (targetWxPhoneResumeDialogBean != null) {
            common.onDialogViewButtonClickCallback(targetWxPhoneResumeDialogBean.chatBean, 1);
            return dialogType;
        }

        return ChatConstant.NONE;
    }

    @Override
    public void onLocatePermissionGranted() {
        setNoBossLocationPermission(false);
    }

    private void setNoBossLocationPermission(boolean noPermission) {
        if (bottomFunctionView != null) {
            bottomFunctionView.setNoBossLocationPermission(noPermission);
        }
    }

    private void clearAIGeneratedText() {
        chooseAIGeneratedText = "";
    }

    /**
     * 1308.165【B&C】AI帮你推
     * 检查是否需要调用AI直聊推荐理由接口
     * @param aiDirectChatReasonInterval
     */
    private void requestAIDirectChatReasonDelay(long aiDirectChatReasonInterval) {
        if (aiDirectChatReasonInterval <= 0) return;
        ContactBean contactBean = common.getContactBean();
        // 非AI帮你推来源、非被动开聊、非双聊： 用户没有做出任何回应（没有回复/也没有同意or发起交换）
        if (contactBean == null || contactBean.aiDirectChat == 0 || contactBean.friendType != 3 || contactBean.isBatchChatBack()) {
            return;
        }
        if (AIDirectChatManager.isRecordContactResponse(contactBean)) return;
        Utils.runOnUiThreadDelayed(this::requestAIDirectChatReason, aiDirectChatReasonInterval * 1000);
    }

    volatile boolean isRequestDirectReason = false;
    /**
     * 请求AI直聊推荐理由
     */
    private void requestAIDirectChatReason() {

        ContactBean contactBean = common.getContactBean();
        if (contactBean == null || isRequestDirectReason) return;
        if (ActivityUtils.isInvalid(this)) {
            return;
        }

        AIDirectChatReasonRequest request = new AIDirectChatReasonRequest(new SimpleApiRequestCallback<AIDirectChatReasonResponse>() {
            @Override
            public void onSuccess(ApiData<AIDirectChatReasonResponse> data) {
                if (data != null && data.resp != null && !TextUtils.isEmpty(data.resp.reason)) {
                    showAIDirectChatReasonPopup(data.resp);
                }
                isRequestDirectReason = true;
            }
        });

        request.securityId = contactBean.securityId;
        request.execute();
    }


    /**
     * 显示AI直聊推荐理由弹窗
     * @param reasonResponse
     */
    @SuppressLint("BZL-PostDelay")
    private void showAIDirectChatReasonPopup(AIDirectChatReasonResponse reasonResponse) {
        if (ActivityUtils.isInvalid(this)  || reasonResponse == null || TextUtils.isEmpty(reasonResponse.reason)) {
            return;
        }
        AIDirectChatManager.recordContactResponse(common.getContactBean());
        TextView tvReasonPro = aiDirectReasonPopupView.findViewById(R.id.tv_reason_pro);
        TextView tvReason = aiDirectReasonPopupView.findViewById(R.id.tv_reason);
        tvReason.setText(reasonResponse.reason);
        aiDirectReasonPopupView.setVisibility(View.VISIBLE);
        //AI 沟通助手
        aiAssistantView.setVisibility(View.GONE);
        //AI 帮聊
        aiAssistChatView.setVisibility(View.GONE);
        aiDirectReasonPopupView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new ZPManager(ChatRoomActivity.this,reasonResponse.tipUrl).handler();
            }
        });
        // 5秒后自动关闭
        Utils.runOnUiThreadDelayed(() -> {
            if (ActivityUtils.isValid(this)) {
                aiDirectReasonPopupView.setVisibility(View.GONE);

                if (UserManager.isBossRole()) {
                    //AI 沟通助手
                    if (GrayStageManager.getInstance().getChatHelperActionStatus() > 0) {
                        aiAssistantView.setVisibility(View.VISIBLE);
                    }
                }
                //AI 帮聊
                setAiAssistChatView(common.getAiHelpChatType());
            }
        }, 5000);
    }

    //刷新页面
    private final Runnable refreshPageMessage = new Runnable() {
        @Override
        public void run() {
            common.reloadPageMessage();
        }
    };

    int lastConnectStatus = ConnectStatus.SUCCESS;

    @Override
    public void onMqttConnectStatusChanged(int status) {
        super.onMqttConnectStatusChanged(status);
        if (lastConnectStatus != ConnectStatus.SUCCESS && status == ConnectStatus.SUCCESS) { // 当前页面重新连接时，延时刷新页面，减少消息不可见的情况
            fixPageReloadMessage();
        }
        lastConnectStatus = status;
    }


    private void fixPageReloadMessage() {
        int delayTime = AndroidDataStarGray.getInstance().fixPageReloadMessage();
        if (delayTime > 0) {
            TLog.info(TAG, "fixPageReloadMessage reloadPage");
            Utils.runOnUiThreadDelayed(refreshPageMessage, 1000L * delayTime);
        }
    }

}
