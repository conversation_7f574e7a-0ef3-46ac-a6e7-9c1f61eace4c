package com.hpbr.bosszhipin.chat;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.chat.action.AccCallCardProtocolImp;
import com.hpbr.bosszhipin.chat.action.AddUserRejectProtocolImp;
import com.hpbr.bosszhipin.chat.action.BackToChatProtocolImp;
import com.hpbr.bosszhipin.chat.action.BgActionProtocolImp;
import com.hpbr.bosszhipin.chat.action.CloseSecondGreetingProtocolImp;
import com.hpbr.bosszhipin.chat.action.ConfigJobQuestionImp;
import com.hpbr.bosszhipin.chat.action.FastContactProtocolImp;
import com.hpbr.bosszhipin.chat.action.GeekDirectCallProtocolImp;
import com.hpbr.bosszhipin.chat.action.GeekFastReplyProtocolImp;
import com.hpbr.bosszhipin.chat.action.GeekHunterEvaluateTagProtocolImp;
import com.hpbr.bosszhipin.chat.action.GeekUserBlackOptProtocolImp;
import com.hpbr.bosszhipin.chat.action.HandleBlueCollarOneKeyApplyImp;
import com.hpbr.bosszhipin.chat.action.HandleCappedSwitchProtocolImp;
import com.hpbr.bosszhipin.chat.action.InviteFeedBackImp;
import com.hpbr.bosszhipin.chat.action.JobFreeBackProtocolImp;
import com.hpbr.bosszhipin.chat.action.JobQuestionAnswerImp;
import com.hpbr.bosszhipin.chat.action.JoinMediaInterviewProtocolImp;
import com.hpbr.bosszhipin.chat.action.MediaCallAction;
import com.hpbr.bosszhipin.chat.action.OpenChatPageProtocolImp;
import com.hpbr.bosszhipin.chat.action.OpenCommonWordsImp;
import com.hpbr.bosszhipin.chat.action.OpenFastReplyListImp;
import com.hpbr.bosszhipin.chat.action.OpenNewPeopleImp;
import com.hpbr.bosszhipin.chat.action.OpenPrivacySettingImp;
import com.hpbr.bosszhipin.chat.action.OpenVideoImp;
import com.hpbr.bosszhipin.chat.action.OpenWeiXinEditImp;
import com.hpbr.bosszhipin.chat.action.ReaSuranceFeedBackProtocolImp;
import com.hpbr.bosszhipin.chat.action.RecommendJobCardImp;
import com.hpbr.bosszhipin.chat.action.SwitchJobProtocolImp;
import com.hpbr.bosszhipin.chat.action.UserBothWayCallImp;
import com.hpbr.bosszhipin.chat.action.WxNoticeGuideImp;
import com.hpbr.bosszhipin.chat.dialog.AiDirectChatTipsDialog;
import com.hpbr.bosszhipin.chat.dialog.AutoReplyFeedBack;
import com.hpbr.bosszhipin.chat.dialog.ChatDailDialog;
import com.hpbr.bosszhipin.chat.dialog.ExplainMsgTipDialog;
import com.hpbr.bosszhipin.chat.dialog.JobQuestionIntroduceDialog;
import com.hpbr.bosszhipin.chat.dialog.RemarkUserDialog;
import com.hpbr.bosszhipin.chat.dialog.WatchHunterReasonDialog;
import com.hpbr.bosszhipin.chat.dialog.advantage.SelfAdvantageEditDialog;
import com.hpbr.bosszhipin.chat.dialog.hunter.HunterAskIntentProxy;
import com.hpbr.bosszhipin.chat.exchange.BatchExchangeWeChatPhoneManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangePhoneManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeRequireResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeSendResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeWeChatManager;
import com.hpbr.bosszhipin.chat.export.constant.NLPConstant;
import com.hpbr.bosszhipin.chat.export.hunter.HunterProxyParams;
import com.hpbr.bosszhipin.chat.export.router.AIChatRouter;
import com.hpbr.bosszhipin.chat.export.router.ChatRouter;
import com.hpbr.bosszhipin.chat.export.router.CustomerRouter;
import com.hpbr.bosszhipin.chat.export.router.NoticeRouter;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.chat.export.routerservice.IPlayReplyVideo;
import com.hpbr.bosszhipin.chat.protocol.ExchangeProtocolHandler;
import com.hpbr.bosszhipin.chat.single.dialog.AiChatHelperGuideDialog;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.CallVirtualPhoneDialog;
import com.hpbr.bosszhipin.common.dialog.ChatWechatDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.common.share.ShareMiniProgram;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactCache;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.announce.NoticeInfo;
import com.hpbr.bosszhipin.module.contacts.PriorityNotifyHttp;
import com.hpbr.bosszhipin.module.contacts.exchange.RejectHttpManager;
import com.hpbr.bosszhipin.module.contacts.exchange.UserRejectManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.contacts.util.ReportUtil;
import com.hpbr.bosszhipin.module.hunter.CIBusinessSource;
import com.hpbr.bosszhipin.module.hunter.CIClickSource;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.module.main.fragment.manager.InteractedChatManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.local.GeekHunter;
import com.hpbr.bosszhipin.module.my.activity.HeadHunterSettingsActivity;
import com.hpbr.bosszhipin.module.videointerview.VideoUtil;
import com.hpbr.bosszhipin.module_boss_export.BossConst;
import com.hpbr.bosszhipin.module_boss_export.BossUrlConfig;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.network.ChatMsgTipResponse;
import com.hpbr.bosszhipin.protocol.Protocol;
import com.hpbr.bosszhipin.protocol.ProtocolAction;
import com.hpbr.bosszhipin.protocol.ProtocolConstants;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.MapUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.monch.lbase.activity.LActivity;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterService;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.AgreeContactPhoneRequest;
import net.bosszhipin.api.ChatHelperModifyStatusResponse;
import net.bosszhipin.api.ChatQuickSettingInfoResponse;
import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.ExchangeCancelTestResponse;
import net.bosszhipin.api.ExchangeChatRequest;
import net.bosszhipin.api.GeekHunterResponse;
import net.bosszhipin.api.GeoValidateResponse;
import net.bosszhipin.api.ResultVirtualPhoneResponse;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UserSettingPhoneRequest;
import net.bosszhipin.api.UserSettingPhoneResponse;
import net.bosszhipin.api.bean.ExchangePhoneExtraBean;
import net.bosszhipin.api.bean.UserNotifySetting;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import message.handler.dao.MessageDaoFactory;


@RouterService(interfaces = ProtocolAction.class,
        singleton = true,
        key = {ProtocolConstants.Chat.TYPE_VIRTUAL_CALL,
                ProtocolConstants.Chat.TYPE_ACCEPT_VIDEO_RESUME,
                ProtocolConstants.Chat.TYPE_REJECT_VIDEO_RESUME,
                ProtocolConstants.Chat.TYPE_CONTACT_ASSIST_CALL,
                ProtocolConstants.Chat.TYPE_CONTACT_ASSIST_EXCHANGE,
                ProtocolConstants.Chat.TYPE_WECHAT_ASSIST_EXCHANGE,
                ProtocolConstants.Chat.TYPE_SEND_RESUME_BY_QUICK_TOP,
                ProtocolConstants.Chat.TYPE_EXCHANGE_PHONE_BY_QUICK_TOP,
                ProtocolConstants.Chat.TYPE_BOSS_FAST_REPLY,
                ProtocolConstants.Chat.TYPE_SEND_FAST_REPLY_CARD,
                ProtocolConstants.Chat.TYPE_MATE_SHARE_LIST,
                ProtocolConstants.Chat.TYPE_WX_BINDING_SETTING,
                ProtocolConstants.Chat.TYPE_HEAD_HUNTER_CHAT_SETTING,
                ProtocolConstants.Chat.TYPE_OPEN_CHAT_SETTING,
                ProtocolConstants.Chat.TYPE_TRANSMIT_HISTORY,
                ProtocolConstants.Chat.TYPE_OPEN_CHAT_REMARK,
                ProtocolConstants.Chat.TYPE_DIAL_2_FRIEND,
                ProtocolConstants.Chat.TYPE_PHONE_CALL_ITEM,
                ProtocolConstants.Chat.TYPE_OEPN_WEIXIN_EDIT,
                ProtocolConstants.Chat.TYPE_OEPN_QUESTION_DESC,
                ProtocolConstants.Chat.TYPE_CLOSE_GEEK_SELF_ANSWER,
                ProtocolConstants.Chat.TYPE_OPEN_NOTIFY_INTEREST,
                ProtocolConstants.Chat.TYPE_OPEN_VIEWED,
                ProtocolConstants.Chat.TYPE_OPEN_NEW_PEOPLE,
                ProtocolConstants.Chat.TYPE_OPEN_COMMON_WORDS,
                ProtocolConstants.Chat.TYPE_OPEN_CHAT_PAGE,
                ProtocolConstants.Chat.TYPE_CALL_VIDEO,
                ProtocolConstants.Chat.TYPE_ADD_USER_REJECT,
                ProtocolConstants.Chat.TYPE_INVITE_FEEDBACK,
                ProtocolConstants.Chat.TYPE_VIDEO_FAST_REPLAY,
                ProtocolConstants.Chat.TYPE_PLAY_FAST_REPLY_VIDEO,
                ProtocolConstants.Chat.TYPE_CLOSE_SECOND_GREETING,
                ProtocolConstants.Chat.TYPE_VIDEO_RESUME,
                ProtocolConstants.Chat.TYPE_CHAT_SWITCH_JOB,
                ProtocolConstants.Chat.TYPE_JOIN_MEDIA_INTERVIEW,
                ProtocolConstants.Chat.TYPE_FILTER_MESSAGE,
                ProtocolConstants.Chat.TYPE_FAST_CONTACT,
                ProtocolConstants.Chat.TYPE_NEW_EXPECT_WORK_LOCATION,
                ProtocolConstants.Chat.TYPE_TODAY_MATCH,
                ProtocolConstants.Chat.TYPE_GEEK_DIRECT_CALL,
                ProtocolConstants.Chat.TYPE_GEEK_HUNTER_EVALUATE_TAG,
                ProtocolConstants.Chat.TYPE_REMIND_PRIORITY_GEEK,
                ProtocolConstants.Chat.TYPE_JOB_FREE_BACK,
                ProtocolConstants.Chat.TYPE_HANDICAPPED_SWITCH,
                ProtocolConstants.Chat.TYPE_OPEN_BLACK_LIST,
                ProtocolConstants.Chat.TYPE_BG_ACTION,
                ProtocolConstants.Chat.TYPE_BG_ACTION_ONLY,
                ProtocolConstants.Chat.TYPE_GEEK_USER_BLACK_OPT,
                ProtocolConstants.Chat.TYPE_REASSURANCE_FEED_BACK,
                ProtocolConstants.Chat.TYPE_GEEK_FAST_REPLY,
                ProtocolConstants.Chat.TYPE_INTENTION_BOSS_CHAT_CARD,
                ProtocolConstants.Chat.TYPE_OPEN_FAST_REPLY_LIST,
                ProtocolConstants.Chat.TYPE_GEEK_WX_NOTICE_GUIDE,
                ProtocolConstants.Chat.TYPE_GET_ALL_HISTORY_EXCHANGE,
                ProtocolConstants.Chat.TYPE_BASE_INFO_GEEK,
                ProtocolConstants.Chat.TYPE_USER_BOTH_WAY_CALL,
                ProtocolConstants.Chat.TYPE_DIAL_PHONE_NUMBER,
                ProtocolConstants.Chat.TYPE_JOB_QUES_ANSWER_LIST,
                ProtocolConstants.Chat.TYPE_JOB_QUES_ANSWER,
                ProtocolConstants.Chat.TYPE_CONFIG_JOB_QUESTION,
                ProtocolConstants.Chat.TYPE_GET_GEEK_ANSWERS,
                ProtocolConstants.Chat.TYPE_RECOMMEND_JOB_CARD,
                ProtocolConstants.Chat.TYPE_HANDLE_BLUE_COLLAR_ONE_KEY_APPLY,
                ProtocolConstants.Chat.TYPE_OPEN_PRIVACY_SETTINGS,
                ProtocolConstants.Chat.TYPE_OPEN_VIDEO,
                ProtocolConstants.Chat.TYPE_ACC_CALL_CARD,
                ProtocolConstants.Chat.TYPE_OPEN_MINIPROGRAM,
                ProtocolConstants.Chat.TYPE_GEEK_GPT,
                ProtocolConstants.Chat.TYPE_UNFIT,
                ProtocolConstants.Chat.TYPE_BACK_TO_CHAT,
                ProtocolConstants.Chat.TYPE_OPEN_FAST_GEEK_MSG_SETTING_PAGE,
                ProtocolConstants.Chat.TYPE_VALIATE_BOSS_GEO,
                ProtocolConstants.Chat.TYPE_TRANSFER_CUSTOMER,
                ProtocolConstants.Chat.TYPE_OPEN_NOTICE_PAGE,
                ProtocolConstants.Chat.TYPE_CHAT_HELPER_EXPLAIN_PROVIDER,
                ProtocolConstants.Chat.TYPE_MODIFY_CHAT_HELPER_STATUS,
                ProtocolConstants.Chat.TYPE_CHAT_HELPER_RESTART,
                ProtocolConstants.Chat.TYPE_SELF_ADVANTAGE_EDIT,
                ProtocolConstants.Chat.TYPE_GEEK_SCORE_SHARE,
                ProtocolConstants.Chat.TYPE_ZP_SCORE_SETTING,
                ProtocolConstants.Chat.TYPE_FAST_CONTACT_ACCEPT,
                ProtocolConstants.Chat.TYPE_CHAT_ROBOT_SETTING,
                ProtocolConstants.Chat.TYPE_CHAT_CHAT_ROBOT_EXPERIENCE,
                ProtocolConstants.Chat.TYPE_CHECK_GEEK_HUNTER,
                ProtocolConstants.Chat.TYPE_GET_BOSS_VIDEO_JOB_VIRTUAL_PHONE,
                ProtocolConstants.Chat.TYPE_GET_GEEK_VIDEO_JOB_VIRTUAL_PHONE,
                ProtocolConstants.Chat.TYPE_OPEN_NOTICE,
                ProtocolConstants.Chat.TYPE_CALL_MEDIA_INTERVIEW,
                ProtocolConstants.Chat.TYPE_GEEK_ONE_KEY_DELIVER,
                ProtocolConstants.Chat.TYPE_SEND_TEXT_MESSAGE,
                ProtocolConstants.Chat.TYPE_VERBAL_INTERVIEW,
                ProtocolConstants.Chat.TYPE_GUAN_JIA_CONTINUE_SERVICE,
                ProtocolConstants.Chat.TYPE_UPDATE_USER_NOTIFY_SETTING,
                ProtocolConstants.Chat.TYPE_OPEN_NO_INTEREST_SETTING,
                ProtocolConstants.Chat.TYPE_OPEN_MSG_TIP,
                ProtocolConstants.Chat.TYPE_SEND_TEXT_AND_EXCHANGE,
                ProtocolConstants.Chat.TYPE_OPEN_GEEK_CHAT_QUICK_LIST,
                ProtocolConstants.Chat.TYPE_SHOW_ADD_WIDGET_DIALOG,
                ProtocolConstants.Chat.TYPE_CLICK_REQUEST_EXCHANGE,
                ProtocolConstants.Chat.TYPE_CLICK_REQUEST_EXCHANGE_CANCEL,
                ProtocolConstants.Chat.TYPE_CHAT_HELPER_QUICK_SETTING,
                ProtocolConstants.Chat.TYPE_FRIEND_CLEAN,
                ProtocolConstants.Chat.TYPE_AI_OFFER_COMPANION,
                ProtocolConstants.Chat.TYPE_AI_DIRECT_EXPLAIN,
                ProtocolConstants.Chat.TYPE_CHAT_EXCHANGE,
                ProtocolConstants.Chat.TYPE_SEND_LIGHT_RESUME
        })
public class ChatProtocolAction extends ProtocolAction {
    private static final String TAG = "ChatProtocolAction";

    @Protocol(ProtocolConstants.Chat.TYPE_VIRTUAL_CALL)
    void handlerVirtualCall(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerVirtualCall(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_ACCEPT_VIDEO_RESUME)
    void handlerAcceptVideoResume(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerAcceptVideoResume(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_REJECT_VIDEO_RESUME)
    void handlerRejectVideoResume(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerRejectVideoResume(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CONTACT_ASSIST_CALL)
    void handlerContactAssistCall(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerContactAssistCall(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CONTACT_ASSIST_EXCHANGE)
    void handlerContactAssistExchange(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerContactAssistExchange(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_WECHAT_ASSIST_EXCHANGE)
    void handlerWechatAssistCall(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.handlerWechatAssist(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_SEND_RESUME_BY_QUICK_TOP)
    void sendResumeByQuickTop(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.sendResumeByQuickTop(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_EXCHANGE_PHONE_BY_QUICK_TOP)
    void exchangePhoneByQuickTop(@NonNull Context context, @NonNull Map<String, String> params) {
        ExchangeProtocolHandler.exchangePhoneByQuickTop(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_BOSS_FAST_REPLY)
    void bossFastReply(@NonNull Context context, @NonNull Map<String, String> params) {
        String replyType = params.get("replyType");
        String securityId = params.get("securityId");
        String msgId = params.get("msgId");
        long friendId = LText.getLong(params.get("friendId"));
        int friendSource = LText.getInt(params.get("friendSource"));
        //放行沟通钥匙检查, 0 不放行 / 1 放行
        int releaseBlock = LText.getInt(params.get("releaseBlock"));

        //需要检测沟通钥匙
        if (releaseBlock == 0) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), friendSource);
            if (contactBean == null) return;
            //判断沟通钥匙
            ContactKeyManager.getInstance().onCheckListener(contactBean,
                    new ContactKeyManager.OnContactKeyCheckCallBack2() {
                        @Override
                        public void onCheckNoBlockListener() {

                            bossFastReplyRequest(securityId, msgId, replyType);

                        }
                    }, ContactKeyManager.BgSource.BOSS_RECRUIT_SOURCE);
        } else {
            //不需要检测沟通钥匙
            bossFastReplyRequest(securityId, msgId, replyType);
        }


    }

    static void bossFastReplyRequest(String securityId, String msgId, String replyType) {
        SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_ZPCHAT_FAST_REPLY_SNED_REPLY_MSG);
        get.setRequestCallback(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        get.addParam("securityId", securityId);
        get.addParam("msgId", msgId);
        get.addParam("replyType", replyType);
        HttpExecutor.execute(get);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_SEND_FAST_REPLY_CARD)
    void sendFastReplyCard(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = params.get("securityId");

        SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_ZPCHAT_FAST_REPLY_SNED_REPLY_CARD);
        get.setRequestCallback(new ApiRequestCallback<HttpResponse>() {
            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        get.addParam("securityId", securityId);
        HttpExecutor.execute(get);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_MATE_SHARE_LIST)
    void openMateRecommendPage(@NonNull Context context, @NonNull Map<String, String> params) {
        SingleRouter.startColleagueShareGeekList(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_WX_BINDING_SETTING)
    void onWxBindSettingListener(@NonNull Context context, @NonNull Map<String, String> params) {
        SettingRouter.jumpToAccountAndBindingActivity(context, SettingRouter.WxBindingSettingParams.obj().setExType(LText.getInt(MapUtils.get(params, "exType"))).setExtraInfo(MapUtils.get(params, "extraInfo")));
    }


    @Protocol(ProtocolConstants.Chat.TYPE_HEAD_HUNTER_CHAT_SETTING)
    void onHunterChatSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        // 猎头服务设置页面
        Intent intent = new Intent(context, HeadHunterSettingsActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        AppUtil.startActivity(context, intent);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_CHAT_SETTING)
    void onChatSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        String friendId = params.get("friendId");
        String from = params.get("from");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
        if (contactBean != null) {
            if (UserGrayFeatureManager.getInstance().isNewChatRoom()) {
                SingleRouter.startSettingChatNew(context, contactBean, from, "");
            } else {
                SingleRouter.startSettingChat(context, contactBean, from, "");
            }
        }

    }

    @Protocol(ProtocolConstants.Chat.TYPE_TRANSMIT_HISTORY)
    void onOpenChatHistory(@NonNull Context context, @NonNull Map<String, String> params) {

        long shareId = 0;
        long fromId = 0;
        long userId = 0;
        String userName = "";
        try {
            userName = params.get("naviTitle");
            shareId = Long.parseLong(params.get("shareId"));
            fromId = Long.parseLong(params.get("bossId"));
            userId = Long.parseLong(params.get("geekId"));
        } catch (Exception e) {
            TLog.error("TYPE_TRANSMIT_HISTORY", "TYPE_TRANSMIT_HISTORY = 参数错误");
        }

        SingleRouter.startChatMate(context, userName, userId, shareId, fromId, 0);

    }


    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_CHAT_REMARK)
    void onOpenChatRemark(@NonNull Context context, @NonNull Map<String, String> params) {
        String friendId = params.get("friendId");
        int source = LText.getInt(params.get("source"));
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);

        if (UserManager.isBossRole()) {
            RemarkUserDialog d = new RemarkUserDialog((Activity) context, contactBean);
            d.setSource(source);
            d.prepareAndShow(null);
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_DIAL_2_FRIEND)
    void dail2Friend(@NonNull Context context, @NonNull Map<String, String> params) {
        final String friendId = MapUtils.get(params, "friendId");
        final String friendSource = MapUtils.get(params, "friendSource");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean == null || LText.empty(contactBean.friendPhone)) return;
        //拨打电话
        StringUtil.dial(context, contactBean.friendPhone);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_PHONE_CALL_ITEM)
    void onPhoneCallListener(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = MapUtils.get(params, "securityId");
        UserSettingPhoneRequest request = new UserSettingPhoneRequest(new ApiRequestCallback<UserSettingPhoneResponse>() {
            @Override
            public void onSuccess(ApiData<UserSettingPhoneResponse> data) {
                UserSettingPhoneResponse resp = data.resp;
                //虚拟电话开关标记  1-已开启  2-已关闭
                int phoneFlag = resp.phoneFlag;
                String phoneNum = resp.phoneNum;
                if (phoneFlag == 1) {
                    startTelCall(context, phoneNum);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OEPN_QUESTION_DESC)
    void onOpenQuestionDesc(@NonNull Context context, @NonNull Map<String, String> params) {
        /*1 引导牛人回答卡片
         2 引导boss设置卡片
         3 通知boss牛人回答卡片*/
        String source = params.get("source");
        String msgId = params.get("msgId");
        JobQuestionIntroduceDialog introduceDialog = new JobQuestionIntroduceDialog(msgId, source);
        introduceDialog.show((Activity) context);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CLOSE_GEEK_SELF_ANSWER)
    void closeGeekSelfAnswer(@NonNull Context context, @NonNull Map<String, String> params) {
        SimpleApiRequest.GET(ChatUrlConfig.URL_ZPCHAT_GREETING_JOB_CLOSE_GEEK_SELF_ANSWER).execute();
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_NOTIFY_INTEREST)
    void onOpenNotifyInterest(@NonNull Context context, @NonNull Map<String, String> params) {
        //更新UID
        String uId = MapUtils.get(params, "anchor");
        String extParams = MapUtils.get(params, "extParams");
        if (UserManager.isBossRole()) {
            InteractedChatManager.getInstance().putInteractPushTopId(BossConst.TYPE_INTEREST, uId);
            InteractedChatManager.getInstance().putInteractPushExtend(BossConst.TYPE_INTEREST, extParams);
            startMain(context, 2, 1, BossConst.TYPE_INTEREST);
        }
        if (UserManager.isGeekRole()) {
            InteractedChatManager.getInstance().putInteractPushTopId(GeekPageRouter.TYPE_INTEREST, uId);
            InteractedChatManager.getInstance().putInteractPushExtend(GeekPageRouter.TYPE_INTEREST, extParams);
            startMain(context, 2, 1, GeekPageRouter.TYPE_INTEREST);
        }

    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_VIEWED)
    void onOpenViewedMe(@NonNull Context context, @NonNull Map<String, String> params) {
        //更新UID
        String uId = MapUtils.get(params, "anchor");
        String extParams = MapUtils.get(params, "extParams");
        if (UserManager.isGeekRole()) {
            InteractedChatManager.getInstance().putInteractPushTopId(GeekPageRouter.TYPE_VIEWED, uId);
            InteractedChatManager.getInstance().putInteractPushExtend(GeekPageRouter.TYPE_VIEWED, extParams);
            startMain(context, 2, 1, GeekPageRouter.TYPE_VIEWED);
        }
        if (UserManager.isBossRole()) {
            InteractedChatManager.getInstance().putInteractPushTopId(BossConst.TYPE_VIEWED, uId);
            InteractedChatManager.getInstance().putInteractPushExtend(BossConst.TYPE_VIEWED, extParams);
            startMain(context, 2, 1, BossConst.TYPE_VIEWED);
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_COMMON_WORDS)
    void onOpenCommonWords(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenCommonWordsImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_NEW_PEOPLE)
    void onOpenNewPeople(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenNewPeopleImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OEPN_WEIXIN_EDIT)
    void onOpenWeiXinEdit(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenWeiXinEditImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_CHAT_PAGE)
    void openChatPage(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenChatPageProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_MINIPROGRAM)
    void openMiniProgram(@NonNull Context context, @NonNull Map<String, String> params) {
        String userName = params.get("name");
        String path = params.get("path");
        //1019版本 安星保需求
        try {
            if (params.containsKey("allowContactMe")) {
                String decodePath = URLDecoder.decode(path);
                String newPath = ZPManager.UrlHandler.addParams(decodePath,
                        "allowContactMe", params.get("allowContactMe"));
                path = URLEncoder.encode(newPath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Activity activity = ForegroundUtils.get().getTopActivity();
        if (activity != null && LText.notEmpty(userName) && LText.notEmpty(path)) {
            ShareMiniProgram.openMiniProgram(activity, userName, path, true, true);
        }
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CALL_VIDEO)
    void onCallVideo(@NonNull Context context, @NonNull Map<String, String> params) {
        VideoUtil videoUtil = new VideoUtil(context);
        videoUtil.setFriendId(LText.getLong(MapUtils.get(params, "userId")));
        videoUtil.startCalling("1","2");
    }


    @Protocol(ProtocolConstants.Chat.TYPE_FILTER_MESSAGE)
    void onFilterMessage(@NonNull Context context, @NonNull Map<String, String> params) {
        if (UserManager.isGeekRole()) {
            String source = MapUtils.get(params, "source");
            SingleRouter.startInconformityChat(context, source);
        }
    }


    @Protocol(ProtocolConstants.Chat.TYPE_JOIN_MEDIA_INTERVIEW)
    void onJoinMediaInterview(@NonNull Context context, @NonNull Map<String, String> params) {
        new JoinMediaInterviewProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_SWITCH_JOB)
    void onChatSwitchJob(@NonNull Context context, @NonNull Map<String, String> params) {
        new SwitchJobProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_VIDEO_RESUME)
    void onVideoResume(@NonNull Context context, @NonNull Map<String, String> params) {
        final String playParam = MapUtils.get(params, "playParam");
        String messageId = MapUtils.get(params, "mid");
        String geekId = MapUtils.get(params, "geekId");
        String from = MapUtils.get(params, "from");
        int source = LText.getInt(from, GeekConsts.From.FROM_CHAT);
        GeekPageRouter.jumpToVideoResumePlayByResumeId(context, playParam, messageId, source, geekId);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CLOSE_SECOND_GREETING)
    void onCloseSecondGreeting(@NonNull Context context, @NonNull Map<String, String> params) {
        new CloseSecondGreetingProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_PLAY_FAST_REPLY_VIDEO)
    void onPlayfastReplyVideo(@NonNull Context context, @NonNull Map<String, String> params) {
        //跳转到播放是否
        String encryptId = params.get("encryptId");
        String securityId = params.get("securityId");
        long messageId = LText.getLong(params.get("messageId"));
        long friendId = LText.getLong(params.get("friendId"));
        int friendSource = LText.getInt(params.get("friendSource"));

        IPlayReplyVideo replyVideo = Router.getService(IPlayReplyVideo.class, NLPConstant.CHAT_MODULE_NLP_PLAY_REPLY_VIDEO);
        if (replyVideo != null) {
            replyVideo.playVideo(encryptId, securityId, messageId, friendId, friendSource);
        }
    }


    @Protocol(ProtocolConstants.Chat.TYPE_VIDEO_FAST_REPLAY)
    void onVideofastReply(@NonNull Context context, @NonNull Map<String, String> params) {
        //跳转到 视频招呼列表页面
        GeekPageRouter.openGreetingTemplate(context, GeekPageRouter.GreetingVideoFrom.fromF3Mine);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_ADD_USER_REJECT)
    void onAddUserReject(@NonNull Context context, @NonNull Map<String, String> params) {
        new AddUserRejectProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_INVITE_FEEDBACK)
    void onInviteFeedBack(@NonNull Context context, @NonNull Map<String, String> params) {
        new InviteFeedBackImp().onProtocolExecute(context, params);
    }


    void startTelCall(Context context, String phone) {
        if (TextUtils.isEmpty(phone)) return;

        Uri mobileUri = Uri.parse("tel:" + phone);
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_DIAL);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(mobileUri);
        try {
            AppUtil.startActivity(context, intent);
        } catch (Exception ignored) {
        }
    }

    @Override
    protected void handleProtocolAccept(Context context, Map<String, String> params) {
        ApmAnalyzer.create().action("action_temp", "chatProtocol").p2(GsonUtils.toJson(params)).reportNow();
    }


    public static void startMain(Context context, int level1Tab, int level2Tab, int level3Tab) {
        startMain(context, level1Tab, level2Tab, level3Tab, 0, 0, 0, 0);
    }

    static void startMain(Context context, int level1Tab, int level2Tab, int level3Tab, long jobId, long expectId, int f1SortType, int source) {
        Intent intent = new Intent();
        intent.putExtra(Constants.DATA_INT, level1Tab);
        intent.putExtra(Constants.DATA_INT_2, level2Tab);
        intent.putExtra(Constants.DATA_INT_3, level3Tab);
        intent.putExtra(Constants.DATA_JOB_ID, jobId);
        intent.putExtra(Constants.DATA_JOB_EXPECT_ID, expectId);
        intent.putExtra(Constants.DATA_F1_SUB_TAB, f1SortType);
        intent.putExtra(Constants.DATA_F1_NEAR_SOURCE, source);
        intent.setAction(Constants.RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        if (null != context) {
            context.sendBroadcast(intent);
        }
        AppUtil.startActivity(context, new Intent(context, MainActivity.class), ActivityAnimType.NONE);
        if (level1Tab == 2) {
            int p2 = 0;
            if (level2Tab == 0) { //联系人
                p2 = 1;
            } else {//互动
                if (level3Tab == 0) {//感兴趣
                    p2 = 2;
                } else if (level3Tab == 1) {//看过我
                    p2 = 3;
                } else if (level3Tab == 2) {//新牛人
                    p2 = 4;
                }
            }
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_PROTLCOL_RETURN).param("p2", String.valueOf(p2)).buildSync();
        }
    }

    public static void startChatView(Context context, int role,
                                     long uid, long jobId,
                                     long exceptId, String from, String lid,
                                     String securityId, String inputText, boolean fromDz) {
        if (!UserManager.isCurrentLoginStatus()) return;
        if (UserManager.getUserRole().get() != role) {
            if (role == ROLE.BOSS.get()) {
                ToastUtils.showText("请切换到Boss身份");
            } else {
                ToastUtils.showText("请切换到牛人身份");
            }
            return;
        }

        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setFriendId(uid);
        singleChatParam.setJobId(jobId);
        singleChatParam.setExpectId(exceptId);
        singleChatParam.setSecurityId(securityId);
        singleChatParam.setFrom(from);
        singleChatParam.setLid(lid);
        singleChatParam.setInputText(inputText);
        singleChatParam.setFromDZUser(fromDz);
        SingleRouter.startChat(context, singleChatParam);

        ReportUtil.reportNoJobId(jobId, ZPManager.class.getSimpleName(), "startChatView");
    }

    @Protocol(ProtocolConstants.Chat.TYPE_FAST_CONTACT)
    void onFastContact(@NonNull Context context, @NonNull Map<String, String> params) {
        new FastContactProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_NEW_EXPECT_WORK_LOCATION)
    void onNewExpectWorkLocation(@NonNull Context context, @NonNull Map<String, String> params) {
        final String cityCode = MapUtils.get(params, "cityCode");
        final String source = MapUtils.get(params, "source");
        GeekPageRouter.openGeekSelectExpectLocation(context, cityCode, source);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_TODAY_MATCH)
    void onTodayMatch(@NonNull Context context, @NonNull Map<String, String> params) {
        //打开今日速配
        String axbJid = MapUtils.get(params, "axbJid");
        GeekPageRouter.jumpToDailyRecommendActivity(context, axbJid);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_DIRECT_CALL)
    void onGeekDirectCall(@NonNull Context context, @NonNull Map<String, String> params) {
        new GeekDirectCallProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_HUNTER_EVALUATE_TAG)
    void onGeekHunterEvaluateTag(@NonNull Context context, @NonNull Map<String, String> params) {
        new GeekHunterEvaluateTagProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_REMIND_PRIORITY_GEEK)
    void onRemindPriorityGeek(@NonNull Context context, @NonNull Map<String, String> params) {
        String param = MapUtils.get(params, "param");
        if (param != null) {
            String friendId = MapUtils.get(params, "friendId");
            String frinedSource = MapUtils.get(params, "frinedSource");
            new PriorityNotifyHttp(context).onCLickZpUrl(param, friendId, frinedSource);
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_JOB_FREE_BACK)
    void onJobFreeBack(@NonNull Context context, @NonNull Map<String, String> params) {
        new JobFreeBackProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_HANDICAPPED_SWITCH)
    void onHandicappedSwitch(@NonNull Context context, @NonNull Map<String, String> params) {
        new HandleCappedSwitchProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_BLACK_LIST)
    void onOpenBlackList(@NonNull Context context, @NonNull Map<String, String> params) {
        SettingRouter.jumpToBlackContactListActivity(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_BG_ACTION)
    void onBgAction(@NonNull Context context, @NonNull Map<String, String> params) {
        new BgActionProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_BG_ACTION_ONLY)
    void onBgActionOnly(@NonNull Context context, @NonNull Map<String, String> params) {
        // 仅打点，不处理任何逻辑
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_USER_BLACK_OPT)
    void onGeekUserBlackOpt(@NonNull Context context, @NonNull Map<String, String> params) {
        new GeekUserBlackOptProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_REASSURANCE_FEED_BACK)
    void onReaSuranceFeedBack(@NonNull Context context, @NonNull Map<String, String> params) {
        new ReaSuranceFeedBackProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_FAST_REPLY)
    void onGeekFastReply(@NonNull Context context, @NonNull Map<String, String> params) {
        new GeekFastReplyProtocolImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_INTENTION_BOSS_CHAT_CARD)
    void onIntentionBossChatCard(@NonNull Context context, @NonNull Map<String, String> params) {

        String friendId = MapUtils.get(params, "friendId");
        String friendSource = MapUtils.get(params, "friendSource");

        String hasRedPoint = MapUtils.get(params, "hasRedPoint");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean != null) {
            String jobId = String.valueOf(contactBean.jobId);
            String securityId = contactBean.securityId;
            // 1221.500【意向沟通】APP在F1的意向沟通引流入口优化：规范意向沟通使用方法入参
            try {
                HunterProxyParams proxyParams = HunterProxyParams.obj(LText.getLong(friendId), jobId, securityId)
                        .setBusinessSource(CIBusinessSource.CHAT)
                        .setClickSource(CIClickSource.CHAT_CARD)
                        .setHasRedPoint(hasRedPoint);
                HunterAskIntentProxy.checkAndShow(proxyParams);
            } catch (Exception e) {
                TLog.error("onIntentionBossChatCard", e.getMessage());
            }
        }

    }


    @Protocol(ProtocolConstants.Chat.TYPE_RECOMMEND_JOB_CARD)
    void recommendJobCard(@NonNull Context context, @NonNull Map<String, String> params) {
        new RecommendJobCardImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_HANDLE_BLUE_COLLAR_ONE_KEY_APPLY)
    void handleBlueCollarOneKeyApply(@NonNull Context context, @NonNull Map<String, String> params) {
        new HandleBlueCollarOneKeyApplyImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_PRIVACY_SETTINGS)
    void openPrivacySetting(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenPrivacySettingImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_VIDEO)
    void openVideo(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenVideoImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_FAST_REPLY_LIST)
    void onOpenFastReplyList(@NonNull Context context, @NonNull Map<String, String> params) {
        new OpenFastReplyListImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_WX_NOTICE_GUIDE)
    void geekWxNoticeGuide(@NonNull Context context, @NonNull Map<String, String> params) {
        new WxNoticeGuideImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_GET_ALL_HISTORY_EXCHANGE)
    void getAllHistoryExchange(@NonNull Context context, @NonNull Map<String, String> params) {
        GeekPageRouter.startPrivacy(context);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_BASE_INFO_GEEK)
    void baseInfoGeek(@NonNull Context context, @NonNull Map<String, String> params) {
        GeekPageRouter.jumpGeekEditInfoActivity(context);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_DIAL_PHONE_NUMBER)
    void dialPhoneNumber(@NonNull Context context, @NonNull Map<String, String> params) {
        final String phoneNumber = MapUtils.get(params, "phone");
        if (!LText.empty(phoneNumber)) {
            StringUtil.dial(context, phoneNumber);
        }
    }


    @Protocol(ProtocolConstants.Chat.TYPE_JOB_QUES_ANSWER_LIST)
    void jobQuesAnswerList(@NonNull Context context, @NonNull Map<String, String> params) {
        //牛人点击"我的回答"跳转 "或" 牛人回答更多问题
        String source = MapUtils.get(params, "source");
        SingleRouter.jumpGeekAnswerList(context, source);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_JOB_QUES_ANSWER)
    void jobQuesAnswer(@NonNull Context context, @NonNull Map<String, String> params) {
        new JobQuestionAnswerImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_GET_GEEK_ANSWERS)
    void getGeekAnswers(@NonNull Context context, @NonNull Map<String, String> params) {
        //boss点击灰条消息的"去看看"
        String securityId = MapUtils.get(params, "securityId");
        SingleRouter.jumpBossWatchGeekAnswerList(context, securityId);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CONFIG_JOB_QUESTION)
    void configJobQuestion(@NonNull Context context, @NonNull Map<String, String> params) {
        new ConfigJobQuestionImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_USER_BOTH_WAY_CALL)
    void userBothWayCall(@NonNull Context context, @NonNull Map<String, String> params) {
        new UserBothWayCallImp().onProtocolExecute(context, params);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_ACC_CALL_CARD)
    void accCallCard(@NonNull Context context, @NonNull Map<String, String> params) {
        new AccCallCardProtocolImp().onProtocolExecute(context, params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_GPT)
    void openGeekGptPage(@NonNull Context context, @NonNull Map<String, String> params) {
        SingleRouter.jumpGeekGpt(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_UNFIT)
    void openUnfitPage(@NonNull Context context, @NonNull Map<String, String> params) {
        SingleRouter.startUnfitChat(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_BACK_TO_CHAT)
    void backToChat(@NonNull Context context, @NonNull Map<String, String> params) {
        new BackToChatProtocolImp().onProtocolExecute(context, params);
    }

    @Deprecated
    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_FAST_GEEK_MSG_SETTING_PAGE)
    void openGeekMsgSettingPage(@NonNull Context context, @NonNull Map<String, String> params) {
        SingleRouter.startAppletSettingChat(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_VALIATE_BOSS_GEO)
    void valiateBossGeo(@NonNull Context context, @NonNull Map<String, String> params) {
        FragmentActivity fragmentActivity = null;
        if (context instanceof FragmentActivity) {
            fragmentActivity = (FragmentActivity) context;
        } else {
            context = ForegroundUtils.get().getTopActivity();
            if (context instanceof FragmentActivity) {
                fragmentActivity = (FragmentActivity) context;
            }
        }
        if (fragmentActivity == null) {
            return;
        }
        FragmentActivity activity = fragmentActivity;
        PermissionHelper
                .getLocationHelper(activity, LocationPermissionHelper.BOSS_CHAT,
                        new PermissionHelper.ExtendBean().setAnalyticName(PermissionConstants.SCENE_CHAT))
                .sync(true)
                .setPermissionCallback(new PermissionCallback<LocationPermissionHelper.LocationPermissionData>() {
                    @Override
                    public void onResult(boolean yes, LocationPermissionHelper.LocationPermissionData permissionData) {
                        if (permissionData != null && permissionData.locationBean != null) {
                            SimpleApiRequest.POST(ChatUrlConfig.URL_CHAT_GEO_VALIDATE)
                                    .addParam("securityId", params.get("securityId"))
                                    .addNeedLocInfo(true)
                                    .setRequestCallback(new SimpleCommonApiRequestCallback<GeoValidateResponse>() {
                                        @Override
                                        public void onSuccess(ApiData<GeoValidateResponse> data) {
                                            new DialogUtils.Builder(activity)
                                                    .setSingleButton()
                                                    .setTitle(data.resp.tipText)
                                                    .setDesc(data.resp.tipRemark)
                                                    .setTitleIsNullGone(true)
                                                    .setPositiveAction("我知道了")
                                                    .build()
                                                    .show();
                                        }
                                    }).execute();
                        } else {
                            new DialogUtils.Builder(activity)
                                    .setSingleButton()
                                    .setTitle("定位服务未开启")
                                    .setDesc("请进入系统【设置】>【隐私】>【定位服务】中打开开关，并允许BOSS直聘使用定位服务")
                                    .setTitleIsNullGone(true)
                                    .setPositiveAction("我知道了")
                                    .build()
                                    .show();
                        }

                    }
                }).requestPermission();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_TRANSFER_CUSTOMER)
    void sendTransferStaffServiceBroadcast(@NonNull Context context, @NonNull Map<String, String> params) {
        CustomerRouter.sendTransferStaffServiceBroadcast(App.getAppContext());
    }

    @Protocol({ProtocolConstants.Chat.TYPE_OPEN_NOTICE_PAGE, ProtocolConstants.Chat.TYPE_OPEN_NOTICE})
    void startNotice(@NonNull Context context, @NonNull Map<String, String> params) {
        if (!UserManager.isCurrentLoginStatus()) return;
        long noticeId = LText.getTrimLong(MapUtils.get(params, "noticeId"), NoticeInfo.All_NOTICE.id);
        NoticeRouter.startNotice(context, noticeId, -1);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_HELPER_EXPLAIN_PROVIDER)
    void chatHelperExplainProvider(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = params.get("securityId");
        /*服务拼接的mid到协议*/
        String lastAiMsgId = params.get("lastAiMsgId");
        if (LText.empty(lastAiMsgId)) {
            lastAiMsgId = params.get("msgId");
        }
        String friendId = params.get("friendId");
        String frinedSource = params.get("frinedSource");

        Activity activity = ForegroundUtils.get().getTopActivity();
        if (activity instanceof BaseActivity) {
            new AutoReplyFeedBack((BaseActivity) activity, "",
                    LText.getLong(lastAiMsgId), securityId, LText.getLong(friendId),
                    LText.getInt(frinedSource)).showFeedBackDialog();
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_SELF_ADVANTAGE_EDIT)
    void chatSelfAdvantageEdit(@NonNull Context context, @NonNull Map<String, String> params) {
        boolean useGPT = Boolean.valueOf(params.get("useGPT"));
        String securityId = params.get("securityId");
        String msgId = params.get("msgId");
        SelfAdvantageEditDialog dialog = new SelfAdvantageEditDialog();
        dialog.setUseGPT(useGPT);
        dialog.setSecurityId(securityId);
        dialog.setMsgId(msgId);
        dialog.loadData(false);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_SCORE_SHARE)
    void chatGeekScoreShare(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = params.get("securityId");
        SimpleApiRequest
                .POST(GeekUrlConfig.URL_GEEK_SCORE_SHARE)
                .addParam("securityId", securityId)
                .execute();
    }


    @Protocol(ProtocolConstants.Chat.TYPE_ZP_SCORE_SETTING)
    void chatZPScoreSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        SettingRouter.jumpToZpScoreChatActivity(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_MODIFY_CHAT_HELPER_STATUS)
    void chatHelperStatus(@NonNull Context context, @NonNull Map<String, String> params) {

        Activity activity = ForegroundUtils.get().getTopActivity();
        if (activity instanceof BaseActivity) {

            BaseActivity baseActivity = (BaseActivity) activity;
            String securityId = params.get("securityId");
            //1 开启 2 关闭 （协议不需要转）
            String chatHelperStatus = params.get("optionType");

            SimpleApiRequest
                    .GET(ChatUrlConfig.URL_CHAT_HELPER_MODIFY_STATUS)
                    .addParam("securityId", securityId)
                    .addParam("optionType", chatHelperStatus)
                    .setRequestCallback(new ApiRequestCallback<ChatHelperModifyStatusResponse>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            if (ActivityUtils.isValid(baseActivity)) {
                                baseActivity.showProgressDialog();
                            }
                        }

                        @Override
                        public void onSuccess(ApiData<ChatHelperModifyStatusResponse> data) {
                            ChatHelperModifyStatusResponse resp = data.resp;
                            String toastText = resp.toastText;
                            if (!LText.empty(toastText)) {
                                ToastUtils.showText(toastText);
                            }
                        }

                        @Override
                        public void onComplete() {
                            if (ActivityUtils.isValid(baseActivity)) {
                                baseActivity.dismissProgressDialog();
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    }).execute();
        }


    }


    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_HELPER_RESTART)
    void chatHelperRestart(@NonNull Context context, @NonNull Map<String, String> params) {
        String friendId = MapUtils.get(params, "friendId");
        String frinedSource = MapUtils.get(params, "frinedSource");

        long fId = LText.getLong(friendId);
        int fSource = LText.getInt(frinedSource);

        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(fId, UserManager.getUserRole().get(), fSource);
        if (contactBean == null) return;
        LActivity lActivity = getLActivity(context);
        ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
            @Override
            public void onCheckNoBlockListener() {
                SimpleApiRequest
                        .GET(ChatUrlConfig.URL_CHAT_HELPER_MODIFY_STATUS)
                        .addParam("securityId", contactBean.securityId)
                        .addParam("optionType", "1")
                        .setRequestCallback(new SimpleCommonApiRequestCallback<ChatHelperModifyStatusResponse>() {

                            @Override
                            public void onStart() {
                                super.onStart();
                                if (ActivityUtils.isValid(lActivity)) {
                                    lActivity.showProgressDialog();
                                }
                            }

                            @Override
                            public void onSuccess(ApiData<ChatHelperModifyStatusResponse> data) {
                                ChatHelperModifyStatusResponse resp = data.resp;
                                String toastText = resp.toastText;
                                if (!LText.empty(toastText)) {
                                    ToastUtils.showText(toastText);
                                }
                            }

                            @Override
                            public void onComplete() {
                                if (ActivityUtils.isValid(lActivity)) {
                                    lActivity.dismissProgressDialog();
                                }
                            }

                        }).execute();
            }
        }, ContactKeyManager.BgSource.CHAT_HELPER_RESTART);

    }

    @Protocol(ProtocolConstants.Chat.TYPE_CHECK_GEEK_HUNTER)
    void chatCheckGeekHunter(@NonNull Context context, @NonNull Map<String, String> params) {
        Activity topActivity = ForegroundUtils.get().getTopActivity();

        String group = MapUtils.get(params, "group");
        String topHunterIds = MapUtils.get(params, "topHunterIds");
        String locateHunterId = MapUtils.get(params, "locateHunterId");
        SimpleApiRequest get = SimpleApiRequest.GET(BossUrlConfig.URL_ZPCHAT_GEEK_HUNTER_RECOMMEND_PAGE_URL);
        get.setRequestCallback(new SimpleCommonApiRequestCallback<GeekHunterResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        if (topActivity instanceof BaseActivity) {
                            BaseActivity activity = (BaseActivity) topActivity;
                            activity.showProgressDialog();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<GeekHunterResponse> data) {
                        GeekHunterResponse resp = data.resp;
                        String clickUrl = resp.clickUrl;

                        boolean showHunter = resp.showHunter;
                        String message = resp.message;

                        if (showHunter) {
                            new ZPManager(context, clickUrl).handler();
                            GeekHunter.GeekHunterDB.getInstance().clearNoneRead();
                        } else {
                            WatchHunterReasonDialog watchHunterReasonDialog = new WatchHunterReasonDialog();
                            watchHunterReasonDialog.show(topActivity, message);

                        }

                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        if (topActivity instanceof BaseActivity) {
                            BaseActivity activity = (BaseActivity) topActivity;
                            activity.dismissProgressDialog();
                        }
                    }
                })
                .addParam("group", group)
                .addParam("topHunterIds", topHunterIds)
                .addParam("locateHunterId", locateHunterId)
                .execute();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_ROBOT_SETTING)
    void chatRobotSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        //设置页面 ，需求延期到 1207版本：https://zhishu.zhipin.com/wiki/4amHhjNAfxP
        SettingRouter.jumpToAiChatHelperSettingActivity(context,params.get("from"));
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_CHAT_ROBOT_EXPERIENCE)
    void chatRobotExperience(@NonNull Context context, @NonNull Map<String, String> params) {
        //体验页面，需求延期到 1207版本 https://zhishu.zhipin.com/wiki/4amHhjNAfxP
        ChatRouter.startChatHelperMockRoom(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GET_GEEK_VIDEO_JOB_VIRTUAL_PHONE)
    void chatGeekVideoJobVirtualPhone(@NonNull Context context, @NonNull Map<String, String> params) {
        String msgId = params.get("msgId");
        String friendId = params.get("friendId");
        String friendSource = params.get("friendSource");
        String videoId = params.get("videoId");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean == null) return;
        String phoneNumber = ViewCommon.keepPhoneSecret(UserManager.getPhone());
        String desc="确认本机呼出号码为注册手机号"+phoneNumber+"\r\n您拨打的是老板的虚拟号，不可以直接加微信";
        String buttonText="确认拨打";
        new CallVirtualPhoneDialog((Activity) context, () ->

                SimpleApiRequest.GET(ChatUrlConfig.URL_GEEK_VIDEO_JOB_VIRTUAL_PHONE)
                .setRequestCallback(new ApiRequestCallback<ResultVirtualPhoneResponse>() {
                    @Override
                    public void onSuccess(ApiData<ResultVirtualPhoneResponse> data) {
                        ResultVirtualPhoneResponse resp = data.resp;
                        String virtualPhone = resp.virtualPhone;
                        if (LText.empty(virtualPhone)) return;
                        StringUtil.dial(context, virtualPhone);
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                })
                .addParam("securityId", contactBean.securityId)
                .addParam("msgId", msgId)
                .addParam("lid", contactBean.lid)
                .addParam("videoId", videoId)
                .execute()

                , desc, buttonText).show();

    }

    @Protocol(ProtocolConstants.Chat.TYPE_GEEK_ONE_KEY_DELIVER)
    void chatOneKeyDeliver(@NonNull Context context, @NonNull Map<String, String> params) {
        long friendId = LText.getLong(params.get("friendId"));
        int friendSource = LText.getInt(params.get("friendSource"));
        long mid = LText.getLong(params.get("msgId"),0);
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, AccountHelper.getIdentity(), friendSource);
        if (contactBean == null || !contactBean.isContactEachOther()) {
            T.ss("双方回复之后才能使用");
            return;
        }
        Activity activity = getActivity(context);
        if (activity != null) {
            ExchangeSendResumeManager sendResumeManager = new ExchangeSendResumeManager(activity, contactBean);
            sendResumeManager.setMid(mid);
            sendResumeManager.onChatSendResumeListener();
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GET_BOSS_VIDEO_JOB_VIRTUAL_PHONE)
    void chatBossVideoJobVirtualPhone(@NonNull Context context, @NonNull Map<String, String> params) {
        String msgId = params.get("msgId");
        String friendId = params.get("friendId");
        String friendSource = params.get("friendSource");
        String videoId = params.get("videoId");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean == null) return;
        new ChatDailDialog().showDialog(context, contactBean.securityId, msgId,videoId);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_FAST_CONTACT_ACCEPT)
    void chatFastContactAccept(@NonNull Context context, @NonNull Map<String, String> params) {
        AgreeContactPhoneRequest request = new AgreeContactPhoneRequest(new ApiRequestCallback<HttpResponse>() {

            @Override
            public void onStart() {
                if (context instanceof BaseActivity) {
                    BaseActivity activity = (BaseActivity) context;
                    activity.showProgressDialog("");
                }
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                ToastUtils.showText("已将您的联系方式告知boss");
            }

            @Override
            public void onComplete() {
                if (context instanceof BaseActivity) {
                    BaseActivity activity = (BaseActivity) context;
                    activity.dismissProgressDialog();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.friendId = LText.getLong(params.get("friendId"));
        HttpExecutor.execute(request);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_CALL_MEDIA_INTERVIEW)
    void chatCallMediaInterview(@NonNull Context context, @NonNull Map<String, String> params){
       new MediaCallAction().onProtocolExecute(context,params);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_VERBAL_INTERVIEW)
    void verbalInterview(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = params.get("securityId");
        String recordId = params.get("recordId");
        String source = params.get("source");

        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_CHAT_VERBAL_INTERVIEW);
        intent.putExtra("securityId", securityId);
        intent.putExtra("recordId", recordId);
        intent.putExtra("source", source);
        context.sendBroadcast(intent);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_GUAN_JIA_CONTINUE_SERVICE)
    void guanJiaContinueService(@NonNull Context context, @NonNull Map<String, String> params) {
        SimpleApiRequest.POST(ChatUrlConfig.URL_GUAN_JIA_CONTINUE_SERVICE).addParam("securityId", params.get("securityId"))
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                    }
                }).execute();
    }

    // 1217.613【C端】蓝领-被开聊场景引导 C 快捷回复 bosszp://bosszhipin.app/openwith?type=sendTextMessage&text="请问,hello"
    @Protocol(ProtocolConstants.Chat.TYPE_SEND_TEXT_MESSAGE)
    void sendTextMessage(@NonNull Context context, @NonNull Map<String, String> params){
        TLog.debug("chatcommon", "执行sendTextMessage");
        String text = params.get("text");
        String toUid = params.get("toUid");
        String toSource = params.get("toSource");
        String bizType = params.get("bizType");
        String bgSource = params.get("bgSource");

        Intent intent = new Intent();
        intent.setAction(Constants.CHAT_SEND_TEXT_MESSAGE);
        intent.putExtra(Constants.DATA_LONG, LText.getLong(toUid));
        intent.putExtra(Constants.DATA_INT, LText.getInt(toSource));
        intent.putExtra(Constants.DATA_INT_2, LText.getInt(bizType));
        intent.putExtra(Constants.DATA_STRING, text);
        intent.putExtra(Constants.DATA_STRING2, bgSource);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        App.getAppContext().sendBroadcast(intent);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_UPDATE_USER_NOTIFY_SETTING)
    void updateUserNotifySetting(@NonNull Context context, @NonNull Map<String, String> params) {
        TLog.debug("chatprotocolaction", "执行updateUserNotifySetting， params = %s", params);
        int notifyType = LText.getInt(params.get("notifyType"));
        int settingType = LText.getInt(params.get("settingType"));
        int scene = LText.getInt(params.get("scene"));

        GeekF1Util.updateSettingState(context, notifyType, scene, settingType == UserNotifySetting.FUNCTION_ENABLE, new SimpleCommonApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                super.onSuccess(data);
            }
        });
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_NO_INTEREST_SETTING)
    void openNoInterestSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        String friendId = params.get("friendId");
        String friendSource = params.get("friendSource");
        if (TextUtils.isEmpty(friendId) || TextUtils.isEmpty(friendSource)) return;
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(Long.parseLong(friendId), UserManager.getUserRole().get(), Integer.parseInt(friendSource));
        UserRejectManager.getInstance().changeRejectListener(contactBean, UserRejectManager.PAGE_TYPE_CHAT_PAGE,
                new RejectHttpManager.OnLockUnLockCallBack() {

                    @Override
                    public void onSuccess(ContactBean contactBean) {
                        if (contactBean.isReject && UserRejectManager
                                .getInstance().checkEnableRejectJumpToMain()) {
                            AppUtil.finishActivity(context);
                        }
                    }

                    @Override
                    public void onFailed(ContactBean contactBean) {
                        TLog.error("openNoInterestSetting", "openNoInterestSetting failed");
                    }
                });
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_MSG_TIP)
    void openMsgTip(@NonNull Context context, @NonNull Map<String, String> params) {
        String source = params.get("source");
        String msgId = params.get("msgId");
        SimpleApiRequest.GET(ChatUrlConfig.URL_GET_MSG_TIP)
                .setRequestCallback(new SimpleCommonApiRequestCallback<ChatMsgTipResponse>() {
                    @Override
                    public void onSuccess(ApiData<ChatMsgTipResponse> data) {
                        if (data.resp == null || LText.empty(data.resp.title) || LText.empty(data.resp.text)) {
                            return;
                        }

                        ExplainMsgTipDialog explainMsgTipDialog = new ExplainMsgTipDialog(context, data.resp);
                        explainMsgTipDialog.show();
                    }
                })
                .addParam("msgId", LText.getLong(msgId))
                .addParam("source", LText.getInt(source))
                .execute();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_SEND_TEXT_AND_EXCHANGE)
    void sendTextAndExchange(@NonNull Context context, @NonNull Map<String, String> params) {
        String msgId = params.get("msgId");
        String text = params.get("text");
        String friendId = params.get("friendId");
        String friendSource = params.get("friendSource");

        if (LText.empty(msgId) || LText.empty(text)) {
            TLog.info(TAG, "发送电话失败, 数据错误，msgId = %s, text = %s", msgId, text);
            return;
        }

        String message = "";
        try {
            message = URLDecoder.decode(text, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            TLog.error(TAG, "UrlDecode text failed, error msg = %s", e);
        }

        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean != null && ActivityUtils.isValid(context) && LText.empty(contactBean.friendPhone)) {
            ExchangePhoneManager exchangePhoneManager = new ExchangePhoneManager((Activity) context, contactBean);
            ExchangePhoneExtraBean extraBean = new ExchangePhoneExtraBean();
            extraBean.msgId = LText.getLong(msgId);
            extraBean.message = message;
            exchangePhoneManager.setExtraBean(extraBean);

            exchangePhoneManager.onExecuteExchange();
        } else {
            long friendIdLong = LText.getLong(friendId);
            int friendSourceInt = LText.getInt(friendSource);
            // 发送文本消息
            Intent sendTextIntent = new Intent();
            sendTextIntent.setAction(Constants.CHAT_SEND_TEXT_MESSAGE);
            sendTextIntent.putExtra(Constants.DATA_LONG, friendIdLong);
            sendTextIntent.putExtra(Constants.DATA_INT, friendSourceInt);
            sendTextIntent.putExtra(Constants.DATA_STRING, message);
            sendTextIntent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            ReceiverUtils.sendBroadcastSystem(context, sendTextIntent);

            //发送广播创建查看电话卡片
            Intent intent = new Intent(Constants.RECEIVER_ADD_LOCAL_PHONE_CARD);
            intent.putExtra(Constants.DATA_LONG, friendIdLong);
            intent.putExtra(Constants.DATA_INT, friendSourceInt);
            ReceiverUtils.sendBroadcastSystem(context, intent);
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_OPEN_GEEK_CHAT_QUICK_LIST)
    void openGeekChatQuickList(@NonNull Context context, @NonNull Map<String, String> params) {
        SingleRouter.startQuickHandleJobList(context);
    }


    @Protocol(ProtocolConstants.Chat.TYPE_SHOW_ADD_WIDGET_DIALOG)
    void showAddWidgetDialog(@NonNull Context context, @NonNull Map<String, String> params) {
        if (UserManager.isBossRole()) {
            int type = LText.getInt(params.get("showType"));
            SingleRouter.showAddWidgetDialog(type);
        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CLICK_REQUEST_EXCHANGE)
    void clickRequestExchange(@NonNull Context context, @NonNull Map<String, String> params) {
        String exchangeType = params.get("exchangeType");
        // 微信exchangeType：2  交换前判断是否有微信号
        if (LText.getInt(exchangeType) == 2 && LText.empty(UserManager.getWeiXin()) && ActivityUtils.isValid(context)) {
            // 弹出对话框要求当前用户设置微信
            ChatWechatDialog dialog = new ChatWechatDialog((Activity) context, input -> requestExchangeWxPhone(context, params));
            dialog.show();
        } else {
            requestExchangeWxPhone(context, params);
        }
    }

    private void requestExchangeWxPhone(@NonNull Context context, @NonNull Map<String, String> params) {
        String securityId = params.get("securityId");
        String exchangeType = params.get("exchangeType");
        String msgId = params.get("msgId");

        SimpleApiRequest.POST(ChatUrlConfig.URL_CHAT_EXCHANGE_REQUEST)
                .addParam("securityId", securityId)
                .addParam("exchangeType", LText.getInt(exchangeType))
                .addParam("mid", LText.getLong(msgId))
                .setRequestCallback(new SimpleApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                })
                .execute();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CLICK_REQUEST_EXCHANGE_CANCEL)
    void clickRequestExchangeCancel(@NonNull Context context, @NonNull Map<String, String> params) {
        String startChatProcessExpGroup = params.get("startChatProcessExpGroup");
        String msgId = params.get("msgId");

        SimpleApiRequest.POST(ChatUrlConfig.URL_CHAT_EXCHANGE_CANCEL_TEST)
                .addParam("startChatProcessExpGroup", startChatProcessExpGroup)
                .addParam("mid", LText.getLong(msgId))
                .setRequestCallback(new SimpleCommonApiRequestCallback<ExchangeCancelTestResponse>() {
                    @Override
                    public void onSuccess(ApiData<ExchangeCancelTestResponse> data) {
                        if (data == null || data.resp == null || data.resp.dialogInfo == null || LList.isEmpty(data.resp.dialogInfo.buttons)) {
                            TLog.info(TAG, "clickRequestExchangeCancel：数据错误");
                            return;
                        }

                        ExchangeCancelTestResponse.DialogInfo dialogInfo = data.resp.dialogInfo;
                        if (dialogInfo.buttons.size() != 2) {
                            TLog.info(TAG, "dialog按钮数量不是2个");
                            return;
                        }

                        String tip = data.resp.tip;
                        if (ActivityUtils.isValid(context)) {
                            DialogUtils.Builder builder = new DialogUtils.Builder((Activity) context)
                                    .setCancelable(false)
                                    .setTitle(dialogInfo.title)
                                    .setDesc(dialogInfo.content)
                                    .setDoubleButton();
                            List<ExchangeCancelTestResponse.Button> buttonList = dialogInfo.buttons;
                            for (ExchangeCancelTestResponse.Button button : buttonList) {
                                if (button == null || LText.empty(button.text)) {
                                    continue;
                                }

                                if (button.actionType == 1) {
                                    builder.setPositiveAction(button.text, v -> {
                                        SimpleApiRequest.POST(ChatUrlConfig.URL_CHAT_EXCHANGE_CANCEL)
                                                .addParam("startChatProcessExpGroup", startChatProcessExpGroup)
                                                .addParam("mid", LText.getLong(msgId))
                                                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                                                    @Override
                                                    public void onSuccess(ApiData<EmptyResponse> data) {
                                                        if (!LText.empty(tip)) {
                                                            ToastUtils.showText(tip);
                                                        }
                                                    }
                                                }).execute();
                                    });
                                } else {
                                    builder.setNegativeAction(button.text, v -> {
                                    });
                                }
                            }

                            builder.build().show();
                        }
                    }
                })
                .execute();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_HELPER_QUICK_SETTING)
    void chatHelperQuickSetting(@NonNull Context context, @NonNull Map<String, String> params) {
        String source = params.get("source");
        String securityId = params.get("securityId");
        if (LText.empty(source)) {
            return;
        }

        SimpleApiRequest.GET(ChatUrlConfig.URL_CHAT_QUICK_SETTING_INGO)
                .addParam("source", source)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<ChatQuickSettingInfoResponse>() {
                    @Override
                    public void onSuccess(ApiData<ChatQuickSettingInfoResponse> data) {
                        if (data == null || data.resp == null || !ActivityUtils.isValid(context)) {
                            return;
                        }

                        AiChatHelperGuideDialog aiChatHelperGuideDialog = new AiChatHelperGuideDialog();
                        aiChatHelperGuideDialog.showDialog((Activity) context, data.resp, securityId, source);
                    }
                })
                .execute();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_AI_DIRECT_EXPLAIN)
    void aiDirectChatTipsDialog(@NonNull Context context, @NonNull Map<String, String> params) {
        String scene = params.get("scene");
        AiDirectChatTipsDialog aiChatHelperGuideDialog = new AiDirectChatTipsDialog(getActivity(context));
        aiChatHelperGuideDialog.setScene(scene);
        aiChatHelperGuideDialog.showDialog();
    }

    @Protocol(ProtocolConstants.Chat.TYPE_CHAT_EXCHANGE)
    void chatExchange(@NonNull Context context, @NonNull Map<String, String> params) {
        String type = params.get("exchgType");
        long msgId = LText.getLong(params.get("msgId"), 0);
        int scene = LText.getInt(params.get("scene"), 0);
        String friendId = params.get("friendId");
        String friendSource = params.get("friendSource");
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendId), UserManager.getUserRole().get(), LText.getInt(friendSource));
        if (contactBean == null) {
            TLog.error(TAG, "chatExchange  = contactBean is null friendId= %s friendSource = %s", friendId, friendSource);
            return;
        }
        if (ExchangeChatRequest.EXCHANGE_PHONE.equals(type)) {
            ExchangePhoneManager exchangePhoneManager = new ExchangePhoneManager((Activity) context, contactBean);
            exchangePhoneManager.setScene(scene);
            exchangePhoneManager.setMid(msgId);
            exchangePhoneManager.onExchangePhoneListener();
        } else if (ExchangeChatRequest.EXCHANGE_WEIXIN.equals(type)) {
            ExchangeWeChatManager exchangeWeChatManager = new ExchangeWeChatManager(getActivity(context), contactBean);
            exchangeWeChatManager.setScene(scene);
            exchangeWeChatManager.setMid(msgId);
            exchangeWeChatManager.exchangeWxChat();
        } else if (ExchangeChatRequest.EXCHANGE_GEEK_SEND_RESUME.equals(type)) {
            ExchangeSendResumeManager exchangeWeChatManager = new ExchangeSendResumeManager(getActivity(context), contactBean);
            exchangeWeChatManager.setScene(scene);
            exchangeWeChatManager.setMid(msgId);
            exchangeWeChatManager.onChatSendResumeListener();
        } else if (ExchangeChatRequest.EXCHANGE_BOSS_REQUIRE_RESUME.equals(type)) {
            ExchangeRequireResumeManager exchangeRequireResumeManager = new ExchangeRequireResumeManager(getActivity(context), contactBean);
            exchangeRequireResumeManager.setScene(scene);
            exchangeRequireResumeManager.setMid(msgId);
            exchangeRequireResumeManager.onRequireResumeListener();
        } else if (ExchangeChatRequest.EXCHANGE_PHONE_WECHAT.equals(type)) {
            BatchExchangeWeChatPhoneManager batchExchangeWeChatPhoneManager = new BatchExchangeWeChatPhoneManager(getActivity(context), contactBean);
            batchExchangeWeChatPhoneManager.setScene(scene);
            batchExchangeWeChatPhoneManager.setMid(msgId);
            batchExchangeWeChatPhoneManager.onBatchExchangeListener();

        }
    }

    @Protocol(ProtocolConstants.Chat.TYPE_FRIEND_CLEAN)
    void friendClean(@NonNull Context context, @NonNull Map<String, String> params) {
        String zpFriendIds = params.get("zpFriendIds");
        String dzFriendIds = params.get("dzFriendIds");
        String scene = params.get("scene");

        if (TextUtils.isEmpty(dzFriendIds) && TextUtils.isEmpty(zpFriendIds)) {
            TLog.info(TAG, "TYPE_FRIEND_CLEAN: dzFriendIds and zpFriendIds are empty");
            return;
        }
        // 添加校验逻辑，检查好友关系
        final List<String> validZpBossIds = StringUtil.split(",",zpFriendIds);
        final List<String> validDzBossIds =  StringUtil.split(",",dzFriendIds);

        if (LList.isEmpty(validZpBossIds) && LList.isEmpty(validDzBossIds)) {
            TLog.info(TAG, "TYPE_FRIEND_CLEAN: No valid friends to clean");
            return;
        }


        if (TextUtils.equals(scene, "1")) {
            Intent intent = getIntent(Constants.RECEIVER_CHAT_PAGE_FRIEND_CLEAN, validZpBossIds, validDzBossIds);
            intent.putExtra("friendCleaned", true);
            ReceiverUtils.sendBroadcastSystem(context, intent);
        }

        final LActivity lActivity = getLActivity(context);
        SimpleApiRequest.POST(ChatUrlConfig.URL_ZPRELATION_FRIEND_CLEAN)
                .addParam("zpFriendIds", zpFriendIds)
                .addParam("dzFriendIds", dzFriendIds)
                .addParam("scene", scene) // 场景区分，1.点击灰条清理单个联系人2.通过批量清理联系人
                .setRequestCallback(new SimpleCommonApiRequestCallback<SuccessResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        if (lActivity != null) {
                            lActivity.showProgressDialog();
                        }
                    }

                    @Override
                    public void handleInChildThread(ApiData<SuccessResponse> data) {
                        if (LList.isNotEmpty(validZpBossIds)) {
                            for (String zpFriendId : validZpBossIds) {
                                long friendId = LText.getLong(zpFriendId);
                                if (friendId <= 0) continue;
                                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, AccountHelper.getIdentity(), ContactBean.FROM_BOSS);
                                if (contactBean != null) {
                                    ContactManager.getInstance().deleteContact(contactBean);
                                    MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), friendId, ContactBean.FROM_BOSS);
                                }
                            }
                        }
                        if (LList.isNotEmpty(validDzBossIds)) {
                            for (String dzFriendId : validDzBossIds) {
                                long friendId = LText.getLong(dzFriendId);
                                if (friendId <= 0) continue;
                                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, AccountHelper.getIdentity(), ContactBean.FROM_DIAN_ZHANG);
                                if (contactBean != null) {
                                    ContactManager.getInstance().deleteContact(contactBean);
                                    MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), friendId, ContactBean.FROM_DIAN_ZHANG);
                                }
                            }
                        }
                        
                        super.handleInChildThread(data);
                    }

                    @Override
                    public void onSuccess(ApiData<SuccessResponse> data) {
                        super.onSuccess(data);
                        ToastUtils.showText("已清理，即将退出对话");
                        if (TextUtils.equals(scene, "1")) {
                            Intent intent = getIntent(ContactManager.ACTION_DELETE_CONTACT, validZpBossIds, validDzBossIds);
                            ReceiverUtils.sendBroadcastSystem(context, intent);
                        }
                    }

                    @Override
                    public void onComplete() {
                        if (lActivity != null) {
                            lActivity.dismissProgressDialog();
                        }
                    }
                }).execute();

    }


    @Protocol(ProtocolConstants.Chat.TYPE_AI_OFFER_COMPANION)
    void aiOfferCompanion(@NonNull Context context, @NonNull Map<String, String> params) {
        AIChatRouter.jumpToUpscaleGeekAIActivity(context);
    }

    @Protocol(ProtocolConstants.Chat.TYPE_SEND_LIGHT_RESUME)
    void onSendLightResume(@NonNull Context context, @NonNull Map<String, String> params) {
        String clickBtnType = params.get("clickBtnType");
        String msgId = params.get("msgId");

        SimpleApiRequest.POST(ChatUrlConfig.URL_MICRO_RESUME_REQUEST_CARD)
                .addParam("msgId", msgId)
                .addParam("clickBtnType", clickBtnType)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                    }
                })
                .execute();
    }

    @NonNull
    private static Intent getIntent(String action, List<String> validZpBossIds, List<String> validDzBossIds) {
        Intent intent = new Intent(action);
        if (LList.getCount(validZpBossIds) == 1) {
            intent.putExtra(Constants.DATA_LONG, LText.getLong(validZpBossIds.get(0)));
            intent.putExtra(Constants.DATA_INT, ContactBean.FROM_BOSS);
        } else if (LList.getCount(validDzBossIds) == 1) {
            intent.putExtra(Constants.DATA_LONG, LText.getLong(validDzBossIds.get(0)));
            intent.putExtra(Constants.DATA_INT, ContactBean.FROM_DIAN_ZHANG);
        }
        return intent;
    }

    @Nullable
    private static LActivity getLActivity(@NonNull Context context) {
        if (context instanceof LActivity) {
            return  (LActivity) context;
        }else {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (topActivity instanceof LActivity){
                return (LActivity) topActivity;
            }
        }
        return null;
    }

    private Activity getActivity(Context context) {
        Activity activity = null;
        if (context instanceof Activity) {
            activity = (Activity) context;
        } else {
            return ForegroundUtils.get().getTopActivity();
        }
        return activity;
    }
}
