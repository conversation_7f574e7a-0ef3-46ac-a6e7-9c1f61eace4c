package com.hpbr.bosszhipin.chat.contact.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.chat.utils.ChatAnalysisUtil;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.interview.entity.ServerInterviewDetailBean;
import com.hpbr.bosszhipin.module.main.fragment.contacts.F2ContactMarkLabelManager;
import com.hpbr.bosszhipin.module.main.fragment.contacts.viewholder.BossQuestionAskManager;
import com.hpbr.bosszhipin.module.main.fragment.contacts.viewholder.GeeKQuestionResearchManager;
import com.hpbr.bosszhipin.utils.ColorUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.ContactExchangeChatView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;

import net.bosszhipin.api.CommonUserConfigResponse;

/**
 * 1017.88 聊天优化第一期 聊天列表item消息状态
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
public class ContactNewStatusTextView extends MTextView {

    public ContactNewStatusTextView(Context context) {
        super(context);
    }

    public ContactNewStatusTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ContactNewStatusTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    boolean isNewContactRecommendSort;

    public void setNewContactRecommendSort(boolean newContactRecommendSort) {
        isNewContactRecommendSort = newContactRecommendSort;
    }

    // 面试>>取消>超时>拒绝>电话直播>附件简历>电话>微信>平台推荐>草稿>校招提醒>金牛图标>新招呼>发送中>沟通助手>已发送>失败>已读
    public void refreshStatus(@Nullable ContactBean contactBean) {

        if (contactBean == null) {
            setVisibility(View.GONE);
            return;
        }
        setSingleLine();
        TextValueColor textValue = getTextValue(contactBean);

        if (textValue != null) {
            setVisibility(View.VISIBLE);
            //去掉背景图片
            setBackgroundDrawable(null);
            //去掉文字
            setText("");
            //设置TextView宽高为WrapContent
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) getLayoutParams();
            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            setLayoutParams(layoutParams);
            // 显示图片
            Drawable drawable = textValue.getDrawable();
            if (drawable != null) {//16dp
                int size = Scale.dip2px(getContext(), 14);
                drawable.setBounds(0, 0, size, size);
                // 设置背景图标
                setBackgroundDrawable(drawable);

                // 需要设置TextView的宽高为图片宽高,不然会拉伸
                ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) getLayoutParams();
                marginLayoutParams.width = size;
                marginLayoutParams.height = size;
                setLayoutParams(marginLayoutParams);
            } else {
                setText(textValue.getValue());
                setTextColor(textValue.getColor());
            }

        } else {
            setVisibility(View.GONE);
        }

    }

    // 面试>>取消>超时>拒绝>电话直播>附件简历>电话>微信>平台推荐>草稿>待对方接受>校招提醒>金牛图标>新招呼>发送中> 沟通助手>已发送>失败>已读
    private @Nullable
    TextValueColor getTextValue(@Nullable ContactBean contactBean) {

        if (contactBean == null) return null;

        final TextValueColor textValueColor = new TextValueColor();
        int highlightColor = ContextCompat.getColor(getContext(), R.color.color_BOSS7);

        if (UserManager.isBossRole()
                && contactBean.noneReadCount > 0
                && F2ContactMarkLabelManager.getInstance().isMarkType(contactBean.friendId, contactBean.friendSource, F2ContactMarkLabelManager.MarkSPManage.KEY_AUTO_REPLY)) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FF15ADA9));
            textValueColor.setValue("[需要协助]");
            return textValueColor;
        }

        if (UserManager.isGeekRole() && contactBean.messageExchangeIcon == ContactExchangeChatView.BOSS_ASK_GEEK_INTERVIEW) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[面试]");
            return textValueColor;
        }
        if (UserManager.isBossRole() && contactBean.currentInterviewStatus == ServerInterviewDetailBean.INTERVIEW_WAIT_START
                && !contactBean.hasJumpToChat) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[面试接受]");
            return textValueColor;
        }
        if (contactBean.currentInterviewStatus == ServerInterviewDetailBean.INTERVIEW_CANCELLED
                && !contactBean.hasJumpToChat) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[面试取消]");
            return textValueColor;
        }
        if (contactBean.currentInterviewStatus == ServerInterviewDetailBean.INTERVIEW_TIME_OUT
                && !contactBean.hasJumpToChat) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[面试超时]");
            return textValueColor;
        }
        if (UserManager.isBossRole()
                && contactBean.currentInterviewStatus == ServerInterviewDetailBean.INTERVIEW_REJECTED
                && !contactBean.hasJumpToChat) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[面试拒绝]");
            return textValueColor;
        }
        if (contactBean.noneReadCount > 0
                && contactBean.directCallStatus == 1) {//1 电话未接通
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[电话未接通]");
            return textValueColor;
        }
        if (contactBean.noneReadCount > 0
                && contactBean.directCallStatus == 2) {// 2 未接电话
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[未接电话]");
            return textValueColor;
        }
        if (contactBean.noneReadCount > 0
                && contactBean.directCallStatus == 3) {// 3 通话成功
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[通话成功]");
            return textValueColor;
        }
        if (contactBean.messageExchangeIcon == ContactExchangeChatView.ASK_RESUME && contactBean.noneReadCount > 0) {
            textValueColor.setColor(highlightColor);
            textValueColor.setValue("[附件简历]");
            ChatAnalysisUtil.statsMsgTagPV("[附件简历]");
            return textValueColor;
        }
        if (contactBean.messageExchangeIcon == ContactExchangeChatView.ASK_PHONE && contactBean.noneReadCount > 0) {
            textValueColor.setColor(highlightColor);
            textValueColor.setValue("[换电话]");
            ChatAnalysisUtil.statsMsgTagPV("[换电话]");
            return textValueColor;
        }
        if (contactBean.messageExchangeIcon == ContactExchangeChatView.ASK_WEI_XIN && contactBean.noneReadCount > 0) {
            textValueColor.setColor(highlightColor);
            textValueColor.setValue("[换微信]");
            ChatAnalysisUtil.statsMsgTagPV("[换微信]");
            return textValueColor;
        }
        if (contactBean.isPlatFromResearch) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[平台调研]");
            return textValueColor;
        }

        if (!LText.empty(contactBean.RoughDraft) || contactBean.quoteMessageId > 0) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFFF4753));
            textValueColor.setValue("[草稿]");
            return textValueColor;
        }

        if ((!contactBean.isIHaveSendMsgToFriend())
                && BossQuestionAskManager.getInstance().isReceiveAskCard(contactBean)) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[已答题]");
            return textValueColor;
        }

        if (contactBean.ats) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[校招提醒]");
            return textValueColor;
        }
        if (UserManager.isGeekRole() && contactBean.goldGeekStatus == 1) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[来自金牛服务]");
            return textValueColor;
        }
        if (contactBean.getGroupType() == ContactBean.GroupType.NEW_GREETING) {
            if (isNewContactRecommendSort) {
                if (AccountHelper.isGeek()) {
                    textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FF5E5E5E_FF9E9EA1));
                    if (contactBean.invalidJob == 1) { //停止招聘
                        textValueColor.setValue("[停止招聘]");
                        return textValueColor;
                    } else if (!TextUtils.isEmpty(contactBean.bossJobPosition) || !TextUtils.isEmpty(contactBean.salaryDesc)) {
                        textValueColor.setValue("[" + StringUtil.connectTextWithChar(" ", contactBean.bossJobPosition, contactBean.salaryDesc) + "]");
                        return textValueColor;
                    }
                }
            } else if (UserManager.isGeekRole() && !LText.empty(contactBean.recommendReason)) {
                textValueColor.setColor(highlightColor);
                textValueColor.setValue(contactBean.recommendReason);
                return textValueColor;
            } else {
                textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
                textValueColor.setValue("[新招呼]");
                return textValueColor;
            }
        }

        //问卷信息
        if (UserManager.isGeekRole()
                &&contactBean.noneReadCount>0
                && GeeKQuestionResearchManager.getInstance().checkIsQuestionResearchCardMsgId(contactBean)) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[问卷信息]");
            return textValueColor;
        }

        if (contactBean.lastChatStatus == 0) {
            Drawable drawable = ContextCompat.getDrawable(getContext(), R.mipmap.ic_chat_status_sending);
            textValueColor.setDrawable(drawable);
            return textValueColor;
        }
        if (UserManager.isBossRole() && contactBean.isContactHelper) {
            textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
            textValueColor.setValue("[沟通助手]");
            return textValueColor;
        }
        if (contactBean.lastChatStatus == 1) {
            CommonUserConfigResponse.MsgStatusConfigs msgStatusConfigs = CommonConfigManager.getInstance().getMsgStatusConfigs();
            if (msgStatusConfigs != null && msgStatusConfigs.received != null) {
                if (msgStatusConfigs.received.chatListDisplay == 1) return null;
                textValueColor.setColor(TextUtils.isEmpty(msgStatusConfigs.received.color)
                        ? ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70)
                        : ColorUtils.parserColor(getContext(), msgStatusConfigs.received.color));
                textValueColor.setValue(TextUtils.isEmpty(msgStatusConfigs.received.text) ? "[送达]" : ("["+msgStatusConfigs.received.text)+"]");
            } else {
                textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
                textValueColor.setValue("[送达]");
            }
            return textValueColor;
        }
        if (contactBean.lastChatStatus == 2) {
            Drawable drawable = ContextCompat.getDrawable(getContext(), R.mipmap.ic_chat_f2_failed);
            textValueColor.setDrawable(drawable);
            return textValueColor;
        }
        if (contactBean.lastChatStatus == 3) {
            CommonUserConfigResponse.MsgStatusConfigs msgStatusConfigs = CommonConfigManager.getInstance().getMsgStatusConfigs();
            if (msgStatusConfigs != null && msgStatusConfigs.read != null) {
                if (msgStatusConfigs.read.chatListDisplay == 1) return null;
                textValueColor.setColor(TextUtils.isEmpty(msgStatusConfigs.read.color)
                        ? ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70)
                        : ColorUtils.parserColor(getContext(), msgStatusConfigs.read.color));
                textValueColor.setValue(TextUtils.isEmpty(msgStatusConfigs.read.text) ? "[已读]" : ("["+msgStatusConfigs.read.text)+"]");
            } else {
                textValueColor.setColor(ContextCompat.getColor(getContext(), R.color.color_FFB8B8B8_FF6D6D70));
                textValueColor.setValue("[已读]");
            }
            return textValueColor;
        }

        return null;
    }



    private static class TextValueColor {

        private String value = "";

        private int color = -1;

        private Drawable drawable;

        public void setDrawable(Drawable drawable) {
            this.drawable = drawable;
        }


        public void setValue(String value) {
            this.value = value;
        }


        public Drawable getDrawable() {
            return drawable;
        }

        public void setColor(int color) {
            this.color = color;
        }

        public String getValue() {
            return value;
        }

        public int getColor() {
            return color;
        }
    }


}