package com.hpbr.bosszhipin.chat.airecruit.presenter;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.chat.airecruit.bean.AiUpScaleGeekJobCardBean;
import com.hpbr.bosszhipin.chat.airecruit.itf.IAICommonSceneView;
import com.hpbr.bosszhipin.chat.airecruit.view.AiDeepChatView;
import com.hpbr.bosszhipin.chat.entity.GptButtonMessage;
import com.hpbr.bosszhipin.chat.entity.GptDeepChatMessage;
import com.hpbr.bosszhipin.chat.entity.GptListResultMessage;
import com.hpbr.bosszhipin.chat.entity.GptLocalDividerMessage;
import com.hpbr.bosszhipin.chat.entity.GptLocalFailedMessage;
import com.hpbr.bosszhipin.chat.entity.GptLocalLikeUnLikeMessage;
import com.hpbr.bosszhipin.chat.entity.GptLocalLoadingMessage;
import com.hpbr.bosszhipin.chat.entity.GptLocalTextMessage;
import com.hpbr.bosszhipin.chat.entity.GptMessage;
import com.hpbr.bosszhipin.chat.entity.GptNotifyMessage;
import com.hpbr.bosszhipin.chat.entity.GptRetryMessage;
import com.hpbr.bosszhipin.chat.entity.GptSuggestMessage;
import com.hpbr.bosszhipin.chat.entity.GptSuggestMessage2;
import com.hpbr.bosszhipin.chat.export.bean.AICommonSceneBundleBean;
import com.hpbr.bosszhipin.chat.export.bean.AiReceiveCommonSceneParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossUrlConfig;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.AiHistoryResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.ListIterator;
import java.util.Set;

public class BossUpScaleGeekPresenterImp extends BaseIAiPresenterImp{

    private static final String TAG = "BossUpScaleGeekImpl";
    private IAICommonSceneView iAdvantageSearchView;

    public String sessionId;

    private String currentQueryText;

    private final Set<String> stopMessageSet = new HashSet<>();
    private long timeout = 65000;

    private boolean isRecommend = false;
    public boolean btnRequired = false;

    public final Set<String> recommendMessageSet = new HashSet<>();


    public BossUpScaleGeekPresenterImp(IAICommonSceneView iAdvantageSearchView, BaseActivity activity, String sessionId
            , AICommonSceneBundleBean aiCommonSceneBundleBean) {
        super(activity, sessionId, aiCommonSceneBundleBean);
        this.iAdvantageSearchView = iAdvantageSearchView;
        this.style = 1;
    }

    @Override
    public void initSessionId(boolean addLoadingView, @Nullable List<GptLocalTextMessage> textMessage, @Nullable AiCommonSceneRequestHelper.OnInitSessionCallback onInitSessionCallback) {
        // 创建新的sessionId
        this.sessionId = "";
        if (addLoadingView) {
            dataMessage.add(getCustomTimeOutLoadingMessage(GptRetryMessage.obtainTextMessage("",
                    GptRetryMessage.RetryTextMessage.INPUT_TEXT,
                    GptRetryMessage.RetryTextMessage.ERROR_TIME), () -> refreshAdapter(false), timeout));
            refreshAdapter(true);
        }
        requestHelper.initSessionId((success, id) -> {
            if (success) {
                this.sessionId = id;
            }

            if (onInitSessionCallback != null) {
                onInitSessionCallback.onSessionListener(success, sessionId);
            }
        });

    }


    public void continueSession(String sessionId, @Nullable AiCommonSceneRequestHelper.OnInitSessionCallback onInitSessionCallback, boolean addLoadingView) {
        this.sessionId = sessionId;
        cleanAllAppendData();
        if (addLoadingView) {
            dataMessage.add(getCustomTimeOutLoadingMessage(GptRetryMessage.obtainTextMessage("",
                    GptRetryMessage.RetryTextMessage.INPUT_TEXT,
                    GptRetryMessage.RetryTextMessage.ERROR_TIME), () -> refreshAdapter(false), timeout));
        }
        requestHelper.continueSession(sessionId, (success, id) -> {
            if (success) {
                this.sessionId = id;
            }
            if (onInitSessionCallback != null) {
                onInitSessionCallback.onSessionListener(success, sessionId);
            }
        });
        refreshAdapter(true);
    }

    public void appendLoadingMessage() {
        if (LList.getLastElement(dataMessage) instanceof GptLocalLoadingMessage) {
            return;
        }
        dataMessage.add(getCustomTimeOutLoadingMessage(GptRetryMessage.obtainTextMessage("",
                GptRetryMessage.RetryTextMessage.INPUT_TEXT,
                GptRetryMessage.RetryTextMessage.ERROR_TIME), () -> refreshAdapter(false), timeout));
        refreshAdapter(true);
    }

    public void setTimeout(long timeout) {
        if (timeout <= 0) return;
        this.timeout = timeout;
    }

    @Override
    public void onNetErrorResetSendListener(@NonNull GptLocalFailedMessage gptMessage) {
        GptRetryMessage retryGptMessage = gptMessage.retryGptMessage;
        if (retryGptMessage.retryTextListener != null) {
            int index = dataMessage.indexOf(gptMessage);
            if (index == 0 || (dataMessage.get(index -1) instanceof GptLocalDividerMessage && ((GptLocalDividerMessage) dataMessage.get(index -1)).dividerType == GptLocalDividerMessage.DIVIDER_TYPE_SESSION)) {
                //替换第一个为loading样式
                dataMessage.set(index, getCustomTimeOutLoadingMessage(retryGptMessage, () -> refreshAdapter(false), timeout));
                refreshAdapter(false);
                //点击的第一个消息卡片重试，需要重新创建sessionId,需要提示开聊语
                if (TextUtils.isEmpty(sessionId)) {
                    initSessionId(false, null, null);
                }
            } else {
                String text = retryGptMessage.retryTextListener.getText();
                requestHelper.realSubmit(new JSONObject(), text, null, new AiCommonSceneRequestHelper.OnSubmitCallBack() {

                    @Override
                    public void onStart() {
                        findAndReplaceLoading(gptMessage, retryGptMessage, () -> refreshAdapter(false), true);
                        refreshAdapter(false);
                    }

                    @Override
                    public void onSubmitSuccess(String taskId) {
                        TLog.info(TAG, "retry success %s", taskId);
                    }

                    @Override
                    public void onSubmitFailed() {
                        replaceLoadingFailItem(false, "");
                        refreshAdapter(false);
                    }
                }, true);
            }
        }
    }

    @Override
    public void mySendAudioMessage(String text, String filePath, int duration) {
        if (LText.empty(text)) {
            ToastUtils.showText("您的指令中似乎没有文字内容,再说一次试试~");
            return;
        }
        preQueryText = text;
        dataMessage.add(getGptLocalAudioMessage(text, filePath, duration));
        refreshAdapter(true);
        mySendTextMessage(text, false, false, 1);
    }

    @Override
    public void mySendTextMessage(@Nullable String text, boolean appendText, boolean isSuggestText, int type) {
        cleanAllAppendData();

        GptMessage lastMessage = LList.getLastElement(dataMessage);
        if (lastMessage instanceof GptDeepChatMessage) {
            GptDeepChatMessage message = (GptDeepChatMessage) lastMessage;
            if (!message.localComplete) {
                message.localStop = true;
            }
            message.canRegenerate = false;
            stopMessageSet.add(message.taskId);
        }

        if (appendText) {
            dataMessage.add(getLocalMessage(text));
            refreshAdapter(true);
        }
        currentQueryText = text;
        btnRequired = false;
        if (iAdvantageSearchView != null) {
            iAdvantageSearchView.refreshLabels2("", null, "");
            iAdvantageSearchView.setInputPlaceHolder("试试提出你的要求");
        }
        requestHelper.submit(new JSONObject(), text, new AiCommonSceneRequestHelper.OnSubmitCallBack2() {

            @Override
            public void onStart() {
                /*只要点击了就需要添加loading，个别情况会出现接口还没有回来，消息先回来了*/
                dataMessage.add(getCustomTimeOutLoadingMessage(GptRetryMessage.obtainTextMessage(text, isSuggestText
                                ? GptRetryMessage.RetryTextMessage.CLICK_TEXT : GptRetryMessage.RetryTextMessage.INPUT_TEXT,
                        GptRetryMessage.RetryTextMessage.ERROR_TIME), () -> refreshAdapter(false), timeout));
                refreshAdapter(true);
            }

            @Override
            public void onSubmitSuccess(String taskId) {
                TLog.info(TAG, "mySendTextMessage success %s", taskId);
            }

            @Override
            public void onSubmitFailed(ErrorReason reason) {
                //收到该类型消息 需要替换loading为失败样式
                if (reason.getErrCode() == 5) { //敏感词异常
                    GptDeepChatMessage gptTextMessage = new GptDeepChatMessage();
                    gptTextMessage.text = reason.getErrReason();
                    gptTextMessage.sessionId = requestHelper.getSessionId();
                    gptTextMessage.localComplete = true;
                    replaceLoadMessage(gptTextMessage);
                } else {
                    replaceLoadingFailItem(false, "");
                    ToastUtils.showText(reason.getErrReason());
                }
                refreshAdapter(true);
            }
        });
        if (iAdvantageSearchView != null) {
            iAdvantageSearchView.refreshLabels("", null);
        }
    }

    private void replaceLoadMessage(GptMessage message) {
        ListIterator<GptMessage> gptMessageListIterator = dataMessage.listIterator();
        while (gptMessageListIterator.hasNext()) {
            GptMessage next = gptMessageListIterator.next();
            if (next instanceof GptLocalLoadingMessage) {
                gptMessageListIterator.set(message);
                break;
            }
        }
    }

    private GptLocalFailedMessage getNetErrorMessage(@NonNull GptRetryMessage retryMessage, boolean disableRetry, String gptText) {
        GptLocalFailedMessage localFailedMessage = new GptLocalFailedMessage();
        localFailedMessage.styleVersion = 1;
        localFailedMessage.retryGptMessage = retryMessage;
        localFailedMessage.notifyContent = LText.empty(gptText) ? "抱歉，目前服务器繁忙，请稍后再试" : gptText;
        if (disableRetry) {
            localFailedMessage.sceneStatus = GptMessage.STATUS_UNCHANGEABLE;
        }
        return localFailedMessage;
    }


    @Override
    public void handleReceiveAiMessage(@NonNull AiReceiveCommonSceneParamBean aiReceiveCommonSceneParamBean) {

        if (TextUtils.isEmpty(aiReceiveCommonSceneParamBean.taskId) || stopMessageSet.contains(aiReceiveCommonSceneParamBean.taskId)) {
            return;
        }

        cleanAllAppendData();
        btnRequired = false;
        try {
            JSONObject jsonObject = new JSONObject(aiReceiveCommonSceneParamBean.content);
            int mediaType = jsonObject.optInt("mediaType");
            if (mediaType == GptMessage.MEDIA_TYPE_NOTIFY) {
                dealNotifyMessage((GptNotifyMessage) parserGptMessage(GptMessage.MEDIA_TYPE_NOTIFY, aiReceiveCommonSceneParamBean.content));
            }

            if (mediaType == GptMessage.MEDIA_TYPE_LIST) {
                handleListTypeMessage(jsonObject, aiReceiveCommonSceneParamBean, false);
            }


            if (iAdvantageSearchView != null) {
                refreshAdapter(iAdvantageSearchView.isVisibleLastItem());
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }

    }


    private void dealNotifyMessage(GptNotifyMessage gptNotifyMessage) {
        if (gptNotifyMessage != null) {
            if (gptNotifyMessage.notifyType == GptNotifyMessage.NOTIFY_TYPE_ERROR) {
                replaceLoadingFailItem(gptNotifyMessage.isHideButton(), gptNotifyMessage.notifyContent);
            }
        }
    }


    @Override
    protected void replaceLoadingFailItem(boolean disableRetry, String gptText) {
        //收到该类型消息 需要替换loading为失败样式
        ListIterator<GptMessage> gptMessageListIterator = dataMessage.listIterator();
        while (gptMessageListIterator.hasNext()) {
            GptMessage next = gptMessageListIterator.next();
            if (next instanceof GptLocalLoadingMessage) {
                gptMessageListIterator.set(getNetErrorMessage(((GptLocalLoadingMessage) next).retryGptMessage, disableRetry, gptText));
            }
        }
    }

    @Override
    protected void replaceLoadingIndex(@NonNull GptMessage gptMessage) {
        //开聊语超时不接受后续返回的
        if (dataMessage.size() == 1 && dataMessage.get(0) instanceof GptLocalFailedMessage) return;
        ListIterator<GptMessage> gptMessageListIterator = dataMessage.listIterator();
        while (gptMessageListIterator.hasNext()) {
            GptMessage next = gptMessageListIterator.next();
            //Loading消息做替换
            if (next instanceof GptLocalLoadingMessage) {
                //关闭loading记录倒计时
                GptLocalLoadingMessage localLoadingMessage = (GptLocalLoadingMessage) next;
                localLoadingMessage.removeListener();
                gptMessageListIterator.set(gptMessage);
                return;
            }
            //同一个消息做替换
            if (LText.equal(next.taskId, gptMessage.taskId)
                    && LText.equal(next.responseId, gptMessage.responseId)) {
                //缓存里面的消息已经是完成状态，不替换，防止最后来个空消息
                gptMessageListIterator.set(gptMessage);
                return;
            }
        }
        dataMessage.add(gptMessage);
    }

    protected @Nullable GptMessage parserGptMessage(int mediaType, String content) {

        if (mediaType == GptMessage.MEDIA_TYPE_TEXT) {
            return GsonUtils.fromJson(content, GptDeepChatMessage.class);
        }

        if (mediaType == GptMessage.MEDIA_TYPE_SUGGEST2) {
            return GsonUtils.fromJson(content, GptSuggestMessage2.class);
        }

        if (mediaType == GptMessage.MEDIA_TYPE_NOTIFY) {
            return GsonUtils.fromJson(content, GptNotifyMessage.class);
        }

        if (mediaType == GptMessage.MEDIA_TYPE_BUTTON) {
            return GsonUtils.fromJson(content, GptButtonMessage.class);
        }

        if (mediaType == GptMessage.MEDIA_TYPE_LIST_RESULT) {
            GptListResultMessage listResultMessage = GsonUtils.fromJson(content, GptListResultMessage.class);
            if (listResultMessage != null) {
                listResultMessage.parserUpScaleGeekJobInfo();
            }
            return listResultMessage;
        }
        return null;
    }

    /**
     * 删除所有本地添加上去的item
     */
    private void cleanAllAppendData() {
        ListIterator<GptMessage> gptMessageListIterator = dataMessage.listIterator();
        while (gptMessageListIterator.hasNext()) {
            /*需要把前面所有的建议词  "赞/踩"  都 删除*/
            GptMessage next = gptMessageListIterator.next();
            if (next instanceof GptLocalLikeUnLikeMessage
                    || next instanceof GptSuggestMessage) {
                gptMessageListIterator.remove();
            }
            /*删除全部建议词*/
            if (next instanceof GptButtonMessage) {
                gptMessageListIterator.remove();
            }

            if (next instanceof GptLocalFailedMessage) {
                GptLocalFailedMessage message = (GptLocalFailedMessage) next;
                message.sceneStatus = GptMessage.STATUS_UNCHANGEABLE;
            }
        }
    }

    private void refreshAdapter(boolean smoothBottom) {
        App.get().getMainHandler().post(() -> {
            adapterDataMessage.clear();
            adapterDataMessage.addAll(dataMessage);
            if (iAdvantageSearchView != null) {
                iAdvantageSearchView.refreshAdapter(adapterDataMessage, smoothBottom, true, false);
            }
        });
    }


    private void refreshAdapter(boolean smoothBottom, boolean canRefresh, boolean isUpdateNow) {
        App.get().getMainHandler().post(() -> {
            adapterDataMessage.clear();
            adapterDataMessage.addAll(dataMessage);
            if (iAdvantageSearchView != null) {
                iAdvantageSearchView.refreshAdapter(adapterDataMessage, smoothBottom, canRefresh, isUpdateNow);
            }
        });
    }


    private void refreshAdapter(int position, boolean canRefresh) {
        App.get().getMainHandler().post(() -> {
            adapterDataMessage.clear();
            adapterDataMessage.addAll(dataMessage);
            if (iAdvantageSearchView != null) {
                iAdvantageSearchView.refreshAdapter(adapterDataMessage, position, canRefresh);
            }
        });
    }

    public boolean canOperate() {
        return !isRecommend && !btnRequired && !isLoadingStatus();
    }

    @Override
    public boolean isLoadingStatus() {
        int count = LList.getCount(dataMessage);
        for (int i = count; i >= 0; i--) {
            GptMessage gptMessage = LList.getElement(dataMessage, i);
            if (gptMessage instanceof GptLocalLoadingMessage) {
                return true;
            }
        }
        return false;
    }

    public void loadHistoryMessages(boolean first, Runnable runnable) {
        String encTaskId = "";
        String encSessionId = "";
        GptMessage firstElement = LList.getFirstElement(dataMessage);
        if (firstElement != null) {
            encTaskId = firstElement.taskId;
            encSessionId = firstElement.sessionId;
        }

        SimpleApiRequest request = SimpleApiRequest.GET(BossUrlConfig.URL_BOSS_INTENTION_ASSISTANT_HISTORY);
        request.addParam("encTaskId", encTaskId);
        request.addParam("encSessionId", encSessionId);
        request.addParam("encJobId", aiCommonSceneBundleBean.jobId);
        request.setRequestCallback(new SimpleCommonApiRequestCallback<AiHistoryResponse>() {
            @Override
            public void onSuccess(ApiData<AiHistoryResponse> data) {
                if (data.resp == null || LList.isEmpty(data.resp.data)) {
                    refreshAdapter(false, data.resp == null || data.resp.hasMore, false);
                    return;
                }

                if (first) {
                    dataMessage.clear();
                    recommendMessageSet.clear();
                    refreshAdapter(true, false, false);
                }

                List<AiHistoryResponse.AiHistoryMessage> historyMessages = data.resp.data;
                int oldSize = LList.getCount(dataMessage);
                for (int i = 0 ; i < LList.getCount(historyMessages); i++) {
                    AiHistoryResponse.AiHistoryMessage message = historyMessages.get(i);
                    if (message.type == 1) {
                        if (message.resubmit != 1 && !TextUtils.isEmpty(message.content)) {
                            // 用户消息
                            GptLocalTextMessage localMessage = getLocalMessage(message.content);
                            localMessage.receiveTime = message.createTime;
                            localMessage.sessionId = message.encSessionId;
                            localMessage.taskId = message.encTaskId;
                            dataMessage.add(0, localMessage);
                        }
                    } else {
                        // AI响应消息
                        try {
                            AiReceiveCommonSceneParamBean aiReceiveCommonSceneParamBean = new AiReceiveCommonSceneParamBean();
                            aiReceiveCommonSceneParamBean.content = message.content;
                            aiReceiveCommonSceneParamBean.sessionId = message.encSessionId;
                            aiReceiveCommonSceneParamBean.receiveTime = message.createTime;
                            aiReceiveCommonSceneParamBean.taskId = message.encTaskId;
                            handleReceiveHistoryAiMessage(aiReceiveCommonSceneParamBean);

                        } catch (Exception e) {
                            TLog.error(TAG, e.getMessage());
                        }
                    }
                }
                if (first) {
                    refreshAdapter(true, data.resp.hasMore, true);
                } else {
                    refreshAdapter(LList.getCount(dataMessage) - oldSize, data.resp.hasMore);
                }

                if (runnable != null) {
                    runnable.run();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                super.onFailed(reason);
                ToastUtils.showText(reason.getErrReason());
                refreshAdapter(false, true, false);
            }
        });
        request.execute();
    }

    public void handleReceiveHistoryAiMessage(@NonNull AiReceiveCommonSceneParamBean aiReceiveCommonSceneParamBean) {
        try {
            JSONObject jsonObject = new JSONObject(aiReceiveCommonSceneParamBean.content);
            int mediaType = jsonObject.optInt("mediaType");

            if (mediaType == GptMessage.MEDIA_TYPE_LIST) {
                handleListTypeMessage(jsonObject, aiReceiveCommonSceneParamBean, true);
            }

        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }


    private void handleListTypeMessage(JSONObject jsonObject, AiReceiveCommonSceneParamBean paramBean, boolean isHistory) {
        JSONArray itemObjectsArray = jsonObject.optJSONArray("items");
        if (itemObjectsArray == null || itemObjectsArray.length() == 0) {
            return;
        }

        List<GptMessage> tempList = new ArrayList<>();
        for (int i = 0; i < itemObjectsArray.length(); i++) {
            JSONObject itemObject = extractItemObject(itemObjectsArray, i);
            if (itemObject == null) continue;

            int mediaTypeItem = itemObject.optInt("mediaType");
            GptMessage gptMessage = parserGptMessage(mediaTypeItem, itemObject.toString());

            if (gptMessage == null || gptMessage instanceof GptNotifyMessage) continue;

            if (gptMessage instanceof GptDeepChatMessage) {
                GptDeepChatMessage deepChatMessage = null;
                if (!TextUtils.isEmpty(((GptDeepChatMessage) gptMessage).text)){
                    deepChatMessage = handleDeepChatTextMessage(gptMessage, jsonObject, paramBean, isHistory);
                }

                try {
                 JSONObject textExtend = gptMessage.getExtend();
                 if (textExtend != null && textExtend.optInt("type", 0) == 1 && !isHistory && !(LList.getLastElement(dataMessage) instanceof GptLocalLoadingMessage)) {
                     tempList.add(getCustomTimeOutLoadingMessage(GptRetryMessage.obtainTextMessage("",
                             GptRetryMessage.RetryTextMessage.INPUT_TEXT,
                             GptRetryMessage.RetryTextMessage.ERROR_TIME), () -> refreshAdapter(false), 10 * 60 * 1000,
                             textExtend.optString("loadingText", loadingText)));
                     isRecommend = true;
                 }
                } catch (Exception e) {
                    TLog.error(TAG, e.getMessage());
                }
                if (deepChatMessage != null) {
                    tempList.add(deepChatMessage);
                }
                continue;
            }

            if (gptMessage instanceof GptListResultMessage) {
                AiUpScaleGeekJobCardBean aiUpScaleGeekJobCardBean = ((GptListResultMessage) gptMessage).aiUpScaleGeekJobCardBean;

                //同样的推荐只展示一次
                if (aiUpScaleGeekJobCardBean != null) {
                    if (recommendMessageSet.contains(aiUpScaleGeekJobCardBean.encRecommendId)) {
                       return;
                    }
                    recommendMessageSet.add(aiUpScaleGeekJobCardBean.encRecommendId);
                }

                if (!isHistory) {
                    isRecommend = false;
                    reportJobExport((GptListResultMessage) gptMessage);
                    GptMessage lastElement = LList.getLastElement(dataMessage);
                    if (lastElement instanceof GptLocalLoadingMessage) {
                        LList.delElement(dataMessage, lastElement);
                    }
                }
            }

            if (gptMessage instanceof GptSuggestMessage2 && iAdvantageSearchView != null && !isHistory) {
                iAdvantageSearchView.refreshLabels2("", ((GptSuggestMessage2) gptMessage).suggestList, "");
                continue;
            }

            if (gptMessage instanceof GptButtonMessage) {
                if (!isHistory || dataMessage.isEmpty()) {
                    dealGptButtonMessage((GptButtonMessage) gptMessage);
                } else {
                    continue;
                }
            }

            gptMessage.parserActionInfo(paramBean);
            tempList.add(gptMessage);


        }

        if (paramBean.complete && iAdvantageSearchView != null) {
            iAdvantageSearchView.onMsgComplete();
        }

        if (isHistory) {
            dataMessage.addAll(0, tempList);
        } else {
           for (int i = 0; i < tempList.size(); i++) {
               if (i == 0) {
                   replaceLoadingIndex(tempList.get(i));
               } else {
                   dataMessage.add(tempList.get(i));
               }
           }
        }


        refreshAdapter(true);
    }

    private void dealGptButtonMessage(GptButtonMessage gptButtonMessage) {
        try {
            JSONObject jsonObject = gptButtonMessage.getExtend();
            if (!TextUtils.isEmpty(jsonObject.optString("inputPlaceHolder")) && iAdvantageSearchView != null) {
                iAdvantageSearchView.setInputPlaceHolder(jsonObject.optString("inputPlaceHolder"));
            }

            btnRequired = jsonObject.optInt("btnRequired") == 1;

        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }

    private void reportJobExport(GptListResultMessage message) {
        if (message.aiUpScaleGeekJobCardBean != null) {
            SimpleApiRequest.POST(BossUrlConfig.URL_BOSS_INTENTION_ASSISTANT_RECOMMEND_CARD_EXPOSED)
                    .addParam("encRecommendId", message.aiUpScaleGeekJobCardBean.encRecommendId)
                    .execute();
        }
    }

    private GptDeepChatMessage handleDeepChatTextMessage(GptMessage gptMessage, JSONObject jsonObject,
                                              AiReceiveCommonSceneParamBean paramBean, boolean history) {
        GptDeepChatMessage deepChatMessage = (GptDeepChatMessage) gptMessage;
        deepChatMessage.parserBaseInfo(jsonObject);
        deepChatMessage.parserActionInfo(paramBean);
        if (history) {
            deepChatMessage.localComplete = true;
        }

        if (!history) {
            // 点击停止后，有可能还会吐一会流
            if (!stopMessageSet.contains(gptMessage.taskId)) {
                if (iAdvantageSearchView != null) {
                    iAdvantageSearchView.onMsgReceive();
                }
            }

            if (deepChatMessage.localComplete && iAdvantageSearchView != null) {
                stopMessageSet.add(deepChatMessage.taskId);
                iAdvantageSearchView.onMsgComplete();
            }
        }
        return dealGptDeepChatMessage(deepChatMessage, history);
    }

    private GptDeepChatMessage dealGptDeepChatMessage(@NonNull GptDeepChatMessage gptDeepChatMessage, boolean isHistory) {
        if (!isHistory) {
            GptMessage lastMessage = LList.getLastElement(dataMessage);
            if (lastMessage instanceof GptLocalLoadingMessage) {
                GptLocalLoadingMessage localLoadingMessage = (GptLocalLoadingMessage) lastMessage;
                localLoadingMessage.onDestroy();
                LList.delElement(dataMessage, lastMessage);
            }
            if (lastMessage instanceof GptLocalFailedMessage) {
                GptLocalFailedMessage localFailedMessage = (GptLocalFailedMessage) lastMessage;
                if (localFailedMessage.sceneStatus != GptMessage.STATUS_UNCHANGEABLE) {
                    LList.delElement(dataMessage, lastMessage);
                }
            }
            if (lastMessage instanceof GptDeepChatMessage) {
                GptDeepChatMessage deepChatMessageLast = (GptDeepChatMessage) lastMessage;
                if (LText.equal(deepChatMessageLast.taskId, gptDeepChatMessage.taskId)) {
                    //同一个消息
                    if (deepChatMessageLast.localComplete) return null;
                    gptDeepChatMessage.canRegenerate = deepChatMessageLast.canRegenerate;
                    gptDeepChatMessage.localStop = deepChatMessageLast.localStop;
                    gptDeepChatMessage.isExpand = deepChatMessageLast.isExpand;
                    gptDeepChatMessage.startTime = deepChatMessageLast.startTime;
                    gptDeepChatMessage.endTime = deepChatMessageLast.endTime;
                    deepChatMessageLast.onDestroy();
                    LList.delElement(dataMessage, deepChatMessageLast);
                } else {
                    //不同的消息
                    if (!lastMessage.localComplete) return null;
                }
            }
        }
        return processGptDeepChatMessage(gptDeepChatMessage);
    }

    private GptDeepChatMessage processGptDeepChatMessage(@NonNull GptDeepChatMessage gptDeepChatMessage) {
        gptDeepChatMessage.modelIcon = "";
        if (gptDeepChatMessage.startTime == 0) {
            gptDeepChatMessage.startTime = System.currentTimeMillis();
        }
        if (gptDeepChatMessage.endTime == 0 && gptDeepChatMessage.getStatus() == AiDeepChatView.Status.MARKDOWN_RUNNING) {
            gptDeepChatMessage.endTime = System.currentTimeMillis();
        }
        if (!gptDeepChatMessage.localComplete) {
            gptDeepChatMessage.startTimeOut(timeout, () -> {
                gptDeepChatMessage.onDestroy();
                int index = dataMessage.indexOf(gptDeepChatMessage);
                if (index > -1) {
                    dataMessage.set(index, getNetErrorMessage(
                            GptRetryMessage.obtainTextMessage(currentQueryText, GptRetryMessage.RetryTextMessage.INPUT_TEXT, GptRetryMessage.RetryTextMessage.ERROR_TIME),
                            false,
                            ""
                    ));
                    if (iAdvantageSearchView != null) {
                        iAdvantageSearchView.onMsgFail();
                    }
                }
                refreshAdapter(true);
            });
        }
        return gptDeepChatMessage;
    }


    public void stopGptMessage() {
        GptMessage lastMessage = LList.getLastElement(dataMessage);
        stopMessageSet.add(lastMessage.taskId);
        if (lastMessage instanceof GptDeepChatMessage) {
            GptDeepChatMessage message = (GptDeepChatMessage) lastMessage;
            message.localStop = true;
            message.canRegenerate = true;
            message.onDestroy();
            requestHelper.stopSession(message, new JSONObject());
        } else {
            removeLastLoadingMessage();
        }
        refreshAdapter(true);
    }

    private void removeLastLoadingMessage() {
        GptMessage lastMessage = LList.getLastElement(dataMessage);
        if (lastMessage instanceof GptLocalLoadingMessage) {
            GptLocalLoadingMessage localLoadingMessage = (GptLocalLoadingMessage) lastMessage;
            localLoadingMessage.onDestroy();
            LList.delElement(dataMessage, lastMessage);
            refreshAdapter(true);
        }
    }
    public void regenerateMessage(GptMessage gptMessage) {
        if (gptMessage == null) {
            return;
        }
        GptMessage lastMessage = LList.getLastElement(dataMessage);
        if (lastMessage instanceof GptDeepChatMessage) {
            GptDeepChatMessage gptDeepChatMessage = (GptDeepChatMessage) lastMessage;
            gptDeepChatMessage.onDestroy();
            if (gptDeepChatMessage.taskId.equals(requestHelper.getStartTaskId())) {
                LList.delElement(dataMessage, lastMessage);
                initSessionId(true, null, null);
            } else {
                LList.delElement(dataMessage, lastMessage);
                mySendTextMessage(currentQueryText, false, false, 2);
            }
            refreshAdapter(true);
        }
    }


    @Override
    public String getLoadingDispatchTouchEventText() {
        return isLoadingStatus() ? super.getLoadingDispatchTouchEventText() : "";
    }


    @Override
    public void onDestroy() {
        iAdvantageSearchView = null;
        super.onDestroy();
    }
}
