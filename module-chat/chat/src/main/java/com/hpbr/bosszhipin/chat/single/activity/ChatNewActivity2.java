package com.hpbr.bosszhipin.chat.single.activity;

import static com.hpbr.bosszhipin.data.db.entry.ContactBean.FROM_BOSS;
import static com.hpbr.bosszhipin.data.db.entry.ContactBean.FROM_DIAN_ZHANG;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_DOUBLE_CHAT_LABEL_CLICK;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_DOUBLE_CHAT_LABEL_PUSH;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_LOCATION_SEND;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_WINDOW_MORE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CLICK_CHAT_POSITION;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_NLP_SHOW;
import static com.hpbr.bosszhipin.module.contacts.constant.IntentParamConstant.FROM_FAST_HANDLE_CHAT;
import static com.hpbr.bosszhipin.module.my.activity.boss.location.SelectWorkLocationActivity.SELECT_WORK_CHAT_SOURCE;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Pair;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.chat.R;
import com.hpbr.bosszhipin.chat.adapter.ChatAdapter2;
import com.hpbr.bosszhipin.chat.adapter.itf.IAdapterCallBack;
import com.hpbr.bosszhipin.chat.adapter.itf.IRefreshAdapterCallBack;
import com.hpbr.bosszhipin.chat.adapter.itf.ISendUserCallBack;
import com.hpbr.bosszhipin.chat.banner.toptips.ChatTopStatusView;
import com.hpbr.bosszhipin.chat.banner.toptips.IUserEnter;
import com.hpbr.bosszhipin.chat.constant.ChatConstant;
import com.hpbr.bosszhipin.chat.dialog.ContactRemarkManagerDialog;
import com.hpbr.bosszhipin.chat.entity.PanItemType;
import com.hpbr.bosszhipin.chat.exchange.ExchangePhoneManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeRequireResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeSendResumeManager;
import com.hpbr.bosszhipin.chat.exchange.ExchangeWeChatManager;
import com.hpbr.bosszhipin.chat.export.constant.SingleConstant;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.chat.export.routerservice.IChatNlpTransferService;
import com.hpbr.bosszhipin.chat.nlp.DailyGuideBean;
import com.hpbr.bosszhipin.chat.nlp.DispatchSuggestCallBack;
import com.hpbr.bosszhipin.chat.nlp.IChatNLPObserver;
import com.hpbr.bosszhipin.chat.nlp.NLPContainerLayout;
import com.hpbr.bosszhipin.chat.nlp.NLPListBean;
import com.hpbr.bosszhipin.chat.nlp.OnSuggestCallBack;
import com.hpbr.bosszhipin.chat.nlp.view.NormalView;
import com.hpbr.bosszhipin.chat.single.BossBlockCheck;
import com.hpbr.bosszhipin.chat.single.ChatCommon;
import com.hpbr.bosszhipin.chat.single.dialog.ChatEditGreetingWordDialog;
import com.hpbr.bosszhipin.chat.single.listener.IChatCommon;
import com.hpbr.bosszhipin.chat.single.listener.OnBindAdapterCallBack;
import com.hpbr.bosszhipin.chat.single.presenter.chat2.ChatBottomFuncPresenter2;
import com.hpbr.bosszhipin.chat.single.presenter.chat2.IChatBottomFunView2;
import com.hpbr.bosszhipin.chat.single.presenter.chat2.exchange.BossRoleChatDZUserPresenter;
import com.hpbr.bosszhipin.chat.single.presenter.chat2.exchange.GeekRoleChatDZUserPresenter;
import com.hpbr.bosszhipin.chat.utils.AudioCovertTextUtils;
import com.hpbr.bosszhipin.chat.utils.ChatBlockManager;
import com.hpbr.bosszhipin.chat.utils.ExchangeMessage;
import com.hpbr.bosszhipin.chat.utils.SingleChatOnlineUtil;
import com.hpbr.bosszhipin.chat.utils.UploadChatManager;
import com.hpbr.bosszhipin.chat.view.NLPDispatchLayout;
import com.hpbr.bosszhipin.chat.views.ChatAudioView;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.ChatWechatDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.NLPSuggestBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.interviews.export.InterviewsRouter;
import com.hpbr.bosszhipin.listener.Callback;
import com.hpbr.bosszhipin.listener.OnZPUIChatGifLongClickListener;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.contacts.adapter.listener.OnMessageSendCallBack;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.exchange.RejectHttpManager;
import com.hpbr.bosszhipin.module.contacts.exchange.UserRejectManager;
import com.hpbr.bosszhipin.module.contacts.manager.CommonWordManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.contacts.service.ChatSendCallback;
import com.hpbr.bosszhipin.module.contacts.sounds.SoundFile;
import com.hpbr.bosszhipin.module.contacts.util.ChatUtils;
import com.hpbr.bosszhipin.module.interview.api.GetInterviewDetailResponse;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCreateIntentBean;
import com.hpbr.bosszhipin.module.interview.entity.InterviewParams;
import com.hpbr.bosszhipin.module.interview.utils.InterviewCommonUtils;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.my.activity.boss.location.SelectWorkLocationActivity;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.CloneUtils;
import com.hpbr.bosszhipin.utils.LiveDataBus;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.ContactExchangeChatView;
import com.hpbr.bosszhipin.views.MessageSlideView;
import com.hpbr.bosszhipin.views.chatbottom2.commonwords.CommonWordsUIBean;
import com.hpbr.bosszhipin.views.chatbottom2.depends.single.DianZhangUserLayout;
import com.hpbr.bosszhipin.views.exchange.depend.DZUserExchangeContainer;
import com.hpbr.bosszhipin.views.exchange.icon.OnExchangeCallBack;
import com.hpbr.bosszhipin.views.exchange.utils.ExchangeHelper;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.signer.Signer;
import com.twl.ui.ToastUtils;
import com.twl.ui.ZPUIBadgeUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.BossGreetingGuideBean;
import net.bosszhipin.api.UnlockChatRequest;
import net.bosszhipin.api.UnlockChatResponse;
import net.bosszhipin.api.bean.NLPSuggestResultBean;
import net.bosszhipin.api.bean.ServerVipItemBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import message.handler.MSGSender;
import message.handler.MessageTargetInfo;
import message.handler.MessageUtils;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;
import message.server.connect.ConnectStatus;
import zpui.lib.ui.badge.ZPUIBadgeView;
import zpui.lib.ui.refreshlayout.ZPUIRefreshLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Created by monch on 15/11/4.
 * 店长聊天页面
 * <p>
 * 只有常用语
 * 表情只有内置emotion
 * 没有消息撤回
 * 更多只有 拍照和语音
 */
@RouterUri(path = SingleConstant.PATH.SINGLE_CHAT_DIAN_ZHANG_USER)
public class ChatNewActivity2 extends ChatBaseActivity implements IChatCommon,
        OnBindAdapterCallBack, IChatNLPObserver {

    private ChatCommon common;
    private AppTitleView titleView;

    // 聊天列表控件
    private RecyclerView mRecycleView;

    private ZPUIRefreshLayout mZpuiRefreshLayout;


    private ChatTopStatusView tipStatusView;
    private View mCoverBlockView;
    // 默认标题栏内容
    private String title = "";

    //用于加好友的friendId，可能是0
    private long tempFriendId;

    private String from;

    private DianZhangUserLayout bottomFunctionView;

    private DZUserExchangeContainer mTempExchangeLayout;

    private MessageSlideView slideView;
    private long jobId;
    private ChatBlockManager chatBlockManager;
    private String defaultInputText;
    private String securityId;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //注册监听NLP接收器
        IChatNlpTransferService nlpTransferService = SingleRouter.getNlpTransferService();
        if (nlpTransferService != null) {
            nlpTransferService.register(this);
        }

        registerVoiceObserver();
    }

    private void registerVoiceObserver() {

        String friendKey = common.getFriendId() + "_" + common.getFriendSource();
        LiveDataBus.multi(ChannelConstants.SUCCESS_DOWNLOAD_AUDIO_FILE, String.class).observe(this, url -> {
            Pair<String, ChatBean> paramPair;
            paramPair = AudioCovertTextUtils.getInstance().mReceiveAudioMsgMap.get(SoundFile.getInstance().getFileName(String.valueOf(url)));

            if (paramPair == null) {
                paramPair = AudioCovertTextUtils.getInstance().mOnClickAudioMsgMap.get(SoundFile.getInstance().getFileName(String.valueOf(url)));
            }

            String fileParent = PathUtils.getCacheDirChildPathExternalFirst("sound");
            File fileSound = FileUtils.getFileByPath(fileParent, SoundFile.getInstance().getFileName((String) url));

            if (paramPair != null && paramPair.first.startsWith(friendKey) && paramPair.second != null) {
                AudioCovertTextUtils.getInstance().convertAudioToText(new AudioCovertTextUtils.AudioCovertTextParam(paramPair.first, fileSound.getPath(), paramPair.second.msgId, paramPair.second));
                common.soundFirstTrans(paramPair.second.msgId, false);
            }

        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_COVERT_RESULT).observe(this, obj -> {
            if (chatAdapter2 == null || LList.isEmpty(chatAdapter2.getData())) return;
            Pair<String, AudioCovertTextUtils.AudioCovertTextParam> param = (Pair<String, AudioCovertTextUtils.AudioCovertTextParam>) obj;
            AudioCovertTextUtils.AudioCovertTextParam audioCovertTextParam = param.second;
            String text = param.first;
            if (audioCovertTextParam.getFriendKey().startsWith(friendKey)) {
                common.updateAudioMessage(audioCovertTextParam.getMid(), text, param.second.isNeedUpdate());
            }
        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_FIRST_PLAY).observe(this, mid -> {
            common.soundFirstPlay((Long) mid);
        });

        LiveDataBus.multi(ChannelConstants.RECEIVE_AUDIO_FIRST_TRANS).observe(this, param -> {
            Pair<Long, Boolean> paramPair = (Pair<Long, Boolean>) param;
            common.soundFirstTrans(paramPair.first, paramPair.second);
        });

    }


    private void checkEncryMessage() {
        obtainFriendId(contactBean -> {
            UnlockChatRequest request = new UnlockChatRequest(new ApiRequestCallback<UnlockChatResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    showProgressDialog("加载中");
                }

                @Override
                public void onSuccess(ApiData<UnlockChatResponse> data) {
                    final UnlockChatResponse resp = data.resp;
                    if (resp.block && resp.page != null) {
                        ParamBean paramBean = new ParamBean();
                        paramBean.userId = contactBean.friendId;
                        BusinessPageRouter.jumpToChatLimitActivity(ChatNewActivity2.this, paramBean, true, resp.page);
                    } else {
                        T.ss(resp.vipChatToast);
                    }
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            request.securityId = contactBean.securityId;
            HttpExecutor.execute(request);
        });
    }

    /**
     * 快速处理 是否显示过 键盘
     */
    private boolean hasShowFastHandlerKeyBoard;

    private String nlpSuggestValue;

    private final OnSuggestCallBack onSuggestCallBack = new OnSuggestCallBack() {

        @Override
        public void sendLocationToGeek(long id, String type, String label) {//发送位置到牛人
            obtainFriendId(contactBean -> {

                NLPContainerLayout.sendNLPLocationAction(contactBean.friendId, id, jobId);
                AnalyticsFactory.create().action(ACTION_CHAT_LOCATION_SEND)
                        .param("p2", label)
                        .param("p", "" + contactBean.friendId).build();
                noticeCleanNLPMessage(id, type, true);

            });

        }

        @Override
        public void exchangePhone(long id, String type, String label, @Nullable NLPSuggestResultBean nlpSuggestResultBean) {//交换电话
            noticeCleanNLPMessage(id, type, true);
            if (onAgreeExchange(LText.getInt(type)) >= 0) {
                return;
            }
            onExchangePhoneListener(nlpSuggestResultBean);
        }

        @Override
        public void exchangeWeiChat(long id, String type, String label, @Nullable NLPSuggestResultBean nlpSuggestResultBean) {//交换微信
            obtainFriendId(contactBean -> {
                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                if (onAgreeExchange(LText.getInt(type)) >= 0) {
                    return;
                }

                ExchangeWeChatManager exchangeWeChatManager = new ExchangeWeChatManager(ChatNewActivity2.this, contactBean);
                exchangeWeChatManager.setScene(nlpSuggestResultBean != null ? nlpSuggestResultBean.scene : 0);
                exchangeWeChatManager.setCallBack(new ExchangeWeChatManager.OnExchangeWeChatCallBack() {
                    @Override
                    public void onSendLocalWeChatListener(ChatBean chatBean) {
                        sendWechatNumberTextMessage(false);
                    }

                    @Override
                    public void onRefreshExchangeViewListener() {
                        refreshExchange();
                    }
                });
                exchangeWeChatManager.onExchangeWeChatListener();
            });
        }

        @Override
        public void sendResume(long id, String type, String label) {//发送简历
            obtainFriendId(contactBean -> {
                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                if (onAgreeExchange(LText.getInt(type)) >= 0) {
                    return;
                }

                ExchangeSendResumeManager sendResumeManager = new ExchangeSendResumeManager(ChatNewActivity2.this, contactBean);
                sendResumeManager.setCallBack(ChatNewActivity2.this::refreshExchange);
                sendResumeManager.onChatSendResumeListener();
            });
        }

        @Override
        public void requestResume(long id, String type, String label) {//请求简历
            obtainFriendId(contactBean -> {
                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                if (onAgreeExchange(LText.getInt(type)) >= 0) {
                    return;
                }

                ExchangeRequireResumeManager exchangeRequireResumeManager = new ExchangeRequireResumeManager(ChatNewActivity2.this, contactBean);
                exchangeRequireResumeManager.setCallBack(() -> refreshExchange());
                exchangeRequireResumeManager.onRequireResumeListener();
            });
        }

        @Override
        public void interview(long defaultTime, long id, String type, String label) {//约面试
            obtainFriendId(contactBean -> {
                //隐藏NLP消息
                noticeCleanNLPMessage(id, type, true);
                if (UserManager.isBossRole()) {
                    checkInterviewJump(defaultTime);
                } else {
                    ApmAnalyzer.create().action("action_temp", ApmAnalyticsAction.ACTION_INTERVIEW_CREATE_BY_GEEK)
                            .p2("ChatNewActivity2").report();
                    InterviewsRouter.startGeekCreateInterview(ChatNewActivity2.this, InterviewCommonUtils.createContactBean(contactBean));
                }
            });
        }

        @Override
        public void sendText(String text, long id, String type, String label) {//发送文本
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(text)) {
                common.sendTextMessage(text, false);
            }
        }

        @Override
        public void protocolJump(String protocol, long id, String type, String label) {//协议跳转
            //隐藏NLP消息
            noticeCleanNLPMessage(id, type, true);
            if (!LText.empty(protocol)) {
                new ZPManager(ChatNewActivity2.this, protocol).handler();
            }
        }

        @Override
        public void onCloseNlpListener(long id, String suggestions, String label, String resident) {
            //点击关闭NLP按钮,通知服务区
            onDeleteNlpMessage(id, suggestions);
            //隐藏nlp控件
            mNlpView.setNlpGone();
        }

        @Override
        public void onDaily(long id, String suggestions, String label, String value) {

        }

        @Override
        public void backChat(long id, String suggestions, String label, String value) {

        }

        @Override
        public void changeJob(long id, String suggestions, String label, String value) {

        }

        @Override
        public void dailyGuide(long id, String suggestions, String label, String value, DailyGuideBean dailyGuideBean) {
            noticeCleanNLPMessage(id, suggestions, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false);
            }
        }

        @Override
        public void soundCall(long id, String suggestions, String label, String value) {

        }

        @Override
        public void onSuggestClickListener(long id, String value) {

            obtainFriendId(contactBean -> AnalyticsFactory
                    .create()
                    .action(ACTION_CHAT_DOUBLE_CHAT_LABEL_CLICK)
                    .param("p", contactBean.friendId)
                    .param("p2", jobId)
                    .param("p3", contactBean.jobIntentId)
                    .param("p4", value)
                    .param("p5", bottomFunctionView.getInputEditText())
                    .build());

            bottomFunctionView.setEditInputText(value);
            nlpSuggestValue = value;
        }

        @Override
        public void livingSuggest(long id, String type, String label, String value) {
            noticeCleanNLPMessage(id, type, true);
            if (!TextUtils.isEmpty(value)) {
                common.sendTextMessage(value, false);
            }
        }

        @Override
        public void sendVideoGreatText(String content) {
            if (!TextUtils.isEmpty(content)) {
                bottomFunctionView.setEditInputText(content);
                bottomFunctionView.setShowKeyboard();
            }
        }

        @Override
        public void sendChatEmotionText(String content) {
            common.sendTextMessage(content, false);
        }

        @Override
        public void acceptExchangePhone(long reqMsgId) {
            common.onAcceptExchangePhoneOrWx(reqMsgId);
        }

        @Override
        public void acceptExchangeWeChat(long reqMsgId) {
            common.onAcceptExchangePhoneOrWx(reqMsgId);
        }

        @Override
        public void sendVerbalInterviewToGeek(String protocol) {
            new ZPManager(ChatNewActivity2.this, protocol).handler();
        }
    };


    //面试请求接口 决定跳转到那个界面
    private void checkInterviewJump(long defaultTime) {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                InterviewsRouter.getInterviewDetailByBoss(0, "", contactBean.securityId, "6", new ApiRequestCallback<GetInterviewDetailResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(ApiData<GetInterviewDetailResponse> data) {
                        GetInterviewDetailResponse resp = data.resp;
                        if (resp.bizCode == 100156) {
                            String applyAddtion = null;
                            String applyTip = null;
                            if (resp.geekApplyInfo != null) {
                                applyAddtion = resp.geekApplyInfo.applyAddition;
                                applyTip = resp.geekApplyInfo.applyTip;
                            }
                            InterviewCreateIntentBean bean = new InterviewCreateIntentBean(InterviewCommonUtils.createContactBean(contactBean), 2);
                            bean.setDefaultTimeLong(defaultTime).setApplyAddition(applyAddtion).setApplyTip(applyTip);
                            InterviewsRouter.tryCreateInterview(ChatNewActivity2.this, bean);
                        } else {
                            InterviewParams params = new InterviewParams();
                            params.interviewId = resp.interviewDetail.interviewId;
                            params.encryptInterviewId = resp.interviewDetail.encryptInterviewId;
                            params.securityId = resp.interviewDetail.securityId;
                            params.isFinishActivityWhenStartChat = true;
                            params.from = InterviewParams.FROM_OTHER;
                            params.apiFrom = "6";
                            InterviewsRouter.startInterviewDetailActivity(ChatNewActivity2.this, params);
                        }
                    }

                    @Override
                    public void onComplete() {
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
            }
        });
    }

    // **************** 发送附件简历消息 ****************


    private NLPDispatchLayout mNlpView;


    //检测好友id与securityId是否为空
    private boolean checkFriendIdSecurityIdEmpty() {
        if (tempFriendId <= 0 && LText.empty(securityId)) {
            ToastUtils.showText("数据异常");
            AppUtil.finishActivity(this);
            return true;
        }
        return false;
    }

    @Override
    protected void onCreateView(Bundle savedInstanceState) {
        common = new ChatCommon();
        common.setFriendSource(ContactBean.FROM_DIAN_ZHANG);
        Intent intent = getIntent();
        initParams(intent);

        if (checkFriendIdSecurityIdEmpty()) return;

        setContentView(R.layout.chat_activity2);
        //创建好友关系
        common.init(this);
        common.create(tempFriendId);
        common.initSwipeRefreshHelper(mZpuiRefreshLayout, mRecycleView, bottomFunctionView);
    }

    //发送nlp出席消息
    private void sendNLPPresenterMessage() {
        //注册监听NLP接收器
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                obtainFriendId(contactBean -> {
                    //发送最后一个聊天消息id,没有就传0
                    MessageDao messageDao = MessageDaoFactory.getMessageDao();
                    long id = messageDao.queryFriendChatMaxMessageId(UserManager.getUID(), UserManager.getUserRole().get(), contactBean.friendId);
                    MSGSender.sendMessageSuggest(System.currentTimeMillis(),
                            UserManager.getUID(), "0",
                            contactBean.friendId, id, FROM_DIAN_ZHANG);
                });

            }
        });
    }

    /**
     * 通知服务器清空NLP消息
     */
    private void noticeCleanNLPMessage(long msgId, String type, boolean fromNLP) {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                if (fromNLP) {
                    MSGSender.sendCleanTAGNLPMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, msgId, type, FROM_DIAN_ZHANG, "");
                } else {
                    MSGSender.sendCleanExchangeNLPMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, type, FROM_DIAN_ZHANG);
                }
            }
        });

    }

    /**
     * 用户点击 删除NLP按钮
     */
    private void onDeleteNlpMessage(long msgId, String suggestions) {
        obtainFriendId(contactBean -> MSGSender.sendDeleteMessageSuggest(System.currentTimeMillis(), UserManager.getUID(), contactBean.friendId, msgId, suggestions, null, FROM_DIAN_ZHANG));
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        long oldFriendId = tempFriendId;
        setIntent(intent);
        initParams(intent);

        if (checkFriendIdSecurityIdEmpty()) return;

        if (oldFriendId != tempFriendId) { //处理聊天界面不同聊天对象的重入问题 add by datian
            common.reset();
            common.create(tempFriendId);
            sendNLPPresenterMessage();
        } else {
            common.onNewIntent(intent);

            obtainFriendId(contactBean -> tipStatusView.userEnter(ChatNewActivity2.this, false, contactBean, securityId, new Callback<IUserEnter>() {
                @Override
                public void call(IUserEnter iUserEnter) {
                    if (iUserEnter.bossEnterResponse != null) {
                        common.bossLimitCheckUtils.setBossChatPluginBlock(iUserEnter.bossEnterResponse.bossChatPluginBlockSwitch, iUserEnter.bossEnterResponse.bossChatPluginBlockThreshold, iUserEnter.bossEnterResponse.bossChatPluginBlockTips);
                    }
                    ChatNewActivity2.this.isOnline = iUserEnter.isOnline;
                    int onlineStyle = iUserEnter.onlineStyle;
                    if (isOnline) {
                        if (onlineStyle == 2) {
                            titleView.setTitleRightIcon(R.mipmap.chat_icon_online, 6);
                        } else {
                            titleView.setTitleLeftIcon(R.drawable.bg_67cc35_dot);
                        }
                        SingleChatOnlineUtil.getInstance().registerOnLine(ChatNewActivity2.this, contactBean.friendId, contactBean.friendSource, true, onlineStyle);
                    } else {
                        SingleChatOnlineUtil.getInstance().removeUserOnLine(contactBean.friendId, contactBean.friendSource);
                    }

                    if (iUserEnter.refreshExchange) {
                        refreshExchange();
                    }
                }
            }));

        }

        startCheckIsContacted();
        refreshExchange();
    }

    private void initParams(Intent intent) {
        tempFriendId = intent.getLongExtra(SingleConstant.CHAT_LONG_FRIEND_ID, -1);
        jobId = intent.getLongExtra(SingleConstant.CHAT_LONG_JOB_ID, 0);
        long jobIntentId = intent.getLongExtra(SingleConstant.CHAT_LONG_EXPECT_ID, 0);
        String lid = intent.getStringExtra(SingleConstant.CHAT_STRING_LID);
        from = intent.getStringExtra(SingleConstant.CHAT_STRING_FROM);
        long jobPositionId = intent.getLongExtra(SingleConstant.CHAT_LONG_JOB_POSITION, 0);
        int jobCityCode = intent.getIntExtra(SingleConstant.CHAT_INT_JOB_CITY_CODE, 0);
        String backProtocol = intent.getStringExtra(SingleConstant.CHAT_STRING_BACK_PROTOCOL);
        String changePositionDesc = intent.getStringExtra(SingleConstant.CHAT_STRING_CHANGE_POSITION_DESC);
        securityId = intent.getStringExtra(SingleConstant.CHAT_STRING_SECURITY_ID);
        String url = intent.getStringExtra(SingleConstant.CHAT_STRING_URL);
        int similarPosition = intent.getIntExtra(SingleConstant.CHAT_INT_SIMILAR_POSITION, 0);
        long smoothToTargetId = intent.getLongExtra(SingleConstant.CHAT_LONG_SMOOTH_TO_TARGET_MESSAGE_ID, 0);
        int entrance = intent.getIntExtra(SingleConstant.CHAT_INT_ENTRANCE, 0);
        String greet = intent.getStringExtra(SingleConstant.CHAT_STRING_GREET);
        String contentInEditText = intent.getStringExtra(SingleConstant.CHAT_STRING_CONTENT_IN_EDIT_TEXT);
        int inviteType = intent.getIntExtra(SingleConstant.CHAT_INT_INVITE_TYPE, -1);
        String addFriendSendText = intent.getStringExtra(SingleConstant.CHAT_STRING_ADD_FRIEND_SEND_TEXT);

        int applyJobDirectly = intent.getIntExtra(SingleConstant.CHAT_STRING_APPLY_DIRECT, 0);
        // 1225.66【B/C】口语测试提效：通过口语测试打开聊天界面时，新增 languageId 界面入参
        long speakTestLanguageId = intent.getLongExtra(SingleConstant.CHAT_SPEAK_TEST_LANGUAGE_ID, 0L);
        //1302.110 【BC】蓝领C快速达成路径尝试 一键报名灰度 对照组为0，实验组为1-3
        int startChatProcessExpGroup = intent.getIntExtra(SingleConstant.CHAT_ONE_SIGN_GRAY_TYPE, 0);
        //1308.165【B&C】AI帮你推
        boolean fromAiRecommend = intent.getBooleanExtra(SingleConstant.CHAT_BOOLEAN_FROM_AI_RECOMMEND, false);
        defaultInputText = intent.getStringExtra(SingleConstant.CHAT_DEFAULT_INPUT_TEXT);

        common.setJobId(jobId);
        common.setJobIntentId(jobIntentId);
        common.setAddFriendSecurityId(securityId);
        common.setLid(lid);
        common.setFrom(from);
        common.setJobPositionId(jobPositionId);
        common.setJobCityCode(jobCityCode);
        common.setBackProtocol(backProtocol);
        common.setSmoothToTargetMessageId(smoothToTargetId);
        common.setChangePositionDesc(changePositionDesc);
        common.setUrl(url);
        common.setSimilarPosition(similarPosition);
        common.setEntrance(entrance);
        common.setGreet(greet);
        common.setChatContentInEditText(contentInEditText);
        common.setInviteType(inviteType);
        common.setAddFriendSendText(addFriendSendText);
        common.setApplyDirect(applyJobDirectly);
        common.setSpeakTestLanguageId(speakTestLanguageId);
        common.setStartChatProcessExpGroup(startChatProcessExpGroup);
        common.setFromAiRecommend(fromAiRecommend);
    }

    @Override
    protected void onResume() {
        super.onResume();
        common.resume();
        SecurityFrameworkManager.getInstance().check();
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return common.keyDown(keyCode, event) || super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        common.activityResult(this, requestCode, resultCode, data);
    }

    @Override
    protected void onPause() {
        super.onPause();

        obtainFriendId(contactBean -> {
            contactBean.messageExchangeIcon = ContactExchangeChatView.NONE;
            contactBean.isPlatFromResearch = false;
            contactBean.improveMessageExposure = false;
            contactBean.ats = false;
            contactBean.recommendReason = "";

            String inputText = bottomFunctionView.getInputEditText();
            refreshDraftInfo(contactBean, inputText, 0);
            common.pause();
        });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                try {


                    if (chatAdapter2 == null) return;
                    List<ChatBean> data = chatAdapter2.getData();
                    if (LList.isEmpty(data)) return;

                    /*刷新最后一个聊天消息*/
                    MessageUtils.finishRefreshLastChatText(contactBean, data);

                    IChatNlpTransferService nlpTransferService = SingleRouter.getNlpTransferService();
                    if (nlpTransferService != null) {
                        nlpTransferService.unRegister(this);
                    }

                    tipStatusView.unRegister();
                    common.destroy();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

    }

    @Override
    protected String getTitleText() {
        return title;
    }

    @Override
    protected @Nullable
    TextView getTitleView() {
        if (titleView != null) {
            return titleView.getTitleTextView();
        }
        return null;
    }

    @Override
    public View initConvertView() {
        return getWindow().getDecorView();
    }

    @Override
    protected int getStatusBarColor() {
        return ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F);
    }


    @Override
    public void initTitle(ContactBean contact) {
        titleView = findViewById(R.id.title_view);
        titleView.setBackClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //隐藏键盘
                AppUtil.hideSoftInput(ChatNewActivity2.this);
                //关闭当前页面
                AppUtil.finishActivity(ChatNewActivity2.this);
            }
        });
        titleView.changeAutoStyle(ContextCompat.getColor(this, R.color.color_FFFEFFFF_FF1D1D1F));

        titleView.setDividerInvisible();
        if (contact == null) return;
        initTitleValue(contact);
        titleView.setAction2ClickListener(R.mipmap.ic_action_more_function_black, new OnTitleMoreButtonClickListener());

        if (!CommonConfigManager.getInstance().isProgressStyle()) {
            titleView.setAction1ClickListener(R.mipmap.ic_action_remark_black, v -> new ContactRemarkManagerDialog().show(ChatNewActivity2.this, contact));

        }
    }


    @Override
    public void initTitleValue(ContactBean contact) {
        if (contact == null) return;
//        initTitleView();
        title = contact.friendName;
        final int connectStatus = MSGManager.get().getStatus();
        if (connectStatus == ConnectStatus.SUCCESS) {
            titleView.setTitle(UserManager.isBossRole() && contact.teamMemberSize > 1 ?
                    String.format(Locale.getDefault(), "%s 等%s人", title, contact.teamMemberSize) : title);/*1105.85 BOSS 看牛人 如果是组队，那么展示「牛人名字 等n人」*/
        } else {
            titleView.setTitle("未连接");
        }
        titleView.getTitleTextView().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f);
        if (UserManager.getUserRole() == ROLE.BOSS) {
            if (!LText.empty(contact.bossJobPosition)) {
//                mSubTitleTextView.setVisibility(View.VISIBLE);
                titleView.setSubTitle(contact.bossJobPosition);
            }
        } else {
//            mSubTitleTextView.setVisibility(View.VISIBLE);
            titleView.setSubTitle(StringUtil.connectTextWithChar(" · ", contact.bossCompanyName, contact.geekPositionName));
        }


    }

    private ZPUIBadgeView noneReadDot;

    @Override
    public void initBackButton(int otherNoneReadCountString) {
        TextView tvBackText = titleView.getTvBackText();
        if (otherNoneReadCountString > 0) {
            tvBackText.setVisibility(View.VISIBLE);

            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) tvBackText.getLayoutParams();
            params.width = ZPUIDisplayHelper.dp2px(this, 50);
            params.height = ZPUIDisplayHelper.dp2px(this, 25);
            tvBackText.setLayoutParams(params);

            if (noneReadDot == null) {
                noneReadDot = ZPUIBadgeUtils.createBadgeIcon(this, false);
                noneReadDot.setBadgeGravity(Gravity.CENTER | Gravity.START);
                noneReadDot.setBadgeTextColor(ContextCompat.getColor(this, R.color.text_c6));
                noneReadDot.setBadgeBackgroundColor(ContextCompat.getColor(this, R.color.color_FFF5F5F5_FF262629));
                noneReadDot.bindTarget(tvBackText);
            }

            noneReadDot.setBadgeNumber(otherNoneReadCountString);
        } else {
            tvBackText.setVisibility(View.GONE);
            if (noneReadDot != null) {
                noneReadDot.hide(false);
            }
        }
    }


    @Override
    public void initView() {
        slideView = findViewById(R.id.slide_view);
        bottomFunctionView = findViewById(R.id.chat_functions);

        mRecycleView = findViewById(R.id.mRecycleView);
        mRecycleView.setLayoutManager(new LinearLayoutManager(this));
        mZpuiRefreshLayout = findViewById(R.id.mZpuiRefreshLayout);

        tipStatusView = findViewById(R.id.ll_top_tips);
        mCoverBlockView = findViewById(R.id.mCoverBlockView);
        mTempExchangeLayout = findViewById(R.id.mTempExchangeLayout);
        slideView.setOnSlideClickCallBack(common);
        mNlpView = findViewById(R.id.mNlpView);
    }


    private View getSubAudioView(long friendId, long expectId) {
        return PanItemType.INSTANCE.getChatAudio(this, friendId, jobId, expectId,
                new ChatAudioView.OnRecordCallBck() {
                    @Override
                    public void sendTransferText(String text) {
                        obtainFriendId(contactBean -> ContactKeyManager.getInstance().onSendAudioListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                common.sendTextMessage(text, false);
                            }
                        }));
                    }

                    @Override
                    public void sendAudioFile(String audioFile, int duration, String resultText) {
                        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
                            @Override
                            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                                ContactKeyManager.getInstance().onSendAudioListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                                    @Override
                                    public void onCheckNoBlockListener() {
                                        common.onRecordSoundCompleteCallback(audioFile, duration);
                                    }
                                });

                            }
                        });
                    }
                }, bottomFunctionView.getToolsLayout(), mNlpView);
    }


    /**
     * 刷新NLP建议列表
     */
    @Override
    public void refreshNlpSuggest(NLPListBean nlpListBean) {
        if (mNlpView == null) return;
        if (common == null) return;
        common.obtainContactBean(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {

                if (contactBean.friendId == nlpListBean.getFriendId() &&
                        nlpListBean.getFriendSource() == contactBean.friendSource) {//用户是店长
                    mNlpView.showNLP(common.filterNLPListBean(nlpListBean));
                    nlPBgAction(nlpListBean);
                }

            }
        });

    }


    private void nlPBgAction(NLPListBean nlpListBean) {
        obtainFriendId(contactBean -> {

            if (!LList.isEmpty(nlpListBean.getNlpList())) {
                List<String> labels = new ArrayList<>();
                List<String> types = new ArrayList<>();
                for (NLPSuggestBean nlpBean : nlpListBean.getNlpList()) {
                    if (nlpBean == null) {
                        continue;
                    }
                    types.add(String.valueOf(nlpBean.type));
                    //https://jira.kanzhun-inc.com/browse/BSA-19975?filter=-1
                    labels.add(nlpBean.label);
                }
                AnalyticsFactory.create().action(ACTION_NLP_SHOW)
                        .param("p", String.valueOf(contactBean.friendId))
                        .param("p2", StringUtil.connectTextWithChar(",", labels))
                        .param("p3", StringUtil.connectTextWithChar(",", types))
                        .param("p4", String.valueOf(nlpListBean.getBusinessSceneType()))
                        .param("p5", String.valueOf(getJobId()))
                        .param("p7", NormalView.getBgActionP7(nlpListBean))
                        .param("p8", 1)
                        .build();
            }

        });
    }


    private long getJobId() {
        obtainFriendId(contactBean -> jobId = contactBean.jobId);
        return jobId;
    }


    /**
     * recycleView滑动回掉
     */
    @Override
    public void onBindAdapterItem(long id) {
        //检测是否消失电梯
        MessageSlideView.checkWeatherHideSlideView(slideView, id);
    }

    /**
     * 重新编辑撤回文案
     *
     * @param chatBean
     * @param text
     */
    @Override
    public void onReEditRevocationText(ChatBean chatBean, String text) {
        bottomFunctionView.onReEditRevocationTextListener(text);
    }


    @Override
    public void finishThis() {
        AppUtil.finishActivity(this);
    }

    @Override
    public void initTopCommon(boolean show) {

        refreshExchange();
    }

    private boolean hasScrolled;

    private ChatAdapter2 chatAdapter2;

    @Override
    public void refreshShowData() {

        obtainFriendId(contact -> {

            refreshExchange();
            common.refreshTopStatusView();

            //没有更多聊天内容,显示 【仅展示30天内的聊天记录】
            List<ChatBean> data = common.getMessageData2();

            if (data == null) return;

            if (chatAdapter2 == null) {
                chatAdapter2 = new ChatAdapter2(new IAdapterCallBack.SingleChatImp(common,
                        bottomFunctionView, new IRefreshAdapterCallBack() {
                    @Override
                    public void onRefreshAdapterListener() {
                        refreshShowData();
                    }

                    @Override
                    public void onReplyListener(ChatBean bean) {
                        //不支持引用消息
                    }

                    @Override
                    public void onAddGifEmotionListener(ChatBean bean) {
                        OnZPUIChatGifLongClickListener.executeFavourite(ChatNewActivity2.this, bean.message.messageBody.gifImageBean,bean.msgId);
                    }
                }), new ISendUserCallBack.ChatNewActivityUserCallBack());


                mRecycleView.setAdapter(chatAdapter2);
            }
            chatAdapter2.setNewData(data);


            if (UserManager.isBossRole()) {
                //检测是否有加密的消息,控制【常用语】【输入框】【+】是否可以点击
                boolean hasEncryptMessage = common.hasEncryptMessage();
                if (hasEncryptMessage) {
                    mCoverBlockView.setVisibility(View.VISIBLE);
                    mCoverBlockView.setOnClickListener(v -> checkEncryMessage());
                    return;
                }

                //技术类牛人开聊收费
                if (contact.isTechGeekBlock) {
                    mCoverBlockView.setVisibility(View.VISIBLE);
                    mCoverBlockView.setOnClickListener(v -> checkShowChatBlock());
                    bottomFunctionView.setInputHintText("需使用道具回复");
                    return;
                }
                bottomFunctionView.setInputHintText("新消息");
                mCoverBlockView.setVisibility(View.GONE);
                if (chatBlockManager != null) {
                    chatBlockManager.hideBottomView();
                }
            }

            //从快速处理进来显示键盘
            checkIsFromFastHandler();
        });

    }


    //交换电话
    private void onExchangePhoneListener() {
        onExchangePhoneListener(null);
    }

    private void onExchangePhoneListener(@Nullable NLPSuggestResultBean nlpSuggestResultBean) {
        obtainFriendId(contactBean -> ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
            @Override
            public void onCheckNoBlockListener() {
                ExchangePhoneManager exchangePhoneManager = new ExchangePhoneManager(ChatNewActivity2.this, contactBean);
                exchangePhoneManager.setScene(nlpSuggestResultBean != null ? nlpSuggestResultBean.scene : 0);
                exchangePhoneManager.setCallBack(new ExchangePhoneManager.OnExchangePhoneCallBack() {
                    @Override
                    public void onSendLocalPhoneMessage(ChatBean chatBean) {
                        common.addData(chatBean);
                        refreshShowData();
                        common.scrollToBottom(false);
                    }

                    @Override
                    public void onRefreshExchangeView() {
                        refreshShowData();
                    }
                });
                exchangePhoneManager.onExchangePhoneListener();
            }
        }, ""));

    }

    public @Nullable
    ChatBlockManager getChatBlockManager() {
        obtainFriendId(contactBean -> {
            if (chatBlockManager == null) {
                chatBlockManager = new ChatBlockManager(ChatNewActivity2.this, contactBean.securityId, new ChatBlockManager.OnPayCallback() {
                    @Override
                    public void onPayListener(ServerVipItemBean bean) {
                        common.setEncryptBlockBagId(bean.encryptBlockBagId);
                    }
                });
                chatBlockManager.setGeekId(contactBean.friendId);
                chatBlockManager.setJobId(jobId);
            }
        });
        return chatBlockManager;
    }


    @Override
    public void checkShowChatBlock() {
        obtainFriendId(contactBean -> {
            ChatBlockManager chatBlockManager = getChatBlockManager();
            if (chatBlockManager != null) {
                chatBlockManager.showPayDialog();
            }
        });
    }

    /**
     * 7.07 简聊，获取详情页的简聊文字后，放置在聊天页的输入框中，并弹出键盘
     *
     * @param text
     */
    @Override
    public void setContentInEditText(String text) {
        bottomFunctionView.setEditInputText(text);
        bottomFunctionView.setShowKeyboard();
    }

    @Override
    public void onNewChatCallback(ChatBean bean) {
        startCheckIsContacted();
        refreshExchange();
    }

    @Override
    public void onSendChatCallback(ChatBean bean) {
        startCheckIsContacted();
        refreshExchange();
    }

    @Override
    public void refreshHeadTips() {
        tipStatusView.postInvalidateUI();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        tipStatusView.postInvalidateUI();
        tipStatusView.onRestart();
    }

    /**
     * 显示键盘
     */
    @Override
    public void showKeyBoard(long friendId) {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                if (friendId == contactBean.friendId) {
                    App.get().getMainHandler().postDelayed(() -> bottomFunctionView.setShowKeyboard(), 200);
                }
            }
        });
    }

    /**
     * 快速处理弹出键盘
     */
    private void checkIsFromFastHandler() {
        if (FROM_FAST_HANDLE_CHAT.equals(from) && !hasShowFastHandlerKeyBoard && !common.hasEncryptMessage()) {
            hasShowFastHandlerKeyBoard = true;
            //弹起键盘
            App.get().getMainHandler().postDelayed(() -> bottomFunctionView.setShowKeyboard(), 200);
        }
    }

    private boolean onDialogViewButtonClickIntercept;

    @Override
    public boolean onDialogViewButtonClickIntercept(final ChatBean chatBean, ChatDialogBean dialogBean, final int index) {
        if (dialogBean.type == 1
                && index == 1
                && showSettingWechatDialog(false, chatBean, index)) {
            // 这里意为对话框为微信对话框，点击同意按键，同时还未设置微信号码
            return true;
        }

        //每次点击默认值是false
        onDialogViewButtonClickIntercept = false;

        obtainFriendId(contact -> {

            if (UserManager.getUserRole() == ROLE.GEEK
                    && dialogBean.type == 8
            ) { // 这里是牛人点击拒绝Boss的牛炸开聊，并且牛人当前未拒绝过Boss
                if (!contact.isReject && index == 2) { // 拒绝
                    DialogUtils d = new DialogUtils.Builder(ChatNewActivity2.this)
                            .setDoubleButton()
                            .setTitle(R.string.string_reject_boss_position_dialog_title)
                            .setDesc(R.string.string_reject_boss_position_dialog_desc)
                            .setNegativeAction(R.string.string_think_twice)
                            .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    chatDialogOnReject(chatBean, index);
                                }
                            })
                            .build();
                    d.show();
                    onDialogViewButtonClickIntercept = true;
                } else if (contact.isReject && index == 1) { // 接受
                    chatDialogOnReject(chatBean, index);
                    onDialogViewButtonClickIntercept = true;
                }
            }
        });
        return onDialogViewButtonClickIntercept;
    }

    private void chatDialogOnReject(final ChatBean chatBean, final int index) {

        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                UserRejectManager.getInstance().changeRejectListener(contactBean, UserRejectManager.PAGE_TYPE_CHAT_PAGE, new RejectHttpManager.OnLockUnLockCallBack() {

                    @Override
                    public void onSuccess(ContactBean contactBean) {
                        common.resume();
                        common.onDialogViewButtonClickCallback(chatBean, index);
                    }

                    @Override
                    public void onFailed(ContactBean contactBean) {

                    }
                });
            }
        });

    }

    /**
     * Boss检测发送请求附件简历消息
     */
    private void sendCheckRequestGeekResumeMessage() {
        obtainFriendId(contactBean -> {
            if (contactBean.isContactEachOther()) {
                sendRequestGeekResumeMessage();
            } else {
                ToastUtils.showText("对不起，您需要在牛人回复后，才可以求简历");
            }
        });
    }

    @Override
    public void startCheckIsContacted() {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contact) {
                //检测收否已经收到 电话交换助手 卡片，收到默认开启双聊，可以随便发送卡片信息

                if (contact.isContactEachOther()) return;

                AppThreadFactory.POOL.execute(() -> {

                    /*检测双聊*/
                    checkContactIsContactWithEachOther(contact);

                    App.get().getMainHandler().post(() -> refreshShowData());
                });
            }
        });
    }

    @Override
    public String getChatFrom() {
        return from;
    }

    @Override
    public boolean checkShowEditGreet(BossGreetingGuideBean bossGreetingGuide) {
        if (bossGreetingGuide != null
                && bossGreetingGuide.customTemplateId > 0) {
            new ChatEditGreetingWordDialog().showDialog(ChatNewActivity2.this, bossGreetingGuide, common.getFriendId(), common.getFriendSource(), common.getJobId());
            common.setBossGreetingGuideBean(null);
            return true;
        }
        return false;
    }

    @Override
    public boolean showEditBossPosition() {
        if (UserManager.isBossRole() && DataStarGray.getInstance().getBossTitleSuggestGuideGroup() == 2) {
            return BossPageRouter.showEditBossPositionDialog(this);
        }
        return false;
    }

    /**
     * Boss发送请求附件简历消息
     */
    private void sendRequestGeekResumeMessage() {
        obtainFriendId(new ChatCommon.OnFriendIdCallBack() {
            @Override
            public void onHasFriendIdListener(@NonNull ContactBean contact) {
                String email = "";
                UserBean loginUser = UserManager.getLoginUser();
                if (loginUser != null && loginUser.bossInfo != null) {
                    email = loginUser.bossInfo.receiveResumeEmail;
                }
                ChatBean bean = common.getChatSendCommon().sendRequestAnnexResumeMessage(MessageTargetInfo.fromContactBean(contact), email, FROM_BOSS, FROM_DIAN_ZHANG, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        refreshShowData();
                        noticeCleanNLPMessage(mNlpView.getCurrentNlpMsgId(), "5", false);
                    }
                });
                if (bean != null) {
                    common.addData(bean);
                    refreshShowData();
                    common.scrollToBottom(false);
                } else {
                    ToastUtils.showText("发送消息失败");
                }
            }
        });

    }


    //刷新顶部 交换电话和交换微信图标
    private void refreshExchange() {
        obtainFriendId(contactBean -> {
            //冻结>拉黑>拒绝,三种状态下顶部操作区域都不显示
            if (mTempExchangeLayout != null) {
                mTempExchangeLayout.refreshContact(contactBean);
            }
        });
    }


    // ************* 发送交换微信相关方法 ****************

    /**
     * 判断是否需要弹出设置微信的对话框
     *
     * @param isSendRequest 是否发送交换微信号
     * @param bean          实例
     * @param index         点击下标
     * @return true：需要设置微信号 false：不需要设置微信号
     */
    private boolean showSettingWechatDialog(final boolean isSendRequest, final ChatBean bean, final int index) {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) {
            T.ss("您现在处于未登录状态，请登录后再操作");
            return true;
        }
        String wechatNumber = UserManager.getWeiXin();
        if (LText.empty(wechatNumber)) {
            // 弹出对话框
            ChatWechatDialog dialog = new ChatWechatDialog(this, new ChatWechatDialog.IWechatClickListener() {
                @Override
                public void setWechat(String input) {
                    if (isSendRequest) {
                        sendWechatNumberTextMessage(false);
                    } else {
                        common.onDialogViewButtonClickCallback(bean, index);
                    }
                }
            });
            dialog.show();
            return true;
        }
        return false;
    }


    /**
     * 发送一条交换微信号消息
     */
    private void sendWechatNumberTextMessage(final boolean fromNLP) {
        obtainFriendId(contact -> {
            ChatBean bean = common.getChatSendCommon().sendExchangeWechatMessage(MessageTargetInfo.fromContactBean(contact), ContactBean.FROM_DIAN_ZHANG, new ChatSendCallback() {
                @Override
                public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                    refreshShowData();
                    noticeCleanNLPMessage(mNlpView.getCurrentNlpMsgId(), "4", fromNLP);
                }
            });
            if (bean != null) {
                common.addData(bean);
                refreshShowData();
                common.scrollToBottom(false);
            } else {
                ToastUtils.showText("发送消息失败");
            }
        });
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    /**
     * 右上解点击事件实现类
     */
    private class OnTitleMoreButtonClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {

            obtainFriendId(contactBean -> {
                AnalyticsFactory.create().action(ACTION_CHAT_WINDOW_MORE).secId(contactBean.securityId).param("p",
                        String.valueOf(contactBean.friendId)).build();
                SingleRouter.startSettingChat(ChatNewActivity2.this, contactBean, null, from);
            });

        }
    }


    private final ChatBottomFuncPresenter2 chatBottomFuncPresenter2 = new ChatBottomFuncPresenter2();

    private final IChatBottomFunView2 iChatBottomFunView2 = new IChatBottomFunView2() {

        @Override
        public void refreshCommonWordsListener() {
            bottomFunctionView.refreshCommonWords(chatBottomFuncPresenter2.getChatCommonList());
        }

        @Override
        public void onCommonWordsItemClickListener(CommonWordsUIBean item) {
            bottomFunctionView.onCommonWordTextListener(item.commonWordText);
            bottomFunctionView.setShowKeyboard();

            obtainFriendId(contactBean -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_QUICK_REPLY)
                    .param("p2", String.valueOf(contactBean.friendId))
                    .param("p3", Signer.getPassword(item.commonWordText))
                    .param("p4", "1")
                    .param("p5", item.commonWordId)
                    .build());

        }


        @Override
        public void onCameraUploadPhoto(Uri fileUri, ContactBean contact) {
            ContactKeyManager.getInstance().onPhotoSendListener(contact, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    super.onCheckNoBlockListener();

                    //拍照上传图片
                    UploadChatManager.onSingleUpload(ChatNewActivity2.this, fileUri, contact, ContactBean.FROM_DIAN_ZHANG, new OnMessageSendCallBack() {
                        @Override
                        public void onMsgStartSendListener(ChatBean contactBean) {
                            common.addData(contactBean);
                            refreshShowData();
                            common.scrollToBottom(false);
                        }

                        @Override
                        public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                            if (BossBlockCheck.checkMaxLimitAddText(contact, common.refreshBossTempCheckLimitList())) {
                                common.addData(common.createBossLimitStartNoticeText());
                                refreshShowData();
                                common.scrollToBottom(false);
                            }
                        }
                    });
                }
            });
        }

        @Override
        public void onGallerySendImageListener(List<Uri> uris, ContactBean contactBean) {
            ContactKeyManager.getInstance().onPhotoSendListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    super.onCheckNoBlockListener();

                    //图库上传照片
                    UploadChatManager.onGalleryUploadUri(ChatNewActivity2.this, uris, contactBean, ContactBean.FROM_DIAN_ZHANG, new OnMessageSendCallBack() {

                        @Override
                        public void onMsgStartSendListener(ChatBean itemBean) {
                            common.addData(itemBean);
                            refreshShowData();
                            common.scrollToBottom(false);

                            //递归上传图片
                            if (!LList.isEmpty(uris)) {
                                UploadChatManager.onGalleryUploadUri(ChatNewActivity2.this, uris, contactBean, ContactBean.FROM_DIAN_ZHANG, this);
                            }
                        }

                        @Override
                        public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                            //通知查看是否双聊
                            onSendChatCallback(null);
                            //通知刷新列表
                            refreshShowData();
                        }

                    });
                }
            });
        }


    };


    private boolean isOnline;

    @Override
    public void initContactInfo(final ContactBean contact) {
        // 加载用户双方是否已经开聊
        startCheckIsContacted();
        //刷新交换状态
        refreshExchange();
        //初始化底部表情控件
        initBottomView(contact);
        //初始交换
        initExchangeView(contact);
        //初始化nlp控件
        initNlpView(contact);


        initChatTopStatusView(contact);

        //延时可以看到NLP动画过程
        //必须放到这个位置初始化NLP接收器,NLP根据ListView是否填满内容,计算显示FootView还是悬浮View
        sendNLPPresenterMessage();
    }

    private void initChatTopStatusView(ContactBean contact) {
        slideView.refreshSlideView(contact.noneReadCount, contact.friendId, false);

        tipStatusView.setChatCommon(common);
        tipStatusView.setiChatCommon(this);
        tipStatusView.setFriendId(contact.friendId);
        tipStatusView.setFriendSource(FROM_DIAN_ZHANG);
        tipStatusView.register(false);
        tipStatusView.userEnter(ChatNewActivity2.this, true, contact, securityId, new Callback<IUserEnter>() {
            @Override
            public void call(IUserEnter iUserEnter) {
                if (iUserEnter.bossEnterResponse != null) {
                    common.bossLimitCheckUtils.setBossChatPluginBlock(iUserEnter.bossEnterResponse.bossChatPluginBlockSwitch, iUserEnter.bossEnterResponse.bossChatPluginBlockThreshold, iUserEnter.bossEnterResponse.bossChatPluginBlockTips);
                }
                isOnline = iUserEnter.isOnline;
                int onlineStyle = iUserEnter.onlineStyle;
                if (isOnline) {
                    if (onlineStyle == 2) {
                        titleView.setTitleRightIcon(R.mipmap.chat_icon_online, 6);
                    } else {
                        titleView.setTitleLeftIcon(R.drawable.bg_67cc35_dot);
                    }
                    SingleChatOnlineUtil.getInstance().registerOnLine(ChatNewActivity2.this, contact.friendId, contact.friendSource, true, onlineStyle);
                } else {
                    SingleChatOnlineUtil.getInstance().removeUserOnLine(contact.friendId, contact.friendSource);
                }

                if (iUserEnter.refreshExchange) {
                    refreshExchange();
                }

                 //1224.112【招聘者】用户换公司后自动打招呼场景
                if (iUserEnter.bossEnterResponse != null
                        && iUserEnter.bossEnterResponse.greetingGuide != null
                        && iUserEnter.bossEnterResponse.greetingGuide.customTemplateId > 0) {
                    if (common.isHasAgreeLocation()) {
                        checkShowEditGreet(iUserEnter.bossEnterResponse.greetingGuide);
                    } else {
                        common.setBossGreetingGuideBean(iUserEnter.bossEnterResponse.greetingGuide);
                    }
                }
            }
        });
    }

    private void initNlpView(ContactBean contact) {
        mNlpView.setChatCommon(common);
        mNlpView.setCallBack(new DispatchSuggestCallBack(contact.friendId, FROM_DIAN_ZHANG, onSuggestCallBack));
        mNlpView.setJobId(jobId);
        mNlpView.setHasInitRegister();
    }

    private void initExchangeView(ContactBean contact) {
        mTempExchangeLayout.initViewAndCallBack(contact, CommonConfigManager.getInstance().isProgressStyle(), getExchangeHelperWithCallBack(contact));
    }

    private void initBottomView(ContactBean contact) {
        chatBottomFuncPresenter2.setActivity(this);
        chatBottomFuncPresenter2.setFriendId(contact.friendId);
        chatBottomFuncPresenter2.setSecurityId(contact.securityId);
        chatBottomFuncPresenter2.setJobId(contact.jobId);
        chatBottomFuncPresenter2.setiChatBottomFunView2(iChatBottomFunView2);

        bottomFunctionView.setGifAboveView(mNlpView);

        bottomFunctionView.setCommonWordsParam(chatBottomFuncPresenter2.getCommonWordsParams());

        bottomFunctionView.setEmotionParams(chatBottomFuncPresenter2.getEmotionParams());

        bottomFunctionView.setOnChatMoreCallBack(new DianZhangUserLayout.OnChatMoreCallBack() {
            @Override
            public void onCameraListener() {
                chatBottomFuncPresenter2.onChatMorePhotoListener(contact);
            }

            @Override
            public void onSendLocationListener() {

                obtainFriendId(contactBean -> {

                    AnalyticsFactory.create()
                            .action(ACTION_CLICK_CHAT_POSITION)
                            .param("p", String.valueOf(contactBean.friendId))//对方id
                            .param("p2", String.valueOf(contactBean.jobId))//职位id
                            .buildSync();
                    JobBean jobBean = UserManager.findJobById(contactBean.jobId);
                    if (jobBean == null) {
                        T.ss("沟通职位不在线，无法使用");
                        return;
                    }

                    ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                        @Override
                        public void onCheckNoBlockListener() {
                            JobBean cloneJobBean = CloneUtils.cloneObject(jobBean);//里面会修改职位的经纬度
                            if (cloneJobBean != null) {
                                Intent intent = SelectWorkLocationActivity.createIntent(ChatNewActivity2.this, cloneJobBean, true, "发送", PermissionConstants.SCENE_CHAT_SEND_LOCATION);
                                intent.putExtra("from", SELECT_WORK_CHAT_SOURCE);
                                AppUtil.startActivityForResult(ChatNewActivity2.this, intent, REQ_SEND_LOCATION, ActivityAnimType.UP_GLIDE);
                            }
                        }
                    }, ContactKeyManager.BgSource.SEND_LOCATION_SOURCE);

                });

            }
        });

        bottomFunctionView.setOnMoreClickListener(v -> {

            obtainFriendId(contactBean -> AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_PLUS).secId(contactBean.securityId)
                    .param("p", String.valueOf(contactBean.friendId))
                    .build());

        });

        bottomFunctionView.addTextChangeCallBack(s -> {

            common.obtainContactBean(new ChatCommon.OnFriendIdCallBack() {
                @Override
                public void onHasFriendIdListener(@NonNull ContactBean contactBean) {
                    if (mNlpView != null) {
                        mNlpView.onInputTextHideNLP(s);
                        mNlpView.getChatSuggest(s, contactBean.securityId, contactBean.friendId, contactBean.friendSource);
                    }
                }
            });

        });
        bottomFunctionView.setOnSendMessageClickCallBack(this::sendChatTextListener);
        bottomFunctionView.setInputTextStateChangeCallBack((boolean visible) -> {
            if (visible) {
                common.scrollToBottom(false);
            }
        });
        bottomFunctionView.setSubAudioView(getSubAudioView(contact.friendId, contact.jobIntentId));
        bottomFunctionView.setOnAudioClickListener(v -> AnalyticsFactory
                .create()
                .action(AnalyticsAction.ACTION_VOICE_SEND_CLICK)
                .param("p", contact.friendId)
                .param("p2", contact.jobId)
                .param("p3", contact.jobIntentId)
                .build());
        bottomFunctionView.initBottomLayout();
        if (!LText.empty(defaultInputText)) {
            bottomFunctionView.setEditInputText(defaultInputText);
        } else {
            bottomFunctionView.setEditInputText(ChatUtils.getContactDraft(contact.RoughDraft));
        }
    }


    private OnExchangeCallBack getExchangeHelperWithCallBack(ContactBean contact) {

        if (UserManager.isBossRole()) {

            return new BossRoleChatDZUserPresenter(this,
                    contact,
                    mNlpView,
                    new BossRoleChatDZUserPresenter.BossRoleChatDZUserPresenterCallBack() {

                        @Override
                        public void refreshContainer() {
                            refreshExchange();
                        }

                        @Override
                        public void setEncryptBlockBagId(String encryptBlockBagId) {
                            common.setEncryptBlockBagId(encryptBlockBagId);
                        }

                        @Override
                        public void onNewChatBean(ChatBean chatBean) {
                            common.addData(chatBean);
                            refreshShowData();
                        }

                        @Override
                        public void smoothToBottom() {
                            common.scrollToBottom(true, true);
                        }

                        @Override
                        public int onAgreeExchangeDirectly(int dialogType) {
                            return onAgreeExchange(dialogType, false);
                        }
                    });

        } else {

            return new GeekRoleChatDZUserPresenter(this,
                    contact,
                    mNlpView,
                    new GeekRoleChatDZUserPresenter.GeekRoleChatDZUserPresenterCallBack() {

                        @Override
                        public void refreshContainer() {
                            refreshExchange();
                        }

                        @Override
                        public void onNewChatBean(ChatBean chatBean) {
                            common.addData(chatBean);
                            refreshShowData();
                        }

                        @Override
                        public void onSmoothToBottom() {
                            common.scrollToBottom(true, true);
                        }

                        @Override
                        public int onAgreeExchangeDirectly(int dialogType) {
                            return onAgreeExchange(dialogType, false);
                        }
                    });
        }
    }


    private void sendChatTextListener(String text) {
        obtainFriendId(contactBean -> {
            common.bossLimitCheckUtils.checkLimit(contactBean, common.refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
                @Override
                public void onBlockResult(String blockTip) {
                    if (blockTip != null) {
                        common.sendFailedTextMessage(text, blockTip);
                    } else{
                        boolean sendSuggestNlp = !LText.empty(nlpSuggestValue);
                        ContactKeyManager.getInstance().onInputSendMsgListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                            @Override
                            public void onCheckNoBlockListener() {
                                if (sendSuggestNlp) {
                                    common.sendNlpSuggestTextMessage(text, 0, null);
                                } else {
                                    common.sendTextMessage(text, CommonWordManager.getInstance().checkWeatherHasSet(text));
                                }
                            }

                            @Override
                            public void onCheckBlockUrlListener(String url) {
                                common.sendFailedTextMessage(text, null);
                            }

                            @Override
                            public void onCheckBlockToastListener(String msg) {
                                common.sendFailedTextMessage(text, null);
                            }
                        });

                        if (!LText.empty(nlpSuggestValue)) {

                            AnalyticsFactory
                                    .create()
                                    .action(ACTION_CHAT_DOUBLE_CHAT_LABEL_PUSH)
                                    .param("p", nlpSuggestValue)
                                    .param("p2", text)
                                    .param("p3", LText.equal(nlpSuggestValue, text) ? 1 : 0)
                                    .build();
                            nlpSuggestValue = "";
                        }
                    }
                }
            });

        });
    }


    @Override
    public void OnPhoneContactCallBack(long friendId, int friendSource) {
        obtainFriendId(contactBean -> {
            if (contactBean.friendId == friendId && contactBean.friendSource == friendSource) {
                onExchangePhoneListener();
            }
        });
    }

    //获得好友friendId
    private void obtainFriendId(@NonNull ChatCommon.OnFriendIdCallBack callBack) {
        if (common != null) {
            common.obtainContactBean(callBack);
        }
    }

    @Override
    public void onAgreeExchangeTip(ChatBean chatBean) {
        if (common != null) {
            common.onDialogViewButtonClickCallback(chatBean, 1);
        }
    }

    private int onAgreeExchange(int dialogType){
        return onAgreeExchange(dialogType, true);
    }

    private int onAgreeExchange(int dialogType, boolean fromNlp) {
        if (fromNlp) {
            dialogType = ExchangeMessage.mapNlpType2DialogType(dialogType);
        }

        // 先清除数据标记
        ExchangeHelper.getInstance().clearFlags();

        if (onAgreeWaitReceiveExchange(dialogType) > 0) {
            return dialogType;
        } else {
            return ChatConstant.NONE;
        }
    }

    private int onAgreeWaitReceiveExchange(int dialogType) {
        if (ExchangeHelper.getInstance().isWaitReceiveStatus(dialogType, common.getContactBean())) {
            ChatBean targetChatBean = ExchangeHelper.getInstance().getTargetChatBean();
            if (targetChatBean != null) {
                ExchangeHelper.getInstance().setIgnoreConfirm(false);
                ExchangeHelper.getInstance().setSource(ExchangeHelper.Source.EXCHANGE_BUTTON);
                common.onDialogViewButtonClickCallback(targetChatBean, 1);
                return dialogType;
            }
        }

        return ChatConstant.NONE;
    }
}
