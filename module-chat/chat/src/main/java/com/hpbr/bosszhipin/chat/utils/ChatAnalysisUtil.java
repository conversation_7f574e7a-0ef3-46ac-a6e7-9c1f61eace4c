package com.hpbr.bosszhipin.chat.utils;

import android.text.TextUtils;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.chat.export.constant.ChatAnalyticsAction;
import com.hpbr.bosszhipin.chat.wisdomstone.dialog.BaseCustomerEvaluateDialog;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactModeManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.LocalItf;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.local.GeekWaitAchieve;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

/**
 * @ClassName ：ChatAnalysisUtil
 * @Description ：Chat埋点工具类
 * <AUTHOR> SheYi
 * @Date ：2022/10/24  1:42 PM
 */
public class ChatAnalysisUtil {

    /**
     * 点击更多问题后点击二级类标签时上报
     */
    public static void dotHighFreQuencyLabelClick(String labelName) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_ZHS_HIGHFREQUENCY_LABEL_CLICK)
                .param("p", labelName)/*点击的二级标签名称*/
                .debug()
                .build();
    }

    /**
     * 智慧石点击，V1015加入 常用3级问点击时属于哪个二级标签下
     *
     * @param question
     * @param title
     * @param secondLevelLabel
     */
    public static void dotGuessQuestionClick(String question, String title, String secondLevelLabel) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_ZHS_GUESS_QUESTION_CLICK)
                .param("p", question)/*问题名称title （ “以上没有我想问的” 也做一下上报¶）¶V1009 新增，点击的二级类名称）*/
                .param("p2", title)/*标题title：¶“这些问题是否符合你的描述” -- 「引导问」列表*/
                .param("p7", TextUtils.isEmpty(secondLevelLabel) ? "" : secondLevelLabel)/*V1015新增，点击常用问更多问题弹窗具体问题时，上报问题所属的二级标签*/
                .debug()
                .buildSync();
    }

    /**
     * 智能客服满意度调研提交时上报
     *
     * @param score
     * @param labels
     */
    public static void dotZhsAlertSubmit(String score, String labels, int source, String evaluateType) {

        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_ZHS_ALERT_SUBMIT)
                .param("p", score)/*智能客服评价打分(1~5，1是非常差 5是非常好)*/
                .param("p2", labels)/*用户选择的标签。用逗号分隔*/
                .param("p7", UserManager.getUserRole().get())
                .param("p8", getRealSource(source))
                .param("p9", evaluateType)
                .debug()
                .build();
    }

    private static int getRealSource(int source) {
        int realSource;
        if (source == BaseCustomerEvaluateDialog.EvaluateSource.BACK) {
            realSource = 2;
        } else if (source == BaseCustomerEvaluateDialog.EvaluateSource.MSG_INVITE) {
            realSource = 1;
        } else {
            realSource = 0;
        }
        return realSource;
    }

    /**
     * 智能客服满意度调研弹窗展示
     */
    public static void dotZhsAlertShow(int source) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_ZHS_ALERT_SHOW)
                .param("p", UserManager.getUserRole().get())
                .param("p2", getRealSource(source))
                .debug()
                .build();
    }

    public static void dotZhsAlertClick(int source, String clickContent) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_USER_FEEDBACK_CLICK)
                .param("p", UserManager.getUserRole().get())
                .param("p2", getRealSource(source))
                .param("p3", clickContent)
                .debug()
                .build();
    }

    public static void dotZhsFeedBackCardClick(long evaluateScore, long evaluateType) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_USER_FEEDBACK_CLICK_NEW)
                .param("p", UserManager.getUserRole().get())
                .param("p2", evaluateScore)
                .param("p3", evaluateType)
                .debug()
                .build();
    }

    /**
     * 双聊满意度调研弹窗评价提交
     */
    public static void dotChatAlertCommit(String question, String friendId, String score, String labels, String content) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_CHAT_ALERT_SUBMIT)
                .param("p", question)/*问题文案*/
                .param("p2", friendId)/*对侧UID*/
                .param("p3", score)/*提交时选的分数( 1，2，3，4，5；非常不满意1->非常满意5)*/
                .param("p4", labels)/*用户提交时选择的标签，用逗号分隔*/
                .param("p5", content)/*用户填的反馈内容，(填了就报，没填报空)*/
                .debug()
                .build();
    }

    /**
     * 双聊满意度调研弹窗展示时上报
     *
     * @param question
     * @param friendId
     */
    public static void dotChatAlertShow(String question, String friendId) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_CHAT_ALERT_SHOW)
                .param("p", question)/*问题文案*/
                .param("p2", friendId)/*对侧UID*/
                .debug()
                .build();
    }

    /**
     * 聊天导航区域点击名称时上报
     *
     * @param friendId
     */
    public static void dotF2DetailHeadClick(String friendId, boolean haveRemark) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_DETAIL_HEAD_CLICK)
                .param("p", friendId)/*对侧UID*/
                .param("p2", haveRemark ? 1 : 2)/*对面是否有备注，1-有 2-没有*/
                .debug()
                .build();
    }

    /**
     * 聊天导航区域点击名称时上报
     *
     * @param friendId
     */
    public static void dotF2DetailClickBack(String friendId) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_DETAIL_CLICK_BACK)
                .param("p", friendId)/*对侧UID*/
                .debug()
                .build();
    }

    /**
     * V1104联系人菜单页 删除联系人入口点击
     *
     * @param friendId
     */
    public static void dotF2SetClickDelete(String friendId) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_SET_CLICK_DELETE)
                .param("p", friendId)/*对侧UID*/
                .debug()
                .build();
    }

    /**
     * V1104联系人设置页标记不合适点击操作
     *
     * @param friendId
     */
    public static void dotF2SetHideClick(String friendId, boolean isMark) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_SET_HIDE_CLICK)
                .param("p", friendId)/*对侧UID*/
                .param("p2", isMark ? 1 : 2)/*1-标记 2-取消*/
                .debug()
                .build();
    }

    /**
     * V1104联系人菜单页面 黑名单入口点击
     *
     * @param friendId
     */
    public static void dotF2SetBlockClick(String friendId, boolean isJoin) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_SET_BLOCK_CLICK)
                .param("p", friendId)/*对侧UID*/
                .param("p2", isJoin ? 1 : 2)/*1-加入 2-取消*/
                .debug()
                .build();
    }

    /**
     * V1104 在联系人设置页点击 头像/名称时上报
     *
     * @param friendId
     * @param clickPosition
     */
    public static void dotF2SetClickMore(String friendId, int clickPosition) {
        AnalyticsFactory.create()
                .action(ChatAnalyticsAction.ACTION_F2_SET_CLICK_MORE)
                .param("p", friendId)/*对侧UID*/
                .param("p2", String.valueOf(clickPosition))/*点击的位置(1-头像 2-名称/箭头)*/
                .debug()
                .build();
    }

    /**
     * V1104聊天设置页，点击分享牛人/boss
     */
    public static void dotShareGeek(String friendId, String securityId) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_SHARE_GEEK)
                .param("p", "2")
                .param("p3", friendId)
                .param("p4", securityId)
                .debug()
                .build();
    }

    /**
     * 查看历史聊天记录点击
     *
     * @param friendId
     * @param jobId
     */
    public static void dotRecentChatHistoryEnterClick(long friendId, long jobId) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_RECENT_CHAT_HISTORY_ENTER_CLICK)
                .param("p", friendId)
                .param("p2", jobId)
                .debug()
                .buildSync();
    }

    /**
     * 【聊天-新增查看隐私状态】聊天设置-查看隐私状态入口的点击
     */
    public static void dotChatSetChatClickSetChatClick(long friendId) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_CHAT_SET_CHAT_CLICK_SET_CHAT_CLICK)
                .param("p", friendId)
                .debug()
                .buildSync();
    }

    /**
     * 点击删除好友
     *
     * @param friendId
     */
    public static void dotFriendDelete(long friendId) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_FRIEND_DELETE).param("p2", "0")
                .param("p", String.valueOf(friendId))
                .param("p3", "1")
                .buildSync();
    }

    public static void dotAddHunterBlackList() {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADD_HUNTER_BLACK_LIST)
                .debug()
                .build();
    }

    public static void dotBlockBossExpo(long friendId) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BLOCK_BOSS_EXPO)
                .param("p", friendId)
                .debug()
                .build();
    }

    /**
     * 查看「隐私状态」"曝光"埋点
     */
    public static void dotChatSetChatClickSetChatExpo(long friendId) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_SET_CHAT_CLICK_SET_CHAT_EXPO)
                .param("p", String.valueOf(friendId))
                .debug()
                .buildSync();
    }

    /**
     * Boss在聊天详情页点击收藏/取消收藏
     */
    public static void dotChatClickInterest(long friendId, int type) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_CLICK_INTEREST)
                .param("p", String.valueOf(friendId))/*对侧GEEK_id*/
                .param("p2", String.valueOf(type))/*1-点击收藏 2-点击取消收藏*/
                .debug()
                .build();
    }

    public static void doClickContactDetail(int p) {
        if (AccountHelper.isGeek()) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_DETAIL_CLICK)
                    .param("p", p)
                    .debug()
                    .build();
        }
    }

    /**
     * 1120.603 羚羊群聊点击埋点
     *
     * @param clickSource 点击模块：1：快捷问询，2：图片，3：语音，4：语音通话，5：视频通话
     * @param gid         群聊加密id
     */
    public static void doClickGroupAnte(int clickSource, String gid) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GROUP_ANTE_CLICK)
                .param("p", clickSource)
                .param("p2", gid)
                .debug()
                .build();
    }

    /**
     * @param encJobId   job_id
     * @param inputType  0:文字输入，1:语音输入，2：点选
     * @param content    输入内容or语音解析内容or点选内容
     * @param filterList 当前筛选条件list，英文逗号分隔
     */
    public static void doExpoBossF1ReplyFilter(String encJobId, int inputType, String content, String filterList) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_REPLY_EXPO)
                .param("p", encJobId)
                .param("p2", inputType)
                .param("p3", content)
                .param("p4", filterList)
                .debug()
                .build();

    }

    /**
     * @param encJobId     job_id
     * @param deletedLabel 本次删除的筛选条件
     * @param leftLabels   剩余条件list，英文逗号分隔
     */
    public static void doClickBossF1ReplyFilterDel(String encJobId, String deletedLabel, String leftLabels) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_REPLY_DELETE_CLICK)
                .param("p", encJobId)
                .param("p2", deletedLabel)
                .param("p3", leftLabels)
                .debug()
                .build();

    }

    /**
     * @param encJobId   job_id
     * @param inputType  0:文字输入，1:语音输入，2：点选
     * @param content    输入内容or语音解析内容or点选内容
     * @param failedType 0:网络失败、1:解析失败
     */
    public static void doExpoAnalyzeFailed(String encJobId, int inputType, String content, int failedType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_ANALYZE_FAIL_EXPO)
                .param("p", encJobId)
                .param("p2", inputType)
                .param("p3", content)
                .param("p4", failedType)
                .debug()
                .build();
    }

    /**
     * @param encJobId  job_id
     * @param inputType 0:文字输入，1:语音输入，2：点选
     * @param content   输入内容or语音解析内容or点选内容
     */
    public static void doClickAnalyze(String encJobId, int inputType, String content) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_INPUT_CLICK)
                .param("p", encJobId)
                .param("p2", inputType)
                .param("p3", content)
                .debug()
                .build();
    }

    /**
     * @param encJobId   当前列表的job_id
     * @param jobName    当前列表的title，如产品经理
     * @param labels     filter的条件，如：b端产品经验，985大学等，英文逗号分隔
     * @param submitType 是否为引导页直接点选刷新，0:否，1:是
     */
    public static void doClickSubmit(String encJobId, String jobName, String labels, int submitType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_SUBMIT_CLICK)
                .param("p", encJobId)
                .param("p2", jobName)
                .param("p3", labels)
                .param("p4", submitType)
                .debug()
                .build();
    }

    /**
     * @param feedbackType     0：点赞，1：点踩，2：取消点赞，3：取消点踩
     * @param msgId            点击的消息id
     * @param analyzingContent 消息内容，正在解析内容
     * @param labelText        当前状态下的标签list，逗号分隔
     */
    public static void doClickFeedback(String feedbackType, String msgId, String analyzingContent, String labelText) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_FEEDBACK_CLICK)
                .param("p", feedbackType)
                .param("p2", msgId)
                .param("p3", analyzingContent)
                .param("p4", labelText)
                .debug()
                .build();
    }

    /**
     * @param feedbackType      0：点赞，1：点踩，2：取消点赞，3：取消点踩
     * @param msgId             点击的消息id
     * @param analyzingContent  消息内容，正在解析内容
     * @param labelText         当前状态下的标签list，逗号分隔
     * @param clickType         点击位置：0:提交，1：关闭，右上角“X”or空白
     * @param content           if p5 = 0，评论内容（用户输入）
     * @param feedbackLabelText if p5 = 0，评论内容（用户点选），逗号分隔的点选标签list
     */
    public static void doClickFeedbackDialog(String feedbackType, String msgId, String analyzingContent, String labelText, String clickType, String content, String feedbackLabelText) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_F1_FEEDBACK_DIALOG_CLICK)
                .param("p", feedbackType)
                .param("p2", msgId)
                .param("p3", analyzingContent)
                .param("p4", labelText)
                .param("p5", clickType)
                .param("p6", content)
                .param("p7", feedbackLabelText)
                .debug()
                .build();
    }

    /**
     * @param feedbackType 1：正反馈 2：负反馈
     * @param responseId
     * @param sessionId
     * @param taskId
     */
    public static void doGeekF1AssistantClickFeedback(String feedbackType, String responseId, String sessionId, String taskId, String bizCode) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_F1_ASSISTANT_FEEDBACK_CLICK)
                .param("p", feedbackType)
                .param("p2", responseId)
                .param("p3", sessionId)
                .param("p4", taskId)
                .param("p5", bizCode)
                .debug()
                .build();
    }

    /**
     * @param clickType 1：预置语料 点击 2：语音按钮 点击 3：提交按钮 点击 4：提交按钮 点击 5：query 删除 6：半弹层 关闭 7：你可以说 点击
     */
    public static void doGeekF1AssistantClick(String clickType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_F1_ASSISTANT_CLICK)
                .param("p", clickType)
                .debug()
                .build();
    }

    public static void statsMsgTagPV(String msgTagText) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CONNECT_MSG_SHOW)
                .param("p", msgTagText)
                .param("p2", 1)
                .param("p3", UserManager.getUID())
                .param("p4", UserManager.getUID())
                .debug()
                .build();
    }

    public static void statsGeekConnectQuickReplyShow(ChatBean chatBean) {
        if (!ChatUtils.isValidChatDialogMsg(chatBean)) {
            return;
        }

        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(chatBean.fromUserId, UserManager.getUserRole().get(), chatBean.message.fromUser != null ? chatBean.message.fromUser.friendSource : 0);
        String pText = LList.isEmpty(chatBean.message.messageBody.dialog.buttons) ? "" : StringUtil.connectTextWithCharNew("_", chatBean.message.messageBody.dialog.buttons, button -> button.text);
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CONNECT_QUICK_REPLY_SHOW)
                .param("p", pText)
                .param("p2", contactBean != null ? contactBean.jobId : 0)
                .param("p3", UserManager.isBossRole() ? UserManager.getUID() : chatBean.fromUserId)
                .param("p4", UserManager.isBossRole() ? chatBean.fromUserId : UserManager.getUID())
                .debug()
                .build();
    }

    public static void statsGeekConnectQuickReplyClick(ChatBean chatBean, String text) {
        if (!ChatUtils.isValidChatDialogMsg(chatBean)) {
            return;
        }

        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(chatBean.fromUserId, UserManager.getUserRole().get(), chatBean.message.fromUser != null ? chatBean.message.fromUser.friendSource : 0);
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CONNECT_QUICK_REPLY_CLICK)
                .param("p", text)
                .param("p2", contactBean != null ? contactBean.jobId : 0)
                .param("p3", UserManager.isBossRole() ? UserManager.getUID() : chatBean.fromUserId)
                .param("p4", UserManager.isBossRole() ? chatBean.fromUserId : UserManager.getUID())
                .debug()
                .build();
    }

    public static void statsGeekSwitchChatTab(int sourceTab, int destTab) {
        if (UserManager.isBossRole()) {
            return;
        }

        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CLASSIFY_TAB_SWITCH)
                .param("p", sourceTab)
                .param("p2", destTab)
                .debug()
                .build();
    }

    public static void statsHunterGuideBarClick(ContactBean contactBean, int clickPosition) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_HUNTER_CUSTOMER_APP_GUIDE_BAR_CLICK)
                .param("p", UserManager.getUID())
                .param("p2", contactBean != null ? String.valueOf(contactBean.jobId) : "")
                .param("p3", clickPosition)
                .debug()
                .build();
    }

    public static void statsHunterGuideBarExpo(ContactBean contactBean) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_HUNTER_CUSTOMER_APP_GUIDE_BAR_EXPO)
                .param("p", UserManager.getUID())
                .param("p2", contactBean != null ? String.valueOf(contactBean.jobId) : "")
                .debug()
                .build();
    }

    public static void statsGeekPendingDrawerShow() {
        LocalItf localItf = F2ContactModeManager.getInstance().getLocalItf(MqttConfig.GEEK_WAIT_ACHIEVE);
        if (!(localItf instanceof GeekWaitAchieve)) {
            return;
        }

        ContactBean contactBean = localItf.getLocal();
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_PENDING_DRAWER_SHOW)
                .param("p", contactBean != null ? contactBean.lastChatText : "")
                .debug()
                .build();
    }

    public static void statsExchangeGuidePop(int position) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_PILOT_BUBBLE_SHOW)
                .param("p", position)
                .debug()
                .build();
    }

    public static void statsDoubleChatAssist(ContactBean contactBean, String currentSelectText, String clickPosition, String type, int scene) {
        if (contactBean == null) {
            return;
        }
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_ACTION_CHAT_DOUBLECHAT_ASSIST)
                .param("p", String.valueOf(contactBean.friendId))
                .param("p2", currentSelectText)
                .param("p3", clickPosition)
                .param("p4", type)
                .param("p5", contactBean.jobId)
                .param("p7", scene)
                .debug()
                .buildSync();
    }

    public static void statsEditInterviewInvitation(long geekId, long jobId, int scene) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_INTERVIEW_GUIDE_CLICK)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", scene)
                .debug()
                .build();
    }

    public static void statsPromoteConsumeTipShow(String tipTitle, String rights) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_PROMOTE_CONSUME_TIP_SHOW)
                .param("p", tipTitle)
                .param("p2", 1)
                .param("p3", rights)
                .debug()
                .build();
    }

    public static void statsPromoteConsumeTipClick(int clickPosition, String clickText, String tipTitle) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_PROMOTE_CONSUME_TIP_CLICK)
                .param("p", clickPosition)
                .param("p2", clickText)
                .param("p3", tipTitle)
                .param("p4", 1)
                .debug()
                .build();
    }

    public static void statsWidgetUpdate(String style, String type, String contentType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_WIDGET_SHOW)
                .param("p", style)
                .param("p2", type)
                .param("p3", contentType)
                .debug()
                .build();
    }

    public static void statsWidgetClick(String style, String type, String contentType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_WIDGET_CLICK)
                .param("p", style)
                .param("p2", type)
                .param("p3", contentType)
                .debug()
                .build();
    }

    public static void statsAcceptLocation(long friendUid, long jobId, int clickSource) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_LOCATION_AGREE)
                .param("p", "" + friendUid)
                .param("p2", jobId)
                .param("p3", clickSource)
                .debug()
                .buildSync();
    }

    public static void statsRefuseLocation(long friendUid, long jobId, int clickSource) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_LOCATION_REFUSE)
                .param("p", "" + friendUid)
                .param("p2", jobId)
                .param("p3", clickSource)
                .debug()
                .buildSync();
    }

    public static void statsRecommendReasonClick(ContactBean bean) {
        if (bean.getGroupType() == ContactBean.GroupType.NEW_GREETING && !TextUtils.isEmpty(bean.recommendReason)) {
            AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CONNECT_RECOMMENDATION_REASON_CLICK)
                    .param("p2", bean.friendId)
                    .param("p3", bean.jobId)
                    .param("p4", bean.recommendReason)
                    .buildSync();
        }
    }

    public static void statsInterviewPopShow(long friendId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_INTERVIEW_POP_SHOW)
                .param("p", friendId)
                .debug()
                .build();
    }

    public static void statsAIChatInteractionExpo(long friendId, long jobId, long expectId, int status) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_INTERACTION_EXPO)
                .param("p", friendId)
                .param("p2", jobId)
                .param("p3", expectId)
                .param("p4", status)
                .debug()
                .build();
    }

    public static void statsAIChatInteractionClick(long friendId, long jobId, long expectId, int status) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_INTERACTION_CLICK)
                .param("p", friendId)
                .param("p2", jobId)
                .param("p3", expectId)
                .param("p4", status)
                .debug()
                .build();
    }

    public static void statsAIQuickSetExpo(String securityId, String source, int step) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_QUICK_SET_EXPO)
                .secId(securityId)
                .param("p4", source)
                .param("p5", step)
                .debug()
                .build();
    }

    public static void statsAIQuickSetClick(String securityId, String source, int step, int clickPosition, String chooseValues) {
        AnalyticsFactory factory = AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_QUICK_SET_CLICK)
                .secId(securityId)
                .param("p4", source)
                .param("p5", step)
                .param("p6", clickPosition);

        if (step == 3) {
            factory.param("p7", chooseValues);
        }

        factory.debug().build();
    }


    public static void statsAiQuickRecruitmentClick(String jobId, String position) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_AI_QUICKRECRUITMENT_FUNCTION_CLICK)
                .param("p", jobId)
                .param("p2", position)
                .debug()
                .build();
    }

    public static void statsAiQuickRecruitmentSendMsg(String jobId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_AI_QUICKRECRUITMENT_SEND_MESSAGE_CLICK)
                .param("p", jobId)
                .debug()
                .build();
    }

    public static void statsCustomBottomButtonExpo(String sessionId, String stage, int category) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BELOW_BUTTON_EXPO)
                .param("p", sessionId)
                .param("p2", stage)
                .param("p3", category)
                .debug()
                .build();
    }

    public static void statsCustomBottomToolbarExpo(String sessionId, String stage) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_BOTTOM_TOOLBAR_EXPO)
                .param("p", sessionId)
                .param("p2", stage)
                .debug()
                .build();
    }

    public static void statsCustomerPhonePopupWindowExpo(String sessionId, String stage) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_PHONE_POPUP_WINDOW_EXPO)
                .param("p", sessionId)
                .param("p2", stage)
                .debug()
                .build();
    }

    public static void statsCustomerMessageBodyNoSendMessageClick(long sessionId, long messageId, String stage, int category) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_MESSAGE_BODY_NO_SEND_MESSAGE_CLICK)
                .param("p", sessionId)
                .param("p2", messageId)
                .param("p3", stage)
                .param("p4", category)
                .debug()
                .build();
    }

    public static void statsCustomerMessageBodySendMessageClick(long sessionId, long messageId, String stage, int category, String itemId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_MESSAGE_BODY_SEND_MESSAGE_CLICK)
                .param("p", sessionId)
                .param("p2", messageId)
                .param("p3", stage)
                .param("p4", category)
                .param("p5", itemId)
                .debug()
                .build();
    }

    public static void statsCustomerMessageBodyNouseCardClick(long sessionId, long messageId, String answerId, String reason) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_MESSAGE_BODY_NOUSE_CARD_CLICK)
                .param("p", sessionId)
                .param("p2", messageId)
                .param("p3", answerId)
                .param("p4", reason)
                .debug()
                .build();
    }

    public static void statsCustomerBottomToolbarClick(String sessionId, String stage, int category) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_BOTTOM_TOOLBAR_CLICK)
                .param("p", sessionId)
                .param("p2", stage)
                .param("p3", category)
                .debug()
                .build();
    }

    public static void statsCustomerBottomToolbarEntranceClick(String sessionId, String stage, int category) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_BOTTOM_TOOLBAR_ENTRANCE_CLICK)
                .param("p", sessionId)
                .param("p2", stage)
                .param("p3", category)
                .debug()
                .build();
    }

    public static void statsCustomerRobotCommonQuestionsClick(long sessionId, String stage, String category) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_ROBOT_COMMON_QUESTIONS_CLICK)
                .param("p", sessionId)
                .param("p2", stage)
                .param("p3", category)
                .debug()
                .build();
    }

    public static void statsCustomerEmojisButtonClick(String sessionId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_EMOJISBUTTON_CLICK)
                .param("p", sessionId)
                .debug()
                .build();
    }

    public static void statsCustomerInputAssociationClick(long sessionId, String stage, long itemId,  long answerId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CUSTOMER_INPUT_ASSOCIATION_CLICK)
                .param("p", sessionId)
                .param("p2", stage)
                .param("p3", itemId)
                .param("p4", answerId)
                .debug()
                .build();
    }
    public static void statsAiFastSearchUserFeedback(int feedbackType, String feedbackContent) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_AI_FAST_SEARCH_USER_FEEDBACK)
                .param("p2", feedbackType)
                .param("p3", feedbackContent)
                .debug()
                .build();
    }

    public static void statsChatGuideBarExpo(long userId, int exchangeType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_GUIDE)
                .param("p", userId)
                .param("p2", exchangeType)
                .debug()
                .build();
    }

    public static void statsChatGuideBarClick(long userId, int clickPosition, int exchangeType) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_GUIDE_CLICK)
                .param("p", userId)
                .param("p2", clickPosition)
                .param("p3", exchangeType)
                .debug()
                .build();
    }

    public static void statsChatAIReplyShow(long friendId, long jobId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_CHAT_AI_REPLY_SHOW)
                .param("p", friendId)
                .param("p2", jobId)
                .debug()
                .build();
    }

    public static void statsChatAIReplyClick(long friendId, long jobId, int curState, int contentType, int clickPosition, String content) {
        AnalyticsFactory analyticsFactory = AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_AI_REPLY_CLICK)
                .param("p", friendId)
                .param("p2", jobId)
                .param("p5", clickPosition);
        if (clickPosition != 7) {
            analyticsFactory.param("p3", curState);
            if (curState == 1) {
                analyticsFactory.param("p4", contentType);
            }
        }

        if (clickPosition == 1 || clickPosition == 2 || clickPosition == 3) {
            analyticsFactory.param("p6", content);
        }

        analyticsFactory.debug().build();
    }

    public static void statsChatAIReplyResultShow(long friendId, long jobId, int state, int contentType, String content) {
        AnalyticsFactory analyticsFactory = AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_AI_REPLY_RESULT_SHOW)
                .param("p", friendId)
                .param("p2", jobId)
                .param("p3", state);
        if (state == 1) {
            analyticsFactory.param("p4", contentType);
        }

        if (!LText.empty(content)) {
            analyticsFactory.param("p5", content);
        }
        analyticsFactory.debug().build();
    }

    public static void statsChatExchangeExpo(long geekId, long jobId, boolean isVip, int remainNum) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_SINGLE_CHAT_EXCHANGE_EXPOSURE)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", isVip ? 1 : 0)
                .param("p4", remainNum)
                .debug()
                .build();
    }

    public static void statsChatExchangeClick(long geekId, long jobId, boolean isVip, int remainNum, int clickPosition, String type) {
        AnalyticsFactory analyticsFactory = AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_SINGLE_CHAT_EXCHANGE_CLICK)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", isVip ? 1 : 0)
                .param("p4", remainNum)
                .param("p5", clickPosition);
        if (clickPosition == 1) {
            analyticsFactory.param("p6", type);
        }
        analyticsFactory.debug().build();
    }

    public static void statsAIKeypointRestartClick(long geekId, long jobId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_RESUME_AI_KEYPOINT_RESTART)
                .param("p", geekId)
                .param("p2", jobId)
                .debug()
                .build();
    }

    public static void statsAIKeypointFeedback(long geekId, long jobId, int likeOrUnlike) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_RESUME_AI_KEYPOINT_FEEDBACK)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", likeOrUnlike)
                .debug()
                .build();
    }

    public static void statsAIKeypointCopy(long geekId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_RESUME_AI_KEYPOINT_COPY)
                .param("p", geekId)
                .debug()
                .build();
    }


    public static void statsAIKeypointPopClick(long geekId, String label, String remark) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_RESUME_AI_KEYPOINT_FEEDBACK_POP_CLICK)
                .param("p", geekId)
                .param("p2", label)
                .param("p3", remark)
                .debug()
                .build();
    }


    public static void statsQuickProcessCardClick(int clickType, long geekId) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_QUICK_PROCESS_CARD_CLICK)
                .param("p2", clickType)
                .param("p3", geekId)
                .debug()
                .build();
    }



    public static void statsExchangeConfirmationWindowExpo(long geekId, String title, String content, int exchangeType, String buttonText, int scene) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_EXCHANGE_CONFIRMATION_WINDOW_EXPOSURE)
                .param("p", geekId)
                .param("p2", title)
                .param("p3", content)
                .param("p4", exchangeType)
                .param("p5", buttonText)
                .param("p6", scene)
                .debug()
                .build();
    }

    public static void statsAITalkStatusClick(long geekId, CharSequence label) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_AI_TALK_STATUS_CLICK)
                .param("p", geekId)
                .param("p2", label != null ? label.toString() : "")
                .debug()
                .build();
    }

    public static void statsAITalkStatusShow(String label) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_AI_TALK_STATUS_SHOW)
                .param("p", label)
                .debug()
                .build();
    }

    public static void statsAITalkAssistantShow(int from) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.B_AI_TALK_ASSISTANT_SET_SHOW)
                .param("p", from)
                .debug()
                .build();
    }

    public static void statsAITalkAssistantClick(int clickPosition) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.B_AI_TALK_ASSISTANT_SET_CLICK)
                .param("p", clickPosition)
                .debug()
                .build();
    }


    public static void statsAIProcessClick(int tab, String clickPosition) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.AI_CHAT_PROCESS_CLICK)
                .param("p", tab)
                .param("p2", clickPosition)
                .debug()
                .build();
    }

    public static void statsAIProcessExpo(int tab, int position, int model) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.AI_CHAT_PROCESS_EXPOSE)
                .param("p", tab)
                .param("p2", position)
                .param("p3", model)
                .debug()
                .build();
    }

    /**
     * 羚羊计划埋点相关参数 常量
     */
    public interface GROUP_ANTELOPE {
        /**
         * 点击快捷问询, 将问询文案添加到 输入框
         */
        int FAST_ASK_CLICK = 1;
        /**
         * 点击图片
         */
        int PICTURE_CLICK = 2;
        /**
         * 点击语音
         */
        int AUDIO_CLICK = 3;
        /**
         * 点击语音通话
         */
        int AUDIO_CALL_CLICK = 4;
        /**
         * 点击视频通话
         */
        int VIDEO_CALL_CLICK = 5;
    }
}
