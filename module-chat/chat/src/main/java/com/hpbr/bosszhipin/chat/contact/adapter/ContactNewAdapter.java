package com.hpbr.bosszhipin.chat.contact.adapter;


import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListUpdateCallback;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.diff.BaseQuickDiffCallback;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.chat.contact.listener.ContactListEventListener;
import com.hpbr.bosszhipin.chat.contact.provider.ContactGroupChatProvider;
import com.hpbr.bosszhipin.chat.contact.provider.ContactSingleProvider;
import com.hpbr.bosszhipin.chat.contact.provider.ContactSystemProvider;
import com.hpbr.bosszhipin.chat.contact.provider.ContactTopDividerProvider;
import com.hpbr.bosszhipin.chat.contact.provider.ContactWeekAgeProvider;
import com.hpbr.bosszhipin.chat.contact.provider.NewHelloSortProvider;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.techwolf.lib.tlog.TLog;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class ContactNewAdapter extends BaseMultipleItemRvAdapter<ContactBean, BaseViewHolder> {

    public static final String TAG = "ContactNewAdapter";
    private ContactListEventListener contactListEventListener;

    private OnItemClickCallBack onItemClickCallBack;

    private final Set<String> currentChangedContactIds = Collections.newSetFromMap(new ConcurrentHashMap<String, Boolean>());

    public ContactNewAdapter(@Nullable List<ContactBean> data, @NonNull ContactListEventListener contactListEventListener, OnItemClickCallBack itemClickCallback) {
        super(data);
        this.contactListEventListener = contactListEventListener;
        this.onItemClickCallBack = itemClickCallback;
    }

    @Override
    protected int getViewType(List<ContactBean> data, int position) {
        ContactBean contactBean = data.get(position);
        return getViewType(contactBean);
    }

    private int getViewType(ContactBean contactBean) {
        if (contactBean.friendId == NewHelloSortProvider.TYPE) {
            return NewHelloSortProvider.TYPE;
        }
        if (contactBean.friendId == ContactTopDividerProvider.TYPE) {
            return ContactTopDividerProvider.TYPE;
        }
        if (contactBean.friendId == ContactWeekAgeProvider.TYPE) {
            return ContactWeekAgeProvider.TYPE;
        }
        if (F2ContactHelper.getInstance().isSystemContact(contactBean.friendId)) {
            return ContactSystemProvider.TYPE;
        }
        if (contactBean.isGroup) {
            return ContactGroupChatProvider.TYPE;
        }
        return ContactSingleProvider.TYPE;
    }

    @Override
    public void registerItemProvider() {
        //本地联系人
        registerProvider(new ContactSystemProvider(contactListEventListener, onItemClickCallBack));
        //群聊
        registerProvider(new ContactGroupChatProvider(contactListEventListener, onItemClickCallBack));
        //单聊
        registerProvider(new ContactSingleProvider(contactListEventListener, onItemClickCallBack));
        //一周之前分割线
        registerProvider(new ContactWeekAgeProvider(contactListEventListener));
        //置顶分割线
        registerProvider(new ContactTopDividerProvider(contactListEventListener));
        //新招呼排序
        registerProvider(new NewHelloSortProvider(contactListEventListener));
    }


    public int getItemPosition(ContactBean item) {
        return item != null && mData != null && !mData.isEmpty() ? mData.indexOf(item) : -1;
    }


    boolean isReportTimeDelay = false;

    @Override
    protected DiffUtil.ItemCallback<ContactBean> getAsyncDiffCallback() {
        return new DiffUtil.ItemCallback<ContactBean>() {
            @Override
            public boolean areItemsTheSame(@NonNull ContactBean oldItem, @NonNull ContactBean newItem) {
                return (oldItem.friendId == newItem.friendId && oldItem.friendSource == newItem.friendSource);
            }

            @Override
            public boolean areContentsTheSame(@NonNull ContactBean oldItem, @NonNull ContactBean newItem) {
                //不满足灰度或者不是单聊的，走之前的逻辑
                if (!AndroidDataStarGray.getInstance().openContactUpdateOptimize() || getViewType(newItem) != ContactSingleProvider.TYPE) return false;
                boolean isChanged = currentChangedContactIds.contains(newItem.friendId + "_" + newItem.friendSource);
                // 只有在需要更新时才打印日志
                if (isChanged) {
                    TLog.debug(TAG, "areContentsTheSame: %s_%s, changed: %b",
                            newItem.friendId, newItem.friendSource, isChanged);
                }
                return !isChanged;

            }
        };
    }

    // 替换原有setNewDiffData方法
    public void setAsyncDiffData(@NonNull List<ContactBean> newData, Set<String> tempChangedContactIds) {
        long startTime = System.currentTimeMillis();
        if(!tempChangedContactIds.isEmpty()) {
            this.currentChangedContactIds.addAll(tempChangedContactIds);
        }
        setAsyncDiffData(newData, () -> {
            long delayTime = System.currentTimeMillis() - startTime;
            if(!tempChangedContactIds.isEmpty()) {
                this.currentChangedContactIds.removeAll(tempChangedContactIds);
            }
            TLog.debug(TAG, "setAsyncDiffData = %s", delayTime);
            if (delayTime > 150) {
                TLog.info(TAG, "setNewDiffData = %s", delayTime);
                if (!isReportTimeDelay && delayTime > 1000) {
                    ApmAnalyzer.create().action("action_contact_doctor", "diff_time_out").p2(String.valueOf(delayTime)).debug().report();
                    isReportTimeDelay = true;
                }
            }
        });
    }


    public void setNewDiffData(@NonNull List<ContactBean> newData) {
        if (getEmptyViewCount() == 1) {
            setNewData(newData);
            return;
        }
        long startTime = System.currentTimeMillis();
        BaseQuickDiffCallback<ContactBean> baseQuickDiffCallback = new SimpleDiffCallback(mData, newData);
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(baseQuickDiffCallback, true);
        diffResult.dispatchUpdatesTo(new BaseQuickAdapterListUpdateCallback(this));
        long delayTime = System.currentTimeMillis() - startTime;
        if (delayTime > 50) {
            TLog.info(TAG, "setNewDiffData = %s", delayTime);
            if (!isReportTimeDelay && delayTime > 1000) {
                ApmAnalyzer.create().action("action_contact_doctor", "diff_time_out").p2(String.valueOf(delayTime)).debug().report();
                isReportTimeDelay = true;
            }
        }

        mData = baseQuickDiffCallback.getNewList();
    }


    static final class SimpleDiffCallback extends BaseQuickDiffCallback<ContactBean> {
        public SimpleDiffCallback(List<ContactBean> mData, @Nullable List<ContactBean> newList) {
            super(newList);
            setOldList(mData);
        }

        @Override
        protected boolean areItemsTheSame(@NonNull ContactBean oldItem, @NonNull ContactBean newItem) {
            return (oldItem.friendId == newItem.friendId && oldItem.friendSource == newItem.friendSource);
        }

        @Override
        protected boolean areContentsTheSame(@NonNull ContactBean oldItem, @NonNull ContactBean newItem) {
            return false;
        }
    }

    ;

    static final class BaseQuickAdapterListUpdateCallback implements ListUpdateCallback {

        @NonNull
        private final BaseQuickAdapter mAdapter;

        public BaseQuickAdapterListUpdateCallback(@NonNull BaseQuickAdapter adapter) {
            this.mAdapter = adapter;
        }

        @Override
        public void onInserted(int position, int count) {
            TLog.debug(TAG, "onInserted==== position = [%d], count = [ %d ]", position, count);
            this.mAdapter.notifyItemRangeInserted(position + mAdapter.getHeaderLayoutCount(), count);
        }

        @Override
        public void onRemoved(int position, int count) {
            TLog.debug(TAG, "onRemoved==== position = [%d], count = [ %d ]", position, count);
            this.mAdapter.notifyItemRangeRemoved(position + mAdapter.getHeaderLayoutCount(), count);
        }

        @Override
        public void onMoved(int fromPosition, int toPosition) {
            TLog.debug(TAG, "onMoved==== fromPosition = [%d], toPosition = [ %d ]", fromPosition, toPosition);
            this.mAdapter.notifyItemMoved(fromPosition + mAdapter.getHeaderLayoutCount(), toPosition + mAdapter.getHeaderLayoutCount());
        }

        @Override
        public void onChanged(int position, int count, @Nullable Object payload) {
            TLog.debug(TAG, "onChanged==== position = [%d], count = [ %d ] payload = [%s]", position, count, payload);
            this.mAdapter.notifyItemRangeChanged(position + mAdapter.getHeaderLayoutCount(), count, payload);
        }
    }

    public void dismissLongPressGuideDialog() {
        SparseArray<BaseItemProvider<ContactBean, BaseViewHolder>> itemProviders = mProviderDelegate.getItemProviders();
        if (itemProviders != null && itemProviders.size() > 0) {
            for (int i = 0; i < itemProviders.size(); i++) {
                int key = itemProviders.keyAt(i);
                BaseItemProvider<ContactBean, BaseViewHolder> baseItemProvider = itemProviders.get(key);
                if (baseItemProvider == null) continue;
                if (baseItemProvider instanceof ContactSystemProvider) {
                    ContactSystemProvider contactSystemProvider = (ContactSystemProvider) baseItemProvider;
                    contactSystemProvider.dismissLongPressGuideDialog();
                } else if (baseItemProvider instanceof ContactGroupChatProvider) {
                    ContactGroupChatProvider contactGroupChatProvider = (ContactGroupChatProvider) baseItemProvider;
                    contactGroupChatProvider.dismissLongPressGuideDialog();
                } else if (baseItemProvider instanceof ContactSingleProvider) {
                    ContactSingleProvider contactSingleProvider = (ContactSingleProvider) baseItemProvider;
                    contactSingleProvider.dismissLongPressGuideDialog();
                }
            }
        }

    }


    public interface OnItemClickCallBack {
        //用于业务端埋点 id: 对方id或群聊id
        void onItemClickListener(ContactBean contactBean, String id, int childPosition, int unReadCount);
    }
}
