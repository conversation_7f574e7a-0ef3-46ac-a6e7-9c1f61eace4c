package com.hpbr.bosszhipin.chat.single;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Build;
import android.text.TextUtils;
import android.util.Pair;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.bszp.kernel.account.AccountHelper;
import com.google.gson.reflect.TypeToken;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.business_export.BusinessConst;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.chat.CreateFriendManager;
import com.hpbr.bosszhipin.chat.R;
import com.hpbr.bosszhipin.chat.adapter.ChatAdapter;
import com.hpbr.bosszhipin.chat.contact.viewholder.SingleChatViewHolder;
import com.hpbr.bosszhipin.chat.dialog.ChangeChatPositionDialog;
import com.hpbr.bosszhipin.chat.dialog.VerbalInterviewBottomDialog;
import com.hpbr.bosszhipin.chat.exchange.ExchangeTestManager;
import com.hpbr.bosszhipin.chat.exchange.ResumeHandleManager;
import com.hpbr.bosszhipin.chat.nlp.DailyGuideBean;
import com.hpbr.bosszhipin.chat.nlp.NLPListBean;
import com.hpbr.bosszhipin.chat.single.activity.ChatBaseActivity;
import com.hpbr.bosszhipin.chat.single.activity.ChatNewActivity;
import com.hpbr.bosszhipin.chat.single.broadcast.ChatBroadcastManager;
import com.hpbr.bosszhipin.chat.single.dialog.OverdueBossInfoDialog;
import com.hpbr.bosszhipin.chat.single.listener.IChatCommon;
import com.hpbr.bosszhipin.chat.single.listener.ISwipRefreshHelper;
import com.hpbr.bosszhipin.chat.single.listener.OnClickMessageListener;
import com.hpbr.bosszhipin.chat.single.listener.OnClickSendFailViewListener;
import com.hpbr.bosszhipin.chat.single.listener.OnClickSendTextListener;
import com.hpbr.bosszhipin.chat.single.listener.OnDialogViewButtonClickCallback;
import com.hpbr.bosszhipin.chat.single.listener.OnRedMoneyViewClickCallback;
import com.hpbr.bosszhipin.chat.utils.AIHelpPanelUtils;
import com.hpbr.bosszhipin.chat.utils.AudioCovertTextUtils;
import com.hpbr.bosszhipin.chat.utils.ChatAnalysisUtil;
import com.hpbr.bosszhipin.chat.utils.ChatScrollManager;
import com.hpbr.bosszhipin.chat.utils.ChatUtils;
import com.hpbr.bosszhipin.chat.utils.ExposeAnaly;
import com.hpbr.bosszhipin.chat.utils.HKPostJobDialogUtils;
import com.hpbr.bosszhipin.chat.utils.TimeUtils;
import com.hpbr.bosszhipin.chat.view.ChatListView;
import com.hpbr.bosszhipin.common.PhoneExchangeDialog;
import com.hpbr.bosszhipin.common.SwitchCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.ChatWechatDialog;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.ReportDialog;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.common.report.Report;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.GrayStageManager;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.NLPSuggestBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactMessageTimeRangeManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.group.util.UploadAudioUtil;
import com.hpbr.bosszhipin.interviews.export.InterviewsConstant;
import com.hpbr.bosszhipin.interviews.export.InterviewsRouter;
import com.hpbr.bosszhipin.listener.Callback;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.block.utils.SmsItemUtils;
import com.hpbr.bosszhipin.module.block.utils.VirtualCallUtils;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.module.contacts.activity.ReportEvidenceActivity;
import com.hpbr.bosszhipin.module.contacts.adapter.listener.OnMessageSendCallBack;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatReaderBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.ChatReaderBeanManager;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogButtonBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.contacts.manager.ChatMessageFactory;
import com.hpbr.bosszhipin.module.contacts.manager.CommonWordManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.contacts.service.ChatBeanFactory;
import com.hpbr.bosszhipin.module.contacts.service.ChatSendCallback;
import com.hpbr.bosszhipin.module.contacts.service.transfer.ChatObserver;
import com.hpbr.bosszhipin.module.contacts.sounds.OnPlayerSoundCallback;
import com.hpbr.bosszhipin.module.contacts.sounds.OnPlayerSoundCompleteCallback;
import com.hpbr.bosszhipin.module.contacts.sounds.OnRecordSoundCallback;
import com.hpbr.bosszhipin.module.contacts.sounds.SoundFile;
import com.hpbr.bosszhipin.module.contacts.sounds.SoundPlayer;
import com.hpbr.bosszhipin.module.contacts.sr.coder.AudioSummary;
import com.hpbr.bosszhipin.module.contacts.util.SendMessageUtil;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCreateIntentBean;
import com.hpbr.bosszhipin.module.interview.entity.SelectContactParamBean;
import com.hpbr.bosszhipin.module.interview.utils.InterviewCommonUtils;
import com.hpbr.bosszhipin.module.main.common.LocationServiceInitializer;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.module.my.activity.boss.location.SelectWorkLocationActivity;
import com.hpbr.bosszhipin.module.pay.entity.ItemPaySource;
import com.hpbr.bosszhipin.module.pay.entity.PayResult;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.module_geek_export.starter.param.AttachmentResumeStarter;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.report.ReportContextUtils;
import com.hpbr.bosszhipin.utils.CloneUtils;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.DialogCommonHandler;
import com.hpbr.bosszhipin.utils.PoiLocationTranslator;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.ToastNoPermissionAsDialogUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.MessageSlideView;
import com.hpbr.bosszhipin.views.chatbottom2.BaseBottomLayout;
import com.hpbr.bosszhipin.views.exchange.utils.ExchangeHelper;
import com.hpbr.bosszhipin.views.swipe.listview.SwipeRefreshListView;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.mms.service.AppStatus;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.GeekChatRecommendJobListResponse;
import net.bosszhipin.api.BlockUserCardRequest;
import net.bosszhipin.api.BossGreetingGuideBean;
import net.bosszhipin.api.ChangeChatJobRequest;
import net.bosszhipin.api.ContactFullInfoRequest;
import net.bosszhipin.api.ContactFullInfoResponse;
import net.bosszhipin.api.ExchangeAcceptRequest;
import net.bosszhipin.api.ExchangeChatResponse;
import net.bosszhipin.api.ExchangeRejectRequest;
import net.bosszhipin.api.ExchangeTestAcceptResponse;
import net.bosszhipin.api.GeekSendPhoneRequest;
import net.bosszhipin.api.GeekSendPhoneResponse;
import net.bosszhipin.api.InterviewStatusRequest;
import net.bosszhipin.api.OneKeySendWechatRequest;
import net.bosszhipin.api.OneKeySendWechatResponse;
import net.bosszhipin.api.SendJobCardRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UnlockChatRequest;
import net.bosszhipin.api.UnlockChatResponse;
import net.bosszhipin.api.VerbalInterviewResponse;
import net.bosszhipin.api.bean.ChatPendingTaskBean;
import net.bosszhipin.api.bean.ExchangeGuideBean;
import net.bosszhipin.api.bean.JobSwitchBean;
import net.bosszhipin.api.bean.SendMsgExtraBean;
import net.bosszhipin.api.bean.ServerAddFriendBean;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.api.bean.ServerTranslatedPoiAddressBean;
import net.bosszhipin.api.bean.geek.OverdueBossInfoBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

import message.handler.MSGSender;
import message.handler.MessageTargetInfo;
import message.handler.MessageUtils;
import message.handler.analysis.ChatAnalyzer;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDaoFactory;
import message.handler.dao.MessageDoctorFactory;
import message.handler.receiver.RSingleMessageHandler;
import zpui.lib.ui.refreshlayout.ZPUIRefreshLayout;


/**
 * Created by monch on 15/10/16.<br/>
 * 此类为聊天工具类，添加了基本的聊天功能，使用者需自行调用其中的方法。生命周期方法包括：<br/>
 * onCreate->create<br/>
 * onResume->resume<br/>
 * onPause->pause<br/>
 * onDestroy->destroy<br/>
 * 其它：<br/>
 * dispatchTouchEvent->dispatchTouchEvent<br/>
 * onKeyDown->goBack<br/>
 * onActivityResult->activityResult<br/>
 */
public class ChatCommon implements SwipeRefreshListView.OnPullRefreshListener,
        OnRecordSoundCallback,
        OnPlayerSoundCompleteCallback,
        OnPlayerSoundCallback,
        OnRedMoneyViewClickCallback,
        SensorEventListener,
        OnDialogViewButtonClickCallback,    // 点击消息中对话框按钮的回调
        OnClickSendFailViewListener,    // 点击消息中重发消息的回调
        OnClickSendTextListener,    // 点击消息中的发送文字消息的回调
        ChatObserver,   // 接收新消息回调
        ChatAdapter.OnClickZPManagerUrlListener,    // 点击直聘协议回调
        OnClickMessageListener,
        ChatScrollManager.OnScrollCallBack,
        MessageSlideView.OnSlideClickCallBack,// 点击消息统一事件回调
        ChatBroadcastManager.OnReceiverCallBack {

    private static final String tag = ChatCommon.class.getSimpleName();

    private static final int SCROLL_LIST_DELAYED = 200;// 滚动ListView的延迟
    public static final String ACTION_PAUSE_AUDIO = "ACTION_PAUSE_AUDIO";

    // 上下文实例
    private FragmentActivity activity;
    // 聊天功能接口实例，用于获取子类的必要参数
    private IChatCommon iChatCommon;
    // 页面View实例
    private View windowView;

    private volatile boolean hasEncryptMessage;

    //好友蓝白
    private int friendSource = ContactBean.FROM_BOSS;

    public final BossBlockCheck bossLimitCheckUtils = new BossBlockCheck();

    public void setFriendSource(int friendSource) {
        this.friendSource = friendSource;
    }


    public int getFriendSource() {
        return friendSource;
    }

    // 异步任务执行器
    private ExecutorService taskManager = Executors.newFixedThreadPool(1, new ThreadFactory() {
        @SuppressLint("twl_thread")
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "chat-thread");
        }
    });

    // 这个集合为了防止数据重复出现，在某种特定的情况下，会出现此类问题
    private Set<Long> messageIdSet = new HashSet<>();

    // 牛人详情页接口返回的
    // &&只是用于加好友接口使用
    // &&加好友成功后需要使用聊天页面baseInfo或者FullInfo返回的新的securityId
    private String addFriendSecurityId;

    // 联系人的lid，为后台提示数据统计，本地无任何作用
    private String lid;
    // 联系人的来源
    private String from;
    // 联系人的id 用于加好友,
    private long tempFriendId;

    // 与联系人沟通使用的职位id
    private long jobId;
    // 与联系人沟通使用的求职意向id
    private long jobIntentId;
    // 开聊时职位ID
    private long jobPositionId;
    // 开聊时的城市编码
    private int jobCityCode;
    // 6.19 搜索中相似职位引导的开聊
    private int similarPosition;
    private String lightChatText;
    // 7.08 开聊入口
    private int entrance;
    // 7.13 VIP简历助手，开聊语
    private String greet;
    // 7.15 引导Boss开聊有附件简历的牛人
    private String chatContentInEditText;
    // 返回协议
    private String backProtocol;
    // 切换职位描述
    private String changePositionDesc;
    // 上页面传递进来的url
    private String url;

    private String jobAddressId;

    // 联系人实例
    private ContactBean contactData;

    // 联系人消息数据
    private final List<ChatBean> messageData = new ArrayList<>();

    // 发送消息的公共实例
    private MSGSender chatSendCommon;

    // 是否为第一次启动状态
    private boolean isOnResumeFirstStart = true;
    // 是否还有更多的消息可以上拉刷新
    private boolean hasMoreChatItem = true;

    /*809*/
    public int inviteType;//牛直邀约类型 0:简历 1:电话
    // 当前已经发送的最大的已读消息ID
    private volatile long currentMaxReadMessageId = -1;

    // 距离传感器管理器实例
    private SensorManager sensorManager;
    // 距离传感器实例
    private Sensor sensor;


    // 添加消息锁
    private static final Object ADD_MESSAGE_LOCK = new Object();
    private final String anExtend = "extends=";


    private ChatBroadcastManager mBroadcast;
    private DialogUtils blockDialog;

    private int applyJobDirectly;

    /**
     * 从搜索聊天记录里面跳转过来,直接定位到指定位置
     */
    private long smoothToTargetMessageId;

    // 1225.66【B/C】口语测试提效：通过口语测试打开聊天界面时，新增 languageId 界面入参
    private long speakTestLanguageId;
    //1302.110 【BC】蓝领C快速达成路径尝试 一键报名灰度 对照组为0，实验组为1-3
    private int startChatProcessExpGroup;
    // 1308.165【B&C】AI帮你推：是否是从Ai推荐界面打开的聊天页
    private boolean fromAiRecommend;

    public void setSmoothToTargetMessageId(long smoothToTargetMessageId) {
        this.smoothToTargetMessageId = smoothToTargetMessageId;
    }


    private String addFriendSendText;

    public void setAddFriendSendText(String addFriendSendText) {
        this.addFriendSendText = addFriendSendText;
    }


    private ISwipRefreshHelper refreshHelper;

    //记录是不是同意位置权限
    private boolean hasAgreeLocation;
    private BossGreetingGuideBean bossGreetingGuideBean;
    private int aiHelpChatType = AIHelpPanelUtils.TYPE_PANEL;
    private final List<ChatPendingTaskBean> pendingTaskAfterLocationPermissionGrant = new ArrayList<>();
    private boolean ignoreSetAIHelpChatPanel;

    private boolean isResume;
    private boolean hasReceiveMessageWhenAppPaused;

    public boolean isHasAgreeLocation() {
        return hasAgreeLocation;
    }

    public void setBossGreetingGuideBean(BossGreetingGuideBean bossGreetingGuideBean) {
        this.bossGreetingGuideBean = bossGreetingGuideBean;
    }

    public void initSwipeRefreshHelper(ZPUIRefreshLayout swipeRefreshLayout,
                                       RecyclerView mContainerView,
                                       BaseBottomLayout bottomFunctionView) {

        refreshHelper = new ChatRoomSwipRefreshHelper(swipeRefreshLayout,
                mContainerView,
                new ISwipRefreshHelper.IOnLoadMessageCallBack() {
                    @Override
                    public void loadMoreListener() {
                        onRefresh();
                    }

                    @Override
                    public void onResetBottomViewListener() {
                        bottomFunctionView.resetAllView();
                    }
                });

    }


    public void initSwipeRefreshHelper(ChatListView listView, BaseBottomLayout bottomFunctionView) {
        refreshHelper = new ChatActSwipRefreshHelper(listView, new ISwipRefreshHelper.IOnLoadMessageCallBack() {

            @Override
            public void loadMoreListener() {
                onRefresh();
            }

            @Override
            public void onResetBottomViewListener() {
                bottomFunctionView.resetAllView();
            }
        });

    }


    //是否已经添加 【仅展示30天内的聊天记录】 提示灰条消息
    private boolean hasAddHeadChatBean = false;


    /**
     * 设置接口实现
     *
     * @param activity
     */
    public void init(FragmentActivity activity) {
        this.activity = activity;
        if (!(activity instanceof IChatCommon)) {
            throw new IllegalArgumentException("上下文必须实现IChatCommon接口");
        }
        this.iChatCommon = (IChatCommon) activity;
        this.mBroadcast = new ChatBroadcastManager(activity, this);
        this.mBroadcast.setCallBack(this);
        registerPauseReceiver();
        // 再注入一次，修改权限回来页面重建数据回收。
        GrayStageManager.getInstance().setAiAssistInfo(CommonConfigManager.getInstance().getAIAssistInfo());
    }


    /**
     * 设置接口实现
     *
     * @param activity
     * @param iChatCommon
     */
    public void init(FragmentActivity activity, IChatCommon iChatCommon) {
        this.activity = activity;
        this.iChatCommon = iChatCommon;
    }

    public Activity getActivity() {
        return activity;
    }

    /**
     * 获取异步任务执行器
     *
     * @return
     */
    public ExecutorService getTaskManager() {
        return taskManager;
    }

    /**
     * 检测线程任务执行器是否可以使用
     *
     * @return
     */
    public boolean checkExecutorCanWork() {
        ExecutorService executor = getTaskManager();
        return executor != null && !executor.isShutdown();
    }

    public void submitTask(Runnable runnable) {
        if (checkExecutorCanWork()) {
            try {
                getTaskManager().submit(runnable);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置securityId
     *
     * @param securityId
     */
    public void setAddFriendSecurityId(String securityId) {
        this.addFriendSecurityId = securityId;
    }

    public String getSecurityId() {
        if (contactData != null) {
            return contactData.securityId;
        }
        return "";
    }

    /**
     * 设置lid
     *
     * @param lid
     */
    public void setLid(String lid) {
        this.lid = lid;
    }

    /**
     * 设置来源
     *
     * @param from
     */
    public void setFrom(String from) {
        this.from = from;
    }

    /**
     * 获取lid
     *
     * @return
     */
    public String getLid() {
        return this.lid;
    }


    /**
     * 设置联系人沟通职位id
     *
     * @param id
     */
    public void setJobId(long id) {
        this.jobId = id;
    }

    public void setApplyDirect(int applyDirect) {
        this.applyJobDirectly = applyDirect;
    }

    /**
     * 1225.66【B/C】口语测试提效：通过口语测试打开聊天界面时，新增 languageId 界面入参
     *
     * @param speakTestLanguageId 口语测试数据返回的 languageId
     */
    public void setSpeakTestLanguageId(long speakTestLanguageId) {
        this.speakTestLanguageId = speakTestLanguageId;
    }

    public void setStartChatProcessExpGroup(int startChatProcessExpGroup) {
        this.startChatProcessExpGroup = startChatProcessExpGroup;
    }

    public void setFromAiRecommend(boolean fromAiRecommend) {
        this.fromAiRecommend = fromAiRecommend;
    }

    public void setJobAddressId(String jobAddressId) {
        this.jobAddressId = jobAddressId;
    }

    /**
     * 获取联系人沟通职位id
     *
     * @return
     */
    public long getJobId() {
        return this.jobId;
    }

    /**
     * 设置联系人沟通求职意向id
     *
     * @param id
     */
    public void setJobIntentId(long id) {
        this.jobIntentId = id;
    }

    /**
     * 获取联系人沟通求职意向id
     *
     * @return
     */
    public long getJobIntentId() {
        return jobIntentId;
    }

    public long getJobPositionId() {
        return jobPositionId;
    }

    public void setJobPositionId(long jobPositionId) {
        this.jobPositionId = jobPositionId;
    }

    public int getJobCityCode() {
        return jobCityCode;
    }

    public void setJobCityCode(int jobCityCode) {
        this.jobCityCode = jobCityCode;
    }

    public String getBackProtocol() {
        return backProtocol;
    }

    public void setBackProtocol(String backProtocol) {
        this.backProtocol = backProtocol;
    }


    public void setChangePositionDesc(String changePositionDesc) {
        this.changePositionDesc = changePositionDesc;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isOnResumeFirstStart() {
        return isOnResumeFirstStart;
    }

    /**
     * 获取本地是否还有更多联系人消息
     *
     * @return
     */
    public boolean getMessageHasMore() {
        return this.hasMoreChatItem;
    }

    public long getFriendId() {
        if (contactData != null) {
            return contactData.friendId;
        }
        return tempFriendId;
    }

    /**
     * 获取消息数据列表
     *
     * @return
     */
    public List<ChatBean> getMessageData() {
        if (canShowHeadView() && !hasAddHeadChatBean) {
            hasAddHeadChatBean = true;
        }
        return messageData;
    }


    /**
     * 获取消息数据列表
     *
     * @return
     */
    public List<ChatBean> getMessageData2() {


        if (canShowHeadView() && !hasAddHeadChatBean) {
            hasAddHeadChatBean = true;
            //没有更多聊天内容,显示 【仅展示30天内的聊天记录】
            String messageTimeRangeRecord = ContactMessageTimeRangeManager.getInstance().getMessageTimeRangeRecord();
            if (!LText.empty(messageTimeRangeRecord)) {
                synchronized (ADD_MESSAGE_LOCK) {
                    messageData.add(0, ChatUtils.createTimeChatBean(messageTimeRangeRecord));
                }
            }
        }

        return messageData;
    }

    public int getSimilarPosition() {
        return similarPosition;
    }

    public void setSimilarPosition(int similarPosition) {
        this.similarPosition = similarPosition;
    }

    public String getLightChatText() {
        return lightChatText;
    }

    public void setLightChatText(String lightChatText) {
        this.lightChatText = lightChatText;
    }

    public int getEntrance() {
        return entrance;
    }

    public void setEntrance(int entrance) {
        this.entrance = entrance;
    }


    public void setGreet(String greet) {
        this.greet = greet;
    }


    public void setChatContentInEditText(String chatContentInEditText) {
        this.chatContentInEditText = chatContentInEditText;
    }

    public int getInviteType() {
        return inviteType;
    }

    public void setInviteType(int inviteType) {
        this.inviteType = inviteType;
    }

    /**
     * 初始化，对应页面中onCreate，需要在setContentView之后调用
     *
     * @param friendId
     */
    public void create(long friendId) {
        tempFriendId = friendId;
        // 检测必要参数是否已被初始化
        if (activity == null || iChatCommon == null) {
            throw new IllegalArgumentException("调用create之前，必须先调用初始化init方法");
        }
        // 初始化view
        initView();
        // 初始化功能
        initCommon();
        // 初始化广播接收器
        initReceiver();
        // 初始化联系人
        initContact();
        //刷新返回键未读数量
        refreshContactInfo();
    }

    public void reset() {
        isOnResumeFirstStart = true;
        synchronized (ADD_MESSAGE_LOCK) {
            messageData.clear();
        }
        refreshMessageData();
    }

    /**
     * 初始化页面view
     */
    private void initView() {
        // 初始化布局文件
        windowView = iChatCommon.initConvertView();
        // 检测必要参数是否已被返回
        if (windowView == null) {
            throw new IllegalArgumentException("初始化布局文件返回的View不能为空");
        }
        // 初始化标题
        iChatCommon.initTitle(null);
        // 初始化其它控件
        iChatCommon.initView();
        // 获取下拉刷新控件，为其设置监听事件
        //  SwipeRefreshListView listView = iChatCommon.getListView();
        //  if (listView != null) listView.setOnPullRefreshListener(this);

        if (refreshHelper != null) {
            refreshHelper.onPullRefresh();
        }
    }


    /**
     * 初始化功能变量
     */
    private void initCommon() {
        sensorManager = (SensorManager) activity.getSystemService(Context.SENSOR_SERVICE);
        sensor = sensorManager.getDefaultSensor(Sensor.TYPE_PROXIMITY);
        chatSendCommon = new MSGSender();
    }

    /**
     * 初始化广播接收器
     */
    private void initReceiver() {
        // 注册新消息接收器
        ChatMessageFactory.getInstance().createChatTransfer().register(this);
        //注册广播
        mBroadcast.register();
        observeContacts();
    }

    private void observeContacts() {
        ContactManager.getInstance().observeContacts(activity, new Observer<List<ContactBean>>() {
            @Override
            public void onChanged(List<ContactBean> contactBeans) {
                refreshContactInfo();
            }
        });
        ContactManager.getInstance().observeContact(activity, new Observer<Long>() {
            @Override
            public void onChanged(Long aLong) {
                if (contactData == null) return;
                long friendId = contactData.friendId;
                if (aLong != null && aLong == friendId) {
                    //刷新好友状态
                    refreshFriendStatus();
                    // 刷新返回键未读消息数量
                    refreshContactInfo();
                }
            }
        });
    }

    //是否需要加好友
    private boolean isNeedAddFriend;

    /**
     * 初始化联系人数据
     */
    private void initContact() {
        /*
        --------------------------------------------------------------------------------
        * 兼容匿名牛人反馈场景： https://bpm.zhipin.com/system/support/process/approval?processInstanceId=402c5a59935a9fc8pBYH2929EFJQx4-9VfqZROain_fQMw~~&tabType=2
        * --------------------------------------------------------------------------------
        *  1.如果friendId>0
        *     判断本地是否存在联系人，
        *     本地不存在走加好友流程
        *     本地存在加载消息/fullInfo接口
        * --------------------------------------------------------------------------------
        *  2.如果friendId<=0
        *     如果存在securityId走加好友流程
        * --------------------------------------------------------------------------------
        * 注：正常应该判断>1000,小于1000是系统id，IOS目前判断的是 >0 暂保持一致
        * --------------------------------------------------------------------------------
        * */
        contactData = ContactManager.getInstance()
                .queryContactByFriendId(tempFriendId, UserManager.getUserRole().get(), friendSource);
        if (contactData == null||tempFriendId==0) {
            if (contactData != null) {
                ApmAnalyzer.create().action("action_temp", "addZeroFriendId").report();
            }

            //需要加好友
            isNeedAddFriend = true;
            // 同步
            checkLocationUpload(() -> createContact(tempFriendId, jobId, friendSource));
        } else {
            //异步
             checkLocationUpload(null);
             loadContactInfo(true);

        }
    }

    private void checkLocationUpload(Runnable runnable) {
        if (UserManager.isGeekRole()) {
            if (runnable != null) runnable.run();
            return;
        }
        LocationPermissionHelper
                .getLocationHelper(activity, LocationPermissionHelper.BOSS_CHAT,
                         new PermissionHelper.ExtendBean()
                        .setAnalyticName(PermissionConstants.SCENE_CHAT)
                        .setFriendId(String.valueOf(tempFriendId)))
                .setPermissionCallback(new PermissionCallback<LocationPermissionHelper.LocationPermissionData>() {
                    @Override
                    public void onResult(boolean yes, @NonNull LocationPermissionHelper.LocationPermissionData permission) {
                        if (yes) {
                            onPermissionNextRun(runnable);
                        } else {
                            activity.finish();
                        }
                    }

                    @Override
                    public boolean onSkipPermission(@NonNull LocationPermissionHelper.LocationPermissionData permission) {
                        onPermissionNextRun(runnable);
                        return true;
                    }
                })
                .requestPermission();
    }

    private void onPermissionNextRun(Runnable runnable) {
        iChatCommon.onLocatePermissionGranted();
        if (runnable != null) runnable.run();
        LocationServiceInitializer.checkChatLocation();
        hasAgreeLocation = true;
        showOtherDialog();
        executePendingTasks();
    }

    private void showOtherDialog() {
       if (!iChatCommon.checkShowEditGreet(bossGreetingGuideBean)) {
           iChatCommon.showEditBossPosition();
       }
    }

    private void executePendingTasks() {
        if (LList.isEmpty(pendingTaskAfterLocationPermissionGrant)) {
            return;
        }

        for (ChatPendingTaskBean chatPendingTaskBean : pendingTaskAfterLocationPermissionGrant) {
            if (chatPendingTaskBean != null && chatPendingTaskBean.execute != null) {
                chatPendingTaskBean.execute.run();
            }
        }

        pendingTaskAfterLocationPermissionGrant.clear();
    }

    /**
     * 创建好友关系
     *
     * @param friendId 好友ID
     * @param jobId    职位ID
     */
    private void createContact(final long friendId, final long jobId, int friendSource) {
        CreateFriendManager.FriendParams params = new CreateFriendManager.FriendParams();
        params.setFriendId(String.valueOf(friendId));
        params.setJobId(String.valueOf(jobId));
        params.setExpectId(String.valueOf(jobIntentId));
        params.setLid(lid);
        params.setFrom(from);
        params.setJobAddressId(jobAddressId);
        params.setFriendSource(friendSource);
        params.setSimilarPosition(String.valueOf(similarPosition));
        params.setSecurityId(addFriendSecurityId);
        params.setEntrance(entrance);
        params.setGreet(greet);
        params.setApplyJobDirectly(applyJobDirectly);
        // 1225.66【B/C】口语测试提效：通过口语测试建立好友关系时，新增 languageId 接口入参
        params.setSpeakTestLanguageId(String.valueOf(speakTestLanguageId));
        //1302.110 【BC】蓝领C快速达成路径尝试 一键报名灰度 对照组为0，实验组为1-3
        params.setStartChatProcessExpGroup(startChatProcessExpGroup);
        params.setFromAiRecommend(fromAiRecommend);
        new CreateFriendManager(activity).createFriend(params, new CreateFriendManager.OnCreateFriendCallBack() {
            @Override
            public void onSuccessListener(ContactBean mContactBean) {

                contactData = mContactBean;

                try {
                    //加好友完毕后服务器返回了securityId,客户端没有获得
                    String printContact = mContactBean.toString();

                    loadContactInfo(false);

                    //打印日志
                    if (UserManager.isBossRole()
                            && friendSource == ContactBean.FROM_BOSS
                            && LText.empty(mContactBean.securityId)) {
                        TLog.info("ChatNewActivity.TAG", "securityId empty %s", LText.toNoNullString(printContact, ""));
                    }


                    //特殊场景 需要客户端加好友后自己发送
                    if (!LText.empty(addFriendSendText)) {
                        //延时半秒发送一个消息
                        App.get().getMainHandler().postDelayed(() -> sendTextMessage(addFriendSendText, false), 400);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onFailedListener(ErrorReason reason) {
                ToastNoPermissionAsDialogUtil.showAlertToast(activity, reason, true);
            }
        });
        //接口请求接口，接口会发送文案，客户的就不发送
        greet = "";
    }


    /**
     * 初始化联系人
     */
    private void loadContactInfo(boolean requestFullInfo) {
        if (contactData == null) return;
        requestPostNotification(requestFullInfo); //1303.613【C】多场景引导用户打开push  提升push覆盖率@陈红羽
        TLog.info(TAG, "load contact %d %d", contactData.friendId, contactData.friendSource);
        if (requestFullInfo && contactData.needRequestFullInfo()) {
            // 被动好友,需要请求更新数据
            requestFriendFullinfo();
        }
        iChatCommon.initContactInfo(contactData);
        iChatCommon.initTitle(contactData);
        iChatCommon.refreshHeadTips();

        refreshLoadMorePage(true);
        createChatReaderMessageAndSend();
        dealChangePosition();

        // 现在暂时用于【一键投递】功能，在未上传附件简历时候，添加联系人成功时进行的二次跳转
        if (!TextUtils.isEmpty(url)) {
            new ZPManager(activity, url).handler();
        }


        // 创建好友之后，将传入的文案放入输入框
        setContentInEditText();

        // 809 打电话
        if (inviteType == 1) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_TOPCARD_QUICKSEND)
                    .secId(addFriendSecurityId)
                    .param("p", jobId)
                    .param("p2", contactData.friendId)
                    .param("p3", 1)
                    .build();

            sendPhoneCall();

        } else if (inviteType == 2) {
            if (checkIfHasWechat()) {
                sendWechatCard();
            }
        }
        // 优先  面试＞ 拒绝＞已读不回
        // 1301.604【C】蓝领-具有到店面试意图时推荐附近职位@徐楠@张群
        // 1216.609【C端】蓝领-对话中含拒绝意图场景推荐更多职位
        if (!requestRecommendJob()) {
            // 1214 牛人请求推荐职位
            getGeekRecommendJobs();
        }
    }

    //1303.613【C】多场景引导用户打开push  提升push覆盖率@陈红羽@徐楠@陈青云
    private void requestPostNotification(boolean requestFullInfo) {
        if (!requestFullInfo) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && AccountHelper.isGeek()
                    && NotificationCheckUtils.isShowNewGuideCloseNotification()
                    && SpManager.get().global("Permission").getLong("PostNotification", 0) <= 0) {
                PermissionHelper.getPostNotificationHelperLiteChat(activity).setPermissionCallback(new PermissionCallback<PermissionData>() {
                    @Override
                    public void onResult(boolean yes, @NonNull PermissionData permission) {
                        SpManager.get().global("Permission").edit().putLong("PostNotification", System.currentTimeMillis()).apply();
                    }
                }).requestPermission();
            }
        }
    }

    /**
     * 809 牛人发送电话
     */
    private void sendPhoneCall() {
        GeekSendPhoneRequest request = new GeekSendPhoneRequest(new ApiRequestCallback<GeekSendPhoneResponse>() {

            @Override
            public void onStart() {
                iChatCommon.showProgressDialog("");
            }

            @Override
            public void onSuccess(ApiData<GeekSendPhoneResponse> data) {
            }

            @Override
            public void onComplete() {
                iChatCommon.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = TextUtils.isEmpty(addFriendSecurityId) ? "" : addFriendSecurityId;
        request.lid = lid;
        HttpExecutor.execute(request);
    }

    private boolean checkIfHasWechat() {
        if (TextUtils.isEmpty(UserManager.getWeiXin())) {
            // 弹出对话框要求当前用户设置微信
            ChatWechatDialog dialog = new ChatWechatDialog(activity, new ChatWechatDialog.IWechatClickListener() {
                @Override
                public void setWechat(String input) {
                    sendWechatCard();
                }
            });
            dialog.show();

            if (contactData != null) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_TOPCARD_QUICKSEND)
                        .secId(addFriendSecurityId)
                        .param("p", jobId)
                        .param("p2", contactData.friendId)
                        .param("p3", 2)
                        .param("p4", 2)
                        .build();
            }
            return false;
        }

        if (contactData != null) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_TOPCARD_QUICKSEND)
                    .secId(addFriendSecurityId)
                    .param("p", jobId)
                    .param("p2", contactData.friendId)
                    .param("p3", 2)
                    .param("p4", 1)
                    .build();
        }
        return true;
    }

    private void sendWechatCard() {
        OneKeySendWechatRequest request = new OneKeySendWechatRequest(new ApiRequestCallback<OneKeySendWechatResponse>() {

            @Override
            public void onStart() {
                iChatCommon.showProgressDialog("");
            }

            @Override
            public void onSuccess(ApiData<OneKeySendWechatResponse> data) {
            }

            @Override
            public void onComplete() {
                iChatCommon.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.lid = lid;
        request.securityId = TextUtils.isEmpty(addFriendSecurityId) ? "" : addFriendSecurityId;
        HttpExecutor.execute(request);
    }


    /**
     * 处理牛人对Boss聊天切换职位
     */
    private void dealChangePosition() {

        if (contactData == null) return;

        if (UserManager.getUserRole() == ROLE.GEEK
                && contactData.jobId!=jobId
                &&jobId>0) {

            if (activity == null || activity.isFinishing()) return;
            AnalyticsFactory.create()
                    .action("add-job-change")
                    .param("p", String.valueOf(contactData.friendId))
                    .param("p2", String.valueOf(getJobId()))
                    .param("p3", String.valueOf(getJobIntentId()))
                    .param("p4", getLid())
                    .build();
            ChangeChatPositionDialog d = new ChangeChatPositionDialog(activity);
            d.setPosition(changePositionDesc);
            d.setOnSwitchPositionListener(new ChangeChatPositionDialog.OnSwitchPositionListener() {
                @Override
                public void onSwitchPosition() {
                    changeChatPosition();
                }
            });
            d.show();
        }
    }

    /**
     * 切换职位
     */
    private void changeChatPosition() {
        if (contactData == null) return;

        iChatCommon.showProgressDialog("正在为您切换开聊职位");
        final int myUserRole = UserManager.getUserRole().get();
        ChangeChatJobRequest request = new ChangeChatJobRequest(new ApiRequestCallback<SuccessBooleanResponse>() {

            @Override
            public void handleInChildThread(ApiData<SuccessBooleanResponse> data) {
                super.handleInChildThread(data);
                if (data.resp.isResult()) {
                    if (contactData != null) {
                        contactData.jobId = jobId;
                        ContactManager.getInstance().insertOrUpdateServerField(contactData, myUserRole);
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                if (data.resp.isResult()) {
                    iChatCommon.refreshShowData();
                } else {
                    ToastUtils.showText("切换职位失败");
                }
            }

            @Override
            public void onComplete() {
                iChatCommon.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (reason.getErrCode() == 1049) { // 该code的作用用来关闭本页面并且刷新前一个详情页面
                    Intent intent = new Intent(Constants.RECEIVER_REFRESH_JOB_DETAIL_ACTION);
                    intent.putExtra(Constants.DATA_JOB_ID, jobId);
                    ReceiverUtils.sendBroadcast(activity, intent);
                }
                T.ss(reason.getErrReason());
            }
        });

        request.friendId = String.valueOf(contactData.friendId);
        request.newJobId = String.valueOf(jobId);
        request.expectId = String.valueOf(jobIntentId);
        if (contactData != null) {
            request.oldJobId = String.valueOf(contactData.jobId);
            request.oldExpectId = String.valueOf(contactData.jobIntentId);
            request.friendSource = contactData.friendSource;
        }
        request.lid = getLid();
        HttpExecutor.execute(request);
    }

    /**
     * 请求被动联系人的数据
     */
    private void requestFriendFullinfo() {

        if (contactData == null) return;

        ContactFullInfoRequest request = new ContactFullInfoRequest(new ApiRequestCallback<ContactFullInfoResponse>() {

            @Override
            public void handleInChildThread(ApiData<ContactFullInfoResponse> data) {
                super.handleInChildThread(data);
                ContactFullInfoResponse resp = data.resp;
                if (resp != null && resp.fullInfo != null) {
                    ServerAddFriendBean fullInfo = resp.fullInfo;
                    if (fullInfo.getFriendId() <= 0) {

                        //接口返回 friendId=0,客户端本地删除联系人关闭聊天页面
                        ApmAnalyzer.create().action("action_temp",
                                        "fullInfo_delete_friend")
                                .p2(String.valueOf(contactData.friendId))
                                .p3(String.valueOf(contactData.friendName))
                                .report();

                        ContactManager.getInstance().deleteContact(contactData);
                        MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), getFriendId(), getFriendSource());
                        /*先删除数据，后关闭页面*/
                        AppUtil.finishActivity(activity);
                    } else {
                        // addTime >= delTime  需要删除之前的消息 只处理这里
                        if (contactData != null && contactData.lastRefreshTime <= 0 && data.resp.fullInfo.getAddTime() > 0 && data.resp.fullInfo.getDelTime() > 0 && data.resp.fullInfo.getAddTime() >= data.resp.fullInfo.getDelTime()) {
                            int removeChatCount = -1;
                            if (AndroidDataStarGray.getInstance().isMessageDelV2()) {
                                removeChatCount = MessageDaoFactory.getMessageDao().removeChatList(resp.userId, resp.identity, fullInfo.getFriendId(), data.resp.fullInfo.getFriendSource(), data.resp.fullInfo.getDelTime());
                            }
                            if (removeChatCount > 0) {
                                ApmAnalyzer.create().action("action_contact_doctor", "add_del_friend")
                                        .p2(String.valueOf(data.resp.fullInfo.getFriendId()))
                                        .p3(String.valueOf(data.resp.fullInfo.getDelTime()))
                                        .p4(String.valueOf(removeChatCount))
                                        .debug()
                                        .report();
                            }
                        }
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<ContactFullInfoResponse> data) {
                ContactFullInfoResponse resp = data.resp;
                if (resp != null && resp.fullInfo != null) {
                    ServerAddFriendBean fullInfo = resp.fullInfo;
                    if (fullInfo.getFriendId() > 0) {
                        //从缓存查找 接口对应的联系人 有可能接口返回的联系人非当前聊天的联系人
                        ContactBean contactServer = ContactManager.getInstance().queryContactByFriendId(fullInfo.getFriendId(), UserManager.getUserRole().get(), fullInfo.getFriendSource());
                        if (contactServer != null) {
                            contactServer.fromServerContactFullInfoBean(fullInfo, resp.userId, resp.identity);
                            ContactManager.getInstance().insertOrUpdateServerField(contactServer, UserManager.getUserRole().get());
                            //日志记录下
                            if (contactData != null && fullInfo.getFriendId() != contactData.friendId) {
                                TLog.info("ChatNewActivity.TAG", "fullInfo friendId is not current chat friendId fullInfo=" + fullInfo.getFriendId() + "=friendId" + contactData.friendId);
                            }

                        }
                        if (contactData != null) {
                            contactData = ContactManager.getInstance().queryContactByFriendId(contactData.friendId, UserManager.getUserRole().get(), contactData.friendSource);
                        }
                    }


                }
            }

            @Override
            public void onComplete() {
                refreshContactInfo();
                //刷新阻断
                iChatCommon.refreshShowData();
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.friendId = String.valueOf(contactData.friendId);
        request.friendSource = friendSource;
        request.securityId = contactData.securityId;
        HttpExecutor.execute(request);
    }

    /**
     * 好友关系发生变化
     */
    public void refreshFriendStatus() {
        if (!checkExecutorCanWork()) return;
        submitTask(() -> {
            //刷新黄条状态,比如拉黑,冻结等状态
            App.get().getMainHandler().post(() -> RefreshBlackViewStatus());
        });
    }


    public void reloadPageMessage() {
        if (!checkExecutorCanWork()) return;
        submitTask(() -> {
            //获得当前页面最新的数据
            MessageDao messageDao = MessageDaoFactory.getMessageDao();
            if (contactData != null) {
                int messageSize = LList.getCount(messageData) - (hasAddHeadChatBean ? 1 : 0);
                synchronized (ADD_MESSAGE_LOCK) {
                    List<ChatBean> filterList = filterChatList(messageDao.reloadPageMessage(contactData.myId, contactData.myRole, messageData, contactData.friendId, contactData.friendSource));
                    messageData.clear();
                    messageData.addAll(filterList);
                }
                hasAddHeadChatBean = false;
                TLog.info(tag, "reloadPageMessage messageSize = %s %s", messageSize, LList.getCount(messageData));
                if (messageSize != LList.getCount(messageData)) {
                    ChatAnalyzer.report("fix_page_reload_message").p2(String.valueOf(messageSize)).p3(String.valueOf(LList.getCount(messageData))).debug().report();
                }

            }

            // 刷新当前列表数据
            App.get().getMainHandler().post(() -> refreshMessageData());
        });
    }

    /**
     * 刷新当前页面数据
     */
    public void refreshCurrentPage() {
        refreshCurrentPage(0L);
    }

    /**
     * 删除聊天页消息的场景 action消息 type=96触发
     * @param deleteMsgId
     */
    public void refreshCurrentPage(long deleteMsgId) {
        if (!checkExecutorCanWork()) return;
        submitTask(() -> {
            //刷新未读数量
            refreshMessageNoneRead();
            //获得当前页面最新的数据
            MessageDao messageDao = MessageDaoFactory.getMessageDao();
            if (deleteMsgId > 0L) {
                deleteMsgFromMessageData(deleteMsgId);
            }
            if (contactData != null) {
                synchronized (ADD_MESSAGE_LOCK) {
                    List<ChatBean> filterList = filterChatList(messageDao.getChatRefreshList(contactData.myId, contactData.myRole, messageData, contactData.friendId));
                    messageData.clear();
                    messageData.addAll(filterList);
                    dealVoiceMessage();
                }
            }

            // 刷新当前列表数据
            App.get().getMainHandler().post(() -> refreshMessageData());
        });
    }

    private void deleteMsgFromMessageData(long deleteMsgId) {
        if (LList.isEmpty(messageData)) {
            return;
        }

        Iterator<ChatBean> iterator = messageData.iterator();
        while (iterator.hasNext()) {
            ChatBean chatBean = iterator.next();
            if (chatBean != null && chatBean.msgId == deleteMsgId) {
                iterator.remove();
            }
        }
    }

    /**
     * 要求第一次不显示头部30天联系人文案,在拉一次才显示
     */
    private int pullFullCount = 0;

    public boolean canShowHeadView() {
        return pullFullCount > 1;
    }


    //记录卡片第一次进来,收到新消息场景 曝光
    private final ExposeAnaly exposeAnaly = new ExposeAnaly();

    /**
     * 刷新加载更多后列表数据
     */
    public void refreshLoadMorePage(final boolean isSmoothToBottom) {
        if (!checkExecutorCanWork()) return;
        //是否从搜索聊天进来,跳转到制指定的位置
        boolean smoothToTargetPosition = smoothToTargetMessageId > 0;

        submitTask(() -> {

            //刷新未读数量
            refreshMessageNoneRead();
            checkEncryptMessage();
            //获得当前页面最新的数据
            MessageDao messageDao = MessageDaoFactory.getMessageDao();

            int oldMessageCount;
            long allCount = 0;
            long pullSmoothTargetId = 0;

            synchronized (ADD_MESSAGE_LOCK) {
                oldMessageCount = messageData.size();
                List<ChatBean> filterList;
                if (contactData != null) {

                    ChatBean firstItem = LList.getElement(messageData, 0);
                    if (firstItem != null) {
                        pullSmoothTargetId = firstItem.id;
                    }

                    if (smoothToTargetPosition) {
                        filterList = filterChatList(messageDao.getChatMoreList(contactData.myId, contactData.myRole, messageData, contactData.friendId, smoothToTargetMessageId, friendSource));
                    } else {
                        filterList = filterChatList(messageDao.getChatMoreList(contactData.myId, contactData.myRole, messageData, contactData.friendId, friendSource));
                    }
                    messageData.clear();
                    messageData.addAll(filterList);
                    allCount = messageDao.getSingleChatCount(contactData.myId, contactData.myRole, contactData.friendId, friendSource);


                    //有个反馈 第一次打开聊天窗口没有数据（DB里面有），后续打开才有
                    String messageInfo = "queryMsgCount=" +
                            LList.getCount(filterList) +
                            "，allCount=" +
                            allCount;
                    TLog.info(TAG, "refreshLoadMorePage is :%s", messageInfo);
                }


            }


            //曝光列表数据
            exposeAnaly.checkAndExposeAnalyList(messageData, friendSource);

            //从消息获取securityId,赋值给联系人
            updateContactSecurityId(oldMessageCount);

            hasMoreChatItem = allCount > LList.getCount(messageData);

            //系统或者手动执行下拉列表次数,第一次进来执行一次
            if (!hasMoreChatItem) {
                pullFullCount++;
            }


            final long pullId = pullSmoothTargetId;
            dealVoiceMessage();
            App.get().getMainHandler().post(() -> {

                //刷新聊天列表
                refreshMessageData();

                //刷新完毕聊天列表后发送文案，只发送一次
                checkSendNiceText();

                //第一次进来滑动到指定位置
                if (smoothToTargetPosition) {
                    refreshHelper.smoothToTargetMsgShake(smoothToTargetMessageId, messageData);
                    smoothToTargetMessageId = 0;
                    return;
                }
                // 第一次进来滑动到底部
                if (isSmoothToBottom) {
                    if (ActivityUtils.isValid(activity)) {
                        scrollToBottom(true);
                    }
                    return;
                }

                //下拉刷新
                loadMoreAndSmoothToPosition(pullId);
            });


        });
    }

    private void dealVoiceMessage() {
        if (!DataStarGray.getInstance().isVoiceTranscriptionButtonEnabled()) return;
        synchronized (ADD_MESSAGE_LOCK) {
            boolean isClean = false;
            for (ChatBean chatBean : messageData) {
                if (chatBean != null && chatBean.message != null && chatBean.message.messageBody != null
                        && chatBean.message.messageBody.sound != null && contactData != null && chatBean.fromUserId == contactData.friendId) {

                    if (chatBean.message.messageBody.sound.isNotCovertOrPlay() && CommonConfigManager.getInstance().shouldShowSoundRedPoint(chatBean.time) && !isClean) {
                        chatBean.message.messageBody.sound.setIsPageFirst(true);
                        isClean = true;
                    } else if (isClean) {
                        chatBean.message.messageBody.sound.setIsPageFirst(false);
                    }
                }
            }
        }
    }

    public void soundFirstPlay(long messageId) {
        try {
            synchronized (ADD_MESSAGE_LOCK) {
                MessageDao messageDao = MessageDaoFactory.getMessageDao();
                for (ChatBean chatBean : messageData) {
                    if (chatBean != null && chatBean.message != null && chatBean.msgId == messageId
                            && chatBean.message.messageBody != null && chatBean.message.messageBody.sound != null) {
                        chatBean.message.messageBody.sound.saveIsPlayed(1);
                        SimpleApiRequest.POST(ChatUrlConfig.URL_UPDATE_SOUND_MSG)
                                .addParam("messageId", messageId)
                                .addParam("play", chatBean.message.messageBody.sound.getIsPlayed())
                                .addParam("trans", chatBean.message.messageBody.sound.getIsTrans())
                                .execute();
                        AppThreadFactory.POOL.execute(() -> messageDao.update(chatBean));
                        break;
                    }
                }
                dealVoiceMessage();
                refreshMessageData();
            }
        } catch (Exception e) {
            TLog.error(TAG, "onCovertSound error:%s", e);
        }
    }

    public void soundFirstTrans(long messageId, boolean hasText) {
        try {
            synchronized (ADD_MESSAGE_LOCK) {
                MessageDao messageDao = MessageDaoFactory.getMessageDao();
                for (ChatBean chatBean : messageData) {
                    if (chatBean != null && chatBean.message != null && chatBean.msgId == messageId
                            && chatBean.message.messageBody != null && chatBean.message.messageBody.sound != null) {
                        chatBean.message.messageBody.sound.saveIsTrans(1);
                        AppThreadFactory.POOL.execute(() -> messageDao.update(chatBean));
                        break;
                    }
                }

                if (hasText) {
                    dealVoiceMessage();
                }
                refreshMessageData();
            }
        } catch (Exception e) {
            TLog.error(TAG, "onCovertSound error:%s", e);
        }
    }


    //记录只发送一次
    private boolean hasSendNiceText;

    //到聊天页面刷新完毕后需要,发送文本
    private void checkSendNiceText() {
        if (hasSendNiceText) return;
        hasSendNiceText = true;
        //每次从继续沟通进来 好友情况 发送一个默认文案
        if (!LText.empty(greet)) {
            sendTextMessage(greet, false);
        }
        // 获取详情页的简聊文字后，发送文本消息
        if (!TextUtils.isEmpty(lightChatText)) {
            sendTextMessage(lightChatText, false);
        }
    }

    //从消息获取securityId,赋值给联系人
    private void updateContactSecurityId(int oldMessageCount) {
        /**
         *  case:解决getBaseInfo/getFullInfo接口超时异常,获得不到securityId
         *  .联系人没有securityId,第一次load聊天纪录,从15个最近聊天消息里面获取securityId赋值到联系人
         */

        if (oldMessageCount == 0 && contactData != null && LText.empty(contactData.securityId)) {
            for (ChatBean chatBean : messageData) {
                if (chatBean == null) continue;

                ChatMessageBean message = chatBean.message;
                if (message == null) continue;
                //消息里面securityId为空 继续循环
                if (LText.empty(message.securityId)) continue;

                //这块不操作数据库保存,在(ChatNewActivity2#onPause/ChatNewActivity#onPause)时候会保存
                if (LText.empty(contactData.securityId)) {
                    //消息的securityId赋值到联系人（消息里面的securityId缺少resumeId/positionId信息）
                    contactData.securityId = message.securityId;
                    TLog.info("securityIdTag", "update contact securityId from message");
                }
                break;
            }
        }
    }

    /**
     * 消息发生变化,刷新消息未读数量
     */
    private void refreshMessageNoneRead() {
        if (contactData == null) return;
        if (!ActivityUtils.isValid(activity)) {
            return;
        }
        ContactManager contactManager = ContactManager.getInstance();
        ContactBean tempContact = contactManager.queryContactByFriendId(contactData.friendId, contactData.myRole, friendSource);
        if (tempContact != null) {

            boolean needRefreshF2 = tempContact.noneReadCount > 0;

            tempContact.noneReadCount = 0;
            byte tempwitch1 = tempContact.switch1;
            tempContact.setSwitch1Open(false, ContactBean.SWITCH_NEW_REPLY);

            boolean needDb = needRefreshF2 || tempContact.switch1 != tempwitch1;
            if (needDb) {
                contactData = contactData.cloneBean(tempContact);
                contactManager.initContactData(contactData);
            }

            if (needRefreshF2) {//消息置为已读,刷新f2列表和f2底部气泡数量
                ContactManager.getInstance().refreshContacts();
            }
        }
    }


    private List<ChatBean> filterChatList(List<ChatBean> source) {
        messageIdSet.clear();
        List<ChatBean> filter = new ArrayList<>(source.size());
        for (ChatBean bean : source) {
            filter.add(bean);
            if (bean.msgId > 0) {
                messageIdSet.add(bean.msgId);
            }

            addWaitReceiveMsg(bean);
        }
        return filter;
    }


    boolean isFrist = true;


    private static final String TAG = "ChatCommon";


    private long findFirstSendMsgId() {
        synchronized (ADD_MESSAGE_LOCK) {
            for (ChatBean messageDatum : messageData) {
                if (messageDatum == null) continue;
                ChatUserBean fromUser = messageDatum.message.fromUser;
                if (fromUser.id == UserManager.getUID()) {
                    return messageDatum.msgId;
                }
            }
        }
        return 0;
    }


    // 刷新消息
    private void refreshMessageData() {


        iChatCommon.refreshShowData();

        if (refreshHelper != null) {
            refreshHelper.onRefreshComplete(hasMoreChatItem, hasAddHeadChatBean);
        }

        //联系人列表与聊天界面消息不同步打点上报（非业务 addby wangtian 2019.7.11）
        try {


            if (contactData == null) return;

            if (isFrist) {
                isFrist = false;

                //来源F2消息列表
                if (SingleChatViewHolder.isFromF2Contact) {
                    SingleChatViewHolder.isFromF2Contact = false;
                    //非加好友流程&&f2消息列表有联系人没有lastChatText&&没有消息
                    if (!isNeedAddFriend
                            && LText.empty(contactData.lastChatText)
                            && LList.isEmpty(messageData)) {


                        /*https://apm.weizhipin.com/main/action-get-info?addition=%255B%257B%2522router%2522%253A%2522home%2522%252C%2522name%2522%253A%2522%25E6%25A6%2582%25E8%25A7%2588%2522%257D%252C%257B%2522router%2522%253A%2522action%2522%252C%2522name%2522%253A%2522%25E7%25BB%259F%25E8%25AE%25A1%25E7%2582%25B9%2522%257D%252C%257B%2522router%2522%253A%2522action-get%2522%252C%2522name%2522%253A%2522action_chat%2522%252C%2522query%2522%253A%257B%2522appKey%2522%253A%2522n9mObpaxi2d2AFbV%2522%252C%2522action%2522%253A%2522action_chat%2522%252C%2522appName%2522%253A%2522Boss-Android%2522%252C%2522breadName%2522%253A%2522action_chat%2522%252C%2522occurFrequency%2522%253A%25229859491%2522%252C%2522dayOccurFrequency%2522%253A%2522415210%2522%252C%2522influenceCount%2522%253A%25223254015%2522%252C%2522dayInfluenceCount%2522%253A%2522280433%2522%257D%257D%252C%257B%2522name%2522%253A%2522%25E5%2588%2597%25E8%25A1%25A8%2522%252C%2522router%2522%253A%2522%2522%257D%255D&appKey=n9mObpaxi2d2AFbV&action=action_chat&appName=Boss-Android&sysCode=101&queryData=%7B%22curAppVersion%22%3A%22%22,%22childType%22%3A%5B%22contact_no_message1%22%5D,%22timeType%22%3A0,%22dateDuring%22%3A%5B1694707200000,1694758181101%5D,%22userId%22%3A%22%22,%22oldMode%22%3Afalse%7D*/
                        /*
                         *APM埋点异常统计：数据偶尔飙升 排出后由于2个原因：
                         * case1:主动开聊 加好友，创建抽屉，服务器消息堆积，没有收到消息，到聊天窗口会上报
                         * case2:调用getBaseInfoList同步到新联系人抽屉，服务器消息堆积，没有收到消息，到聊天窗口会上报
                         *
                         * **完善埋点信息，如果是当天新加的好友，没有消息，lastChatTime为空 可以确定是由于服务器消息堆积导致没有说到消息**
                         *
                         * */

                        String addFriendDate = "None";
                        /*具体加好友时间 */
                        if (contactData.addTime > 0) {
                            addFriendDate = DateUtil.isTodayWithTime(contactData.addTime) ?
                                    "Toady" : DateUtil.formatterDate(contactData.addTime, "yyyy-MM-dd");
                        }

                        /*接口不会更新，只有收到消息||发送消息才会更新*/
                        String lastChatTimeDate = "None";
                        if (contactData.lastChatTime > 0) {
                            lastChatTimeDate = DateUtil.formatterDate(contactData.lastChatTime, "yyyy-MM-dd");
                        }

                        ChatAnalyzer.
                                report(ChatAnalyzer.CONTACT_NO_MESSAGE)
                                .p2(String.valueOf(contactData.friendId))
                                .p3(addFriendDate)
                                .p4(lastChatTimeDate)
                                .report();
                    }
                }


                if (!TextUtils.isEmpty(contactData.lastChatText) && !contactData.needRequestFullInfo()) {
                    if (messageData.size() == 0) {//列表有数据，但是聊天界面没数据
                        ReportContextUtils.contactAndMesssageNosyc(contactData, null);
                        ApmAnalyzer.create()
                                .action(ApmAnalyticsAction.ACTION_MESSAGE_SYNC, ApmAnalyticsAction.TYPE_CHAT_EMPTY)
                                .report();
                    } else {
                        if (contactData.lastChatClientMessageId != -1
                                && contactData.lastChatClientMessageId != 0) {

                            //最后一个聊天消息
                            ChatBean lastChatBean = messageData.get(messageData.size() - 1);
                            //检测f2和列表最后一个消息状态不同步【送达】【已读】
                            if (lastChatBean.clientTempMessageId == contactData.lastChatClientMessageId
                                    && contactData.lastChatStatus != lastChatBean.status
                                    && lastChatBean.status != 4) {//撤回消息不算

                                ReportContextUtils.contactAndMesssageNosyc(contactData, lastChatBean);
                                ApmAnalyzer.create()
                                        .action(ApmAnalyticsAction.ACTION_MESSAGE_SYNC, ApmAnalyticsAction.TYPE_CONTACT_NO_SYNC)
                                        .param("p2", GsonUtils.getGson().toJson(contactData))
                                        .param("p3", GsonUtils.getGson().toJson(lastChatBean))
                                        .report();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {

        }
    }


    /**
     * 刷新列表&隐藏阻断对话框
     */
    public void refreshAdapterAndBlock(long fromId) {
        if (contactData == null) return;
        if (contactData.friendId == fromId) {
            iChatCommon.refreshShowData();
        }
    }


    // 刷新联系人
    public void refreshContactInfo() {
        if (contactData != null) {
            int f2NoneReadCount = F2ContactHelper.getInstance().getOtherContactNoneReadMsgCount(getFriendId(), getFriendSource());
            iChatCommon.initBackButton(f2NoneReadCount);
            iChatCommon.initTitleValue(contactData);
            iChatCommon.refreshHeadTips();
        }
    }

    public void RefreshBlackViewStatus() {
        iChatCommon.refreshHeadTips();
    }


    /**
     * 点击滑梯事件
     */
    @Override
    public void onSlideClickListener(long minId, int mNoneReadCount) {

        if (contactData == null) return;

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_GROUP_CHAT_UNREAD_MESSAGES).param("p", String.valueOf(contactData.friendId)).param("p2", String.valueOf(mNoneReadCount)).buildSync();


        if (!checkExecutorCanWork()) return;
        //隐藏键盘
        AppUtil.hideSoftInput(activity);

        submitTask(() -> {
            //刷新未读数量
            refreshMessageNoneRead();
            //获得当前页面最新的数据
            MessageDao messageDao = MessageDaoFactory.getMessageDao();
            long allCount;

            if (contactData != null) {
                synchronized (ADD_MESSAGE_LOCK) {
                    //找到滑梯未读
                    ChatBean tempBean;
                    while ((tempBean = LList.getElement(messageData, 0)) != null && tempBean.id > minId) {
                        List<ChatBean> filterList = filterChatList(messageDao.getChatMoreList(contactData.myId, contactData.myRole, messageData, contactData.friendId, friendSource));
                        if (LList.getCount(filterList) == LList.getCount(messageData)) break;
                        messageData.clear();
                        messageData.addAll(filterList);
                    }
                    allCount = messageDao.getSingleChatCount(contactData.myId, contactData.myRole, contactData.friendId, friendSource);
                    hasMoreChatItem = allCount > LList.getCount(messageData);
                }
            }


            App.get().getMainHandler().postDelayed(() -> {

                refreshMessageData();

                //sendScrollToPosition(getSlideIndex(minId));

                refreshHelper.smoothToTargetPositionByMid(minId, messageData);

            }, 200);

        });
    }


    /**
     * 滑动到底部
     *
     * @param delay
     * @param hasAnimation
     */
    public void scrollToBottom(boolean delay, boolean hasAnimation) {

        if (refreshHelper != null) {
            refreshHelper.smoothToBottom(hasAnimation, delay);
        }

    }

    public void scrollToBottom(boolean delay) {

        if (refreshHelper != null) {
            refreshHelper.smoothToBottom(false, delay);
        }

    }


    /**
     * 发送消息给ListView，滚动到指定位置的item，不带动画
     */
    private void loadMoreAndSmoothToPosition(long id) {
        if (refreshHelper != null) {
            refreshHelper.smoothToTargetPositionByMid(id, messageData);
        }
    }

    /**
     * 创建一条已读消息并发送
     */
    public void createChatReaderMessageAndSend() {
        if (!checkExecutorCanWork()) return;
        submitTask(() -> {
            if (checkEncryptMessage()) return;

            if (contactData != null) {
                // 创建一条已读消息并发送
                L.i(tag, "创建一条已读消息并发送");

                ChatReaderBean bean = ChatReaderBeanManager.
                        getInstance().create(contactData.friendId);
                if (bean == null) return;
                if (bean.messageId <= currentMaxReadMessageId) return;
                MSGSender.sendMessageReaderMessage(bean, friendSource);
                currentMaxReadMessageId = bean.messageId;
            }
        });
    }


    /**
     * @param intent
     */
    public void onNewIntent(Intent intent) {
        if (UserManager.getUserRole() == ROLE.GEEK &&
                !TextUtils.isEmpty(changePositionDesc)) {
            dealChangePosition();
        }
        //解决询问它场景
        hasSendNiceText = false;
        checkSendNiceText();
        setContentInEditText();
    }


    private void setContentInEditText() {
        // 创建好友之后，将传入的文案放入输入框
        if (!TextUtils.isEmpty(chatContentInEditText)) {
            iChatCommon.setContentInEditText(chatContentInEditText);
        }
    }


    /**
     * 页面重新回到可见状态的调用，在onResume时调用
     */
    public void resume() {
        isResume = true;
        if (!isOnResumeFirstStart) {
            if (hasReceiveMessageWhenAppPaused) {
                refreshMessageNoneRead();
                createChatReaderMessageAndSend();
                hasReceiveMessageWhenAppPaused = false;
            }
            refreshFriendStatus();
            //刷新顶部好友状态
            if (iChatCommon != null) {
                iChatCommon.initTopCommon(true);
            }
        }
        isOnResumeFirstStart = false;

    }


    public boolean showOverdueBossDialog() {
        if (overdueBossInfo == null || overdueBossInfo.currentBoss == 0) return false;
        if (contactData.isContactEachOther()) return false;
        if (mBroadcast.friendCleaned) return false; //点击灰条
        // 第一种情况：未点击灰条
        if (OverdueBossInfoDialog.canShowDialog(1)) {
            OverdueBossInfoDialog dialog = new OverdueBossInfoDialog(activity)
                    .setDialogScene(1)
                    .setFriendId(getFriendId())
                    .setJobId(getJobId())
                    .setSubtitle("该会话存续时间超过50天，且您长期未回复，可 “点击不再接收该boss信息”，点击后Ta将无法给你发送消息，减少无效打扰。")
                    .setConfirmText("不再接收该BOSS信息")
                    .setCancelText("取消")
                    .setOnDialogActionListener(new OverdueBossInfoDialog.OnDialogActionListener() {
                        @Override
                        public void onConfirm() {
                            int friendSource = getFriendSource();
                            if (friendSource == ContactBean.FROM_BOSS) {
                                friendClean(Collections.singletonList(getFriendId()), null, 1);
                            } else if (friendSource == ContactBean.FROM_DIAN_ZHANG) {
                                friendClean(null, Collections.singletonList(getFriendId()), 1);
                            }
                        }

                        @Override
                        public void onCancel(int type) {
                            AnalyticsFactory.create().action("intercept-unbind-popup-click").param("p", getFriendId()).param("p2", getJobId()).param("p3", type).build();
                            AppUtil.finishActivity(activity);
                        }
                    });

            // 带频率限制的显示
            return dialog.showWithLimit();
        }
        return false;
    }

    // 第二种情况：存在多个超时未回复的Boss
    public boolean showOverdueBossDialogBatch() {
        if (overdueBossInfo == null) return false;
        if (OverdueBossInfoDialog.canShowDialog(2)) {
            if (LList.isNotEmpty(overdueBossInfo.zpBossIdList) || LList.isNotEmpty(overdueBossInfo.dzBossIdList)) {
                OverdueBossInfoDialog dialog = new OverdueBossInfoDialog(activity)
                        .setDialogScene(2)
                        .setTitle("温馨提示")
                        .setSubtitle(String.format(Locale.CHINA, "还有 %d 个 聊天消息“存续时间超过50天，且您长期未回复”，是否一起清理？", LList.getCount(overdueBossInfo.zpBossIdList) + LList.getCount(overdueBossInfo.dzBossIdList)))
                        .setConfirmText("一键清理")
                        .setCancelText("取消")
                        .setOnDialogActionListener(new OverdueBossInfoDialog.OnDialogActionListener() {
                            @Override
                            public void onConfirm() {
                                friendClean(overdueBossInfo.zpBossIdList, overdueBossInfo.dzBossIdList, 2);
                            }

                            @Override
                            public void onCancel(int type) {
                                AnalyticsFactory.create().action("relation_batchunbind_popup_click").param("p", type).build();
                                AppUtil.finishActivity(activity);
                            }

                        });

                // 带频率限制的显示
                return dialog.showWithLimit();
            }
        }
        return false;
    }

    private static final StringUtil.IStringFunction<Long> iValueFunction = friendId -> {
        if (friendId > 0) {
            return String.valueOf(friendId);
        }
        return "";
    };

    private void friendClean(List<Long> zpFriendIds, List<Long> dzFriendIds, int scene) {
        String zpFriends = StringUtil.connectTextWithCharNew(",", zpFriendIds, iValueFunction);
        String dzFriends = StringUtil.connectTextWithCharNew(",", dzFriendIds, iValueFunction);
        mBroadcast.friendCleaned = true;
        SimpleApiRequest.POST(ChatUrlConfig.URL_ZPRELATION_FRIEND_CLEAN)
                .addParam("zpFriendIds", zpFriends)
                .addParam("dzFriendIds", dzFriends)
                .addParam("scene", scene) //场景区分，1.点击灰条清理单个联系人2.通过批量清理联系人
                .setRequestCallback(new SimpleCommonApiRequestCallback<SuccessResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<SuccessResponse> data) {
                        if (LList.isNotEmpty(zpFriendIds)) {
                            for (Long zpFriendId : zpFriendIds) {
                                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(zpFriendId, AccountHelper.getIdentity(), ContactBean.FROM_BOSS);
                                if (contactBean != null) {
                                    ContactManager.getInstance().deleteContact(contactBean);
                                    MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), zpFriendId, ContactBean.FROM_BOSS);
                                }
                            }
                        }
                        if (LList.isNotEmpty(dzFriendIds)) {
                            for (Long dzFriendId : dzFriendIds) {
                                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(dzFriendId, AccountHelper.getIdentity(), ContactBean.FROM_DIAN_ZHANG);
                                if (contactBean != null) {
                                    ContactManager.getInstance().deleteContact(contactBean);
                                    MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), dzFriendId, ContactBean.FROM_DIAN_ZHANG);
                                }
                            }
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<SuccessResponse> data) {
                        super.onSuccess(data);
                        ToastUtils.showText("已清理，即将退出对话");
                        // 1307.80【C】缓解部分招聘者超期会话资源占用问题 scene = 2 批量清理
                        //场景2：用户未点击点击灰条，但是在返回时二次拦截了接触提示，用户点击不再接收该BOSS信息之后，我们同步调起这个弹窗
                        if (scene == 2 || !showOverdueBossDialogBatch()) {
                            AppUtil.finishActivity(activity);
                        }
                    }

                }).execute();

        String friendStr = StringUtil.connectTextWithChar(",", Arrays.asList(zpFriends, dzFriends));
        if (scene == 1) {
            AnalyticsFactory.create().action("intercept-unbind-popup-click").param("p", friendStr).param("p2", getJobId()).param("p3", "1").build();
        } else if (scene == 2) {
            AnalyticsFactory.create().action("relation_batchunbind_popup_click").param("p", "1").param("p2", friendStr).build();
        }
    }
    /**
     * 用户按下返回键的操作，在onKeyDown事件中调用，如果返回值为true，直接返回
     *
     * @param keyCode
     * @param event
     * @return
     */
    public boolean keyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            //1307.80【C】缓解部分招聘者超期会话资源占用问题
            if (showOverdueBossDialog()) {
                return true;
            }
            if (!TextUtils.isEmpty(backProtocol)) {
                ZPManager manager = new ZPManager(activity, backProtocol);
                if (manager.isZPUrl() || manager.isWebUrl() || manager.isSystemUrl()) {
                    manager.handler();
                    return true;
                }
            }
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            if (mSoundPlayer != null && mSoundPlayer.isPlaying()) {
                mSoundPlayer.addVolume();
                return true;
            }
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
            if (mSoundPlayer != null && mSoundPlayer.isPlaying()) {
                mSoundPlayer.lowerVolume();
                return true;
            }
        }
        return false;
    }


    //技术开聊阻断
    private String encryptBlockBagId;

    public void setEncryptBlockBagId(String encryptBlockBagId) {
        this.encryptBlockBagId = encryptBlockBagId;
    }

    /**
     * Activity返回结果，在onActivityResult中调用
     *
     * @param activity
     * @param requestCode
     * @param resultCode
     * @param data
     */
    public void activityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (resultCode != Activity.RESULT_OK) return;
        if (requestCode == BusinessPageRouter.IReqCode.REQ_CHAT_LIMITATION) {//购买权益成功之后,在掉一次接口,服务推送秘钥
            if (contactData != null) {
                UnlockChatRequest request = new UnlockChatRequest(new ApiRequestCallback<UnlockChatResponse>() {
                    @Override
                    public void onSuccess(ApiData<UnlockChatResponse> data) {
                        if (!data.resp.block) {
                            T.ss(data.resp.vipChatToast);
                        }
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        T.ss(reason.getErrReason());
                    }
                });
                request.securityId = contactData.securityId;
                HttpExecutor.execute(request);
            }
            Object result = data.getSerializableExtra(Constants.DATA_ENTITY);
            if (result instanceof PayResult) {
                PayResult payResult = (PayResult) result;
                if (TextUtils.isEmpty(payResult.title) || TextUtils.isEmpty(payResult.desc)) {
                    return;
                }
                DialogUtils d = new DialogUtils.Builder(activity)
                        .setTitle(payResult.title)
                        .setDesc(payResult.desc)
                        .setSingleButton()
                        .setPositiveAction(R.string.string_confirm)
                        .build();
                d.show();
            }
        } else if (requestCode == BusinessConst.IReqCode.REQ_PAY_CONFIRM) {//技术开聊支付成功
            if (contactData == null) return;
            //网络请求,服务端推送消息同步
            BlockUserCardRequest request = new BlockUserCardRequest(new ApiRequestCallback<SuccessResponse>() {
                @Override
                public void onSuccess(ApiData<SuccessResponse> data) {
                    String toast = data.resp.toast;
                    if (!LText.empty(toast)) {
                        T.ss(toast);
                    }
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    T.ss(reason.getErrReason());
                }
            });
            request.securityId = contactData.securityId;
            request.encryptBlockBagId = encryptBlockBagId == null ? " " : encryptBlockBagId;
            HttpExecutor.execute(request);


            PayResult result = (PayResult) data.getSerializableExtra(Constants.DATA_ENTITY);
            if (result != null) {
                blockDialog = new DialogUtils.Builder(activity)
                        .setSingleButton()
                        .setCancelable(false)
                        .setTitle(result.title)
                        .setDesc(result.desc)
                        .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                boolean free = contactData != null && !contactData.isTechGeekBlock;
                                // 弹出键盘  取消阻断
                                if (free) {
                                    //显示键盘
                                    iChatCommon.showKeyBoard(contactData.friendId);
                                    //取消阻断
                                    refreshAdapterAndBlock(contactData.friendId);
                                }

                                blockDialog = null;
                            }
                        })
                        .build();
                blockDialog.show();
            }
        } else if (requestCode == BusinessPageRouter.IReqCode.REQ_SMS_ITEM_PURCHASE) {
            int sourceType = data.getIntExtra(BusinessConst.SOURCE_TYPE, 0);
            smsItemPreUse(sourceType);
        } else if (requestCode == BusinessPageRouter.IReqCode.REQ_SMS_ITEM_PURCHASE_GRAY) {
            int sourceType = data.getIntExtra(BusinessConst.SOURCE_TYPE, 0);
            String lastSmsContent = data.getStringExtra(BusinessConst.LAST_SMS_CONTENT);
            smsItemSendMessage(lastSmsContent, sourceType);
        } else if (requestCode == ChatNewActivity.REQ_SEND_LOCATION) {//发送地理位置
            if (contactData == null) return;
            ContactKeyManager.getInstance().onSendLocationListener(contactData, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    ServerTranslatedPoiAddressBean mPoiAddress = (ServerTranslatedPoiAddressBean) data.getSerializableExtra(PoiLocationTranslator.POI_TRANSLATE_ADDRESS);
                    if (mPoiAddress != null) {
                        //多职位case locationText=PoiTitle=poiTitle+门牌号
                        MSGSender sender = new MSGSender();
                        sender.sendLocationAction(contactData, mPoiAddress);
                    }
                }
            });
        } else if (requestCode == ChatBaseActivity.REQ_SELECT_LOCATION) {
            ServerTranslatedPoiAddressBean poiAddress = (ServerTranslatedPoiAddressBean) data.getSerializableExtra(PoiLocationTranslator.POI_TRANSLATE_ADDRESS);
            if (poiAddress == null) {
                TLog.info(TAG, "poiAddress is null, update verbal interview dialog failed.");
                return;
            }

            if (mVerbalInterviewBottomDialog != null) {
                mVerbalInterviewBottomDialog.updateAddress(poiAddress);
            }
        } else if (requestCode == InterviewsConstant.INTERVIEW_REQUEST_CODE_ADD_CONTACT) {
            if (mVerbalInterviewBottomDialog != null && data != null) {
                String contactPhone = data.getStringExtra(Constants.DATA_STRING);
                String contactName = data.getStringExtra(Constants.DATA_STRING2);
                String contactId = data.getStringExtra(Constants.DATA_CONTACT_ID);

                // 添加联系人场景没有contactId，需要请求接口用手机号去查
                if (TextUtils.isEmpty(contactId)) {
                    InterviewsRouter.getNewAddedBossContact(contactPhone, bossContactBean -> {
                        mVerbalInterviewBottomDialog.updateContactView(bossContactBean);
                    });
                } else {
                    SelectContactParamBean.BossContactBean bossContactBean = new SelectContactParamBean.BossContactBean(contactId, contactName, contactPhone);
                    mVerbalInterviewBottomDialog.updateContactView(bossContactBean);
                }

            }
        } else if (requestCode == BusinessPageRouter.IReqCode.REQ_VIRTUAL_CALL_PURCHASE) { // 通过协议购买虚拟电话
            if (data != null) {
                String securityId = data.getStringExtra(BusinessConst.SECURITY_ID);
                VirtualCallUtils virtualCallUtils = new VirtualCallUtils((BaseActivity) activity);
                virtualCallUtils.setSecurityId(securityId, ItemPaySource.VIRTUAL_CHAT_CALL);
                virtualCallUtils.preUse();
            }
        } else if (requestCode == BusinessPageRouter.IReqCode.BLOCK_REQUEST) { //阻断购买成功（BLOCK_REQUEST进入）
            Object result = data.getSerializableExtra(Constants.DATA_ENTITY);
            if (result instanceof PayResult && null != activity) {
                PayResult payResult = (PayResult) result;
                if (!TextUtils.isEmpty(payResult.title) && !TextUtils.isEmpty(payResult.desc)) {

                    /*兜底按钮为空的场景为*/
                    if (LList.isEmpty(payResult.actionList)) {
                        payResult.actionList = new ArrayList<>();
                        ServerButtonBean defBtn = new ServerButtonBean();
                        defBtn.text = activity.getString(R.string.string_see);
                        payResult.actionList.add(defBtn);
                    }

                    ServerDialogBean dialogBean = new ServerDialogBean();
                    dialogBean.title = payResult.title;
                    dialogBean.content = payResult.desc;
                    dialogBean.buttonList = payResult.actionList;
                    dialogBean.targetId = payResult.targetId;

                    DialogCommonHandler handler = new DialogCommonHandler();
                    handler.handle(activity, dialogBean, new DialogCommonHandler.OnActionTypeHandleListener() {

                        @Override
                        public void onActionType21() {

                        }

                        @Override
                        public void onActionType22(String url) {
                            if (ActivityUtils.isValid(activity)) { //购买成功，准备激活
                                new ZPManager(activity, url).handler();
                            }
                        }
                    });
                }
            }
        }
    }

    /**
     * 短信通知预使用
     */
    public void smsItemPreUse() {
        smsItemPreUse(SmsItemUtils.TYPE_PLUS);
    }

    private void smsItemPreUse(int sourceType) {
        String securityId = "";
        if (contactData != null) {
            securityId = contactData.securityId;
        }
        SmsItemUtils utils = new SmsItemUtils(activity);
        utils.preUse(securityId, sourceType);
    }

    private void smsItemSendMessage(String lastSmsContent, int sourceType) {
        String securityId = "";
        if (contactData != null) {
            securityId = contactData.securityId;
        }
        SmsItemUtils utils = new SmsItemUtils(activity);
        utils.commitCustomMessage((BaseActivity) activity, securityId, lastSmsContent, sourceType);
    }

    /**
     * 接收解锁技术牛人阻断
     *
     * @param friendId
     */
    @Override
    public void onReceiverCancelChatBlockListener(long friendId) {

        //没有对话框,直接弹出键盘
        if (blockDialog == null) {
            //显示键盘
            iChatCommon.showKeyBoard(friendId);
            //取消阻断
            refreshAdapterAndBlock(friendId);
        }

    }

    /**
     * 检测技术牛人是否阻断
     *
     * @param friendId
     */
    @Override
    public void onReceiverCheckChatBlockListener(long friendId) {
        if (contactData != null && contactData.friendId == friendId) {
            //检测是否需要阻断
            iChatCommon.checkShowChatBlock();
        }
    }

    @Override
    public void onRevocationMessageListener() {
        //PC端撤回消息
        refreshCurrentPage();
    }

    @Override
    public void OnPhoneContactCallBack(long friendId, int friendSource) {
        //回掉业务层刷新卡片
        iChatCommon.OnPhoneContactCallBack(friendId, friendSource);
    }


    /**
     * 页面暂停，无法再查看此页面，在onPause方法中调用
     */
    public void pause() {
        isResume = false;
        stopAudio();
    }

    private void stopAudio() {
        // 停止当前正在播放的语音
        if (mCurrentPlayerBean != null) {
            mCurrentPlayerBean.playing = false;
            iChatCommon.refreshShowData();
            mCurrentPlayerBean = null;
        }
        // 停止声音控件
        if (mSoundPlayer != null) {
            mSoundPlayer.stop();
            mSoundPlayer = null;
        }
    }

    /**
     * 页面销毁，释放资源，在onDestroy中调用
     */
    public void destroy() {
        if (contactData != null) {
            MessageDoctorFactory.checkMessageValidFriend(contactData.friendId, getFriendSource());
        }
        AudioCovertTextUtils.getInstance().onDestroy();
        // 解除注册所有广播接收
        destroyReceiver();
        if (!checkExecutorCanWork()) return;
        getTaskManager().shutdown();
        ExchangeHelper.getInstance().clear();
        GrayStageManager.getInstance().setUnLightExchangePhoneWx(false);
    }

    /**
     * 销毁广播接收器
     */
    private void destroyReceiver() {
        // 注销新消息接收器
        ChatMessageFactory.getInstance().createChatTransfer().unregister(this);
        TLog.info(TAG, "destroyReceiver un register receiver %s", this);
        try {
            mBroadcast.unRegister();
        } catch (Exception e) {
            MException.printError(e);
        }
        ReceiverUtils.unregister(activity, receiver);
    }

    // ---------- 回调函数实现 ----------

    /**
     * 下拉刷新回调方法
     */
    @Override
    public void onRefresh() {
        refreshLoadMorePage(false);
    }

   public Map<String, Long> mReceiveAudioMsgMap = new HashMap<>();

    @Override
    public boolean onNewChatMessage(ChatBean chatBean) {
        //单聊
        if (chatBean.domain == 1) {
            boolean isCurrentPage = RSingleMessageHandler.getMessFriendSource(chatBean) == friendSource
                    && (chatBean.fromUserId == getFriendId() || chatBean.toUserId == getFriendId())
                    && ActivityUtils.isValid(activity);

            //用户反馈,用户在后台,已读了消息，
            if (!AppStatus.isForeground()) {
                TLog.info("chat", "user read message background msgId=: %s , isCurrentPage %s ,this object  %s", chatBean.msgId, String.valueOf(isCurrentPage), this);
            }

            if (isCurrentPage) {
                //接收到消息判断是否 安心保 自动跳转
                checkAutoJumpAnXinBaoH5Url(chatBean);

                // 接收到一条新的聊天消息，暂未实现
                if (contactData != null) {
                    //曝光消息
                    exposeAnaly.checkAndExposeAnaly(chatBean, friendSource);
                    //对方发来个引用消息
                    boolean quoteMsgNotInMemory = queryAndSetQuoteMsg(chatBean);

                    addData(chatBean);

                    iChatCommon.refreshShowData();
                    iChatCommon.onNewChatCallback(chatBean);

                    //刷新好友关系状态
                    refreshFriendStatus();

                    //只有页面处于resume状态才发已读
                    if(isResume) {
                        //刷新未读数量
                        refreshMessageNoneRead();
                        createChatReaderMessageAndSend();
                    } else {
                        hasReceiveMessageWhenAppPaused = true;
                    }
                    //scrollManager.onReceiverNewMessage(iChatCommon.getListView().getRefreshableView(), LList.getCount(getMessageData()));
                    refreshHelper.onReceiveNewMsgCheckScrollBottom();

                    if (chatBean.message != null && chatBean.message.messageBody != null && chatBean.message.messageBody.type == 2) {
                        mReceiveAudioMsgMap.put(chatBean.message.messageBody.sound.url, chatBean.msgId);
                        String friendKey = AudioCovertTextUtils.getInstance().getFriendKey(getFriendId(), getFriendSource(), chatBean.fromUserId);
                        AudioCovertTextUtils.getInstance().mReceiveAudioMsgMap.put(SoundFile.getInstance().getFileName(chatBean.message.messageBody.sound.url), new Pair<>(friendKey, chatBean));
                        AudioCovertTextUtils.getInstance().addToShowAudioMsgMap(friendKey, chatBean.msgId);
                    }

                    checkIsBother();
                    if (quoteMsgNotInMemory) {
                        refreshCurrentPage();
                    }
                    return false;
                }
            }

        }
        return true;
    }

    //接收到消息判断是否 安心保 自动跳转
    private void checkAutoJumpAnXinBaoH5Url(@NonNull ChatBean chatBean) {
        if (UserManager.isGeekRole()
                && chatBean.message != null
                && chatBean.message.messageBody != null
                && chatBean.message.messageBody.type == 7
                && chatBean.message.messageBody.dialog != null
                && chatBean.message.messageBody.dialog.type == 27
        ) {
            ChatDialogBean dialog = chatBean.message.messageBody.dialog;
            String extend = dialog.extend;
            if (extend != null) {
                try {
                    JSONObject jsonObject = new JSONObject(extend);
                    int axbAutoJump = jsonObject.optInt("axbAutoJump");
                    // 1 需要跳转, 0 无需跳转
                    if (axbAutoJump == 1) {
                        ChatDialogButtonBean buttonBean = LList.getElement(dialog.buttons, 0);
                        if (buttonBean != null) {
                            String autoUrl = buttonBean.url;
                            new ZPManager(activity, autoUrl).handler();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private boolean queryAndSetQuoteMsg(ChatBean chatBean) {
        if (chatBean == null) return false;
        if (chatBean.message.quoteId > 0 && chatBean.message.quoteChatBean == null) {
            ChatBean queryQuoteChatBean = queryQuoteChatBean(chatBean.message.quoteId);
            chatBean.message.quoteChatBean = queryQuoteChatBean;
            return queryQuoteChatBean == null;
        }
        return false;
    }

    //控制 是否滑动到底部
    @Override
    public void onScrollListener() {
        scrollToBottom(false);
    }

    @Override
    public void onUpdateMsgId(long localMesasgeId, long mid) {

    }


    public void showChatPageDialog(final ChatDialogBean dialogBean) {
        int width = App.get().getDisplayWidth() - Scale.dip2px(activity, 40);
        LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.view_chat_page_dialog, null);
        ImageView ivCancel = view.findViewById(R.id.tv_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvText = view.findViewById(R.id.tv_desc);
        MTextView tvButton = view.findViewById(R.id.tv_button);
        tvTitle.setText(dialogBean.title);
        tvText.setText(dialogBean.text);
        final Dialog dialog = new Dialog(activity, R.style.AdvertDialogStyle);
        dialog.addContentView(view,
                new ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
        ivCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        final ChatDialogButtonBean buttonBean = LList.getElement(dialogBean.buttons, 0);
        if (buttonBean != null) {
            tvButton.setVisibility(View.VISIBLE);
            tvButton.setText(buttonBean.text);
            tvButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    new ZPManager(activity, buttonBean.url).handler();
                    dialog.dismiss();
                }
            });
        } else {
            tvButton.setVisibility(View.GONE);
        }
        if (!activity.isFinishing()) {
            dialog.show();
        }
        Window window = dialog.getWindow();
        if (window != null && window.getAttributes() != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = width;
            window.setAttributes(params);
        }
    }


    private void onVideoInterviewClickListener(ChatBean bean, long interviewId, int status, long interviewTimeMillSecond) {
        InterviewStatusRequest request = new InterviewStatusRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                //同意面试
                if (status == InterviewStatusRequest.ACCEPT_APPOINTMENT) {
                    //𓀃添加👀事件到手机系统日历
                    ChatUtils.addCalendarVideoInterviewEvent(activity, interviewTimeMillSecond, interviewId);
                }

                //置灰
                bean.message.messageBody.dialog.clickMore = false;
                bean.message.messageBody.dialog.operated = true;
                bean.message.messageBody.dialog.clickTime = System.currentTimeMillis();
                //更新数据库
                AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().updateChat(bean));
            }

            @Override
            public void onComplete() {
                //刷新界面
                iChatCommon.refreshShowData();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(activity, reason.getErrReason());
            }
        });

        request.interviewId = interviewId;
        request.status = status;
        HttpExecutor.execute(request);
    }

    @Override
    public void onDialogViewButtonClickCallback(final ChatBean bean, final int index) {
        if (!MessageUtils.isDialogMessage(bean)) return;
        ChatDialogBean dialogBean = bean.message.messageBody.dialog;
        if (dialogBean == null) return;


        //牛人点击视频面试
        if (UserManager.getUserRole() == ROLE.GEEK
                && dialogBean.type == 13) {

            //超时不可点击
            if (System.currentTimeMillis() > dialogBean.timeout) {
                ///刷新界面&置灰
                bean.message.messageBody.dialog.clickTime = System.currentTimeMillis();
                bean.message.messageBody.dialog.clickMore = false;
                bean.message.messageBody.dialog.operated = true;
                iChatCommon.refreshShowData();
                //更新数据库
                AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().updateChat(bean));
                ToastUtils.showText(activity, "该预约已超时");
                return;
            }

            ChatDialogButtonBean button = bean.message.messageBody.dialog.buttons.get(index - 1);
            if (!LText.empty(button.url)) {
                try {
                    JSONObject jsonObject = new JSONObject(button.url);
                    long interviewId = jsonObject.optLong("interviewId");
                    long interviewTime = jsonObject.optLong("interviewTime");
                    //同意
                    if (index == 1) {
                        onVideoInterviewClickListener(bean, interviewId, InterviewStatusRequest.ACCEPT_APPOINTMENT, interviewTime);
                    } else {//拒绝
                        onVideoInterviewClickListener(bean, interviewId, InterviewStatusRequest.REJECT_APPOINTMENT, interviewTime);
                    }

                    bean.message.messageBody.dialog.clickMore = false;

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

        //点击同意交换电话号码
        if (dialogBean.type == 11) {
            if (index == 1) {
                if (contactData != null) {
                    iChatCommon.checkShowNewAvAudioMore(activity, SwitchCommon.isShowVideoIcon(), contactData.friendId);
                }
            }
        }

        //==========================================================
        //===========================交换电话========================
        //==========================================================

        //交换电话号码
        if (dialogBean.type == 11) {
            int startChatProcessExpGroup = ChatUtils.getStartChatProcessExpGroup(dialogBean.extend);
            int requestScene = ChatUtils.getRequestScene(dialogBean.extend);
            //同意
            if (index == 1) {
                //校验是否已经绑定了手机号
                if (contactData != null) {
                    final String securityId = contactData.securityId;
                    int scene =  ExchangeHelper.getInstance().needConfirm(bean) ? 1 : 0;
                    ExchangeTestManager.exchangeAcceptTest(activity, securityId, 1, bean.msgId, scene, new Callback<ExchangeTestAcceptResponse>() {
                        @Override
                        public void call(ExchangeTestAcceptResponse exchangeTestAcceptResponse) {
                            ExchangeAcceptRequest acceptRequest = new ExchangeAcceptRequest(new ApiRequestCallback<ExchangeChatResponse>() {

                                @Override
                                public void onStart() {
                                    super.onStart();
                                    if (iChatCommon != null) {
                                        iChatCommon.showProgressDialog("");
                                    }
                                }

                                @Override
                                public void handleInChildThread(ApiData<ExchangeChatResponse> data) {
                                    super.handleInChildThread(data);
                                    ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                                    dialogBean.operated = true;
                                    MessageDaoFactory.getMessageDao().update(bean);
                                }

                                @Override
                                public void onSuccess(ApiData<ExchangeChatResponse> data) {
                                    refreshCurrentPage();
                                }

                                @Override
                                public void onComplete() {
                                    if (iChatCommon != null) {
                                        iChatCommon.dismissProgressDialog();
                                    }
                                }

                                @Override
                                public void onFailed(ErrorReason reason) {
                                    ToastUtils.showText(reason.getErrReason());
                                }
                            });
                            acceptRequest.securityId = securityId;
                            acceptRequest.type = "1";
                            acceptRequest.mid = String.valueOf(bean.msgId);
                            acceptRequest.startChatProcessExpGroup = startChatProcessExpGroup;
                            acceptRequest.scene = requestScene > 0 ? requestScene : ExchangeHelper.getInstance().getSource();
                            HttpExecutor.execute(acceptRequest);
                        }
                    });
                }

            }
            //拒绝
            if (index == 2) {
                if (contactData != null) {
                    ExchangeRejectRequest rejectRequest = new ExchangeRejectRequest(new ApiRequestCallback<SuccessResponse>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            if (iChatCommon != null) {
                                iChatCommon.showProgressDialog("");
                            }
                        }

                        @Override
                        public void handleInChildThread(ApiData<SuccessResponse> data) {
                            super.handleInChildThread(data);
                            ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                            dialogBean.operated = true;
                            MessageDaoFactory.getMessageDao().update(bean);
                        }

                        @Override
                        public void onSuccess(ApiData<SuccessResponse> data) {
                            refreshCurrentPage();
                        }

                        @Override
                        public void onComplete() {
                            if (iChatCommon != null) {
                                iChatCommon.dismissProgressDialog();
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    rejectRequest.securityId = contactData.securityId;
                    rejectRequest.type = "1";
                    rejectRequest.mid = String.valueOf(bean.msgId);
                    rejectRequest.startChatProcessExpGroup = startChatProcessExpGroup;
                    rejectRequest.scene = requestScene;
                    HttpExecutor.execute(rejectRequest);
                }

            }
            return;
        }

        //==========================================================
        //===========================交换电话========================
        //==========================================================

        //协议去解析
        if (dialogBean.type == 14) {
            // 初始化被点击按钮的样式
            ChatDialogButtonBean button = bean.message.messageBody.dialog.buttons.get(index - 1);
            ZPManager zpManager = new ZPManager(activity, button.url);
            if (!zpManager.isTypeActionSend()) {
                zpManager.handler();
                return;
            }
        }

        //Boss和Boss用于 确定简历-采用http替换action
        if (UserManager.getUserRole() == ROLE.GEEK
                && dialogBean.type == 2
                && friendSource == ContactBean.FROM_BOSS) {

            if (index == 1) {//同意
                if (contactData != null) {
                    if (contactData.isHkApplyJob()) {
                        HKPostJobDialogUtils hkPostJobDialogUtils=new HKPostJobDialogUtils(activity);
                        hkPostJobDialogUtils.getChatHKPostJobDialog(() -> geekAgreeExchangeResume(bean),contactData.securityId,2);
                    }else {
                        geekAgreeExchangeResume(bean);
                    }
                }
            } else {//拒绝
                if (contactData != null) {
                    ExchangeRejectRequest rejectRequest = new ExchangeRejectRequest(new ApiRequestCallback<SuccessResponse>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            if (iChatCommon != null) {
                                iChatCommon.showProgressDialog("");
                            }
                        }

                        @Override
                        public void onSuccess(ApiData<SuccessResponse> data) {
                            if (!ActivityUtils.isValid(activity)) return;
                            //dialog按钮置灰&&保存到数据库
                            AppThreadFactory.POOL.execute(new Runnable() {
                                @Override
                                public void run() {
                                    ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                                    dialogBean.operated = true;
                                    MessageDaoFactory.getMessageDao().update(bean);
                                    refreshCurrentPage();
                                }
                            });
                        }

                        @Override
                        public void onComplete() {
                            if (iChatCommon != null) {
                                iChatCommon.dismissProgressDialog();
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    rejectRequest.securityId = contactData.securityId;
                    rejectRequest.type = "4";
                    rejectRequest.mid = String.valueOf(bean.msgId);
                    HttpExecutor.execute(rejectRequest);
                }

            }
            return;
        }


        //点击牛人简历对话框  应该没有用了， 店长BOSS不支持求简历
        if (UserManager.getUserRole() == ROLE.GEEK
                && dialogBean.type == 2
                && index == 1 && friendSource == ContactBean.FROM_DIAN_ZHANG) {
            //店长点击同意的按钮
            ResumeHandleManager.onDianZhangResumeAgreeClickListener(activity, (resumeId, isEmptyOfResumeList) -> {
                // 初始化被点击按钮的样式
                ChatDialogButtonBean button = bean.message.messageBody.dialog.buttons.get(index - 1);
                String url = button.url;
                ZPManager manager = new ZPManager(activity, url);
                if (!LText.empty(url) && manager.isTypeActionSend()) {
                    if (resumeId > 0) {
                        if (url.contains(anExtend)) {
                            String[] aplits = url.split(anExtend);
                            if (aplits.length == 2) {
                                String extand = aplits[1];
                                try {
                                    JSONObject jsonObject = new JSONObject(extand);
                                    jsonObject.put("resumeId", resumeId);
                                    aplits[1] = jsonObject.toString();
                                    button.url = aplits[0] + anExtend + aplits[1];
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            } else {
                                try {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("resumeId", resumeId);
                                    button.url = aplits[0] + anExtend + jsonObject.toString();
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    bean.message.messageBody.dialog.clickTime = System.currentTimeMillis();
                    iChatCommon.refreshShowData();
                }
                /**没有简历发送 20 和30的action消息*/
                if (contactData != null) {
                    final String securityId = contactData.securityId;
                    ExchangeTestManager.exchangeAcceptTest(activity, securityId, 4, bean.msgId, new Callback<ExchangeTestAcceptResponse>() {
                        @Override
                        public void call(ExchangeTestAcceptResponse exchangeTestAcceptResponse) {
                            ResumeHandleManager.checkResumeException(securityId, bean.msgId, String.valueOf(resumeId), () -> AppThreadFactory.POOL.execute(new Runnable() {
                                @Override
                                public void run() {
                                    ChatDialogBean dialogBean12 = bean.message.messageBody.dialog;
                                    dialogBean12.operated = true;
                                    MessageDaoFactory.getMessageDao().update(bean);
                                    refreshCurrentPage();
                                }
                            }));
                        }
                    });
                }

            });
            return;
        }

        if (dialogBean.type == 22) {
            if (!dialogBean.operated) {
                sendButtonClickMessageWithSelectIndex(bean, index);
            }
            return;
        }

        //对话框样式是否可以点击【微信】【Boss接收附件简历对话框】
        if (iChatCommon.onDialogViewButtonClickIntercept(bean, dialogBean, index)) return;

        //点击微信
        if (dialogBean.type == 1) {

            //是否商业道具，商业道具走发消息流程
            boolean isBusinessTools = false;

            ChatDialogButtonBean buttonBean = LList.getElement(dialogBean.buttons, index - 1);
            if (buttonBean != null) {
                String url = buttonBean.url;
                if (url != null) {
                    Map<String, String> maps = ZPManager.UrlHandler.getParams(url);
                    String aid = maps.get("aid");
                    if (index == 1) {//同意
                        isBusinessTools = LText.equal(aid, "126");
                    }
                    if (index == 2) {//拒绝
                        isBusinessTools = LText.equal(aid, "127");
                    }
                }
            }

            int startChatProcessExpGroup = ChatUtils.getStartChatProcessExpGroup(dialogBean.extend);
            int requestScene = ChatUtils.getRequestScene(dialogBean.extend);
            //同意
            if (index == 1 && !isBusinessTools) {
                if (contactData != null) {
                    final String securityId = contactData.securityId;
                    int scene = ExchangeHelper.getInstance().needConfirm(bean) ? 1 : 0;
                    ExchangeTestManager.exchangeAcceptTest(activity, securityId, 2, bean.msgId, scene, new Callback<ExchangeTestAcceptResponse>() {
                        @Override
                        public void call(ExchangeTestAcceptResponse exchangeTestAcceptResponse) {
                            ExchangeAcceptRequest request = new ExchangeAcceptRequest(new ApiRequestCallback<ExchangeChatResponse>() {

                                @Override
                                public void onStart() {
                                    super.onStart();
                                    if (iChatCommon != null) {
                                        iChatCommon.showProgressDialog("");
                                    }
                                }

                                @Override
                                public void handleInChildThread(ApiData<ExchangeChatResponse> data) {
                                    super.handleInChildThread(data);
                                    ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                                    dialogBean.operated = true;
                                    MessageDaoFactory.getMessageDao().update(bean);
                                }

                                @Override
                                public void onSuccess(ApiData<ExchangeChatResponse> data) {
                                    refreshCurrentPage();
                                }

                                @Override
                                public void onComplete() {
                                    if (iChatCommon != null) {
                                        iChatCommon.dismissProgressDialog();
                                    }
                                }

                                @Override
                                public void onFailed(ErrorReason reason) {
                                    ToastUtils.showText(reason.getErrReason());
                                }
                            });
                            request.securityId = securityId;
                            request.type = "2";
                            request.mid = String.valueOf(bean.msgId);
                            request.startChatProcessExpGroup = startChatProcessExpGroup;
                            request.scene = requestScene > 0 ? requestScene : ExchangeHelper.getInstance().getSource();
                            HttpExecutor.execute(request);
                        }
                    });
                }

                return;
            }

            //拒绝
            if (index == 2 && !isBusinessTools) {
                if (contactData != null) {
                    ExchangeRejectRequest rejectRequest = new ExchangeRejectRequest(new ApiRequestCallback<SuccessResponse>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            if (iChatCommon != null) {
                                iChatCommon.showProgressDialog("");
                            }
                        }

                        @Override
                        public void handleInChildThread(ApiData<SuccessResponse> data) {
                            super.handleInChildThread(data);
                            ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                            dialogBean.operated = true;
                            MessageDaoFactory.getMessageDao().update(bean);
                        }

                        @Override
                        public void onSuccess(ApiData<SuccessResponse> data) {
                            refreshCurrentPage();
                        }

                        @Override
                        public void onComplete() {

                            if (iChatCommon != null) {
                                iChatCommon.dismissProgressDialog();
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    rejectRequest.securityId = contactData.securityId;
                    rejectRequest.type = "2";
                    rejectRequest.mid = String.valueOf(bean.msgId);
                    rejectRequest.startChatProcessExpGroup = startChatProcessExpGroup;
                    rejectRequest.scene = requestScene;
                    HttpExecutor.execute(rejectRequest);
                }

                return;
            }

        }

        //BOSS同意或者拒绝简历

        if (UserManager.isBossRole() && dialogBean.type == 2) {

            //同意简历
            if (index == 1) {
                if (contactData != null) {
                    //BOSS点击同意接受牛人简历，添加check权益检测 https://zhishu.zhipin.com/wiki/OkBAyXAvrCH
                    ContactKeyManager.getInstance().onCheckListener(contactData, new ContactKeyManager.OnContactKeyCheckCallBack2() {

                        @Override
                        public void onCheckNoBlockListener() {
                            final String securityId = contactData.securityId;
                            int scene = ExchangeHelper.getInstance().needConfirm(bean) ? 1 : 0;
                            ExchangeTestManager.exchangeAcceptTest(activity, securityId, 3, bean.msgId, scene, new Callback<ExchangeTestAcceptResponse>() {
                                @Override
                                public void call(ExchangeTestAcceptResponse exchangeTestAcceptResponse) {
                                    ExchangeAcceptRequest request = new ExchangeAcceptRequest(new ApiRequestCallback<ExchangeChatResponse>() {
                                        @Override
                                        public void onStart() {
                                            super.onStart();
                                            if (iChatCommon != null) {
                                                iChatCommon.showProgressDialog("");
                                            }
                                        }

                                        @Override
                                        public void handleInChildThread(ApiData<ExchangeChatResponse> data) {
                                            super.handleInChildThread(data);
                                            ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                                            dialogBean.operated = true;
                                            MessageDaoFactory.getMessageDao().update(bean);
                                        }

                                        @Override
                                        public void onSuccess(ApiData<ExchangeChatResponse> data) {
                                            refreshCurrentPage();
                                        }

                                        @Override
                                        public void onComplete() {
                                            if (iChatCommon != null) {
                                                iChatCommon.dismissProgressDialog();
                                            }
                                        }

                                        @Override
                                        public void onFailed(ErrorReason reason) {
                                            ToastUtils.showText(reason.getErrReason());
                                        }
                                    });
                                    request.securityId = securityId;
                                    request.type = "3";
                                    request.mid = String.valueOf(bean.msgId);
                                    request.scene = ExchangeHelper.getInstance().getSource();
                                    HttpExecutor.execute(request);
                                }
                            });
                        }
                    }, ContactKeyManager.BgSource.NONE);
                }

            }
            //拒绝简历
            if (index == 2) {
                if (contactData != null) {
                    ExchangeRejectRequest rejectRequest = new ExchangeRejectRequest(new ApiRequestCallback<SuccessResponse>() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            if (iChatCommon != null) {
                                iChatCommon.showProgressDialog("");
                            }
                        }

                        @Override
                        public void handleInChildThread(ApiData<SuccessResponse> data) {
                            super.handleInChildThread(data);
                            ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                            dialogBean.operated = true;
                            MessageDaoFactory.getMessageDao().update(bean);
                        }

                        @Override
                        public void onSuccess(ApiData<SuccessResponse> data) {
                            refreshCurrentPage();
                        }

                        @Override
                        public void onComplete() {
                            if (iChatCommon != null) {
                                iChatCommon.dismissProgressDialog();
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    rejectRequest.securityId = contactData.securityId;
                    rejectRequest.type = "3";
                    rejectRequest.mid = String.valueOf(bean.msgId);
                    HttpExecutor.execute(rejectRequest);
                }

            }
            return;
        }


        // 初始化被点击按钮的样式
        ChatDialogButtonBean button = bean.message.messageBody.dialog.buttons.get(index - 1);
        ZPManager manager = new ZPManager(activity, button.url);
        if (!LText.empty(button.url) && manager.isTypeActionSend()) {
            Map<String, String> maps = ZPManager.UrlHandler.getParams(button.url);
            String aid = maps.get("aid");
            boolean isExchangePhone = LText.equal("94", aid);
            boolean isExchangeWx = LText.equal("126", aid);
            if (isExchangePhone || isExchangeWx) {
                int exchangeType = isExchangePhone ? 1 : 2;
                ExchangeTestManager.exchangeAcceptTest(activity, contactData.securityId, exchangeType, bean.msgId, new Callback<ExchangeTestAcceptResponse>() {
                    @Override
                    public void call(ExchangeTestAcceptResponse exchangeTestAcceptResponse) {
                        bean.message.messageBody.dialog.clickTime = System.currentTimeMillis();
                        iChatCommon.refreshShowData();
                        if (contactData != null) {
                            //联系人阻断&&交换简历|电话|微信 点击同意不置灰
                            boolean canClick = contactData.isTechGeekBlock && (dialogBean.type == 1 || dialogBean.type == 2 || dialogBean.type == 11);
                            // 发送点击事件
                            sendButtonClickMessage(bean, index, canClick);
                        }
                    }
                });
                return;
            } else {
                bean.message.messageBody.dialog.clickTime = System.currentTimeMillis();
                iChatCommon.refreshShowData();
            }
        }
        if (contactData != null) {
            //联系人阻断&&交换简历|电话|微信 点击同意不置灰
            boolean canClick = contactData.isTechGeekBlock && (dialogBean.type == 1 || dialogBean.type == 2 || dialogBean.type == 11);
            // 发送点击事件
            sendButtonClickMessage(bean, index, canClick);
        }

    }

    private void geekAgreeExchangeResume(ChatBean bean) {
        ResumeHandleManager resumeHandleManager = new ResumeHandleManager(activity);
        resumeHandleManager.setSecurityId(contactData.securityId);
        resumeHandleManager.setFriendId(contactData.friendId);
        resumeHandleManager.setFriendSource(contactData.friendSource);
        resumeHandleManager.setMsgId(bean.msgId);
        resumeHandleManager.setSceneV2(ExchangeHelper.getInstance().needConfirm(bean) ? 1 : 0);
        resumeHandleManager.onGeekAgreeClickListener(new ResumeHandleManager.OnClickResumeDialogCallBack() {
            @Override
            public void onClickDialogListener(long resumeId, boolean isEmptyOfResumeList) {
                if (resumeId == 0) {

                    if (contactData == null) return;

                    GeekPageRouter.Resume.openAttachmentResume(activity, AttachmentResumeStarter.obj().
                            target(GeekConsts.Target.TARGET_DEFAULT)
                            .setFriendId(contactData.friendId)
                            .setMid(bean.msgId)
                            .setBossRequire(true)
                            .setEmptyOfResumeList(isEmptyOfResumeList)
                            .setFriendSource(contactData.friendSource));

                }
            }

            @Override
            public void onDialogEnableListener() {
                //dialog按钮置灰&&保存到数据库
                AppThreadFactory.POOL.execute(new Runnable() {
                    @Override
                    public void run() {
                        ChatDialogBean dialogBean = bean.message.messageBody.dialog;
                        dialogBean.operated = true;
                        MessageDaoFactory.getMessageDao().update(bean);
                        refreshCurrentPage();
                    }
                });
            }
        });
    }

    /**
     * 向本地聊天数据清加一条数据
     *
     * @param bean
     */
    public void addData(ChatBean bean) {
        if (bean == null) return;
        synchronized (ADD_MESSAGE_LOCK) {
            if (!messageIdSet.contains(bean.msgId) || bean.msgId <= 0) {
                if (bean.msgId > 0) {
                    messageIdSet.add(bean.msgId);
                }
                messageData.add(bean);

                addWaitReceiveMsg(bean);
            }
        }
    }

    public void updateAudioMessage(long mid, String text, boolean needUpdateToDb) {
        MessageDao messageDao = MessageDaoFactory.getMessageDao();
        //防止出现多线程操作问题，加锁会有偶现遗漏问题
        try {
            List<ChatBean> messageList = new ArrayList<>(messageData);
            for (ChatBean messageDatum : messageList) {
                if (messageDatum.msgId == mid) {
                    messageDatum.message.messageBody.sound.saveAudioText(text, needUpdateToDb);
                    if (needUpdateToDb) {
                        TLog.info(TAG, "audio update to db %s", messageDatum);
                        AppThreadFactory.POOL.execute(() -> {
                            messageDao.update(messageDatum);
                        });
                        SimpleApiRequest.POST(ChatUrlConfig.URL_UPDATE_SOUND_MSG)
                                .addParam("messageId", messageDatum.msgId)
                                .addParam("text",  text)
                                .addParam("play", messageDatum.message.messageBody.sound.getIsPlayed())
                                .addParam("trans",  messageDatum.message.messageBody.sound.getIsTrans())
                                .execute();
                        dealVoiceMessage();
                    }
                    refreshMessageData();
                }
            }
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
        }
    }


    //查找消息列表里面的被引用的消息
    private ChatBean queryQuoteChatBean(long quoteId) {
        synchronized (ADD_MESSAGE_LOCK) {
            for (ChatBean messageDatum : messageData) {
                if (messageDatum.msgId == quoteId) {
                    return messageDatum;
                }
            }
        }
        refreshCurrentPage();
        return null;
    }


    /**
     * 获取发送实例
     *
     * @return
     */
    public MSGSender getChatSendCommon() {
        return chatSendCommon;
    }


    private SendMessageUtil sendMessageUtil;


    private boolean isClickChatCard;

    public void setClickChatCard(boolean clickChatCard) {
        isClickChatCard = clickChatCard;
    }

    /**
     * 发送一条文字消息
     *
     * @param text 发送内容
     */
    public void sendGeekGuessAsk(final String text) {

        bossLimitCheckUtils.checkLimit(contactData, refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
            @Override
            public void onBlockResult(String blockTip) {
                if (blockTip != null) {
                    sendFailedTextMessage(text, blockTip);
                } else {
                    if (sendMessageUtil == null) {
                        sendMessageUtil = new SendMessageUtil();
                    }
                    if (contactData == null) return;
                    sendMessageUtil.sentGeekGuessAskChatTextMessage(contactData,
                            text, friendSource, new OnMessageSendCallBack() {
                                @Override
                                public void onMsgStartSendListener(ChatBean contactBean) {
                                    addData(contactBean);
                                    iChatCommon.refreshShowData();
                                    scrollToBottom(false);
                                }

                                @Override
                                public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {

                                    if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                                        addData(createBossLimitStartNoticeText());
                                    }
                                    //通知查看是否双聊
                                    iChatCommon.onSendChatCallback(null);
                                    //通知刷新列表
                                    iChatCommon.refreshShowData();
                                    //滑动到底部
                                    scrollToBottom(false);
                                }
                            });
                }
            }
        });

    }


    private final List<ChatBean> bossTempCheckLimit=new ArrayList<>();

    public synchronized List<ChatBean> refreshBossTempCheckLimitList(){
        bossTempCheckLimit.clear();
        bossTempCheckLimit.addAll(messageData);
        return bossTempCheckLimit;
    }

    public void sendFailedTextMessage(final String text) {
        sendFailedTextMessage(text, null);
    }
    /**
     * 沟通钥匙需要 发送一个失败的消息->入库
     *
     * @param text
     * @param blockTip
     */
    public void sendFailedTextMessage(final String text, String blockTip) {
        if (sendMessageUtil == null) {
            sendMessageUtil = new SendMessageUtil();
        }
        if (contactData == null) return;
        sendMessageUtil.sendContactKeyFailedChatTextMessage(contactData, text, friendSource, new SendMessageUtil.OnSaveFailedMessageCallBack() {

            @Override
            public void onSaveMessageSuccessListener(ChatBean chatBean) {
                //添加到消息集合
                addData(chatBean);
                if (blockTip != null) {
                    addData(createBossLimitReasonText(blockTip));
                }
                //刷新适配器
                iChatCommon.refreshShowData();
                //滑动到底部
                scrollToBottom(false);
            }
        });
    }

    /**
     * @param text
     * @param quoteId
     */
    public void sendNlpSuggestTextMessage(final String text, long quoteId, ChatBean quoteChatBean) {
        if (sendMessageUtil == null) {
            sendMessageUtil = new SendMessageUtil();
        }

        int bizCode = 108;

        if (contactData == null) return;

        sendMessageUtil.sentChatTextMessageWithBizCode(contactData,
                text, friendSource,
                bizCode,
                quoteId, new OnMessageSendCallBack() {
                    @Override
                    public void onMsgStartSendListener(ChatBean contactBean) {
                        contactBean.message.quoteChatBean = quoteChatBean;
                        addData(contactBean);
                        iChatCommon.refreshShowData();
                        scrollToBottom(false);
                    }

                    @Override
                    public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                        if (activity.isFinishing()) {
                            return;
                        }

                        if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                            addData(createBossLimitStartNoticeText());
                        }

                        if (isClickChatCard) {//点击卡片进来提示常用语
                            isClickChatCard = false;
                            //已经存在了，不出现提示
                            if (CommonWordManager.getInstance().checkWeatherHasSet(text)) return;
                            //添加到聊天列表里面
                            addData(ChatBeanFactory.getInstance().createChatCommonBean(text));
                            iChatCommon.refreshShowData();
                            scrollToBottom(false);
                        } else {
                            //检测是否提示 添加常用语文案
                            checkShowAddCommon(clientTempMessageId, text);
                        }

                        //通知查看是否双聊
                        iChatCommon.onSendChatCallback(null);
                        //通知刷新列表
                        iChatCommon.refreshShowData();

                    }
                });
    }

    public void sendTextMessage(final String text,
                                boolean isNLPChatCommon,
                                long quoteId, ChatBean quoteChatBean) {
        sendTextMessage(text, isNLPChatCommon, quoteId, quoteChatBean, null);
    }

    public void sendTextMessage(final String text,
                                boolean isNLPChatCommon,
                                long quoteId, ChatBean quoteChatBean, @Nullable DailyGuideBean dailyGuideBean) {
        sendTextMessage(text, isNLPChatCommon, quoteId, quoteChatBean, dailyGuideBean, null);
    }

    public void sendTextMessage(final String text, boolean isNLPChatCommon, long quoteId, ChatBean quoteChatBean, @Nullable DailyGuideBean dailyGuideBean, SendMsgExtraBean extraBean) {
        if (sendMessageUtil == null) {
            sendMessageUtil = new SendMessageUtil();
        }

        int bizCode = 0;
        if (isNLPChatCommon) {
            bizCode = 105;
        } else if (dailyGuideBean != null && dailyGuideBean.scene == 1) {
            bizCode = 21050161;
        } else if (extraBean != null && extraBean.bizCode > 0) {
            bizCode = extraBean.bizCode;
        }

        if (contactData == null) return;

        sendMessageUtil.sentChatTextMessageWithBizCode(contactData,
                text, friendSource,
                bizCode,
                quoteId, new OnMessageSendCallBack() {
                    @Override
                    public void onMsgStartSendListener(ChatBean contactBean) {
                        contactBean.message.quoteChatBean = quoteChatBean;
                        addData(contactBean);
                        iChatCommon.refreshShowData();
                        scrollToBottom(false);
                    }

                    @Override
                    public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                        if (activity.isFinishing()) {
                            return;
                        }

                        if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                            addData(createBossLimitStartNoticeText());
                        }

                        if (isClickChatCard) {//点击卡片进来提示常用语
                            isClickChatCard = false;
                            //已经存在了，不出现提示
                            if (CommonWordManager.getInstance().checkWeatherHasSet(text)) return;
                            //添加到聊天列表里面
                            addData(ChatBeanFactory.getInstance().createChatCommonBean(text));
                            iChatCommon.refreshShowData();
                            scrollToBottom(false);
                        } else {
                            //检测是否提示 添加常用语文案
                            checkShowAddCommon(clientTempMessageId, text);
                        }

                        //通知查看是否双聊
                        iChatCommon.onSendChatCallback(null);
                        //通知刷新列表
                        iChatCommon.refreshShowData();

                    }
                });
    }

    public void sendTextMessageWithBizTypeCheckKey(final String text, int bizId, String bgSource) {
        if(UserManager.isGeekRole()) {
            sendTextMessageWithBizType(text, bizId);
        } else {
            bossLimitCheckUtils.checkLimit(contactData, refreshBossTempCheckLimitList(), blockTip -> {
                if (blockTip != null) {
                    sendFailedTextMessage(text, blockTip);
                } else {
                    ContactKeyManager.getInstance().onSendMessageWithSourceListener(contactData, bgSource, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                        @Override
                        public void onCheckNoBlockListener() {
                            sendTextMessageWithBizType(text, bizId);
                        }

                        @Override
                        public void onCheckBlockUrlListener(String url) {
                            sendFailedTextMessage(text);
                        }

                        @Override
                        public void onCheckBlockToastListener(String msg) {
                            sendFailedTextMessage(text);
                        }
                    });
                }
            });
        }
    }

    public void sendTextMessageWithBizType(final String text,
                                           int bizType) {
        if (sendMessageUtil == null) {
            sendMessageUtil = new SendMessageUtil();
        }

        if (contactData == null) return;

        sendMessageUtil.sentChatTextMessageWithBizIDBizId(contactData,
                text, friendSource,
                "",
                bizType,
                new OnMessageSendCallBack() {
                    @Override
                    public void onMsgStartSendListener(ChatBean contactBean) {
                        addData(contactBean);
                        iChatCommon.refreshShowData();
                        scrollToBottom(false);
                    }

                    @Override
                    public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                        if (activity.isFinishing()) {
                            return;
                        }

                        if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                            addData(createBossLimitStartNoticeText());
                        }

                        if (isClickChatCard) {//点击卡片进来提示常用语
                            isClickChatCard = false;
                            //已经存在了，不出现提示
                            if (CommonWordManager.getInstance().checkWeatherHasSet(text)) return;
                            //添加到聊天列表里面
                            addData(ChatBeanFactory.getInstance().createChatCommonBean(text));
                            iChatCommon.refreshShowData();
                            scrollToBottom(false);
                        } else {
                            //检测是否提示 添加常用语文案
                            checkShowAddCommon(clientTempMessageId, text);
                        }

                        //通知查看是否双聊
                        iChatCommon.onSendChatCallback(null);
                        //通知刷新列表
                        iChatCommon.refreshShowData();

                    }
                });
    }

    /**
     * 发送一条文字消息
     *
     * @param text            发送内容
     * @param isNLPChatCommon
     */
    public void sendTextMessage(final String text, boolean isNLPChatCommon) {
        sendTextMessage(text, isNLPChatCommon, null);
    }

    public void sendTextMessage(final String text, SendMsgExtraBean extraBean) {
        sendTextMessage(text, false, null, extraBean);
    }

    public void sendTextMessage(final String text, boolean isNLPChatCommon, @Nullable DailyGuideBean dailyGuideBean) {
        sendTextMessage(text, isNLPChatCommon, dailyGuideBean, null);
    }

    public void sendTextMessage(final String text, boolean isNLPChatCommon, @Nullable DailyGuideBean dailyGuideBean, SendMsgExtraBean extraBean) {
        if (UserManager.isGeekRole()) {
            sendTextMessage(text, isNLPChatCommon, 0, null, dailyGuideBean, extraBean);
        } else {
            bossLimitCheckUtils.checkLimit(contactData, refreshBossTempCheckLimitList(), blockTip -> {
                if (blockTip != null) {
                    sendFailedTextMessage(text, blockTip);
                } else {
                    ContactKeyManager.getInstance().onInputSendMsgListener(contactData, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                        @Override
                        public void onCheckNoBlockListener() {
                            sendTextMessage(text, isNLPChatCommon, 0, null, dailyGuideBean, extraBean);
                        }

                        @Override
                        public void onCheckBlockUrlListener(String url) {
                            sendFailedTextMessage(text);
                        }

                        @Override
                        public void onCheckBlockToastListener(String msg) {
                            sendFailedTextMessage(text);
                        }
                    });

                }
            });
        }
    }

    /**
     * 发送追聊常用语消息
     *
     * @param text
     * @param bizCode
     */
    public void sendChatCommon(final String text, int bizCode) {

        if (contactData == null) return;

        bossLimitCheckUtils.checkLimit(contactData, refreshBossTempCheckLimitList(), new BossBlockCheck.BlockCallBackListener() {
            @Override
            public void onBlockResult(String blockTip) {
                if (blockTip != null) {
                    sendFailedTextMessage(text, blockTip);
                } else {
                    if (sendMessageUtil == null) {
                        sendMessageUtil = new SendMessageUtil();
                    }

                    sendMessageUtil.sentChatTextMessageWithBizCode(contactData,
                            text, friendSource,
                            bizCode,
                            0, new OnMessageSendCallBack() {
                                @Override
                                public void onMsgStartSendListener(ChatBean contactBean) {

                                    addData(contactBean);
                                    iChatCommon.refreshShowData();
                                    scrollToBottom(false);
                                }

                                @Override
                                public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                                    if (activity.isFinishing()) {
                                        return;
                                    }

                                    if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                                        addData(createBossLimitStartNoticeText());
                                    }

                                    if (isClickChatCard) {//点击卡片进来提示常用语
                                        isClickChatCard = false;
                                        //已经存在了，不出现提示
                                        if (CommonWordManager.getInstance().checkWeatherHasSet(text))
                                            return;
                                        //添加到聊天列表里面
                                        addData(ChatBeanFactory.getInstance().createChatCommonBean(text));
                                        iChatCommon.refreshShowData();
                                        scrollToBottom(false);
                                    } else {
                                        //检测是否提示 添加常用语文案
                                        checkShowAddCommon(clientTempMessageId, text);
                                    }

                                    //通知查看是否双聊
                                    iChatCommon.onSendChatCallback(null);
                                    //通知刷新列表
                                    iChatCommon.refreshShowData();

                                }
                            });
                }
            }
        });


    }


    //检测是否提示 添加常用语文案
    private void checkShowAddCommon(long clientTempMessageId, String text) {
        //设置为常用语提示
        CommonWordManager.getInstance().addToCache(text, new CommonWordManager.OnSendAddChatCommonCallback() {
            @Override
            public void onSendAddChatCommonListener(boolean isClipBoard) {
                //延时等待消息同步id
                App.get().getMainHandler().postDelayed(() -> AppThreadFactory.POOL.execute(new Runnable() {
                    @Override
                    public void run() {
                        //埋点
                        ChatBean localBean = MessageDaoFactory.getMessageDao().queryChatByClientMsgId(clientTempMessageId);
                        if (localBean != null) {
                            if (contactData != null) {
                                AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_QUICK_REPLAY)
                                        .param("p", contactData.friendId + "")
                                        .param("p2", localBean.msgId + "")
                                        .param("p3", isClipBoard ? "1" : "2")
                                        .build();
                            }
                        }
                    }
                }), 1000);


                //添加到聊天列表里面
                addData(ChatBeanFactory.getInstance().createChatCommonBean(text));
                iChatCommon.refreshShowData();
                scrollToBottom(false);
            }
        });
    }


    /**
     * 发送一条dialog按钮点击事件消息
     *
     * @param useChatBean 使用的实例
     * @param index       按钮的下标
     */
    public void sendButtonClickMessage(ChatBean useChatBean, int index, boolean canClick) {
        if (contactData != null) {
            chatSendCommon.sendButtonClickMessage(MessageTargetInfo.fromContactBean(contactData), "", useChatBean, index, friendSource, new ChatSendCallback() {
                @Override
                public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                    ChatDialogBean dialogBean = useChatBean.message.messageBody.dialog;
                    dialogBean.operated = success;
                    //保存到数据库
                    AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().update(useChatBean));
                    iChatCommon.refreshShowData();
                }
            });
            iChatCommon.refreshShowData();
        }
    }

    /**
     * 这个 处理两次
     * 第一个 发送点击Action 消息
     * 第二个 处理  协议地址
     *
     * @param useChatBean
     * @param index
     */
    public void sendButtonClickMessageWithSelectIndex(ChatBean useChatBean, int index) {
        if (contactData != null) {
            chatSendCommon.sendButtonClickMessage(MessageTargetInfo.fromContactBean(contactData), "", useChatBean, index, friendSource, new ChatSendCallback() {
                @Override
                public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                    ChatDialogBean dialogBean = useChatBean.message.messageBody.dialog;
                    dialogBean.operated = true;
                    dialogBean.selectedIndex = index;
                    //保存到数据库
                    AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().update(useChatBean));
                    iChatCommon.refreshShowData();
                }
            });
            iChatCommon.refreshShowData();
        }
    }

    /**
     * 只处理 发送 点击 Action 消息
     *
     * @param useChatBean
     * @param index
     */
    public void sendButtonMessageWithSelectIndex(ChatBean useChatBean, int index) {
        if (contactData != null) {
            chatSendCommon.sendButtonActionClickMessage(MessageTargetInfo.fromContactBean(contactData), useChatBean, index, friendSource, new ChatSendCallback() {
                @Override
                public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                    ChatDialogBean dialogBean = useChatBean.message.messageBody.dialog;
                    dialogBean.operated = true;
                    dialogBean.selectedIndex = index;
                    //保存到数据库
                    AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().update(useChatBean));
                    iChatCommon.refreshShowData();
                }
            });
            iChatCommon.refreshShowData();
        }
    }


    @Override
    public void onClickSendFailViewListener(final ChatBean bean) {
        if (bean == null) {
            ToastUtils.showText("重新发送消息失败");
            return;
        }

        if (contactData != null) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_RESEND_CHAT_CLICK).param("p", String.valueOf(contactData.friendId)).build();

            bossLimitCheckUtils.checkLimit(contactData, refreshBossTempCheckLimitList(), blockTip -> {

                if (blockTip != null) {
                    AppThreadFactory.POOL.execute(() -> {
                        MessageDaoFactory.getMessageDao().deleteByClientId(bean.clientTempMessageId);
                        bean.time = System.currentTimeMillis();
                        bean.id=0;
                        contactData.lastChatText = MessageUtils.getSingleChatLastText(bean, contactData, false);
                        contactData.lastChatStatus = 2;
                        contactData.lastChatTime = System.currentTimeMillis();
                        contactData.updateTime = contactData.lastChatTime;
                        ContactManager.getInstance().insertOrUpdateAllField(contactData, UserManager.getUserRole().get());
                        //保存一个失败的消息到数据库
                        MessageDaoFactory.getMessageDao().saveChat(bean);
                    });
                    synchronized (ADD_MESSAGE_LOCK) {
                        messageData.remove(bean);
                        addData(bean);
                        addData(createBossLimitReasonText(blockTip));
                    }
                    refreshMessageData();
                    //滑动到底部
                    scrollToBottom(false);
                    return;
                }

                ContactKeyManager.getInstance().onSendFailedMessageListener(contactData, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                    @Override
                    public void onCheckNoBlockListener() {
                        final int friendSource = contactData != null ? contactData.friendSource : 0;
                        final ChatBean temp = chatSendCommon.sendMessage(MessageTargetInfo.fromContactBean(contactData), friendSource, bean, new ChatSendCallback() {
                            @Override
                            public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                                iChatCommon.refreshShowData();
                            }
                        });
                        if (temp != null) {
                            MessageDaoFactory.getMessageDao().deleteByClientId(bean.clientTempMessageId);
                            synchronized (ADD_MESSAGE_LOCK) {
                                messageData.remove(bean);
                                addData(temp);
                            }
                            refreshMessageData();
                            //滑动到底部
                            scrollToBottom(false);
                        } else {
                            ToastUtils.showText("重新发送消息失败");
                        }
                    }
                });
            });



        }


    }

    @Deprecated
    @Override
    public void onRedMoneyViewClickCallback(long id) {
        /**
         * 【红包】功能下线
         */
    }

    /**
     * 发送一条红包消息
     */
    @Deprecated
    public void sendRedMoneyMessage() {
        L.d("红包功能下线");
    }


    // ---------- 播放声音方法集合相关操作方法 ----------

    // 播放声音实例
    private SoundPlayer mSoundPlayer;
    // 当前正在播放的消息数据实例
    private ChatSoundBean mCurrentPlayerBean;


    private void registerPauseReceiver() {
        ReceiverUtils.register(activity, receiver, ACTION_PAUSE_AUDIO, Constants.RECEIVER_PHONE_EXCHANGE_DIALOG_GUIDE);
    }

    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {

            if (LText.equal(intent.getAction(), Constants.RECEIVER_PHONE_EXCHANGE_DIALOG_GUIDE)) {
                ChatBean chatBean = (ChatBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
                if (chatBean != null) {

                    if (contactData != null && chatBean.fromUserId == contactData.friendId) {
                        boolean isShow = true;
                        try {
                            ChatActionBean chatActionBean = chatBean.message.messageBody.action;
                            if (!LText.empty(chatActionBean.extend)) {
                                JSONObject jsonObject = new JSONObject(chatActionBean.extend);
                                isShow = jsonObject.optInt("show") == 1;
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        PhoneExchangeDialog phoneExchangeDialog = new PhoneExchangeDialog(activity, isShow);
                        phoneExchangeDialog.setJobId(jobId);
                        phoneExchangeDialog.setFrom(2);
                        phoneExchangeDialog.setOnOpenExchangeCallback(new PhoneExchangeDialog.PhoneExchangeListener());
                        phoneExchangeDialog.showChatTip();
                    }
                }

            } else if (LText.equal(intent.getAction(), ACTION_PAUSE_AUDIO)) {
                //暂停
                stopAudio();
            }
        }
    };


    @Override
    public boolean onPlayer(ChatSoundBean bean, AudioSummary audioSummary) {
        if (bean == null) return false;
        if (LText.empty(bean.url)) return false;
        if (mSoundPlayer == null) {
            mSoundPlayer = new SoundPlayer(activity);
        }

        mSoundPlayer.setOnPlayerSoundCompleteCallback(this);
        mSoundPlayer.setOnMediaPlayerProgressListener(progress -> {
            bean.progress = progress;
            if (audioSummary != null) {
                audioSummary.setProgress(progress);
            }
        });

        if (!bean.playing) {
            ChatSoundBean currentPlayerBean = mSoundPlayer.getCurrentBean();
            if (currentPlayerBean != null) {
                currentPlayerBean.playing = false;
                iChatCommon.refreshShowData();
            }
            mCurrentPlayerBean = bean;
            sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_NORMAL);
            return mSoundPlayer.player(bean);
        } else {
            mSoundPlayer.stop();
            sensorManager.unregisterListener(this);
        }
        mCurrentPlayerBean = null;
        return false;
    }


    @Override
    public void onPlayerSoundCompleteCallback(ChatSoundBean bean) {
        sensorManager.unregisterListener(this);
        if (bean != null) {
            bean.playing = false;
            iChatCommon.refreshShowData();
        }
        mCurrentPlayerBean = null;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (mSoundPlayer == null) return;
        float f = event.values[0];
        if (f == sensor.getMaximumRange()) {
            // 距离远时
            mSoundPlayer.setNormalPlayMode();
        } else {
            // 距离近时
            mSoundPlayer.setInCallPlayMode();
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }

    /**
     * ==============================================开始录制声音和上传====================================================
     */


    @Override
    public void onRecordSoundStartCallback(int status) {
        if (mSoundPlayer != null) {
            mSoundPlayer.stop();
        }
        if (mCurrentPlayerBean != null) {
            mCurrentPlayerBean.playing = false;
            iChatCommon.refreshShowData();
        }
    }

    @Override
    public void onRecordSoundStopCallback(String filePath) {
    }

    //上传语音
    private UploadAudioUtil uploadAudioUtil;

    /**
     * 普通录音
     *
     * @param filePath
     * @param duration
     */
    @Override
    public void onRecordSoundCompleteCallback(String filePath, int duration) {
        if (uploadAudioUtil == null) {
            uploadAudioUtil = new UploadAudioUtil(activity);
        }
        if (contactData == null) return;
        //发送语音
        uploadAudioUtil.uploadSingleChatAudio(contactData, filePath, url -> {
            //发送声音消息
            uploadAudioUtil.sendChatSoundMessage(contactData,
                    filePath, url,
                    duration, friendSource
                    , new OnMessageSendCallBack() {

                        @Override
                        public void onMsgStartSendListener(ChatBean contactBean) {
                            addData(contactBean);
                            iChatCommon.refreshShowData();
                            scrollToBottom(false);
                        }

                        @Override
                        public void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess) {
                            if (activity.isFinishing()) {
                                return;
                            }

                            if (BossBlockCheck.checkMaxLimitAddText(contactData, refreshBossTempCheckLimitList())) {
                                addData(createBossLimitStartNoticeText());
                            }
                            //通知查看是否双聊
                            iChatCommon.onSendChatCallback(null);
                            //通知刷新列表
                            iChatCommon.refreshShowData();
                        }
                    });
        });
    }


    public void sendFailedGif(String encSid, long packageId, long emotionId, ChatImageInfoBean origin, ChatImageInfoBean tiny, String name, String blockTip) {
        new SendMessageUtil().sendFailedGifMessage(contactData,
                MessageTargetInfo.fromContactBean(contactData),
                encSid,
                packageId,
                emotionId,
                origin,
                tiny,
                name, new SendMessageUtil.OnSaveFailedMessageCallBack() {
                    @Override
                    public void onSaveMessageSuccessListener(ChatBean chatBean) {
                        addData(chatBean);
                        addData(createBossLimitReasonText(blockTip));
                        iChatCommon.refreshShowData();
                        scrollToBottom(false);
                    }
                });
    }


    public void sendFailedAudioFile(String audioFile, int duration, String blockTip) {
        UploadAudioUtil uploadAudioUtil = new UploadAudioUtil(activity);
        uploadAudioUtil.uploadSingleChatAudio(contactData, audioFile, url -> {
            new SendMessageUtil().sendFailedAudioMessage(contactData,
                    MessageTargetInfo.fromContactBean(contactData),
                    audioFile, url,
                    duration, contactData.friendSource, new SendMessageUtil.OnSaveFailedMessageCallBack() {
                        @Override
                        public void onSaveMessageSuccessListener(ChatBean chatBean) {
                            addData(chatBean);
                            addData(createBossLimitReasonText(blockTip));
                            iChatCommon.refreshShowData();
                            scrollToBottom(false);
                        }
                    });

        });

    }

    public ChatBean createBossLimitReasonText(String blockTip) {
        ChatBean chatBean = ChatBeanFactory.getInstance().createGrayText(MessageTargetInfo.fromContactBean(contactData), blockTip);
        AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().saveChat(chatBean));
        return chatBean;
    }

    public ChatBean createBossLimitReasonText() {
        int bossBlockThreshold = CommonConfigManager.getInstance().getBossBlockThreshold();
        ChatBean chatBean = ChatBeanFactory.getInstance().createGrayText(MessageTargetInfo.fromContactBean(contactData), "您已经发送了" + bossBlockThreshold + "条消息，请耐心等待对方回复");
        AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().saveChat(chatBean));
        return chatBean;
    }

    public ChatBean createBossLimitStartNoticeText() {
        int bossBlockThreshold = CommonConfigManager.getInstance().getBossBlockThreshold();
        ChatBean chatBean = ChatBeanFactory.getInstance().createGrayText(MessageTargetInfo.fromContactBean(contactData), "对方未回复您之前，您最多只能发送" + bossBlockThreshold + "条消息");
        AppThreadFactory.POOL.execute(() -> MessageDaoFactory.getMessageDao().saveChat(chatBean));
        return chatBean;
    }

    @Override
    public void onUpdateMicStatus(int status, int time) {
    }

    /**
     * ==============================================结束录制声音和上传====================================================
     */


    @Override
    public void onClickSendText(String text) {
        sendTextMessage(text, false);
    }

    @Override
    public void onClickZPManagerUrl(String url) {
        ZPManager manager = new ZPManager(activity, url);
        if (manager.isOpenReportDialog()) {
            if (contactData != null) {
                new Report.Builder()
                        .setReportedId(contactData.friendId)
                        .setJobId(contactData.jobId)
                        .setExpectId(contactData.jobIntentId)
                        .setSource(ReportEvidenceActivity.SOURCE_CHAT)
                        .setSecurityId(contactData.securityId)
                        .setListener((index, reportUserId, jobId, expectId, code, name) -> ReportEvidenceActivity.startActivity(activity, reportUserId, jobId, expectId, code, ReportEvidenceActivity.SOURCE_CHAT, contactData.securityId))
                        //Boss举报牛人，在牛人详情页和聊天设置页举报处，新增一个栏目「面试爽约」、当牛人是BOSS直聘的C时展示。店长直聘的C不展示
                        .setExtarReson((UserManager.isBossRole() && contactData.friendSource == ContactBean.FROM_BOSS) ? new ReportDialog.ExtarReson(Collections.singletonList(ReportDialog.RESON_INTEVIEW)).setPosition(0) : null)
                        .build(activity)
                        .show();
            }

        } else {
            manager.handler();
        }
    }

    @Override
    public void onClickInterviewMessage(String protocol) {
        if (contactData == null) return;
        if (contactData.currentInterviewStatus > -1 &&
                !TextUtils.isEmpty(contactData.currentInterviewProtocol)) {
            String url = contactData.currentInterviewProtocol;
            new ZPManager(activity, url).handler();
        } else if (!TextUtils.isEmpty(protocol)) {
            new ZPManager(activity, protocol).handler();
        } else if (UserManager.getUserRole() == ROLE.BOSS) {
            InterviewCreateIntentBean bean = new InterviewCreateIntentBean(InterviewCommonUtils.createContactBean(contactData), 1);
            InterviewsRouter.tryCreateInterview(activity, bean);
        }
    }


    /**
     * 发送职位卡片
     *
     * @param job
     */
    public void sendJobCard(final JobSwitchBean job) {
        if (job == null || contactData == null) return;
        iChatCommon.showProgressDialog("正在发送");

        SendJobCardRequest request = new SendJobCardRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void handleInChildThread(ApiData<SuccessResponse> data) {
                super.handleInChildThread(data);
                if (contactData != null) {
                    if (job.jobType == 6) {
                        contactData.bossJobPosition = "兼职·" + job.jobName;
                    } else {
                        contactData.bossJobPosition = job.jobName;
                    }
                    contactData.jobId = job.jobId;
                    //更新数据库
                    ContactManager.getInstance().insertOrUpdateAllField(contactData, UserManager.getUserRole().get());
                }
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (contactData != null) {
                    iChatCommon.initTitleValue(contactData);
                }
                ContactKeyManager.getInstance().clear();
                /*发送开放职位广播*/
                Intent intent = new Intent(Constants.RECEIVER_OPEN_JOB);
                ReceiverUtils.sendBroadcast(activity, intent);
            }

            @Override
            public void onComplete() {
                iChatCommon.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.geekId = String.valueOf(contactData.friendId);
        request.jid = String.valueOf(job.jobId);
        request.securityId = contactData.securityId;
        HttpExecutor.execute(request);
    }

    /**
     * 1214.612【C】针对开聊后无达成的情况，在消息对话时给牛人推荐更活跃的职位
     */
    private void getGeekRecommendJobs() {
        if (!DataStarGray.getInstance().isOpenJobRecommend() || !UserManager.isGeekRole()) {
            return;
        }

        // 双聊或牛人从来没发过消息的情况下直接结束
        if (contactData.isContactEachOther()
                || contactData.friendType != 1
                || contactData.lastChatTime <= 0) {
            return;
        }

        if ((contactData.lastChatStatus == 1 || contactData.lastChatStatus == 3)
                && TimeUtils.getIntervalMinute(contactData.lastChatTime) > CommonConfigManager.getInstance().getUnReplyCheckInterval()) {
            requestRecommendJobList("1", null);
        }
    }

    public boolean requestRecommendJob() {
        if (!isShowRecommendJobListCard) {
            boolean requestRecommendJobParamType = requestRecommendJobParamType();
            if (requestRecommendJobParamType) return true;
            String recommendJobParamScene = getRecommendJobParamRefuseReason();
            if (recommendJobParamScene != null) {
                requestRecommendJobList("2", recommendJobParamScene);
                return true;
            }
        }
        return false;
    }

    //1301.604【C】蓝领-具有到店面试意图时推荐附近职位@徐楠@张群
    public boolean requestRecommendJobParamType() {
        long friendId = getFriendId();
        String key = Constants.SP_KEY_CHAT_ACTION_MESSAGE_174_PRE + friendId;
        String message173 = SpManager.get().user("chat").getString(key, null);
        if (message173 != null) {
            SpManager.get().user("chat").edit().remove(key).apply();
            try {
                JSONObject jsonObject = new JSONObject(message173);
                long expireTime = jsonObject.optLong("expireTime", 0);
                if (expireTime <= 0 || System.currentTimeMillis() < expireTime) {
                    String type = jsonObject.optString("type");
                    String parameters = jsonObject.optString("parameters");
                    Map<String, String> paramsMap = null;
                    if (!TextUtils.isEmpty(parameters)) {
                        paramsMap = GsonUtils.fromJson(parameters, new TypeToken<Map<String, String>>() {
                        }.getType());
                    }
                    requestRecommendJobList(type, null, paramsMap);
                }
            } catch (Exception e) {
                TLog.error("ChatBroadCast", e, "requestRecommendJobParamType");
            }

        }
        return false;
    }

    public String getRecommendJobParamRefuseReason() {
        long friendId = getFriendId();
        String key = Constants.SP_KEY_CHAT_ACTION_MESSAGE_171_PRE + friendId;
        String message171 = SpManager.get().user("chat").getString(key, null);
        if (message171 != null) {
            SpManager.get().user("chat").edit().remove(key).apply();
            try {
                JSONObject jsonObject = new JSONObject(message171);
                String scene = jsonObject.optString("scene");
                long expireTime = jsonObject.optLong("expireTime", 0);
                if (System.currentTimeMillis() < expireTime) {
                    return scene;
                }
            } catch (Exception e) {
                TLog.error("ChatBroadCast", e, "checkRecommendJobParam");
            }

        }
        return null;
    }

    private boolean isShowRecommendJobListCard = false;

    public void requestRecommendJobList(String type, String refuseReason) {
        requestRecommendJobList(type, refuseReason, null);
    }

    public void requestRecommendJobList(String type, String refuseReason, Map<String, String> parameters) {
        if (contactData == null) return;
        SimpleApiRequest.GET(GeekUrlConfig.URL_GEEK_CHAT_RECOMMEND_JOB_LIST)
                .addParam("securityId", contactData.securityId)
                .addParam("type", type) // 触发场景值：1（1214.612【C】针对开聊后无达成的情况，在消息对话时给牛人推荐更活跃的职位） type=2, 1216.609【C端】蓝领-对话中含拒绝意图场景推荐更多职位
                .addParam("refuseReason", refuseReason)// refuseReason， type=2时具体拒绝原因，1. C 对 B 表达拒绝意图 - 当 C 侧表达了对 B 的拒绝意图后进行展示， 2. B 对 C 表达拒绝意图—C 有感知 - 当 B侧表达了对 C 的拒绝意图后进行展示    ，3. B 对 C 表达拒绝意图—C无感知 - 当 B侧表达了对 C 的拒绝意图后进行展示， 4. 特殊场景 - 职位下线/关闭等其他职位异常场景
                .addParams(parameters)
                .setRequestCallback(new SimpleApiRequestCallback<GeekChatRecommendJobListResponse>() {
                    @Override
                    public void onSuccess(ApiData<GeekChatRecommendJobListResponse> data) {
                        if (data == null
                                || data.resp == null
                                || LList.isEmpty(data.resp.jobList)
                                || data.resp.jobList.size() < 2) {
                            TLog.debug(TAG, "geek recommend job list is null.");
                            return;
                        }

                        ChatBean chatBean = ChatBeanFactory.getInstance().createGeekRecommendJobsChatBean(data.resp.title, data.resp.popupTitle, data.resp.jobList);
                        addData(chatBean);
                        iChatCommon.refreshShowData();
                        scrollToBottom(false);
                        isShowRecommendJobListCard = true;
                        TLog.debug(TAG, "geek recommend job list request success, refresh page.");
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        TLog.debug(TAG, "recommend job list requested failed, error = %s", reason.toString());
                    }
                })
                .execute();
    }


    public void vip4PurchaseSuccess() {
        String smsInput = SpManager.get().user().getString(BusinessConst.LAST_SMS_CONTENT, "");
        int sourceType = SpManager.get().user().getInt(BusinessConst.SOURCE_TYPE, 0);
        smsItemSendMessage(smsInput, sourceType);
    }

    /**
     * 是否有加密消息
     *
     * @return
     */
    public boolean hasEncryptMessage() {
        return hasEncryptMessage;
    }


    public boolean checkEncryptMessage() {
        return hasEncryptMessage = contactData != null && MessageDaoFactory.getMessageDao().hasEncryptMessage(contactData.friendId);
    }

    //判断当前
    public boolean isCurrentFriendId(long friendId, int friendSource) {
        return friendId == getFriendId() && getFriendSource() == friendSource;
    }


    //监控好友id回掉
    public interface OnFriendIdCallBack {
        void onHasFriendIdListener(@NonNull ContactBean contactBean);
    }


    //暴露给外侧activity使用,获得ContactBean方法
    public void obtainContactBean(@NonNull OnFriendIdCallBack callBack) {
        if (contactData != null) {
            callBack.onHasFriendIdListener(contactData);
        }
    }


    //注册 监听查询引用消息
    public void registerQuoteMessage(long mid, @Nullable OnQuoteMessageCallBack onQuoteMessageCallBack) {
        if (mid <= 0) {
            App.get().getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    //切换到主UI涉及到UI更新隐藏
                    if (onQuoteMessageCallBack != null) {
                        onQuoteMessageCallBack.onQuoteMessageListener(null);
                    }
                }
            });
            return;
        }
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                //查询消息
                ChatBean chatBean = MessageDaoFactory.getMessageDao().queryChatByMsgId(mid);
                //切换到主UI涉及到UI更新隐藏
                App.get().getMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (onQuoteMessageCallBack != null) {
                            onQuoteMessageCallBack.onQuoteMessageListener(chatBean);
                        }
                    }
                });
            }
        });
    }

    // NLP气泡：「同意交换微信」和「同意交换电话」的点击。https://zhishu.zhipin.com/wiki/b0DRBXJ8hRg
    public void onAcceptExchangePhoneOrWx(long reqMsgId) {
        AppThreadFactory.POOL.execute(()->{
            ChatBean chatBean = MessageDaoFactory.getMessageDao().queryChatByMsgId(reqMsgId);
            if (chatBean != null) {
                App.get().getMainHandler().post(()->{
                    ExchangeHelper.getInstance().clearFlags();
                    onDialogViewButtonClickCallback(chatBean, 1);
                });
            }
        });
    }

    private VerbalInterviewBottomDialog mVerbalInterviewBottomDialog;

    public void sendVerbalInterviewToGeek(String securityId, String recordId, int source) {
        SimpleApiRequest.GET(ChatUrlConfig.URL_VERBAL_INTERVIEW)
                .addParam("securityId", securityId)
                .addParam("recordId", recordId)
                .setRequestCallback(new SimpleCommonApiRequestCallback<VerbalInterviewResponse>() {
                    @Override
                    public void onSuccess(ApiData<VerbalInterviewResponse> data) {
                        if (data.resp == null) {
                            TLog.info(TAG, "verbal interview data is null");
                            return;
                        }

                        mVerbalInterviewBottomDialog = new VerbalInterviewBottomDialog(activity, data.resp, source, jobBean -> {
                            selectLocation(activity, jobBean, ChatBaseActivity.REQ_SELECT_LOCATION);
                        });
                        mVerbalInterviewBottomDialog.setPageFrom(VerbalInterviewBottomDialog.PAGE_FROM_CHAT);
                        if (ActivityUtils.isValid(activity)) {
                            mVerbalInterviewBottomDialog.show();
                        }
                    }
                })
                .execute();
    }

    public void selectLocation(Activity activity, JobBean jobBean, int requestCode) {
        if (jobBean == null) {
            ToastUtils.showText("沟通职位不在线，无法使用");
            TLog.info(TAG, "没有找到有效的职位数据");
            return;
        }

        if (jobBean.isMultiAddress()) {//职位存在多地址
            MultiAddressActivity.startForB(activity, String.valueOf(jobBean.id), String.valueOf(jobBean.locationIndex), requestCode);
        } else {
            JobBean cloneJobBean = CloneUtils.cloneObject(jobBean);//里面会修改职位的经纬度
            if (cloneJobBean != null) {
                Intent intent = SelectWorkLocationActivity.createIntent(activity, cloneJobBean, true, "确定", PermissionConstants.SCENE_CHAT_INTERVIEW_LOCATION);
                intent.putExtra("from", SelectWorkLocationActivity.SELECT_WORK_CHAT_SOURCE);
                AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);
            }
        }
    }

    public NLPListBean filterNLPListBean(@NonNull NLPListBean originNLPListBean) {
        List<NLPSuggestBean> nlpSuggestBeanList = originNLPListBean.getNlpList();
        if (LList.isEmpty(nlpSuggestBeanList)) {
            return originNLPListBean;
        }

        boolean isContactEachOther = contactData != null && contactData.isContactEachOther();
        List<NLPSuggestBean> filteredList = new ArrayList<>();
        for (NLPSuggestBean nlpSuggestBean : nlpSuggestBeanList) {
            boolean isTargetNLP = nlpSuggestBean.type == NLPSuggestBean.EXCHANGE_ACCEPT_PHONE
                    || nlpSuggestBean.type == NLPSuggestBean.EXCHANGE_ACCEPT_WECHAT;
            // 非22和23消息，不处理
            if (!isTargetNLP) {
                filteredList.add(nlpSuggestBean);
                continue;
            }

            // 22和23消息，只有双聊后才保留，否则过滤掉
            if (isContactEachOther) {
                filteredList.add(nlpSuggestBean);
            }
        }

        originNLPListBean.setNlpList(filteredList);
        return originNLPListBean;
    }

    public void refreshAiHelperStatusBar(int status) {
        if (iChatCommon != null) {
            iChatCommon.setAIChatHelperView(status);
        }
    }

    private OverdueBossInfoBean overdueBossInfo;

    public void setOverdueBossInfo(OverdueBossInfoBean overdueBossInfo) {
        this.overdueBossInfo = overdueBossInfo;
    }

    private int botherMaxNum = Integer.MAX_VALUE;

    public void setBotherMaxNum(int botherMaxNum) {
        this.botherMaxNum = botherMaxNum;
    }
    public void checkIsBother() {
        if (AccountHelper.isBoss() || contactData == null) return;
        if (!messageData.isEmpty() && messageData.size() < botherMaxNum) return;
        int count = 0;
        synchronized (ADD_MESSAGE_LOCK) {
            for (int i = messageData.size() - 1; i > 0; i--) {
                ChatBean chatBean = LList.getElement(messageData, i);
                //只判断文本
                if (chatBean != null && chatBean.message.messageBody.type == 1 && chatBean.message.messageBody.templateId == 1) {
                    //只判断
                    if (chatBean.fromUserId == contactData.friendId) {
                        count++;
                    }

                    if (chatBean.fromUserId == AccountHelper.getUid()) {
                        return;
                    }
                } else {
                    return;
                }

                if (count == botherMaxNum) {
                    SimpleApiRequest.POST(ChatUrlConfig.URL_ZPCHAT_MESSAGE_SEND)
                            .addParam("securityId", contactData.securityId)
                            .addParam("scene", 1)
                            .setRequestCallback(new SimpleApiRequestCallback<HttpResponse>() {
                                @Override
                                public void onSuccess(ApiData<HttpResponse> data) {
                                    TLog.info(TAG, "bother success");
                                }

                                @Override
                                public void onFailed(ErrorReason reason) {
                                    TLog.info(TAG, "bother failed %s", reason.getErrReason());
                                }
                            })
                            .execute();
                    return;
                }
            }
        }

    }

    private void addWaitReceiveMsg(ChatBean chatBean) {
        if (!DataStarGray.getInstance().isWaitReceiveGray() || chatBean == null) {
            return;
        }

        // C简历撤回会把dialog消息变为文本消息
        if (ExchangeHelper.getInstance().contains(chatBean) && (!ChatUtils.isValidChatDialogMsg(chatBean) || chatBean.message.messageBody.dialog.operated)) {
            ExchangeHelper.getInstance().removeData(chatBean);
        } else {
            ExchangeHelper.getInstance().addData(chatBean);
        }
    }

    public void refreshTopStatusView() {
        if (ExchangeHelper.getInstance().isWaitReceiveStatus(contactData)) {
            iChatCommon.refreshHeadTips();
        }
    }

    public void reportAIHelpChatPanelShow() {
        ChatAnalysisUtil.statsChatAIReplyShow(getFriendId(), getJobId());
    }

    public ContactBean getContactBean() {
        return contactData;
    }

    public interface OnQuoteMessageCallBack {
        void onQuoteMessageListener(@Nullable ChatBean chatBean);
    }

    public OnShowExchangeGuideCallback onShowExchangeGuideCallback;

    public interface OnShowExchangeGuideCallback {
        void onShowExchangeGuide(ExchangeGuideBean exchangeGuideBean);
    }

    public void setOnShowExchangeGuideCallback(OnShowExchangeGuideCallback onShowExchangeGuideCallback) {
        this.onShowExchangeGuideCallback = onShowExchangeGuideCallback;
    }

    public int getAiHelpChatType() {
        return aiHelpChatType;
    }

    public void setAiHelpChatType(int aiHelpChatType) {
        this.aiHelpChatType = aiHelpChatType;
    }

    public List<ChatPendingTaskBean> getPendingTaskAfterLocationPermissionGrant() {
        return pendingTaskAfterLocationPermissionGrant;
    }

    public boolean isIgnoreSetAIHelpChatPanel() {
        return ignoreSetAIHelpChatPanel;
    }

    public void setIgnoreSetAIHelpChatPanel(boolean ignoreSetAIHelpChatPanel) {
        this.ignoreSetAIHelpChatPanel = ignoreSetAIHelpChatPanel;
    }
}
