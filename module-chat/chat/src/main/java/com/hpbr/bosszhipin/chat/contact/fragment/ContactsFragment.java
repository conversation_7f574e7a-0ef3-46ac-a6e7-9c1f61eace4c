package com.hpbr.bosszhipin.chat.contact.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bszp.kernel.account.AccountHelper;
import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.chat.R;
import com.hpbr.bosszhipin.chat.contact.adapter.ContactNewAdapter;
import com.hpbr.bosszhipin.chat.contact.filter.GeekFilterData;
import com.hpbr.bosszhipin.chat.contact.fragment.boss.BossContactsFragment;
import com.hpbr.bosszhipin.chat.contact.fragment.geek.GeekContactsFragment;
import com.hpbr.bosszhipin.chat.contact.listener.ContactListEventListener;
import com.hpbr.bosszhipin.chat.contact.listener.DoubleClickHandlerListener;
import com.hpbr.bosszhipin.chat.contact.listener.ResumedLifecycleOwner;
import com.hpbr.bosszhipin.chat.contact.listener.ViewPagerSelectFragmentListener;
import com.hpbr.bosszhipin.chat.contact.manager.ContactGroupData;
import com.hpbr.bosszhipin.chat.contact.manager.ContactRecyclerViewAnalyticsChangeListener;
import com.hpbr.bosszhipin.chat.contact.manager.F2ContactClassifyHelper;
import com.hpbr.bosszhipin.chat.contact.manager.F2ContactData;
import com.hpbr.bosszhipin.chat.contact.manager.FixScrollTopRecyclerViewObserver;
import com.hpbr.bosszhipin.chat.contact.queryfilter.HelloQueryFilterActivity;
import com.hpbr.bosszhipin.chat.contact.queryfilter.HelloQueryFilterV2Activity;
import com.hpbr.bosszhipin.chat.contact.utils.DoubleClickNavigationManager;
import com.hpbr.bosszhipin.chat.contact.utils.ViewUtils;
import com.hpbr.bosszhipin.chat.contact.view.ContactClassifyTabView;
import com.hpbr.bosszhipin.chat.contact.view.ContactItemAnimator;
import com.hpbr.bosszhipin.chat.export.constant.ChatAnalyticsAction;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactMessageTimeRangeManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.module.contacts.util.ContactUtils;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.recycleview.SafeLinearLayoutManager;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.TimeUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import message.server.SyncMessageManager;


/**
 * 1016.60 聊天优化一期
 */
public class ContactsFragment extends BaseFragment implements DoubleClickHandlerListener, ViewPagerSelectFragmentListener, ContactManager.ContactChangeObserver {
    public static final String TAG = "ContactsFragment";
    public static final String GROUP_TYPE = "classifyItem";

    private final DoubleClickNavigationManager doubleClickNavigationManager = new DoubleClickNavigationManager();
    private final TopItemDecoration topItemDecoration = new TopItemDecoration();
    private final ResumedLifecycleOwner resumedLifecycleOwner = new ResumedLifecycleOwner(getLifecycle());
    private final Map<String, String> extras = new ArrayMap<>(4);
    private final ContactRecyclerViewAnalyticsChangeListener listAnalyticsListener = new ContactRecyclerViewAnalyticsChangeListener(extras);
    private final ContactItemAnimator contactItemAnimator = new ContactItemAnimator();
    private final long asyncLimitCount = AndroidDataStarGray.getInstance().getContactAsyncLimitCount();
    private View rootView;
    private RecyclerView recyclerView;
    private View llTopExpandState;
    protected ContactNewAdapter adapter;
    private LinearLayoutManager linearLayoutManager;
    protected ContactClassifyTabView.ClassifyItem classifyItem;
    private int topExpandViewPosition = -1;
    private BaseViewHolder topExpandStateViewHolder;
    private AppBarLayout appBarLayout;
    private LinearLayout llTopUnReadDismissTip;
    protected ConstraintLayout clInterestGeekTip;
    protected MTextView interestGeekTipText;
    protected ImageView closeInterestGeekTip;
    protected MTextView btnUseRights;

    private boolean isClickUnReadContact = false;
    //是否Diff
    private boolean isIgnoreDiff = false;
    private boolean unReadDismissTipShowed = false;
    // 缓存发生变更的联系人ID集合（使用long类型key，参考ContactCache.dataMaps）
    private final Set<String> changedContactIds = Collections.newSetFromMap(new ConcurrentHashMap<String, Boolean>());


    private WeakReference<F2ContactClassifyHelper> f2ContactClassifyHelperWeakReference;

    private final Runnable dismssUnReadDismissTipRunnable = new Runnable() {
        @Override
        public void run() {
            if (llTopUnReadDismissTip.getVisibility() == View.VISIBLE) {
                llTopUnReadDismissTip.setVisibility(View.GONE);
            }
        }
    };

    private NewGreetingRecommendSort newGreetingRecommendSort;

    public static ContactsFragment newInstance(@NonNull ContactClassifyTabView.ClassifyItem classifyItem) {
        Bundle args = new Bundle();
        args.putSerializable(GROUP_TYPE, classifyItem);
        ContactsFragment fragment;
        if (AccountHelper.isBoss()) {
            fragment = new BossContactsFragment();
        } else if (AccountHelper.isGeek()) {
            fragment = new GeekContactsFragment();
        } else {
            fragment = new ContactsFragment();
        }
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * ViewPager + Fragment LiveData 问题
     *
     * @param savedInstanceState If the fragment is being re-created from
     *                           a previous saved state, this is the state.
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        observeContacts();
        ContactManager.getInstance().observeContact(this, this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (rootView != null) { // 解决 getFootView view#getParent != null  ViewHolder views must not be attached when created. Ensure that you are not passing 'true' to the attachToRoot" parameter of LayoutInflater.inflate(..., boolean attachToRoot)
            return rootView;
        }
        return rootView = inflater.inflate(R.layout.chat_fragment_contact_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            classifyItem = (ContactClassifyTabView.ClassifyItem) arguments.get(GROUP_TYPE);
        }
        TLog.debug(TAG, "onViewCreated = classifyItem#type = %s", classifyItem.groupType);
        //初始化View
        initView(view);
        if (UserManager.isBossRole()) {
            setShowGuideAddWidget();
        }
        extras.put("pagesource", "1");
        extras.put("sort_type", "0");
        extras.put("pagetab", String.valueOf(classifyItem.groupType));
    }

    //初始化View
    private void initView(View view) {
        recyclerView = view.findViewById(R.id.recyclerView);
        llTopExpandState = view.findViewById(R.id.ll_top_expand_state);
        topExpandStateViewHolder = new BaseViewHolder(llTopExpandState);
        llTopUnReadDismissTip = view.findViewById(R.id.ll_top_un_read_dismiss_tip);
        llTopExpandState.setOnClickListener(v -> contactListEventListener.onExpandContact(!F2ContactClassifyHelper.getInstance().isTopExpandState()));
        clInterestGeekTip = view.findViewById(R.id.cl_interest_geek_tip_container);
        interestGeekTipText = view.findViewById(R.id.tv_tip_title);
        closeInterestGeekTip = view.findViewById(R.id.iv_tip_close);
        btnUseRights = view.findViewById(R.id.btn_use);
        if (adapter == null) {
            adapter = new ContactNewAdapter(null, contactListEventListener, new ContactNewAdapter.OnItemClickCallBack() {
                @Override
                public void onItemClickListener(ContactBean contactBean, String id, int childPosition, int unReadCount) {

                    //全部tab并且筛选未读时，点击联系人需要弹出提示文案
                    if (classifyItem.groupType == ContactBean.GroupType.ALL && F2ContactClassifyHelper.getInstance().isSelectUnReadFilter()) {
                        isClickUnReadContact = true;
                    }

                    //系统联系人不处理
                    if (F2ContactHelper.getInstance().isSystemContact(contactBean.friendId)) {
                        return;
                    }

                    GeekFilterData geekFilterData = F2ContactClassifyHelper.getInstance().getGeekFilterData();
                    int classifySortType = getClassifySortType();
                    String p5 = classifySortType > ContactGroupData.NONE_SORT_TYPE ? classifySortType + "" : null;
                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_CONTACT_CLICK)
                            .param("p", id)
                            .param("p2", classifyItem.groupType)
                            .param("p3", F2ContactClassifyHelper.getInstance().getSelectFilter() >= F2ContactClassifyHelper.TYPE_GEEK_MULTI ? 1 : 0)
                            .param("p4", geekFilterData != null ? geekFilterData.uuid : null)
                            .param("p5", p5)
                            .param("p7", childPosition)
                            .param("p8", unReadCount)
                            .paramIfExist("p10", isNewGreetingContactRecommendSort() ? "1" : "2", ContactGroupData.isSupportNewGreetingRecommendSort(classifyItem.groupType))
                            .build();
                }
            });
            adapter.setEmptyView(showEmptyView());
            adapter.addFooterView(getFootView());
            topItemDecoration.setDivider(ContextCompat.getDrawable(activity, R.drawable.chat_contact_top_divider));
            recyclerView.addItemDecoration(topItemDecoration);
            adapter.registerAdapterDataObserver(new FixScrollTopRecyclerViewObserver(recyclerView) {
                @Override
                public void onItemRangeRemoved(int positionStart, int itemCount) {
                    super.onItemRangeRemoved(positionStart, itemCount);
                    adapter.dismissLongPressGuideDialog();
                    if (classifyItem.groupType != 0) {
                        ContactBean item = adapter.getItem(positionStart);
                        if (item != null && item.getGroupType() != classifyItem.groupType) {
                            //这里 post 是为了 解决  从别的页面回来时   isResumed = false
                            recyclerView.postDelayed(() -> {
                                if (isResumed()) {
                                    F2ContactClassifyHelper.getInstance().changeClassifyContacts(Collections.singletonList(item));
                                }
                            }, 300);
                            TLog.debug(TAG, "onItemRangeRemoved = ContactBean#groupType = %s positionStart = %d itemCount = %d", item.getGroupType(), positionStart, itemCount);
                        }
                    }

                    //等联系人消失时，判断是否点击过联系人
                    if (isClickUnReadContact && !unReadDismissTipShowed) {
                        if (!SpManager.get().user().getBoolean(Constants.SP_POP_UNREAD_DISMISS_TIP, false)) {
                            llTopUnReadDismissTip.setVisibility(View.VISIBLE);
                            Utils.runOnUiThreadDelayed(dismssUnReadDismissTipRunnable, 5000);
                            SpManager.get().user().edit().putBoolean(Constants.SP_POP_UNREAD_DISMISS_TIP, true).apply();
                            unReadDismissTipShowed = true;
                        }
                    }
                }
            });
        } else {
            TLog.debug(TAG, "initView = recyclerView = %s adapter = %s count = %s", recyclerView, adapter, LList.getCount(adapter.getData()));
        }

        linearLayoutManager = new SafeLinearLayoutManager(activity);
        contactItemAnimator.setSupportsChangeAnimations(false);
        contactItemAnimator.setMoveDuration(120);
        contactItemAnimator.setChangeDuration(0);
        recyclerView.setItemAnimator(contactItemAnimator);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.addOnScrollListener(onScrollListener);
        recyclerView.addOnScrollListener(listAnalyticsListener); //列表曝光配置
        recyclerView.setHasFixedSize(true);
        recyclerView.setAdapter(adapter);

        appBarLayout = ViewUtils.getAppBarLayout(getView());
        if (appBarLayout != null) {
            appBarLayout.addOnOffsetChangedListener(onOffsetChangedListener);
        }
        //默认进入不会走Scroll回调
        checkTopExpandState();
    }


    private final ContactListEventListener contactListEventListener = new ContactListEventListener() {

        @NonNull
        @Override
        public RecyclerView getRecyclerView() {
            return recyclerView;
        }

        @Override
        public int getClassifyType() {
            return classifyItem.groupType;
        }

        @Override
        public boolean isNewHelloStyle() {
            return isNewGreetingTab();
        }

        @Override
        public void onExpandContact(boolean expandState) {
            isScrollTop = true;
            isIgnoreDiff = true;
            F2ContactClassifyHelper.getInstance().setTopExpandState(expandState);
            ContactManager.getInstance().refreshContacts();
        }

        @Override
        public int getClassifySortType() {
            return ContactsFragment.this.getClassifySortType();
        }

        @Override
        public void onSortSelect(int sortType) {
            extras.put("sort_type", String.valueOf(sortType));
            ContactsFragment.this.onSortSelect(sortType);
        }

        @Override
        public void onHelloQueryFilterClick() {
            AnalyticsFactory.create().action("F2-new-batchprocessing-click").param("p", classifyItem.contactCount).build();
            if (DataStarGray.getInstance().isBossFastHandleGray()) {
                Intent intent = new Intent(activity, HelloQueryFilterV2Activity.class);
                intent.putExtra(Constants.DATA_ENTITY, F2ContactClassifyHelper.getInstance().getBossFilterData());
                AppUtil.startActivity(activity, intent);
            } else {
                AppUtil.startActivity(activity, new Intent(activity, HelloQueryFilterActivity.class));
            }
        }
    };

    protected int getClassifySortType() {
        return newGreetingRecommendSort != null ?
                newGreetingRecommendSort.sortType :
                ContactGroupData.NONE_SORT_TYPE;
    }

    protected void onSortSelect(int sortType) {
        //这里需要刷新tab 未读数量不能用上面的方法 做当前页面刷新
        F2ContactClassifyHelper.getInstance().setIgnoreAnim(true);
        isIgnoreDiff = true;
        if (isNewGreetingTab()) {
            AnalyticsFactory.create().action("f2-filter-rec-click").param("p", sortType).build();
            ToastUtils.showText(sortType == ContactGroupData.RECOMMEND_SORT_TYPE ?
                    "已开启推荐排序" : "已开启时间排序");

            if (newGreetingRecommendSort != null) {
                newGreetingRecommendSort.refreshSortType(sortType, refreshContacts);
            }
            if (sortType == ContactGroupData.TIME_SORT_TYPE) {
                checkTopExpandState();
            } else {
                llTopExpandState.setVisibility(View.GONE);
            }
        }
    }

    private final Runnable refreshContacts = () -> {
        F2ContactClassifyHelper.getInstance().setIgnoreAnim(true);
        ContactManager.getInstance().refreshContacts();
    };


    boolean isScrollTop = false;

    protected void onContactChange() {
        //这里isResumed 放前面，防止 isChangeFilter调用之后重置状态
        //SyncMessageManager.getInstance().isSyncMessage()  如果没有消息同步，一分钟之内都是同步中状态， 如果有消息同步， 则是实时的状态
        Utils.runOnUiThreadDelayed(refreshAdapter, 150);
    }


    final Runnable refreshAdapter = new Runnable() {
        @Override
        public void run() {
            Utils.removeHandlerCallbacks(this);
            boolean isNewData = isIgnoreDiff  //屏蔽动画
                    || !isResumed()  //不可见
                    || F2ContactClassifyHelper.getInstance().isChangeFilter()  //筛选变化
                    || F2ContactClassifyHelper.getInstance().isIgnoreAnim()
                    || SyncMessageManager.getInstance().isMsgSyncing(); // 同步中

            refreshAdapter(isNewData);

            isIgnoreDiff = false; //还原动画配置
        }
    };

    protected void refreshAdapter(boolean isNewData) {
        ContactGroupData contactGroupData = F2ContactClassifyHelper.getInstance().getContactGroupData(classifyItem.groupType);
        if (contactGroupData != null && contactGroupData.contactBeans != null) {
            extras.put("tab_contact_num", String.valueOf(contactGroupData.exposureContactCount));
            TLog.info(TAG, "refreshAdapter groupType = %s contactBeans = %d  isNewData = %b isResume = %b", contactGroupData.groupType, LList.getCount(contactGroupData.contactBeans), isNewData, isResumed());
            List<ContactBean> data = new ArrayList<>();
            //第一：新招呼排序Tab
            setContactSortDataSource(contactGroupData, data);
            //第二：置顶数据
            List<ContactBean> otherDataSource = setContactTopDataSource(contactGroupData, data);
            //第三：添加一周标记分割线
            setContactWeekAgeDataSource(contactGroupData.weekAgeContactDivider, otherDataSource);
            //第四：添加剩余数据
            data.addAll(otherDataSource);

            TLog.debug(TAG, "type  = %d new =  %d  adapterCount = %d", contactGroupData.groupType, LList.getCount(data), LList.getCount(adapter.getData()));
            Set<String> tempChangedContactIds = new HashSet<>(changedContactIds);
            if (isNewData || Math.abs(LList.getCount(data) - LList.getCount(adapter.getData())) > 5) { // 大量联系人部分手机从后台唤起时 出现ANR
                adapter.setNewData(data);
            } else if (AndroidDataStarGray.getInstance().isContactF2AsyncDiff() && LList.getCount(data) > asyncLimitCount) {
                adapter.setAsyncDiffData(data, tempChangedContactIds);
            } else {
                adapter.setNewDiffData(data);
            }
            changedContactIds.removeAll(tempChangedContactIds);

            if (isScrollTop) {
                recyclerView.scrollToPosition(0);
                isScrollTop = false;
            }
            doubleClickNavigationManager.setContactBeans(adapter.getData());
            if (F2ContactClassifyHelper.getInstance().getSelectFilter() == 1 || (classifyItem.redDot <= 0 && classifyItem.noneReadCount <= 0)) { //1、选择未读隐藏提示 2、处理完所有未读的消息
                setHideGuideView();
            }
        } else {
            TLog.error(TAG, "refreshAdapter groupType = %s ", classifyItem.groupType);
            topItemDecoration.setIndex(TopItemDecoration.NONE);
            adapter.setNewDiffData(new ArrayList<>());
        }
    }

    /**
     * 排序数据
     *
     * @param contactGroupData
     * @param data
     */
    private void setContactSortDataSource(ContactGroupData contactGroupData, List<ContactBean> data) {
        handleNewGreetingSort(contactGroupData);
        if (contactGroupData.recommendSortContact != null) {
            extras.put("sort_type", String.valueOf(getClassifySortType()));
            data.add(contactGroupData.recommendSortContact);
        }
    }

    /**
     * 一周之前分割线
     *
     * @param data
     */
    private void setContactWeekAgeDataSource(ContactBean weekAgeContactDivider, List<ContactBean> data) {
        if (weekAgeContactDivider != null) {
            int index = -1;
            for (int i = 0; i < data.size(); i++) {
                ContactBean contactBean = data.get(i);
                if (ContactUtils.isWeekAgeGray(contactBean)) {
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                LList.addElement(data, weekAgeContactDivider, index);
            }
        }
    }

    private List<ContactBean> setContactTopDataSource(ContactGroupData contactGroupData, List<ContactBean> data) {
        ArrayList<ContactBean> contactBeans = new ArrayList<>(contactGroupData.contactBeans);
        //新招呼推荐排序 忽略置顶数据
        if (LList.isEmpty(contactGroupData.contactTops) || isNewGreetingContactRecommendSort()) {
            topItemDecoration.setIndex(TopItemDecoration.NONE);
            topExpandViewPosition = -1;
            return contactBeans;
        }
        //置顶联系人分割线
        if (contactGroupData.topExpandContactDivider != null) {
            if (F2ContactClassifyHelper.getInstance().isTopExpandState()) { //展开 放在置顶之后
                data.addAll(contactGroupData.contactTops);
            } else { //收缩 放在未读之后
                data.addAll(contactGroupData.contactUnReadTops);
            }
            //置顶联系人分割线分割线位置
            topExpandViewPosition = data.size();
            data.add(contactGroupData.topExpandContactDivider);
            topItemDecoration.setIndex(TopItemDecoration.NONE);
        } else {
            data.addAll(contactGroupData.contactTops);
            topItemDecoration.setIndex(data.size());
            topExpandViewPosition = -1;
        }
        contactBeans.removeAll(contactGroupData.contactTops);
        return contactBeans;
    }

    View emptyView;

    private View showEmptyView() {
        if (emptyView == null) {
            emptyView = createEmptyView();
        }
        return emptyView;
    }

    protected View createEmptyView() {
        return LayoutInflater.from(activity).inflate(R.layout.chat_view_contacts_new_none_data, null);
    }

    //底部View
    private View footView;

    protected View getFootView() {
        if (footView == null) {
            footView = LayoutInflater.from(activity).inflate(R.layout.view_contact_foot, null);
            MTextView mTextView = footView.findViewById(R.id.tv_foot);
            mTextView.setText(ContactMessageTimeRangeManager.getInstance().getContactTimeRangeRecord());
        }
        return footView;
    }

    private final Observer<List<ContactBean>> observer = contactBeans -> onContactChange();
    protected void observeContacts() {
        if (AndroidDataStarGray.getInstance().isContactF2Async()) { //是否开启异步
            F2ContactClassifyHelper.getInstance().observeContacts(resumedLifecycleOwner, groupDataMap -> onContactChange());
        } else {
            ContactManager.getInstance().observeContacts(resumedLifecycleOwner, observer);
        }
        f2ContactClassifyHelperWeakReference = new WeakReference<>(F2ContactClassifyHelper.getInstance());
    }

    @Override
    public void onResume() {
        super.onResume();
        TLog.debug(TAG, "onResume = classifyItem#type = %s", classifyItem.groupType);
        if (AndroidDataStarGray.getInstance().isContactF2Async()) {
            F2ContactClassifyHelper helperRef = f2ContactClassifyHelperWeakReference != null ? f2ContactClassifyHelperWeakReference.get() : null;
            F2ContactClassifyHelper helperInstance = F2ContactClassifyHelper.getInstance();
            if (helperRef == null || helperRef != helperInstance) {
                observeContacts();
                ApmAnalyzer.create().action("f2_not_refresh").report();
                TLog.debug(TAG, "f2_not_refresh");
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        TLog.debug(TAG, "onPause = classifyItem#type = %s", classifyItem.groupType);
    }

    @Override
    public void onDestroyView() {
        if (recyclerView != null) {
            recyclerView.removeOnScrollListener(onScrollListener);
        }
        if (appBarLayout != null) {
            appBarLayout.removeOnOffsetChangedListener(onOffsetChangedListener);
        }
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Utils.removeHandlerCallbacks(dismssUnReadDismissTipRunnable);
        if (newGreetingRecommendSort != null) {
            newGreetingRecommendSort = null;
        }
    }

    @Override
    public boolean onSingleClick() {
        return false;
    }


    @Override
    public boolean onDoubleClick() {
        //双击导航栏
        int index = doubleClickNavigationManager.handle();
        TLog.debug(TAG, "onDoubleClick = %d", index);
        if (index > -1) { // 有未读数
//            recyclerView.smoothScrollToPosition(index);
            linearLayoutManager.scrollToPositionWithOffset(index, 0);
            return true;
        } else if (recyclerView.canScrollVertically(-1)) { // 是否置顶
            recyclerView.smoothScrollToPosition(0);
            return true;
        }
        return false;
    }

    final RecyclerView.OnScrollListener onScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
        }

        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            checkTopExpandState();
        }

    };

    private boolean isViewCompletelyVisible(View view) {
        Rect rect = new Rect();
        boolean isVisible = view.getLocalVisibleRect(rect);
        return isVisible && rect.width() == view.getWidth() && rect.height() == view.getHeight();
    }

    /**
     * 1205.92【BC】新版聊天部分用户未读勾选策略优化
     *
     * @param runnable
     */
    @SuppressLint("twl_postdelay")
    void setShowGuideView(final Runnable runnable) {
        if (F2ContactData.isShowUnreadListGray()) {
            return;
        }
        int f2ContactUnreadLimit = CommonConfigManager.getInstance().isF2ContactUnreadLimit();
        if (f2ContactUnreadLimit < 0 || classifyItem.noneReadContactCount < f2ContactUnreadLimit) {
            return;
        }
        long newContactGuideUnreadTime = SpManager.get().user().getLong("new_contact_guide_unread_time", 0);
        if (TimeUtils.isToday(newContactGuideUnreadTime)) { //同一天不走
            return;
        }
        if (rootView == null) return;
        View llGuileUnread = find(rootView, R.id.ll_guile_unread);
        if (llGuileUnread != null) {
            if (F2ContactClassifyHelper.getInstance().getSelectFilter() == 1) { //已经勾选了未读
                llGuileUnread.setVisibility(View.GONE);
                return;
            }
            if (llGuileUnread.getVisibility() != View.VISIBLE) {

                //延时显示，因为列表还未展示出来
                llGuileUnread.postDelayed(() -> {
                    hideGuideAddWidget();
                    llGuileUnread.setVisibility(View.VISIBLE);
                }, 200L);
                llGuileUnread.findViewById(R.id.ic_close).setOnClickListener(v -> {
                    llGuileUnread.setVisibility(View.GONE);
                    setShowGuideAddWidget();
                    SpManager.get().user().edit().putLong("new_contact_guide_unread_time", System.currentTimeMillis()).apply();
                });
                TextView tv_title = llGuileUnread.findViewById(R.id.tv_title);

                if (classifyItem.noneReadCount > 0) {
                    tv_title.setText(String.format("有超过%s条未读消息，点击可快速查看", classifyItem.noneReadCount));
                } else {
                    tv_title.setText("有较多未读消息，点击可快速查看");
                }

                llGuileUnread.findViewById(R.id.tv_click).setOnClickListener(v -> {
                    llGuileUnread.setVisibility(View.GONE);
                    setShowGuideAddWidget();
                    if (runnable != null) runnable.run();
                });
                llGuileUnread.postDelayed(() -> {
                    llGuileUnread.setVisibility(View.GONE);
                    setShowGuideAddWidget();
                }, 6000L);
            }
        }
    }

    void setHideGuideView() {
        if (rootView == null) return;
        View llGuileUnread = find(rootView, R.id.ll_guile_unread);
        if (llGuileUnread != null && llGuileUnread.getVisibility() != View.GONE) {
            llGuileUnread.setVisibility(View.GONE);
        }
    }

    void setShowGuideAddWidget() {
        if (rootView == null || classifyItem.groupType != 0) return;
        View llGuideAddWidget = find(rootView, R.id.ll_guile_add_widget);
        View llGuileUnread = find(rootView, R.id.ll_guile_unread);
        long showGuideAddWidgetTime = SpManager.get().user().getLong("showGuideAddWidgetTime", 0);
        // 获取后台配置的间隔天数
        int intervalDays = 1; // 默认1天
        if (CommonConfigManager.getInstance().getWidgetInfo() != null &&
            CommonConfigManager.getInstance().getWidgetInfo().chatFreq != null) {
            intervalDays = CommonConfigManager.getInstance().getWidgetInfo().chatFreq.interval;
            // 如果间隔天数为0，表示不展示
            if (intervalDays == 0) {
                return;
            }
            if (intervalDays < 0) {
                intervalDays = 1; // 负值使用保底值
            }
        }
        if (llGuideAddWidget != null
                && llGuileUnread.getVisibility() == View.GONE
                && DataStarGray.getInstance().widgetGuideTipSwitch()
                && !SingleRouter.isAddChatWidget()
                && !DateUtil.isWithinXDays(showGuideAddWidgetTime, intervalDays)) {
            llGuideAddWidget.setVisibility(View.VISIBLE);
            llGuideAddWidget.findViewById(R.id.ic_close).setOnClickListener(v -> {
                SpManager.get().user().edit().putLong("showGuideAddWidgetTime", System.currentTimeMillis()).apply();
                llGuideAddWidget.setVisibility(View.GONE);
            });
            llGuideAddWidget.findViewById(R.id.tv_click).setOnClickListener(v -> {
                SingleRouter.showAddWidgetDialog(1);
            });

            if (!TextUtils.isEmpty(CommonConfigManager.getInstance().getComponentText())) {
               TextView tv_title = llGuideAddWidget.findViewById(R.id.tv_title);
               tv_title.setText(CommonConfigManager.getInstance().getComponentText());
            }

            AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_WIDGET_GUIDE_TIP_SHOW)
                    .param("p2", "1")
                    .debug()
                    .build();
        }
    }

    void hideGuideAddWidget() {
        if (rootView == null) return;
        View llGuideAddWidget = find(rootView, R.id.ll_guile_add_widget);
        if (llGuideAddWidget != null && llGuideAddWidget.getVisibility() == View.VISIBLE) {
            llGuideAddWidget.setVisibility(View.GONE);
        }
    }

    /**
     * 1、列表初始化时，需要调用
     * 2、RecyclerView 滑动时调用
     * 3、AppBarLayout 滑动时调用
     */
    private void checkTopExpandState() {
        ContactGroupData contactGroupData = F2ContactClassifyHelper.getInstance().getContactGroupData(classifyItem.groupType);
        if (contactGroupData == null) return;
        if (topExpandViewPosition < 0) {
            llTopExpandState.setVisibility(View.GONE);
            return;
        }
        int firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
        int lastVisibleItemPosition = linearLayoutManager.findLastCompletelyVisibleItemPosition();
        if (firstVisibleItemPosition > topExpandViewPosition) {
            llTopExpandState.setVisibility(View.GONE);
            return;
        }
//            TLog.debug(TAG,"firstVisibleItemPosition= %d topExpandViewPosition = %d lastVisibleItemPosition = %d",firstVisibleItemPosition,topExpandViewPosition,lastVisibleItemPosition);
        boolean isItemCompletelyVisible = topExpandViewPosition >= firstVisibleItemPosition && topExpandViewPosition <= lastVisibleItemPosition;
        if (topExpandViewPosition == lastVisibleItemPosition) { // CoordinatorLayout   AppBarLayout  导致 findLastCompletelyVisibleItemPosition 是否显示完全异常
            View viewByPosition = linearLayoutManager.findViewByPosition(topExpandViewPosition);
            if (viewByPosition != null) {
                isItemCompletelyVisible = isViewCompletelyVisible(viewByPosition);
            }
        }
        if (isItemCompletelyVisible) {
            llTopExpandState.setVisibility(View.GONE);
        } else {
            llTopExpandState.setVisibility(View.VISIBLE);
            if (F2ContactClassifyHelper.getInstance().isTopExpandState()) {
                topExpandStateViewHolder.setGone(R.id.img_top_arrow, true);
                topExpandStateViewHolder.setText(R.id.tv_title, "折叠置顶聊天");
                topExpandStateViewHolder.setImageResource(R.id.img_top_arrow, R.mipmap.ic_arrow_collapse);
            } else {
                int count = LList.getCount(contactGroupData.contactTops) - LList.getCount(contactGroupData.contactUnReadTops);
                topExpandStateViewHolder.setGone(R.id.img_top_arrow, count > 0);
                if (count > 0) {
                    topExpandStateViewHolder.setText(R.id.tv_title, count + "个置顶聊天");
                    topExpandStateViewHolder.setImageResource(R.id.img_top_arrow, R.mipmap.ic_arrow_expand);
                } else {
                    topExpandStateViewHolder.setText(R.id.tv_title, "置顶聊天");
                }
            }
        }
    }

    final AppBarLayout.BaseOnOffsetChangedListener<AppBarLayout> onOffsetChangedListener = new AppBarLayout.BaseOnOffsetChangedListener<AppBarLayout>() {
        @Override
        public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
            checkTopExpandState();
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) llTopExpandState.getLayoutParams();
            if (llTopExpandState.getVisibility() == View.VISIBLE) { //这里不能屏蔽 bottomMargin 一致时不更新 导致 UI 不显示
                layoutParams.bottomMargin = verticalOffset + appBarLayout.getTotalScrollRange();
                llTopExpandState.requestLayout();
            }
        }
    };

    @Override
    public void onSelectFragment(boolean isSelect) {
        if (isAdded()) {
            //当用户手动滑动或者点击选择的时候，需要重置忽略动画
            if (isSelect) {
                isIgnoreDiff = true;
            }
            //是否已经选中了新招呼的推荐排序
            if (isSelect && isNewGreetingContactRecommendSort()) { //点击刷新排序规则
                refreshNewGreetingData(refreshContacts);
            }
//            TLog.debug(TAG, "onSelectFragment = [%d] %b",classifyItem.groupType, isSelect);
        }
    }

    @Override
    public void onContactChange(ContactBean contact) {
        if (contact != null) {
            changedContactIds.add(contact.friendId + "_" + contact.friendSource);
        }
    }

    class TopItemDecoration extends RecyclerView.ItemDecoration {
        public static final int NONE = -1;
        private int index = NONE;

        private final Rect mBounds = new Rect();
        private Drawable mDivider;


        public void setDivider(Drawable mDivider) {
            this.mDivider = mDivider;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        @Override
        public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
            drawVertical(c, parent);
        }


        private void drawVertical(Canvas canvas, RecyclerView parent) {
            canvas.save();
            final int left;
            final int right;
            //noinspection AndroidLintNewApi - NewApi lint fails to handle overrides.
            if (parent.getClipToPadding()) {
                left = parent.getPaddingLeft();
                right = parent.getWidth() - parent.getPaddingRight();
                canvas.clipRect(left, parent.getPaddingTop(), right,
                        parent.getHeight() - parent.getPaddingBottom());
            } else {
                left = 0;
                right = parent.getWidth();
            }

            final int childCount = parent.getChildCount();
            for (int i = 0; i < childCount; i++) {
                if (index == i && mDivider != null) {
                    final View child = parent.getChildAt(i);
                    parent.getDecoratedBoundsWithMargins(child, mBounds);
                    mDivider.setBounds(left, 0, right, mBounds.bottom);
                    mDivider.draw(canvas);
                }
            }
            canvas.restore();
        }

        @Override
        public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
            if (mDivider == null) {
                return;
            }
            int childAdapterPosition = parent.getChildAdapterPosition(view);
            if (childAdapterPosition == index && NONE != index) {
                outRect.set(0, mDivider.getIntrinsicHeight(), 0, 0);
            } else {
                outRect.set(0, 0, 0, 0);
            }
        }
    }

    /**
     * 处理新招呼数据排序
     *
     * @param contactGroupData 联系人分组数据
     */
    private void handleNewGreetingSort(ContactGroupData contactGroupData) {
        if (!isNewGreetingTab() || contactGroupData == null || !contactGroupData.hasData()) {
            return;
        }

        if (ContactGroupData.isSupportNewGreetingRecommendSort(classifyItem.groupType)) {
            if (newGreetingRecommendSort == null) {
                newGreetingRecommendSort = new NewGreetingRecommendSort(classifyItem.groupType);
                refreshNewGreetingData(refreshContacts);
            }
            newGreetingRecommendSort.recommendSortData(contactGroupData);
            contactGroupData.recommendSortContact = newGreetingRecommendSort.getRecommendSortContact();
        }
    }

    /**
     * 判断当前是否为新招呼tab
     */
    protected boolean isNewGreetingTab() {
        return classifyItem != null && classifyItem.groupType == ContactBean.GroupType.NEW_GREETING;
    }

    /**
     * 刷新新招呼数据
     */
    public void refreshNewGreetingData(Runnable runnable) {
        if (newGreetingRecommendSort != null) {
            newGreetingRecommendSort.requestRecommendData(runnable);
        }
    }

    /**
     * 判断是否为新招呼推荐排序模式
     */
    public boolean isNewGreetingContactRecommendSort() {
        if (ContactGroupData.isSupportNewGreetingRecommendSort(classifyItem.groupType)) {
            if (newGreetingRecommendSort != null) {
                return newGreetingRecommendSort.sortType == ContactGroupData.RECOMMEND_SORT_TYPE;
            }
            ContactGroupData contactGroupData = F2ContactClassifyHelper.getInstance().getContactGroupData(classifyItem.groupType);
            if (contactGroupData == null) {
                return CommonConfigManager.getInstance().getGreetingRecSort() == ContactGroupData.RECOMMEND_SORT_TYPE;
            }
        }
        return false;
    }
}
