package com.hpbr.bosszhipin;

import com.hpbr.bosszhipin.config.URLConfig;

public class ChatUrlConfig extends URLConfig {

    /**
     * 1125.604 羚羊群下发文件消息
     * https://api.weizhipin.com/project/30/interface/api/648247
     */
    public static final String URL_GROUP_CHAT_SEND_FILE_MSG = buildUrl("zpchat/group/antelope/sendFileMsg");
    /**
     * 1125.604 羚羊群下发文件消息更新
     * https://api.weizhipin.com/project/30/interface/api/648275
     */
    public static final String URL_GROUP_CHAT_UPDATE_FILE_MSG = buildUrl("zpchat/group/antelope/updateFileMsg");

    /**
     * 简历辅导群下发文件消息
     * https://api.weizhipin.com/project/30/interface/api/669051
     */
    public static final String URL_GROUP_RESUME_SEND_FILE_MSG = buildUrl("zpchat/group/resumeTutored/sendFileMsg");

    /**
     * 简历辅导群下发文件消息更新
     * https://api.weizhipin.com/project/30/interface/api/669058
     */
    public static final String URL_GROUP_RESUME_UPDATE_FILE_MSG = buildUrl("zpchat/group/resumeTutored/updateFileMsg");

    /**
     * 1120.603 结束羚羊计划群聊
     * https://api.weizhipin.com/project/30/interface/api/629594
     */
    public static final String URL_CHAT_GROUP_ANTE_FINISH_COURSE = buildUrl("zpchat/group/antelope/finishCourse");

    /**
     * 简历辅导群结束咨询
     * https://api.weizhipin.com/project/30/interface/api/669044
     */
    public static final String URL_GROUP_RESUME_FINISH_COURSE = buildUrl("zpchat/group/resumeTutored/finish");

    /**
     * 简历辅导群申请结束
     * https://api.weizhipin.com/project/30/interface/api/669037
     */
    public static final String URL_GROUP_RESUME_BOSS_APPLY_FINISH = buildUrl("zpchat/group/resumeTutored/applyForFinish");

    public static final String URL_ZPCHAT_RESUME_GET_INQUIRE_INFO = buildUrl("zpchat/resumeTutored/getInquiryInfos");
    public static final String URL_ZPCHAT_RESUME_UPDATE_GREETING_STATUS = buildUrl("zpchat/resumeTutored/updateGreetingStatus");
    public static final String URL_ZPCHAT_RESUME_UPDATE = buildUrl("zpchat/resumeTutored/updateInquiry");
    public static final String URL_ZPCHAT_RESUME_SAVE = buildUrl("zpchat/resumeTutored/saveInquiry");
    public static final String URL_ZPCHAT_RESUME_DELETE = buildUrl("zpchat/resumeTutored/delInquiry");
    public static final String URL_ZPCHAT_RESUME_RESORTE = buildUrl("zpchat/resumeTutored/resortInquiry");

    /**
     * 查询快捷问询
     * https://api.weizhipin.com/project/30/interface/api/627962
     */
    public static final String URL_ZPCHAT_ANTELOPE_GET_INQUIRE_INFO = buildUrl("zpchat/antelope/getInquiryInfos");
    /**
     * 修改快捷问询默认开聊语状态
     * https://api.weizhipin.com/project/30/interface/api/627980
     */
    public static final String URL_ZPCHAT_ANTELOPE_UPDATE_GREETING_STATUS = buildUrl("zpchat/antelope/updateGreetingStatus");
    /**
     * 修改快捷问询
     * https://api.weizhipin.com/project/30/interface/api/627944
     */
    public static final String URL_ZPCHAT_ANTELOPE_UPDATE = buildUrl("zpchat/antelope/updateInquiry");
    /**
     * 添加快捷问询
     * https://api.weizhipin.com/project/30/interface/api/627935
     */
    public static final String URL_ZPCHAT_ANTELOPE_SAVE = buildUrl("zpchat/antelope/saveInquiry");

    /**
     * 删除快捷问询
     * https://api.weizhipin.com/project/30/interface/api/627953
     */
    public static final String URL_ZPCHAT_ANTELOPE_DELETE = buildUrl("zpchat/antelope/delInquiry");
    /**
     * 快捷问询排序
     * https://api.weizhipin.com/project/30/interface/api/628574
     */
    public static final String URL_ZPCHAT_ANTELOPE_RESORTE = buildUrl("zpchat/antelope/resortInquiry");

    /**
     * 新招呼页面展示批量处理
     * https://api.weizhipin.com/project/30/interface/api/469465
     */
    public static final String URL_BOSS_BATCH_PROCESS_SHOW = buildUrl("zpchat/session/batchProcess/show");

    /**
     * 开场问题列表v3
     * https://api.weizhipin.com/project/30/interface/api/616709
     */
    public static final String URL_BOSS_QUESTION_GET_MAPPING_V3 = buildUrl("zpchat/greeting/job/question/getMappingV3");

    /**
     * 添加职位问题页面列表
     * https://api.weizhipin.com/project/30/interface/api/616865
     */
    public static final String URL_BOSS_QUESTION_JOB_WITH_QUESTION = buildUrl("zpchat/greeting/job/question/jobWithQuestion");

    /**
     * boss 把职位绑定上某个问题v2
     * https://api.weizhipin.com/project/30/interface/api/616868
     */
    public static final String URL_BOSS_QUESTION_BIND_V2 = buildUrl("zpchat/greeting/job/question/bindV2");

    /**
     * boss配置职位问题前检查
     * https://api.weizhipin.com/project/30/interface/api/616931
     */
    public static final String URL_BOSS_QUESTION_CHECK_BIND = buildUrl("zpchat/greeting/job/question/checkBind");

    /**
     * 查询拉黑好友列表
     * https://api.weizhipin.com/project/30/interface/api/233118
     */
    public static final String URL_USER_BLACK_LIST = buildUrl("zprelation/userBlack/getBlackList");

    /**
     * 1016.110 boss沟通过列表
     * https://api.weizhipin.com/project/30/interface/api/453408
     */
    public static final String URL_BOSS_GET_CONTACT_GEEK_LIST = buildUrl("zprelation/bossTag/contactList");

    /**
     * 1016.110 boss沟通过筛选条件
     * https://api.weizhipin.com/project/30/interface/api/452561
     */
    public static final String URL_BOSS_GET_CONTACT_FILTERS = buildUrl("zprelation/bossTag/contactFilter");

    /**
     * 1016.110 boss 沟通过搜索页面
     * https://api.weizhipin.com/project/30/interface/api/452631
     */
    public static final String URL_BOSS_GET_CONTACT_SEARCH_SUGGEST = buildUrl("zprelation/bossTag/contactSearchGeek");

    /**
     * 1101.111 收藏列表
     * https://api.weizhipin.com/project/30/interface/api/507013
     */
    public static final String URL_BOSS_GET_COLLECTION_GEEK_LIST = buildUrl("zprelation/bossTag/interestedList");

    /**
     * 1101.111 boss收藏筛选条件
     * https://api.weizhipin.com/project/30/interface/api/507003
     */
    public static final String URL_BOSS_GET_COLLECTION_FILTERS = buildUrl("zprelation/bossTag/interestedFilter");

    /**
     * 1101.111 boss收藏搜索页面
     * https://api.weizhipin.com/project/30/interface/api/507002
     */
    public static final String URL_BOSS_GET_COLLECTION_SEARCH_SUGGEST = buildUrl("zprelation/bossTag/interestedSearch");

    /**
     * Boss获得牛人【感兴趣】【看过我】【新牛人】
     */
    public static final String URL_BOSS_GET_GEEK = buildUrl("zprelation/interaction/bossGetGeek");

    /**
     * Boss获取感兴趣/沟通过的牛人
     */
    public static final String URL_BOSS_GET_GEEKLIST_BY_GEEKTAG = buildUrl("zprelation/bossTag/bossGetGeek");

    /**
     * Boss在自己的职位详情中查看沟通过和查看过的牛人
     */
    public static final String URL_BOSS_GET_GEEKLIST_BY_JOBTAG = buildUrl("zprelation/bossTag/jobTag/bossGetGeek");

    /**
     * 904 F2推荐相似牛人
     * http://api.kanzhun-inc.com/project/30/interface/api/167057
     */
    public static final String URL_GET_INTERACT_SIMILAR_GEEK_LIST = buildUrl("zprelation/interaction/geekSimilar");


    /**
     * 用户设置开关详情说明
     * https://api.weizhipin.com/project/30/interface/api/219472
     */
    public static final String URL_ZP_CHAT_NOTIFY_SETTING_TIPS = buildUrl("zpchat/notify/setting/tips");

    /**
     * 706 获取打招呼语的问题列表
     * https://api.weizhipin.com/project/30/interface/api/219472
     */
    public static final String URL_BOSS_GREETING_QUESTION_LIST = buildUrl("zpchat/greeting/question/list");

    /**
     * 706 添加或者更新招呼语问题
     * https://api.weizhipin.com/project/30/interface/api/219472
     */
    public static final String URL_BOSS_GREETING_QUESTION_UPDATE = buildUrl("zpchat/greeting/question/save");
    /**
     * 706 删除招呼语回复
     * https://api.weizhipin.com/project/30/interface/api/219472
     */
    public static final String URL_BOSS_GREETING_QUESTION_REPLY_DELETE = buildUrl("zpchat/greeting/question/remove");
    /**
     * 706 获取未设置问答的职位列表
     * https://api.weizhipin.com/project/30/interface/api/219472
     */
    public static final String URL_BOSS_GREETING_QUESTION_UNANSWER_JOB_LIST = buildUrl("zpchat/greeting/question/getJobList");

    /**
     * 获取聊天列表弹窗
     * <a href="https://api.weizhipin.com/project/30/interface/api/501307">...</a>
     */
    public static final String URL_CHAT_SESSION_GET_WINDOW_CONFIG = buildUrl("zpchat/session/getWindowConfig");

    public static final String URL_ZPRELATION_FRIEND_GEEK_ADD = buildUrl("zprelation/friend/geek/add");
    public static final String URL_ZPRELATION_FRIEND_BOSS_ADD = buildUrl("zprelation/friend/boss/add");

    /**
     * 曝光聊天列表弹窗
     * <a href="https://api.weizhipin.com/project/30/interface/api/504226">...</a>
     */
    public static final String URL_CHAT_SESSION_EXPOSE_WINDOW = buildUrl("zpchat/session/exposeWindow");
    /**
     * 点击聊天列表弹窗
     * <a href="https://api.weizhipin.com/project/30/interface/api/501319">...</a>
     */
    public static final String URL_CHAT_SESSION_UPDATE_WINDOW_CONFIG = buildUrl("zpchat/session/updateWindowConfig");

    /**
     * 同步新招呼牛人信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/534628">...</a>
     */
    public static final String URL_ZPRELATION_FRIEND_SYNC_GEEKINFO = buildUrl("zprelation/friend/syncGeekInfo");


    /**
     * 牛人点击Boss好友进入聊天界面
     * <a href="https://api.weizhipin.com/project/30/interface/api/113151">...</a>
     */
    public static final String URL_ZPCHAT_GEEK_ENTER = buildUrl("zpchat/session/geekEnter");

    /**
     * 新招呼快速处理排序接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/631319">...</a>
     */
    public static final String URL_ZPRELATION_BOSS_FRIEND_GREET_SORT_LIST = buildUrl("zprelation/friend/bossRecSortList");
    public static final String URL_ZPRELATION_GEEK_FRIEND_GREET_SORT_LIST = buildUrl("zprelation/friend/geekGreetRecSortList");
    /**
     * https://api.weizhipin.com/project/30/interface/api/736792
     */
    public static final String URL_ZPRELATION_GEEK_FRIEND_SORT_LIST = buildUrl("zprelation/friend/getRecSortList");

    /**
     * 创建音视频通话
     * <a href="https://api.weizhipin.com/project/30/interface/api/629327">...</a>
     */
    public static final String URL_CHAT_GROUP_CREATE_ROOM = buildUrl("zpchat/group/media/createRoom");

    /**
     * 音视频获取签名信息获取签名
     * <a href="https://api.weizhipin.com/project/30/interface/api/629330">...</a>
     */
    public static final String URL_CHAT_GROUP_GET_SIGNATURE_INFO = buildUrl("zpchat/group/media/getSignatureInfo");

    /**
     * 获取音视频间信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/629333">...</a>
     */
    public static final String URL_CHAT_GROUP_GET_ROOM_INFO = buildUrl("zpchat/group/media/getRoomInfo");

    /**
     * 获取音视频呼叫信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/629336">...</a>
     */
    public static final String URL_CHAT_GROUP_GET_CALL_INFO = buildUrl("zpchat/group/media/getCallInfo");

    /**
     * 音视频间状态同步
     * <a href="https://api.weizhipin.com/project/30/interface/api/629342">...</a>
     */
    public static final String URL_CHAT_GROUP_MEDIA_SYNC_STATUS = buildUrl("zpchat/group/media/syncStatus");
    /**
     * 1122.91【B】触发境外提示后解除提示流程优化
     */
    public static final String URL_CHAT_GEO_VALIDATE = buildUrl("zpchat/geo/validate");
    /**
     * 获取新招呼牛人筛选项
     * <a href="https://api.weizhipin.com/project/30/interface/api/663241">...</a>
     */
    public static final String URL_CHAT_FRIEND_FILTER_OPTION = buildUrl("zprelation/friend/getGeekFilterOptions");
    /**
     * 获取新招呼牛人筛选信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/663031">...</a>
     */
    public static final String URL_CHAT_FRIEND_FILTER_INFO = buildUrl("zprelation/friend/getGeekFilterInfos");


    /**
     * 牛人屏蔽或解除屏蔽智能助手
     * <a href="https://api.weizhipin.com/project/30/interface/api/663759">...</a>
     */
    public static final String URL_CHAT_HELPER_BLOCK_OR_RELEASE = buildUrl("zpchat/chatHelper/blockOrRelease");
    /**
     * 获取模型推荐问题
     * <a href="https://api.weizhipin.com/project/30/interface/api/724738">...</a>
     */
    public static final String URL_CHAT_HELPER_GET_RECOMMEND_QAS = buildUrl("zpchat/chatHelper/getRecommendQAs");

    /**
     * Boss修改智能助手状态
     * <a href="https://api.weizhipin.com/project/30/interface/api/663598">...</a>
     */
    public static final String URL_CHAT_HELPER_MODIFY_STATUS = buildUrl("zpchat/chatHelper/modifyStatus");

    /**
     * 助手消息反馈提交
     * <a href="https://api.weizhipin.com/project/30/interface/api/667357">...</a>
     */
    public static final String URL_CHAT_FEED_BACK_SUBMIT = buildUrl("zpchat/chatHelper/feedback/submit");

    /**
     * 助手消息反馈弹窗
     * <a href="https://api.weizhipin.com/project/30/interface/api/663591">...</a>
     */
    public static final String URL_CHAT_HELPER_FEED_BACK = buildUrl("zpchat/chatHelper/feedback/alertInfo");

    /**
     * 驻外职位-BOSS发送口语测试消息邀请
     * <a href="https://api.weizhipin.com/project/30/interface/api/662716">...</a>
     */
    public static final String URL_BOSS_GET_SEND_SPECK_TEST_INVITE = buildUrl("zpchat/foreign/sendSpeakTestInvite");

    /**
     * 驻外职位-BOSS获取职位要求语种列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/662919">...</a>
     */
    public static final String URL_BOSS_GET_LANGUAGE_LIST = buildUrl("zpchat/foreign/getLanguageList");

    /**
     * 追聊气泡换一换
     * <a href="https://api.weizhipin.com/project/30/interface/api/647155">...</a>
     */
    public static final String URL_ZPCHAT_FAST_REPLY_KEEP_TALKING_CHANGE = buildUrl("zpchat/fastreply/keepTalking/change");

    /**
     * B端招聘助手V1.0-F3新招呼筛选 获取牛人信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/648443">...</a>
     */
    public static final String URL_FRIEND_ROBOT_FILTER_GET_RECORD_INFOS = buildUrl("zprelation/friend/greetRobotFilter/getGeekInfos");

    /**
     * B端招聘助手V1.0-F3新招呼筛选 提交任务V2
     * <a href="https://api.weizhipin.com/project/30/interface/api/673930">...</a>
     */
    public static final String URL_FRIEND_ROBOT_FILTER_SUBMIT_V2 = buildUrl("zprelation/friend/greetRobotFilter/submitV2");

    /**
     * boss进入banner 2s后关闭行为上报
     * <a href="https://api.weizhipin.com/project/30/interface/api/621023">...</a>
     */
    public static final String URL_ZPCHAT_REPORT_CLOSE_BANNER = buildUrl("zpchat/greeting/job/question/reportCloseBanner");

    /**
     * boss 关闭牛人自问自答功能
     * <a href="https://api.weizhipin.com/project/30/interface/api/617462">...</a>
     */
    public static final String URL_ZPCHAT_GREETING_JOB_CLOSE_GEEK_SELF_ANSWER = buildUrl("zpchat/greeting/job/question/closeGeekSelfAnswer");

    /**
     * 获取boss 可关联职位列表V2
     * <a href="https://api.weizhipin.com/project/30/interface/api/616724">...</a>
     */
    public static final String URL_ZPCHAT_GREETING_JOB_JOBLIST_V2 = buildUrl("zpchat/greeting/job/question/jobListV2");

    /**
     * 删除自定义问题
     * <a href="https://api.weizhipin.com/project/30/interface/api/616874">...</a>
     */
    public static final String URL_ZPCHAT_CHAT_CUSTOM_DELETE = buildUrl("zpchat/greeting/job/question/custom/delete");

    /**
     * boss更新自定义职位问题前检查
     * <a href="https://api.weizhipin.com/project/30/interface/api/617444">...</a>
     */
    public static final String URL_ZPCHAT_CHAT_CUSTOM_CHECK_UPDATE = buildUrl("zpchat/greeting/job/question/custom/checkUpdate");
    /**
     * 添加更新自定义问题
     * <a href="https://api.weizhipin.com/project/30/interface/api/616871">...</a>
     */
    public static final String URL_ZPCHAT_CHAT_CUSTOM_ADD_OR_UPDATE = buildUrl("zpchat/greeting/job/question/custom/addOrUpdate");

    /**
     * 消息问号点击提示弹窗
     * <a href="https://api.weizhipin.com/project/30/interface/api/616643">...</a>
     */
    public static final String URL_ZPCHAT_CHAT_GREETING_JOB_QUESTION_NOTICE = buildUrl("zpchat/greeting/job/question/notice");

    /**
     * 牛人点击气泡给boss发快捷回复卡片
     * <a href="https://api.weizhipin.com/project/30/interface/api/586482">...</a>
     */
    public static final String URL_ZPCHAT_FAST_REPLY_SNED_REPLY_CARD = buildUrl("zpchat/fastreply/sendFastReplyCard");

    /**
     * 牛人/boss快捷回复功能-发送消息
     * <a href="https://api.weizhipin.com/project/30/interface/api/586466">...</a>
     */
    public static final String URL_ZPCHAT_FAST_REPLY_SNED_REPLY_MSG = buildUrl("zpchat/fastreply/sendReplyMsg");

    /**
     * 创建session
     * <a href="https://api.weizhipin.com/project/30/interface/api/552511">...</a>
     */
    public static final String URL_GPT_CREATE_SESSION = buildUrl("zpchat/robot/createSession");

    /**
     * 获取用户标签(能筛出好友的标签)
     * <a href="https://api.weizhipin.com/project/30/interface/api/521884">...</a>
     */
    public static final String URL_USER_MARK_GET_LABELS_SEARCH = buildUrl("zprelation/userMark/getLabels4Search");
    /**
     * BOSS简历卡片加载设计图
     * <a href="https://api.weizhipin.com/project/30/interface/api/521330">...</a>
     */
    public static final String URL_GET_RESUME_MSG_DESIGN_PIC = buildUrl("zpchat/message/getResumeMsgDesignPic");

    /**
     * 蓝领牛人一键报名处理接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/506029">...</a>
     */
    public static final String URL_ZPCHAT_HANDLE_APPLE = buildUrl("zpchat/blue/onekey/handleApply");

    /**
     * boss获取答题版
     * <a href="https://api.weizhipin.com/project/30/interface/api/502333">...</a>
     */
    public static final String URL_GREETING_JOB_QUESTION_ANSWER_BOARD = buildUrl("zpchat/greeting/job/question/answerBoard");

    /**
     * 获取问题列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/502264">...</a>
     */
    public static final String URL_GREETING_JOB_QUESTION_LIST = buildUrl("zpchat/greeting/job/question/list");

    /**
     * 牛人删除答案
     * <a href="https://api.weizhipin.com/project/30/interface/api/502318">...</a>
     */
    public static final String URL_GREETING_DEL_ANSWER = buildUrl("zpchat/greeting/job/question/deleAnswer");
    /**
     * 获取牛人特定问题下的答案
     * <a href="https://api.weizhipin.com/project/30/interface/api/503419">...</a>
     */
    public static final String URL_GREETING_QUESTION_GET_ANSWER = buildUrl("zpchat/greeting/job/question/getAnswer");
    /**
     * 获取牛人所有回答
     * <a href="https://api.weizhipin.com/project/30/interface/api/502306">...</a>
     */
    public static final String URL_GREETING_QUESTION_ANSWERS = buildUrl("zpchat/greeting/job/question/getAnswers");
    /**
     * 牛人回答问题
     * <a href="https://api.weizhipin.com/project/30/interface/api/502309">...</a>
     */
    public static final String URL_GREETING_QUESTION_ANSWER = buildUrl("zpchat/greeting/job/question/answer");

    /**
     * 牛人回答弹窗所需信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/502345">...</a>
     */
    public static final String URL_GREETING_GET_ANSWER_EXTRA = buildUrl("zpchat/greeting/job/question/getAnswerDialogInfo");

    /**
     * <a href="https://api.weizhipin.com/project/30/interface/api/436987">...</a>
     */
    public static final String URL_SELF_INTRODUCE_CLOSE = buildUrl("zpchat/fastreply/selfIntroduce/close");

    /**
     * 获取安心保评价模板
     * <a href="https://api.weizhipin.com/project/30/interface/api/297900">...</a>
     */
    public static final String URL_FEED_BACK_REASSURANEC_TEMPLATE = buildUrl("zpchat/feedback/reassuranceTemplate");

    /**
     * 牛人端安心保职位评价保存接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/297873">...</a>
     */
    public static final String URL_FEED_BACK_REASSURANEC_ANSWER = buildUrl("zpchat/feedback/reassuranceAnswer");

    /**
     * 获取模型生成答案
     * <a href="https://api.weizhipin.com/project/30/interface/api/621032">...</a>
     */
    public static final String URL_ZPCHAT_JOB_QUESTION_GPT_ANSWER = buildUrl("zpchat/greeting/job/question/getGPTAnswer");

    /**
     * 聊天消息中系统消息更新后，调用此接口获取最新消息内容
     * <a href="https://api.weizhipin.com/project/30/interface/api/262323">...</a>
     */
    public static final String URL_MESSAGE_HISTORY_MSG = buildUrl("zpchat/message/historyMsg");

    /**
     * B端招聘助手V1.0-F3新招呼筛选 获取踩原因列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/676100">...</a>
     */
    public static final String URL_CHAT_GET_FEED_BACK_REASON = buildUrl("zprelation/friend/greetRobotFilter/getFeedbackReason");

    /**
     * B端招聘助手V1.0-F3新招呼筛选 提交反馈
     * <a href="https://api.weizhipin.com/project/30/interface/api/676065">...</a>
     */
    public static final String URL_CHAT_FRIEND_GET_FEED_BACK = buildUrl("zprelation/friend/greetRobotFilter/feedback");

    /**
     * 弹窗信息与助答文案生成接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/684780">...</a>
     */
    public static final String URL_CHAT_GREETING_JOB_SELF_ADVANTAGE_EDIT = buildUrl("zpchat/greeting/job/question/selfAdvantageEdit");

    /**
     * ai停止生成接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/684787">...</a>
     */
    public static final String URL_CHAT_GREETING_JOB_STOP_GPT = buildUrl("zpchat/greeting/job/question/stopGPT");

    /**
     * 7.13 同意好友直接获取电话联系
     * <a href="https://api.weizhipin.com/project/30/interface/api/33925">...</a>
     */
    public static final String URL_ZPCHAT_CONTACT_AUTH = buildUrl("zpchat/contact/auth");

    /**
     * 提交gpt任务
     * <a href="https://api.weizhipin.com/project/30/interface/api/692842">...</a>
     */
    public static final String URL_GPT_ENTRANCE_SUBMIT = buildUrl("zpchat/gpt/entrance/submit");

    /**
     * 停止生成上报
     * <a href="https://api.weizhipin.com/project/30/interface/api/702554">...</a>
     */
    public static final String URL_GPT_ENTRANCE_STOP = buildUrl("zpchat/gpt/entrance/stop");
    /**
     * 提交反馈
     * <a href="https://api.weizhipin.com/project/30/interface/api/692845">...</a>
     */
    public static final String URL_GPT_ENTRANCE_FEEDBACK = buildUrl("zpchat/gpt/entrance/feedback");

    /**
     * 获取赞踩原因列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/692844">...</a>
     */
    public static final String URL_GPT_ENTRANCE_FEEDBACK_REASON = buildUrl("zpchat/gpt/entrance/getFeedbackReasons");

    /**
     * 1206.166【智能沟通助手】沟通目标信息列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/663584">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_TARGET_INFO = buildUrl("zpchat/chatHelper/targetInfos");
    /**
     * 1206.166【智能沟通助手】高级功能-入口
     * <a href="https://api.weizhipin.com/project/30/interface/api/692677">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_ADVANCED_FEATURE = buildUrl("zpchat/chatHelper/getAdvancedFeature");
    /**
     * 1206.166【智能沟通助手】沟通目标-获取职位列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/692675">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_JOB_TARGET_INFO = buildUrl("zpchat/chatHelper/jobTargetInfos");
    /**
     * 1206.166【智能沟通助手】沟通目标-修改职位配置信息
     * <a href="https://api.weizhipin.com/project/30/interface/api/692676">...</a>
     */
    public static final String URL_UPDATE_CHAT_HELPER_JOB_TARGET_INFO = buildUrl("zpchat/chatHelper/updateJobTarget");
    /**
     * 1213.165 沟通目标-最近一次使用的交换类型
     * <a href="https://api.weizhipin.com/project/30/interface/api/704683">...</a>
     */
    public static final String URL_UPDATE_CHAT_HELPER_LAST_EXCHABGE_TYPE = buildUrl("zpchat/chatHelper/getUserLastExchangeType");
    public static final String URL_UPDATE_CHAT_HELPER_EXT_CONFIGS = buildUrl("zpchat/chatHelper/getExtConfigs");
    public static final String URL_UPDATE_CHAT_HELPER_UPDATE_EXT_CONFIGS = buildUrl("zpchat/chatHelper/updateExtConfig");

    /**
     * 1206.166【智能沟通助手】沟通目标-获取职位列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/692688">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_ONLINE_JOB_LIST = buildUrl("zpchat/chatHelper/getOnlineJobList");
    /**
     * 1206.166【智能沟通助手】高级功能-获取常见问题答复
     * <a href="https://api.weizhipin.com/project/30/interface/api/692678">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_GREETING_QA_LIST = buildUrl("zpchat/chatHelper/qaConfigList");
    /**
     * 1206.166【智能沟通助手】高级功能-获取追问(更多)
     * <a href="https://api.weizhipin.com/project/30/interface/api/692684">...</a>
     */
    public static final String URL_GET_CHAT_HELPER_PROBE_LIST = buildUrl("zpchat/chatHelper/getProbeList");
    public static final String URL_GET_CHAT_HELPER_SIMPLE_PROBE_LIST = buildUrl("zpchat/chatHelper/getSimplifyProbeList");
    /**
     * 1206.166【智能沟通助手】高级功能-修改常用问题答复
     * <a href="https://api.weizhipin.com/project/30/interface/api/692680">...</a>
     */
    public static final String URL_AI_CHAT_GREETING_UPDATE_QA_CONFIG = buildUrl("zpchat/chatHelper/updateQaConfig");
    /**
     * 1206.166【智能沟通助手】高级功能-添加常用问题答复
     * <a href="https://api.weizhipin.com/project/30/interface/api/692681">...</a>
     */
    public static final String URL_AI_CHAT_GREETING_ADD_QA_CONFIG = buildUrl("zpchat/chatHelper/addQaConfig");
    /**
     * 1206.166【智能沟通助手】高级功能-新增编辑自定义追问问题
     * https://api.weizhipin.com/project/30/interface/api/724327
     */
    public static final String URL_AI_CHAT_GREETING_ADD_OR_UPDATE_CUSTOM_PROBE = buildUrl("zpchat/chatHelper/addOrUpdateCustomProbe");
    /**
     * 1206.166【智能沟通助手】高级功能-删除自定义追问问题
     * https://api.weizhipin.com/project/30/interface/api/724324
     */
    public static final String URL_AI_CHAT_GREETING_DEL_CUSTOM_PROBE = buildUrl("zpchat/chatHelper/delCustomProbe");
    /**
     * 1206.166【智能沟通助手】高级功能-删除常用问题答复
     * https://api.weizhipin.com/project/30/interface/api/692682
     */
    public static final String URL_AI_CHAT_GREETING_DELETE_QA_CONFIG = buildUrl("zpchat/chatHelper/delQaConfig");
    /**
     * 1206.166【智能沟通助手】高级功能-获取常用问题职位列表
     * https://api.weizhipin.com/project/30/interface/api/692679
     */
    public static final String URL_GET_CHAT_HELPER_GREETING_QA_JOB_LIST = buildUrl("zpchat/chatHelper/getQaJobList");
    /**
     * 1206.166【智能沟通助手】高级功能-获取追问职位列表
     * https://api.weizhipin.com/project/30/interface/api/692686
     */
    public static final String URL_GET_CHAT_HELPER_PROBE_JOB_LIST = buildUrl("zpchat/chatHelper/getProbeModifyJobList");
    /**
     * 1206.166【智能沟通助手】高级功能-修改追问绑定职位信息
     * https://api.weizhipin.com/project/30/interface/api/692687
     */
    public static final String URL_AI_CHAT_UPDATE_PROBE_JOBS = buildUrl("zpchat/chatHelper/updateProbeJobMapping");
    /**
     * 对话策略-更新职位追问
     * https://api.weizhipin.com/project/30/interface/api/771613
     */
    public static final String URL_AI_CHAT_UPDATE_PROBE_BIND = buildUrl("zpchat/chatHelper/updateProbeBind");
    public static final String URL_AI_CHAT_SAVE_PROBE_ADDRESS = buildUrl("zpchat/chatHelper/saveProbeAddress");

    /**
     * 1206.166【智能沟通助手】智能聊天模拟沟通相关信息
     * https://api.weizhipin.com/project/30/interface/api/692692
     */
    public static final String URL_CHAT_HELPER_MOCK_INFO = buildUrl("zpchat/chatHelper/mock/info");
    /**
     * 1206.166【智能沟通助手】智能聊天模拟沟通发送消息
     * https://api.weizhipin.com/project/30/interface/api/692703
     */
    public static final String URL_CHAT_HELPER_MOCK_SEND_MSG = buildUrl("zpchat/chatHelper/mock/sendMsg");
    /**
     * 1206.166【智能沟通助手】获取在线职位列表
     * https://api.weizhipin.com/project/30/interface/api/692688
     */
    public static final String URL_CHAT_HELPER_FRIEND_LIST = buildUrl("zpchat/chatHelper/friendList");

    public static final String URL_GEEK_VIDEO_JOB_VIRTUAL_PHONE = buildUrl("zpchat/videoJob/geekGetVirtualPhone");
    public static final String URL_BOSS_VIDEO_JOB_VIRTUAL_PHONE = buildUrl("zpchat/videoJob/bossGetVirtualPhone");

    /**
     * 1208.165【智能沟通助手】模拟沟通选择职位toast
     * https://api.weizhipin.com/project/30/interface/api/699444
     */
    public static final String URL_CHAT_HELPER_MOCK_CHECK_JOB_NOTIFY_SETTING = buildUrl("zpchat/chatHelper/checkJobAndNotifySetting");

    public static final String URL_CERTIFICATION_SECURITY_CHECK_BLOCK = buildUrl("certification/security/sceneLimit/checkAndBlock");

    /**
     * 【视频招呼】保存打招呼视频
     * <a href="https://api.weizhipin.com/project/30/interface/api/147409">...</a>
     */
    public static final String URL_ZPCHAT_FASTREPLY_VIDEO_SAVE = buildUrl("zpchat/fastreply/video/save");

    /**
     * 【视频招呼】获取模板
     * <a href="http://api.kanzhun-inc.com/project/30/interface/api/147430">...</a>
     */
    public static final String URL_ZPCHAT_FASTREPLY_VIDEO_GET_VIDEO_TEMPLATE = buildUrl("zpchat/fastreply/video/getVideoTemplate");

    /**
     * 【视频招呼】获取视频示例
     * <a href="http://api.kanzhun-inc.com/project/30/interface/api/147706">...</a>
     */
    public static final String URL_ZPCHAT_FASTREPLY_VIDEO_GET_VIDEO_SAMPLE = buildUrl("zpchat/fastreply/video/getVideoSample");

    /**
     * 引导牛人开启微信通知页面
     * <a href="https://api.weizhipin.com/project/30/interface/api/336159">...</a>
     */
    public static final String URL_WECHAT_GUIDE_DATA = buildUrl("zpchat/wechat/guideData");

    /**
     * 获取常用语模板v3
     * <a href="https://api.weizhipin.com/project/30/interface/api/427339">...</a>
     */
    public static final String URL_FAST_REPLY_GET_TEMPLATE = buildUrl("zpchat/fastreply/getTemplateV3");

    /**
     * 常用语获取模板文案
     * <a href="https://api.weizhipin.com/project/30/interface/api/427379">...</a>
     */
    public static final String URL_FAST_REPLY_TEMPLATE_CONTENTV3 = buildUrl("zpchat/fastreply/getTemplateContentV3");

    /**
     * 是否是限制用户校验接口，点击交换电话和微信混合按钮时调用
     * http://api.kanzhun-inc.com/project/30/interface/api/146679
     */
    public static final String URL_EXCHANGE_CHECK_USER = buildUrl("zpchat/exchange/checkUser");
    /**
     * 受邀的群列表
     */
    public static final String URL_GET_INVITED_GROUP_CONTACTS = buildUrl("zpchat/group/getInvitedGroups");
    /**
     * 感兴趣的群列表
     */
    public static final String URL_GET_INTEREST_GROUP_CONTACTS = buildUrl("zpchat/group/getRecommGroups");

    /**
     *
     */
    public static final String URL_GET_MIX_RECOMMEND_GROUP_CONTACTS = buildUrl("zpchat/group/getMixedRecommGroups");
    /**
     * 更新群信息
     */
    public static final String URL_UPDATE_GROUP_INFO = buildUrl("zpchat/group/updateGroupInfo");
    /**
     * 邀请入群
     */
    public static final String URL_INVITE_USERS_TO_GROUP = buildUrl("zpchat/group/inviteUsersToGroup");
    /**
     * 选择邀请同公司同事列表接口
     */
    public static final String URL_GET_USERS_BY_COMPANY = buildUrl("zpchat/group/getUsersByCompany");
    /**
     * 用户创建群接口
     */
    public static final String URL_CREATE_GROUP = buildUrl("zpchat/group/createGroup");
    /**
     * 获得群资料
     */
    public static final String URL_GET_GROUP_MATERIAL = buildUrl("zpchat/group/getGroupView");
    /**
     * 获取群聊的分享职位
     */
    public static final String URL_GET_SHARE_JOB_LIST = buildUrl("zpchat/group/getSharableJobs");
    /**
     * 获取群聊的分享简历
     */
    public static final String URL_GET_SHARE_RESUME_LIST = buildUrl("zpchat/group/getSharableResumes");
    /**
     * 转移群名片
     */
    public static final String URL_TRANSFER_GROUP_ADMIN = buildUrl("zpchat/group/transferGroup");
    /**
     * 获取待加入的群成员
     */
    public static final String URL_GET_INVITEES_LIST = buildUrl("zpchat/group/getInvitees");
    /**
     * 发送简历分享
     */
    public static final String URL_GROUP_SEND_RESUME_SHARE = buildUrl("zpchat/group/sendResumeShare");
    /**
     * 发送职位分享
     */
    public static final String URL_GROUP_SEND_JOB_SHARE = buildUrl("zpchat/group/sendJobShare");
    /**
     * 保存第二招呼语
     * http://api.kanzhun-inc.com/project/30/interface/api/144562
     */
    public static final String URL_ZPCHAT_SECOND_GREETING_SAVE = buildUrl("zpchat/greeting/second/save");
    /**
     * http://api.kanzhun-inc.com/project/30/interface/api/144567
     * 关闭第二招呼语
     */
    public static final String URL_ZPCHAT_SECOND_GREETING_CLOSE = buildUrl("zpchat/greeting/second/close");
    /**
     * 1016.250 wukong群聊用户下线校验
     * 1109.51【安心保】群聊功能优化
     * https://api.weizhipin.com/project/30/interface/api/457840
     */
    public static final String URL_CHAT_GROUP_CHECK_WUKONG_OFFLINE = buildUrl("zpchat/group/userGroupEnter");
    /**
     * 1109.51 获取群聊历史分享职位
     * https://api.weizhipin.com/project/30/interface/api/554032
     */
    public static final String URL_CHAT_GROUP_ALL_RESUME_JOB = buildUrl("zpchat/group/getShareJobs");
    public static final String URL_EXCHANGE_RESUME_GET = buildUrl("zpchat/exchange/auth/resume/get");
    /**
     * https://api.weizhipin.com/project/30/interface/api/505441
     */
    public static final String URL_RECOMMEND_TIP_CLOSE = buildUrl("zpchat/shortmsg/recommend/tipClose");
    public static final String URL_ASSISTANT_CALL = buildUrl("zpchat/contact/assistant/call");
    public static final String URL_ASSISTANT_TEST = buildUrl("zpchat/contact/assistant/test");
    public static final String URL_ASSISTANT_EXCHANGE = buildUrl("zpchat/contact/assistant/exchange");
    public static final String URL_WECHAT_ASSISTANT_TEST = buildUrl("zpchat/wechat/assistant/test");
    public static final String URL_WECHAT_ASSISTANT_EXCHANGE = buildUrl("zpchat/wechat/assistant/exchange");
    public static final String URL_EXCHANGE_TEST = buildUrl("zpchat/exchange/test");
    public static final String URL_EXCHANGE_GET_PHONE = buildUrl("zpchat/exchange/getPhoneV2");

    public static final String URL_EXCHANGE_REQUEST = buildUrl("zpchat/exchange/request");
    public static final String URL_EXCHANGE_ACCEPT_TEST = buildUrl("zpchat/exchange/testAccept");
    public static final String URL_EXCHANGE_ACCEPT = buildUrl("zpchat/exchange/accept");
    public static final String URL_ZPCHAT_EXCHANGE_REJECT = buildUrl("zpchat/exchange/reject");
    public static final String URL_ZPCHAT_EXCHANGE_ACCEPT_VIDEO_RESUME = buildUrl("zpchat/exchange/videoResume/accept");
    public static final String URL_ZPCHAT_EXCHANGE_REJECT_VIDEO_RESUME = buildUrl("zpchat/exchange/videoResume/reject");
    /**
     * 获取群屏蔽信息
     */
    public static final String URL_ZPCHAT_GROUP_GET_BLACKUSER = buildUrl("zpchat/group/blackUser/get");
    public static final String URL_ZPCHAT_GROUP_SAVE_BLACKUSER = buildUrl("zpchat/group/blackUser/save");
    /**
     * 1109.51 置顶分享的职位
     * https://api.weizhipin.com/project/30/interface/api/553222
     */
    public static final String URL_GROUP_CHAT_TOP_JOB = buildUrl("zpchat/group/topShareJob");
    public static final String URL_ZP_CHAT_SESSION_SUGGEST = buildUrl("zpchat/session/suggest");
    /**
     * https://api.weizhipin.com/project/30/interface/api/301833
     */
    public static final String URL_FAST_REPLY_SUGGEST_TEMPLATE = buildUrl("zpchat/fastreply/suggest/template");
    /**
     * https://api.weizhipin.com/project/30/interface/api/310743
     */
    public static final String URL_ZPCHAT_FAST_REPLY_CHASE_CHAT_SEAVE = buildUrl("zpchat/fastreply/keepTalking/save");
    // 面试表达接口, 属于聊天模块
    public static final String URL_ZPCHAT_FAST_REPLY_INTERVIEWrEMID_SEND = buildUrl("zpchat/fastreply/interviewRemind/saveAndSend");
    /**
     * https://api.weizhipin.com/project/30/interface/api/326205
     */
    public static final String URL_FAST_REPLY_GET_TEMPLATEV2 = buildUrl("zpchat/fastreply/getTemplateV2");
    /**
     * https://api.weizhipin.com/project/30/interface/api/325539
     */
    public static final String URL_FAST_REPLY_KEYWORDS = buildUrl("zpchat/fastreply/keywords");
    /**
     * https://api.weizhipin.com/project/30/interface/api/326745
     * 客户端双聊触发接口,替换 #URL_FAST_REPLY_SELF_INTRODUCE_GUIDE
     */
    public static final String URL_SESSION_GEEK_REPLY_REACH_BOTH_CHAT = buildUrl("zpchat/session/geekReply/reachBothChat");
    /**
     * https://api.weizhipin.com/project/30/interface/api/334719
     */
    public static final String URL_ZPCHAT_SHORT_MSG_CLOSE = buildUrl("zpchat/shortmsg/close");
    public static final String URL_FEED_BACK_ANSWER_JOB_QUESTION = buildUrl("zpchat/feedback/answerJobQuestionV2");
    public static final String URL_FEED_BACK_GET_JOB_QUESTION = buildUrl("zpchat/feedback/getJobQuestion");
    public static final String URL_ZPCHAT_MESSAGE_GET_ANXINBAO_WEICHAT_DATA = buildUrl("zpchat/message/getAnXinBaoWechatMessage");
    public static final String URL_GPT_ENTRANCE_SESSION = buildUrl("zpchat/gpt/entrance/session");

    public static final String URL_ZPCHAT_GPT_ENTRANCE_SHUTDOWN = buildUrl("zpchat/gpt/entrance/shutdown");

    public static final String URL_GPT_ENTRANCE_CONTINUE_SESSION = buildUrl("zpchat/gpt/entrance/continue/session");
    /**
     * http://api.kanzhun-inc.com/project/30/interface/api/144564
     * 校验第二招呼语状态
     */
    public static final String URL_ZPCHAT_SECOND_GREETING_CHECK = buildUrl("zpchat/greeting/second/check");
    /**
     * https://api.weizhipin.com/project/30/interface/api/296991
     */
    public static final String URL_ZP_RELATATION_GEEK_FILTER_LIST = buildUrl("zprelation/friend/geekFilterList");
    public static final String URL_ZPRELATION_RECOMMEND_GEEK_GET_JOBS = buildUrl("zprelation/recommend/geekGetJobs");


    public static final String URL_ZPRELATION_GEEK_TAG_IS_GEEK_TAG_JOB = buildUrl("zprelation/geekTag/isGeekTagJob");
    public static final String URL_CHAT_FAST_REPLY_GREETING_CARD_INFO = buildUrl("zpchat/fastreply/greetingCard/info");

    /**
     * 1215.613【C端】达成—引导C 接受交换（1 期）
     * https://api.weizhipin.com/project/30/interface/api/708931
     */
    public static final String URL_EXCHANGE_ACCEPT_TIP = buildUrl("zpchat/exchange/acceptTip");

    /**
     * 1217.40【招聘者】同事协助认证优化
     * <a href="https://api.weizhipin.com/project/30/interface/api/714670">...</a>
     */
    public static final String URL_ASSIST_CONFIRM = buildUrl("certification/assist/confirmAssist");

    /**
     * <a href="https://api.weizhipin.com/project/30/interface/api/715285">1217.110【招聘者】 蓝领口头约面识别与流程简化</a>
     */
    public static final String URL_VERBAL_INTERVIEW = buildUrl("zpchat/session/verbalInterview/suggestInfo");
    public static final String URL_VERBAL_INTERVIEW_SEND = buildUrl("zpchat/session/verbalInterview/send");
    public static final String URL_GENERATE_VERBAL_INTERVIEW_MAP = buildUrl("zpchat/session/verbalInterview/mapurl");


    /**
     * 继续人工服务
     * <a href="https://api.weizhipin.com/project/30/interface/api/715009"></a>
     */
    public static final String URL_GUAN_JIA_CONTINUE_SERVICE = buildUrl("zpchat/guanjia/continueService");

    /**
     * 发送消息接口
     * https://api.weizhipin.com/project/30/interface/api/724045
     */
    public static final String URL_ZPCHAT_MESSAGE_SEND = buildUrl("zpchat/message/send");

    /**
     * 更新语音消息状态
     * https://api.weizhipin.com/project/30/interface/api/724060
     */
    public static final String URL_UPDATE_SOUND_MSG = buildUrl("zpchat/message/updateSoundMsg");

    /**
     * 1220.920【C】招呼语优化
     * <a href="https://api.weizhipin.com/project/30/interface/api/724213"></a>
     */
    public static final String URL_GREETING_ALERT_TIP_TEMPLATE = buildUrl("zpchat/greeting/alertTipTemplate");


    public static final String URL_KEFU_WISH_SUBMIT = buildUrl("zpkefu/app/user/wish/submit");

    public static final String URL_KEFU_WISH_DETAIL = buildUrl("zpkefu/app/user/wish/detail");

    /**
     * <a href="https://api.weizhipin.com/project/2365/interface/api/724972">1221.502【意向沟通】BOSS端意向沟通消息场景当日临期提醒</a>
     */
    public static final String URL_TODAY_EXPIRE_REMIND_CLOSE = buildUrl("hunter/app/intention/boss/chat/entrance/todayExpireRemind/close");

    /**
     * <a href="https://api.weizhipin.com/project/30/interface/api/88148">创建直联群</a>
     */
    public static final String URL_ZPCHAT_GROUP_CREARE = buildUrl("zpchat/group/createDirectGroup");//get双叶草 创建群聊

    /**
     * 牛人查看与当前boss 30天内交换过历史
     */
    public static final String URL_EXCHANGE_HISTORY = buildUrl("zprelation/exchange/getExchangeHistory");

    /**
     * 查询牛人90天所有交换记录
     * <a href="https://api.weizhipin.com/project/30/interface/api/405547">...</a>
     */
    public static final String URL_EXCHANGE_GET_ALL_HISTORY = buildUrl("zprelation/exchange/getAllHistory");

    /**
     * 牛人首次进入求职记录弹窗校验
     * <a href="https://api.weizhipin.com/project/30/interface/api/429475">...</a>
     */
    public static final String URL_RECORD_FIRST_SHOW = buildUrl("zprelation/applyHistory/geekFirstCome");

    /**
     * 牛人设置统计起始时间
     * <a href="https://api.weizhipin.com/project/30/interface/api/429491">...</a>
     */
    public static final String URL_RECORD_SET_STATISTIC_TIME = buildUrl("zprelation/applyHistory/setStartDate");

    /**
     * 获取牛人设置的统计起始时间
     * <a href="https://api.weizhipin.com/project/30/interface/api/429483">...</a>
     */
    public static final String URL_RECORD_GET_STATISTIC_START_TIME = buildUrl("zprelation/applyHistory/getStartDate");

    /**
     * 批量清理感兴趣停止招聘职位
     * <a href="https://api.weizhipin.com/project/30/interface/api/437475">...</a>
     */
    public static final String URL_RECORD_CLEAR_OFFLINE_JOB = buildUrl("zprelation/geekTag/cleanInterestingOfflineJob");

    /**
     * JD猜你喜欢模块
     */
    public static final String URL_GEEK_JD_GUESS_LIKE_JOB = buildUrl("zprelation/interaction/geekGetSimilarJob");

    /**
     * 互动职位相似接口
     * <a href="https://api.weizhipin.com/project/30/interface/api/182971">...</a>
     */
    public static final String URL_ZPRELATION_INTERACTION_GEEKGETSIMILARJOB = buildUrl("zprelation/interaction/geekGetSimilarJob");
    public static final String URL_ZPRELATION_INTERACTION_GEEK_RETURN = buildUrl("zprelation/interaction/geek/return");

    public static final String URL_ZPRELATION_INTERACTION_GEEK_FEEDBACK_EXPOSE = buildUrl("zprelation/interaction/geek/feedback/expose");

    /**
     * 删除好友关系 接口名： friendrelation.delete 参数： friendId 好友id
     * <a href="https://api.weizhipin.com/project/30/interface/api/24869">...</a>
     */
    public static final String URL_FRIEND_RELATION_DELETE = buildUrl("zprelation/friend/remove");

    /**
     * 拉黑好友
     * https://api.weizhipin.com/project/30/interface/api/24871
     */
    public static final String URL_ADD_BLACKLIST = buildUrl("zprelation/userBlack/add");
    /**
     * 取消拉黑好友
     * https://api.weizhipin.com/project/30/interface/api/24872
     */
    public static final String URL_DELETE_BLACKLIST = buildUrl("zprelation/userBlack/delete");
    /**
     * Boss获取通知数据的接口
     * https://api.weizhipin.com/project/30/interface/api/232
     */
    public static final String URL_BOSS_GET_NOTIFY_COUNT = buildUrl("zprelation/interaction/bossGetInfo");
    /**
     * 牛人获取通知数据的接口
     * https://api.weizhipin.com/project/30/interface/api/240
     */
    public static final String URL_GEEK_GET_NOTIFY_COUNT = buildUrl("zprelation/interaction/geekGetInfo");

    /**
     * 置顶/取消置顶联系人
     * https://api.weizhipin.com/project/30/interface/api/25033
     */
    public static final String URL_FRIENDRELATION_ISTOP = buildUrl("zprelation/friend/setTop");
    /**
     * 举报用户4.93api
     * https://api.weizhipin.com/project/30/interface/api/24875
     */
    public static final String URL_USER_REPORT_4_93 = buildUrl("zprelation/user/report");

    /**
     * 3.1 牛人感兴趣
     * https://api.weizhipin.com/project/30/interface/api/4200
     */
    public static final String URL_GEEK_TAG_INTEREST = buildUrl("zprelation/geekTag/updateFlag");
    /**
     * 3.1 牛人对职位打标签
     * https://api.weizhipin.com/project/30/interface/api/706270
     */
    public static final String URL_BOSS_TAG_INTEREST = buildUrl("zprelation/bossTag/updateJobTagFlag");

    /**
     * 标记好友(不合适、星标)
     * https://api.weizhipin.com/project/30/interface/api/6387
     */
    public static final String URL_CONTACT_REJECT = buildUrl("zprelation/userMark/add");
    /**
     * 取消不合适
     * https://api.weizhipin.com/project/30/interface/api/36007
     */
    public static final String URL_CONTACT_UNREJECT = buildUrl("zprelation/userMark/del");
    /**
     * boss获取牛人备注标签和备注
     * https://api.weizhipin.com/project/30/interface/api/11011
     */
    public static final String URL_BOSS_GET_GEEK_LABEL_AND_NOTE = buildUrl("zprelation/userMark/getAllLabelsAndNote");

    /**
     * 1019.41 获取用户给好友设置的标签
     * https://api.weizhipin.com/project/30/interface/api/505480
     */
    public static final String URL_GET_PREVIEW_LABELS = buildUrl("zprelation/userMark/getFriendLabels");

    /**
     * boss添加牛人标签
     * https://api.weizhipin.com/project/30/interface/api/11020
     */
    public static final String URL_BOSS_ADD_GEEK_LABEL = buildUrl("zprelation/userMark/addLabel");

    /**
     * 切换开聊的职位
     * https://api.weizhipin.com/project/30/interface/api/24876
     */
    public static final String URL_FRIEND_CHANGE_JOB_ID = buildUrl("zprelation/friend/changeJobId");

    /**
     * 【F4交换过列表】
     * https://api.weizhipin.com/project/30/interface/api/233766
     */
    public static final String URL_GEEK_CHANGE_EXCHANGE_LIST = buildUrl("zprelation/exchange/getExchangeList");

    /**
     * 牛人获取感兴趣/沟通过的职位
     * https://api.weizhipin.com/project/30/interface/api/288
     */
    public static final String URL_GEEK_GET_JOBLIST_BY_JOBTAG = buildUrl("zprelation/geekTag/jobTag/geekGetJob");

    /**
     * F3互动职位列表
     * http://api.kanzhun-inc.com/project/30/interface/api/336
     */
    public static final String URL_GEEK_GET_BOSS = buildUrl("zprelation/interaction/geekGetJob");
    /**
     * Geek获取热门职位推荐列表
     * https://api.weizhipin.com/project/30/interface/api/535942
     */
    public static final String URL_F2_RECOMMEND_JOB_LIST = buildUrl("zprelation/interaction/geekGetHotJobRec");

    /**
     *
     */
    public static final String URL_GEEK_JOB_RECOMMEND = buildUrl("zprelation/geekGetJobRecommend");

    /**
     * 猜你想看不推荐暂停7天
     * http://api.kanzhun-inc.com/project/30/interface/api/88064
     */
    public static final String URL_ZPRELATION_INTERACTION_DISABLE_GUESS_DZ_EXPECT = buildUrl("zprelation/interaction/disableGuessDZExpect");
    /**
     * 职位收藏 新职位列表
     * https://api.weizhipin.com/project/30/interface/api/403299
     */
    public static final String URL_GET_FOCUS_POSITION_NEW_JOBS = buildUrl("zprelation/geekTag/geekInterestRec"); // done
    /**
     * 保存添加的备注和标签
     * https://api.weizhipin.com/project/30/interface/api/6448
     */
    public static final String URL_SAVE_NOTE_AND_LABELS = buildUrl("zprelation/userMark/saveNoteAndLabels");
    /**
     * 7.11灰度 f2联系人筛选V2
     * https://api.weizhipin.com/project/30/interface/api/32263
     */
    public static final String URL_FILTER_FRIENDIDS_NEW = buildUrl("zprelation/friend/bossFilterV2");

    /**
     * 获取系统抽屉信息
     * https://api.weizhipin.com/project/30/interface/api/434179
     */
    public static final String URL_FRIEND_GET_DRAWER_INFO = buildUrl("zprelation/friend/getDrawerInfo");

    /**
     * 免打扰系统通知抽屉
     * https://api.weizhipin.com/project/30/interface/api/434075
     */
    public static final String URL_FRIEND_SET_NO_DISTURB = buildUrl("zprelation/friend/setNoDisturb");

    /**
     * 7.11 f2联系人筛选条件V2
     * https://api.weizhipin.com/project/30/interface/api/32257
     */
    public static final String URL_CHAT_FILTER_LIST_NEW = buildUrl("zprelation/friend/filterLayoutV2");
    /**
     * 视频预约状态同步 聊天模块依赖
     * https://api.weizhipin.com/project/30/interface/api/22573
     */
    public static final String URL_GET_INTERVIEW_STATUS = buildUrl("zprelation/interview/video/status");

    /**
     * 7.06 f2顶部黄条接口
     * https://api.weizhipin.com/project/30/interface/api/24413
     */
    public static final String URL_GET_F2_TOPBAR = buildUrl("zprelation/friend/getF2TopBar");

    /**
     * 7.06 对被过滤的牛人标记符合
     * https://api.weizhipin.com/project/30/interface/api/24383
     */
    public static final String URL_SET_UNFIT_GEEK_FIT = buildUrl("zprelation/friend/bossRemoveFilter");
    /**
     * 7.08 对被过滤的boss标记符合
     * https://api.weizhipin.com/project/30/interface/api/24922
     */
    public static final String URL_SET_UNFIT_BOSS_FIT = buildUrl("zprelation/friend/geekRemoveFilter");

    /**
     * 高端牛人-防打扰属性查询
     * https://api.weizhipin.com/project/30/interface/api/48294
     */
    public static final String URL_INTERACTION_HIGH_GEEK = buildUrl("zprelation/interaction/getHighGeek");

    /**
     * 高端牛人-设置防打扰状态
     * https://api.weizhipin.com/project/30/interface/api/48310
     */
    public static final String URL_INTERACTION_HIGH_GEEK_STATUS = buildUrl("zprelation/interaction/setHighGeekStatus");

    /**
     * 高端牛人-关闭弹窗
     * https://api.weizhipin.com/project/30/interface/api/48318
     */
    public static final String URL_INTERACTION_CLOSE_POUP = buildUrl("zprelation/interaction/closePopup");

    /**
     * 高端牛人-关闭黄条
     * https://api.weizhipin.com/project/30/interface/api/48326
     */
    public static final String URL_CLOSE_YELLOW_STATUS = buildUrl("zprelation/interaction/closeYellowStatus");

    /**
     * 好友简略信息
     * https://api.weizhipin.com/project/30/interface/api/25044
     */
    public static final String URL_FRIEND_GET_SIMPLE_INFO = buildUrl("zprelation/friend/getSimpleInfo");

    /**
     * 1105.131 未读新招呼牛人列表
     * https://api.weizhipin.com/project/30/interface/api/520732
     */
    public static final String URL_ZPRELATION_FRIEND_UNREAD_NEWGREET_LIST = buildUrl("zprelation/friend/unReadNewGreetList");

    /**
     * 标签管理-标签列表接口
     * https://api.weizhipin.com/project/30/interface/api/87609
     */
    public static final String URL_USER_MARK_LABEL_MANAGER_LIST = buildUrl("zprelation/userMark/labelManageList");

    /**
     * 标签管理-标签删除
     * https://api.weizhipin.com/project/30/interface/api/87616
     */
    public static final String URL_USER_MARK_LABEL_MANAGER_DEL = buildUrl("zprelation/userMark/labelManageDel");

    /**
     * 猜你想看F2展示
     * https://api.weizhipin.com/project/30/interface/api/105711
     */
    public static final String URL_ZPRELATION_INTERACTION_GUESSYOURLIKES = buildUrl("zprelation/interaction/guessYourLikes");

    /**
     * 810 大蓝 达成补贴
     * http://api.kanzhun-inc.com/project/30/interface/api/111703
     */
    public static final String URL_ZPRELATION_GEEK_SUBSIDY = buildUrl("zprelation/geek/subsidy");

    /**
     * 批量标记不合适
     * https://api.weizhipin.com/project/30/interface/api/415899
     */
    public static final String URL_USER_MARK_BATCH_REJECT = buildUrl("zprelation/userMark/batchReject");

    /**
     * B端沟通进度-获取
     * https://api.weizhipin.com/project/30/interface/api/422259
     */
    public static final String URL_FRIEND_PROGRESS_GET = buildUrl("zprelation/friend/progress/get");

    /**
     * B端沟通进度-修改
     * https://api.weizhipin.com/project/30/interface/api/422291
     */
    public static final String URL_FRIEND_PROGRESS_UPDATE = buildUrl("zprelation/friend/progress/update");

    /**
     * B端沟通进度-取消
     * https://api.weizhipin.com/project/30/interface/api/422315
     */
    public static final String URL_FRIEND_PROGRESS_CANCEL = buildUrl("zprelation/friend/progress/cancel");

    /**
     * 获取新招呼优质牛人
     * https://api.weizhipin.com/project/30/interface/api/643413
     */
    public static final String URL_FRIEND_GET_HIGH_QUALITY_GEEKS = buildUrl("zprelation/friend/getHighQualityGeeks");

    /**
     * 新招呼快速处理排序接口
     * https://api.weizhipin.com/project/30/interface/api/612818
     */
    public static final String URL_ZPCHAT_RELATION_FRIEND_SORE_LIST = buildUrl("zprelation/friend/greetSortList");

    /**
     * 对job不感兴趣原因列表
     * https://api.weizhipin.com/project/30/interface/api/153596
     */
    public static final String URL_USER_MARK_GET_JOB_REJECT_REASON = buildUrl("zprelation/userMark/getJobRejectReason");

    /**
     * f2牛人筛选条件
     * https://api.weizhipin.com/project/30/interface/api/324351
     */
    public static final String URL_GEEK_FILTER_LAYOUTV2 = buildUrl("zprelation/friend/geekFilterLayout");

    /**
     * 获取MQTT节点信息
     * 5.5.2添加
     * https://api.weizhipin.com/project/30/interface/api/679586
     */
    public static final String URL_MQTT_IP = buildUrl("zpchat/config/ips");

    /**
     * 更新群设置
     * https://api.weizhipin.com/project/30/interface/api/3216
     */
    public static final String URL_UPDATE_MEMBER_SETTING = buildUrl("zpchat/group/updateMemberSetting");
    /**
     * 移除群
     */
    public static final String URL_GROUP_REMOVE_MEMBER = buildUrl("zpchat/group/removeMember");

    /**
     * 加入群聊
     * https://api.weizhipin.com/project/30/interface/api/3136
     */
    public static final String URL_JOIN_GROUP = buildUrl("zpchat/group/joinGroup");

    /**
     * 同步群成员信息
     * https://api.weizhipin.com/project/30/interface/api/3248
     */
    public static final String URL_SYNC_GROUP_MEMBERS = buildUrl("zpchat/group/syncMembers");

    /**
     * 编辑群名片
     * https://api.weizhipin.com/project/30/interface/api/3080
     */
    public static final String URL_EDIT_GROUP_USER_CARD = buildUrl("zpchat/group/updateGroupCard");

    /**
     * 获取个人群名片信息
     *
     */
    public static final String URL_GET_USER_GROUP_CARD = buildUrl("zpchat/group/getGroupCard");
    /**
     * 牛人反馈收集标签
     * https://api.weizhipin.com/project/30/interface/api/148031
     */
    public static final String URL_GET_FEEDBACK_REASONS = buildUrl("zpchat/notify/setting/reasons");

    /**
     * 获取用户开关列表
     * https://api.weizhipin.com/project/30/interface/api/24349
     */
    public static final String URL_USER_GET_NOTIFY_SETTINGS = buildUrl("zpchat/notify/setting/get");
    /**
     * 更新开关配置
     * https://api.weizhipin.com/project/30/interface/api/24711
     */
    public static final String URL_USER_UPDATE_NOTIFY_SETTINGS = buildUrl("zpchat/notify/setting/update");

    /**
     * 根据开关类型获取开关设置
     * https://api.weizhipin.com/project/30/interface/api/59292
     */
    public static final String URL_BOSS_GET_USER_SETTING = buildUrl("zpchat/notify/setting/getWithType");
    /**
     * 更新开关配置
     * https://api.weizhipin.com/project/30/interface/api/24711
     */
    public static final String URL_BOSS_USER_SETTING_UPDATE = buildUrl("zpchat/notify/setting/update");

    /**
     * 合规信息采集上报
     * https://api.weizhipin.com/project/30/interface/api/217284
     */
    public static final String URL_ZPCHAT_COMPLIANCE_REPORT = buildUrl("zpchat/compliance/report");

    /**
     * 3.8 添加快捷回复
     * https://api.weizhipin.com/project/30/interface/api/6302
     */
    public static final String URL_ADD_QUICK_REPLY = buildUrl("zpchat/fastreply/update");
    /**
     * 5.0 删除快捷回复
     * https://api.weizhipin.com/project/30/interface/api/24808
     */
    public static final String URL_DELETE_QUICK_REPLY = buildUrl("zpchat/fastreply/delete");

    /**
     * 快速回复语排序
     * https://api.weizhipin.com/project/30/interface/api/19417
     */
    public static final String URL_ZP_FAST_REPLAY_SORT = buildUrl("zpchat/fastreply/sort");

    /**
     * 牛人获取打招呼语
     * https://api.weizhipin.com/project/30/interface/api/6180
     */
    public static final String URL_GET_GREETING_WORDS = buildUrl("zpchat/greeting/get");
    /**
     * 牛人修改打招呼语
     * https://api.weizhipin.com/project/30/interface/api/24802
     */
    public static final String URL_GREETING_WORDS_UPDATE = buildUrl("zpchat/greeting/update");

    /**
     * 获取要删除的最大messageId
     */
    public static final String DISCARD_MESSAGE_ID = buildUrl("zpchat/message/discardlastmsgid");

    /**
     * https://api.weizhipin.com/project/30/interface/api/6181
     */
    public static final String URL_GREETING_CUSTOM_UPDATE = buildUrl("zpchat/greeting/customUpdate");

    /**
     * 添加表情接口
     * https://api.weizhipin.com/project/30/interface/api/21514
     */
    public static final String URL_ADD_EMOTION = buildUrl("zpchat/sticker/add");
    /**
     * 删除表情
     * https://api.weizhipin.com/project/30/interface/api/6432
     */
    public static final String URL_EMOTION_DELETE = buildUrl("zpchat/sticker/delete");

    /**
     * 新用户招呼语提示弹窗模板
     * https://api.weizhipin.com/project/30/interface/api/412363
     */
    public static final String URL_ZPCHAT_GREETING_TIPTEMPLATE = buildUrl("zpchat/greeting/getTipTemplate");

    /**
     * 获取BOSS设置的不合适自动回复信息
     * https://api.weizhipin.com/project/30/interface/api/6342
     */
    public static final String URL_BOSS_GET_IMPROPERREPLY = buildUrl("zpchat/improper/bossGetSettings");

    /**
     * 打开或者关闭不合适自动回复
     * https://api.weizhipin.com/project/30/interface/api/6351
     */
    public static final String URL_OPEN_GET_IMPROPERREPLY = buildUrl("zpchat/improper/bossSwitch");

    /**
     * 保存/修改 自动回复内容
     * https://api.weizhipin.com/project/30/interface/api/6352
     */
    public static final String URL_SAVE_REPLAY_CONTENGT = buildUrl("zpchat/improper/bossSaveReply");

    /**
     * 切换自动回复方式接口
     * https://api.weizhipin.com/project/30/interface/api/6374
     */
    public static final String URL_CHANGE_IMPROPER_REPLAY_TYPE = buildUrl("zpchat/improper/bossChangeType");
    /**
     * 用户收藏对方发送的自定义表情
     * https://api.weizhipin.com/project/30/interface/api/6497
     */
    public static final String URL_ADD_FAVOURITE_EMOTION = buildUrl("zpchat/sticker/favorite");

    /**
     * 获取GIF表情
     * https://api.weizhipin.com/project/30/interface/api/6433
     */
    public static final String URL_GET_USER_STICKER = buildUrl("zpchat/sticker/get");

    /**
     * 消息撤回
     * https://api.weizhipin.com/project/30/interface/api/16123
     */
    public static final String URL_POST_MESSAGE_WITHDRAW = buildUrl("zpchat/message/withdraw");

    /**
     * 删除群聊
     * https://api.weizhipin.com/project/30/interface/api/18184
     */
    public static final String URL_BOSS_GROUP_HIDE_GROUP = buildUrl("zpchat/group/hideGroup");

    /**
     * 隐藏群(批量)
     * https://api.weizhipin.com/project/30/interface/api/123690
     */
    public static final String URL_GROUP_DISMISS_GRAVITY_GROUPS = buildUrl("zpchat/group/hideGroups");

    /**
     * 7.01 boss继续沟通进入聊天会话
     * https://api.weizhipin.com/project/30/interface/api/22583
     */
    public static final String URL_BOSS_ENTER_CHAT = buildUrl("zpchat/session/bossEnter");

    /**
     * 下拉刷新 同步未读消息
     * https://api.weizhipin.com/project/30/interface/api/649157
     */
    public static final String URL_MESSAGE_SYNC_READV2 = buildUrl("zpchat/message/syncReadV2");

    /**
     * 聊天消息中系统消息更新后，调用此接口获取最新消息内容
     * https://api.weizhipin.com/project/30/interface/api/23971
     */
    public static final String URL_MESSAGE_UPDATE_REFRESH = buildUrl("zpchat/message/refresh");

    /**
     * 7.13 直接获取好友联系方式
     * https://api.weizhipin.com/project/30/interface/api/33931
     */
    public static final String URL_ZPCHAT_CONTACT_GET = buildUrl("zpchat/contact/get");

    /**
     * 聊天模块中使用
     * https://api.weizhipin.com/project/30/interface/api/42595
     */
    public static final String URL_VIDEO_INTERVIEW_APPLY = buildUrl("zpchat/videoInterview/apply");

    /**
     * 客户端视频简历请求交换接口
     * https://api.weizhipin.com/project/30/interface/api/141054
     */
    public static final String URL_EXCHANGE_VIDEO_RESUME_REQUEST = buildUrl("zpchat/exchange/videoResume/request");

    /**
     * 开启或更新微信强提醒设置
     * https://api.weizhipin.com/project/30/interface/api/91907
     */
    public static final String URL_ZPCHAT_WX_REMIND = buildUrl("zpchat/wechat/save/wxRemind/setting");

    /**
     * 获取用户微信通知信息
     * https://api.weizhipin.com/project/30/interface/api/91914
     */
    public static final String URL_WX_NOTIFY_SETTING = buildUrl("zpchat/wechat/get/WxNotify/setting");

    /**
     * 判断是否关注公众号
     * https://api.weizhipin.com/project/477/interface/api/454353
     */
    public static final String URL_WECHAT_HASSUBSCRIBED = buildUrl("zpchat/wechat/hasSubscribed");

    /**
     * 获取视频常用语播放地址
     * https://api.weizhipin.com/project/30/interface/api/147429
     */
    public static final String URL_FAST_REPLAY_VIDEO_GET_URL = buildUrl("zpchat/fastreply/video/getUrl");

    /**
     * https://api.weizhipin.com/project/30/interface/api/153673
     */
    public static final String URL_FAST_REPLAY_VIDEO_CHAT_CONTENTS = buildUrl("zpchat/fastreply/video/chatContents");

    /**
     * 908.560 职位招呼语列表
     * https://api.weizhipin.com/project/30/interface/api/189628
     */
    public static final String URL_ZPCHAT_GREETING_JOB_GET = buildUrl("zpchat/greeting/job/get");

    /**
     * 908.560 职位招呼语保存(编辑)
     * https://api.weizhipin.com/project/30/interface/api/189705
     */
    public static final String URL_ZPCHAT_GREETING_JOB_SAVE = buildUrl("zpchat/greeting/job/save");

    /**
     * 908.560 职位招呼语删除
     * https://api.weizhipin.com/project/30/interface/api/189719
     */
    public static final String URL_ZPCHAT_GREETING_JOB_DELETE = buildUrl("zpchat/greeting/job/delete");

    /**
     * https://api.weizhipin.com/project/20/interface/api/287118
     */
    public static final String URL_ZPCHAT_CONTACT_VIRTUAL_PHONE_CALL = buildUrl("zpchat/contact/virtualPhone/call");

    /**
     * 职位反馈问卷提交
     * https://api.weizhipin.com/project/30/interface/api/225927
     */
    public static final String URL_ZPCHAT_FEEDBACK_ANSWER_JOB_QUESTION = buildUrl("zpchat/feedback/answerJobQuestion");

    /**
     * https://api.weizhipin.com/project/30/interface/api/228465
     */
    public static final String URL_SELF_INTRODUCE_GET = buildUrl("zpchat/fastreply/selfIntroduce/get");

    /**
     * 判断boss是否满足设置入口的展示条件
     * https://api.weizhipin.com/project/30/interface/api/503374
     */
    public static final String URL_GREETING_JOB_QUESTION_CONFIG_ENTRANCE = buildUrl("zpchat/greeting/job/question/configEntrance");

    /**
     * 获取安心保推荐职位列表
     * https://api.weizhipin.com/project/30/interface/api/507036
     */
    public static final String URL_CHAT_ANXINBAO_JOBS = buildUrl("zpchat/message/getAnXinBaoJobs");

    /**
     * 1104.061
     * https://api.weizhipin.com/project/30/interface/api/519145
     * 获取聊天满意度收集弹窗模板
     */
    public static final String URL_CHAT_FEEDBACK_SATISFACTION_CARD = buildUrl("zpchat/feedback/getSatisfactionCard");

    /**
     * 微信绑定记录缓存
     * https://api.weizhipin.com/project/30/interface/api/519110
     */
    public static final String URL_ZPCHAT_WECHAT_MARK_WX_LIN = buildUrl("zpchat/wechat/markWxLink");
    /**
     * 1105.603 F1通知弹窗引导 批量开启开关
     * https://api.weizhipin.com/project/30/interface/api/520941
     */
    public static final String URL_GEEK_BATCH_OPEN_PUSH = buildUrl("zpchat/notify/setting/batchOpenPush");

    /**
     * 获取GPT职位推荐信息
     * https://api.weizhipin.com/project/30/interface/api/552592
     */
    public static final String URL_GPT_GET_JOB_RECOMMED = buildUrl("zpchat/robot/getJobRecommend");

    /**
     * 点击不合适提示弹窗
     * https://api.weizhipin.com/project/30/interface/api/613118
     */
    public static final String URL_ZPCHAT_IMPROPER_SHOW_REJECT_TIP = buildUrl("zpchat/improper/showRejectTip");

    /**
     * 用户切换拒绝语
     * https://api.weizhipin.com/project/30/interface/api/613112
     */
    public static final String URL_ZPCHAT_IMPROPER_BOSS_CHANGE_TEMPLATE = buildUrl("zpchat/improper/bossChangeTemplate");

    /**
     * 用户添加自定义拒绝语
     * https://api.weizhipin.com/project/30/interface/api/613106
     */
    public static final String URL_ZPCHAT_IMPROPER_BOSS_SAVE_TEMPLATE = buildUrl("zpchat/improper/bossSaveTemplate");

    /**
     * 用户编辑自定义拒绝语
     * https://api.weizhipin.com/project/30/interface/api/614240
     */
    public static final String URL_ZPCHAT_IMPROPER_BOSS_UPDATE_TEMPLATE = buildUrl("zpchat/improper/bossUpdateTemplate");


    /**
     * 招呼语gpt手动生成
     * https://api.weizhipin.com/project/30/interface/api/639029
     */
    public static final String URL_GREETING_GET_GPT = buildUrl("zpchat/greeting/getSuggestGreeting");

    public static final String URL_WECHAT_NOTIFY_TOGGLE = buildUrl("zpchat/wechat/notify/toggle");

    public static final String URL_ZP_RELATION_INTERACTION_CLOSE_INTEREST_JOB = buildUrl("zprelation/interaction/closeInterestJob");
    public static final String URL_ZP_RELATION_INTERACTION_SAVE_INTEREST_JOB = buildUrl("zprelation/interaction/saveInterestJob");

    /**
     * 1225.619 <a href="https://api.weizhipin.com/project/30/interface/api/736549">...</a>
     */
    public static final String URL_GET_MSG_TIP = buildUrl("zpchat/message/getMsgTip");

    /**
     * 1225.111 <a href="https://api.weizhipin.com/project/30/interface/api/736660">...</a>
     */
    public static final String URL_KEEP_TALKING_CLOSE_TIP = buildUrl("zpchat/fastreply/keepTalking/closeTip");

    /**
     * https://api.weizhipin.com/project/30/interface/api/736636
     */
    public static final String URL_GPT_ASSIS_PROTOCAL = buildUrl("zpchat/gpt/entrance/bais/protocol");

    /**
     * https://api.weizhipin.com/project/30/interface/api/738262
     */
    public static final String URL_ZPCHAT_WIDGET_GET = buildUrl("zpchat/widget/get");

    /**
     * 1226.110【招聘者】蓝领面试地址卡片优化
     * <a href="https://api.weizhipin.com/project/30/interface/api/739402">...</a>
     */
    public static final String URL_LOCATION_INFO = buildUrl("zpchat/session/verbalInterview/locationInfo");
    public static final String URL_LOCATION_OPERATE = buildUrl("zpchat/session/verbalInterview/locationOperate");

    /**
     * https://api.weizhipin.com/project/30/interface/api/739558
     */
    public static final String URL_ZPRELATION_REJECT_SHOW_DRAWER = buildUrl("zprelation/reject/showDrawer");
    /**
     * https://api.weizhipin.com/project/30/interface/api/739561
     */
    public static final String URL_ZPRELATION_REJECT_FRIENDS = buildUrl("zprelation/reject/friends");
    /**
     * 1226.80 主动移除拒绝好友
     * https://api.weizhipin.com/project/30/interface/api/739567
     */
    public static final String URL_REMOVE_REJECTED_FRIEND = buildUrl("zprelation/reject/remove");

    /**
     * 1226.607【C】新招呼消息突出推荐理由 1.0
     * <a href="https://api.weizhipin.com/project/30/interface/api/739096">...</a>
     */
    public static final String URL_GET_RECOMMEND_REASON = buildUrl("zprelation/friend/getRecommendReason");

    public static final String URL_REFRESH_FAST_REPLY_CARD = buildUrl("zpchat/fastreply/card/refresh");
    /**
     * https://api.weizhipin.com/project/30/interface/api/742336
     */
    public static final String URL_CHAT_HELPER_NOTIFY_SETTING = buildUrl("zpchat/chatHelper/getNotifySettingsV2");
    /**
     * 1301.165【智能沟通助手8期】沟通筛选目标-职位筛选信息列表
     * https://api.weizhipin.com/project/30/interface/api/742351
     */
    public static final String URL_CHAT_HELPER_GEEK_START_FILTER_INFO = buildUrl("zpchat/chatHelper/geekStart/filterInfos");
    /**
     * 1301.165【智能沟通助手8期】沟通筛选目标-筛选项职位列表
     * https://api.weizhipin.com/project/30/interface/api/742360
     */
    public static final String URL_CHAT_HELPER_GEEK_START_JOB_FILTER_INFO = buildUrl("zpchat/chatHelper/geekStart/jobFilterInfos");
    /**
     * 1301.165【智能沟通助手8期】沟通筛选目标-保存或更新筛选条件
     * https://api.weizhipin.com/project/30/interface/api/742348
     */
    public static final String URL_CHAT_HELPER_GEEK_START_UPDATE_FILTER = buildUrl("zpchat/chatHelper/geekStart/saveOrUpdateFilters");
    /**
     * 1301.165【智能沟通助手8期】沟通筛选目标-筛选条件枚举接口
     * https://api.weizhipin.com/project/30/interface/api/742363
     */
    public static final String URL_CHAT_HELPER_GEEK_START_FILTER_ITEMS = buildUrl("zpchat/chatHelper/geekStart/filterItems");
    public static final String URL_CHAT_NOTIFY_SETTING_PUSH_NOTICE_UPDATE = buildUrl("zpchat/notify/setting/pushNotice/update");
    public static final String URL_CHAT_NOTIFY_SETTING_PUSH_NOTICE_GET = buildUrl("zpchat/notify/setting/pushNotice/get");

    /**
     * 1302.110 【BC】蓝领C快速达成路径尝试
     */
    public static final String URL_CHAT_EXCHANGE_REQUEST = buildUrl("zpchat/exchange/blueCollarRequest");
    public static final String URL_CHAT_EXCHANGE_CANCEL_TEST = buildUrl("zpchat/exchange/blueCollarRequest/cancel/test");
    public static final String URL_CHAT_EXCHANGE_CANCEL= buildUrl("zpchat/exchange/blueCollarRequest/cancel");

    /**
     * 1303.1.4 【B端、PC同步】【智能沟通助手10期】交互优化
     */
    public static final String URL_CHAT_QUICK_SETTING_INGO = buildUrl("zpchat/chatHelper/quickSettingInfos");
    public static final String URL_CHAT_REPORT_GEEK_INFO_TOAST = buildUrl("zpchat/chatHelper/reportGeekInfoToast");
    public static final String URL_CHAT_HELPER_DS_CONFIG = buildUrl("zpchat/chatHelper/dsConfig");

    public static final String URL_KEFU_APP_SESSION_GET_CURRENT_SESSION = buildUrl("kefu/app/session/getCurrentSession");

    /**
     * 拒绝banner文案
     * https://api.weizhipin.com/project/30/interface/api/763612
     */
    public static final String URL_ZPRELATION_REJECT_BANNER_INFO = buildUrl("zprelation/reject/bannerInfo");

    public static final String URL_AI_HIRING_HISTORY = buildUrl("zpjob/ai/hiring/history");

    /**
     * <a href="https://api.weizhipin.com/project/30/interface/api/29125">...</a>
     */
    public static final String URL_MESSAGE_CLICK_REPORT = buildUrl("zpchat/message/click");
    /**
     * 1307.608【B】新招呼卡片展示双边匹配信息：获取简历AI匹配信息
     * https://api.weizhipin.com/project/30/interface/api/765739
     */
    public static final String URL_RESUME_CARD_SUMMARY_INFO = buildUrl("zpchat/message/getResumeMsgSummary");

    /**
     * 1307.80【C】缓解部分招聘者超期会话资源占用问题
     * 双向清除好友(批量)
     * https://api.weizhipin.com/project/30/interface/api/764704
     */
    public static final String URL_ZPRELATION_FRIEND_CLEAN = buildUrl("zprelation/friend/clean");
    /**
     * [AI直聊] 获取抽屉信息
     * https://api.weizhipin.com/project/30/interface/api/768151
     */
    public static final String URL_ZPRELATION_AI_DIRECT_CHAT_GET_DRAWER = buildUrl("zprelation/ai/directChat/getDrawer");

    /**
     * 1308.165【B&C】AI帮你推：获取推荐牛人
     * https://api.weizhipin.com/project/30/interface/api/767986
     */
    public static final String URL_CHAT_AI_REC_GEEK = buildUrl("zprelation/ai/directChat/recommend/geek");

    /**
     * 1308.165【B&C】AI帮你推：获取推荐职位
     * https://api.weizhipin.com/project/30/interface/api/767989
     */
    public static final String URL_CHAT_AI_REC_JOB = buildUrl("zprelation/ai/directChat/recommend/job");
    /**
     * [AI直聊] 获取直闪闪推荐理由
     * https://api.weizhipin.com/project/30/interface/api/767998
     */
    public static final String URL_ZPRELATION_AI_DIRECT_CHAT_REASON = buildUrl("zprelation/ai/directChat/recommend/reason");

    /**
     * [AI直聊] 获取直聊推荐介绍
     * https://api.weizhipin.com/project/30/interface/api/770194
     */
    public static final String URL_ZPRELATION_CHAT_AI_DIRECT_EXPLAIN = buildUrl("zprelation/ai/directChat/getExplain");

    /**
     * 获取AI聊天助手自定义策略
     * https://api.weizhipin.com/project/30/interface/api/765274
     */
    public static final String URL_CHAT_HELPER_GET_CUSTOM_STRATEGY = buildUrl("zpchat/chatHelper/chatStrategyInfos");

    /**
     * 更新AI聊天助手自定义策略
     * https://api.weizhipin.com/project/30/interface/api/745846
     */
    public static final String URL_CHAT_HELPER_UPDATE_CUSTOM_STRATEGY = buildUrl("zpchat/chatHelper/updateChatStrategy");
    public static final String URL_CHAT_HELPER_GEEK_START_DELETE_FILTER = buildUrl("zpchat/chatHelper/geekStart/deleteFilter");
    public static final String URL_ZPCHAT_CHATHELPER_ASSISTCONFIG = buildUrl("zpchat/chatHelper/assistConfig");

    /**
     * 查询用户session问答历史
     * GET /api/zpchat/gpt/entrance/session/history
     */
    public static final String URL_AI_SESSION_HISTORY = buildUrl("zpchat/gpt/entrance/session/history");


    public static final String URL_AI_HISTORY = buildUrl("zpchat/gpt/entrance/history");

    public static final String URL_AI_SESSION_DELETE = buildUrl("zpchat/gpt/entrance/session/delete");

    /**
     * <a href="https://api.weizhipin.com/project/30/interface/api/780379">...</a>
     * 1312.881
     */
    public static final String URL_MICRO_RESUME_REQUEST_CARD = buildUrl("zpchat/message/clickStreamerMicroResumeRequestCard");
}
