package com.hpbr.bosszhipin.chat.export.constant;

/**
 * @ClassName ：ChatAnalyticsAction
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/24  1:43 PM
 */
public interface ChatAnalyticsAction {

    /**
     * 10.15
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=48992
     * 点击更多问题后点击二级类标签时上报
     */
    String ACTION_ZHS_HIGHFREQUENCY_LABEL_CLICK = "zhs-highfrequency-label-click";

    /**
     * 10.15
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=43559
     * 智慧石点击，V1015加入 常用3级问点击时属于哪个二级标签下
     */
    String ACTION_ZHS_GUESS_QUESTION_CLICK = "zhs-guess-question-click";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54049
     * 智能客服满意度调研提交时上报
     */
    String ACTION_ZHS_ALERT_SUBMIT = "zhs_alert_submit";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54048
     * 智能客服满意度调研弹窗展示
     */
    String ACTION_ZHS_ALERT_SHOW = "zhs_alert_show";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54047
     * 双聊满意度调研弹窗评价提交
     */
    String ACTION_CHAT_ALERT_SUBMIT = "chat-alert-submit";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54046
     * 双聊满意度调研弹窗展示时上报
     */
    String ACTION_CHAT_ALERT_SHOW = "chat-alert-show";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54043
     * 聊天导航区域点击名称时上报
     */
    String ACTION_F2_DETAIL_HEAD_CLICK = "f2-detail-head-click";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54042
     * V1104聊天区域, 返回按钮点击
     */
    String ACTION_F2_DETAIL_CLICK_BACK = "f2-detail-click-back";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54039
     * V1104联系人菜单页 删除联系人入口点击
     */
    String ACTION_F2_SET_CLICK_DELETE = "f2-set-click-delete";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54036
     * V1104联系人设置页标记不合适点击操作
     */
    String ACTION_F2_SET_HIDE_CLICK = "f2-set-hide-click";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54035
     * V1104联系人菜单页面 黑名单入口点击
     */
    String ACTION_F2_SET_BLOCK_CLICK = "f2-set-block-click";

    /**
     * 11.04
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/event/100?showdetail=54034
     * V1104 在联系人设置页点击 头像/名称时上报
     */
    String ACTION_F2_SET_CLICK_MORE = "f2-set-click-more";

    /**
     * 1119.252 项目外包消息盒子中的会话点击，并记录点击的是哪一个
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=69300
     */
    String ACTION_WUKONG_LIST_CLICK = "op-messagebox-sessionclick";

    /**
     * 1120.603
     * https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=79607
     */
    String ACTION_GROUP_ANTE_CLICK = "biz-publicWelfare-click-chat";

    /**
     * <a href="https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=221088">1125.169 系统回复筛选条件打点</a>
     */
    String ACTION_BOSS_F1_REPLY_EXPO = "extension-aiagent-filter-reply";

    /**
     * <a href="https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=221098">1125.169 删除筛选条件打点</a>
     */
    String ACTION_BOSS_F1_REPLY_DELETE_CLICK = "extension-aiagent-filter-delete";

    /**
     * <a href="https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=221102">1125.169 识别失败，解析失败打点</a>
     */
    String ACTION_BOSS_F1_ANALYZE_FAIL_EXPO = "extension-aiagent-filter-error";

    /**
     * <a href="https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=221081">1125.169 ai高级筛选输入信息</a>
     */
    String ACTION_BOSS_F1_INPUT_CLICK = "extension-aiagent-filter-chat";

    /**
     * <a href="https://datastar.kanzhun-inc.com/dashboard/warlock/metamanage/meta/event/100?showdetail=221075">1125.169 刷新推荐页面打点</a>
     */
    String ACTION_BOSS_F1_SUBMIT_CLICK = "extension-aiagent-filter-refresh";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=346979">12.04.169 用户使用AI筛选后，点赞点踩，包含取消点赞，取消点踩</a>
     */
    String ACTION_BOSS_F1_FEEDBACK_CLICK = "extension-aiagent-filter-likes";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=346997">1204.169 点赞点踩后，收集评论弹窗点击</a>
     */
    String ACTION_BOSS_F1_FEEDBACK_DIALOG_CLICK = "extension-aiagent-filter-likesComment";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2392102">1207.601 正负反馈点击</a>
     */
    String ACTION_GEEK_F1_ASSISTANT_FEEDBACK_CLICK = "ai-assistant-feedback-pop-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2392193">12.07 601 负反馈类型</a>
     */
    String ACTION_GEEK_F1_ASSISTANT_FEEDBACK_REASON_CLICK = "ai-assistant-feedback-negtype-pop-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2392262">1207.601 ai求职助手聊天页面点击</a>
     */
    String ACTION_GEEK_F1_ASSISTANT_CLICK = "ai-assistant-chatbox-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=2785465">...</a>
     */
    String ACTION_GEEK_CONNECT_MSG_SHOW = "geek-connect-xiaoxibiaoqian-show";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=2786592&appId=100">...</a>
     */
    String ACTION_LLM_GEEK_FLASH_INFO = "chat-page-llm-geek-info";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2824400&region=ml&appId=100">...</a>
     */
    String ACTION_BOSS_INTERVIEW_ADDRESS_CLICK = "Boss-interview-address-interview-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2824335&region=ml&appId=100">...</a>
     */
    String ACTION_BOSS_INTERVIEW_ADDRESS_SHOW = "Boss-interview-address-interview-show";

    String ACTION_GEEK_CONNECT_QUICK_REPLY_SHOW = "geek-connect-quickreply-show";
    String ACTION_GEEK_CONNECT_QUICK_REPLY_CLICK = "geek-connect-quickreply-click";

    /**
     * 1220.910 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=2918296">...</a>
     */
    String ACTION_GEEK_CLASSIFY_TAB_SWITCH = "geek-connect-Listswitching-click";


    String ACTION_GEEK_CONNECT_TO_TEXT_CLICK = "geek-connect-To_text-click";

    String ACTION_GEEK_CONNECT_VOICE_PLAYBACK_CLICK = "geek-connect-voice-playback-click";

    String ACTION_USER_FEEDBACK_CLICK = "userfeedback-OnlineService-evaluation-popup-click";

    String ACTION_USER_FEEDBACK_SHOW = "userfeedback-OnlineService-evaluation-card-show";

    String ACTION_USER_FEEDBACK_CLICK_NEW = "userfeedback-OnlineService-evaluation-card-click";

    /**
     * 1221.502 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2959967&region=ml">...</a>
     */
    String ACTION_HUNTER_CUSTOMER_APP_GUIDE_BAR_CLICK = "extension-hunter-cvcustomer-APPguidebarClick";
    /**
     * 1221.502 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=2959965&region=ml">...</a>
     */
    String ACTION_HUNTER_CUSTOMER_APP_GUIDE_BAR_EXPO = "extension-hunter-cvcustomer-APPguidebarExpo";

    /**
     * 1222.607  C-沟通-待达成抽屉-曝光 <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=2978651&appId=100">...</a>
     */
    String ACTION_GEEK_PENDING_DRAWER_SHOW = "geek-connect-pending-drawer-show";

    String ACTION_GEEK_PILOT_BUBBLE_SHOW = "geek-connect-pilot-bubble-show";

    String ACTION_F2_SEARCH_RESULT_SHOW = "f2-search-result-show";


    String ACTION_GEEK_MASKING_YELLOW_BAR_SHOW = "geek-connect-message-masking-yellow-bar-exposure-show";

    String ACTION_GEEK_MASKING_YELLOW_BAR_CLICK = "geek-connect-message-masking-yellow-bar-click-click";


    String ACTION_USER_FEEDBACK_FIXED_SHOW = "userfeedback-OnlineService-fixed-evaluation-show";

    String ACTION_USER_FEEDBACK_TREE_POP_CLICK = "userfeedback-OnlineService-tree_pop-click";

    /**
     * 1224.500
     */
    String ACTION_PROMOTE_CONSUME_TIP_SHOW = "extension-hunter-cvcustomer-f2-prompt-expose";
    String ACTION_PROMOTE_CONSUME_TIP_CLICK = "extension-hunter-cvcustomer-f2-prompt-click";
    String ACTION_GREETING_CONFIRM_CLICK = "greeting-confirm-click";
    String ACTION_GREETING_CONFIRM_SHOW = "greeting-confirm";

    String ACTION_HUNTER_AIAGENT_CHAT = "extension-hunter-cvcustomer-app-aiagent-chat";
    String ACTION_HUNTER_AIAGENT_FEEDBACK = "extension-hunter-cvcustomer-app-aiagent-feedback";

    String ACTION_HUNTER_AIAGENT_REPLY = "extension-hunter-cvcustomer-app-aiagent-reply";
    String ACTION_HUNTER_AIAGENT_DISLIKE_REASON = "extension-hunter-cvcustomer-app-aiagent-dislike-reason";

    String ACTION_WIDGET_SHOW = "widget-show";

    String ACTION_WIDGET_CLICK = "widget-click";

    String ACTION_WIDGET_GUIDE_CLICK = "widget-guide-click";

    String ACTION_WIDGET_GUIDE_SHOW = "widget-guide-dialog-show";

    String ACTION_WIDGET_GUIDE_TIP_SHOW = "widget-guide-tip-show";

    String ACTION_GEEK_CONNECT_RECOMMENDATION_REASON_SHOW = "geek-connect-recommendation-reason-show";

    String ACTION_GEEK_CONNECT_RECOMMENDATION_REASON_CLICK = "geek-connect-recommendation-reason-click";

    String ACTION_BOSS_CHAT_QUICKREPLY_CLICK = "Boss-chat-quickreply-click";
    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&showdetail=3441535&appId=100">...</a>
     */
    String ACTION_CHAT_INTERVIEW_POP_SHOW = "chat-interview-pop-expo";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?version=13.03&stakeholder=7507&region=ml&showdetail=3594008&appId=100">...</a>
     */
    String ACTION_CHAT_INTERACTION_CLICK = "ai-chat-interaction-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?version=13.03&stakeholder=7507&region=ml&showdetail=3593980&appId=100">...</a>
     */
    String ACTION_CHAT_INTERACTION_EXPO = "ai-chat-interaction-expo";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=3593635&region=ml&appId=100">...</a>
     */
    String ACTION_QUICK_SET_EXPO = "ai-chat-quickset-expo";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=3593882&region=ml&appId=100">...</a>
     */
    String ACTION_QUICK_SET_CLICK = "ai-chat-quickset-click";


    String ACTION_BOSS_AI_QUICKRECRUITMENT_FUNCTION_CLICK = "Boss-AI-quickrecruitment-function-click";

    String ACTION_BOSS_AI_QUICKRECRUITMENT_SWITCH_JOB_CLICK = "Boss-AI-quickrecruitment-switch-job-click";

    String ACTION_BOSS_AI_QUICKRECRUITMENT_TIME_CONFIRM_CLICK = "Boss-AI-quickrecruitment-time-confirm-click";

    String ACTION_BOSS_AI_QUICKRECRUITMENT_SEND_MESSAGE_CLICK = "Boss-AI-quickrecruitment-send-message-click";
    String ACTION_BELOW_BUTTON_EXPO = "zhs-belowbutton-exposure";

    String ACTION_CUSTOMER_BOTTOM_TOOLBAR_EXPO = "customer-bottomtoolbar-exposure";

    String ACTION_CUSTOMER_PHONE_POPUP_WINDOW_EXPO = "customer-phonepopupwindow-exposure";

    String ACTION_CUSTOMER_MESSAGE_BODY_NO_SEND_MESSAGE_CLICK = "zhs-messagebody-nosendmessage-click";

    String ACTION_CUSTOMER_MESSAGE_BODY_SEND_MESSAGE_CLICK = "zhs-messagebody-sendmessage-click";

    String ACTION_CUSTOMER_MESSAGE_BODY_NOUSE_CARD_CLICK = "system-zhs-nouse-cardclick-app";

    String ACTION_CUSTOMER_BOTTOM_TOOLBAR_CLICK = "zhs-belowbutton-click";

    String ACTION_CUSTOMER_BOTTOM_TOOLBAR_ENTRANCE_CLICK = "customer-bottomtoolbar-entrance-click";

    String ACTION_ROBOT_COMMON_QUESTIONS_CLICK = "robot-commonquestions-click";

    String ACTION_CUSTOMER_EMOJISBUTTON_CLICK = "customer-emojisbutton-click";

    String ACTION_CUSTOMER_INPUT_ASSOCIATION_CLICK = "zhs-inputassociation-click";

    String ACTION_AI_FAST_SEARCH_NEW_PAGE_EXPOSURE = "AI-fast-search-new-page-exposure";

    String ACTION_AI_QUICK_SEARCH_PAGE_FUNCTION_BAR_CLICK = "AI-quick-search-page-function-bar-click";

    String ACTION_AI_FAST_SEARCH_USER_FEEDBACK = "AI-fast-search-user-feedback";

    String ACTION_AI_FAST_SEARCH_JOB_LIST_EXPOSED = "AI-fast-search-job-list-exposed";

    String ACTION_AI_QUICK_SEARCH_JOB_LIST_CLICK = "AI-quick-search-job-list-click";

    String ACTION_RECORD_CHASE_EXPOSURE = "record-chase";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=3683531&region=ml&appId=100">...</a>
     */
    String ACTION_CHAT_GUIDE = "chat-guidebar";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=3683578&region=ml&appId=100">...</a>
     */
    String ACTION_CHAT_GUIDE_CLICK = "chat-guidebar-click";

    String ACTION_USERFEEDBACK_CUSTOMER_SERVICE_PAGE_SHOW = "Userfeedback-customer-service-page-show";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?version=13.07&stakeholder=7507&region=ml&showdetail=3969135&appId=100">...</a>
     */
    String ACTION_CHAT_AI_REPLY_SHOW = "chat-AI-reply-show";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?version=13.07&stakeholder=7507&region=ml&showdetail=3969219&appId=100">...</a>
     */
    String ACTION_AI_REPLY_CLICK = "chat-AI-reply-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?version=13.07&stakeholder=7507&region=ml&showdetail=3969172&appId=100">...</a>
     */
    String ACTION_AI_REPLY_RESULT_SHOW = "chat-AI-reply-result-show";

    /**
     * AI招聘页面，“创建新对话”被点击时，打此点
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=3933224">...</a>
     */
    String ACTION_BOSS_JOB_AI_NEW_CONVERSATION_CLICK = "Boss-job-AIjob-page-new-conversation-click";
    /**
     * AI招聘页面，消息层按钮曝光时，打此点
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=3933288">...</a>
     */
    String ACTION_BOSS_JOB_AI_MESSAGE_BUTTON_SHOW = "Boss-job-AIjob-page-message-button-show";

    /**
     * B-职位-AI招聘页消息层按钮-点击
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=3933352">...</a>
     */
    String ACTION_BOSS_JOB_AI_MESSAGE_BUTTON_CLICK = "Boss-job-AIjob-page-message-button-click";

    /**
     * AI招聘页面，推荐职类名称曝光时，打此点
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?region=ml&appId=100&showdetail=3933055">...</a>
     */
    String ACTION_BOSS_JOB_AI_RECOMMEND_POSITION_SHOW = "Boss-job-AIjob-page-recommend-position-show";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?condition=action-exchange-contact-click&region=ml&showdetail=4001805&appId=100">...</a>
     */
    String ACTION_BOSS_SINGLE_CHAT_EXCHANGE_EXPOSURE = "bosst-singlechat-exchange-exposure";
    String ACTION_BOSS_SINGLE_CHAT_EXCHANGE_CLICK = "bosst-singlechat-exchange-click";

    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=3996681&region=ml&appId=100">...</a>
     */
    String ACTION_RESUME_AI_KEYPOINT_RESTART = "resume-attachment-ai-keypoint-restart";
    String ACTION_RESUME_AI_KEYPOINT_FEEDBACK_POP_CLICK = "resume-attachment-ai-keypoint-pop-click";
    String ACTION_RESUME_AI_KEYPOINT_COPY = "resume-attachment-ai-keypoint-copy";
    String ACTION_RESUME_AI_KEYPOINT_FEEDBACK = "resume-attachment-ai-keypoint-feedback";

    String ACTION_QUICK_PROCESS_CARD_CLICK = "quick-process-card";

    String ACTION_EXCHANGE_CONFIRMATION_WINDOW_CLICK = "exchange-confirmation-window-click";

    String ACTION_EXCHANGE_CONFIRMATION_WINDOW_EXPOSURE = "exchange-confirmation-window-exposure";

    String ACTION_AI_QUICK_RECRUITMENT_PAGE_EXPOSE = "AI-quickrecruitment-page-expose";
    /**
     * <a href="https://datastar.kanzhun-inc.com/warlock/metamanage/meta/event/100?showdetail=4358194&region=ml&appId=100">...</a>
     */
    String ACTION_AI_TALK_STATUS_CLICK = "AI-B_AI_talk-status-click";
    String ACTION_AI_TALK_STATUS_SHOW = "AI-B_AI_talk-status-show";

    String B_AI_TALK_ASSISTANT_SET_SHOW = "AI-B_AI_talk-assistant-set-show";
    String B_AI_TALK_ASSISTANT_SET_CLICK = "AI-B_AI_talk-assistant-set-click";
    String AI_CHAT_PROCESS_CLICK = "ai-chat-progress-click";
    String AI_CHAT_PROCESS_EXPOSE = "ai-chat-progress-expose";
}
