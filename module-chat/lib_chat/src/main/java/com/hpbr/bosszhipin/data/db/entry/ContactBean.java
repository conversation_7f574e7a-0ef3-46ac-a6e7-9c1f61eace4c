package com.hpbr.bosszhipin.data.db.entry;


import android.annotation.SuppressLint;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.event.ListAnalyticsSwitch;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.interview.entity.ServerInterviewDetailBean;
import com.monch.lbase.orm.db.annotation.Ignore;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;
import com.twl.utils.StringUtils;

import net.bosszhipin.api.bean.ServerAddFriendBean;
import net.bosszhipin.api.bean.WarningTipsBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by monch on 15/4/1.
 */
@Table("Contact")
@ListAnalyticsSwitch(key = {"myId", "friendId", "myRole"}, params = {"friend_id:friendId", "chat_relation:userFromTitle",
        "expectId:jobIntentId", "jobId:jobId"
        , "last_updatetime:lastChatTime", "red_num:noneReadCount", "friend_name:friendName", "is_group_chat:isGroup", "lid:lid"})
public class ContactBean extends BaseEntityAuto {

    private static final long serialVersionUID = -1;

    /**
     * 开关:新回复
     * 会话里有我发出去的message，并且有我未读的message的时候才显示
     */
    public static final byte SWITCH_NEW_REPLY = 0x01;

    /**
     * 我的ID
     */
    public long myId;
//    /**
//     * 聊天创建的时间
//     */
//    public long createTime;
    /**
     * 基于哪个职位ID开聊
     */
    public long jobId;
    /**
     * 基于哪个求职意向ID开聊
     */
    public long jobIntentId;
    /**
     * 好友是BOSS时，BOSS公司的名称
     */
    public String bossCompanyName;
    /**
     * 是否将好友加入黑名单
     */
    public boolean isBlack;
    /**
     * 好友的ID
     */
    public long friendId;
    /**
     * 申请交换电话的时间
     */
    public long exchangePhoneTime;
    /**
     * 好友的电话
     */
    public String friendPhone;

    /**
     * 境外号码
     * :wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
     */
    public String regionCode;

    /**
     * 申请交换微信号的时间
     */
    public long exchangeWxNumberTime;
    /**
     * 好友的微信号
     */
    public String friendWxNumber;
    /**
     * 我聊天时的角色
     */
    public int myRole;
    /**
     * 注意,这个是Boss的职务名称
     */
    public String geekPositionName;
    /**
     * 这个才是牛人的期望名称
     */
    public String expectPositionName;
    /**
     * 与联系人沟通的职位名称
     */
    public String bossJobPosition;

    /**
     * 用户来源
     */
    public String userFromTitle;

    public int itemType;//道具来源，用于f2抽屉分组

    public String greetingText;

    /**
     * 好友的名字
     */
    public String friendName;
    /**
     * 性别
     */
    public int gender;
    /**
     * 年龄
     */
    public int age;
    /**
     * 好友默认头像的下标
     */
    public int friendDefaultAvatarIndex;
    /**
     * 好友默认头像
     */
    public String friendDefaultAvatar;
    /**
     * 未读消息数量
     */
    public int noneReadCount;
    /**
     * 最后一条消息的mid;最后一条成功消息的id（可能是别人发的，可能是自己发的）
     */
    public long lastMsgId;
    /**
     * 最后聊天时间
     */
    public long lastChatTime;
    /**
     * 最后聊天文本
     */
    public String lastChatText;
    /**
     * 最后聊天消息状态，-1为好友发送，0为发送中，1为发送成功，2为发送失败，3为已读
     */
    public int lastChatStatus = -1;
    /**
     * 最后聊天消息的消息ID
     */
    public long lastChatClientMessageId = -1L;
    /**
     * 联系人在服务端的更新时间
     */
    public long updateTime;
    /**
     * 我输入的草稿
     */
    public String RoughDraft;


    /**
     * 引用消息id
     */
    public long quoteMessageId;

    /**
     * 809版本新增沟通助手 只有Boss身份有
     */
    public boolean isContactHelper;

    /**
     * 聊天是否被置顶
     */
    public boolean isTop;
    /**
     * 交换附件简历状态 0 为交换，1 交换
     */
    public long exchangeResumeStatus;
    /**
     * 交换附件简历的时间
     */
    public long exchangeAnnexResumeTime;
    /**
     * 简历的地址
     */
    public String exchangeResumeUrl;
    /**
     * 面试邀请的时间
     */
    public long exchangeInterviewTime;

    /**
     * BOSS认证
     */
    public int certification;
    /**
     * 好友是否已被冻结
     */
    public boolean isFreeze;

    /**
     * 合规冻结
     */
    public boolean isPreFreeze;

    /**
     * 好友是否已被拒绝
     */
    public boolean isReject;

    /**
     * 好友是否已被星标（只针对牛人 710）
     */
    public boolean isStar;

    /**
     * 拒绝原因
     */
    public String rejectReason;


    //给好友发送过消息
    public static final int STAGE_HAVE_SEND_TO_FRIEND = 0x0002;

    //好友给我发送过消息
    public static final int STAGE_FRIEND_SEND_TO_ME = 0x0001;

    //双聊状态
    @Deprecated
    public static final int STAGE_CONTACT_EACH_OTHER = 0x0004;

    /**
     * 好友的新状态
     * 0001 -> STAGE_FRIEND_SEND_TO_ME
     * 0010 -> STAGE_HAVE_SEND_TO_FRIEND
     * 0100 -> STAGE_CONTACT_EACH_OTHER
     * 后续双聊 判断 STAGE_FRIEND_SEND_TO_ME && STAGE_HAVE_SEND_TO_FRIEND
     */
    @SuppressLint("twl_type_id_like_not_serialize")
    public int fridendStage;

    public int friendType;// 1 主动开聊 3 被动开聊


    //设置好友的状态
    public void setFriendStageNew(int stage) {
        fridendStage |= stage;
    }


    //好友是否给我 发送过 文本,语音,图片 消息
    public boolean isFriendHaveSendMsgToMe() {
        return ((fridendStage & STAGE_FRIEND_SEND_TO_ME) == STAGE_FRIEND_SEND_TO_ME) || friendType == 3;
    }

    //我是否给好友 发送过 文本,语音,图片 消息
    public boolean isIHaveSendMsgToFriend() {
        return ((fridendStage & STAGE_HAVE_SEND_TO_FRIEND) == STAGE_HAVE_SEND_TO_FRIEND) || friendType == 1;
    }

    public boolean hasFriendStage(int friendStage){
        return (fridendStage & friendStage) == friendStage;
    }

    //是否双聊状态，高位是100，或者011都是双聊
    public boolean isContactEachOther() {

        return (
                (fridendStage & STAGE_CONTACT_EACH_OTHER) == STAGE_CONTACT_EACH_OTHER )
                ||
                (isFriendHaveSendMsgToMe()&&isIHaveSendMsgToFriend()
                );
    }


    public boolean isBossResumeHighLight() {
        return resumeShowStatus == 2;
    }

    public boolean isBossWxPhoneHighLight() {
        return wxwithphoneShowStatus == 2;
    }

    /**
     * 接受到好友的新招呼信息
     */
    public boolean receiveFriendGreetMsg;

    /**
     * 是否是被动开聊的好友,主要是用来标志是否完善了联系人数据
     */
    public boolean isPassive;

    /**
     * 就是两种状态的开关模式
     * 00000000
     * 第0位:新回复是否显示,0隐藏,1显示
     */
    public byte switch1;

    /**
     * 是否需要完善全量信息
     **/
    public boolean isNeedComplete;

    /**
     * 最后一次全量更新时间
     **/
    public long lastRefreshTime;

    /**
     * 加密id
     **/
    public String securityId;


    /**
     * 是否猎头，6.04增加字段
     **/
    @Deprecated
    public boolean isHeadhunter;

    /**
     * 电话预授权状态 (c对b预授权)0 禁用 1.待授权 2.已授权
     */
    @Deprecated //12.15下线
    public int phoneAuthStatus;

    /**
     * 微信预授权状态 (c对b预授权) 0 禁用 1.待授权 2.已授权
     */
    @Deprecated //12.15下线
    public int weiXinAuthStatus = 0;
    /**
     * 0 禁用对应着交换按钮置灰，1对应着交换按钮高亮可点击交换，2对应着牛人已经授权过交换按钮出现闪电标志
     */
    @Deprecated //12.15下线
    public int resumeAuthStatus;

    /**
     * 电话预授权状态 (b对c预授权)0 禁用 1.待授权 2.已授权
     */
    @Deprecated //12.15下线
    public int bPhoneAuthStatus = 0;

    /**
     * 微信预授权状态 (b对c预授权) 0 禁用 1.待授权 2.已授权
     */
    @Deprecated //12.15下线
    public int bWeiXinAuthStatus = 0;

    /**
     * 用于自定义排序
     * 优先使用{@link #lastChatTime}
     * 否则使用{@link #updateTime}
     */
    public transient long compareSort;

    /**
     * 推荐排序
     */
    @Ignore
    public transient long recommendSort;

    /**
     * 8.06版本视频简历
     */
    public String videoResumeUrl;

    /**
     * 是否群聊
     */
    public transient boolean isGroup;
    /**
     * 默认群聊
     * {@link #groupType}
     */
    public static final int DEFAULT_GROUP = 0;
    /**
     * 引力波群聊
     * {@link #groupType}
     */
    public static final int GRAVITATION_WAVE_GROUP = 1;

    /**
     * 悟空项目外包
     */
    public static final int OUTSOURCING_GROUP = 10011;

    /**
     * 「羚羊计划」群聊
     */
    public static final int ANTELOPE_GROUP = 10012;

    /**
     * 「简历付费辅导」群聊
     */
    public static final int RESUME_ADVICE_GROUP = 10013;

    /**
     * GET 双叶草
     * {@link #groupType}
     */
    public static final int GET_DIRECT_GROUP = 2;

    /**
     * {@link #isGroup()==true时候}
     * {@link #DEFAULT_GROUP}
     * {@link #GRAVITATION_WAVE_GROUP}
     * {@link ContactBean#GET_DIRECT_GROUP}
     */
    public transient int groupType = DEFAULT_GROUP;

    public transient String gravitationName;

    /**
     * 【技术类牛人】今日还有n次免费机会继续沟通"
     */
    public String chatMsgBlockHeadBar;


    public boolean isGroup() {
        return isGroup;
    }

    /**
     * 是否有人@我
     */
    public transient boolean isAtMe;

    /**
     * 这个会话是否受技术牛人阻断 受阻断在发送消息前需要请求block.preCheckChat接口 6.14新增
     */
    public boolean isTechGeekBlock;//

    /**
     * 跳转直聘协议URL|网页URL
     */
    public String zpUrl;

    /**
     * // 7.04新增friendCompanies字段，用于表示该好友加入过的公司（可能多个）。
     */
    public String friendCompanies; // 如：百度#&#腾讯#&#搜狗

    /**
     * 语音视频面试时间
     */
    public long appointmentTime;

    /**
     * f2显示 预约14：00视频
     */
    public String videoInterviewF2Text;
    /**
     * f2显示 预约14：00进行视频面试
     */
    public String videoInterviewChatTopText;

    public boolean highGeek;//是否高端牛人免打扰 7.18需求

    public int jobSource;

    //小圆点，免打扰
    public static final int NONE_READ_SILENT = 1;
    //显示未读数量
    public static final int NONE_READ_COUNT = 0;
    /**
     * 客户端自己定义的 ,非服务器字段
     *
     * @see #NONE_READ_COUNT
     * @see #NONE_READ_SILENT
     */
    public transient int noneReadType = NONE_READ_COUNT;

    //客户端自己定义的，非服务器字段
    public transient int avatarResource;


    /**
     * 联系人消息的交换状态 618
     */
    public int messageExchangeIcon;


    /**
     * 是否提示过 【电话联系TA】的对话框提示
     */
    public boolean hasShowExchangePhoneTip;


    /**
     * 平台调研
     */
    public boolean isPlatFromResearch;

    /**
     * 校招ATS
     */
    public boolean ats;


    /**
     * 提升消息曝光
     */
    public boolean improveMessageExposure;


    /**
     * 是否是猎头（仅适用于猎头/中介开关的区分）
     */
    public boolean isHunter;

    /**
     * 是否是中介（仅适用于猎头/中介开关的区分）
     */
    public boolean isAgency;
    public int teamMemberSize; // 大于1表示该牛人与其他牛人组队求职,  1105.85【B&C】提供部分蓝领岗位组队求职功能

    public String workYear;//工作年限 xxx年 如果是学生 该字段为xx应届生等信息
    public String degree;//学历
    public String expectSalary;//期望薪资
    public String workDesc;//牛人最后一份工作经历 如果是学生 填学校信息
    public int invalidJob;//职位状态 0 正常 1停止招聘 c端逻辑

    @Deprecated
    public String goldInterviewer;// 已下架：金牌面试官 1


    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=227138495
    public List<WarningTipsBean> warningTips;

    // 1303.111 【招聘者】消息列表卡片信息展示优化
    public int positionMarkType; // 职类标记信息 11:"白领", 21:"普蓝", 22:"深蓝" 23:"锐蓝"
    // 1308.165【B&C】AI帮你推
    public int aiDirectChat;

    public long waterLevel; // 用户水位信息

    /**
     * 判断开关1
     *
     * @param b
     * @return
     */
    public boolean isSwitch1Open(byte b) {
        return (switch1 & b) == b;
    }


    /**
     * https://api.weizhipin.com/project/30/interface/api/784
     * 10.07添加是否收藏联系人字段
     * 与李俊确认过 该字段已废弃,服务器不下发
     */
    @Deprecated
    public String starTypes;

    /**
     * 设置开关
     *
     * @param open
     * @param b
     */
    public void setSwitch1Open(boolean open, byte b) {
        if (open && !isSwitch1Open(b)) {
            switch1 |= b;
        } else if (!open && isSwitch1Open(b)) {
            switch1 ^= b;
        }
    }

    /**
     * 是否有视频约面试
     *
     * @return
     */
    public boolean hasVideoInterview() {
        return !LText.empty(videoInterviewF2Text) || !LText.empty(videoInterviewChatTopText);
    }


    /**
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=202984593
     * 牛人创建面试日程
     * {@link ServerInterviewDetailBean#STATUS_GEEK_ADD_GREATER_THAN_3_HOUR}
     * {@link ServerInterviewDetailBean#STATUS_GEEK_ADD_LESS_THAN_3_HOUR}
     * {@link ServerInterviewDetailBean #STATUS_GEEK_ADD_START}
     * {@link ServerInterviewDetailBean#STATUS_GEEK_ADD_END}
     */
    public int interviewScheduleStatus;

    /**
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=202984593
     * 面试日程协议
     */
    public String interviewScheduleProtocol;

    /**
     * 面试日程时间
     */
    public long interviewScheduleTime;


    // 从这里开始是约面试所用字段
    public int currentInterviewStatus = -1; // 当前面试状态

    public String currentInterviewDesc; // 当前面试状态描述
    public String currentInterviewProtocol; // 当前面试的协议

    public String interviewDateStr;//聊天二期面试添加具体时间
    public String interviewTimeStr;//聊天二期面试添加具体时间

    //消息列表是否已经显示过面试状态【面试超时】【面试取消】  currentInterviewStatus
    public boolean hasJumpToChat;


    /********begin 创建面试提醒：5.49开始这几个字段应该可以废弃掉了，为了保险起见，先保留引用**/
    @Deprecated
    public String createInterviewText;      // 创建约面试的提示
    @Deprecated
    public String createInterviewProtocol;    // 创建约面试的协议
    @Deprecated
    public long createInterviewTimeout;     // 创建约面试的超时时间
    /********end 创建面试提醒：5.49开始这几个字段应该可以废弃掉了，为了保险起见，先保留引用**/


    // 用于F1，属于本地参数，无网络解析
    public String lid;


    //是否被过滤
    public boolean isFiltered;


    public int friendSource;//0是之前的 1是店长

    @Deprecated
    public int partJobStrategy;// 1是交换合并，0是不合并

    public static final int FROM_BOSS = 0;
    public static final int FROM_DIAN_ZHANG = 1;

    /*「常用语」Tab*/
    public static final int SELECT_TAB_CHAT_COMMON = 1;
    /*「招呼语」Tab*/
    public static final int SELECT_TAB_GREETING_WORDS = 2;

    //被过滤的原因
    public List<String> filterReasonList;

    public String blackDesc;//7.13 拉黑原因描述

    public String positionName;//7.15新招呼和沟通中需要显示

    public String salaryDesc;//7.15新招呼和沟通中需要显示

    public long lowSalary; // 1217.921
    public long highSalary;// 1217.921

    public String workplace;//7.15新招呼和沟通中需要显示

    public int directCallStatus;//1 电话未接通  2 未接电话  3 通话成功


    public boolean isWxRemind;//是否开启微信提示

    public int itemSource;  // 是否是道具来源 0否 1 是

    /**
     * 是否人才经纪人，720
     **/
    public boolean isAgentRecruit;

    /**
     * 加好友时间 Date类型
     */
    public long addTime;

    /**
     * 最后一次删除好友时间 Date类型
     */
    public long delTime;

    /**
     * https://zhishu.zhipin.com/wiki/R7m0kdanM7q
     * 多位 位运算表示 (iconFlag & 1) != 0 则代表top300
     *
     */
    public int iconFlag;

    /**
     * 是否符合快速处理
     *
     * @return
     */
    public boolean isCanFastHandler() {
        if (myRole == ROLE.BOSS.get()) {
            return noneReadCount > 0;
        } else if (myRole == ROLE.GEEK.get()) {
            return noneReadCount > 0;
        }
        return false;
    }

    /**
     * 人才经纪人
     *
     * @return
     */
    public boolean isRecruiter() {
        return isAgentRecruit || isHeadhunter;
    }


    public int goldGeekStatus;//// 金牛标志 0 否 1是

    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=167077528
    // 标签 多个标签以 #&# 分隔 服务器客户端统一用这个规则
    public String labels;

    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=167077528
    // 备注
    public String note;


    public String freezeInfo;

    public String preFreezeInfo;


    public int noDisturb;// 免打扰状态 1开启 0 未开启 1014.59

    /**
     * 1222.607 仅本地使用，baseInfo/fullInfo接口不下发
     * 1：请求简历，2：请求电话，3：请求微信。
     * 默认为0
     */
    public int askResumePhoneWx;

    /**
     * 是否批量追聊
     *
     * @return
     */
    public boolean isBatchChatBack() {
        return itemType == 43;
    }

    /**
     * 0:无状态 1:置灰 2:点亮
     */
    @Ignore
    public transient int resumeShowStatus;
    /**
     * 0:无状态 1:置灰 2:点亮
     */
    @Ignore
    public transient int wxwithphoneShowStatus;

    @Ignore
    public transient int hkApplyJob;

    //1223.650 标记是不是拒绝了交换，只有收到服务端type=1，templateId=14的时候
    public boolean isRejectExchange;

    public String jobTypeDesc; //职位性质

    public String jobCity; //城市

    @Ignore
    public transient String recommendReason; // 1226.607 联系人列表页推荐理由

    @Ignore
    public transient boolean highlightExchange;

    public boolean isHkApplyJob(){
        return hkApplyJob==1;
    }

    private static boolean hasReport;//是否已经上报


    public boolean isOpenNoDisturb() {
        return noDisturb == 1;
    }

    /**
     * 将参数2的值赋给参数1
     *
     * @param bean2
     * @return
     */
    public ContactBean cloneBean(ContactBean bean2) {
        if (bean2 != null) {

            //优化线上用户 联系人 lastChatText偶尔为空，根据apm排出这个位置null覆盖了,一周有48个用户左右
            if (!LText.empty(bean2.lastChatText)) {
                lastChatText = bean2.lastChatText;//
            }

            id = bean2.id;
            myId = bean2.myId;
            myRole = bean2.myRole;
//          createTime = bean2.createTime;
            jobId = bean2.jobId;
            jobIntentId = bean2.jobIntentId;
            jobSource = bean2.jobSource;
            bossCompanyName = bean2.bossCompanyName;
            isHunter = bean2.isHunter;
            isAgency = bean2.isAgency;
            isBlack = bean2.isBlack;
            friendId = bean2.friendId;
            exchangePhoneTime = bean2.exchangePhoneTime;//
            friendPhone = bean2.friendPhone;
            regionCode = bean2.regionCode;
            exchangeWxNumberTime = bean2.exchangeWxNumberTime;//
            friendWxNumber = bean2.friendWxNumber;
            geekPositionName = bean2.geekPositionName;
            friendName = bean2.friendName;
            age = bean2.age;
            gender = bean2.gender;
            friendDefaultAvatarIndex = bean2.friendDefaultAvatarIndex;
            friendDefaultAvatar = bean2.friendDefaultAvatar;
            teamMemberSize = bean2.teamMemberSize;
            itemSource = bean2.itemSource;
            noneReadCount = bean2.noneReadCount;//
            lastChatTime = bean2.lastChatTime;//
            lastMsgId = bean2.lastMsgId;//

            starTypes = bean2.starTypes;
            quoteMessageId = bean2.quoteMessageId;
            certification = bean2.certification;
            goldGeekStatus = bean2.goldGeekStatus;
            lowSalary = bean2.lowSalary;
            highSalary = bean2.highSalary;

            if (bean2.lastChatStatus > lastChatStatus) {
                // 当状态值
                lastChatStatus = bean2.lastChatStatus;//
            }
            lastChatClientMessageId = bean2.lastChatClientMessageId;//
            updateTime = bean2.updateTime;
            RoughDraft = bean2.RoughDraft;//
            isTop = bean2.isTop;
            exchangeResumeUrl = bean2.exchangeResumeUrl;
            exchangeResumeStatus = bean2.exchangeResumeStatus;
            exchangeAnnexResumeTime = bean2.exchangeAnnexResumeTime;//
            exchangeInterviewTime = bean2.exchangeInterviewTime;//
            messageExchangeIcon = bean2.messageExchangeIcon;
            isFreeze = bean2.isFreeze;
            isPreFreeze = bean2.isPreFreeze;
            freezeInfo = bean2.freezeInfo;
            preFreezeInfo = bean2.preFreezeInfo;
            interviewDateStr = bean2.interviewDateStr;
            interviewTimeStr = bean2.interviewTimeStr;

            lid = bean2.lid;
            isReject = bean2.isReject;
            isStar = bean2.isStar;
            rejectReason = bean2.rejectReason;
            fridendStage = bean2.fridendStage;//
            expectPositionName = bean2.expectPositionName;
            bossJobPosition = bean2.bossJobPosition;
            switch1 = bean2.switch1;//
            userFromTitle = bean2.userFromTitle;//
            currentInterviewStatus = bean2.currentInterviewStatus;
            currentInterviewDesc = bean2.currentInterviewDesc;
            currentInterviewProtocol = bean2.currentInterviewProtocol;
            createInterviewText = bean2.createInterviewText;
            createInterviewProtocol = bean2.createInterviewProtocol;
            createInterviewTimeout = bean2.createInterviewTimeout;
            receiveFriendGreetMsg = bean2.receiveFriendGreetMsg;
            directCallStatus = bean2.directCallStatus;

            isNeedComplete = bean2.isNeedComplete;//
            lastRefreshTime = bean2.lastRefreshTime;//

            //存在null覆盖security情况，如果不为空 在覆盖
            if (!LText.empty(bean2.securityId)) {
                securityId = bean2.securityId;
            }

            isHeadhunter = bean2.isHeadhunter;
            chatMsgBlockHeadBar = bean2.chatMsgBlockHeadBar;
            videoInterviewF2Text = bean2.videoInterviewF2Text;
            videoInterviewChatTopText = bean2.videoInterviewChatTopText;
            friendCompanies = bean2.friendCompanies;
            blackDesc = bean2.blackDesc;
            addTime = bean2.addTime;
            delTime = bean2.delTime;
            invalidJob = bean2.invalidJob;
            workYear = bean2.workYear;
            workDesc = bean2.workDesc;
            degree = bean2.degree;
            expectSalary = bean2.expectSalary;
            isRejectExchange = bean2.isRejectExchange;
            askResumePhoneWx = bean2.askResumePhoneWx;
            jobTypeDesc = bean2.jobTypeDesc;
            jobCity = bean2.jobCity;
            recommendReason = bean2.recommendReason;
            highlightExchange = bean2.highlightExchange;
            positionMarkType = bean2.positionMarkType;
            aiDirectChat = bean2.aiDirectChat;
            waterLevel = bean2.waterLevel;
        }
        return this;
    }


    public ContactBean fromServerGeekAddFriendBean(ServerAddFriendBean contactBean, long mMYId, int mMyRole) {
        myId = mMYId;
        myRole = mMyRole;
        friendId = contactBean.getFriendId();
        friendName = contactBean.getName();
        gender = contactBean.getGender();
        age = contactBean.getAge();

        securityId = contactBean.getSecurityId();
        jobId = contactBean.getJobId();
        bossJobPosition = contactBean.getJobName();
        jobIntentId = contactBean.getExpectId();
        jobSource = contactBean.getJobSource();
        currentInterviewStatus = contactBean.getInterviewStatus();
        currentInterviewDesc = contactBean.getInterviewStatusDesc();
        currentInterviewProtocol = contactBean.getInterviewUrl();
        exchangeResumeUrl = contactBean.getResumeUrl();
        chatMsgBlockHeadBar = contactBean.getChatMsgBlockHeadBar();
        friendDefaultAvatar = contactBean.getTinyUrl();

        geekPositionName = contactBean.getTitle();
        userFromTitle = contactBean.getSourceTitle();

        certification = contactBean.getCertification();
        bossCompanyName = contactBean.getCompany();
        expectPositionName = contactBean.getExpectPositionName();
        isTop = contactBean.isTop();
        goldGeekStatus = contactBean.getGoldGeekStatus();
        itemSource = contactBean.getItemSource();
        isHunter = contactBean.isHunter();

        isAgency = contactBean.isAgency();
        teamMemberSize = contactBean.getTeamMemberSize();
        isReject = contactBean.isRejectUser();
        isStar = contactBean.isStar();
        rejectReason = contactBean.getRejectDesc();
        isHeadhunter = contactBean.isHeadhunter();
        friendPhone = contactBean.getPhoneNumber();
        friendWxNumber = contactBean.getWxNumber();
        isBlack = contactBean.isBlack();
        isFreeze = contactBean.isFreeze();
        isTechGeekBlock = contactBean.isTechGeekBlock();
        updateTime = contactBean.getDatetime();
        friendSource = contactBean.getFriendSource();

        positionName = contactBean.getPositionName();
        salaryDesc = contactBean.getSalaryDesc();
        workplace = contactBean.getWorkplace();
        isAgentRecruit = contactBean.isAgentRecruit();
        itemType = contactBean.getItemType();
        noDisturb = contactBean.getNoDisturb();

        isHunter = contactBean.isHunter();

        isAgency = contactBean.isAgency();
        invalidJob = contactBean.getInvalidJob();

        lowSalary = contactBean.getLowSalary();
        highSalary = contactBean.getHighSalary();

        friendType = contactBean.getFriendType();
        jobTypeDesc = contactBean.getJobTypeDesc();
        jobCity = contactBean.getJobCity();

        friendCompanies = StringUtils.connectTextWithChar(LABEL_SPLIT_CHAR, contactBean.getCompanyHistoryList());
        positionMarkType = contactBean.getPositionMarkType();
        degree = contactBean.getDegree();
        expectSalary = contactBean.getExpectSalary();
        aiDirectChat = contactBean.getAiDirectChat();
        // 接口要求 0 不存储
        if (contactBean.getWaterLevel() > 0) {
            waterLevel = contactBean.getWaterLevel();
        }
        return this;

    }


    public ContactBean fromServerBossAddFriendBean(ServerAddFriendBean contactBean, long mMyId, int mMyRole) {
        myId = mMyId;
        myRole = mMyRole;
        friendId = contactBean.getFriendId();
        friendName = contactBean.getName();
        gender = contactBean.getGender();
        age = contactBean.getAge();

        securityId = contactBean.getSecurityId();
        jobId = contactBean.getJobId();

        jobIntentId = contactBean.getExpectId();
        jobSource = contactBean.getJobSource();
        currentInterviewStatus = contactBean.getInterviewStatus();
        currentInterviewDesc = contactBean.getInterviewStatusDesc();
        currentInterviewProtocol = contactBean.getInterviewUrl();
        exchangeResumeUrl = contactBean.getResumeUrl();
        chatMsgBlockHeadBar = contactBean.getChatMsgBlockHeadBar();
        friendDefaultAvatar = contactBean.getTinyUrl();
        bossJobPosition = contactBean.getJobName();
        geekPositionName = contactBean.getTitle();
        userFromTitle = contactBean.getSourceTitle();
        goldGeekStatus = contactBean.getGoldGeekStatus();

        certification = contactBean.getCertification();
        bossCompanyName = contactBean.getCompany();
        expectPositionName = contactBean.getExpectPositionName();
        isTop = contactBean.isTop();
        isReject = contactBean.isRejectUser();
        teamMemberSize = contactBean.getTeamMemberSize();
        isStar = contactBean.isStar();
        rejectReason = contactBean.getRejectDesc();
        isHeadhunter = contactBean.isHeadhunter();
        friendPhone = contactBean.getPhoneNumber();
        friendWxNumber = contactBean.getWxNumber();
        isBlack = contactBean.isBlack();
        isFreeze = contactBean.isFreeze();
        isTechGeekBlock = contactBean.isTechGeekBlock();
        updateTime = contactBean.getDatetime();
        friendSource = contactBean.getFriendSource();
        itemSource = contactBean.getItemSource();
        positionName = contactBean.getPositionName();
        salaryDesc = contactBean.getSalaryDesc();
        workplace = contactBean.getWorkplace();
        isAgentRecruit = contactBean.isAgentRecruit();
        itemType = contactBean.getItemType();
        noDisturb = contactBean.getNoDisturb();
        isHunter = contactBean.isHunter();
        isAgency = contactBean.isAgency();
        invalidJob = contactBean.getInvalidJob();
        friendType = contactBean.getFriendType();

        lowSalary = contactBean.getLowSalary();
        highSalary = contactBean.getHighSalary();

        friendCompanies = StringUtils.connectTextWithChar(LABEL_SPLIT_CHAR, contactBean.getCompanyHistoryList());
        positionMarkType = contactBean.getPositionMarkType();
        expectSalary = contactBean.getExpectSalary();
        degree = contactBean.getDegree();
        aiDirectChat = contactBean.getAiDirectChat();
        // 接口要求 0 不存储
        if (contactBean.getWaterLevel() > 0) {
            waterLevel = contactBean.getWaterLevel();
        }
        return this;
    }

    /**
     * 基础信息
     **/
    public ContactBean fromServerContactBaseInfoBean(ServerAddFriendBean contactBean, long userId, int role) {
        myId = userId;
        myRole = role;
        jobId = contactBean.getJobId();
        jobIntentId = contactBean.getExpectId();
        jobSource = contactBean.getJobSource();
        bossCompanyName = contactBean.getCompany();
        friendId = contactBean.getFriendId();
        highGeek = contactBean.isHighGeek();
        geekPositionName = contactBean.getTitle();
        friendName = contactBean.getName();
        gender = contactBean.getGender();
        age = contactBean.getAge();

        isWxRemind = contactBean.isWxRemind();

        isStar = contactBean.isStar();

        isHunter = contactBean.isHunter();

        isAgency = contactBean.isAgency();

        transferStarTypes(contactBean);

        friendDefaultAvatarIndex = contactBean.getHeadImg();
        friendDefaultAvatar = contactBean.getTinyUrl();

        isTop = contactBean.isTop();
        updateTime = contactBean.getDatetime();
        certification = contactBean.getCertification();
        expectPositionName = contactBean.getExpectPositionName();
        bossJobPosition = contactBean.getJobName();
        userFromTitle = contactBean.getSourceTitle();
        securityId = contactBean.getSecurityId();
        isReject = contactBean.isRejectUser();
        rejectReason = contactBean.getRejectDesc();
        teamMemberSize = contactBean.getTeamMemberSize();
        isStar = contactBean.isStar();
        isNeedComplete = false;//只要是从服务端请求过基础数据，就设置为false
        isHeadhunter = contactBean.isHeadhunter();

        isFiltered = contactBean.isFiltered();
        filterReasonList = contactBean.getFilterReasonList();
        friendSource = contactBean.getFriendSource();
        itemSource = contactBean.getItemSource();
        positionName = contactBean.getPositionName();
        salaryDesc = contactBean.getSalaryDesc();
        workplace = contactBean.getWorkplace();
        goldGeekStatus = contactBean.getGoldGeekStatus();

        isAgentRecruit = contactBean.isAgentRecruit();
        itemType = contactBean.getItemType();

        greetingText = contactBean.getHeadline();
        noDisturb = contactBean.getNoDisturb();

        invalidJob = contactBean.getInvalidJob();
        iconFlag=contactBean.getIconFlag();

        friendType = contactBean.getFriendType();

        lowSalary = contactBean.getLowSalary();
        highSalary = contactBean.getHighSalary();
        jobTypeDesc = contactBean.getJobTypeDesc();
        workDesc = contactBean.getWorkDesc();
        friendCompanies = StringUtils.connectTextWithChar(LABEL_SPLIT_CHAR, contactBean.getCompanyHistoryList());
        positionMarkType = contactBean.getPositionMarkType();
        degree = contactBean.getDegree();
        expectSalary = contactBean.getExpectSalary();
        aiDirectChat = contactBean.getAiDirectChat();

        // 接口要求 0 不存储
        if (contactBean.getWaterLevel() > 0) {
            waterLevel = contactBean.getWaterLevel();
        }

        return this;
    }

    /**
     * 全量的信息
     **/
    public ContactBean fromServerContactFullInfoBean(ServerAddFriendBean contactBean, long userId, int role) {
        fromServerContactBaseInfoBean(contactBean, userId, role);
        note = contactBean.getNote();
        labels = contactBean.getLabels();
        isBlack = contactBean.isBlack();
        friendPhone = contactBean.getPhoneNumber();
        regionCode = contactBean.getRegionCode();
        friendWxNumber = contactBean.getWxNumber();
        highGeek = contactBean.isHighGeek();
        jobSource = contactBean.getJobSource();
        goldGeekStatus = contactBean.getGoldGeekStatus();
        videoResumeUrl = contactBean.getVideoResumeUrl();
        rejectReason = contactBean.getRejectDesc();
        exchangeResumeUrl = contactBean.getResumeUrl();
        exchangeResumeStatus = contactBean.getResumeStatus();
        currentInterviewStatus = contactBean.getInterviewStatus();
        currentInterviewDesc = contactBean.getInterviewStatusDesc();
        currentInterviewProtocol = contactBean.getInterviewUrl();
        isNeedComplete = false;//只要是从服务端请求过基础数据，就设置为false
        lastRefreshTime = System.currentTimeMillis();
        securityId = contactBean.getSecurityId();
        isHeadhunter = contactBean.isHeadhunter();
        friendSource = contactBean.getFriendSource();
        isTechGeekBlock = contactBean.isTechGeekBlock();
        chatMsgBlockHeadBar = contactBean.getChatMsgBlockHeadBar();
        isFiltered = contactBean.isFiltered();
        filterReasonList = contactBean.getFilterReasonList();
        blackDesc = contactBean.getBlackDesc();
        isStar = contactBean.isStar();
        goldInterviewer = contactBean.getGoldInterviewer();

        transferStarTypes(contactBean);

        isAgentRecruit = contactBean.isAgentRecruit();
        goldGeekStatus = contactBean.getGoldGeekStatus();
        itemType = contactBean.getItemType();

        addTime = contactBean.getAddTime();
        delTime = contactBean.getDelTime();

        isFreeze = contactBean.isFreeze();
        isPreFreeze = contactBean.isPreFreeze();
        freezeInfo = contactBean.getFreezeInfo();
        preFreezeInfo = contactBean.getPreFreezeInfo();

        interviewDateStr = contactBean.getInterviewDateStr();
        interviewTimeStr = contactBean.getInterviewTimeStr();
        workYear = contactBean.getWorkYear();
        workDesc = contactBean.getWorkDesc();
        degree = contactBean.getDegree();
        expectSalary = contactBean.getExpectSalary();
        warningTips = contactBean.getWaringTips();
        iconFlag=contactBean.getIconFlag();

        friendType = contactBean.getFriendType();
        jobTypeDesc = contactBean.getJobTypeDesc();
        jobCity = contactBean.getJobCity();
        positionMarkType = contactBean.getPositionMarkType();
        aiDirectChat = contactBean.getAiDirectChat();
        return this;
    }

    private void transferStarTypes(@NonNull ServerAddFriendBean contactBean) {
        starTypes = "";
        if (contactBean.getStar() != null) {
            StringBuilder StringBuilder = new StringBuilder();
            for (String value : contactBean.getStar()) {
                if (LText.empty(value)) continue;
                StringBuilder.append(value);
                StringBuilder.append(",");
            }
            starTypes = StringBuilder.toString();
        }
    }

    public void addFullInfoData(ContactBean local) {
//        id = local.id;
        currentInterviewStatus = local.currentInterviewStatus;
        currentInterviewDesc = local.currentInterviewDesc;
        currentInterviewProtocol = local.currentInterviewProtocol;
        isBlack = local.isBlack;
        friendPhone = local.friendPhone;
        regionCode = local.regionCode;
        if (!AndroidDataStarGray.getInstance().isIgnoreCompanyUpdate()) {
            friendCompanies = local.friendCompanies;
        }
        friendWxNumber = local.friendWxNumber;
        isFreeze = local.isFreeze;
        directCallStatus = local.directCallStatus;
        freezeInfo = local.freezeInfo;
        preFreezeInfo = local.preFreezeInfo;
        exchangeResumeStatus = local.exchangeResumeStatus;
        addTime = local.addTime;
        delTime = local.delTime;
        interviewDateStr = local.interviewDateStr;
        interviewTimeStr = local.interviewTimeStr;
        workYear = local.workYear;
        workDesc = local.workDesc;
        degree = local.degree;
        expectSalary = local.expectSalary;
        labels=local.labels;
        note=local.note;

        chatMsgBlockHeadBar = local.chatMsgBlockHeadBar;
        isTechGeekBlock = local.isTechGeekBlock;
        positionMarkType = local.positionMarkType;
        aiDirectChat = local.aiDirectChat;
    }


    @Override
    public String toString() {
        return GsonUtils.getGson().toJson(this);
    }

    public boolean needRequestFullInfo() {
        return System.currentTimeMillis() - lastRefreshTime > FULL_INFO_REFRESH;
    }

    public static final int FULL_INFO_REFRESH = 3 * 60 * 60 * 1000;


    public interface GroupType {

        int ALL = 0;

        int NEW_GREETING = 1;

        int ONLY_CONTACT = 2;

        int EXCHANGE_MESSAGE = 3;

        int HAVE_INTERVIEW = 4;

        int UNREAD = 5;

    }

    /**
     * 获得当前联系人的分组类型
     *
     * @return GroupType
     */
    public int getGroupType() {

        //双聊
        if (isContactEachOther()) {
            //有面试 约了面试且接受的联系人，不包括尚未接受和超时未接受和取消的情况；
            if (currentInterviewStatus == 1 ||
                    currentInterviewStatus == 4 ||
                    currentInterviewStatus == 7 ||
                    currentInterviewStatus == 17) {
                return ContactBean.GroupType.HAVE_INTERVIEW;
            }
            //交换信息 有达成行为（简历、电话、微信）的联系人中， 除掉有面试的；
            if (!LText.empty(friendPhone)
                    || !LText.empty(friendWxNumber)
                    || !LText.empty(exchangeResumeUrl)
                    || exchangeResumeStatus == 1) {
                return ContactBean.GroupType.EXCHANGE_MESSAGE;
            }
        }

        //仅沟通 我主动沟通 + 双聊联系人中， 除掉有交换、有面试、有录用的
        if (isIHaveSendMsgToFriend() || isContactEachOther() || isGeekWaitAchieve()) {
            return ContactBean.GroupType.ONLY_CONTACT;
        }

        //新招呼 对方发起的新招呼（与现有逻辑一致）//////  (fridendStage == 0 && lastChatTime > 0)  兜底处理
        //1223.650 排除掉产生拒绝行为的情况
        if ((isFriendHaveSendMsgToMe() || (fridendStage == 0 && lastChatTime > 0)) && !isRejectExchange) {
            return ContactBean.GroupType.NEW_GREETING;
        }

        return ContactBean.GroupType.ALL;
    }


    //主动开聊 我给好友发送过消息&&不是双聊
    public boolean isInitiativeConservation() {
        return !isContactEachOther() && isIHaveSendMsgToFriend();
    }


    //被动开聊 好友给我发送过消息&&不是双聊
    public boolean isPassiveConservation() {
        return !isContactEachOther() && !isIHaveSendMsgToFriend();
    }

    public boolean isGeekWaitAchieve() {
        // GEEK_WAIT_ACHIEVE 本地联系人
        return friendId == Long.MAX_VALUE - 33;
    }

    public static final String LABEL_SPLIT_CHAR = "#&#";

    //获得转化后的labels字符串内容
    public @Nullable
    List<String> getLabelsCollection() {
        if (!LText.empty(labels)) {

            //服务器约定是 #&# 用这个拼接
            if (labels.contains(LABEL_SPLIT_CHAR)) {
                String[] split = labels.split(LABEL_SPLIT_CHAR);
                if (split != null) {
                    return Arrays.asList(split);
                }
            }

            //客户端部分之前本地用","分隔
            if (labels.contains(",")) {
                String[] split = labels.split(",");
                if (split != null) {
                    return Arrays.asList(split);
                }
            }


            List<String> value = new ArrayList<>();
            value.add(labels);
            return value;

        }
        return null;
    }


    public void setLabelsFormatter(@Nullable List<String> mLabels) {
        labels = getLabelsFormatter(mLabels);
    }


    public String getLabelsFormatter(@Nullable List<String> mLabels) {
        if (mLabels != null && LList.getCount(mLabels) > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            for (String label : mLabels) {
                if (LText.empty(label)) continue;
                stringBuilder.append(label);
                stringBuilder.append(LABEL_SPLIT_CHAR);
            }
            if (stringBuilder.toString().endsWith(LABEL_SPLIT_CHAR)) {
                int length = LABEL_SPLIT_CHAR.length();
                int start = stringBuilder.length() - length;
                int end = stringBuilder.length();
                stringBuilder.delete(start, end);
            }
            return stringBuilder.toString();
        }
        return "";
    }

    public long getShowTime() {
        long dateTime = 0;
        if (lastChatTime > 0) {
            dateTime = lastChatTime;
        } else if (updateTime > 0) {
            dateTime = updateTime;
        }
        return dateTime;
    }


    public boolean isAIRecruitUnSupport() {
        if (noneReadCount <= 0
                || isIHaveSendMsgToFriend()
                || isReject
                || LText.empty(securityId)
                || friendSource == ContactBean.FROM_DIAN_ZHANG) {
            return true;
        }
        return false;
    }

    public boolean hasExchangePhoneResumeWx() {
        return !LText.empty(friendPhone)
                || !LText.empty(friendWxNumber)
                || !LText.empty(exchangeResumeUrl)
                || exchangeResumeStatus == 1;
    }

    public boolean askForPhoneResumeWx() {
        return exchangePhoneTime > 0 || exchangeAnnexResumeTime > 0 || exchangeWxNumberTime > 0;
    }
}
