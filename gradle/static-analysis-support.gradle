import java.nio.file.Files

ext.isLintCheck = project.hasProperty("lintCheck")
ext.isLintCheckAll = project.hasProperty("lintCheckAll")
ext.isBooster = project.hasProperty("booster")
ext.isCheckR8Log = true
ext.isSpotBugs = project.hasProperty("spotbugs")
ext.isPmd = project.hasProperty("pmd")
gradle.ext.isNeedBooster = (ext.isBooster || gradle.ext.aop_thread) || gradle.ext.local_sandbox || gradle.ext.aop_fresco
if (gradle.ext.local_tjacoco) {
    ext.booster_version = '5.0.0'
} else {
    ext.booster_version = '4.16.3'
}
ext.booster_task_analyser_version = '4.16.3-fix2'
ext.onelint_version = '0.0.5-beta1'
ext.oneapk_version = '1.1.6'

gradle.ext.isOneApt = gradle.ext.oneapt_wmrouter
ext.oneapt_wmrouter_version = '0.0.5-beta1'
println "isOneApt = ${gradle.ext.isOneApt}, oneapt_wmrouter = ${gradle.ext.oneapt_wmrouter}, oneapt_incremental = ${gradle.ext.oneapt_incremental}"


def onBuildDependencies(DependencyHandler dependencyHandler) {
    if (!gradle.ext.jenkinsEnv) {
        dependencyHandler.classpath "com.bzl.plugins:build-apm:0.0.4"
    }
    if (gradle.ext.local_tjacoco) {
        dependencyHandler.classpath "com.bzl.jacoco:jacoco-agp-plugin:7.4.2-bzl-1"
        dependencyHandler.classpath "com.bzl.jacoco:jacoco-plugin:0.0.41"
    }
    dependencyHandler.classpath "com.bzl.plugins:agp_hook:7.4.2-bzl-1"
    dependencyHandler.classpath "com.android.tools:r8:4.0.71"
    dependencyHandler.classpath "com.android.tools.build:gradle:7.4.2"
    dependencyHandler.classpath("com.hihonor.mcs:asplugin:2.0.0") {//荣耀
        exclude group: 'com.android.tools.build'
    }

    // 添加WMRouter插件
    if (gradle.ext.oneapt_wmrouter) {
        dependencyHandler.classpath "com.bzl.plugins.oneapt:wm_router_plugin:0.0.5-beta1"
    } else {
        dependencyHandler.classpath(deps.wmrouter_gradle_plugin) {
            exclude group: 'com.android.tools.build'
        }
    }

    dependencyHandler.classpath "com.bzl.lint:lint-plugin:${ext.onelint_version}"

    if (gradle.ext.isNeedBooster) {
        dependencyHandler.classpath "com.didiglobal.booster:booster-gradle-plugin:$ext.booster_version"
        dependencyHandler.classpath 'com.hpbr.booster.plugin:classVersionChecker:0.0.5-beta2'
    }
    if (gradle.ext.aop_fresco) {
        dependencyHandler.classpath 'com.hpbr.booster.plugin:fresco:0.0.30'
        dependencyHandler.classpath 'com.hpbr.booster.plugin:okhttp3-internal-util-fixer:0.0.1'
    }

    if ((ext.isBooster || gradle.ext.aop_thread)) {
        dependencyHandler.classpath "com.didiglobal.booster:booster-task-analyser:$booster_task_analyser_version"
    }

    if (gradle.ext.local_sandbox) {
        dependencyHandler.classpath "com.hpbr.booster.plugin:sandbox:0.0.53"
    }

//    dependencyHandler.classpath "com.sankuai.waimai.router:plugin:$wmrouter_version"

    if (gradle.ext.tracecanary) {
        dependencyHandler.classpath(deps.matrixplus.plugin) {
            changing = true
            exclude group: 'com.android.tools.build', module: 'gradle'
        }
    }
    if (gradle.ext.leakcanary) {
        dependencyHandler.classpath(deps.leakcanary.plugin) {
            changing = true
            exclude group: 'com.android.tools.build', module: 'gradle'
            exclude group: 'org.jetbrains.kotlin', module: 'kotlin-gradle-plugin'
        }
    }
    if (gradle.ext.onekit) {
        dependencyHandler.classpath "com.bzl.onekit:dokit-plugin:3.7.14.9-bzl-1.1.2"
    }
    if (ext.isSpotBugs) {
        dependencyHandler.classpath "com.github.spotbugs:com.github.spotbugs.gradle.plugin:6.1.7"
    }

    if (gradle.ext.btrace) {
        dependencyHandler.classpath "com.bytedance.btrace:rhea-gradle-plugin:2.0.3-rc02"
    }
    if (gradle.ext.oneapk) {
        dependencyHandler.classpath "com.bzl.oneapk:oneapk-scanner-plugin:${ext.oneapk_version}"
    }
    if (!gradle.ext.jenkinsEnv) {
        dependencyHandler.classpath "com.bzl.oneapk:oneapk-compress-plugin:${ext.oneapk_version}"
    }
}

def onBuildApply() {
    if (!gradle.ext.jenkinsEnv) {
        project.apply plugin: "com.bzl.plugins.build-apm"
        buildApm {
            //enableLog true
            //enableDebug true
            //abortOnError false
        }
    }
}

/**
 * 务必紧跟在 apply plugin Android 插件之后调用该方法
 * apply plugin: 'com.android.application'
 * 或
 * apply plugin: 'com.android.library'
 */
ext.afterAndroidPluginApply = { Project sub ->
    onAfterAndroidPluginApply(sub)
}

def onAfterAndroidPluginApply(Project sub) {
    if (sub == sub.rootProject) {
        println "skip rootProject"
        return
    }

    boolean isAppProject = isAppProject(sub)
    boolean isLibraryProject = isLibraryProject(sub)
    boolean isAndroidProject = isAppProject || isLibraryProject

    if (isAppProject) {
        if (gradle.ext.isNeedBooster) {
            sub.apply plugin: "com.didiglobal.booster"
        }

        if (gradle.ext.local_huaweiPush) {
            sub.apply plugin: 'com.huawei.agconnect'
            sub.apply plugin: 'com.hihonor.mcs.asplugin'
        }
        if (gradle.ext.local_tinker) {
            //sub.apply from: "${sub.projectDir}/tinker-support.gradle"
            sub.apply plugin: 'com.tencent.bugly.tinker-support'
        }

        if (gradle.ext.isOneApt) {
            sub.apply plugin: 'com.bzl.oneapt'
        }
        // 添加WMRouter插件
        if (!gradle.ext.oneapt_wmrouter) {
            sub.apply plugin: 'WMRouter'
            sub.WMRouter {
                enableLog = true // 调试开关
                enableDebug = true // 调试开关
            }
        }

        if (gradle.ext.btrace) {
            sub.apply from: "$rootDir/gradle/apm/btrace.gradle"
            sub.dependencies.add('implementation', "com.bytedance.btrace:rhea-core:2.0.3-rc02")
        }


        if (gradle.ext.local_sandbox) {
            sub.dependencies.add('implementation', "com.bzl.sandbox:twl-sandbox-api:0.2.67")
        }
        if (gradle.ext.tracecanary) {
            sub.apply from: "$rootDir/gradle/apm/trace-canary-plus.gradle"
        }
        if (gradle.ext.leakcanary) {
            sub.apply from: "$rootDir/gradle/apm/leakcanary.gradle"
        }

        if (gradle.ext.code_locator) {
            sub.dependencies.add('implementation', "com.bytedance.tools.codelocator:codelocator-core:2.0.3")
        }

        if (gradle.ext.version_monitor) {
            println "add module version_monitor "
            sub.dependencies.add('implementation', project(':module_developer:version_monitor'))
        }

        if (gradle.ext.local_tjacoco) {
            sub.apply plugin: 'com.bzl.onejacoco'
            sub.oneJacoco {
                baselineBranch = rootProject.ext.jacocoBaselineBranch
                bizLine = "BOSS"
                excludes = excludes + "com/hpbr/bosszhipin/developer/**"
            }
        }
        if (ext.isCheckR8Log) {
            println "apply CheckR8LogsPlugin"
            sub.apply plugin: CheckR8LogsPlugin
        }
        if (gradle.ext.onekit) {
            sub.apply plugin: 'com.bzl.onekit'
            sub.dokit {
                networkEnable false
                gpsEnable false
                bigImageEnable true
                webViewEnable true
                webView {
                    network true
                    dokitWeb true
                    vConsole true
                }
                gps {
                    amap true
                }
            }
        }
        if (!gradle.ext.jenkinsEnv) {
            sub.apply from: "$rootDir/gradle/oneapk/oneapk-compress.gradle"
        }
        if (gradle.ext.oneapk) {
            sub.apply from: "$rootDir/gradle/oneapk/oneapk-scanner.gradle"
        }
    }

    if (gradle.ext.oneapt_wmrouter && isAndroidProject) {
        sub.afterEvaluate {
            Collection variants = isAppProject ? sub.android.applicationVariants : sub.android.libraryVariants
            variants.all { variant ->
                variant.getAnnotationProcessorConfiguration().resolutionStrategy.dependencySubstitution {
                    substitute module("io.github.meituan-dianping:compiler") with module("com.bzl.plugins.oneapt:wm_router_apt:$oneapt_wmrouter_version")
                }
            }
        }
    }

    //配置lint
    if (isAppProject) {
        sub.apply plugin: "com.bzl.onelint"
    }

    if (isAndroidProject) {
        if (gradle.ext.local_tjacoco) {
            sub.android.buildTypes.debug.testCoverageEnabled = true
        }
        sub.android.lintOptions.abortOnError = false
        sub.dependencies.lintChecks "com.bzl.lint:lint-rules-boss:0.0.4-beta2"
        sub.dependencies.lintChecks "com.bzl.lint:lint-rules-common:0.0.5-beta6"

        configSpotBugs(sub)
        configPmd(sub)
    }
}

def configPmd(Project sub) {
    if (!ext.isPmd) {
        return
    }
    sub.apply plugin: "pmd"
    sub.pmd {
        ignoreFailures = true
        toolVersion = "6.55.0"
        rulesMinimumPriority = 3
        ruleSets = [
                "category/java/bestpractices.xml",
//                "category/java/codestyle.xml",
//                "category/java/design.xml",
                "category/java/errorprone.xml",
                "category/java/multithreading.xml",
                "category/java/performance.xml"
        ]
    }
    sub.tasks.register('pmd', Pmd) {
        description 'Run pmd'
        group 'verification'
        source 'src'
        include '**/*.java'
        exclude '**/gen/**', '**/build/**'
        reports {
            xml.required = true
            html.required = false
        }
    }
}

def configSpotBugs(Project sub) {
    if (!ext.isSpotBugs) {
        return
    }
    sub.apply plugin: "com.github.spotbugs"
    sub.dependencies.spotbugs "com.github.spotbugs:spotbugs:4.8.6"
    sub.dependencies.compileOnly "com.github.spotbugs:spotbugs-annotations:4.8.6"
    sub.dependencies.compileOnly "com.bzl.unisdk:android-jar-33:1.0.0"

    sub.spotbugs {
        ignoreFailures = true
        showStackTraces = false
        showProgress = false
        useAuxclasspathFile = true
//        effort = Effort.valueOf('DEFAULT')
//        reportLevel = Confidence.valueOf('DEFAULT')

//        visitors = [ 'FindSqlInjection', 'SwitchFallthrough' ]
//        omitVisitors = [ 'FindNonShortCircuit']
//        excludeFilter = file("${project.rootDir}/code-analysis/spotbugs/exclude-filter.xml")
        extraArgs = ['-nested:false']
    }
    sub.afterEvaluate {
        sub.tasks.matching { it.name.startsWith("spotbugs") }.configureEach { spotBugsTask ->
            spotBugsTask.sourceDirs.setFrom(file("${project.projectDir}/src/main/java"))
            reports {
                xml {
                    required = true
                }
                html {
                    required = false
//                outputLocation = file("$project.buildDir/reports/spotbugs/spotbugs.html")
                    stylesheet = 'fancy.xsl'
                }
            }
        }
    }
}

copyHooks()

def copyHooks() {
    //复制 commit-msg
    def gitCommitMsgFile = new File(project.rootProject.getRootDir().absolutePath + "/.git/hooks/commit-msg")
    def gitCommitMsgCustomFile = new File(project.rootProject.getRootDir().absolutePath + "/gradle/git/hooks/commit-msg")
    if (gitCommitMsgFile.exists()) {
        if (gitCommitMsgFile.length() != gitCommitMsgCustomFile.length()) {
            println "文件不一致，删除历史文件 commit-msg"
            Files.deleteIfExists(gitCommitMsgFile.toPath())
            Files.copy(gitCommitMsgCustomFile.toPath(), gitCommitMsgFile.toPath())
        }
    } else {
        println "复制 commit-msg 到hooks"
        Files.copy(gitCommitMsgCustomFile.toPath(), gitCommitMsgFile.toPath())
    }

    def gitFile = new File(project.rootProject.getRootDir().absolutePath + "/.git/hooks/pre-commit")
    if (gradle.ext.local_tlintCommit) {
        def preFile = new File(project.rootProject.getRootDir().absolutePath + "/gradle/git/hooks/pre-commit")
        if (gitFile.exists()) {
            if (gitFile.length() != preFile.length()) {
                println "文件不一致，删除历史pre-commit"
                Files.deleteIfExists(gitFile.toPath())
                Files.copy(preFile.toPath(), gitFile.toPath())
            }
        } else {
            println "复制pre-commit到hooks"
            Files.copy(preFile.toPath(), gitFile.toPath())
        }
    } else {
        println "删除hooks下面的pre-commit"
        Files.deleteIfExists(gitFile.toPath())
    }
}

ext.onPluginRepos = this.&onPluginRepos
ext.onBuildDependencies = this.&onBuildDependencies
ext.onBuildApply = this.&onBuildApply


static def isAppProject(Project project) {
    return project.plugins.hasPlugin("com.android.application")
}

static def isLibraryProject(Project project) {
    return project.plugins.hasPlugin("com.android.library")
}

static def isAndroidProject(Project project) {
    return isAppProject(project) || isLibraryProject(project);
}


// 获取通过命令行传递的参数 -PforceSdkVersion=""
if (project.hasProperty('forceSdkVersion')) {
    def forceSdkInfo = project.hasProperty('forceSdkVersion') ? project.forceSdkVersion.trim() : ""

    // 检查是否是空字符串
    if (forceSdkInfo.isEmpty()) {
        println "forceSdkVersion is an empty string."
    } else {
        // 将字符串按行分割为列表
        def dynamicDependencies = "${forceSdkInfo}".split('\\\\n')
        dynamicDependencies.each { dynamicDependency ->
            println "forceSdkVersion： ${dynamicDependency.trim()}"
        }

        subprojects { sub ->
            sub.configurations.all {
                resolutionStrategy {
                    dynamicDependencies.each { dynamicDependency ->
                        force dynamicDependency.trim()
                    }
                }
            }
        }
    }
}

/**
 * R8发现二进制不兼容时，做了一些迷之操作，生成一个 返回值是 IncompatibleClassChangeError()的匿名方法，在不兼容的地方调用
 * 导致运行时候崩溃，该插件会检测 mapping.txt 文件，看看里面有没有相关的内容
 * https://r8.googlesource.com/r8/+/master/src/main/java/com/android/tools/r8/ir/optimize/templates/CfUtilityMethodsForCodeOptimizations.java#31
 */
class CheckR8LogsPlugin implements Plugin<Project> {

    // 定义成员变量：错误关键字列表
    private static final List<String> errorKeywords = [
            "IncompatibleClassChangeError",
            "NoSuchMethodError",
            "IllegalAccessError"
    ]

    @Override
    void apply(Project project) {
        project.android.applicationVariants.all { variant ->
            String variantName = variant.name
            String mappingFilePath = "${project.buildDir}/outputs/mapping/${variantName}/mapping.txt"

            TaskProvider<Task> checkR8LogsTask = project.tasks.register("checkR8LogsFor${variantName.capitalize()}", {
                it.doLast {
                    def logFile = new File(mappingFilePath)
                    if (logFile.exists()) {
                        def logContent = logFile.text
                        def foundErrors = errorKeywords.findAll { keyword -> logContent.contains(keyword) }

                        if (!foundErrors.isEmpty()) {
                            throw new GradleException("IncompatibleCheckForR8, some crash action generated by R8 found in mapping for $variantName: ${foundErrors.join(', ')}")
                        }
                    } else {
                        println "Mapping file for $variantName does not exist."
                    }
                }
            })

            // 将日志检查任务附加到 assemble 任务
            project.tasks.named("assemble${variantName.capitalize()}").configure { assembleTask ->
                assembleTask.finalizedBy(checkR8LogsTask)
            }
        }
    }
}