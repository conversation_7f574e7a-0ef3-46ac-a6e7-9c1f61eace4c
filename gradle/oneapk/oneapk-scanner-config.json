{"--log": "I", "--apk": "", "--mappingTxt": "", "--resMappingTxt": "", "--output": "", "--format": "mm.html,mm.json", "--formatConfig": [{"name": "-count", "group": [{"name": "业务-首页", "package": "com.hpbr.bosszhipin.module.launcher", "follower": "邹东", "type": "business"}, {"name": "业务-首页", "package": "com.hpbr.bosszhipin.module.main", "follower": "邹东", "type": "business"}, {"name": "业务-我的", "package": "com.hpbr.bosszhipin.module.my", "type": "business"}, {"name": "业务-我的", "package": "com.hpbr.bosszhipin.module.myprivacy", "type": "business"}, {"name": "业务-登录", "package": "com.hpbr.bosszhipin.login", "follower": "邹东", "type": "business"}, {"name": "业务-登录", "package": "com.hpbr.bosszhipin.module.login", "follower": "邹东", "type": "business"}, {"name": "业务-设置", "package": "com.hpbr.bosszhipin.setting", "follower": "邹东", "type": "business"}, {"name": "业务-设置", "package": "com.hpbr.bosszhipin.setting_export", "follower": "邹东", "type": "business"}, {"name": "业务-设置", "package": "com.hpbr.bosszhipin.module.unfit", "follower": "邹东", "type": "business"}, {"name": "业务-面试", "package": "com.hpbr.bosszhipin.interviews", "follower": "杜柏均", "type": "business"}, {"name": "业务-面试", "package": "com.hpbr.bosszhipin.module.interview", "follower": "杜柏均", "type": "business"}, {"name": "业务-面试", "package": "com.hpbr.bosszhipin.module.interview", "follower": "杜柏均", "type": "business"}, {"name": "业务-社区", "package": "com.hpbr.bosszhipin.get", "follower": "冯智健", "type": "business"}, {"name": "业务-社区", "package": "com.hpbr.bosszhipin.revision.get", "follower": "冯智健", "type": "business"}, {"name": "业务-直播", "package": "com.hpbr.bosszhipin.live", "follower": "王鹏", "type": "business"}, {"name": "业务-商业", "package": "com.hpbr.bosszhipin.business", "follower": "南萌", "type": "business"}, {"name": "业务-商业", "package": "com.hpbr.bosszhipin.business_export", "follower": "南萌", "type": "business"}, {"name": "业务-商业", "package": "com.hpbr.bosszhipin.module.pay", "follower": "南萌", "type": "business"}, {"name": "业务-Geek搜索", "package": "com.hpbr.bosszhipin.search", "follower": "杨林杰", "type": "business"}, {"name": "业务-Geek搜索", "package": "com.module_geeksearch_export", "follower": "杨林杰", "type": "business"}, {"name": "业务-Geek简历", "package": "com.hpbr.bosszhipin.geekresume", "follower": "刘鑫14", "type": "business"}, {"name": "业务-Geek简历", "package": "com.hpbr.bosszhipin.geekresume_export", "follower": "刘鑫14", "type": "business"}, {"name": "业务-Geek简历", "package": "com.hpbr.bosszhipin.module.onlineresume", "follower": "刘鑫14", "type": "business"}, {"name": "业务-Geek简历", "package": "com.hpbr.bosszhipin.module.resume", "follower": "刘鑫14", "type": "business"}, {"name": "业务-Geek职位列表", "package": "com.hpbr.bosszhipin.geek", "follower": "杨林杰", "type": "business"}, {"name": "业务-Geek职位详情", "package": "com.hpbr.bosszhipin.geekjd", "follower": "王鹏", "type": "business"}, {"name": "业务-Geek职位详情", "package": "com.hpbr.bosszhipin.module.position", "follower": "王鹏", "type": "business"}, {"name": "业务-Geek期望", "package": "com.hpbr.bosszhipin.module.expect", "follower": "赛伊丹", "type": "business"}, {"name": "业务-Geek其他", "package": "com.hpbr.bosszhipin.module_geek_export", "follower": "杨林杰", "type": "business"}, {"name": "业务-Geek待分类", "package": "com.hpbr.bosszhipin.module.greeting", "type": "business"}, {"name": "业务-Geek待分类", "package": "com.hpbr.bosszhipin.module.guest", "type": "business"}, {"name": "业务-Geek待分类", "package": "com.hpbr.bosszhipin.module.localservice", "type": "business"}, {"name": "业务-Geek待分类", "package": "com.hpbr.bosszhipin.module_geek", "type": "business"}, {"name": "业务-Geek待分类", "package": "com.hpbr.bosszhipin.module.commend", "type": "business"}, {"name": "业务-Boss公司", "package": "com.hpbr.bosszhipin.company", "follower": "赛伊丹", "type": "business"}, {"name": "业务-Boss公司", "package": "com.hpbr.bosszhipin.module.company", "type": "business"}, {"name": "业务-Boss简历详情", "package": "com.hpbr.bosszhpin.boss", "follower": "南萌", "type": "business"}, {"name": "业务-Boss待分类", "package": "com.hpbr.bosszhpin.module_boss", "type": "business"}, {"name": "业务-Boss待分类", "package": "com.hpbr.bosszhipin.module.contacts", "type": "business"}, {"name": "业务-Boss猎头", "package": "com.hpbr.bosszhipin.module.hunter2b", "follower": "唐阿辉", "type": "business"}, {"name": "业务-Boss待分类", "package": "com.hpbr.bosszhipin.module.my", "type": "business"}, {"name": "业务-Boss职位管理", "package": "com.hpbr.bosszhpin.module.position", "follower": "佟啸云", "type": "business"}, {"name": "业务-Boss待分类", "package": "com.hpbr.bosszhipin.module.boss", "type": "business"}, {"name": "业务-Boss待分类", "package": "com.hpbr.bosszhipin.module_boss_export", "type": "business"}, {"name": "业务-Geek卡片", "package": "com.hpbr.bosszhipin.commoncard.geek", "follower": "杨林杰", "type": "business"}, {"name": "业务-Boss卡片", "package": "com.hpbr.bosszhipin.commoncard.boss", "follower": "周游", "type": "business"}, {"name": "业务-未知卡片", "package": "com.hpbr.bosszhipin.commoncard.simulation", "follower": "周游", "type": "business"}, {"name": "业务-Boss高级搜索", "package": "com.hpbr.bosszhipin.advancedsearch", "follower": "周游", "type": "business"}, {"name": "业务-Boss高级搜索", "package": "com.hpbr.bosszhipin.ads", "follower": "周游", "type": "business"}, {"name": "业务-Boss高级搜索", "package": "com.hpbr.bosszhipin.module.commend.activity.advanced", "follower": "周游", "type": "business"}, {"name": "业务-消息", "package": "com.hpbr.bosszhipin.chat", "follower": "师天祥", "type": "business"}, {"name": "业务-消息", "package": "com.hpbr.bosszhipin.chathelper", "follower": "邹东", "type": "business"}, {"name": "业务-消息", "package": "com.hpbr.bosszhipin.group", "follower": "邹东", "type": "business"}, {"name": "业务-消息", "package": "message.handler", "follower": "邹东", "type": "business"}, {"name": "业务-消息", "package": "message.server", "follower": "邹东", "type": "business"}, {"name": "业务-联系人", "package": "com.hpbr.bosszhipin.module.contacts", "type": "business"}, {"name": "业务-通用", "package": "com.hpbr.bosszhipin.common", "type": "business"}, {"name": "业务-通用", "package": "com.hpbr.bosszhipin.module.common", "type": "business"}, {"name": "业务-通用", "package": "com.hpbr.bosszhipin.module.component", "type": "business"}, {"name": "公共-支付", "package": "com.hpbr.bosszhipin.cmbapi", "type": "common"}, {"name": "公共-支付", "package": "com.hpbr.bosszhipin.wxapi", "type": "common"}, {"name": "公共-支付", "package": "com.hpbr.bosszhipin.zfbapi", "type": "common"}, {"name": "公共-二维码", "package": "com.hpbr.bosszhipin.zxing", "type": "common"}, {"name": "公共-配置", "package": "com.hpbr.bosszhipin.config", "type": "common"}, {"name": "公共-配置", "package": "com.hpbr.bosszhipin.manager", "type": "common"}, {"name": "公共-上传", "package": "com.hpbr.bosszhipin.cos", "type": "common"}, {"name": "公共-埋点", "package": "com.hpbr.bosszhipin.event", "type": "common"}, {"name": "公共-事件", "package": "com.hpbr.bosszhipin.eventbus", "type": "common"}, {"name": "公共-上报", "package": "com.hpbr.bosszhipin.report", "type": "common"}, {"name": "公共-启动", "package": "com.hpbr.bosszhipin.startup", "type": "common"}, {"name": "公共-通知", "package": "com.twl.daemon", "follower": "邹东", "type": "common"}, {"name": "公共-推送", "package": "com.hpbr.bosszhipin.push", "follower": "邹东", "type": "common"}, {"name": "公共-基础数据", "package": "com.basedata", "type": "common"}, {"name": "公共-筛选器", "package": "com.filter", "type": "common"}, {"name": "公共-筛选器", "package": "com.hpbr.bosszhipin.module.filter", "type": "common"}, {"name": "公共-地图", "package": "com.hpbr.bosszhipin.module.map", "follower": "邹东", "type": "common"}, {"name": "公共-地图", "package": "com.hpbr.bosszhipin.module.mapcompat", "follower": "邹东", "type": "common"}, {"name": "公共-基类", "package": "com.hpbr.bosszhipin.base", "type": "common"}, {"name": "公共-美颜", "package": "com.hpbr.bosszhipin.beauty", "follower": "王鹏", "type": "common"}, {"name": "公共-多媒体", "package": "com.hpbr.bosszhipin.media", "follower": "佟啸云", "type": "common"}, {"name": "公共-多媒体", "package": "com.hpbr.bosszhipin.player", "follower": "佟啸云", "type": "common"}, {"name": "公共-多媒体", "package": "com.hpbr.bosszhipin.video", "follower": "佟啸云", "type": "common"}, {"name": "公共-UGC", "package": "com.hpbr.bosszhipin.ugc", "follower": "佟啸云", "type": "common"}, {"name": "公共-灰度", "package": "com.hpbr.bosszhipin.gray", "type": "common"}, {"name": "公共-路由", "package": "com.hpbr.bosszhipin.router", "type": "common"}, {"name": "公共-接口", "package": "com.hpbr.bosszhipin.net", "type": "common"}, {"name": "公共-接口", "package": "com.hpbr.bosszhipin.network", "type": "common"}, {"name": "公共-接口", "package": "com.hpbr.bosszhipin.export", "type": "common"}, {"name": "公共-接口", "package": "com.hpbr.bosszhipin.listener", "type": "common"}, {"name": "公共-接口", "package": "net.boss<PERSON><PERSON>", "type": "common"}, {"name": "公共-协议", "package": "com.hpbr.protocol", "type": "common"}, {"name": "公共-UI组件", "package": "com.hpbr.bosszhipin.views", "type": "common"}, {"name": "公共-UI组件", "package": "com.hpbr.bosszhipin.weiget", "type": "common"}, {"name": "公共-UI组件", "package": "com.hpbr.bosszhipin.overscroll", "type": "common"}, {"name": "公共-UI组件", "package": "com.hpbr.bosszhipin.window", "type": "common"}, {"name": "公共-UI组件", "package": "com.twl.ui", "type": "common"}, {"name": "公共-UI组件", "package": "com.twl.business", "type": "common"}, {"name": "公共-工具类", "package": "com.hpbr.bosszhipin.util", "type": "common"}, {"name": "公共-工具类", "package": "com.hpbr.bosszhipin.utils", "type": "common"}, {"name": "公共-工具类", "package": "com.monch.lbase", "type": "common"}, {"name": "公共-工具类", "package": "com.twl.utils", "type": "common"}, {"name": "公共-网络", "package": "com.twl.http", "follower": "邹东", "type": "common"}, {"name": "公共-数据库", "package": "com.bszp.kernel", "follower": "邹东", "type": "common"}, {"name": "公共-数据库", "package": "com.hpbr.bosszhipin.data", "follower": "邹东", "type": "common"}, {"name": "公共-图片加载", "package": "com.hpbr.bosszhipin.imageloader", "type": "common"}, {"name": "公共-图片选择器", "package": "com.hpbr.bosszhipin.module.imageviewer", "type": "common"}, {"name": "公共-图片选择器", "package": "com.hpbr.bosszhipin.module.photoselect", "type": "common"}, {"name": "公共-分享", "package": "com.hpbr.bosszhipin.module.share", "follower": "赛伊丹", "type": "common"}, {"name": "公共-升级", "package": "com.hpbr.bosszhipin.module.update", "follower": "邹东", "type": "common"}, {"name": "公共-视频面试", "package": "com.hpbr.bosszhipin.module.videointerview", "type": "common"}, {"name": "公共-语音识别", "package": "com.hpbr.bosszhipin.module.VoiceRecognizer", "type": "common"}, {"name": "公共-WebView", "package": "com.hpbr.bosszhipin.module.webview", "follower": "佟啸云", "type": "common"}, {"name": "SDK-FrescoImageLoader", "package": "com.fresco", "follower": "周游", "type": "sdk"}, {"name": "SDK-TechwolfFileSelecter", "package": "com.monch.lib.file.selecter", "follower": "佟啸云", "type": "sdk"}, {"name": "SDK-TwlMapWrapper", "package": "com.hpbr.bosszhipin.map", "follower": "邹东", "type": "sdk"}, {"name": "SDK-VideoDetection", "package": "com.bzl.videodetection", "follower": "佟啸云", "type": "sdk"}, {"name": "SDK-MobileMqttServiceMars", "package": "com.twl.mms", "follower": "邹东", "type": "sdk"}, {"name": "SDK-MagicIndicator", "package": "net.lucode.hackware.magicindicator", "type": "sdk"}, {"name": "系统库-Android", "package": "android", "type": "system"}, {"name": "系统库-Android", "package": "com.google.android.material", "type": "system"}, {"name": "系统库-Java", "package": "java", "type": "system"}, {"name": "系统库-<PERSON><PERSON><PERSON>", "package": "kotlin", "type": "system"}]}], "options": [{"name": "-manifest"}, {"name": "-fileSize", "--min": "50", "--order": "desc", "--suffix": "so, dex, arsc"}, {"name": "-countClass", "--group": "package"}, {"name": "-count<PERSON><PERSON><PERSON>", "--group": "package"}, {"name": "-findNonAlphaPng", "--min": "10"}, {"name": "-uncompressedFile", "--suffix": "dex, arsc"}, {"name": "-countR"}, {"name": "-duplicatedFile"}, {"name": "-unusedResources", "--rTxt": "", "--resourcesTxt": "", "--ignoreResources": ["<PERSON>.drawable.yw_1222_*", "R.anim.view_shake", "R.anim.cycle", "<PERSON>.drawable.gt3*", "R.mipmap.gt3*", "R.layout.gt3*", "R.string.gt3*", "R.style.gt3*", "R.id.gt3*", "R.id.*geetest*"], "isRTxtConfig": true}, {"name": "-unusedAssets", "--ignoreAssets": ["*.so"]}, {"name": "-countUnusedClass", "--usageTxt": "", "--packages": ["com.hpbr.bosszhipin.*", "com.hpbr.bosszhpin.*", "com.module*", "net.boss<PERSON>pin.*", "com.hpbr.protocol.*", "com.bszp.kernel.*", "com.twl.mms.*", "com.twl.daemon.*", "com.basedata.*", "com.twl.ui.*", "com.twl.business.*", "com.twl.utils.*", "com.twl.http.*", "com.monch.*", "message.handler.*", "message.server.*", "com.filter.*", "com.fresco.*", "com.bzl.videodetection.*", "net.lucode.hackware.magicindicator.*"]}, {"name": "-compressibleImage", "--ignoreResources": []}, {"name": "-checkMultiSTL"}, {"name": "-unstrippedSo"}]}