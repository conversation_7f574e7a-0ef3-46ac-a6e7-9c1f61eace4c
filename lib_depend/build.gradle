apply from: "$rootDir/gradle/common_library.gradle"
android {
    resourcePrefix 'depend_'
}

dependencies {
    //***************  app  ************************

    api 'com.twl.sdk.file:pinyin4j:2.5.0'
//    api files('../libs/protobuf-java-2.5.0.jar')
    api 'com.twl.sdk.file:protobuf-java:2.5.0'

    // 友盟SDK 2021-8-12（9.10）,Common为定制版本，如果需要升级，直接联系友盟团队
//    api  'com.umeng.umsdk:common:9.4.0' // 版本号(必选)
//    api files('../libs/umeng-common-9.4.2.A.jar')
//    api 'com.twl.sdk.file:umeng-common:9.4.2.A'
//    api  'com.umeng.umsdk:asms:1.2.3' // asms包依赖(必选)
//    api 'com.umeng.umsdk:apm:1.4.0'

    // 支付宝
//  之前使用：api 'com.twl.sdk.file:alipaySdk:15.6.5'
//  api 'com.alipay.sdk:alipaysdk-android:15.8.10@aar'
    api deps.alipaysdk

    //极验 远程依赖可能有X5
//    api(name: 'geetest_sensebot_android_v*******_20201217', ext: 'aar')
//    api 'com.twl.sdk.file:geetest_sensebot_android:*******'
//    api 'com.twl.sdk.file:phoneNumber-L-AuthSDK:2.12.1'

    /*阿里一键登录*/
    api 'com.twl.sdk.file:auth_number_product_noUi_cuum:2.14.6'
    api 'com.twl.sdk.file:main:2.2.3'
    api 'com.twl.sdk.file:logger:2.2.2'

//    api 'com.twl.sdk.file:crashshield:2.1.4'
    /*移动一键登录*/
//    api 'com.twl.sdk.file:quick_login:*******'


    //--------begin 腾讯云人脸认证9.10 替换by王田,需要联系腾讯人脸商务对接定制包---------------
    api 'com.twl.sdk.file:WbCloudFaceLiveSdk:v6.3.0-817087f1'
//    api(name: 'WbCloudNormal-v5.0.4-82bad27', ext: 'aar')
    api 'com.twl.sdk.file:WbCloudNormal:v5.1.10-4e3e198'
    //--------end 腾讯云人脸认证---------------


//    api(name: 'NoCaptchaSDK-external-release-5.4.33', ext: 'aar')
//    api 'com.twl.sdk.file:NoCaptchaSDK-external:5.4.33'
    //*************** lib_common
    api deps.yidun
    api deps.geetest

//    api files('../libs/tbs_sdk_thirdapp_v********_43939_sharewithdownloadwithfile_withoutGame_obfs_20200713_223411.jar')
//    api 'com.twl.sdk.file:tbs_sdk_thirdapp:*********'
//    api 'com.twl.sdk.file:tbssdk:44318'


    //meizu
    api 'com.twl.sdk.file:meizu_pushsdk:5.0.3'

    //vivo push sdk 2021.3.16
//    api(name: 'vivo_pushsdk_v3.0.0.7_480', ext: 'aar')
    api 'com.twl.sdk.file:vivo_pushsdk:4.0.4.0_504'
    //xiaomi push sdk  2021.5.18  V9.05
//    api files('../libs/MiPush_SDK_Client_4_0_2.jar')
    api 'com.twl.sdk.file:MiPush_SDK_Client:6.0.1-C_3rd'
    //oppo push sdk  2021.3.16
//    api(name: 'com.heytap.msp', ext: 'aar')
    api 'com.twl.sdk.file:heytap-msp:3.5.2'

    //*************** lib_mobile_mqtt_service_mars
//    compileOnly files('libs/org.apache.http.legacy.jar') 需要打进包吗

    //*************** module-expand geek
//    api files('../libs/upload/cosxml-5.4.20.jar')
//    api 'com.twl.sdk.file:cosxml:5.4.20'
//    api files('../libs/upload/qcloud-foundation-1.5.6.jar')

//    api 'com.twl.sdk.file:qcloud-foundation:1.5.6'


    //*************** module-expand module-get
//    api(name: 'oss-android-sdk-2.9.2', ext: 'aar')


    //***************  融合SDK
    //公司 VR
    api 'com.twl.sdk.file:zp-uikit-panocam:5.2.12'

    //*************** 人脸识别安全SDK的特征向量校验模式
    api "com.bzl.security:verify:0.5.2-beta1"
    api 'com.twl.sdk.file:shumeng:8.4.2-opt'

    //*************** 自研vod视频播放器
    api 'com.twl.sdk.file:afanty-player:*********@aar'
    /*目标检测sdk*/
    api deps.vgroup.zp_sdk_target_recognition_release
    /*ai面试sdk*/
    api deps.vgroup.zp_sdk_h5_native_protocol
    /*国家网络身份认证*/
    api deps.wauth

    api('com.qcloud.cos:cos-android:5.9.0') {
        exclude group: 'com.tencent.qcloud', module: 'beacon-android-release'
    }
    // V组 上传 COS SDK
    api 'com.twl.sdk.file:zp-sdk-zoss:2.0.5'
}
/*
910版本调整
api 'com.twl.sdk.file:amap-so:9.10.1'
api 'com.twl.sdk.file:shumeng-so:9.10.1'
api 'com.twl.sdk.file:msc-so:9.10.1'
api 'com.twl.sdk.file:alipaySdk:15.6.5'
api 'com.twl.sdk.file:aliyun-vod-core-android-sdk:1.2.2'
api 'com.twl.sdk.file:aliyun-vod-upload-android-sdk:1.4.0'
api 'com.twl.sdk.file:heytap-msp:9.10.1'
api 'com.twl.sdk.file:crashshield:2.0.3'
api 'com.twl.sdk.file:du:9.10.1'
api 'com.twl.sdk.file:faceDetectSDK:9.10.1'
api 'com.twl.sdk.file:fmlivesdk:9.10.1'
api 'com.twl.sdk.file:geetest_sensebot_android:*******'
api 'com.twl.sdk.file:HTTPDNS_Android:3.6.0'
api 'com.twl.sdk.file:logger:2.0.3'
api 'com.twl.sdk.file:logUtils:5.4.16'
api 'com.twl.sdk.file:main:2.0.3'
api 'com.twl.sdk.file:meglive_still:9.10.1'
api 'com.twl.sdk.file:miit_mdid:1.0.9'
api 'com.twl.sdk.file:MiPush_SDK_Client:4.0.2'
api 'com.twl.sdk.file:msc:9.10.1'
api 'com.twl.sdk.file:mtaUtils:5.4.16'
api 'com.twl.sdk.file:NoCaptchaSDK-external:5.4.33'
api 'com.twl.sdk.file:phoneNumber-L-AuthSDK:2.12.1'
api 'com.twl.sdk.file:pinyin4j:2.5.0'
api 'com.twl.sdk.file:protobuf-java:2.5.0'
api 'com.twl.sdk.file:push-internal:4.0.7'
api 'com.twl.sdk.file:SphereCameraKit:9.10.1'
api 'com.twl.sdk.file:tbs_sdk_thirdapp:********'
api 'com.twl.sdk.file:cosxml:5.4.20'
api 'com.twl.sdk.file:qcloud-foundation:1.5.6'
api 'com.twl.sdk.file:vivo_pushsdk:3.0.0'
api 'com.twl.sdk.file:WbCloudFaceLiveSdk-noTuring:3.4.9'
api 'com.twl.sdk.file:WbCloudNormal:5.0.4'
api 'com.twl.sdk.file:WXShare:9.10.1'
api 'com.twl.sdk.file:zpeaglesdk:9.10.1'
api 'com.twl.sdk.file:zpfilesdk:9.10.1'
api 'com.twl.sdk.file:zpimsdk:9.10.1'
api 'com.twl.sdk.file:zpnrtcengine:9.10.1'
api 'com.twl.sdk.file:zprtcsdk:9.10.1'
*/
