package net.bosszhipin.api.bean.job;

import android.graphics.Bitmap;
import android.text.TextUtils;

import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.MapViewCompat;

import net.bosszhipin.api.JobOverseasAddressBean;
import net.bosszhipin.api.JobOverseasAddressInfoBean;
import net.bosszhipin.api.ServerJobAddessInfo;
import net.bosszhipin.api.ServerJobAddressFeadBackBean;
import net.bosszhipin.api.ServerJobDetailAddressBean;
import net.bosszhipin.api.ServerJobRequirementsInfoBean;
import net.bosszhipin.api.ServerJobTempleteBean;
import net.bosszhipin.api.ServerSalaryWalfareInfoBean;
import net.bosszhipin.api.ServerSkillIconInfo;
import net.bosszhipin.api.ServerWordHighlightBean;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.ServerAfterNameIconBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.api.bean.ServerRecOnlineImageBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/3/23.
 */
public class ServerJobBaseInfoBean extends BaseServerBean {
    private static final long serialVersionUID = 4769523587027772583L;


    /**
     * jobId : 5695376
     * brandId : 170062
     * jobValidStatus : 2
     * position : 100101
     * positionName : Java
     * positionCategory : java
     * location : 101010100
     * locationName : 北京
     * degree : 203
     * degreeName : 本科
     * experience : 103
     * experienceName : 1年以内
     * jobDesc : 工作内容：
     * java后端服务开发，面向pc平台，包括系统架构设计、技术点攻坚和推广、系统核心部分代码编写等工作；
     * 优酷为阿里集团旗下子公司
     * <p>
     * 任职要求:
     * 1 有激情，能自驱，能抗压；需要具有独挡一面甚至独挡多面的能力
     * 2 诚实、简单、有进取心
     * 3 有责任心，目标驱动，为结果负责；
     * 4 具有良好的沟通能力和团队合作精神，踏实严谨。
     * 5 逻辑思维严谨，视野开阔灵活；知道如何站在用户角度看待问题
     * 6 求知欲/学习能力强，有意愿且能够独立攻关难题
     * 7 精通Web编程，3年以上使用Java语言进行web开发的经验，精通Spring、SpringMVC、bootstrap框架、Mybiatis等框架开发；
     * 8 熟悉分布式部署架构和网络编程，具有设计和开发对外API接口经验和能力；
     * 9 熟练使用linux系统、熟练使用常用数据库及缓存
     * 10 熟悉大型互联网公司高可用架构，同时具备大型互联网公司运维经验优先 ；
     * lowSalary : 10
     * highSalary : 11
     * requiredSkills : ["后端开发"]
     * address : 北京通州区K2百合湾
     * areaDistrict : 通州区
     * businessDistrict : 武夷花园
     * longitude : 116.6905
     * latitude : 39.91327
     * staticMapUrl : https://img.bosszhipin.com/beijin/amap/staticMap/038879586262b8fe7bae990e3a13cb25
     * kindlyReminder : 该Boss承诺名下所有职位不向您收费，如有不实，请立即举报。
     * <p>
     * "proxyJob": 1,  //是否代招职位，0-否，1-是
     * "proxyBrandName": "快手",  // 代招品牌名称
     * "proxyBrandStage": "D轮及以上"  // 代招品牌融资阶段
     */

    /**
     * #8.01
     * zpData -> jobBaseInfo -> jobType  职位类型，5-校招
     * zpData -> jobBaseInfo -> afterNameIcons  职位名称后icon列表
     * zpData -> jobBaseInfo -> graduationTime 毕业时间
     * zpData -> jobBaseInfo -> deadLine 招聘截止日期
     */

    public long jobId;
    public long brandId;
    public int jobValidStatus;
    public int position;
    public String positionName;

    public ServerJDTranslationBean jdTranslation;
    public String positionCategory;
    public int location;
    public String locationName;
    public int degree;
    public String degreeName;
    public int experience;
    public String experienceName;
    public String jobDesc;
    public List<ServerHighlightListBean> jobDescHighlights;
    public int lowSalary;
    public int highSalary;
    public String address;
    public String areaDistrict;
    public String businessDistrict;
    public transient ServerRecOnlineImageBean detailOnline;//非server端下发
    public double longitude;
    public double latitude;
    public String staticMapUrl;
    public List<String> requiredSkills;
    public List<ServerWordHighlightBean> requiredSkillsHighlights;
    public List<String> requiredSkillsExposureAction;
    public int salaryMonth;
    public String performance;
    public String salaryDesc;
    public String salaryDescAppend;
    public ServerJobSalaryDetailBean salaryDetail;

    public boolean blueCollarPosition; // 是否是蓝领职位

    public String locationDesc;

    public int jobType; // 5校招
    public List<ServerAfterNameIconBean> afterNameIcons;
    public List<Bitmap> afterNameIconBitmaps = new ArrayList<>();
    //  "graduationTime": "2020年","deadLine": "2020年6月1日"
    public String graduationTime;
    public String deadLine;
    public int proxyJob;
    public int proxyType;//中介职位类型，0-普通职位，1-代招职位，2-代招自营职位，3-派遣职位，4-派遣自营职位
    public String proxyBrandName;
    public String proxyBrandStage;

    public List<ServerSkillIconInfo> jobSkills;

    /**
     * 8.02 部门和汇报对象
     */
    public String department;
    public int anonymous;//是否匿名职位，如果大于0 就是匿名职位
    public int locationWithSubway;// 1304.802 地点是否携带地铁站 0:不带 1:带样式1 2:带样式2

    public boolean isJobValid() {
        return jobValidStatus == 1;
    }

    public void setJobValidStatus(int jobValidStatus) {
        this.jobValidStatus = jobValidStatus;
    }

    /**
     * 本地字段，804 显示当前/住址 到 job的距离
     */
    public String distanceDesc;
    /**
     * 用到的holder在两个地方使用，所以用这个字段控制下
     */
    public boolean isShowDistance;

    public String partTimeJobDesc;// 1122.601 兼职描述

    public List<ServerJobDetailAddressBean> jobAddressList;
    public int jobAddressCount;
    public String newAddressTypeDesc; // 916 新职位
    public String trafficDesc; //该字段原先定义于917.603距离地铁商圈描述，已下线
    public String trafficDescV2;// 1004.031展示周边交通信息
    public int addressShowType; //地址模块展示形式，0-默认原始展示，1-916.121无工作地址展示形式，2-917.603无静态图可展示地铁商圈距离形式
    public String geoId; //919 044 透传给高德导航的

    public int source; // 职位来源，2-优选中介，4-BossUp，5-工厂

    public List<ServerJobAddessInfo> addressIcons;

    public ServerJobTempleteBean jobTemplateModule;

    public ServerJobAddressFeadBackBean addressFeedback; // 1016.121 职位地址反馈入口，端上判断url不为空展示箭头icon

    public String addressModuleTitle; // 地址模块title，“工作地址”


    public int showHunterJob;  // 1013.520 是否对外展示猎头职位相关说明信息，0-否，1-是。判断优先级比代招/派遣 高。

    public String jobPubTimeDesc; // "该职位于3日内新发布",

    public ServerSalaryWalfareInfoBean salaryWelfareInfo;
    public ServerJobRequirementsInfoBean jobRequirementsInfo; //1301.610

    public String jobAddressId; //1116 客户端先只在安心保职位页调用开聊接口时传一下该地址id
    public List<JobOverseasAddressBean> overseasAddressList;//1126.62 驻外岗位地址
    public  JobOverseasAddressInfoBean overseasAddressInfo;
    public JDOverseasAreaBean overseasInfo;//1210 驻外职位信息
    public List<String> overseasWelfareList;//1211.68 驻外福利
    public String jobSkillLabelDesc;//1216.616职位详情页结构化标签
    public String baiduStaticMapUrl; // 百度静态地图

    /**
     * 如果使用百度地图，不为空返回baiduStaticMapUrl，为空则使用staticMapUrl兜底
     */
    public String getStaticMapUrl() {
        if (!MapViewCompat.isForceAMap() && AndroidDataStarGray.getInstance().isUserBMapType()) {
            //baiduStaticMapUrl为空，使用staticMapUrl兜底
            return !TextUtils.isEmpty(baiduStaticMapUrl) ? baiduStaticMapUrl : staticMapUrl;
        } else {
            return staticMapUrl;
        }
    }
}
