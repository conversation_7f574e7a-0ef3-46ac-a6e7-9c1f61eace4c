package com.hpbr.bosszhipin.protocol;

/**
 * created by tong<PERSON><PERSON><PERSON>.
 * date: 2023/4/12
 * time: 2:49 下午
 * description:
 */
public interface ProtocolConstants {

    interface App {

        // 打开首页页面
        String TYPE_OPEN_MAIN_PAGE = "openmain";

        String TYPE_OPEN_PROFESSIONAL_SKILL = "openProfessionalSkill";
        /**
         * 812.207 蓝领定制推荐列表协议
         */
        String TYPE_BLUE_GEEK_JOB_LIST = "blueGeekCustomJobList";
        /**
         * 813.214 蓝领岗位经验页面协议
         */
        String TYPE_BLUE_POST_EXP = "editPostExp";
        /**
         * 8.18版本新增跳转牛人资格证书页协议
         * bosszp://bosszhipin.app/openwith?type=geekCertification&jdCertName=xxx
         */
        String TPYE_GEEK_CERTIFICATION = "geekCertification";
        /**
         * 打开职场人三级职类选择页面
         */
        String TYPE_CHOOSE_POSITION = "choosePosition";
        /**
         * 打开学生职位类别页面 / 实习/ 兼职 / 全职
         */
        String TYPE_STUDENT_F1TAB_MANAGEMEND = "studentF1TabManage";

        /**
         * 打开公司发布新职位
         */
        String TYPE_OPEN_COM_NEW_JOB = "followcompanyjobs";

        /**
         * 打开F1新筛选
         */
        String TYPE_OPEN_F1_FILTER = "f1Filter";

        /**
         * 残障专区页面
         */
        String TYPE_GEEK_HANDI_ZONE = "handicappedZone";
        // 打开职位详情页面
        String TYPE_OPEN_JOB_DETAIL = "jobview";

        String TYPE_OPEN_RESUME_SECURITY = "openResumeSecurity";

        String TYPE_OPEN_BASE_INFO = "baseInfo";
        //跳转到互动
        String TYPE_INTERACTION = "openInteraction";

        // 打开沟通过的页面
        String TYPE_OPEN_CONTACTING = "opencontacting";
        // 打开收藏过的页面
        String TYPE_OPEN_INTEREST = "openinterest";
        // 打开个人信息页面
        String TYPE_USER_INFO_PAGE = "userInfo";

        String TYPE_DB_REPAIR = "dbRepair";

        String TYPE_REPAIR_X5 = "repairx5";

        // 714 简历下载
        String TYPE_OPEN_RESUME_FILE = "openFile";

        // 登录打开扫描二维码
        String TYPE_QR_ACTION = "qraction";


        /*901 店长店铺页面*/
        String TYPE_DZ_SHOP_HOME_PAGE = "dzShopHomePage";

        String TYPE_NET_TEST = "netTest";

        // 跳转至拔号页面
        String TYPE_TEL_CALL = "telecall";
        /**
         * 网络电话
         */
        String TYPE_CUSTOM_PHONE = "customerPhone";

        //校招ATS
        String TYPE_XZATS_SCAN = "openXzAtsScan";

        String TYPE_LOCATION_PREVIEW = "locationPreview";
        // 目标求职地点
        String TYPE_EXPECT_WORK_LOCATION = "expectWorkLocation";
        String TYPE_COPY_CONTENT = "copyContent";
        // 系统更新
        String TYPE_SEND_UPDATE_SYSTEM_VERSION = "update";
        // 打开联系人列表页面
        String TYPE_OPEN_CONTACT_LIST_PAGE = "opencontact";
        String TYPE_SWITCH_IDENTIFY = "openSwitchIdentify";
        String TYPE_AUTO_REPORT = "autoReport";
        String TYPE_TEST_CRASH_REPORT = "testCrashReport";
        String TYPE_POST_NOTIFICATIONS = "postNotifications";

        String TYPE_CHANGE_IDENTIFY = "quicklySwitchIdentify";

    }

    /**
     * 社区
     */
    interface Get {
        /**
         * 职测协议
         */
        @Deprecated
        String JOB_TEST = "momentGetTabJobTest";
        /**
         * 面试题详情
         */
        String INTERVIEW_DISCUSS = "interviewDiscuss";

        /**
         * 百科榜单
         */
        String ENCYCLOPAEDIC = "encyclopaedic";
        /**
         * 行业资讯——主页协议
         */
        String INDUSTRY_INFO = "industryInfo";
        /**
         * 内容发布
         */
        String TYPE_POST_CONTENT = "postcontent";
        /**
         * 问题列表
         */
        String TYPE_QUESTION_LIST = "questionList";
        /**
         * 等你来答
         */
        String TYPE_GET_QUESTION_FEED = "questionfeed";
        /**
         * BOSS/牛人个人主页
         */
        String TYPE_BOSS_PROFILE = "bossprofile";

        /**
         * BOSS/牛人个人主页
         */
        String TYPE_OPEN_HOME_PAGE = "openhomepage";

        /**
         * 热榜
         */
        String TYPE_HOT_TOPIC = "hotTopic";

        /**
         * 热门好文 & 热门评论
         */
        String TYPE_HOT_COLUMN = "hotLongText";

        /**
         * 热门评论
         */
        String TYPE_HOT_COMMENT = "hotComment";

        /**
         * 热门问题
         */
        String TYPE_HOT_QUESTION = "hotQuestion";

        /**
         * 个人主页（C）
         */
        String TYPE_GEEK_HOMEPAGE = "geekHomePage";

        /**
         * 发现排行榜
         */
        String TYPE_GET_TOP_USER = "getTopUser";

        /**
         * 动态详情
         */
        String TYPE_GET_DYNAMIC_DETAIL = "getFeedWithComment";

        /**
         * 转职指南
         */
        @Deprecated
        String TYPE_MOMENT_CHANGE_INDUSTRY = "momentChangeIndustry";

        /**
         * GET消息中心
         */
        String TYPE_GET_NOTICE = "discoverUserMsg";

        /**
         * 圈子首页
         */
        @Deprecated
        String TYPE_MOMENT_CIRCLE_HOME_PAGE = "momentCircleHomePage";

        /**
         * 我的圈子页面
         */
        @Deprecated
        String TYPE_MOMENT_CIRCLE_MY_HOME = "momentCircleMyHomePage";

        /**
         * 发布读书
         */
        String TYPE_GET_BOOK_POST = "getPushFeed";

        /**
         * 书聚合动态页
         */
        String TYPE_GET_BOOK_DETAIL = "bookFeed";

        /**
         * 每日精选
         */
        @Deprecated
        String TYPE_DAILE_PAGE = "dailyPage";

        /**
         * 圈友列表
         */
        @Deprecated
        String TYPE_MOMENT_USER_LIST = "momentCircleUserList";

        /**
         * 职业榜单
         */
        String TYPE_PEDIA_LIST = "professionList";

        /**
         * PGC个人主页
         */
        String TYPE_OPEN_PGC_HOME_PAGE = "openpgchomepage";

        /**
         * 行业详情
         */
        String TYPE_OPEN_INDUSTRY_DETAIL = "industryAtlasDetail";

        /**
         * 发现话题协议
         */
        String TYPE_TOPIC_FEED = "topicfeed";

        /**
         * 话题详情
         */
        String TYPE_GET_FEED = "getfeed";

        /**
         * 我的GET
         */
        String TYPE_MY_GET = "myget";

        /**
         * 提问详情页面
         */
        String TYPE_ANSWER_FEED = "answerfeed";

        /**
         * 我的发布
         */
        @Deprecated
        String TYPE_MY_POST = "mypost";

        /**
         * 我的发布
         */
        String TYPE_GET_DISCOVER = "discoverhome";

        /**
         * 看行业页面
         */
        String TYPE_VIEW_INDUSTRY = "viewIndustry";

        /**
         * 我的关注
         */
        String TYPE_MY_INTEREST = "myInterest";

        /**
         * 配置页面（求职攻略）
         */
        String TYPE_CUSTOM_PAGE = "customPage";

        /**
         * 发布视频
         */
        String TYPE_POST_VIDEO = "postVideo";

        /**
         * 机会行业主页
         */
        String TYPE_OPEN_CHANCE_INDUSTRY = "openChanceIndustry";

        /**
         * 机会每日推荐页面
         */
        String TYPE_OPEN_CHANCE_DAILY_RECOMMEND = "openChanceDailyRecommend";

        /**
         * 工作环境
         */
        String TYPE_WORK_ENVIRONMENT = "workenvironment";
        /**
         * 申请公众号 页面
         */
        String TYPE_APPLY_APPLY_PUBLIC = "applyPublic";
        /**
         * 个人主页信息编辑页
         */
        String TYPE_JUMP_TO_HOME_PAGE_BASE_INFO = "hmpgEditPage";
        /**
         * 社区视频编辑
         */
        String TYPE_EDIT_VIDEO = "editVideo";
        /**
         * 学生实习文本详情
         */
        String TYPE_INTERNSHIP_TWEETS = "internshipTweets";
        /**
         * 视频草稿
         * */
        String TYPE_OPEN_VIDEO_DRAFT = "openVideoDraft";
        /**
         * 通过草稿重新发布
         * */
        String TYPE_OPEN_POST_VIDEO_DRAFT = "postVideoFromDraft";


    }

    interface Live {
        /**
         * 管理员扫码进直播间LiveProtocol
         */
        String TYPE_LIVE_RECRUIT_ROOM_WITH_ADMIN_QR_CODE = "liveRecruitRoomWithAdminQrCode";
        /**
         * 打开直播品牌主页
         */
        String TYPE_LIVE_BRAND_HOMEPAGE = "openLiveBrandHomepage";
        /**
         * 审核失败列表页，先请求直播详情接口如果为什么失败状态跳转到审核失败页，否则跳转到直播列表页
         */
        String TYPE_LIVE_RECRUIT_ROOM_AUDIT_REJECTED = "liveRecruitRoomAuditRejected";
        /**
         * 打开创建直播页面
         */
        String TYPE_CREATE_LIVE_RECRUIT_ROOM = "createLiveRecruitRoom";
        /**
         * 打开B/C直播列表页面
         */
        String TYPE_LIVE_RECRUIT_LIST = "liveRecruitList";
        /**
         * 打开B/C直播间页面
         */
        String TYPE_LIVE_RECRUIT_ROOM = "liveRecruitRoom";
        /**
         * 定制直播预约成功
         */
        String TYPE_CAMPUS_LIVE_CUSTOM_MADE_SUCCESS = "campusLiveCustomMadeSuccess";
        /**
         * 直播学院
         */
        String TYPE_LIVE_COLLEGE_HOME_PAGE = "liveCollegeHomePage";
        /**
         * 直播管理
         * 1118 liveRecruitmentHomePage替换liveRecruitManage
         */
        String TYPE_LIVE_RECRUIT_MANAGE = "liveRecruitmentHomePage";
        /**
         * 直播学院视频列表页面协议
         */
        String TYPE_COLLEGE_TOPIC_VIDEO = "collegeTopicVideo";
        // 视频面试间 跳转InterviewInviteRequest
        String TYPE_ENTER_INTERVIEW_ROOM = "enterInterviewRoom";
        /**
         * 进入直播日程
         */
        String TYPE_GEEK_LIVE_SCHEDULE = "geekLiveSchedule";
        /**
         * 进入直播专栏
         */
        String TYPE_GEEK_LIVE_SPECIAL_COLUMN = "geekLiveSpecialColumn";
        /**
         * 立即开播待开播页面
         */
        String TYPE_LIVE_TO_START_BOSS_PRE_LIVE= "toStartBossPreLive";
    }

    interface Company {
        String TYPE_OPEN_COMPANY_MAIN = "openeditcompany";

        //打开公司完善主页
        String TYPE_COMPLETE_BRAND = "completeBrand";

        // 707 跳转至公司工作体验页面
        String TYPE_COMPANY_WORK_EXP = "editWorkTaste";

        // 打开公司主页
        String TYPE_OPEN_COMPANY_HOMEPAGE = "openCompanyHomepage";

        //打开相似公司
        String TYPE_OPEN_SIMILAR_COMPANY = "similarCompany";

        //打开主营业务修改页面
        String TYPE_EDIT_BRAND_MAIN_BUSINESS = "editBrandMainBusiness";

        // 719 工作体验更多按钮中跳转至编辑入职时间页面
        String TYPE_EDIT_WORK_FEELING_USER_INFO = "editWorkFeelingUserInfo";

        //  跳转至公司福利页面
        String TYPE_OPEN_BRAND_WELFARE = "brandWelfare"; //这个其实是时间

        //打开公司介绍
        String TYPE_OPEN_BRAND_INTRODUCE = "editBrandBrefInfo";
        // 这个是福利待遇
        String TYPE_OPEN_BRAND_TREATMENT = "editBrandWelfare";
        //打开公司视频
        String TYPE_OPEN_BRAND_VIDEO = "editBrandVideo";
        //打开公司相册
        String TYPE_OPEN_BRAND_PHOTO = "editBrandPhoto";

        //打开公司工作体验（没有协议）
        String TYPE_OPEN_BRAND_EXPERIENCE = "editBrandExperience";
        //打开公司VR（没有协议）
        String TYPE_OPEN_BRAND_VR = "editBrandVR";

        //打开产品介绍
        String TYPE_OPEN_BRAND_PRODUCT = "editBrandProduct";

        //打开高管介绍
        String TYPE_OPEN_BRAND_EXECUITIVE = "editBrandExecutive";

        //打开新公司介绍页面
        String TYPE_OPEN_BRAND_NEW_INTRODUCE = "editBrandNewBrefInfo";

        //打开Logo页面
        String TYPE_OPEN_BRAND_LOGO = "editBrandLogo";

        //打开融资阶段页面
        String TYPE_OPEN_BRAND_STAGE = "editBrandStage";

        //打开人才发展页面
        String TYPE_OPEN_BRAND_TALENT = "editBrandTalent";

        // 集团/标准品牌聚合页协议，kind=1,集团页; kind=2,标准品牌页
        String TYPE_BRAND_AGGREGATION = "brandAggregation";
        String TYPE_AGGREGATION = "aggregation";//旧的公司集合页
        //发现公司
        String TYPE_FIND_COMPANY = "findCompany";

        //工作感受列表
        String TYPE_TYPE_WORK_EXP = "workFeelList";


        //VR拍摄页面和VR列表页跳转协议
        String TYPE_BRAND_VR_PICTURE = "brandPanoramicPicture";
        //地址认证拍摄页跳转协议
        String TYPE_ADDRESS_VERIFY_TAKE = "addressVerifyTake";

        //邀请同事填写工作感受
        String TYPE_INVITE_COLLEAGUES_TO_EDIT_WORK_FEEL = "inviteColleaguesToEditWorkFeel";

        //打开模拟面试品牌列表页面
        String TYPE_OPEN_SIMULATE_INTERVIEW_BRAND_PAGE = "openSimulateInterviewBrandPage";
        //环境认证新增地址
        String TYPE_OPEN_ADD_WORK_ADDRESS = "openAddWorkAddress";

    }

    interface Business {

        /*线上道具购买协议*/
        String TYPE_ITEM_ONLINE_BZB = "itemOnlineBzb";

        /*购买/充值直豆*/
        String TYPE_BZB_DETAIL = "bzbDetail";

        /*打开直豆充值页面*/
        String ZD_WALLET = "zdbzbag";

        /*打开道具商城*/
        String TYPE_OPEN_TOOLS_MALL = "opentoolsmall";

        /*购买记录*/
        String TYPE_OPEN_BEAN_OWNED_HISTORY = "beanOwnedHistory";

        /*可退款直豆-领取页面*/
        String TYPE_OPEN_WITH_DRAW = "withdraw";

        //1011.254【商业】畅聊版达上限引导线下道具消耗
        //bosszp://bosszhipin.app/openwith?type=offlineItemUse&encryptItemType=d9cbdd3b551dc1421n0~&encryptJobId=c4ccc63d6e5b51ac0XB829S6FQ~~&encryptUserItemId=481ce282a7e1dcbb1n170tS9EVNR&paramsJson=%7B%22encryptUserCouponId%22%3A%22%22%2C%22encryptJobId%22%3A%22%22%2C%22vip4Select%22%3A1%7D
        String TYPE_ITEM_OFFLINE_ITEM_USE = "offlineItemUse";

        String TYPE_OPEN_CHAT_CHEER_PACK = "blockChatBag";

        String TYPE_VIP_PRIVILEGE_DETAIL = "vipprivilegedetail";
        // 进入直豆交易明细页面
        String TYPE_OPEN_WALLET_DEAL_LIST_PAGE = "beandetail";

        // 808 普通职位升级畅聊购买页
        String TYPE_NORMAL_JOB_UPGRADE = "jobBagUpgrade";

        //直豆充值记录
        String TYPE_BEAN_DETAIL = "beandetail_boss";

        String TYPE_BZB_PAY_METHOD = "paymentConfirmation";

        // 阻断预下订单协议
        String TYPE_BLOCK_PRE_ORDER = "blockbzbpo";

        // 915版本新增新版阻断预下单协议  bosszp://bosszhipin.app/openwith?type=blockbzbpov2?bizParams=&ba=
        String TYPE_ZP_BLOCK_V2_PRE_ORDER = "blockbzbpov2";

        //道具预下单协议 bosszp://bosszhipin.app/openwith?type=itembzb&itemId=11&paramsJson=json&jobId=&source=&lid=&tsItem=
        String TYPE_ITEM_BZB = "itembzb";

        // 1007.291【CRM】线上线索收集
        String TYPE_CONTACT_ME_V2 = "contactmev2";

        // VIP购买成功后原路径返回  bosszp://bosszhipin.app/openwith?type=bzbBackToOriginalPath&url=xxx
        String TYPE_BZB_BACK_TO_ORIGINAL_PATH = "bzbBackToOriginalPath";

        //903.69【商业】曝光加强迭代 优惠领取协议
        String TYPE_BLOCK_GIFT_GET = "blockGiftGet";

        //910.99【商业】VIP2职位商品售卖调整 -> 展开价格样式B点击后弹窗
        String TYPE_BLOCK_JOB_SHOW_MORE_PRICE = "blockJobShowMorePrice";

        //找平台上同事代付
        String TYPE_OTHER_PAY_SEND_MATE = "otherPaySendMate";

        //当面扫码支付
        String TYPE_OTHER_PAY_SCAN_CODE = "otherPayScanCode";

        // 1304.205【商业】代付流程优化：新增打开新的代付界面协议
        String TYPE_OTHER_PAY_MULTI_PAGE = "otherPayMultiPage";

        //在VIP购买成功页点击完成时增加协议跳转(920.258【商业】批量追聊在vip4中售卖实验)
        String TYPE_BATCH_CONTINUE_CHAT_BZB_SUCCESS = "batchContinueChatBzbSuccess";

        //1002.182 进入新招呼快速处理页
        String TYPE_NEW_GREETINGS_QUICK_HANDLE = "newGreetingsQuickHandle";

        //1002.182 进入批量追聊页面 (type=comeBackYesterdayConnected&sourceType=)
        String TYPE_YESTERDAY_CONNECTED = "yesterdayConnected";

        //1007.280批量追聊特权使用 (type=yesterdayConnected&sourceType=)
        String TYPE_COME_BACK_YESTERDAY_CONNECTED = "comeBackYesterdayConnected";

        /**
         * 1013.275【商业】沟通钥匙激活 - bosszp://bosszhipin.app/openwith?type=chatKeyActivate&encryptIds=x&source=x&ba
         */
        String TYPE_CHAT_KEY_ACTIVATE = "chatKeyActivate";

        /**
         * 1019.276 行业限定职位解锁曝光
         */
        String TYPE_RELEASE_EXPOSURE = "releaseExposure";

        // 进入确认支付页
        String TYPE_PAY_CONFIRM = "bzbconfirm";

        // 打开微信支付页面
        String TYPE_OPEN_WEIXIN_PAY_PAGE = "weixinpay";

        String TYPE_ITEM_GIFT_CONTACT_ME = "gatherContactInfo";

        //1014.233
        String TYPE_REFINED_RIGHT_BUY = "refinedRightsBzb";

        // 跳入电话直拨道具购买页
        String TYPE_DIRECT_CALL = "directCall";

        // 710
        String TYPE_GEEK_VIP_DETAIL = "geekVipDetail";

        // 810 上线提醒
        String TYPE_ONLINE_REMIND = "onlineRemind";

        // 请求短信通知的接口
        String TYPE_OPEN_SMS_NOTIFY = "smsNotice";

        // 716 调起使用电话直拨流程
        String TYPE_USE_DIRECT_CALL = "useDirectCall";
        // 708 牛人vip2.0购买后区分跳转页面
        String TYPE_GEEK_VIP_PAY_SUCCESS = "geekVipBzbSuccess";

        // 1224.247 【商业】免费职位操作流程中道具推荐前置：新增道具二合一弹窗打开协议
        String TYPE_PAY_ITEM_DETAIL = "itemdetail";
    }

    interface Boss {
        /*直接打开职位描述编辑页*/
        String TYPE_OPEN_EDIT_JOB_DESC = "openEditJobDesc";
        /*1113.500 */
        String TYPE_OPEN_DIALOG_HUNTER_SIMILAR_JOB = "hunterRepeatPosiOffTip";

        // 打开Boss发布职位页面
        String TYPE_OPEN_BOSS_JOB_POST = "publishjob";
        String TYPE_OPEN_OVER_SEA_ADDRESS_LIST = "openOverseasAddressList";
        String TYPE_OPEN_OVER_SEA_ADDRESS_EDIT = "openOverseasAddressEdit";
        // 打开Boss编辑职位页面
        String TYPE_OPEN_BOSS_JOB_EDIT = "jobedit";
        // 延长职位
        String TYPE_DELAY_JOB = "prolongJob";
        // 开放职位
        String TYPE_OPEN_JOB = "openJob";
        // 打开职位管理类
        String TYPE_OPEN_JOB_MANAGER = "manageJob";
        // 815 从首善的协议中点击发布职位
        String TYPE_POST_JOB_WITH_CHECK_BASE_INFO = "publishjobWithCheckBaseInfo";
        // 919 外链跳app职位发布页协议
        String TYPE_EXTERNAL_POST_JOB_WITH_CHECK_BASE_INFO = "externalPublishjobWithCheckInfo";

        // 打开Boss认证身份页面
        String TYPE_START_CERTIFY = "certify";
        /*901.522 地址管理*/
        String TYPE_BOSS_ADDRESS_UPDATE = "jobAddressUpdate";

        String TYPE_EMERGENCY_DIALOG = "quickjoblist";

        //我的职务
        String TYPE_OPEN_MY_POSITION = "opentitle";
        // 809 跳转到待开放职位列表
        String TYPE_PROMOTE_WAIT_OPEN_JOB_LIST = "vip2PromoteWaitOpenJobList";

        String TYPE_BASE_INFO_BOSS = "baseInfo_boss";

        /**
         * 1013.254
         */
        String TYPE_F1_RELATIVE_RECOMMEND_QUERY_WORD = "f1RecommendQueryWordSearch";
        // 打开公司信息页面
        String TYPE_OPEN_COMPANY_INFO = "opencompanyinfo";
        // 打开公司全称页面
        String TYPE_OPEN_COMPANY_NAME = "opencompanyname";
        // 810 邮箱更新
        String TYPE_BOSS_UPDATE_EMAIL = "updateResumeEmail";
        // 打开人才经纪人立即上传头像页面
        String TYPE_OPEN_AGENT_AVATAR_PAGE = "agentAvatar";

        String TYPE_LOOK_ADDRESS_RELATED_JOBS = "lookAddrRelatedJobs";

        // 打开牛人意向详情页面
        String TYPE_OPEN_RESUME_SINGLE = "single";

        // 打开牛人详情页（1212.80新增协议，避免参数差异对原有逻辑影响）
        String TYPE_OPEN_GEEK_DETAIL = "openGeekDetail";

        // 打开高级搜索的牛人详情页面
        String TYPE_SUPER_SEARCH_GEEK_DETAIL = "opengeekadvance";

        // 打开匿名牛人详情
        String TYPE_OPEN_ANONYMOUS_GEEK_DETAIL = "viewGeek";
        // boss意向沟通
        String TYPE_OPEN_CONTACT_INTENTION = "bossIntentionPage";

        // 更新职位状态
        String TYPE_UPDATE_JOB_STATUS = "updateJobStatus";

        // 7.13 新增F3动态条扫码入口
        @Deprecated
        String TYPE_DYNAMIC_SCAN = "dynamic_scan";
        // 810 页面刷新
        @Deprecated
        String TYPE_REFRESH_PAGE = "refresh";

        /*跳转「新招呼牛人」页*/
        String TYPE_OPEN_NEW_GREET_GEEK_PAGE = "openNewGreetGeekPage";
        // 817.53 电话助手
        String TYPE_PHONE_SWITCH_DETAIL = "phoneSwitchDetail";

        // 1214.500【H】意向沟通app接售卖，意向沟通入口点击回调
        String TYPE_BOSS_INTENTION_CLICK = "bossF4IntentionClick";

        // 引导Boss与其他牛人聊天
        String TYPE_GUIDE_BOSS_CHAT_OTHER_GEEK = "guideBossChatOtherGeek";

        // 1303.1 AI速招 - 进入匹配结果的列表
        String TYPE_OPEN_AI_QUICK_RECRUIT_MATCH_LIST = "openAIQuickMatchGeekList";

        // 1308.170【招聘者】新增AI面试考察重点：新增 AI 收集考察重点界面打开协议
        String TYPE_OPEN_AI_EXAM_FOCUS_PAGE = "openAiExamFocusPage";
    }

    /**
     * Boss高级搜索
     */
    interface AdvancedSearch {
        // 打开高级搜索页面
        String TYPE_SUPER_SEARCH = "supersearch";
        // 进入高搜详情
        String TYPE_OPEN_ADVANCE_SEARCH_RESULT_PAGE = "advancedSearchResults";
        // 815 通过订阅条件跳转到搜索结果
        String TYPE_ADVANCED_SEARCH_SUBSCRIBE_RESULT = "subscribeResult";
    }

    interface GeekSearch {
        /**
         * 打开牛人F1搜索页
         */
        String TYPE_F1_GEEK_SEARCH = "openF1Search";

        /**
         * 打开牛人F1搜索页 跳转到 牛人端搜索（进入搜索页相当于点放大镜）（备注：很久之前的协议了）
         */
        String TYPE_GEEK_SEARCH_JOB = "searchBoss";
    }


    interface Geek {
        /**
         * 1119.602
         * 学生主题全部专区页面 - 详情页
         */
        String TYPE_STU_F1_TOPIC_DETAIL = "studentOperZoneTopicDetail";

        /**
         * 1119.602
         * 学生主题全部专区页面 - 综合专区列表
         */
        String TYPE_STU_F1_TOPIC_PAGE = "studentOperZone";

        String TYPE_AXB_ZONE = "axbJobZone";

        String TYPE_CHOOSE_EXPECT_CITY = "chooseExpectCity";

        String TYPE_EXPECT_EDIT_OPEN = "expectedit";

        /**
         * 添加求职期望
         */
        String TYPE_OPEN_ADD_EXPECT = "addSpecifyExpect";

        String TYPE_LINK_GEEK_SALARY_MODIFY = "linkGeekSalaryModify";

        // 打开管理求职意向页面
        String TYPE_EXPECT_MANAGER = "openexpectmanager";

        // 打开上传附件简历页面
        String TYPE_OPEN_ATTACHMENT_RESUME = "openattachmentresume";

        // F4 工作经历暂不添加事件
        String TYPE_CLOSE_PANEL = "closePanel";
        /**
         * 牛人成就页面
         */
        String TYPE_GEEK_ACHIEVEMENT = "jobSearchAchievements";

        /**
         * 牛人驻外岗位偏好设置页
         */
        String TYPE_GEEK_OVERSEAS_TRAITOPTIONSSETTING = "geekOverseasTraitOptionsSetting";

        /**
         * 牛人驻外打开地图app弹窗计算路线
         */
        String TYPE_NAV_TO_SEL_MAP = "navToSelMap";

        // 806 普蓝完善跳转协议
        String TYPE_SIMPLE_COMPLETE_SUPPLEMENT = "simpleCompleteSupplement";
        // 打开关注公司页面
        String TYPE_OPEN_FOLLOW_COMPANY_PAGE = "openfollowcompany";

        // 打开牛人修改微简历页面
        String TYPE_START_GEEK_MODIFY_RESUME = "editresume";

        //预览在线简历
        String TYPE_MY_RESUME = "previewonlineresume";

        //蓝领我的优点
        String TYPE_PERSONAL_LABEL = "personalLabel";

        // 打开编辑姓名页
        String TYPE_START_EDIT_USER_NAME = "editUserName";

        // 打开添加工作经历页面
        String TYPE_START_ADD_WORK = "workAdd";

        // 打开添加教育经历页面
        String TYPE_START_ADD_EDU = "eduAdd";

        // 打开添加项目经历页面
        String TYPE_ADD_PROJECT = "projectAddOrUpdate";

        // 打开就职信息填写页
        String TYPE_GEEK_ADD_WORK_NO_CONTENT = "workAddNoContent"; // 1223.615 牛人就职信息填写

        // 打开快速填写页面
        String TYPE_OPEN_RESUME_QUICK_INPUT = "openContentGeneration";

        // 打开志愿者经历
        String TYPE_ADD_VOLUNTEER = "volunteerAddOrUpdate";

        //打开添加志愿者经历介绍页面
        String TYPE_OPEN_ADD_VOLUNTEER_PAGE = "volunteerAddGuide";

//        //1114.604 编辑社团/组织经历页
//        String TYPE_OPEN_CLUB_EXP_EDIT = "openClubExpEdit";

        //切换为学生版
        String TYPE_STUDENT_CHANGE = "myidentity";
        // 添加家庭住址
        String TYPE_ADD_HOME_ADDRESS = "addHomeAddress";

        String TYPE_GEEK_ADD_INTEREST_LOCATION = "geekAddInterestLocation";
        // 1113.52 出生地
        String TYPE_UPDATE_HOMETOWN = "updateHometown";

        String TYPE_ADD_HANDICAPPED = "handicapped";//无障碍求职

        // 牛人 求职记录
        String TYPE_GEEK_MY_RECORD = "geekMyRecord";
        String TYPE_GEEK_RECOMM = "geekJobRecomm";

        // 添加家庭住址
        String TYPE_GEEK_NEAR_FIND_JOB = "nearbyPositionList";
        String TYPE_OPEN_ADDRESS_MANAGER = "openGeekAddressManager";

        //最新发布&高新职位
        String TYPE_GET_2_F1SEARCH = "searchPosition";

        String OPEN_RESUME_NAME_EDIT_PAGE = "openResumeNameEditPage";

        //1114.604 编辑社团/组织经历页
        String TYPE_OPEN_CLUB_EXP_EDIT = "openClubExpEdit";

        //1016.4 编辑or添加培训经历
        String TYPE_EDIT_TRAINING_EXP = "openTrainingEdit";

        //810 到作品集上传页
        String TYPE_DESIGN_WORK = "editDesignWorks";

        /**
         * 810
         * 新增协议 209.跳转至附件解析同步流程
         * bosszp://bosszhipin.app/openwith?type=nlpParserSyncProcess&parserId=
         * parserId：附件解析简历同步流程ID
         */
        String TYPE_ATTACHMENT_RESUME_PARSE = "nlpParserSyncProcess";

        // 添加所获荣誉/证书
        String TYPE_OPEN_ADD_HONOR_PAGE = "editHonor";

        //813 追评评价人才经纪人
        String TYPE_ADDTIONAL_EVALUATION_INTERMEDIARY = "addtionalEvaluationIntermediary";

        String TYPE_EVALUATE_AGENT_RECRUIT = "geekEvaluateIntermediary";


        // 编译牛人我的优势页面
        String TYPE_EDIT_USER_ADVANTAGE = "editUserDesc";

        // 上传形象照
        String TYPE_UPLOAD_PERSONAL_IMAGE = "uploadResumeWorkImage";

        // 打开工作内容页
        String TYPE_EDIT_WORK_CONTENT = "openWorkContent";

        // 更新工作经历在职时间
        String TYPE_MODIFY_WORK_TIME = "modifyWorkExpDate";

        // 1124.608打开公司名称编辑页
        String TYPE_EDIT_WORK_COMPANY = "openWorkName";

        // 1125.611 打开教育经历学校名称编辑页
        String TYPE_EDIT_SCHOOL_NAME = "openEduName";

        String TYPE_EXECUTE_NATIVE_ACTION = "executeNativeAction";

        String TYPE_AI_ASSISTANT = "aiAssistant";
        String TYPE_ADD_EXPECT ="addExpect";
        String TYPE_OPEN_PERSONAL_RECOMMEND ="openPersonalRecommend";

        // "海螺高选"牛人求职安置闭环(https://zhishu.zhipin.com/wiki/ZpnFhLufPDQ)
        String TYPE_GEEK_MAP_NAVIGATION = "geekMapNavigation";

        String TYPE_ADD_INTERESTED_CITY_FOR_HK = "addInterestedCityForHK";
        String TYPE_BLUE_COLLAR_TOPIC = "blueCollarTopic";

        String TYPE_MODIFY_PHONE_NUMBER ="modifyPhoneNumber";
        String TYPE_AGREE_BLUE_WHITE_PROTOCOL ="agreeBlueWhiteProtocol";

        String TYPE_UPDATE_POSITION_UNLIMIT_SWITCH ="updatePositionUnlimitSwitch";

        String TYPE_UPDATE_SHORT_TIME_JOB_SWITCH ="updateShortTimeJobSwitch";

        String TYPE_OPEN_VIRTUAL_PART_TIME="openVirtualParttime";

        /**
         * 打开大卡页面
         */
        String TYPE_OPEN_GEEK_GREET_CARD_LIST = "openGeekGreetCardList";
    }


    interface WisdomStone {
        /*打开客服聊天页*/
        String TYPE_CUSTOMER_SERVICE_GUIDE = "customerServiceGuide";
        /*715 人工客服评价*/
        String TYPE_CUSTOMER_STAR_EVALUATE = "customerStarEvaluate";
        /*推送未完成的订单列表消息（客服聊天页面协议）*/
        String PUSH_UN_FINISH_ORDER_LIST_MSG = "pushUnfinishOrderListMsg";
        /*重新发送未完成订单通知（客服聊天页面协议，未完成订单详消息情页中确定按钮协议）*/
        String RESEND_UN_FINISH_ORDER = "resendUnfinishOrder";
        /*推送未完成的订单详情*/
        String PUSH_UN_FINISH_ORDER_DETAIL_MSG = "pushUnfinishOrderDetailMsg";
        /*智慧石中修改职位*/
        String MODIFY_JOB_4_WISDOM_STONE = "modifyJob4Wisdomstone";
        /*智慧石中职位详情页*/
        String JOB_DETAIL_4_WISDOM_STONE = "jobDetail4Wisdomstone";
        /*推送相关咨询知识库列表*/
        String PUSH_KNOWLEDGES_4_WISDOMSTONE = "pushKnowledges4Wisdomstone";
        /*主动问询答案*/
        String AUTOMATIC_ASK_REPLY_4_WISDOMSTONE = "automaticAskReply4Wisdomstone";
        /*C端 多轮对话， 推荐职位*/
        String RECOMMEND_JOB_LIST = "recommendJobList";
        /*【智慧石】智慧石辅助获取销售线索*/
        String SUBMIT_CALL_IN = "submitcallin";
        /*【智慧石】调整主动问询推送内容的展示*/
        String SUBMIT_NLP_SURVEY = "submitNlpSurvey";
        /*智慧石聊天刷新  已解决  未解决  消息*/
        String CANCEL_JOB_PUBLISH = "cancelJobPublish";
    }

    interface Interview {
        //1115.70 普蓝视频检测
        String INTERVIEW_INSPECT = "interviewInspect";
        /* 6.18 Boss回答面试结果*/
        String TYPE_BOSS_INTERVIEW_DIALOG = "interviewresult";

        // 708 boss跳转邀约面试页面
        String TYPE_BOSS_INVITE_INTERVIEW = "bossInviteInterview";
        /**
         * 打开面试/复试分享详情 （6.13）
         */
        String TYPE_OPEN_INTERVIEW_SHARE_DETAIL = "openInterviewShareDetail";
        /**
         * 1004.040 已有面试后约流程优化 - 若前序（同一个C和沟通职位）面试状态流转到2、3、5、8、12、13、14、15（已拒绝、已取消、已超时、面试时间已过、已完成、牛人没来）时
         * 点击已有创建面试卡片或【面试TA】直接进入新的约面页面
         */
        String TYPE_INTERVIEW_INVITE = "interviewAfreshInvite";
        /**
         * 1007.40 语义达成面试 - boss点击牛人申请的面试卡片
         */
        String TYPE_CONFIRM_INTERVIEW = "interviewIntentionDetailBoss";
        String TYPE_OPEN_INTERVIEW_MANAGER = "interviewmanage";
        String TYPE_OPEN_INTERVIEW_UNANSWERDE = "interviewUnanswered";
        /**
         * 1007.40 语义达成面试 - 牛人发送面试日程邀请气泡
         */
        String TYPE_GEEK_INTERVIEW_INVITE = "interviewInviteGeek";
        /**
         * 1007.40 语义达成面试 - 牛人查看牛人发送的面试邀请详情
         */
        String TYPE_GEEK_INTERVIEW_INTENTION_DETAIL = "interviewIntentionDetailGeek";
        /**
         * 1013.42 Boss发送牛人面试结果气泡
         */
        String TYPE_BOSS_INTERVIEW_SEND_RESULT = "interviewSendResult";
        /**
         * 1013.42 牛人获取面试结果
         */
        String TYPE_GEEK_INTERVIEW_GET_RESULT = "interviewGetResult";

        /**
         * 1013.47 牛人点击面试评价气泡
         */
        String TYPE_GEEK_INTERVIEW_FEEDBACK = "geekInterviewAnswer";

        /**
         * 1018.47 牛人询问面试结果
         */
        String TYPE_GEEK_INTERVIEW_ASK_RESULT = "interviewEnquireResult";
        /**
         * 1107.42 聊天灰条 跳转到牛人面试日程帮助中转页
         */
        String TYPE_INTERVIEW_GEEK_ARRANGE_HELP = "interviewScheduleTip";
        /**
         * 1107.42 聊天顶部面试 点击 跳转到牛人面试日程详情
         */
        String TYPE_INTERVIEW_GEEK_ARRANGE_DETAIL = "geekAddInterviewdetail";
        /**
         * 1109.41 金牌面试官H5 点击去 约面 跳转到面试日程，并弹出待约面牛人dialog
         */
        String TYPE_INTERVIEW_BOSS_INTENTION_LIST = "bossIntentionInterviewList";
        /**
         * 1112.42 三方面试识别 面试邀请
         */
        String TYPE_INTERVIEW_INVITE_BY_THIRD_INFO = "interviewInviteByThirdInfo";
        //905nlp打开面试
        String TYPE_BOSS_INVITE_INTERVIEW_NLP = "openInterviewInvite";

        String TYPE_BOSS_INVITE_VIDEO_INTERVIEW = "bossInviteVideoInterview";

        String TYPE_ADD_INTERVIEW_QUESTION = "addInterviewQuestion";
        String TYPE_OPEN_INTERVIEW_RECORD = "interviewRecord";
        // 打开面试详情页面
        String TYPE_INTERVIEW_DETAIL = "interviewdetail";
        // 跳转面试反馈页面
        String TYPE_INTERVIEW_FEEDBACK = "interviewfeedback";
        // 打开面试列表页面
        String TYPE_OPEN_INTERVIEW_LIST_PAGE = "interviewlist";

        String TYPE_HUNTER_FEED_BACK_INTERVIEW = "hunterFeedBackInterview";

        //804爽约二期  牛人爽约投诉列表协议
        String TYPE_GEEK_COMPLAIN_LIST = "geekComplainList";

        /**
         * 1123.41 小秘书点击未处理面试
         */
        String TYPE_GET_UNACCEPT_INTERVIEW = "getGeekUnAcceptInterview";

        /**
         * 1012.42 面试引导表达进度
         */
        String TYPE_EDIT_INTERVIEWREMIND_SUGGEST = "editInterviewRemindSuggest";
    }

    interface Chat {
        String TYPE_VIRTUAL_CALL = "virtualCall";
        //814同意接受视频简历
        String TYPE_ACCEPT_VIDEO_RESUME = "acceptVideoResume";
        //814拒绝接收视频简历
        String TYPE_REJECT_VIDEO_RESUME = "rejectVideoResume";

        // 手机助手气泡电话联系TA
        String TYPE_CONTACT_ASSIST_CALL = "contactAssistCall";
        //手机助手气泡电话交换
        String TYPE_CONTACT_ASSIST_EXCHANGE = "contactAssistExchange";
        String TYPE_WECHAT_ASSIST_EXCHANGE = "wechatAssistExchange";
        //急聘发送简历
        String TYPE_SEND_RESUME_BY_QUICK_TOP = "sendResumeByQuickTop";

        //急聘交换电话
        String TYPE_EXCHANGE_PHONE_BY_QUICK_TOP = "exchangePhoneByQuickTop";


        //快捷回复卡片按钮新增协议:
        String TYPE_BOSS_FAST_REPLY = "bossFastReply";

        //气泡新增协议
        String TYPE_SEND_FAST_REPLY_CARD = "sendFastReplyCard";

        //同事推荐协议
        String TYPE_MATE_SHARE_LIST = "mateShareList";

        //解绑登录微信号设置页
        String TYPE_WX_BINDING_SETTING = "wxBindingSetting";

        //修改密码
        String TYPE_HEAD_HUNTER_CHAT_SETTING = "hunterChatSetting";

        //打开聊天好友设置页面
        String TYPE_OPEN_CHAT_SETTING = "friendChatSetting";

        //查看历史聊天记录
        String TYPE_TRANSMIT_HISTORY = "chatMsg";


        String TYPE_OPEN_CHAT_REMARK = "remarkFriend";

        String TYPE_DIAL_2_FRIEND = "dial2Friend";

        String TYPE_PHONE_CALL_ITEM = "phoneCall4Item";

        String TYPE_OEPN_WEIXIN_EDIT = "openWeixinEdit";

        String TYPE_OEPN_QUESTION_DESC = "openQuestionDesc";

        String TYPE_CLOSE_GEEK_SELF_ANSWER = "closeGeekSelfAnswer";

        String TYPE_OPEN_NOTIFY_INTEREST = "opennotifyinterest";

        String TYPE_OPEN_VIEWED = "openviewed";

        String TYPE_OPEN_NEW_PEOPLE = "opennew";

        String TYPE_OPEN_COMMON_WORDS = "opencommonwords";

        @Deprecated
        String TYPE_GEEK_ESTIMAT_CARD = "geekEstimatCard";


        String TYPE_OPEN_CHAT_PAGE = "chatview";

        String TYPE_OPEN_MINIPROGRAM = "openMiniProgram";

        String TYPE_CALL_VIDEO = "callVideo";

        String TYPE_FILTER_MESSAGE = "filterMessage";

        String TYPE_JOIN_MEDIA_INTERVIEW = "joinMediaInterview";

        String TYPE_CHAT_SWITCH_JOB = "chatSwitchJob";

        String TYPE_VIDEO_RESUME = "videoResume";

        String TYPE_CLOSE_SECOND_GREETING = "closeSecondGreeting";

        String TYPE_PLAY_FAST_REPLY_VIDEO = "playFastReplyVideo";

        String TYPE_INVITE_FEEDBACK = "invitefeedback";

        String TYPE_VIDEO_FAST_REPLAY = "videoFastReply";

        String TYPE_ADD_USER_REJECT = "addUserReject";


        String TYPE_FAST_CONTACT = "fastContact";


        String TYPE_NEW_EXPECT_WORK_LOCATION = "newExpectWorkLocation";


        String TYPE_TODAY_MATCH = "dailyJobRecommend";

        String TYPE_GEEK_DIRECT_CALL = "geekDirectCall";

        String TYPE_GEEK_HUNTER_EVALUATE_TAG = "geekHunterEvaluationTags";

        String TYPE_REMIND_PRIORITY_GEEK = "remindPriorityGeek";

        //回到标题开始
        //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=145052347
        //1001.166【招聘者】针对生产制造行业普工包职位的反馈收集（第一期）
        String TYPE_JOB_FREE_BACK = "jobFeedBack";

        String TYPE_HANDICAPPED_SWITCH = "handicappedSwitch";

        String TYPE_OPEN_BLACK_LIST = "openBlackList";

        /**
         * 定义的只埋点不处理业务的协议
         */
        String TYPE_BG_ACTION = "bgaction";

        /**
         * 1225版本后仅打点不处理任何逻辑使用该协议
         */
        String TYPE_BG_ACTION_ONLY = "bgActionOnly";

        String TYPE_GEEK_USER_BLACK_OPT = "userBlackOpt";

        //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157169169
        String TYPE_REASSURANCE_FEED_BACK = "reassuranceFeedback";

        String TYPE_GEEK_FAST_REPLY = "geekFastReply";

        String TYPE_INTENTION_BOSS_CHAT_CARD = "intentionBossChatCard";

        String TYPE_OPEN_FAST_REPLY_LIST = "openFastReplyList";

        String TYPE_GEEK_WX_NOTICE_GUIDE = "geekWxNotifyGuide";


        String TYPE_GET_ALL_HISTORY_EXCHANGE = "getAllHistoryExchange";

        String TYPE_BASE_INFO_GEEK = "baseInfo_geek";

        String TYPE_USER_BOTH_WAY_CALL = "useBothWayCall";

        String TYPE_DIAL_PHONE_NUMBER = "te";

        String TYPE_JOB_QUES_ANSWER_LIST = "jobQuesAnswerList";


        String TYPE_JOB_QUES_ANSWER = "jobQuesAnswer";

        String TYPE_CONFIG_JOB_QUESTION = "configJobQuestion";

        String TYPE_GET_GEEK_ANSWERS = "getGeekAnswers";

        String TYPE_RECOMMEND_JOB_CARD = "recommendJobCards";

        String TYPE_HANDLE_BLUE_COLLAR_ONE_KEY_APPLY = "handleBlueCollarOneKeyApply";

        String TYPE_OPEN_PRIVACY_SETTINGS = "privacySettings";

        String TYPE_OPEN_VIDEO = "arrangeVideoInterview";

        String TYPE_ACC_CALL_CARD = "accCallCard";
        /**
         * 1122.91 [B] 触发境外提示后解除提示
         */
        String TYPE_VALIATE_BOSS_GEO = "validateBossGeo";

        /**
         * 转客服
         */
        String TYPE_TRANSFER_CUSTOMER = "transferCustomer";

        /**
         * 打开公告页面
         */
        String TYPE_OPEN_NOTICE_PAGE = "opennotice";
        /**
         * 添加跳转到通知协议
         */
        String TYPE_OPEN_NOTICE = "openNotifications";

        String TYPE_CHAT_HELPER_EXPLAIN_PROVIDER = "chatHelperExplainPopover";

        String TYPE_MODIFY_CHAT_HELPER_STATUS = "modifyChatHelperStatus";
        String TYPE_CHAT_HELPER_RESTART = "chatRobotRestart";


        // 713 蓝领同意快速交换电话
        String TYPE_FAST_CONTACT_ACCEPT = "fastContactAccept";

        String TYPE_ZP_SCORE_SETTING = "zpscoreSetting";


        String TYPE_GEEK_GPT = "GeekGpt";

        String TYPE_CHAT_ROBOT_SETTING = "chatRobotSetting";
        String TYPE_CHAT_CHAT_ROBOT_EXPERIENCE = "chatRobotExperience";

        String TYPE_UNFIT = "uninterestedBoss";

        // 810 购买VIP返回开聊页
        String TYPE_BACK_TO_CHAT = "bzbBackToChat";

        String TYPE_SELF_ADVANTAGE_EDIT = "selfAdvantageEdit";

        // 打开与蓝领小程序聊天设置页面
        @Deprecated
        String TYPE_OPEN_FAST_GEEK_MSG_SETTING_PAGE = "openFastGeekMsgSetting";

        String TYPE_GEEK_SCORE_SHARE = "geekScoreShare";

        // 事件发送类型
        String TYPE_ACTION_SEND = "sendaction";
        // 打开立即举报对话框
        String TYPE_OPEN_REPORT_DIALOG = "openreportdialog";

        String TYPE_CHECK_GEEK_HUNTER = "checkHunterJumpUrl";
        String TYPE_GEEK_ONE_KEY_DELIVER = "oneKeyDeliver";

        String TYPE_CALL_MEDIA_INTERVIEW = "callMediaInterview";

        String TYPE_GET_BOSS_VIDEO_JOB_VIRTUAL_PHONE = "getBossVideoJobVirtualPhone";
        String TYPE_GET_GEEK_VIDEO_JOB_VIRTUAL_PHONE = "getGeekVideoJobVirtualPhone";
        String TYPE_VERBAL_INTERVIEW = "verbalInterview";
        String TYPE_GUAN_JIA_CONTINUE_SERVICE = "guanJiaContinueService";

        // 1217.613【C端】蓝领-被开聊场景引导 C 快捷回复 bosszp://bosszhipin.app/openwith?type=sendTextMessage&text="请问,hello"
        String TYPE_SEND_TEXT_MESSAGE = "sendTextMessage";

        // 1218.601【陆港融合】应法务需求调整获取C授权的方式
        String TYPE_UPDATE_USER_NOTIFY_SETTING = "updateUserNotifySetting";
        String TYPE_OPEN_NO_INTEREST_SETTING = "openNoInterestSetting";

        // 1225.619【C】简化交换流程—被开聊场景快捷交换
        String TYPE_OPEN_MSG_TIP = "openMsgTip";
        String TYPE_SEND_TEXT_AND_EXCHANGE = "sendTextAndExchange";
        String TYPE_OPEN_GEEK_CHAT_QUICK_LIST = "openGeekChatQuickList";
        String TYPE_SHOW_ADD_WIDGET_DIALOG = "showAddWidgetDialog";
        String TYPE_CLICK_REQUEST_EXCHANGE = "oneClickRequestExchange";
        String TYPE_CLICK_REQUEST_EXCHANGE_CANCEL = "oneClickRequestExchangeCancel";
        String TYPE_CHAT_HELPER_QUICK_SETTING = "chatHelperQuickSetting";

        /**
         * 1307.80【C】缓解部分招聘者超期会话资源占用问题
         */
        String TYPE_FRIEND_CLEAN = "cleanFriends";

        /**
         * 1308.500【高C】职位订阅精英版-Offer小伴
         */
        String TYPE_AI_OFFER_COMPANION = "aiOfferCompanion";
        /**
         * 1308.165【B&C】AI帮你推
         */
        String TYPE_AI_DIRECT_EXPLAIN = "aiDirectExplain";

        /**
         * 1308.165【B&C】AI帮你推
         */
        String TYPE_CHAT_EXCHANGE = "chatExchange";

        /**
         * 1312.881 【海雀智选】主播求职者「简历+」生产及应用
         */
        String TYPE_SEND_LIGHT_RESUME = "clickStreamerMicroResumeRequestCard";
    }
    /**
     * 群聊协议
     */
    interface ChatGroup {
        /**
         * 悟空项目外包聊天
         */
        String TYPE_WUKONG_CHAT_ROOM = "wkGroupChatRoom";

        /**
         * 1119.252 进入悟空群聊列表 任务外包盒子
         */
        String TYPE_OPEN_WUKONG_LIST = "openWukongList";

        /**
         * 进入群聊天页面
         */
        String TYPE_GROUP_CHAT_ROOM = "groupChatRoom";
        /**
         * 进入群资料页
         */
        String TYPE_GROUP_CHAT_MATERIAL = "groupView";

        /**
         * 进入群设置界面
         */
        String TYPE_GROUP_INVITE = "groupInvite";

        /**
         * 1202.608 简历付费辅导 C 点击卡片上传简历
         */
        String TYPE_RESUME_UPLOAD = "chatGroupFileUpload";

    }

    interface Settings {
        String TYPE_BETA_SETTING = "betaSetting";
        // 716 调起设置电话授权页面
        String TYPE_OPEN_DIRECT_CALL = "openDirectCallSetting";

        String TYPE_OPEN_PERMISSION_MANAGER = "openPermissionManager";

        // VIP设置页
        String TYPE_OPEN_VIP_SETTINGS_LIST = "vipSettingList";
        // 退出登录设置页
        String TYPE_LOGOUT_SETTING = "logoutSetting";
        // 设置密码（如果设置过就更新密码）
        String TYPE_SETTING_PASSWORD = "modifypwd";
        // 打开开聊引导语页面
        String TYPE_OPEN_CHAT_GUIDE_PAGE = "openchatguidesetting";
        //跳转隐私设置-屏蔽公司
        String TYPE_OPEN_PRIVACY_MASK_COMPANY = "privacyMaskCompany";
        // 跳转屏蔽公司搜索页
        String TYPE_OPEN_PRIVACY_MASK_COMPANY_SEARCH = "searchPrivacyMaskCompany";
        String TYPE_NOTIFY_SETTING = "notifySetting";
        //牛人活动记录管理
        String TYPE_GEEK_ACTIVE_RECORD_MANAGE = "activeRecordManage";
        //904.19 客服引导消息跳转消息推送设置页
        String TYPE_LINK_MSG_SETTING_FROM_CALL_CENTER = "linkMsgSettingFromCallCenter";
        //1009.41打开面试设置页，并高亮对应开关
        String TYPE_OPEN_INTERVIEW_SETTING = "interviewConfig";
        //牛人登录设备管理
        String TYPE_GEEK_LOGIN_DEVICE_MANAGE = "loginDevicesManage";

        String TYPE_OPEN_SYSTEM_SETTING = "openSystemSetting";

        String TYPE_GOVERNMENT_SERVICE = "chaoyangHumanPhone";
        // 招呼语问题回复引导
        String TYPE_GREETING_QUESTION_REPLY_OPEN_GUIDE = "greetingQuestionGuide";
        // 微信设置
        String TYPE_OPEN_WECHAT_NOTIFY_SETTING = "openWeixinNotifySetting";

        // 1216.251 新增打开道具功能管理页
        String TYPE_ITEM_FUNCTION_MANAGEMENT = "itemFunctionManagement";

        // 1307.609【B&C】APP推送通知相关设置页面优化：打开通知设置的子界面
        String TYPE_OPEN_SUB_SETTING_PAGE = "openSubSettingPage";
        String TYPE_CHAT_HELPER_SETINFO = "chatRobotSetInfo";
    }

    interface Login {
        String TYPE_CHANGE_LOGIN_PHONE = "changeLoginPhone";

        String TYPE_THIRD_LOGIN = "thirdLogin";

        String TYPE_VISITOR_LOGIN = "visitorLogin";

    }

    interface Web {
        // 打开WebView页面
        String TYPE_OPEN_WITH_SYSTEM_BROWSER = "system_browser";
        // 打开WebView页面
        String TYPE_START_WEB_VIEW = "webview";
        String TYPE_START_NO_LOGIN_WEB_VIEW = "noLoginWeb";
        // 1011WebView里打开WebView页面
        String TYPE_START_NEW_WEB_VIEW = "newwebview";

        String TYPE_CODE_EDIT = "codeEdit";
        
        // 打开WebView半屏页面
        String TYPE_EMBEDDED_WEB_VIEW = "embeddedWebview";
    }

    interface Share {
        // 启动分享弹出框
        String TYPE_OPEN_SHARE_VIEW = "share";

        //920.267跳转分享页
        String TYPE_JUMP_TO_SHARE_PAGE = "jumpToSharePage";
    }
}
