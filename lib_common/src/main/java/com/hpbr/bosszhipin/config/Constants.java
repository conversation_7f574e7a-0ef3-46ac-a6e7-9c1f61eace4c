package com.hpbr.bosszhipin.config;

import com.monch.lbase.LBase;

/**
 * Created by monch on 15/7/21.
 */
public class Constants {

    public static final String PREFIX = LBase.getPackageName();

    public static final String HOT_CITY = "热门城市";
    public static final String CURRENT_CITY = "当前城市";
    public static final String LOCATION_CITY = CURRENT_CITY;
    public static final String LOCATION_HISTORY = "当前/历史访问城市";

    public static final String KEY_DAYS = Constants.PREFIX + ".days";
    public static final String KEY_CERT_NAME = Constants.PREFIX + ".certName";
    public static final String KEY_EXTRA_MAP = PREFIX + "KEY_EXTRA_MAP";
    public static final String KEY_STATUS = PREFIX + "KEY_STATUS";
    public static final String KEY_EXP_GROUP = PREFIX + "KEY_EXP_GROUP";
    public static final String KEY_JUMP_URL = PREFIX + "KEY_JUMP_URL";
    public static final String KEY_JUMP_TO = PREFIX + "KEY_JUMP_TO";
    public static final String KEY_SOURCE = PREFIX + "KEY_SOURCE";
    /*是否是首个引导流程*/
    public static final String IS_FISRT_GUIDE_PROCESS = PREFIX + "IS_FISRT_GUIDE_PROCESS";
    public static final String KEY_GUIDE_PROGRESS_TYPE = PREFIX + "KEY_GUIDE_PROGRESS_TYPE";
    public static final String KEY_IS_ADVANTAGE_CALL_BACK = PREFIX + "KEY_IS_ADVANTAGE_CALL_BACK";

    public static final String RECOMMEND_CITY = "可能感兴趣城市";
    public static final String RECOMMEND_CITY_1 = "感兴趣的城市";
    public static final String RECOMMEND_CITY_GRAY = "推荐城市";


    public static final String SP_UPLOAD_SECTURITY_LOG_FREQUENCY = PREFIX + ".SP_UPLOAD_SECTURITY_LOG_FREQUENCY";

    /**
     * 用于记录APP启动时间的KEY，存于SP中
     */
    public static final String SP_RECORD_START_TIMER_KEY = PREFIX + ".SP_RECORD_START_TIMER_KEY";

    /**
     * 用于记录APP从后台恢复时间的KEY，存于SP中
     */
    public static final String SP_RECORD_RECOVERY_TIMER_KEY = PREFIX + ".SP_RECORD_RECOVERY_TIMER_KEY";

    public static final String SP_SMS_PURCHASE_POP_UP = PREFIX + ".SP_SMS_PURCHASE_POP_UP";
    public static final String SP_BOSS_FILTER_TIPS = PREFIX + ".SP_BOSS_FILTER_TIPS";
    public static final String SP_BOSS_AI_ENTRANCE = PREFIX + ".SP_BOSS_AI_ENTRANCE";
    public static final String SP_GEEK_JD_IMPROPER = PREFIX + ".SP_GEEK_JD_IMPROPER";
    public static final String SP_NOT_COMPLETE_TIPS = PREFIX + ".SP_NOTCOMOPLETE_FILTER_TIPS";
    public static final String SP_ANXINAPPLY_TIPS = PREFIX + ".SP_ANXINAPPLY_TIPS";

    public static final String SP_PERSONALITY_RECOMMEND_F1_BOTTOM_POP = PREFIX + ".SP_PERSONALITY_RECOMMEND_F1_BOTTOM_POP";

    public static final String SP_SUBSCRIBE_CANCEL = "subscribe_cancel";

    /**
     * 游客模式 用于存储当前的用户角色的KEY，存于SP中，0代表牛人，1代表BOSS
     */
    public static final String SP_TOURIST_USER_ROLE = PREFIX + ".SP_USER_ROLE";

    /**
     * 用于存储当前的用户状态的KEY，存于SP中，0代表未登录，1代表已经登录，2代表已退出
     */
    public static final String SP_USER_LOGIN_STATUS = PREFIX + ".SP_USER_LOGIN_STATUS";

    /**
     * apm 上报使用，记录当前用户是否是首次登录
     */
    public static final String SP_APM_HAS_USER_FIRST_LOGIN = PREFIX + ".APM_HAS_USER_FIRST_LOGIN";

    /**
     * 用于存储当前是否为第一次进入APP
     */
    public static final String IS_FIRST_OPEN_KEY = "IS_FIRST_OPEN_KEY";

    /**
     * Boss聊天页面显示popupWindow
     */
    public static final String SP_SHOW_CONTACT_POP = PREFIX + ".SHOW_CONTACT_POP";

    /**
     * 牛人F1，是否查看过职位详情
     */
    public static final String SP_GEEK_F1_JOB_HAS_CLICKED = PREFIX + ".GEEK_F1_JOB_FIRST_CLICK";

    /**
     * 简历刷新引导
     */
    public static final String SP_RESUME_REFRESH_GUIDE_POPUP_ON_RESUME_ONLINE = PREFIX + ".SP_RESUME_REFRESH_GUIDE_POPUP";
    public static final String SP_RESUME_REFRESH_GUIDE_POPUP_ON_RESUME_ATTACHMENT = PREFIX + ".SP_RESUME_REFRESH_GUIDE_POPUP";

    public static final String SP_SHOW_LAUNCHER_KEY = PREFIX + ".SP_SHOW_LAUNCHER_KEY";
    public static final String SP_GPT_ALERT_DIALOG = PREFIX + ".SP_GPT_ALERT_DIALOG";
    public static final String SP_BOSS_SUPPLEMENT_COMPLETE = PREFIX + ".SP_BOSS_SUPPLEMENT_COMPLETE";
    public static final String SP_BOSS_SUPPLEMENT_COMPLETE_V2 = PREFIX + ".SP_BOSS_SUPPLEMENT_COMPLETE_V2";

    /**
     * 虚拟电话弹窗提示展示次数
     */
    public static final String SP_VIRTUAL_CALL_POP_FLOAT_DIALOG_SHOW_TIMES = PREFIX + ".SP_VIRTUAL_CALL_POP_FLOAT_DIALOG_SHOW_TIMES";
    public static final String SP_VIRTUAL_CALL_NEED_REQUEST = PREFIX + ".SP_VIRTUAL_CALL_NEED_REQUEST";

    public static final String SP_RESUME_PREVIEW_GUIDE_POP = PREFIX + ".SP_RESUME_PREVIEW_GUIDE_POP";

    public static final String SP_VIRTUAL_CALL_TIP_SHOW = "virtual_call_tip";
    /*1210.275【商业】沟通钥匙支持主动拨打电话消耗，沟通钥匙引导气泡展示时间戳记录*/
    public static final String SP_VIRTUAL_CALL_TIP_SHOW_CONNECT_KEY_DAYS = "virtual_call_tip_connect_key_days";
    public static final String SP_GEEK_F1_TAB_INDEX = "geek_f1_tab_index";
    public static final String SP_GEEK_F1_TAB_MIXED_CARD_SHOW = "geek_f1_tab_mixed_card_show";

    /**
     * boss 进入公司首善流程
     */
    public static final String SP_BOSS_HAVE_COMPANY_COMPLETE = "SP_BOSS_HAVE_COMPANY_COMPLETE";

    public static final String SP_JOB_QA_HAS_VIEWED = "job_qa_viewed";

    public static final String SP_SUPPORT_X5 = "sp_support_x5";

    public static final String SP_ADVANCED_SEARCH_CURRENT_TIP = "sp_advanced_search_current_tip";

    public static final String SP_COMPOSITE_TOP_INTRO_CARD = "sp_composite_top_intro_card";


    public static final String SP_LAST_JOB_MEMORY_RECORD_GRAY = PREFIX + ".SP_LAST_JOB_MEMORY_RECORD_GRAY";

    public static final String SP_HOT_DEFAULT_SELECT_JOB_ID = PREFIX + ".SP_HOT_DEFAULT_SELECT_JOB_ID";

    public static final String SP_MAIN_TAB_INDEX_SELECTION = PREFIX + ".SP_MAIN_TAB_INDEX_SELECTION";


    public static final String SP_ADVANCED_MID_PAGE_SELECT_JOB_ID = PREFIX + ".SP_ADVANCED_MID_PAGE_SELECT_JOB_ID";

    public static final String SP_GEEK_VIP_INFO_POP_WINDOW = PREFIX + ".SP_GEEK_VIP_INFO_POP_WINDOW";

    public static final String SP_COM_INSTRUCTION_SHOWED = PREFIX + ".COM_INSTRUCTION_SHOWED";

    public static final String SP_RESUME_COACHING_ENTRANCE_NEW_TAG = PREFIX + ".RESUME_COACHING_ENTRANCE_NEW_TAG";
    public static final String SP_RESUME_COACHING_ENTRANCE_DOT = PREFIX + ".RESUME_COACHING_ENTRANCE_DOT";

    public static final String SP_F1_WARNING_TIP_BAR_CLOSE = PREFIX + ".SP_F1_WARNING_TIP_BAR_CLOSE";

    public static final String SP_AI_CHAT_HELPER_ENTRANCE_RED_DOT = PREFIX + ".SP_AI_CHAT_HELPER_ENTRANCE_RED_DOT";

    public static final String SP_BOSS_COMPLETION_INFO_TEMP = PREFIX + ".SP_BOSS_COMPLETION_INFO_TEMP";

    public static final String SP_EXPECT_DIAGNOSE_TIP = PREFIX + ".SP_EXPECT_DIAGNOSE_TIP";

    public static final String SP_REJECT_INTENT_CONTACT_LIST_TIP = PREFIX + "reject_intent_contact_list_tip";
    public static final String SP_REJECT_INTENT_CONTACT_CLOSE_TIP = PREFIX + "reject_intent_contact_close_tip";

    public static final String SP_SHOW_AI_QUICK_RECRUIT_GUIDE = PREFIX + "SP_SHOW_AI_QUICK_RECRUIT_GUIDE";

    public static final String SP_SHOW_AI_SEARCH_GUIDE = PREFIX + "SP_SHOW_AI_SEARCH_GUIDE";

    // ********** ---------- DATA ---------- ********** //
    /**
     * 参数KEY，埋点ID
     */
    public static final String DATA_LID = "DATA_LID";
    /**
     * 参数KEY，实体类
     */
    public static final String DATA_ENTITY = PREFIX + ".DATA_ENTITY";
    public static final String DATA_ENTITY_INDUSTRY = PREFIX + ".DATA_ENTITY_INDUSTRY";

    public static final String DATA_ENTITYS = PREFIX + ".DATA_ENTITYS";

    /**
     * 参数KEY，实体类
     */
    public static final String DATA_ENTITY1 = PREFIX + ".DATA_ENTITY1";

    public static final String DATA_ENTITY2 = PREFIX + ".DATA_ENTITY2";
    public static final String DATA_ENTITY3 = PREFIX + ".DATA_ENTITY3";
    /**
     * 参数KEY，路径
     */
    public static final String DATA_URL = "DATA_URL";
    /**
     * 参数KEY，直聘URL
     */
    public static final String DATA_ZP_URL = PREFIX + ".DATA_ZP_URL";
    /**
     * 参数KEY，id
     */
    public static final String DATA_ID = PREFIX + ".DATA_ID";
    /**
     * 参数KEY，title
     */
    public static final String DATA_TITLE = PREFIX + ".DATA_TITLE";
    /**
     * 参数KEY，query
     */
    public static final String DATA_QUERY = PREFIX + ".DATA_QUERY";
    /**
     * 参数KEY，prefix
     */
    public static final String DATA_PREFIX = PREFIX + ".DATA_PREFIX";
    /**
     * 参数KEY，id2
     */
    public static final String DATA_ID2 = PREFIX + ".DATA_ID2";
    /**
     * 参数KEY，jobId
     */
    public static final String DATA_JOB_ID = PREFIX + ".DATA_JOB_ID";

    /**
     * 参数KEY，jobId
     */
    public static final String DATA_F1_SUB_TAB = PREFIX + ".DATA_F1_SUB_TAB";

    public static final String DATA_F1_NEAR_SOURCE = PREFIX + ".DATA_F1_NEAR_SOURCE";
    public static final String DATA_F1_EXT_PARAMS = PREFIX + ".DATA_F1_EXT_PARAMS";

    /**
     * 订阅id
     */
    public static final String DATA_SUBSCRIBE_ENCRYPT_ID = PREFIX + ".DATA_SUBSCRIBE_ENCRYPT_ID";

    public static final String DATA_JOB_EXPECT_ID = PREFIX + ".DATA_JOB_EXPECT_ID";

    public static final String DATA_FILTER_EXPECT_ID = PREFIX + ".DATA_FILTER_EXPECT_ID"; // 1106.603 筛选职位期望id

    public static final String DATA_JOB_ADDRESS_ID = PREFIX + ".DATA_JOB_ADDRESS_ID";
    /**
     * 参数KEY，tag
     */
    public static final String DATA_TAG = PREFIX + ".DATA_TAG";
    /**
     * 参数KEY，boolean
     */
    public static final String DATA_BOOLEAN = PREFIX + ".DATA_BOOLEAN";
    public static final String DATA_BOOLEAN2 = PREFIX + ".DATA_BOOLEAN2";
    public static final String DATA_BOOLEAN3 = PREFIX + ".DATA_BOOLEAN3";
    public static final String DATA_BOOLEAN4 = PREFIX + ".DATA_BOOLEAN4";
    public static final String DATA_BOOLEAN5 = PREFIX + ".DATA_BOOLEAN5";
    public static final String DATA_BOOLEAN6 = PREFIX + ".DATA_BOOLEAN6";
    public static final String DATA_BOOLEAN7 = PREFIX + ".DATA_BOOLEAN7";
    public static final String DATA_BOOLEAN8 = PREFIX + ".DATA_BOOLEAN8";
    public static final String DATA_BOOLEAN9 = PREFIX + ".DATA_BOOLEAN9";
    /**
     * 参数KEY，String
     */
    public static final String DATA_STRING = PREFIX + ".DATA_STRING";
    public static final String DATA_STRING2 = PREFIX + ".DATA_STRING2";
    public static final String DATA_STRING3 = PREFIX + ".DATA_STRING3";
    public static final String DATA_STRING4 = PREFIX + ".DATA_STRING4";
    public static final String DATA_STRING5 = PREFIX + ".DATA_STRING5";
    public static final String DATA_STRING6 = PREFIX + ".DATA_STRING6";
    public static final String DATA_STRING7 = PREFIX + ".DATA_STRING7";
    public static final String DATA_STRING8 = PREFIX + ".DATA_STRING8";
    public static final String DATA_STRING9 = PREFIX + ".DATA_STRING9";


    public static final String DATA_H5_URL = PREFIX + ".DATA_JUMP_H5_URL";
    public static final String DATA_JUMP_H5_URL_SAVE_DRAFT = PREFIX + ".DATA_JUMP_H5_URL_SAVE_DRAFT";


    public static final String DATA_SECURITY_ID = PREFIX + ".DATA_SECURITY_ID";
    public static final String DATA_FRIEND_ID = PREFIX + ".FRIEND_ID";
    public static final String DATA_FRIEND_SOURCE = PREFIX + ".FRIEND_SOURCE";
    public static final String DATA_OLD_SECURITY_ID = PREFIX + ".DATA_OLD_SECURITY_ID";
    public static final String DATA_GEEK_ID = PREFIX + ".DATA_GEEK_ID";
    public static final String DATA_FROM = PREFIX + ".DATA_FROM";
    public static final String DATA_TYPE = PREFIX + ".DATA_TYPE";
    public static final String DATA_FOUND_SOURCE = PREFIX + ".DATA_FOUND_SOURCE";
    public static final String DATA_BACK_BY_LEVEL = PREFIX + ".DATA_BACK_BY_LEVEL";// 左上角返回键，是否支持逐级返回
    public static final String SOURCE_FROM_PROTOCOL = PREFIX + ".SOURCE_FROM_PROTOCOL";
    public static final String DATA_FROM_LOGIN = PREFIX + ".DATA_FROM_LOGIN";

    public static final String DATA_SCENE = PREFIX + ".DATA_SCENE";
    public static final String DATA_POSITIONCODE = PREFIX + ".DATA_POSITIONCODE";
    public static final String DATA_JOBTYPE = PREFIX + ".DATA_JOBTYPE";
    public static final String DATA_CITYCODE = PREFIX + ".DATA_CITYCODE";

    public static final String DATA_JOB_INTENT_OPERATE_TYPE = PREFIX + ".DATA_JOB_INTENT_OPERATE_TYPE";

    /*
     * APP应用内支付返回结果
     */
    /******三方支付平台返回的结果code******/
    /*三方支付平台返回的结果Code*/
    public static final String APP_PAY_CODE = PREFIX + ".APP_PAY_CODE";
    /*三方支付平台返回的结果描述*/
    public static final String APP_PAY_ERR_MSG = PREFIX + ".APP_PAY_ERR_MSG";
    public static final String APP_PAY_MEMO = PREFIX + ".APP_PAY_MEMO";
    public static final String APP_PAY_STATE = PREFIX + ".APP_PAY_STATE";

    /******APP内自定义支付平台******/
    public static final String APP_PAY_PLATFORM = PREFIX + ".APP_PAY_PLATFORM";
    /*微信*/
    public static final int APP_PAY_PLATFORM_WX = 0;
    /*支付宝*/
    public static final int APP_PAY_PLATFORM_ZFB = 2;
    /*招行一网通*/
    public static final int APP_PAY_PLATFORM_CMB = 3;
    /*云闪付*/
    public static final int APP_PAY_PLATFORM_UNIFY = 4;

    /******APP内自定义支付结果******/
    public static final String APP_PAY_RESULT = PREFIX + ".APP_PAY_RESULT";
    /*支付成功*/
    public static final int APP_PAY_RESULT_SUCCEED = 0;
    /*签约成功（CMB(招行一网通)）*/
    public static final int APP_PAY_RESULT_SIGNED_CMB_SUCCEED = 1;
    /*支付失败*/
    public static final int APP_PAY_RESULT_FAILED = -1;
    /*用户取消*/
    public static final int APP_PAY_RESULT_USER_CANCEL = -2;
    /*签约失败（CMB(招行一网通)）*/
    public static final int APP_PAY_RESULT_SIGNED_CMB_FAILED = -3;

    /******APP内自定义待支付订单状态******/
    public static final String APP_UNPAID_ORDER_STATUS_NOTIFY = PREFIX + ".APP_UNPAID_ORDER_STATUS_NOTIFY";
    /*生成待支付订单*/
    public static final int APP_UNPAID_ORDER_STATUS_CREATE = 1;
    /*待支付订单支付成功*/
    public static final int APP_UNPAID_ORDER_STATUS_BZB_SUCCESS = 2;
    /*待支付订单页倒计时结束*/
    public static final int APP_UNPAID_ORDER_STATUS_COUNTDOWN_FINISH = 3;
    /*支付接口校验失败 - zpp/app/user/bzbOrder*/
    public static final int APP_UNPAID_ORDER_STATUS_BZB_ORDER_FAIL = 4;

    /**
     * 参数KEY, bool
     */
    public static final String DATA_BOOLEAN_DIRECT_CLOSE_OR_REFRESH = PREFIX + ".DATA_BOOLEAN_DIRECT_CLOSE_OR_REFRESH";
    /**
     * 参数KEY, Int
     */
    public static final String DATA_INT = PREFIX + ".DATA_INT";
    /**
     * 参数KEY, Int
     */
    public static final String DATA_INT_2 = PREFIX + ".DATA_INT_2";

    public static final String DATA_INT_3 = PREFIX + ".DATA_INT_3";
    public static final String DATA_INT_4 = PREFIX + ".DATA_INT_4";
    public static final String DATA_INT_5 = PREFIX + ".DATA_INT_5";
    public static final String DATA_INT_6 = PREFIX + ".DATA_INT_6";
    public static final String DATA_INT_7 = PREFIX + ".DATA_INT_7";

    /**
     * 参数KEY, Long
     */
    public static final String DATA_LONG = PREFIX + ".DATA_LONG";

    public static final String DATA_LONG2 = PREFIX + ".DATA_LONG2";
    public static final String DATA_LONG3 = PREFIX + ".DATA_LONG3";
    public static final String DATA_LONG4 = PREFIX + ".DATA_LONG4";
    public static final String DATA_LONG5 = PREFIX + ".DATA_LONG5";
    public static final String DATA_LONG6 = PREFIX + ".DATA_LONG6";
    /**
     * 参数KEY, Double
     */
    public static final String DATA_DOUBLE = PREFIX + ".DATA_DOUBLE";
    public static final String DATA_DOUBLE2 = PREFIX + ".DATA_DOUBLE2";

    /**
     * 简聊提醒
     */
    public static final String LIGHT_CHAT_GUIDE_TIP = PREFIX + ".LIGHT_CHAT_GUIDE_TIP";

    // ********** ---------- Receiver ---------- ********** //
    /**
     * 登录成功的广播
     */
    public static final String RECEIVER_LOGIN_SUCCESS = PREFIX + ".RECEIVER_LOGIN_SUCCESS";

    /**
     * 聊天中未读条目数量的ACTION
     */
    public static final String RECEIVER_LOGIN_ERROR_ACTION = PREFIX + ".RECEIVER_LOGIN_ERROR_ACTION";
    /**
     * boss职位列表改变的广播ACTION
     */
    public static final String RECEIVER_JOB_LIST_CHANGED_ACTION = PREFIX + ".RECEIVER_JOB_LIST_CHANGED_ACTION";
    public static final String RECEIVER_CONTACT_GROUP_ALL_RECOMMEND_SORT_ACTION = PREFIX + ".CONTACT_GROUP_ALL_RECOMMEND_SORT_ACTION";

    /**
     * boss职位排序
     */
    public static final String RECEIVER_JOB_SORT_ACTION = PREFIX + ".RECEIVER_JOB_SORT_ACTION";
    /**
     * geek求职意向改变的广播ACTION
     */
    public static final String RECEIVER_GEEK_EXPECT_LIST_CHANGED_ACTION = PREFIX + ".RECEIVER_GEEK_EXPECT_LIST_CHANGED_ACTION";

    public static final String RECEIVER_STUDENT_EXPECT_TYPE_CHANGE_ACTION = PREFIX + ".RECEIVER_STUDENT_EXPECT_TYPE_CHANGE_ACTION";


    /**
     * geek求职意向改变的广播ACTION - 蓝领推荐页面使用
     */
    public static final String RECEIVER_GEEK_EXPECT_LIST_CHANGED_ACTION_BLUE = PREFIX + ".RECEIVER_GEEK_EXPECT_LIST_CHANGED_ACTION_BLUE";
    /**
     * F1中的黄条广手播ACTION
     */
    public static final String RECEIVER_FIND_NAVIGATION_TOAST_ACTION = PREFIX + ".RECEIVER_FIND_NAVIGATION_TOAST_ACTION";

    public static final String ACTION_SHOW_SUPPLEMENT_AGREEMENT_DIALOG = PREFIX + ".ACTION_SHOW_SUPPLEMENT_AGREEMENT_DIALOG";
    public static final String ACTION_TECHNOLOGY_SWITCH_BROADCAST = PREFIX + ".ACTION_TECHNOLOGY_SWITCH_BROADCAST";

    public static final String ACTION_SHOW_GREETING = PREFIX + ".ACTION_SHOW_NEW_GREETING";
    /**
     * 联系人消息解密
     */
    public static final String RECEIVER_CONTACT_DECRY_MESSAGE = PREFIX + ".RECEIVER_CONTACT_DECRY_MESSAGE";
    /**
     * 刷新用户中心红点的ACTION
     */
    public static final String RECEIVER_REFRESH_USER_CENTER_ACTION = PREFIX + ".RECEIVER_REFRESH_USER_CENTER_ACTION";
    /**
     * BOSS接收推荐牛人数量的ACTION
     */
    public static final String RECEIVER_RECOMMEND_GEEK_COUNT_ACTION = PREFIX + ".RECEIVER_RECOMMEND_GEEK_COUNT_ACTION";
    /**
     * 聊天页面强制刷新的ACTION
     */
    public static final String CHAT_VIEW_REFRESH_ACTION = PREFIX + ".CHAT_VIEW_REFRESH_ACTION";
    public static final String CHAT_CHANGE_JOB_EXPECTID_ACTION = PREFIX + ".CHAT_CHANGE_JOB_EXPECTID_ACTION";
    /**
     * 刷新当前页面
     */
    public static final String CHAT_REFRESH_CURRENT_PAGE = PREFIX + ".CHAT_REFRESH_CURRENT_PAGE";

    /**
     * 发送文本消息
     */
    public static final String CHAT_SEND_TEXT_MESSAGE = PREFIX + ".CHAT_SEND_TEXT_MESSAGE";
    /**
     * 面试评价弹窗
     */
    public static final String CHAT_VIDEO_COMMON_ACTION = PREFIX + ".CHAT_VIDEO_COMMON_ACTION";
    /**
     * 消息替换action
     */
    public static final String CHAT_VIEW_REPLACE_ACTION = PREFIX + ".CHAT_VIEW_REPLACE_ACTION";

    /**
     * BOSS接收推荐牛人的新数量
     */
    public static final String RECEIVER_RECOMMEND_GEEK_NEW_COUNT_KEY = PREFIX + ".RECEIVER_RECOMMEND_GEEK_NEW_COUNT_KEY";

    /**
     * BOSS接收推荐牛人的所有数量
     */
    public static final String RECEIVER_RECOMMEND_GEEK_ALL_COUNT_KEY = PREFIX + ".RECEIVER_RECOMMEND_GEEK_ALL_COUNT_KEY";

    /**
     * BOSS接收推荐牛人的消息最后时间
     */
    public static final String RECEIVER_RECOMMEND_GEEK_LAST_TIME_KEY = PREFIX + ".RECEIVER_RECOMMEND_GEEK_LAST_TIME_KEY";

    /**
     * 牛人关注公司列表的新职位数量
     */
    public static final String RECEIVER_GEEK_FOLLOW_COMPANY_JOB_COUNT_KEY = PREFIX + ".RECEIVER_GEEK_FOLLOW_COMPANY_JOB_COUNT_KEY";

    /**
     * 信息不完整跳转到f3
     */
    public static final String RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION = PREFIX + ".RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION";
    /**
     * 从聊天页购买VIP4成功
     */
    public static final String RECEIVER_PURCHASE_VIP4_FROM_CHAT_SUCCESS_ACTION = PREFIX + ".RECEIVER_PURCHASE_VIP4_FROM_CHAT_SUCCESS_ACTION";

    /**
     * 购买VIP成功
     */
    public static final String RECEIVER_BUY_VIP_SUCCESS = PREFIX + ".RECEIVER_BUY_VIP_SUCCESS";

    /**
     * Boss查看牛人简历（数据更新）
     */
    public static final String RECEIVER_BOSS_VIEW_GEEK_DATA = PREFIX + ".BOSS_VIEW_GEEK_DATA";

    /**
     * 暗黑模式主题mode
     */
    public static final String INTENT_MODE_NIGHT = PREFIX + ".INTENT_MODE_NIGHT";

    /**
     * 面试间interviewId
     */
    public static final String INTENT_INTERVIEW_ID = PREFIX + ".INTENT_INTERVIEW_ID";

    /**
     * F2接收到不适合牛人
     */
    public static final String RECEIVER_UNFIT_GEEK_ACTION = PREFIX + ".RECEIVER_UNFIT_GEEK_ACTION";

    /**
     * 创建好友回调的ACTION
     */
    public static final String RECEIVER_CREATE_FRIEND_ACTION = PREFIX + ".RECEIVER_CREATE_FRIEND_ACTION";

    /**
     * 创建好友回调的ACTION - 1120.233【商业】vvip-精选版升级 - 移除高搜列表对应牛人卡片
     */
    public static final String RECEIVER_CREATE_FRIEND_ACTION_REMOVE_ADS_LIST_GEEK_CARD = PREFIX + ".RECEIVER_CREATE_FRIEND_ACTION_REMOVE_ADS_LIST_GEEK_CARD";

    /**
     * 弹出独占式广告的ACTION
     */
    public static final String RECEIVER_ADVERT_SHOW_ACTION = PREFIX + ".RECEIVER_ADVERT_SHOW_ACTION";
    public static final String RECEIVER_GEEK_H5_SEND_RESUME_EVENT = PREFIX + ".RECEIVER_GEEK_H5_SEND_RESUME_EVENT";

    /**
     * 进入MainActivity
     * 主要是为了解决 PUSH 进入 App  会重新进入Acitvity
     */
    public static final String RECEIVER_START_MAIN_PAGE = PREFIX + ".RECEIVER_START_MAIN_PAGE";


    /**
     * 新手引导模块Dialog的ACTION
     */
    public static final String RECEIVER_GUIDE_DIALOG_SHOW_ACTION = PREFIX + ".RECEIVER_GUIDE_DIALOG_SHOW_ACTION";

    /**
     * 聊天页面的Dialog的ACTION
     */
    public static final String RECEIVER_CHAT_PAGE_DIALOG_ACTION = PREFIX + ".RECEIVER_CHAT_PAGE_DIALOG_ACTION";
    /**
     * 取消技术阻断开聊
     */
    public static final String RECEIVER_CANCEL_CHAT_SKILL_BLOCK = PREFIX + ".RECEIVER_CANCEL_CHAT_SKILL_BLOCK";
    /**
     * 检测技术牛人是否阻断
     */
    public static final String RECEIVER_CHECK_CHAT_SKILL_BLOCK = PREFIX + ".RECEIVER_CHECK_CHAT_SKILL_BLOCK";

    /**
     * 消息撤回广播
     */
    public static final String RECEIVER_MESSAGE_REVOCATION = PREFIX + ".RECEIVER_MESSAGE_REVOCATION";


    public static final String RECEIVER_REFRESH_TOP_EXCHANGE = PREFIX + ".RECEIVER_REFRESH_TOP_EXCHANGE";
    public static final String RECEIVER_CHAT_RECOMMEND_JOB_LIST = PREFIX + ".RECEIVER_CHAT_RECOMMEND_JOB_LIST";
    public static final String RECEIVER_CHAT_VERBAL_INTERVIEW = PREFIX + ".RECEIVER_CHAT_VERBAL_INTERVIEW";

    public static final String RECEIVER_ADD_LOCAL_PHONE_CARD = PREFIX + ".RECEIVER_ADD_LOCAL_PHONE_CARD";

    public static final String RECEIVER_DIALOG_BUTTON_CLICK = PREFIX + ".RECEIVER_DIALOG_BUTTON_CLICK";

    public static final String RECEIVER_SENSITIVE_WORD = PREFIX + ".RECEIVER_SENSITIVE_WORD";

    /**
     * 开放职位广播
     */
    public static final String RECEIVER_JOB_UPDATE = PREFIX + ".RECEIVER_JOB_UPDATE";
    public static final String RECEIVER_JOB_DELETE = PREFIX + ".RECEIVER_JOB_DELETE";
    public static final String RECEIVER_OPEN_JOB = PREFIX + ".RECEIVER_OPEN_JOB";
    public static final String RECEIVER_JOB_SAVE_DRAFT = PREFIX + ".RECEIVER_JOB_SAVE_DRAFT";
    public static final String RECEIVER_JOB_DESC_FILL_POSITION_NAME = PREFIX + ".RECEIVER_JOB_DESC_FILL_POSITION_NAME";

    public static final String RECEIVER_CONTACT_SUGGEST = PREFIX + ".RECEIVER_CONTACT_SUGGEST";

    public static final String RECEIVER_REFRESH_CHAT_TOP_STATUSs = PREFIX + ".RECEIVER_REFRESH_CHAT_TOP_STATUSs";

    public static final String RECEIVER_REFRESH_SHIELD_COMPANY_CHANGE = PREFIX + ".RECEIVER_REFRESH_SHIELD_COMPANY_CHANGE";


    /**
     * 刷新F3数据
     */
    public static final String REFRESH_F3_DATA_ACTION = PREFIX + ".REFRESH_F3_DATA_ACTION";
    /**
     * 刷新F3小秘书数据
     */
    public static final String REFRESH_F3_SECRETARY_ACTION = PREFIX + ".REFRESH_F3_SECRETARY_ACTION";
    /**
     * 刷新F3本地数据
     */
    public static final String REFRESH_F3_LOCAL_DATA_ACTION = PREFIX + ".REFRESH_F3_LOCAL_DATA_ACTION";

    public static final String FINISH_SWITCH_MIDDLE_PAGE = PREFIX + ".FINISH_SWITCH_MIDDLE_PAGE";

    /**
     * 1307.80【C】缓解部分招聘者超期会话资源占用问题 批量弹窗提示
     */
    public static final String RECEIVER_CHAT_PAGE_FRIEND_CLEAN = PREFIX + ".RECEIVER_CHAT_PAGE_FRIEND_CLEAN";
    /**
     * 刷新 BOSS'我看过'
     */
    public static final String REFRESH_F2_INTERACT_TAB_CHANGE_ACTION = PREFIX + ".REFRESH_F2_INTERACT_TAB_CHANGE_ACTION";


    public static final String REFRESH_SHOW_POP_ACTION = PREFIX + ".REFRESH_SHOW_POP_ACTION";

    /**
     * 全量刷新f3动态配置
     */
    public static final String REFRESH_F3_ITEM_ACTION = PREFIX + ".REFRESH_F3_ITEM_ACTION";

    /**
     * 直聘协议中更新版本的广播ACTION
     */
    public static final String RECEIVER_ZHIPIN_SECRETORY_UPDATE_VERSION_ACTION = PREFIX + ".RECEIVER_ZHIPIN_SECRETORY_UPDATE_VERSION_ACTION";

    /**
     * 微信支付结果广播ACTION
     */
    public static final String RECEIVER_WX_PAY_RESULT_ACTION = PREFIX + ".RECEIVER_WX_PAY_RESULT_ACTION";


    public static final String RECEIVER_ALI_PAY_AUTH_RESULT_ACTION = PREFIX + ".RECEIVER_ALI_PAY_AUTH_RESULT_ACTION";

    /**
     * 支付宝-独立签约（免密支付）
     */
    public static final String RECEIVER_ALI_INDEPENDENT_CONTRACT_RESULT_ACTION = PREFIX + ".RECEIVER_ALI_INDEPENDENT_CONTRACT_RESULT_ACTION";

    /**
     * 刷新零钱页面的余额
     */
    public static final String RECEIVER_REFRESH_WALLET_AMOUNT = PREFIX + ".RECEIVER_REFRESH_WALLET_AMOUNT";

    /**
     * APP第一次打开APP，同时未登录的偏好KEY
     */
    public static final String APP_FIRST_OPEN_TIME_NONE_LOGIN_KEY = PREFIX + ".APP_FIRST_OPEN_TIME_NONE_LOGIN_KEY";

    /**
     * 微信登录授权结果广播ACTION
     */
    public static final String RECEIVER_WX_LOGIN_AUTH_RESULT_ACTION = PREFIX + ".RECEIVER_WX_LOGIN_AUTH_RESULT_ACTION";

    /**
     * 微信登录授权（6.09）
     */
    public static final String RECEIVER_WX_LOGIN_AUTH_CODE_ACTION = PREFIX + ".RECEIVER_WX_LOGIN_AUTH_CODE_ACTION";

    public static final String RECEIVER_AUTO_LOGIN_ACTION = PREFIX + ".RECEIVER_AUTO_LOGIN_ACTION";

    /**
     * 刷新『有了』tab 红点、气泡
     */
    public static final String RECEIVER_REFRESH_YOU_LE_ACTION = PREFIX + ".RECEIVER_REFRESH_YOU_LE_ACTION";
    /**
     * 刷新『有了』当前是视频tab
     */
    public static final String RECEIVER_YOU_LE_SHOW_VIDEO_ACTION = PREFIX + ".RECEIVER_YOU_LE_SHOW_VIDEO_ACTION";

    /**
     * 刷新职位页面的广播
     */
    public static final String RECEIVER_REFRESH_JOB_DETAIL_ACTION = PREFIX + ".RECEIVER_REFRESH_JOB_DETAIL_ACTION";
    /**
     * 接收执行直聘协议的广播
     */
    public static final String RECEIVER_ZP_PROTOCOL_ACTION = PREFIX + ".RECEIVER_ZP_PROTOCOL_ACTION";
    /**
     * 通知首页刷新弹框数据
     */
    public static final String RECEIVER_NOTIFY_UPDATE_DIALOG_DATA_ACTION = PREFIX + ".RECEIVER_NOTIFY_UPDATE_DIALOG_DATA_ACTION";
    /**
     * 标记/解除好友不合适的通知
     */
    public static final String RECEIVER_FRIEND_REJECT_ACTION = PREFIX + ".RECEIVER_FRIEND_REJECT_ACTION";

    /**
     * 强制刷新简历详情页
     */
    public static final String RECEIVER_FORCE_REFRESH_DETAIL_ACTION = PREFIX + ".RECEIVER_FORCE_REFRESH_DETAIL_ACTION";

    /**
     * 详情翻页的广播
     */
    public static final String RECEIVER_DETAIL_NEXT_PAGE_REQUEST = PREFIX + ".RECEIVER_DETAIL_NEXT_PAGE_REQUEST";
    /**
     * 接收app更新事件
     */
    public static final String RECEIVER_APP_UPGRADE_ACTION = PREFIX + ".RECEIVER_APP_UPGRADE_ACTION";

    /**
     * 接收人机验证广播
     */
    public static final String RECEIVER_MACHINE_VERIFY_ACTION = PREFIX + ".RECEIVER_MACHINE_VERIFY_ACTION";

    /**
     * 面试状态改变
     */
    public static final String RECEIVER_INTERVIEW_STATUS_CHANGED = PREFIX + ".RECEIVER_INTERVIEW_STATUS_CHANGED";

    /**
     * 面试间【视频面试间、语音面试间】
     */
    public static final String RECEIVER_RECEIVER_INTERVIEW_ROOM_EVALUATE = PREFIX + ".RECEIVER_INTERVIEW_ROOM_EVALUATE";

    /**
     * 刷新用户中心红点的ACTION
     */
    public static final String RECEIVER_COLOSE_SAFE_WINDOWS = PREFIX + ".RECEIVER_COLOSE_SAFE_WINDOWS";

    /**
     * 刷新附件简历页面
     */
    public static final String RECEIVER_REFRESH_ATTACHMENT_RESUME_LAYOUT = PREFIX + ".RECEIVER_REFRESH_ATTACHMENT_RESUME_LAYOUT";

    /**
     * 刷新群聊信息
     */
    public static final String RECEIVER_REFRESH_GROUP_INFO = PREFIX + ".RECEIVER_REFRESH_GROUP_INFO";

    public static final String RECEIVER_CHANGE_LOCATION = PREFIX + ".RECEIVER_CHANGE_LOCATION";

    public static final String RECEIVER_REFRESH_NEW_JOB_INTENT = PREFIX + ".RECEIVER_REFRESH_NEW_JOB_INTENT";

    /**
     * 生成订单信息
     */
    public static final String RECEIVER_GENERATED_BZB_ORDER_INFO = PREFIX + ".RECEIVER_GENERATED_BZB_ORDER_INFO";

    /**
     * 清除已生成的订单信息
     */
    public static final String RECEIVER_CLEAN_BZB_ORDER_INFO = PREFIX + ".RECEIVER_CLEAN_BZB_ORDER_INFO";

    public static final String RECEIVER_BOSS_VIP_STATE_TIP = PREFIX + ".RECEIVER_BOSS_VIP_STATE_TIP";

    /**
     * 关闭职位延长卡片广播
     */
    public static final String RECEIVER_CLOSE_SCENE_CARD = PREFIX + ".RECEIVER_CLOSE_SCENE_CARD";

    /**
     * 新手引导点击立即体验时，导致f1页面刷新
     */
    public static final String RECEIVER_NOVICE_GUIDE_LEAD_TO_LIST_REFRESH = PREFIX + ".RECEIVER_NOVICE_GUIDE_LEAD_TO_LIST_REFRESH";

    /**
     * 简历是否查看过
     */
    public static final String RECEIVER_RESUME_ALREADY_CLICKED = PREFIX + ".RECEIVER_RESUME_ALREADY_CLICKED";

    public static final String RECEIVER_COMPLETION_DONE = PREFIX + ".RECEIVER_COMPLETION_DONE";
    /**
     * 短信通知购买成功
     */
    public static final String RECEIVER_VIRTUAL_CALL_ITEM_PURCHASE = PREFIX + ".RECEIVER_VIRTUAL_CALL_ITEM_PURCHASE";
    /**
     * 线下道具消耗购买成功
     */
    public static final String RECEIVER_REFRESH_F1_LIST_CARD = PREFIX + ".RECEIVER_REFRESH_F1_LIST_CARD";

    /**
     * 本地数据库中最大的消息ID
     */
    public static final String MESSAGE_MAX_ID = PREFIX + ".MESSAGE_MAX_ID";

    public static final String GID = PREFIX + ".GID";
    public static final String AUTO_JOIN = PREFIX + ".AUTO_JOIN";
    /**
     * 本地数据库中最大的消息ID
     */
    public static final String MESSAGE_MAX_GROUP_ID = PREFIX + ".MESSAGE_MAX_GROUP_ID";
    /**
     * 校友圈二人群刷新
     */
    public static final String NOTICE_ALUMNI_REFRESH = PREFIX + ".NOTICE_ALUMNI_REFRESH";
    /**
     * 校友圈大群刷新
     */
    public static final String NOTICE_ALUMNI_GROUP_REFRESH = PREFIX + ".NOTICE_ALUMNI_GROUP_REFRESH";

    /**
     * 切换学生党和社会人
     */
    public static final String RECEIVER_STUDENT_ROLE_SWITCH = PREFIX + ".RECEIVER_STUDENT_ROLE_SWITCH";
    public static final String RECEIVER_STUDENT_SUB_TAB = PREFIX + ".RECEIVER_STUDENT_SUB_TAB";
    public static final String RECEIVER_FIND_SUB_TAB = PREFIX + ".RECEIVER_FIND_SUB_TAB";

    public static final String RECEIVER_FIND_FILTER_NEW = PREFIX + ".RECEIVER_FIND_FILTER_NEW";
    public static final String RECEIVER_REFRESH_F1 = PREFIX + ".RECEIVER_REFRESH_F1";
    public static final String RECEIVER_GEEK_F1_CARD_VIEWED = PREFIX + ".receiver_geek_f1_card_viewed"; // 职位已经在详情页面被看过

    /**
     * 搜畅使用成功发送广播
     */
    public static final String RECEIVER_CHAT_PRIVILEGE_USE_SUCCESS = PREFIX + ".RECEIVER_CHAT_PRIVILEGE_USE_SUCCESS";
    /**
     * 智慧石聊天刷新  已解决  未解决  消息
     */
    public static final String RECEIVER_CUSTOMER_CHAT_DIALOG = PREFIX + ".RECEIVER_CUSTOMER_CHAT_DIALOG";
    /**
     * 809.28 交换电话引导
     */
    public static final String RECEIVER_PHONE_EXCHANGE_DIALOG_GUIDE = PREFIX + ".PHONE_EXCHANGE_DIALOG_GUIDE";

    public static final String RECEIVER_PURCHASE_SUCCESS_FROM_H5 = PREFIX + ".RECEIVER_PURCHASE_SUCCESS_FROM_H5";

    /**
     * 815 是否展示订阅的tip
     */
    public static final String RECEIVER_ADVANCED_SEARCH_TAB_SUBSCRIBED_TIP = PREFIX + ".RECEIVER_ADVANCED_SEARCH_TAB_SUBSCRIBED_TIP";
    public static final String RECEIVER_ADVANCED_SEARCH_TAB_FUNCTIONAL_TIP = PREFIX + ".RECEIVER_ADVANCED_SEARCH_TAB_FUNCTIONAL_TIP_";
    public static final String RECEIVER_PERSONALITY_FUNCTION_SWITCH_CHANGE = "personality_switch_change";
    public static final String RECEIVER_PERSONALITY_CONTENT_SWITCH_CHANGE = "personality_content_switch_change";

    /**
     * 818 F1为你推荐
     */
    public static final String RECEIVER_RECOMMEND_FOR_YOU_GEEK_CARD = PREFIX + ".RECEIVER_RECOMMEND_FOR_YOU_GEEK_CARD";

    /**
     * 从F2进入开聊后获取牛人列表
     */
    public static final String RECEIVER_CHAT_FROM_F2_INTERACT_GET_GEEK_LIST = PREFIX + ".RECEIVER_CHAT_FROM_F2_INTERACT_GET_GEEK_LIST";

    /**
     * 详情页不合适操作的通知
     */
    public static final String RECEIVER_FEEDBACK_ACTION_IN_CV_DETAIL = PREFIX + ".RECEIVER_FEEDBACK_ACTION_IN_CV_DETAIL";

    /**
     * 1106.255 简历页收藏操作通知
     */
    public static final String RECEIVER_INTEREST_ACTION_IN_CV_DETAIL = PREFIX + ".RECEIVER_INTEREST_ACTION_IN_CV_DETAIL";

    /**
     * 1223.501【意】APP推荐牛人cv页增加意向沟通消耗入口：新增意向沟通道具购买成功监听广播 Key 值
     */
    public static final String RECEIVER_ON_INTENTION_CONNECT_PAY_SUCCESS = PREFIX + ".RECEIVER_ON_INTENTION_CONNECT_PAY_SUCCESS";

    /**
     * 1125.169
     */
    public static final String RECEIVER_REFRESH_LIST_FROM_JOB_ASSISTANT = PREFIX + ".RECEIVER_REFRESH_LIST_FROM_JOB_ASSISTANT";

    /**
     * 1207.601
     */
    public static final String RECEIVER_REFRESH_GEEK_F1_LIST_FROM_JOB_ASSISTANT = PREFIX + ".RECEIVER_REFRESH_GEEK_F1_LIST_FROM_JOB_ASSISTANT";
    /**
     * 1219.500【H】意向沟通-BOSS端-F2新增入口：新增意向沟通购买成功广播
     */
    public static final String RECEIVER_ON_CONTACT_INTENTION_PAY_RESULT = PREFIX + ".RECEIVER_ON_CONTACT_INTENTION_PAY_RESULT";
    /**
     * 1220.501【意向沟通】意向沟通发现&我的订单牛人卡片样式优化：新增意向沟通订单卡片刷新广播
     */
    public static final String RECEIVER_GET_INTENTION_BENEFITS_SUCCESS = PREFIX + ".RECEIVER_GET_INTENTION_BENEFITS_SUCCESS";
    /**
     * 1301.502【意】有权益用户的发现列表样式改动：新增意向沟通发起成功广播，用于刷新牛人卡片上的沟通按钮展示
     */
    public static final String RECEIVER_ON_CONTACT_INTENTION_SEND_SUCCESS = PREFIX + ".RECEIVER_ON_CONTACT_INTENTION_SEND_SUCCESS";

    /**
     * 1305.169
     */
    public static final String RECEIVER_FEEDBACK_ACTION_IN_AI_ASSISTANT = PREFIX + ".RECEIVER_FEEDBACK_ACTION_IN_AI_ASSISTANT";

    /**
     * 819 获取微信授权
     */
    public static final String ACTION_GET_WX_CODE = PREFIX + ".ACTION_GET_WX_CODE";

    /**
     * 1016.706【安全-非版本加急】地址认证接入VR H5页面
     */
    public static final String ACTION_VR_TAKE_SUCCESS_CODE_H5 = PREFIX + ".ACTION_VR_TAKE_SUCCESS_CODE";

    /**
     * 1102.702  地址认证接入VR 原生页面
     */
    public static final String ACTION_VR_TAKE_SUCCESS_CODE_NATIVE = PREFIX + ".ACTION_VR_TAKE_SUCCESS_CODE_NATIVE";

    /**
     * 暗黑模式切换
     */
    public static final String ACTION_CHANGE_DARK_THEME = PREFIX + ".ACTION_CHANGE_DARK_THEME";

    /**
     * 1109.51 群聊 职位置顶
     */
    public static final String ACTION_CHAT_GROUP_TOP_JOB = PREFIX + ".ACTION_CHAT_GROUP_TOP_JOB";

    /**
     * 1207.910【内容】牛人给Boss打电话
     */
    public static final String ACTION_CHAT_CALL_TEL_SUCCESS = PREFIX + ".ACTION_CHAT_CALL_TEL_SUCCESS";

    /**
     * 917 认证成功统一消息回调
     */
    public static final String COMMON_AUTH_SUCCESS_CALLBACK = PREFIX + ".COMMON_AUTH_SUCCESS_CALLBACK";

    public static final String ACTION_CHAT_CONTACT_GROUP_INDEX = PREFIX + ".ACTION_CHAT_CONTACT_GROUP_INDEX";

    /**
     * 网络请求数据列表时，每页请求的Item数量
     */
    public static final String PAGE_SIZE = "15";

    /**
     * 【偏好】将是否有版本更新的信息写入偏好
     * 0 - APP暂时没有新版本更新 | 1 - APP有新版本，不强制更新 | 2 - APP有新版本，强制更新
     */
    public static final String APP_UPGRADE_KEY = "APP_UPGRADE_KEY";
    /**
     * APP升级数据的KEY
     */
    public static final String SP_APP_UPGRADE_VERSION_BEAN_KEY = PREFIX + ".SP_APP_UPGRADE_VERSION_BEAN_KEY2";

    public static final String NOTIFY_STRING_URL = PREFIX + ".NOTIFY_STRING_URL";
    public static final String MAIN_MSG_ID_KEY = "msgId";
    public static final String MAIN_FROM_ID_KEY = "fromId";
    public static final String MAIN_MSG_PUSH_MOTHED = "pushMothed";
    public static final String MAIN_MSG_PUSH_SYSTEM = "pushSystem";
    public static final String MAIN_MSG_PUSH_BIZID = "pushBizId";
    public static final String MAIN_MSG_PUSH_BIZTYPE = "pushBizType";

    public static final String MAIN_MSG_SERVER_PUSH_TYPE = "pushType";

    /************
     * 扫描二维码的扫描类型 以及传递的参数KEY
     ****************/
    public static final String QR_EDIT_GEEK_RESUME = "0"; // 编辑附件简历
    public static final String QR_MANAGE_BOSS_POSITION = "1"; // 管理职位
    public static final String QR_EDIT_BOSS_POSITION = "2"; // 编辑职位
    public static final String QR_UPLOAD_GEEK_RESUME = "3"; // 上传附件简历
    public static final String QR_OTHER_CLIENT_LOGIN = "4"; // 其它客户端扫码登录类型//通知左上角
    public static final String QR_COMPUTER_EDIT_COMPANY = "5"; // 在电脑上装修公司
    public static final String QR_F3 = "8"; // F3
    public static final String QR_UPLOAD_GEEK_WORKS = "10"; // 上传作品集
    public static final String QR_CV_RESUME = "13"; // 直聘简历进入扫码页
    public static final String QR_NEW_RESUME = "16"; // 新建简历
    public static final String QR_ATS_RESUME = "22"; // 校招ATS
    public static final String QR_JOIN_GROUP = "23"; // 1110.999 扫码加入记工记账项目组

    public static final String QR_ID = PREFIX + ".QR_ID"; // 二维码id
    public static final String SECOND_QR_ID = PREFIX + ".SECOND_QR_ID"; // 二维码id
    public static final String ACTION_ID = PREFIX + ".ACTION_ID"; // 动作id
    public static final String EDIT_TYPE = PREFIX + ".EDIT_TYPE"; // 编辑类型
    public static final String SCAN_EXTRA = PREFIX + ".SCAN_EXTRA"; // 扫码
    public static final String QR_SCAN_RESULT = PREFIX + ".QR_SCAN_RESULT";//扫描结果成功还是失败
    public static final String QR_SCAN_TIME_RESULT = PREFIX + ".QR_SCAN_TIME_RESULT";//
    public static final String QR_SCAN_COUNT_RESULT = PREFIX + ".QR_SCAN_COUNT_RESULT";//
    public static final String QR_SCAN_RESULT_FAIL_REASON = PREFIX + ".QR_SCAN_RESULT_FAIL_REASON";//扫描失败原因

    /**
     * 4.1公司编辑页面是否编辑完毕
     */
    public static final String EDIT_COMPANY_DETAIL_COMPLETE_KEY = PREFIX + ".EDIT_COMPANY_DETAIL_COMPLETE_KEY";

    /**
     * 4.1密码输入错误冻结提示窗口
     */

    public static final String FREEZE_NOTIFY_DIALOG = PREFIX + ".FREEZE_NOTIFY_DIALOG";
    /**
     * 4.1 startActivityForResult微信认证
     */
    public static final int WEIXIN_AUTHORIZATION_RESULT_CODE = 100;
    /**
     * 显示品牌样式
     */
    public static final String SP_SHOW_BRAND_SAMPLE = PREFIX + ".SP_SHOW_BRAND_SAMPLE";

    /**
     * 是否显示首次发布职位弹框
     */
    public static final String SP_SHOW_FIRST_TIME_JOB_POST_DIALOG = PREFIX + ".SP_SHOW_FIRST_TIME_JOB_POST_DIALOG";

    public static final String SP_CLICK_COMMON_LANGUAGE = PREFIX + ".SP_CLICK_COMMON_LANGUAGE";
    /**
     * 牛人期望职位数量
     */
    public static final int GEEK_EXPECT_POSITION_COUNT = 3;

    public static final String RECEIVER_REJECT_INTENT_CLOSE = PREFIX + ".RECEIVER_REJECT_INTENT_CLOSE";


    public static final String RECEIVER_REFRESH_INTERACT = PREFIX + "RECEIVER_REFRESH_INTERACT";
    public static final String RECEIVER_REFRESH_FILTER = PREFIX + "RECEIVER_REFRESH_FILTER";
    public static final String RECEIVER_F2_EXPAND_ADD_NEW_JOB = PREFIX + "RECEIVER_F2_EXPAND_ADD_NEW_JOB";
    public static final String RECEIVER_F1_FILTER_SELECT_RESULT_CODE = PREFIX + ".GRECEIVER_F1_FILTER_SELECT_RESULT_CODE";
    public static final String RECEIVER_F2_FILTER_SELECT_RESULT_CODE = PREFIX + ".RECEIVER_F2_FILTER_SELECT_RESULT_CODE";
    public static final String RECEIVER_F2_JD_IMPROPER = PREFIX + ".RECEIVER_F2_JD_IMPROPER";
    /**
     * 牛人隐藏简历或者开放简历
     */
    public static final String RECEIVER_GEEK_RESUME_HIDE_ACTION = PREFIX + ".RECEIVER_GEEK_RESUME_HIDE_ACTION";
    public static final String RECEIVER_GEEK_CHANGE_JOB_INTENT = PREFIX + ".RECEIVER_GEEK_CHANGE_JOB_INTENT";
    public static final String RECEIVER_GEEK_HOME_ADDRESS_CHANGED_TO_F1 = PREFIX + ".RECEIVER_GEEK_HOME_ADDRESS_CHANGED_TO_F1";

    /**
     * 学生端同步移除小黄条
     */
    public static final String RECEIVER_STUDENT_REMOVE_AND_REFRESH_TIP = PREFIX + ".RECEIVER_STUDENT_REMOVE_AND_REFRESH_TIP";

    public static final String RECEIVER_GEEK_CHANGE_BLUE_COLLAR_POI_LOCATION = PREFIX + ".RECEIVER_GEEK_CHANGE_BLUE_COLLAR_POI_LOCATION";

    public static final String RECEIVER_CLOSE_GEEK_SCENE_CARD = PREFIX + ".RECEIVER_CLOSE_GEEK_SCENE_CARD";

    /**
     *
     **/

    public static final String RECEIVER_UPDATE_LIST_WORDS = PREFIX + ".RECEIVER_UPDATE_LIST_WORDS";
    public static final String RECEIVER_JOB_DETAIL_MULTI_ADDRESS_TOCHAT = PREFIX + ".RECEIVER_JOB_DETAIL_MULTI_ADDRESS_TOCHAT";

    /**
     * 公司佐证信息上传成功
     */
    public static final String RECEIVER_COM_DEMONSTRATION_UPLOAD_SUCCESS = PREFIX + ".RECEIVER_COM_DEMONSTRATION_UPLOAD_SUCCESS";

    /**
     * 用户人脸设备删除成功
     */
    public static final String RECEIVER_USER_DEVICE_DELETE_SUCCESS = PREFIX + ".RECEIVER_USER_DEVICE_DELETE_SUCCESS";

    public static final String RECEIVER_REFRESH_F1_WITH_FILTER_PARAM_FROM_H5 = PREFIX + ".RECEIVER_REFRESH_F1_WITH_FILTER_PARAM_FROM_H5";

    /**
     * 搜畅卡购买成功
     */
    public static final String RECEIVER_SEARCH_CARD_BZB_SUCCESS = PREFIX + ".RECEIVER_SEARCH_CARD_BZB_SUCCESS";

    /**
     * 1103.211 关闭待开放的职位引导卡片
     */
    public static final String RECEIVER_CLOSE_PRE_JOB_OP_CARD = PREFIX + ".RECEIVER_CLOSE_PRE_JOB_OP_CARD";
    /**
     * 公司佐证信息上传成功
     */
    public static final String RECEIVER_BZB_IDENTIFICATION_AUTH_SUCCESS = PREFIX + ".RECEIVER_BZB_IDENTIFICATION_AUTH_SUCCESS";

    /**
     * 公司认证材料上传成功
     */
    public static final String RECEIVER_COM_AUTH_UPLOAD_SUCCESS = PREFIX + ".RECEIVER_COM_AUTH_UPLOAD_SUCCESS";


    public static final String RECEIVER_BOSS_VIP_ASSISTANT_DIALOG_CLOSE = PREFIX + ".RECEIVER_BOSS_VIP_ASSISTANT_DIALOG_CLOSE";
    public static final String RECEIVER_FEEDBACK_ACTION_CLOSE = PREFIX + ".RECEIVER_FEEDBACK_ACTION_CLOSE";

    /**
     * boss搜索牛人/牛人搜索 搜索词来源前缀
     */
    public static final String PREFIX_MANUAL = "q";//手动输入
    public static final String PREFIX_HISTORY = "l";//历史
    public static final String PREFIX_SUGGEST = "x";//输入时，匹配到的关键词
    public static final String PREFIX_HOT = "h";//热门
    public static final String PREFIX_FAVOR = "c";//场景关键词
    public static final String PREFIX_TYPE = "t"; // 关键词商用类型
    public static final String PREFIX_RECOMMEND_FOR_ME = "w"; // "为我推荐"
    public static final String PREFIX_POSITION_STATEMENT = "p"; // 从"全部岗位类型"选择了一个岗位词，进行搜索时
    public static final String PREFIX_SEARCH_SUBSCRIBE = "d"; // 点击「搜索订阅」的label进行搜索
    public static final String PREFIX_SEARCH_RELATED = "r"; // 点击「相关搜索」的label进行搜索
    public static final String PREFIX_RECOMMEND_REC = "rec"; // get 推荐词（「有了」顶部搜索框点击进入搜索页会带入搜索推荐词）（为空时）
    public static final String PREFIX_HOT_RECOMMEND_CARD = "card";//(1007新增）card-搜索词来源为新增推荐卡片
    public static final String PREFIX_VOICE_INPUT = "v";//(1012新增）搜索词来源为语音输入
    public static final String PREFIX_PREDICT = "predict";//(1012新增）搜索词来源为语音输入
    public static final String PREFIX_PRESET = "preset";//搜索来源为预置词

    /**
     * 职位名称中英文最大长度
     */
    public static final int JOB_INPUT_MAX_LENGTH = 20;

    /**
     * geek是否第一次进入geek主页
     */
    public static final String GEEK_HOME_PAGE_INIT = "GEEK_HOME_PAGE_INIT";
    /**
     * 主页是否要显示引导编辑页
     */
    public static final String HOME_PAGE_INIT_CARD = "HOME_PAGE_INIT_CARD";

    /**
     * geek是否展示应届生引导添加求职意向
     */
    public static final String GEEK_SUGGEST_EXPECT_POSITION = "GEEK_SUGGEST_EXPECT_POSITION";

    public static final String HISTORY_SEARCH_WORK_LOCATION = "HISTORY_SEARCH_WORK_LOCATION";

    public static final String GET_HISTORY_SEARCH_WORK_LOCATION = "GET_HISTORY_SEARCH_WORK_LOCATION";


    public static final String FIRST_COOR_GUIDE = "FIRST_COOR_GUIDE";

    public static final String FIRST_CHAT_COOR_GUIDE = "FIRST_CHAT_COOR_GUIDE";

    public static final String RESUME_REFRESH_CLICK_CLOSED = "RESUME_REFRESH_CLICK_CLOSED";

    public static final String RESUME_REFRESH_SEE_TIME = "RESUME_REFRESH_SEE_TIME";

    public static final String KEY_BOSS_FILTER_RED_DOT = "key_boss_filter_red_dot";

    public static final String GEEK_STUDY_ABROAD_TIP_SHOWED = "geek_study_abroad_tip";

    /**
     * 单点登录被踢
     */
    public static final String ACTION_LOGIN_KICK_OUT = PREFIX + ".ACTION_LOGIN_KICK_OUT";

    /**
     * 是否进入安全框架
     */
    public static final String ACTION_IN_SECURITY_FRAMEWORK = PREFIX + ".ACTION_IN_SECURITY_FRAMEWORK";

    /**
     * 观看"GET"的单个视频广播
     */
    public static final String ACTION_WATCH_GET_SINGLE_VIDEO = PREFIX + ".ACTION_WATCH_GET_SINGLE_VIDEO";


    public static final String SCENE_CARD_JOB_ID = PREFIX + ".SCENE_CARD_JOB_ID";
    public static final String SCENE_CARD_SOURCE = PREFIX + ".SCENE_CARD_SOURCE";
    public static final String SCENE_CARD_TYPE = PREFIX + ".SCENE_CARD_TYPE";
    public static final String SCENE_CARD_BIZ_PARAMS = PREFIX + ".SCENE_CARD_BIZ_PARAMS";

    public static final String FIRST_POSITION_ITEM = PREFIX + ".FIRST_POSITION_ITEM";
    public static final String SECOND_POSITION_ITEM = PREFIX + ".SECOND_POSITION_ITEM";
    public static final String THIRD_POSITION_ITEM = PREFIX + ".THIRD_POSITION_ITEM";


    public static final String HUNTER_ROLE = PREFIX + ".hunter_role";

    public static final String HUNTER_SWITCH = PREFIX + ".hunter_switch";

    public static final String DATA_BOOLEAN_JUNMP_COMPLETE_PAGE = PREFIX + ".DATA_BOOLEAN_JUNMP_COMPLETE_PAGE";
    /**
     * 工作体验点赞广播
     */
    public static final String WORK_EXP_LIKE_ACTION = "WORK_EXP_LIKE_ACTION";
    public static final String WORK_EXP_LIKE_ACTION2 = "WORK_EXP_LIKE_ACTION2";
    public static final String WORK_EXP_LIKE_ACTION_ISLIKE_ = "WORK_EXP_LIKE_ACTION_ISLIKE_";
    public static final String WORK_EXP_LIKE_ACTION_SID = "WORK_EXP_LIKE_ACTION_SID";

    public static final String SP_CLICKED_CONFIRM = "clicked_confirm";


    /**
     * 公司主页、重新加载公司数据
     */
    public static final String ACTION_LOAD_COMPANY_INFO_NEW = "ACTION_LOAD_COMPANY_INFO_NEW";

    /**
     * 公司主页动态在动态列表中点赞回显
     */
    public static final String COMPANY_DYNAMIC_LIST_LIKE_ACTION = "COMPANY_DYNAMIC_LIST_LIKE_ACTION";

    /**
     * 公司主页动态在动态详情中点赞回显
     */
    public static final String COMPANY_DYNAMIC_DETAIL_LIKE_ACTION = "COMPANY_DYNAMIC_DETAIL_LIKE_ACTION";

    /**
     * 公司主页动态删除回显
     */
    public static final String COMPANY_DYNAMIC_DELETE_ACTION = "COMPANY_DYNAMIC_DELETE_ACTION";

    /**
     * 公司主页问答删除回显
     */
    public static final String COMPANY_QA_ANSWER_DELETE_ACTION = "COMPANY_QA_ANSWER_DELETE_ACTION";

    /**
     * 公司主页动态删除回显
     */
    public static final String BRAND_APP_LIST_REFRESH_ACTION = PREFIX + "BRAND_APP_LIST_REFRESH_ACTION";

    /**
     * 公司主页动态删除回显(灰度页面)
     */
    public static final String BRAND_APP_LIST_REFRESH_ACTION_2 = PREFIX + "BRAND_APP_LIST_REFRESH_ACTION_2";

    /**
     * App内通知弹窗
     */
    public static final long APP_NOTICE_TIME = 5000;

    /**
     * App内场景通知弹窗 （917.241 【商业】场景化头部PUSH时间延长普及）
     */
    public static final long APP_SCENE_NOTICE_TIME = APP_NOTICE_TIME;

    public static final String RECEIVER_APP_EXIT = PREFIX + ".RECEIVER_APP_EXIT";

    /**
     * 地图当前定位模式
     */
    public static final String MAP_CURRENT_LOCATION_MODE = "MAP_CURRENT_LOCATION_MODE";

    public static final String GET_GUIDE_CLOSE_SELF = "GET_GUIDE_CLOSE_SELF";

    public static final String ACTION_PHONE_STATE = "android.intent.action.PHONE_STATE";

    public static final String VOLUME_CHANGE_ACTION = "android.media.VOLUME_CHANGED_ACTION";

    public static final String EXTRA_VOLUME_STREAM_TYPE = "android.media.EXTRA_VOLUME_STREAM_TYPE";
    /**
     * 关注公司、取消关注公司广播使用
     */
    public static final String NOTIFY_REMOVE_ITEM = "com.hpbr.NOTIFY_REMOVE_ITEM";
    public static final String NOTIFY_ADD_ITEM = "com.hpbr.NOTIFY_ADD_ITEM";

    public static final String ACTION_ON_MAIN_TAB_CHANGE = PREFIX + ".ACTION_ON_MAIN_TAB_CHANGE";
    public static final String KEY_CURRENT_TAB_INDEX = PREFIX + ".KEY_CURRENT_TAB_INDEX";
    /**
     * 牛人，我的，在线简历岗位经验红点点击过
     */
    public static final String SP_GEEK_F4_POST_RED_DOT_CLICKED = PREFIX + ".SP_GEEK_F4_POST_RED_DOT_CLICKED";
    /**
     * 在线简历岗位经验, 标题new图标点击过
     */
    public static final String SP_MY_RESUME_NEW_TAG_CLICKED = PREFIX + ".SP_MY_RESUME_NEW_TAG_CLICKED";

    /**
     * 媒体播放，左右滑动动效首次播放过
     */
    public static final String SP_MEDIA_VIEW_PAGE_SLIDE_ANIM_PREFIX = PREFIX + ".SP_MEDIA_VIEW_PAGE_SLIDE_ANIM_PREFIX";

    /**
     * 附件简历媒体左右滑动动效首次播放过
     */
    public static final String SP_SLIDE_ANIM_BOSS_VIEW_RESUME_SUFFIX = "BOSS_VIEW_RESUME_SUFFIX";
    /**
     * geek 发现引导
     */
    public static final String SP_GEEK_DISCOVER_GUIDE_SHOW = PREFIX + "SP_GEEK_DISCOVER_GUIDE_SHOW";
    /**
     * boss 发现引导
     */
    public static final String SP_BOSS_DISCOVER_GUIDE_SHOW = PREFIX + "SP_BOSS_DISCOVER_GUIDE_SHOW";

    /**
     * 语音声音开始播放广播
     */
    public static final String AUDIO_PREPARE_PLAY_ACTION = PREFIX + "AUDIO_PREPARE_PLAY_ACTION";
    /**
     * 语音声音播放完毕广播
     */
    public static final String AUDIO_PLAY_COMPLETE_ACTION = PREFIX + "AUDIO_PLAY_COMPLETE_ACTION";

    /**
     * 小窗检查
     */
    public static final String KEY_CHECK_WINDOW_FUN = "/path_check_window";

    /**
     * 小窗检查
     */
    public static final String KEY_CHECK_WINDOW_FUN_2 = "/path_check_window_2";

    public static final String PATH_COMPANY_LIST_VR = "/path_company_list_vr";
    public static final String PATH_COMPANY_LIST_VR_SHOW = "/path_company_list_vr_show";

    public static final String PATH_MOCK_INTERVIEW_REC_BRAND = "/path_mock_interview_rec_brand";

    public static final String PATH_REPAIR_X5 = "/app/repair_x5";

    public static final String KEY_SHOULD_OPEN_FEEDBACK_MODULE = "key_should_open_feedback_module";

    /**
     * 牛人点击过姓名输入框上面的黄条
     */
    public static final String SP_USER_CLOSED_NAME_LEFT_COUNT_BAR = PREFIX + ".SP_USER_CLOSED_NAME_LEFT_COUNT_BAR";

    /**
     * 牛人点击过微信输入框上面的黄条
     */
    public static final String SP_USER_CLOSED_WX_LEFT_COUNT_BAR = PREFIX + ".SP_USER_CLOSED_WX_LEFT_COUNT_BAR";


    /**
     * 商业购买成功返回广播
     */
    public static final String RECEIVER_BUSINESS_BZB_BACK_RESULT = PREFIX + ".RECEIVER_BUSINESS_BZB_BACK_RESULT";

    /**
     * 商业购买成功返回广播数据
     */
    public static final String RECEIVER_BUSINESS_BZB_BACK_RESULT_DATA = PREFIX + ".RECEIVER_BUSINESS_BZB_BACK_RESULT_DATA";

    public static final String RECEIVER_AI_QUICK_RECRUIT_STATUS_CHANGE = PREFIX + "RECEIVER_AI_QUICK_RECRUIT_STATUS_CHANGE";

    public static final String RECEIVER_AI_QUICK_RECRUIT_REMAIN_MATCH_COUNT_CHANGE = PREFIX + "RECEIVER_AI_QUICK_RECRUIT_STATUS_CHANGE";


    /**
     * 1003.318 词典详情popup 神解读浮层 sp
     */
    public static final String SP_DICTIONARY_DETAIL_POPUP_ENTRY = PREFIX + ".SP_DICTIONARY_DETAIL_POPUP_ENTRY";
    /**
     * 1003.318 词典详情popup 词条造句浮层 sp
     */
    public static final String SP_DICTIONARY_DETAIL_POPUP_ENTRY_SENTENCE = PREFIX + ".SP_DICTIONARY_DETAIL_POPUP_ENTRY_SENTENCE";

    /**
     * 1009.301 推荐发布入口气泡弹窗当天0点时间
     */
    public static final String SP_GET_RECOMMEND_SHARE_ANIM = PREFIX + ".SP_GET_RECOMMEND_SHARE_ANIM";


    public static final String SP_ADS_RESULT_DESIGN_IMG = PREFIX + ".SP_ADS_RESULT_DESIGN_IMG";
    public static final String SP_QUICK_JOB_GUIDE = PREFIX + ".SP_QUICK_JOB_GUIDE";

    public static final String ACTION_PLAY_VIDEO = PREFIX + "ACTION_PLAY_VIDEO";

    /**
     * 1020.80 F2机会角标是否点击
     */
    public static final String SP_F2_CHANGE_LABEL_CLICK = PREFIX + ".SP_F2_CHANGE_LABEL_CLICK";

    /**
     * 1107.160 【C】投诉后高质量职位推荐
     */

    public static final String RECEIVER_ACTION_REPORT_F1_RCMD = PREFIX + "ACTION_REPORT_F1_RCMD";

    /**
     * 1110.3 securityIds
     */
    public static final String DATA_SECURITYIDS = PREFIX + "DATA_SECURITYIDS";

    public static final String DATA_BOSS_QUICK_CREATE_POSITION_BOOT = PREFIX + "DATA_BOSS_QUICK_CREATE_POSITION_BOOT";


    public static final String SP_SHOW_JOB_SELECT_TIP = "sp_show_job_select_tip";

    public static final String ACTION_NOTICE_WINDOW_RESET = PREFIX + "ACTION_NOTICE_WINDOW_RESET";

    public static final String ACTION_STU_EXPECT_HELPER = PREFIX + "STU_EXPECT_HELPER";

    public static final String ACTION_CHAT_HELPER_RECEIVER = PREFIX + "CHAT_HELPER_RECEIVER";

    public static final String SP_POP_JD_TEMPLATE_WRITTEN = PREFIX + "SP_POP_JD_TEMPLATE_WRITTEN";

    public static final String SP_POP_UNREAD_DISMISS_TIP = PREFIX + "SP_POP_UNREAD_DISMISS_TIP";

    public static final String SP_LOST_MESSAGE_MAX_ID = PREFIX + "SP_LOST_MESSAGE_MAX_ID";


    /**
     * 1120.603 面试间呼叫超时 限制
     */
    public static final int TIME_OUT_INTERVIEW_ROOM = 1000 * 95;

    public static final String SP_KEY_CHAT_ACTION_MESSAGE_171_PRE = "actionMessage171";
    public static final String SP_KEY_CHAT_ACTION_MESSAGE_174_PRE = "actionMessage174";

    public static final String DATA_CONTACT_ID = "contactId";

    public static final String DELETE_MSG_ID = "deleteMsgId";

    // 1224.247 【商业】免费职位操作流程中道具推荐前置：为跳转协议中，手动加入界面高度键值对
    public static final String KEY_PAGE_HEIGHT_RATIO = "pageHeightRatio";

    /**
     * 1302.165 交换区的引导
     */
    public static final String ACTION_SHOW_EXCHANGE_GUIDE = PREFIX + "EXCHANGE_GUIDE";
    public static final String KEY_EXCHANGE_TIP_TEXT = "KEY_EXCHANGE_TIP_TEXT";

    /**
     * 1303.1.4 聊天页AI助手提示条
     */
    public static final String ACTION_REFRESH_AI_STATUS_VIEW = PREFIX + "REFRESH_AI_STATUS_VIEW";
    public static final String KEY_AI_STATUS = "KEY_AI_STATUS";

    public static final String ACTION_OPEN_AI_ASSISTANT = PREFIX + "ACTION_OPEN_AI_ASSISTANT";
    /*地图降频-搜索输入-B端*/
    public static final long MAP_INPUT_DEBOUNCE_TIME_B = 800L;
    /*地图降频-搜索输入-C端*/
    public static final long MAP_INPUT_DEBOUNCE_TIME_C = 800L;
    /*地图降频-拖动地图定位-B端*/
    public static final long MAP_CAMERA_MOVE_DEBOUNCE_TIME_B = 800L;
    /*地图降频-拖动地图定位-C端*/
    public static final long MAP_CAMERA_MOVE_DEBOUNCE_TIME_C = 800L;

    public static final String KEY_PERMISSION_ANALYTIC_SERVICE = "KEY_PERMISSION_ANALYTIC_SERVICE";

    /**
     * 1312.243【商业】短信通知升级服务：角标文案的SP-Key值
     */
    public static final String SMS_LABEL_TEXT_VALUE = "smsLabelTextValue";
    /**
     * 1312.243【商业】短信通知升级服务：是否展示角标文案的灰度字段SP-Key值
     */
    public static final String SMS_LABEL_ORDER_GRAY = "smsLabelOrderGray";
}
