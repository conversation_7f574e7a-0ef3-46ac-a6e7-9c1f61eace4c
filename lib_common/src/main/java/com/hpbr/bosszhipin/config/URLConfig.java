package com.hpbr.bosszhipin.config;

import android.text.TextUtils;

import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 15/5/22.
 */
public class URLConfig {

    /**
     * 不需要Token的Http请求URL集合
     */
    public static final List<String> NONE_TOKEN_URL_LIST = new ArrayList<>(56);


    /**
     * 线上环境使用logapi.zhipin.com域名的api集合
     */
    public static final List<String> LOG_HOST_URL_LIST = new ArrayList<>(56);
    /**
     * 线上环境使用https://www.dianzhangzhipin.com域名的api集合
     */
    public static final List<String> DIAN_ZHANG_HOST_URL_LIST = new ArrayList<>(56);


    /**
     * 不要用final修改,mock batch修改不了常量
     */
    public static String API_PATH_PREFIX = "/api/";

    public static final String WEB_URL_REGISTER_PROTOCOL = getWebHost() + "mpa/html/mix/agreement-detail?agreementId=registerprotocol";
    /**
     * 获取隐私协议 WEB URL
     */
    public static final String WEB_URL_PRIVACY_PROTOCOL = getWebHost() + "mpa/html/mix/agreement-detail?agreementId=personalinfopro";

    public static final String WEB_URL_OPEN_PDF_PREFIX = getWebHost() + "mpa/html/resume-detail/attach-pdf-preview?name=";

    public static final String WEB_URL_CHANGE_PHONE_HELP = getWebHost() + "mpa/html/user/change-phone-number/auth-help";


    /**
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=114641289
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=114633037
     */
    public static final String WEB_CHANGE_MOBILE = getWebHost() + "mpa/html/user/change-phone-number/input-number";

    /**
     * BOSS直聘增值服务协议
     */
    public static final String WEB_URL_INCREMENTAL_SERVICE_PROTOCOL = getWebHost() + "mpa/html/mix/agreement-detail?agreementId=appreciationserviceprotocol";
    /**
     * 开发票
     */
    public static final String WEB_URL_GET_RECIPES = getWebHost() + "mpa/html/fapiao/record-list";

    /**
     * B看C，Boss举报牛人
     */
    public static final String WEB_URL_BOSS_REPORT_GEEK = getWebHost() + "mpa/html/mix/geek-report-reasons";

    /**
     * 发布职位发布规则
     */
    public static final String URL_PUBLISH_POSITION_PROTOCOL = getWebHost() + "mpa/html/mix/agreement-detail?agreementId=postrules";

    /**
     * 公司照片看看别人怎么拍
     */
    public static final String URL_COMPANY_PHOTO_LOOK = getWebHost() + "mpa/html/mix/company-show";

    /**
     * https://api.weizhipin.com/project/30/interface/api/312912
     */
    public static final String URL_ZP_COMMON_VIDEO_GETUPLOAD_INFO = buildUrl("zpCommon/video/opteration/getUploadInfo");

    private volatile static String API_PATH;
    private volatile static String API_PATH_SECURE;
    private volatile static String SERVER_HOST;

    /**
     * 获取安全框架url
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_SECURITY_URL = buildUrl("certification/security/get"); // done

    /**
     * 获取动态条
     * https://api.weizhipin.com/project/30/interface/api/43128
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_USER_DYNAMIC_BAR = buildUrl("zpuser/dynamicBar/get");
    /**
     * 获取用户底部按钮
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_USER_BOTTOM_BTNS = buildUrl("zpuser/user/getBottomBtns");
    /**
     * 816 用户信息监测
     * http://api.weizhipin.com/project/30/interface/api/144273
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_USER_INFO_CHECK = buildUrl("zpuser/user/check");

    /**
     * 牛人待面试数量
     * https://api.weizhipin.com/project/30/interface/api/521088
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GEEK_INTERVIEW_FIT_COUNT = buildUrl("zpinterview/geek/interview/fitcount");

    /**
     * 获取用户Boss身份的信息
     * https://api.weizhipin.com/project/30/interface/api/1064
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_BOSS_DETAIL = buildUrl("zpboss/app/boss/get/detail");

    /**
     * 校验聊天页是否展示公司名片
     * https://api.weizhipin.com/project/30/interface/api/506632
     * 【暂不可迁移】
     */
    public static final String URL_CHAT_CHECK_SHOW_BRAND_TAB = buildUrl("zpCompany/brandMix/chat/checkShowBrandTab");

    /**
     * F1职位排序相关接口
     * https://api.weizhipin.com/project/30/interface/api/147770
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_BOSS_JOB_SORT_LIST = buildUrl("zpjob/job/sort/list");

    /**
     * f1无职位或需要认证时的引导接口
     * https://api.weizhipin.com/project/30/interface/api/512296
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_USER_F1_PAGE_GUIDE = buildUrl("zpjob/guide/f1");

    /**
     * 3.7 推荐牛人
     * https://api.weizhipin.com/project/30/interface/api/1160
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_RECOMMEND_GEEK_LIST = buildUrl("zpjob/recommend/geek/list");

    /**
     * APP本地职位数据
     * https://api.weizhipin.com/project/30/interface/api/189068
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_GET_BOSS_JOB_LIST = buildUrl("zpjob/job/local/data");

    /**
     * 1119 学生主题专区主题职位banner
     * https://api.weizhipin.com/project/30/interface/api/619709
     * 【暂不可迁移 Lib_Data_Kernel】StuZoneF1BannerRequest使用
     */
    public static final String URL_STUDENT_FEATRUE = buildUrl("zpgeek/app/student/feature/query");
    /**
     * 【蓝领】兼职蓝领主题bannerV2
     * https://zhishu.zhipin.com/wiki/KLCGTsUZZZ7
     * https://api.weizhipin.com/project/30/interface/api/715828
     * 【暂不可迁移 Lib_Data_Kernel】BlueCollarTopicBannerV2Request使用
     */
    public static final String ZP_GEEK_APP_BLUE_COLLAR_TOPIC_BANNER_V2 = buildUrl("zpgeek/app/bluecollar/topic/banner/v2");

    /**
     *  f1 是否展示职位订阅精英入口
     *  https://api.weizhipin.com/project/2365/interface/api/766750
     * 【暂不可迁移 Lib_Data_Kernel】要启动时候获取保存到本地
     */
    public static final String ZP_HUNTER_APP_ADVGEEKSUBJOB_ASSISTANT_ENTRANCE = buildUrl("hunter/app/advGeekSubJob/assistant/entrance");
    /**
     * 学生兼职金刚位bannerV2
     * https://zhishu.zhipin.com/wiki/KLCGTsUZZZ7
     * https://api.weizhipin.com/project/30/interface/api/715825
     * 【暂不可迁移 Lib_Data_Kernel】StudentPartTimeTopicBannerV2Request使用
     */
    public static final String ZP_GEEK_APP_STUDENT_PART_TIME_TOPIC_BANNER_V2 = buildUrl("zpgeek/app/studentparttime/topic/banner/v2");


    /**
     * 【F1】F顶部配置接口
     * https://zhishu.zhipin.com/wiki/KLCGTsUZZZ7
     * https://zhishu.zhipin.com/wiki/Zy26diwDujt
     * 【暂不可迁移 Lib_Data_Kernel】F1TabConfigRequest
     */
    public static final String ZP_GEEK_APP_F1_TAB_CONFIG = buildUrl("zpgeek/cvapp/f1/tab/config");

    /**
     * 【牛人面板】App-geek 查询面板信息
     * 【暂不可迁移 Lib_Data_Kernel】GeekPanelQueryRequest使用
     */
    public static final String URL_GEEK_PANEL_QUERY = buildUrl("zpgeek/app/panel/query");

    /**
     * 1010.16【简历解析】查询授权信息
     * https://api.weizhipin.com/project/30/interface/api/370163
     * 【暂不可迁移 Lib_Data_Kernel】
     */
    public static final String URL_QUERY_ATTACHMENT_AUTH_INFO = buildUrl("zpgeek/cvapp/nlp/resume/parser/auth/query");

    /**
     * 【简历诊断】F3诊断卡片列表
     * 【暂不可迁移 Lib_Data_Kernel】GeekResumeSuggestCardRequest使用
     */
    public static final String URL_ZPGEEK_RESUME_SUGGEST_F3_CARD_LIST = buildUrl("zpgeek/cvapp/geek/resume/suggest/f3/card/list");

    /**
     * 获取用户牛人详情
     * https://api.weizhipin.com/project/30/interface/api/2888
     * 【暂不可迁移】lib_data_kernel中GetUserAccountGeekDetailRequest使用
     */
    public static final String URL_GET_USER_GEEK_DETAIL = buildUrl("zpgeek/cvapp/geek/baseinfo/query");

    /**
     * nlp 简历解析反馈
     * http://api.kanzhun-inc.com/project/30/interface/api/45507
     * 【暂不可迁移】lib_common中SaveResumeRequest使用
     */
    public static final String URL_NLP_RESUME_CALLBACK = buildUrl("zpgeek/cvapp/nlp/resume/parser/tips");

    /**
     * 【F1】+号期望管理入口引导红点气泡
     * https://api.weizhipin.com/project/30/interface/api/226818
     * 【暂不可迁移】lib_data_kernel中ReddotQueryRequest引用
     */
    public static final String URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_SUGGEST_REDDOT_QUERY = buildUrl("zpgeek/app/geek/expectposition/suggest/reddot/query");

    /**
     * 【F3】查询我的在线简历引导信息
     * https://api.weizhipin.com/project/30/interface/api/261459
     * 【暂不可迁移】lib_data_kernel中GeekResumeSyncStatusRequest引用
     */
    public static final String URL_ZPGEEK_APP_F3_TIP_QUERY = buildUrl("zpgeek/cvapp/f3/tip/query");

    /**
     * 806 【视频简历】查看视频简历播放信息
     * http://api.kanzhun-inc.com/project/30/interface/api/96632
     * 【暂不可迁移】boss_export中MediaPreviewSource使用
     */
    public static final String URL_GEEK_VIDEO_RESUME_PLAY_INFO = buildUrl("zpgeek/cvapp/geek/videoresume/playinfo");

    /**
     * 1018.2【C+B】简历作品优化 -【我的作品】播放作品视频
     * 【暂不可迁移】boss_export中MediaPreviewSource使用
     */
    public static final String URL_ZPGEEK_APP_RESUME_DESIGN_PLAYINFO = buildUrl("zpgeek/cvapp/resume/design/playinfo");

    /**
     * 【附件简历】Nlp附件解析结果轮询
     * http://api.kanzhun-inc.com/project/30/interface/api/152707
     * 【暂不可迁移】lib_common中NlpResumeParserStatusCheckRequest使用
     */
    public static final String URL_ZPGEEK_APP_NLP_RESUME_PARSER_STATUS_CHECK = buildUrl("zpgeek/cvapp/nlp/resume/parser/status/check");

    /**
     * 【附件简历】Nlp附件解析结果查询
     * http://api.kanzhun-inc.com/project/30/interface/api/152714
     * 【暂不可迁移】lib_common中NlpResumeParserResultQueryRequest使用
     */
    public static final String URL_ZPGEEK_APP_NLP_RESUME_PARSER_RESULT_QUERY = buildUrl("zpgeek/cvapp/nlp/resume/parser/result/query");

    /**
     * 获取用户快速回复
     * https://api.weizhipin.com/project/30/interface/api/24803
     * 【暂不可迁移】
     */
    public static final String URL_USER_QUICK_REPLY = buildUrl("zpchat/fastreply/get");

    /**
     * 【暂不可迁移】
     */
    public static final String URL_U_TOKEN_UPDATE = buildUrl("zpchat/uToken/update");
    /**
     * 【暂不可迁移】
     */
    public static final String URL_TOKEN_CLEAR = buildUrl("zpchat/apnstoken/clear");

    /**
     * 添加其它渠道的推送，用于更新某个Push渠道的token
     * 【暂不可迁移】
     */
    public static final String URL_TOKEN_UPDATE = buildUrl("zpchat/apnstoken/update");

    /**
     * 获得群设置信息
     * 【暂不可迁移】
     */
    public static final String URL_GET_GROUP_INFO = buildUrl("zpchat/group/getGroupInfo");

    /**
     * 获取群成员列表
     * 【暂不可迁移】
     */
    public static final String URL_GET_GROUP_MEMBER_LIST = buildUrl("zpchat/group/getMembers");

    /**
     * 已加入的群列表
     * 【暂不可迁移】
     */
    public static final String URL_GET_JOINED_GROUP_CONTACTS = buildUrl("zpchat/group/getJoinedGroups");

    /**
     * 获取群名片
     * 【暂不可迁移】
     */
    public static final String URL_GET_MEMBER_INFO = buildUrl("zpchat/group/getMemberInfo");

    /**
     * 删除视频常用语
     * https://api.weizhipin.com/project/30/interface/api/147426
     * 【暂不可迁移】
     */
    public static final String URL_FAST_REPLAY_VIDEO_DELETE = buildUrl("zpchat/fastreply/video/delete");

    /**
     * 获取常用语视频招呼列表
     * http://api.weizhipin.com/project/30/interface/api/147411
     * 【暂不可迁移】
     */
    public static final String URL_FAST_REPLAY_VIDEO_LIST = buildUrl("zpchat/fastreply/video/list");

    /**
     * 发送视频常用语
     * https://api.weizhipin.com/project/30/interface/api/147427
     * 【暂不可迁移】
     */
    public static final String URL_FAST_REPLAY_VIDEO_SEND = buildUrl("zpchat/fastreply/video/send");

    /**
     * 获取好友全量信息
     * 【暂不可迁移】
     */
    public static final String URL_FRIEND_FULL_INFO = buildUrl("zprelation/friend/getFullInfo");

    /**
     * 获取好友全量信息
     * 【暂不可迁移】
     */
    public static final String URL_FRIEND_BASE_INFO_LIST = buildUrl("zprelation/friend/getBaseInfoList");

    /**
     * https://api.weizhipin.com/project/30/interface/api/642071
     * 【暂不可迁移】
     */
    public static final String URL_FRIEND_LIST = buildUrl("zprelation/friend/getFriendIdList");
    /**
     * 通讯录3.0获取全量好友 id V3版本
     * https://api.weizhipin.com/project/30/interface/api/777175
     * 【暂不可迁移】
     */
    public static final String URL_FRIEND_LIST_V3 = buildUrl("zprelation/friend/getFriendIdListV1");
    /**
     * 获取部分联系人列表
     * https://api.weizhipin.com/project/30/interface/api/284454
     * 【暂不可迁移】
     */
    public static final String URL_FRIEND_BASE_INFO = buildUrl("zprelation/friend/getBaseInfo");

    /**
     * 获取boss非沟通钥匙职位ID列表
     * https://api.weizhipin.com/project/30/interface/api/421763
     * 【暂不可迁移】
     */
    public static final String URL_ZP_BLOCK_CHAT_KEY_LIST = buildUrl("zpblock/job/not/chatkey/list");
    /**
     * 获取新阻断预下单
     * https://api.weizhipin.com/project/30/interface/api/208510
     * 【暂不可迁移】
     */
    public static final String URL_ZP_BLOCK_V2_PAY_PRE_ORDER = buildUrl("zpblock/order/v2/preorder");
    /**
     * 1120.233【商业】vvip-精选版升级 - 获取VVIP批量开聊基本信息
     * https://api.weizhipin.com/project/30/interface/api/628610
     * 【暂不可迁移】
     */
    public static final String URL_ZPBLOCK_VVIP_BATCH_CHAT_GET_INFO = buildUrl("zpblock/vvip/batch/chat/get/info");

    /**
     * 背景调查 B-C聊天页加号入口
     * https://api.weizhipin.com/project/30/interface/api/247536
     * 【暂不可迁移】
     */
    public static final String URL_BACK_CHECK_CHAT_PAGE = buildUrl("zpitem/backgroundCheck/chatPage/entrance");

    /**
     * 牛人电话直拨期望引导
     * https://api.weizhipin.com/project/30/interface/api/261531
     * 【暂不可迁移】
     */
    public static final String URL_ZPITEM_DIRECTCALL_EXPECTGUIDE = buildUrl("zpitem/directCall/expectGuide");

    /**
     * 7.15 BossF3 道具列表
     * 【暂不可迁移】
     */
    public static final String URL_ZPITEM_BOSS_GET_ITEM_MALL_F_3 = buildUrl("zpitem/boss/getItemMallF3");

    /**
     * 1108.201【待支付订单】待支付订单列表
     * <a href="https://api.weizhipin.com/project/30/interface/api/545266">...</a>
     * 【暂不可迁移】
     */
    public static final String URL_USER_UNPAID_ORDER_LIST = buildUrl("zpp/app/user/unBzbOrderList");

    /**
     * 1124.201 充值引导卡片查询
     * <a href="https://api.weizhipin.com/project/30/interface/api/644267">...</a>
     * 【暂不可迁移】
     */
    public static final String URL_APP_USER_RECHARGE_GUIDE = buildUrl("zpp/app/user/rechargeGuide");

    /**
     * 1201.245 app端简历交付获取交付进度
     * <a href="https://api.weizhipin.com/project/30/interface/api/665110">...</a>
     * 【暂不可迁移】
     */
    public static final String URL_ZPITEM_DELIVER_SCHEDULE = buildUrl("zpitem/resumeDeliver/getDeliverSchedule");

    /**
     * 710 牛人F3互动道具卡片入口
     * http://api.kanzhun-inc.com/project/30/interface/api/29101
     * 【暂不可迁移】
     */
    public static final String URL_GET_F3_ITEM_CARD = buildUrl("zpitem/geek/getF3ItemCard");

    /**
     * 7.14 牛人简历页引导简历刷新
     * http://api.kanzhun-inc.com/project/30/interface/api/39361
     * 【暂不可迁移】
     */
    public static final String URL_GUIDE_RESUME_REFRESH = buildUrl("zpitemGeek/resume/guideResumeRefresh");

    /**
     * // 牛人F4 道具列表
     * 【暂不可迁移】
     * https://api.weizhipin.com/project/30/interface/api/24119
     */
    public static final String URL_GEEK_GET_ITEM_MALL_F4 = buildUrl("zpitemGeek/geek/getItemMallF4");

    /**
     * f4牛人VIP卡片信息
     * <p>
     * http://api.kanzhun-inc.com/project/30/interface/api/29029
     * 【暂不可迁移】
     */
    public static final String URL_ZPITEM_GEEK_VIP_INFO = buildUrl("zpitemGeek/geek/vip/info");

    /**
     * 推荐职位接口
     * 【暂不可迁移】lib_common中McpLog使用
     * https://api.weizhipin.com/project/30/interface/api/2432
     */
    public static final String URL_RECOMMEND_JOB_LIST = buildUrl("zpgeek/app/geek/recommend/joblist");

    /**
     * 【身份切换】C身份F4切换身份入口
     * <p>
     * https://api.weizhipin.com/project/30/interface/api/375491
     * 【暂不可迁移】lib_data_kernel中SwitchEntranceRequest使用
     */
    public static final String URL_ZPGEEK_APP_IDENTITY_SWITCH_ENTRANCE = buildUrl("zpgeek/cvapp/identity/switch/entrance");

    /**
     * 1206.500 猎头个人主页引导条
     * https://api.weizhipin.com/project/2365/interface/api/692615
     * 【暂不可迁移 lib_data_kernel】
     */
    public static final String URL_GET_HUNTER_GUIDANCE_BAR = buildUrl("hunter/app/hunter/home/<USER>");

    /**
     * B端-chat入口
     * https://api.weizhipin.com/project/2365/interface/api/625217
     * 【暂不可迁移】
     */
    public static final String URL_HUNTER_APP_INTENTTION_BOSS_CHAT_ENTRANCE = buildUrl("hunter/app/intention/boss/chat/entrance");

    /**
     * 获取城市职类信息
     * <p>
     * http://api.kanzhun-inc.com/project/30/interface/api/144489
     */
    public static final String URL_ZP_COMMON_CONFIG_GET_CITY_POSITION = buildUrl("zpCommon/config/getCityPosition");

    /**
     * 批量接口（一次请求调用多个原子接口） 非登录接口
     */

    public static final String URL_BATCH_RUN = buildUrl("batch/batchRunV2");

    /**
     * 根据ip获取城市信息
     */
    public static final String URL_ZP_COMMON_USER_GET_ATTRIBUTION_BY_IP = buildUrl("zpCommon/user/getAttributionByIP");

    /**
     * 定时上传经纬度
     */
    public static final String URL_ZP_COMMON_REPORT_LOCATION = buildUrl("zpCommon/reportLocation");


    /**
     * 观星台
     */
    public static final String URL_AB_COMMON_TOGGLE_ALL = buildUrl("zpCommon/toggle/all");
    /**
     * 观星台 获取智能发布指定开关配置
     */
    public static final String SAMPLING_TOGGLE_LIST_FLAT = buildUrl("zpCommon/toggle/list/flat");
    /**
     * 原始数据配置数据返回 接口名： config.get 参数：无 不需要登录
     */
    public static final String URL_CONFIG_GET = buildUrl("zpCommon/config/get");
    /**
     * 获取配置数据的数据 不需要登录 非登录接口
     */
    public static final String URL_DATA_GET = buildUrl("zpCommon/data/get");
    /**
     * 获取技能词数据 不需要登录 非登录接口
     */
    public static final String URL_GET_SKILL_WORD = buildUrl("zpCommon/skillwordsConfig/get");
    /**
     * 获取商圈的基础数据
     */
    public static final String URL_CONFIG_BUSINESS_DISTRICT = buildUrl("zpCommon/config/businessDistrict");
    /**
     * 获取地铁的基础数据
     */
    public static final String URL_CONFIG_SUBWAY = buildUrl("zpCommon/config/subway");

    /**
     * 1004 - 新增获取行业信息
     */
    public static final String URL_CONFIG_INDUSTRY = buildUrl("zpCommon/config/industry");

    /**
     * 1004 - 获取城市数据
     */
    public static final String URL_CONFIG_CITY = buildUrl("zpCommon/config/city");

    /**
     * 1004 - 获取薪资数据
     */
    public static final String URL_CONFIG_SALARY = buildUrl("zpCommon/config/salary");

    /**
     * 1004 - 获取学历学位
     */
    public static final String URL_CONFIG_DEGREE = buildUrl("zpCommon/config/degree");

    /**
     * 1004 - 获取实习职位
     */
    public static final String URL_CONFIG_INTERN = buildUrl("zpCommon/config/internPosition");

    /**
     * 1004 - 获取学历学位
     */
    public static final String URL_CONFIG_EXPERIENCE = buildUrl("zpCommon/config/experience");


    /**
     * 1004 - 获取公司规模
     */
    public static final String URL_CONFIG_SCALE = buildUrl("zpCommon/config/scale");

    /**
     * 1004 - 获取公司融资阶段
     */
    public static final String URL_CONFIG_STAGE = buildUrl("zpCommon/config/stage");
    /**
     * 1004 - 获取院校筛选
     */
    public static final String URL_CONFIG_SCHOOL_SEARCH = buildUrl("zpCommon/config/schoolSearch");

    /**
     * 1004 - 获取兼职结算类型
     */
    public static final String URL_CONFIG_PARTIME_PAYTYPE = buildUrl("zpCommon/config/partTimeJobPayType");

    /**
     * 1004 - 获取距离筛选
     */
    public static final String URL_CONFIG_DISTANCE_FILTER = buildUrl("zpCommon/config/distanceFilter");

    /**
     * 1004 - 获取Boss筛选
     */
    public static final String URL_CONFIG_BOSS_FILTER = buildUrl("zpCommon/config/bossFilterConfig");

    /**
     * 1004 - 获取Boss筛选
     */
    public static final String URL_CONFIG_GEEK_FILTER = buildUrl("zpCommon/config/geekFilterConfig");
    /**
     * 1121 - 【驻外岗位偏好】获取驻外时长基础数据配置
     * https://api.weizhipin.com/project/30/interface/api/630929
     */
    public static final String URL_OVERSEAS_DURATION = buildUrl("zpCommon/config/getDurationConfig");
    /**
     * 1121 - 【驻外岗位偏好】获取语言基础数据配置
     * https://api.weizhipin.com/project/30/interface/api/630920
     */
    public static final String URL_OVERSEAS_LANGUAGE = buildUrl("zpCommon/config/getLanguageConfig");
    /**
     * 获取附近距离基础数据
     */
    public static final String URL_CONFIG_DISTANCE = buildUrl("config/distanceFilter");
    /**
     * 不需要登录信息
     */
    public static final String URL_PHOTO_UPLOAD_NO_LOGIN = buildUrl("zpupload/naUploadImage");//done
    /**
     * 上传聊天信息 参数： file ：文件 multipartType：多媒体类型 0：图片 1：语音
     */
    public static final String URL_PHOTO_UPLOAD = buildUrl("zpupload/uploadSingle");//done


    /**
     * 升级类型： 1:app升级 2:检测配置（职位、城市。。json串）升级
     */
    public static final String URL_UPGRADE_CHECK = buildUrl("zpCommon/upgrade/check");

    /**
     * 统计埋点
     */
    public static final String URL_COMMON_BATCH_STATISTICS = buildUrl("zpCommon/batch/statistic");
    public static final String URL_COMMON_BATCH_USTATISTICS = buildUrl("zpCommon/batch/unloginStatistics");


    /**
     * 获取开关状态
     */
    public static final String URL_SWITCH_GET = buildUrl("zpCommon/switchs/get");

    public static final String URL_GET_AREA_LIST = buildUrl("zpCommon/listForCityAreaGps");

    /**
     * 获取开屏广告
     */
    public static final String URL_SCREEN_ADVERT_GET = buildUrl("zpCommon/user/startupAd");
    /**
     * F1曝光统计  7.17从/api/zpupload/logCollector 接口更改为 /api/zpCommon/log/collector
     */
    public static final String URL_LOG_COLLECTOR = buildUrl("zpCommon/log/collector");
    /**
     * 日志文件上传接口
     * http://api.weizhipin.com/project/30/interface/api/22532
     */
    public static final String URL_LOG_UPLOAD = buildUrl("zpupload/uploadAppLog");//done

    public static final String URL_GET_CITY_POSITION = buildUrl("zpCommon/config/cityposition");
    /**
     * 获得广告接口
     * https://api.weizhipin.com/project/30/interface/api/13756
     */
    public static final String URL_GET_ADVERTISE_V2 = buildUrl("zpCommon/adActivity/getV2");
    /**
     *
     */
    public static final String URL_COMMON_APP_ACTIVATE = buildUrl("zpCommon/app/activate");
    /**
     * 牛人上传附件简历接口
     */
    public static final String URL_ATTACHMENT_RESUME_UPLOAD = buildUrl("zpupload/uploadResumeFile");//done

    /**
     * 生成deviceid
     */
    public static final String URL_GENERATE_DEVICE_ID = buildUrl("zpCommon/app/generateDeviceId");

    /**
     * 技能标签
     */
    public static final String URL_SKILL = buildUrl("zpCommon/position/skill");

    /**
     * 获取服务端配置接口
     * 5.5.2添加
     */
    public static final String URL_SERVER_CONFIG = buildUrl("zpCommon/config/custom");

    /**
     * 获取分享朋友圈信息
     */
    public static final String URL_GET_SHARE_TIMELINE = buildUrl("share/getShareTimeline");

    /**
     * 分享职位回调
     */
    public static final String URL_SHARE_CALLBACK = buildUrl("job/shareCallback");

    /**
     * 多图片上传
     */
    public static final String URL_MULTIPLE_PHOTO_UPLOAD = buildUrl("zpupload/uploadMultiPhoto");

    /**
     * APP批量上传网络文件
     */
    public static final String URL_ZPUPLOAD_MULTI_UPLOAD_NET_FILE = buildUrl("zpupload/multiUploadNetFile");

    /**
     * APP上传网络文件
     */
    public static final String URL_ZPUPLOAD_UPLOAD_NET_FILE = buildUrl("zpupload/uploadNetFile");
    /**
     * 用户满意度调查
     */
    public static final String URL_GET_SATISFACTION_INVESTIGATION = buildUrl("pushengine/nps/investigate");

    public static final String URL_GET_BLACK_APP_LIST = buildUrl("zpCommon/blackApk/list");

    public static final String URL_UPLOAD_RUNNING_INFO = buildUrl("zpCommon/blackApk/statistic");

    public static final String URL_NPS_SUBMIT = buildUrl("zpdac/nps/submit");

    // 非登录态配置接口
    public static final String URL_COMMON_CONFIG = buildUrl("zpCommon/common/config");
    public static final String URL_COMMON_H5_DOMAIN_CONFIG = buildUrl("zpCommon/h5/domain/mapping");

    // 登录态配置接口（需要登录）
    public static final String URL_COMMON_USER_CONFIG = buildUrl("zpCommon/userConfig");
    /**
     * 【蓝领】查询蓝领推荐职类
     * http://api.kanzhun-inc.com/project/30/interface/api/30421
     */
    public static final String URL_BLUE_RECOMMAND_POSITION = buildUrl("zpdac/position/recommend");

    /**
     * 7.13 资格证书suggest
     */
    public static final String URL_GEEK_CERTIFICATION_AUTO_COMPLETE = buildUrl("zpCommon/certificate/autoComplete/v2");

    /**
     * https://api.weizhipin.com/project/2535/interface/api/556030
     */
    public static final String URL_PUSHENGINE_CONTENT_TIP = buildUrl("pushengine/contentTip");

    /**
     * 908 安全F1页面强提示
     * http://api.kanzhun-inc.com/project/30/interface/api/197020
     */
    public static final String URL_ZPDAC_SAFE_TIP = buildUrl("zpdac/safeTip");
    /**
     * 安全F1页面强提示弹出回调
     * http://api.kanzhun-inc.com/project/30/interface/api/197048
     */
    public static final String URL_ZPDAC_SAFE_CALL = buildUrl("zpdac/safeCall");

    /**
     * APP-获取内部链接配置
     * <a href="https://api.weizhipin.com/project/30/interface/api/736633">...</a>
     */
    public static final String URL_CHAT_CONFIG_INNER_LINK_RULE_LIST = buildUrl("zpchat/config/getInnerLinkRuleList");

    /**
     * IPV6探测,单独使用IPV6域名
     */
    public static final String URL_IPV6_CONFIG = "https://api-ipv6.zhipin.com/api/zpCommon/ipv6Config";

    public static final String URL_GET_ZPKE_VIDEO_URL = buildUrl("zpke/app/get/video/url");

    public static final String URL_QUICK_UPLOAD = buildUrl("zpupload/quicklyUpload");

    public static final String URL_SIGN_REFRESH_UPLOAD = buildUrl("zpupload/oss/sign/refresh");

    public static final String URL_ZPMSG_HISTORY_PULL = buildUrl("zpmsg/history/pull");

    public static final String URL_SHORTURL_ORIGINAL = buildUrl("shorturl/getOriginal");

    /**
     * 【1217 】H5 秒开 拉取缓存清单
     * <a href="https://api.weizhipin.com/project/30/interface/api/714817">...</a>
     */
    public static final String URL_COMMON_FE_CONFIG = buildUrl("zpCommon/fe/version/h5/config");

    /**
     * 【1225.605】进入面试间
     * <a href="https://api.weizhipin.com/project/30/interface/api/737035">...</a>
     */
    public static final String URL_INTERVIEW_ROOM_ENTER = buildUrl("zpinterview/ai/interview/room/enter");
    /**
     * 【1225.605】离开面试间
     * <a href="https://api.weizhipin.com/project/30/interface/api/737062">...</a>
     */
    public static final String URL_INTERVIEW_ROOM_LEAVE = buildUrl("zpinterview/ai/interview/room/leave");

    /**
     * 【1225.605】完成面试
     * <a href="https://api.weizhipin.com/project/30/interface/api/737083">...</a>
     */
    public static final String URL_INTERVIEW_ROOM_FINISH = buildUrl("zpinterview/ai/interview/room/finishInterview");


    /**
     * 1306.46【招聘者】营业执照执照不清晰提示
     * <a href="https://api.weizhipin.com/project/30/interface/api/737083">...</a>
     */
    public static final String URL_CERTIFICATION_CHECK_LICENSE_CLARITY = buildUrl("certification/newCert/checkLicense/clarity");

    /**
     * 1308.170【招聘者】新增AI面试考察重点：获取面试考察重点界面的 BizCode
     * <a href="https://api.weizhipin.com/project/30/interface/api/768112">...</a>
     */
    public static final String URL_INTERVIEW_AI_FOCUS_POINTS_EXPERIMENT = buildUrl("zpinterview/boss/interview/aiFocusPoints/experiment");


    static {
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_GENERATE_DEVICE_ID));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_BATCH_RUN));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_GET));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_DATA_GET));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_GET_SKILL_WORD));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_DISTANCE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_SUBWAY));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_BUSINESS_DISTRICT));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_CITY));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_DEGREE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_BOSS_FILTER));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_DISTANCE_FILTER));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_EXPERIENCE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_GEEK_FILTER));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_INDUSTRY));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_INTERN));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_SALARY));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_SCALE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_SCHOOL_SEARCH));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_STAGE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_CONFIG_PARTIME_PAYTYPE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_U_TOKEN_UPDATE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_UPGRADE_CHECK));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_SCREEN_ADVERT_GET));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_COMMON_BATCH_USTATISTICS));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_GET_CITY_POSITION));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_GET_ADVERTISE_V2));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_COMMON_APP_ACTIVATE));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_PHOTO_UPLOAD_NO_LOGIN));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_COMMON_CONFIG));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_IPV6_CONFIG));
        NONE_TOKEN_URL_LIST.add(getUrlPath(URLConfig.URL_SHORTURL_ORIGINAL));
    }

    static {
        LOG_HOST_URL_LIST.add(URLConfig.URL_COMMON_BATCH_STATISTICS);
        LOG_HOST_URL_LIST.add(URLConfig.URL_COMMON_BATCH_USTATISTICS);
        LOG_HOST_URL_LIST.add(URLConfig.URL_LOG_COLLECTOR);

    }


    protected static String buildUrl(String api) {
        return getHost() + api;
    }


    public static String getHost() {
        String url = API_PATH_SECURE;
        if (url == null) {
            SERVER_HOST = HostConfig.CONFIG.getApiAddr();
            url = API_PATH_SECURE = "https://" + SERVER_HOST + "/api/";
        }
        return url;
    }

    protected static String buildUrlWithoutApi(String api) {
        return getHostWithoutApi() + api;
    }


    public static String getHostWithoutApi() {
        String host = HostConfig.CONFIG.getApiAddr();
        String url = "https://" + host + "";
        return url;
    }

    public static String getWebHost() {
        return HostConfig.CONFIG.getWebAddr();
    }

    public static String getUrlPath(String url) {
        if (LText.empty(url)) return "";
        final String API = "/api/";
        int apiLocation = url.indexOf(API);
        if (apiLocation < 0) {
            return getUrlPath2(url);
        } else {
            return url.substring(apiLocation);
        }
    }

    /**
     * 处理 非api接口
     *
     * @param url
     * @return
     */
    private static String getUrlPath2(String url) {
        if (LText.empty(url)) return "";
        String path = "/napi/";
        int apiLocation = url.indexOf(path);
        if (apiLocation >= 0) {
            return url.substring(apiLocation);
        }

        path = "/aflow/";
        apiLocation = url.indexOf(path);
        if (apiLocation >= 0) {
            return url.substring(apiLocation);
        }
        return url;
    }

    public static String getUrlPathWitoutApi(String url) {
        if (LText.empty(url)) return "";
        final String API = "/api/";
        int apiLocation = url.indexOf(API);
        if (apiLocation < 0) {
            return url;
        } else {
            return url.substring(apiLocation + API.length());
        }
    }

    public static boolean isNeedLoginUrl(String url) {
        return !NONE_TOKEN_URL_LIST.contains(getUrlPath(url));
    }


    public static String transformUrlHost(String url) {
        if (HostConfig.CONFIG != HostConfig.Addr.ONLINE) {
            return url;
        }
        String path = URLConfig.getUrlPath(url);
        if (LOG_HOST_URL_LIST.contains(url)) {
            return "https://logapi-and.zhipin.com" + path;
        }
        if (DIAN_ZHANG_HOST_URL_LIST.contains(url)) {
            return "https://www.dianzhangzhipin.com" + path;
        }
        if (DNSCommon.isSupportIpV6() && url.startsWith(getApiHost()) &&
                !TextUtils.isEmpty(HostConfig.CONFIG.getApiIPV6Addr())) {
            return "https://" + HostConfig.CONFIG.getApiIPV6Addr() + path;
        }
        return url;
    }

    private static String sApiHost = null;

    private static String getApiHost() {
        if (sApiHost == null) {
            sApiHost = "https://" + HostConfig.Addr.ONLINE.getApiAddr();
        }
        return sApiHost;
    }

}
