package com.hpbr.bosszhipin.gray;


import com.google.gson.annotations.SerializedName;

import net.bosszhipin.base.HttpResponse;


public class BossGrayResponse extends HttpResponse {

    private static final long serialVersionUID = 4746505890972826264L;

    public DataResult<Boolean> get_boss_show_publish_guide;


    /**
     * 1125.923 BOSSAPP第二tab名称 默认有了
     */
    public DataResult<String> boss_tabtwo_name;
    //需求开关
    public DataResult<Integer> exp_autogen_subject_11715_number_1;
    /**
     * 1208.920 是否展示职位视频推荐卡片新样式
     */
    public DataResult<Integer> show_get_videojd_new_style;


    /**
     * 1210.41 首善公司填写流程优化
     */
    public DataResult<Integer> exp_autogen_subject_11782_number_1;

    /**
     * 1212.612【C】引导用户左右滑动切换职位
     */
    public DataResult<Integer> exp_autogen_subject_11881_number_1;

    /**
     * https://zhishu.zhipin.com/wiki/jkravX2e0B6
     * 1214.610【C】引导隐藏简历用户打开简历@芮琦@张群
     */
    public DataResult<Integer> exp_autogen_subject_11936_number_1;

    /**
     * https://zhishu.zhipin.com/wiki/gKbBmVasolT
     * 1214.612【C】针对开聊后无达成的情况，在消息对话时给牛人推荐更活跃的职位
     */
    public DataResult<Integer> exp_autogen_subject_11951_number_1;

    /**
     * <a href="https://zhishu.zhipin.com/wiki/kpt9YO6fdCy">...</a>
     * 1309.801【C】在线简历编辑与诊断新页面
     */
    @SerializedName("exp_autogen_subject_13142_number_1")
    public DataResult<Integer> newOnlineResumeGray;
    /**
     * 1214.605_C端_今日速配人群扩量人群_交互调整优化_燕鑫_参数1_11946
     */
    public DataResult<Integer> exp_autogen_subject_11946_number_1;

    /**
     * 1215.613_C端_达成_引导C_接受交换_1_期
     * https://zhishu.zhipin.com/wiki/b0DRBXJ8hRg
     */
    public DataResult<Integer> exp_autogen_subject_11986_number_1;

    /**
     * 1217.921 https://zhishu.zhipin.com/wiki/tnWD8QfBPLo
     * 联系人展示UI 灰度
     */
    public DataResult<Integer> exp_autogen_subject_12044_number_1;
    /**
     * 1217.921 https://zhishu.zhipin.com/wiki/tnWD8QfBPLo
     * 新筛选能力 灰度
     */
    public DataResult<Integer> exp_autogen_subject_12044_number_2;


    /**
     * 1218.618【C】蓝领工作经历填写流程优化
     * <a href="https://zhishu.zhipin.com/wiki/oZsmSHwDG73">...</a>
     */
    @SerializedName("exp_autogen_subject_12073_number_1")
    public DataResult<Integer> blueStepCompWorkExperiment;  //0-对照组  1-实验组

    /**
     * 1219.112 F1卡片展示UI优化
     */
    public DataResult<Integer> exp_autogen_subject_12175_number_1; //0 不满足灰度  1满足灰度

    /**
     * 1221.45_招聘者_营业执照上传图片压缩优化
     * https://datastar-pre.kanzhun-inc.com/sampling/sampling/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12239_number_1&mode=detail
     */
    public DataResult<Integer> exp_autogen_subject_12239_number_1;


    /**
     * https://datastar.kanzhun-inc.com/sampling/sampling/ab/experimen-detail?id=12191
     */
    public DataResult<Integer> exp_autogen_subject_12191_number_1;

    public DataResult<Integer> voice_to_word;

    public DataResult<Integer> exp_autogen_subject_12317_number_1;

    /**
     * 1223.611【C】 站内横幅消息提醒优化
     */
    public DataResult<Integer> exp_autogen_subject_12419_number_1;

    /**
     * 1224.31_招聘者_职位草稿优化
     * <a href="https://datastar.kanzhun-inc.com/sampling/sampling/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12395_number_1&mode=detail">...</a>
     */
    public DataResult<Integer> exp_autogen_subject_12395_number_1;
    /**
     * 1224.111【招聘者】F2列表页筛选工具优化（APP）
     * https://datastar.kanzhun-inc.com/sampling/sampling/ab/experimen-detail?id=12499
     */
    public DataResult<Integer> exp_autogen_subject_12499_number_1;

    /**
     * 1225.40【招聘者】首善默认选中推荐品牌名
     * https://datastar.kanzhun-inc.com/sampling/sampling/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12522_number_1&mode=detail
     */
    @SerializedName("exp_autogen_subject_12522_number_1")
    public DataResult<Integer> bossCompleteChooseBrand;

    /**
     * 1225.920【C】消息页「全部tab」推荐排序
     * https://datastar.kanzhun-inc.com/sampling/sampling/ab/experimen-detail?id=12534
     */
    public DataResult<Integer> exp_autogen_subject_12534_number_1;

    @SerializedName("B_Framework")
    public DataResult<Integer> getNewBossHome;

    @SerializedName("widget-guide-tip-switch")
    public DataResult<Boolean> widgetGuideTipSwitch;

    /**
     * 1226.40_招聘者_首善职位名称选择流程优化
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12558_number_1&mode=detail">...</a>
     */
    @SerializedName("exp_autogen_subject_12558_number_1")
    public DataResult<Integer> firstPostPositionProcessOptimization;

    @SerializedName("exp_autogen_subject_12630_number_1")
    public DataResult<Integer> interviewBeautyGray;

    @SerializedName("exp_autogen_subject_12759_number_1")
    public DataResult<Integer> isNewBusinessArea;


    @SerializedName("exp_autogen_subject_12941_number_1")
    public DataResult<Integer> aiGuideStyle;

    public DataResult<Integer> exp_autogen_subject_12737_number_1;

    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * <a href="https://zhishu.zhipin.com/wiki/4Hu43DL0uR4?from=create">...</a>">...</a>
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12754_number_1&mode=detail">...</a>
     */

    public DataResult<Integer> exp_autogen_subject_12754_number_1;

    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * <a href="https://zhishu.zhipin.com/wiki/4Hu43DL0uR4?from=create">...</a>">...</a>
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12777_number_1&mode=detail">...</a>
     */
    public DataResult<Integer> exp_autogen_subject_12777_number_1;

    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * <a href="https://zhishu.zhipin.com/wiki/4Hu43DL0uR4?from=create">...</a>">...</a>
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12776_number_1&mode=detail">...</a>
     */
    public DataResult<Integer> exp_autogen_subject_12776_number_1;

    /**
     * 1303.111 【招聘者】消息列表卡片信息展示优化
     */
    public DataResult<Integer> exp_autogen_subject_12772_number_1;
    /**
     * 语音转写按钮灰度实验
     */
    public DataResult<Integer> exp_autogen_subject_12800_number_1;

    /**
     * 1304.921【B&C】聊天交换错位问题解决
     */
    public DataResult<Integer> exp_autogen_subject_12818_number_1;

    public DataResult<Integer> exp_autogen_subject_12924_number_1;


    public DataResult<Integer> exp_autogen_subject_13017_number_1;

    public DataResult<Integer> exp_autogen_subject_13016_number_1;




    @SerializedName("open_v_ugc_editor")
    public DataResult<Boolean> openVEditor; //true 开启 false 关闭
    /**
     * 1305.155_招聘者_版本_中介职位名称支持sug推荐与职类引导
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12930_number_1&mode=detail">...</a>
     */
    @SerializedName("exp_autogen_subject_12930_number_1")
    public DataResult<Integer> postPositionIntermediarySug;


    /**
     * 1305.114【BC】聊天页面--增加接受请求的快捷入口
     */
    public DataResult<Integer> exp_autogen_subject_12885_number_1;
    /**
     * 1306.110【招聘者】F2筛选优化2期-极速筛选优化（APP）
     */
    @SerializedName("exp_autogen_subject_12959_number_1")
    public DataResult<Integer> bossFastHandleGray;

    @SerializedName("exp_autogen_subject_13082_number_1")
    public DataResult<Integer> postJobDescBackDialogStyle;

    /**
     * 1305.910 视频简历交换功能实验开关
     */
    public DataResult<Integer> Exchange_video_resumes;

    /**
     * 1306.46_招聘者_营业执照执照不清晰提示
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_12975_number_1&mode=detail">...</a>
     */
    @SerializedName("exp_autogen_subject_12975_number_1")
    public DataResult<Integer>  openLicenseDefinition;

    /**
     * 1306.40_招聘者_APP端职务填写引导_肖舸_张群_参数1_12961
     */
    @SerializedName("exp_autogen_subject_12961_number_1")
    public DataResult<Integer> bossTitleSuggestGuide;

    /**
     * 1307.921【内容】社区 AI 求职助手&春招 Agent
     */
    @SerializedName("tab2_top_ai_entrance")
    public DataResult<String> momentAskAiTopIconConfig;

    @SerializedName("exp_autogen_subject_13115_boolean_1")
    public DataResult<Boolean> geekSortItemSource;

    @SerializedName("exp_autogen_subject_13065_number_1")
    public DataResult<Integer> c_push_mqtt_channel;


    @SerializedName("exp_autogen_subject_13066_number_1")
    public DataResult<Integer> b_push_mqtt_channel;


    /**
     * 1308.122_app_小程序_目标_家庭地址功能区重构_2_story3去图片_参数1_13097
     */

    public DataResult<Integer> exp_autogen_subject_13097_number_1;

    /**
     * 1310.33【招聘者】F1添加职位按钮
     */
    @SerializedName("exp_autogen_subject_13175_number_1")
    public DataResult<Integer> f1TopMenuStyleGray;


    @SerializedName("exp_autogen_subject_13193_number_1")
    public DataResult<Integer> newHelloNewStyle;

    /**
     * 1310.111【招聘者】仅沟通改为沟通中
     * https://datastar.kanzhun-inc.com/sampling/sampling/ab/experimen-detail?id=13187
     */
    @SerializedName("exp_autogen_subject_13187_number_1")
    public DataResult<Integer> bossContactOnlyContactToCommunicating;

    /**
     * 1310.112【招聘者】未读列表展示
     * https://datastar.kanzhun-inc.com/sampling/sampling/ab/experimen-detail?id=13182
     */
    @SerializedName("exp_autogen_subject_13182_number_1")
    public DataResult<Integer> bossShowUnreadListGray;

    public DataResult<Integer> exp_autogen_subject_13205_number_1;

    @SerializedName("Search_entry_for_nterview_questions")
    public DataResult<Integer> interviewSearchHide;

    /**
     * 1311.111【招聘者】批量处理卡片UI优化：实验配置
     * <a href="https://ab.kanzhun-inc.com/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=exp_autogen_subject_13251_number_1&mode=detail">...</a>
     */
    @SerializedName("exp_autogen_subject_13251_number_1")
    public DataResult<Integer> handleGeekNewChatCard;

    @SerializedName("popup_window_report")
    public DataResult<Integer> popupWindowReport;


    public DataResult<Integer> exp_autogen_subject_13261_number_1;
}
