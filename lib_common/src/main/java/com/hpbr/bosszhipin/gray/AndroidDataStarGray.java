package com.hpbr.bosszhipin.gray;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.bzl.safe.crashprotect.BZLCrashProtectManager;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.gray.bean.AnalyticsStrategyBean;
import com.hpbr.bosszhipin.gray.bean.MapConfigBean;
import com.hpbr.bosszhipin.gray.bean.MqttConfigBean;
import com.hpbr.bosszhipin.gray.bean.UserInfoRefreshDelayStrategyBean;
import com.hpbr.bosszhipin.helper.gray.BZLCrashProtectGrayHelper;
import com.hpbr.bosszhipin.service.location.ILocationProvider;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;


/**
 * <a href="https://datastar.kanzhun-inc.com/sampling/sampling/sdk-list">...</a>
 * boss-android-gray
 * 【注意】 这里只处理，技术灰度需求
 * 【版本灰度】请使用 {$link BaseDataStartGray 、BossGrayResponse}
 * 【跨进程】 需要单独使用SP 存储 获取
 * 【注意】如果要做启动相关得灰度时 请单独存储,处理逻辑
 */
public class AndroidDataStarGray extends BaseDataStartGray<AndroidGrayResponse> {

    private static final String BOSS_ANDROID_GRAY = "6395C7A1DBAC477CBAA905F00C209D8A";
    private static final String CACHE_KEY_NETWORK_RETRY = "networkRetry";
    private static final String CACHE_KEY_CONTACT_UPDATE_GRAY = "contact_update_gray";
    private static final String CACHE_KEY_CONTACT_UPDATE_GRAY_V3 = "contact_update_gray_3";
    private static final String CACHE_KEY_MQTT_MONITOR = "mqtt_monitor";
    private static final String CACHE_KEY_MQTT_SEND_MONITOR = "mqtt_send_monitor";
    private static final String CACHE_KEY_MQTT_PROTOCOL = "mqtt_connect_protocol";
    private static final String CACHE_KEY_MQTT_FIX_NET_CHANGE = "mqtt_fix_net_change";
    private static final String CACHE_KEY_MQTT_SSL_MAX_RETRY_COUNT = "mqtt_ssl_retry_count";
    private static final String CACHE_KEY_MQTT_SSL_IGNORE_BJ = "mqtt_ssl_ignore_bj";
    private static final String CACHE_KEY_MQTT_SUPPORT_IPV6_CONNECT = "mqtt_support_ipv6_connect";
    private static final String CACHE_KEY_MQTT_DNS = "mqtt_dns";
    private static final String CACHE_KEY_MQTT_SUPPORT_IPV6_CONNECT_SPLIT = "mqtt_support_ipv6_connect_split";
    private static final String CACHE_KEY_MQTT_CONNECT_CONFIG = "mqtt_connect_config";
    private static final String CACHE_KEY_FIX_DARK_THEME = "fix_dark_theme";
    private static final String CACHE_KEY_SEND_MSG_UPDATE_MID = "send_msg_update_mid";
    private static final String CACHE_KEY_MSG_HANDLE_CONTACT = "monitor_msg_handle_contact";
    private static final String CACHE_KEY_MSG_HANDLE_CONTACT_V2 = "monitor_msg_handle_contact_v2";
    private static final String CACHE_KEY_CONTACT_DELAY_FIX_DATA = "contact_delay_fix_data";
    private static final String CACHE_KEY_SDK_MAP_GRAY_V2 = "sdk_map_grayV2";
    private static final String CACHE_KEY_AUDIO_FIX_GRAY = "audio_sdk_fix";
    private static final String CACHE_KEY_ANALYTICS_STRATEGY = "analytics_strategy";
    private static final String CACHE_KEY_USER_INFO_REFRESH_STRATEGY = "user_info_refresh_strategy";
    private static final String CACHE_KEY_CHAT_LOAD_MORE_TYPE = "chat_load_more_type";
    private static final String CACHE_KEY_FRESCO_OKHTTP_DNS = "fresco_okhttp_dns";
    private static final String CACHE_KEY_AUTO_UPGRADE_MARKET = "auto_upgrade_market";
    private static final String CACHE_KEY_CONTACT_F2_ASYNC_DIFF = "contact_f2_async_diff";
    private static final String CACHE_KEY_CONTACT_UPDATE_OPTIMIZE = "contact_update_optimize";
    private static final String CACHE_KEY_FIX_SAVE_FAIL_MSG = "fix_save_fail_msg";
    private static final String CACHE_KEY_FIX_PROCESS_LOST_MSG = "fix_process_lost_msg";
    private static final String CACHE_KEY_FIX_MAP_TYPE = "fix_map_type";

    private static final String CACHE_KEY_DNS_TEMP_CACHE_TIME = "dns_temp_cache_time";

    @Deprecated
    String CHECK_CONTACT_LAST_CHAT_TEXT_EMPTY = "checkContactLastChatTextEmpty";
    private static final String CHECK_F2_CHAT_CONTACT_TEXT_EMPTY = "checkF2ChatContactTextEmpty";

    private static final AndroidDataStarGray instance = new AndroidDataStarGray();

    public static AndroidDataStarGray getInstance() {
        return instance;
    }

    @Override
    String getSystem() {
        return BOSS_ANDROID_GRAY;
    }

    @Override
    protected void handleData(AndroidGrayResponse data) {
        super.handleData(data);
        // 特殊存储、因为初始化早，单独存储
        if (data.net_retry != null) {
            SpManager.get().global(FILE_NAME).edit().putBoolean(CACHE_KEY_NETWORK_RETRY, data.net_retry.result).apply();
        }
        // 特殊存储、因为初始化早，单独存储
        if (data.default_theme != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_FIX_DARK_THEME, data.default_theme.result).apply();
        }
        // 跨进程 单独存储
        if (data.mqtt_protocol != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_MQTT_PROTOCOL, data.mqtt_protocol.result).apply();
        }
        // 跨进程 单独存储
        if (data.mqtt_fix_net_change != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_MQTT_FIX_NET_CHANGE, data.mqtt_fix_net_change.result).apply();
        }
        //MQTT 连接SSL重试次数
        if (data.mqtt_ssl_retry_count != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_MQTT_SSL_MAX_RETRY_COUNT, data.mqtt_ssl_retry_count.result).apply();
        }
        //MQTT 连接SSL 后台忽略计数 降级
        if (data.mqtt_ssl_ignore_bj != null) {
            SpManager.get().global(FILE_NAME).edit().putBoolean(CACHE_KEY_MQTT_SSL_IGNORE_BJ, data.mqtt_ssl_ignore_bj.result).apply();
        }
        //MQTT 支持IpV6连接
        if (data.mqtt_support_ipv6_connect != null) {
            SpManager.get().global(FILE_NAME).edit().putBoolean(CACHE_KEY_MQTT_SUPPORT_IPV6_CONNECT, data.mqtt_support_ipv6_connect.result).apply();
        }
        //MQTT DNS优化
        if (data.mqtt_dns != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_MQTT_DNS, data.mqtt_dns.result).apply();
        }
        //MQTT 配置
        if (data.mqtt_connect_config != null) {
            SpManager.get().global(FILE_NAME).edit().putString(CACHE_KEY_MQTT_CONNECT_CONFIG, data.mqtt_connect_config.result).apply();
        }

        if (data.monitor_msg_handle_contact_v2 != null) {
            SpManager.get().user(FILE_NAME).edit().putBoolean(CACHE_KEY_MSG_HANDLE_CONTACT_V2, data.monitor_msg_handle_contact_v2.result).apply();
        }

        if (data.audio_sdk_fix != null) {
            SpManager.get().user(FILE_NAME).edit().putBoolean(CACHE_KEY_AUDIO_FIX_GRAY, data.audio_sdk_fix.result).apply();
        }
        // fresco okhttp dns 兜底读取
        if (data.fresco_okhttp_dns != null) {
            SpManager.get().user(FILE_NAME).edit().putInt(CACHE_KEY_FRESCO_OKHTTP_DNS, data.fresco_okhttp_dns.result).apply();
        }

        // auto_upgrade_market
        if (data.auto_upgrade_market != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_AUTO_UPGRADE_MARKET, data.auto_upgrade_market.result).apply();
        }

        if (data.fixMsgSaveFail != null) {
            SpManager.get().global(FILE_NAME).edit().putInt(CACHE_KEY_FIX_SAVE_FAIL_MSG, data.fixMsgSaveFail.result).apply();
        }

        if (data.fix_lost_message != null) {
            SpManager.get().global(FILE_NAME).edit().putBoolean(CACHE_KEY_FIX_PROCESS_LOST_MSG, data.fix_lost_message.result).apply();
        }

        if (data.dnsTempCacheTime != null) {
            SpManager.get().global(FILE_NAME).edit().putLong(CACHE_KEY_DNS_TEMP_CACHE_TIME, data.dnsTempCacheTime.result).apply();
        }

        //线上容灾-安全气垫
        handleCrashProtectResult(data);

    }

    private void handleCrashProtectResult(AndroidGrayResponse data) {
        //灰度开关
        if (null != data.android_crash_protect_gray && null != data.android_crash_protect_gray.result) {
            boolean isSwitchOpen = data.android_crash_protect_gray.result == 1;

            AppThreadFactory.createThread(() -> {
                BZLCrashProtectGrayHelper.syncGXTGrayEnabled(isSwitchOpen ? "true" : "false");

                //安全气垫配置信息
                if (isSwitchOpen && null != data.crash_protect_gxt_config && null != data.crash_protect_gxt_config.result) {
                    BZLCrashProtectManager.updateConfig(data.crash_protect_gxt_config.result);
                }
            }).start();
        }
    }

    /**
     * 联系人更新策略V3
     */
    public boolean contactRefreshV3() {
        return getCache(CACHE_KEY_CONTACT_UPDATE_GRAY_V3, () -> {
            if (response != null && response.contact_update_gray_v3 != null) {
                return response.contact_update_gray_v3.result == 1;
            }
            return false;
        });
    }


    /**
     * okhttp 是否重试策略
     * 这个特殊处理， okhttp 初始化较早，直接读SP
     */
    public boolean networkRetry() {
        return getCache(CACHE_KEY_NETWORK_RETRY, () -> {
            return SpManager.get().global(FILE_NAME).getBoolean(CACHE_KEY_NETWORK_RETRY, true); //默认true
        });
    }

    /**
     *
     */
    public boolean dbAutoRepair() {
        if (response != null && response.db_auto_repair != null) {
            return response.db_auto_repair.result;
        }
        return true;
    }

    /**
     * mqtt连接失败埋点上报灰度
     */
    public int mqttMonitor() {
        return getCache(CACHE_KEY_MQTT_MONITOR, () -> {
            if (response != null && response.mqtt_monitor != null) {
                return response.mqtt_monitor.result;
            }
            return BuildInfoUtils.isDebug() ? 1 : 0;
        });
    }

    public int startupFixFirstLauncher() {
        if (response != null && response.fixLauncher != null) {
            return response.fixLauncher.result;
        }
        return BuildInfoUtils.isDebug() ? 1 : 0;
    }


    public int minimumStorage() {
        if (response != null && response.minimum_storage != null) {
            return response.minimum_storage.result;
        }
        return 20;
    }

    public int fixDarkTheme() {
        if (response != null && response.fix_dark_theme != null) {
            return response.fix_dark_theme.result;
        }
        return 0;
    }

    public static int defaultTheme() {
//        AppCompatDelegate.MODE_NIGHT_NO  1
//        AppCompatDelegate.MODE_NIGHT_YES  2
//        AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM  -1
        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_FIX_DARK_THEME, BuildInfoUtils.isDebug() ? -1 : 1);
    }

    public boolean fixContactCache() {
        return getCache(CACHE_KEY_CONTACT_UPDATE_GRAY, () -> {
            if (response != null && response.fix_contact_cache != null) {
                return response.fix_contact_cache.result > 1;
            }
            return BuildInfoUtils.isDebug();
        });
    }

    /**
     * mqtt发送健康埋点上报灰度
     */
    public int mqttSendMonitor() {
        return getCache(CACHE_KEY_MQTT_SEND_MONITOR, () -> {
            if (response != null && response.mqtt_send_monitor != null) {
                return response.mqtt_send_monitor.result;
            }
            return BuildInfoUtils.isDebug() ? 1 : 0;
        });
    }


    public int getCheckChatContactTextEmptySwitchStatus() {
        return getCache(CHECK_F2_CHAT_CONTACT_TEXT_EMPTY, () -> {
            if (response != null && response.checkF2ChatContactTextEmpty != null) {
                return response.checkF2ChatContactTextEmpty.result;
            }
            return 0;
        });
    }


    public int getSyncPCReadSwitchStatus() {
        if (response != null && response.SYNC_PC_READ_SWITCH != null) {
            return response.SYNC_PC_READ_SWITCH.result;
        }
        return 0;
    }

    public int getSyncPcReadInterruptCount() {
        if (response != null && response.sync_pc_read_interrupt != null) {
            return response.sync_pc_read_interrupt.result;
        }
        return 0;
    }

    public boolean fixSendingMsg() {
        if (response != null && response.fix_sending_msg != null) {
            return response.fix_sending_msg.result;
        }
        return true;
    }

    public boolean fixPullHistoryMsg() {
        if (response != null && response.fix_pull_history_msg != null) {
            return response.fix_pull_history_msg.result > 0;
        }
        return false;
    }

    public boolean fixPullHistoryMsgV2() {
        if (response != null && response.fix_pull_history_msg != null) {
            return response.fix_pull_history_msg.result > 1;
        }
        return false;
    }

    /**
     * mqtt接收处理健康埋点上报灰度
     * 默认 8s
     */
    public int mqttReceiverMonitor() {
        if (response != null && response.mqtt_receiver_monitor != null) {
            return response.mqtt_receiver_monitor.result;
        }
        return BuildInfoUtils.isDebug() ? 8 : 0;
    }

    public boolean defaultRefreshLite() {
        if (response != null && response.default_refresh_lite != null) {
            return response.default_refresh_lite.result > 0;
        }
        return BuildInfoUtils.isDebug();
    }

    // fresco 支持webp
    public boolean isFrescoSupportWebp() {
        if (response != null && response.supportFrescoWebp != null) {
            return response.supportFrescoWebp.result > 0;
        }
        return BuildInfoUtils.isDebug();
    }

    /**
     * @return true 延迟  false 不延迟
     */

    public boolean isForceSsl() {
        return getCache(CACHE_KEY_MQTT_PROTOCOL, () -> SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_MQTT_PROTOCOL, 0) == 1);
    }

    //支持Ipv6 连接
    public boolean isSupportIpv6Connect() {
        return SpManager.get().global(FILE_NAME).getBoolean(CACHE_KEY_MQTT_SUPPORT_IPV6_CONNECT, false);
    }

    public MqttConfigBean getMqttConfig() {
        if (mqttConfigBean == null) {
            String string = SpManager.get().global(FILE_NAME).getString(CACHE_KEY_MQTT_CONNECT_CONFIG, null);
            if (!TextUtils.isEmpty(string)) {
                mqttConfigBean = GsonUtils.fromJson(string, MqttConfigBean.class);
            }
            TLog.debug("AndroidDataStarGray", "getMqttConfig %s", mqttConfigBean);
        }
        if (mqttConfigBean == null) {
            mqttConfigBean = new MqttConfigBean();
        }
        return mqttConfigBean;
    }

    MqttConfigBean mqttConfigBean;

    public boolean fixOldCookie() {
        if (response != null && response.fix_old_cookie != null) {
            return response.fix_old_cookie.result;
        }
        return false;
    }

    public static boolean isFixMqttNetChange() {
        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_MQTT_FIX_NET_CHANGE, 0) > 0;
    }

    public static int getMaxSSLCount() {
        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_MQTT_SSL_MAX_RETRY_COUNT, 0);
    }

    public static boolean getNewMqttDns() {
        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_MQTT_DNS, 0) > 0;
    }

    public static boolean isMqttSSLIgnoreBgConnect() {
        return SpManager.get().global(FILE_NAME).getBoolean(CACHE_KEY_MQTT_SSL_IGNORE_BJ, false);
    }

    /**
     * 前后台出席频次限制时间
     *
     * @return
     */
    public long getMqttPullMinTime() {
        if (response != null && response.mqtt_pull_min_time != null && response.mqtt_pull_min_time.result != null) {
            return response.mqtt_pull_min_time.result;
        }
        return 2 * 60 * 1000;
    }

    /**
     * 单聊 自己发送的消息 是否更新MID
     *
     * @return
     */
    public boolean isSendUpdateMid() {
        return getCache(CACHE_KEY_SEND_MSG_UPDATE_MID, () -> {
            if (response != null && response.send_msg_update_mid != null) {
                return response.send_msg_update_mid.result;
            }
            return true;
        });
    }

    public int getContactHandleTimeOut() {
        if (response != null && response.contact_handle_time_out != null && response.contact_handle_time_out.result != null) {
            return response.contact_handle_time_out.result;
        }
        return 10;
    }

    public long getPushTokenTimeLimit() {
        if (response != null && response.push_token_time_limit != null) {
            return response.push_token_time_limit.result * ONE_HOUR; //配置 > 0  单位：小时
        }
        return 0;
    }

    public long getPushTokenTimeout() {
        if (response != null && response.push_token_time_out != null) {
            return response.push_token_time_out.result;
        }
        return 10;
    }

    public static final long ONE_HOUR = 3600000; // 1小时

    public boolean isFastNewAddFriendWithMsg() {
        if (response != null && response.fast_msg_add_friend != null) {
            return response.fast_msg_add_friend.result;
        }
        return BuildInfoUtils.isDebug();
    }


    public boolean monitorMsgHandleV2() {
        return Boolean.TRUE.equals(getCache(CACHE_KEY_MSG_HANDLE_CONTACT_V2, () -> SpManager.get().user(FILE_NAME).getBoolean(CACHE_KEY_MSG_HANDLE_CONTACT_V2, BuildInfoUtils.isDebug())));
    }

    public boolean contactAllField() {
        if (response != null && response.contact_save_all_field != null) {
            return response.contact_save_all_field.result;
        }
        return isContactDelayFixData();
    }

    public boolean isSyncMsgStatus() {
        if (response != null && response.message_sync_status != null) {
            return response.message_sync_status.result;
        }
        return BuildInfoUtils.isDebug();
    }

    // 0 关闭策略
    // 1 遇SSL异常 切换 HTTPDNS 需重新初始化 fresco
    // 10 初始化时自定义DNS  但是不默认使用httpDNS 使用系统解析
    // 11 初始化时自定义DNS 遇SSL异常切换HTTPDNS
    // 12 初始化时自定义DNS 默认 HTTPDNS  不判断SSL异常
    public int isFrescoOkhttpDns() {
        if (response != null && response.fresco_okhttp_dns != null) {
            return response.fresco_okhttp_dns.result;
        }
        return SpManager.get().user(FILE_NAME).getInt(CACHE_KEY_FRESCO_OKHTTP_DNS, 0);
    }

    /**
     * 群聊页消息刷新延迟时间，默认20毫秒。下发延迟时间小于等于0则不延迟刷新
     *
     * @return
     */
    public int getDelayRefreshGroupChat() {
        if (response != null && response.delay_refresh_group_chat != null) {
            return response.delay_refresh_group_chat.result;
        }

        return 20;
    }

    public int getBatchRetryReadMessageCount() {
        if (response != null && response.batch_retry_send_read_msg_v2 != null) {
            return response.batch_retry_send_read_msg_v2.result;
        }
        return 0;
    }

    public boolean isMessageDelV2() {
        if (response != null && response.message_del_v2 != null) {
            return response.message_del_v2.result;
        }
        return isContactDelayFixData();
    }

    public boolean isPushTokenNoLogin() {
        if (response != null && response.push_token_no_login != null) {
            return response.push_token_no_login.result;
        }
        return BuildInfoUtils.isDebug();
    }

    public boolean isContactDelayFixData() {
        return Boolean.TRUE.equals(getCache(CACHE_KEY_CONTACT_DELAY_FIX_DATA, () -> {
            if (response != null && response.contact_delay_fix_data != null) {
                return response.contact_delay_fix_data.result;
            }
            return BuildInfoUtils.isDebug();
        }));
    }

    public int isContactDelayFixDataDay() {
        if (response != null && response.contact_delay_fix_data_day != null) {
            return response.contact_delay_fix_data_day.result;
        }
        return 0;
    }

    public boolean isContactDelayFixAllBaseInfo() {
        if (response != null && response.contact_delay_fix_base_info != null) {
            return response.contact_delay_fix_base_info.result;
        }
        return false;
    }

    public boolean isContactDelayFixAllNoneRead() {
        if (response != null && response.contact_delay_fix_none_read != null) {
            return response.contact_delay_fix_none_read.result;
        }
        return false;
    }

    public boolean isH5DomainGuardEnable() {
        if (response != null && response.h5DomainGuardEnable != null) {
            return response.h5DomainGuardEnable.result;
        }
        return false;
    }

    // 0 关闭
    // 1 开启 (H5)
    // 2 开启（智慧石 + H5）
    public int isPhotoAddTimeWater() {
        if (response != null && response.photo_add_time_water != null) {
            return response.photo_add_time_water.result;
        }
        return BuildInfoUtils.isDebug() ? 2 : 0;
    }

    public int isContactDelayFixDataStep() {
        if (response != null && response.contact_delay_fix_data_step != null) {
            return response.contact_delay_fix_data_step.result;
        }
        return 100;
    }

    /**
     * 跳转第三方分享时 开启前台服务端 避免被系统kill
     *
     * @return 0 不开启
     * 1 开启webview相关
     * 2 全局开启
     */
    public int getForegroundServiceShare() {
        if (response != null && response.foreground_service_share != null) {
            return response.foreground_service_share.result;
        }
        return 0;
    }

    /**
     * 跳转系统相机 开启前台服务端 避免被系统kill
     *
     * @return 0 不开启
     * 1 开启webview相关
     * 2 全局开启
     */
    public int getForegroundServiceCamera() {
        if (response != null && response.foreground_service_camera != null) {
            return response.foreground_service_camera.result;
        }
        return 0;
    }


    /**
     * 检查webview加载的url
     *
     * @return true 开启
     * false 不开启
     * 默认打开
     */
    public boolean checkWebViewLoadUrl() {
        if (response != null && response.check_webview_load_url != null) {
            return response.check_webview_load_url.result;
        }
        return true;
    }

    public boolean isWebUrlForceHttps() {
        if (response != null && response.web_url_force_https != null) {
            return response.web_url_force_https.result;
        }
        return false;
    }

    public boolean isAudioFixGray() {
        return Boolean.TRUE.equals(getCache(CACHE_KEY_AUDIO_FIX_GRAY, () -> SpManager.get().user(FILE_NAME).getBoolean(CACHE_KEY_AUDIO_FIX_GRAY, BuildInfoUtils.isDebug())));
    }

    //优化任务：删除30天消息延后处理
    public boolean messageDelayDel30Day() {
        if (response != null && response.message_delay_del_30_day != null) {
            return response.message_delay_del_30_day.result;
        }
        return BuildInfoUtils.isDebug();
    }

    @Nullable
    public AnalyticsStrategyBean getAnalyticsStrategy() {
        return getCache(CACHE_KEY_ANALYTICS_STRATEGY, () -> {
            if (response != null && response.bz_statistics_merge_time_gray_android != null && response.bz_statistics_merge_time_gray_android.result != null) {
                return GsonUtils.fromJson(response.bz_statistics_merge_time_gray_android.result, AnalyticsStrategyBean.class);
            }
            return null;
        });
    }

    @Nullable
    public UserInfoRefreshDelayStrategyBean getUserInfoRefreshDelayStrategyBean() {
        return getCache(CACHE_KEY_USER_INFO_REFRESH_STRATEGY, () -> {
            if (response != null && response.userInfoRefreshDelay != null && response.userInfoRefreshDelay.result != null) {
                return GsonUtils.fromJson(response.userInfoRefreshDelay.result, UserInfoRefreshDelayStrategyBean.class);
            }
            return null;
        });
    }

    public boolean webViewSSLErrorCancel() {
        if (response != null && response.webview_ssl_error_cancel != null) {
            return response.webview_ssl_error_cancel.result;
        }
        return true;
    }

    public boolean userInterviewArrangeV2() {
        if (response != null && response.interview_arrange_v2_gray != null) {
            return response.interview_arrange_v2_gray.result == 1;
        }

        return false;
    }


    public boolean openWebViewConsoleLog() {
        if (response != null && response.open_webview_console_log != null) {
            return response.open_webview_console_log.result;
        }
        return false;
    }

    public boolean chatAutoLoadMoreGray() {
        return Boolean.TRUE.equals(getCache(CACHE_KEY_CHAT_LOAD_MORE_TYPE, () -> {
            if (response != null && response.chat_load_more_type != null) {
                return response.chat_load_more_type.result;
            }
            return BuildInfoUtils.isDebug();
        }));
    }


    public boolean isUserNewMap() {
        if (response != null && response.sdk_map_grayV2 != null) {
            return response.sdk_map_grayV2.result > 0;
        }
        return false;
    }

    public boolean isUserAMapType() {
        if (response != null && response.sdk_map_grayV2 != null) {
            return response.sdk_map_grayV2.result == 2;
        }
        TLog.info("dataStarGray", "isUserAMapType default");
        return true;
    }

    public boolean isUserAMapTypeV3ForJobAddress() {
        if (response != null && response.sdkMapGrayJobAddress != null) {
            if (response.sdkMapGrayJobAddress.result == 1) {
                return true;//强制用高德
            } else if (response.sdkMapGrayJobAddress.result == 2) {
                return false;//强制用百度
            }
        }
        TLog.info("dataStarGray", "isUserAMapTypeV3ForJobAddress default");
        return isUserAMapTypeV3();
    }

    public boolean isUserAMapTypeV3ForAuthAddress() {
        if (response != null && response.sdkMapGrayAuthAddress != null) {
            if (response.sdkMapGrayAuthAddress.result == 1) {
                return true;//强制用高德
            } else if (response.sdkMapGrayAuthAddress.result == 2) {
                return false;//强制用百度
            }
        }
        TLog.info("dataStarGray", "isUserAMapTypeV3ForAuthAddress default");
        return isUserAMapTypeV3();
    }

    public boolean isUserAMapTypeV3() {
        return getUserBMapTypeV3() == 0;
    }

    int getUserBMapTypeV3() {
        Integer map_type = getCache(CACHE_KEY_FIX_MAP_TYPE, new Callable<Integer>() {
            @Override
            public Integer call() {
                if (response != null && response.sdk_map_grayV3 != null) {
                    return response.sdk_map_grayV3.result;
                }
                TLog.info("dataStarGray", "isUserAMapTypeV3 default");
                return 0;
            }
        });
        return map_type == null ? 0 : map_type;
    }


    public boolean isUserBMapTypeV3() {
        return getUserBMapTypeV3() == 1;
    }

    public boolean isUserBMapType() {
        if (response != null && response.sdk_map_grayV2 != null) {
            return response.sdk_map_grayV2.result == 1 || response.sdk_map_grayV2.result == 3;
        }
        return false;
    }

    public boolean isUserBMapStyle() {
        if (response != null && response.sdk_bmap_style != null) {
            return response.sdk_bmap_style.result == 1;
        }
        return false;
    }

    // 使用百度地图， 但关键词搜索使用高德
    public boolean isUserBaiduMapAndSearch() {
        if (response != null && response.sdk_map_grayV2 != null) {
            return response.sdk_map_grayV2.result == 3;
        }
        return false;
    }

    /**
     * 高德地图，去除省市搜索词
     */
    public boolean isRemoveAMapProvinceCity() {
        if (response != null && response.amap_poi_remove_province_city != null) {
            return response.amap_poi_remove_province_city.result;
        }

        return false;
    }

    public boolean disableLogDiagnose() {
        if (response != null && response.disableLogDiagnose != null) {
            return response.disableLogDiagnose.result;
        }

        return false;
    }

    public boolean isCityWithSuffix() {
        if (response != null && response.cityIsWithSuffix != null) {
            return response.cityIsWithSuffix.result;
        }

        return true;
    }

    public boolean judgeF1BusinessByNet() {
        if (response != null && response.f1BusinessJudgeByNet != null) {
            return response.f1BusinessJudgeByNet.result;
        }
        return true;
    }

    public int getBMapWalkLimit() {
        if (response != null && response.bmap_walk_dis_limit != null) {
            return response.bmap_walk_dis_limit.result;
        }
        return 100;
    }

    /**
     * webview 秒开开关
     *
     * @return true 开启
     * false 关闭
     */
    public boolean webViewInSecond() {
        if (response != null && response.webview_in_second != null) {
            return response.webview_in_second.result;
        }
        return true;
    }


    public boolean webViewInSecondBlank() {
        if (response != null && response.webview_in_second_blank != null) {
            return response.webview_in_second_blank.result;
        }
        return false;
    }


    public int webViewInSecondMem() {
        if (response != null && response.webview_in_second_mem != null) {
            return response.webview_in_second_mem.result;
        }
        return 1;

    }

    public boolean configInternalDomainSupport() {
        if (response != null && response.internalDomainSupport != null) {
            return response.internalDomainSupport.result == 1;
        }
        return true;
    }


    public boolean isIgnoreCompanyUpdate() {
        if (response != null && response.ignore_company_update != null) {
            return response.ignore_company_update.result == 1;
        }
        return false;
    }

    public boolean safeDynamicAntiControl() {
        if (response != null && response.safe_dynamic_anti_control != null) {
            return response.safe_dynamic_anti_control.result;
        }
        return false;
    }

    public boolean isShowGPSLoad() {
        if (response != null && response.android_gsp_close_load != null) {
            return response.android_gsp_close_load.result == 1;
        }
        return false;
    }

    public boolean isShowGPSLoadV2() {
        if (response != null && response.android_gsp_close_load != null) {
            return response.android_gsp_close_load.result == 2;
        }
        return false;
    }


    public boolean isIgnoreBattery() {
        if (response != null && response.exp_autogen_subject_12629_number_1 != null) {
            return response.exp_autogen_subject_12629_number_1.result > 0;
        }
        return false;
    }

    MapConfigBean mapConfigBean;

    public MapConfigBean getMapConfig() {
        if (mapConfigBean == null) {
            if (response != null && response.sdk_amap_config != null) {
                mapConfigBean = GsonUtils.fromJson(response.sdk_amap_config.result, MapConfigBean.class);
                TLog.debug("AndroidDataStarGray", "getAMapConfig %s", mapConfigBean);
            }
        }
        if (mapConfigBean == null) {
            mapConfigBean = new MapConfigBean();
        }
        return mapConfigBean;
    }


    public boolean isWebViewErrorHostReplace() {
        if (response != null && response.webViewErrorHostReplace != null) {
            return response.webViewErrorHostReplace.result;
        }
        return true;
    }

    public boolean isDisableScreenShot() {
        if (response != null && response.disable_screenshot_dialog != null) {
            return response.disable_screenshot_dialog.result;
        }
        return false;
    }

    public int getSyncReadPageSize() {
        if (response != null && response.sync_read_page_size != null) {
            return response.sync_read_page_size.result;
        }
        return 3000;
    }

    public boolean closeFixMsgSaveFail() {
        if (response != null && response.fixMsgSaveFail != null) {
            return response.fixMsgSaveFail.result == 0;
        }

        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_FIX_SAVE_FAIL_MSG, 0) == 0;
    }

    /**
     * 是否开启消息丢失修复功能
     */
    public boolean isFixLostMessage() {
        if (response != null && response.fix_lost_message != null) {
            return response.fix_lost_message.result;
        }
        return SpManager.get().global(FILE_NAME).getBoolean(CACHE_KEY_FIX_PROCESS_LOST_MSG, false);
    }

    public boolean isIgnoreDiffIdentityMessage() {
        if (response != null && response.ignore_diff_identity_message != null) {
            return response.ignore_diff_identity_message.result > 0;
        }
        return false;
    }

    public boolean isInterceptExternalUrlRequestPermission() {
        if (response != null && response.interceptExternalUrlRequestPermission != null) {
            return response.interceptExternalUrlRequestPermission.result;
        }
        return true;
    }


    /**
     * @return 华为平行视界在BOSS F1中的实验修复
     */
    public boolean isBossF1HuaweiParallelRefreshGray() {
        if (response != null && response.boss_f1_huawei_pad_parallel_refresh != null) {
            return response.boss_f1_huawei_pad_parallel_refresh.result == 1;
        }
        return false;
    }


    public boolean isOpenHeif2Jpg() {
        if (response != null && response.openHeif2Jpg != null) {
            return response.openHeif2Jpg.result;
        }
        return false;
    }

    public int getF4RefreshDelayTime() {
        if (response != null && response.f4RefreshDelayTime != null && response.f4RefreshDelayTime.result != null) {
            return response.f4RefreshDelayTime.result;
        }
        return 0;
    }

    public boolean isContactsRefreshF4Limit() {
        if (response != null && response.contactsRefreshF4Limit != null && response.contactsRefreshF4Limit.result != null) {
            return response.contactsRefreshF4Limit.result;
        }
        return false;
    }

    public boolean isOpenAddressCustomOpt() {
        if (response != null && response.jobAddressCustomEnhance != null) {
            return response.jobAddressCustomEnhance.result;
        }
        return false;
    }

    public boolean isOpenAddressCityAutoFillOpt() {
        if (response != null && response.bossAddressCityAutoFillOpt != null) {
            return response.bossAddressCityAutoFillOpt.result;
        }
        return false;
    }


    public boolean isShowGalleryUserAdd() {
        if (response != null && response.gallery_user_add != null) {
            return response.gallery_user_add.result;
        }
        return true;
    }

    public static boolean isAutoUpgradeApp() {
        return SpManager.get().global(FILE_NAME).getInt(CACHE_KEY_AUTO_UPGRADE_MARKET, 0) > 0;
    }

    public boolean isFixCheckISendMsgToFriend() {
        if (response != null && response.fixCheckISendMsgToFriend != null) {
            return response.fixCheckISendMsgToFriend.result == 0;
        }

        return true;
    }

    public int getContactF2Async() {
        Integer cache = getCache(CACHE_KEY_CONTACT_F2_ASYNC_DIFF, () -> {
            if (response != null && response.contact_f2_async_diff != null) {
                return response.contact_f2_async_diff.result;
            }
            return 0;
        });
        return cache == null ? 0 : cache;
    }

    public boolean isContactF2Async() {
        return getContactF2Async() % 10 == 1;
    }

    public boolean isContactF2AsyncDiff() {
        return getContactF2Async() / 10 == 1;
    }

    /**
     * 获取联系人刷新操作的节流时间(毫秒)
     */
    public long getContactRefreshThrottleTime() {
        if (response != null && response.contact_refresh_throttle_time != null) {
            return response.contact_refresh_throttle_time.result;
        }
        return 300;
    }

    /**
     * B端进入聊天定位时间
     */
    public long getChatLocationThrottleTime() {
        if (response != null && response.chat_location_throttle_time != null) {
            return response.chat_location_throttle_time.result;
        }
        return 10;
    }

    /**
     * 获取联系人异步Diff 触发条件
     *
     * @return
     */
    public long getContactAsyncLimitCount() {
        if (response != null && response.contact_refresh_async_count != null) {
            return response.contact_refresh_async_count.result;
        }
        return 20;
    }


    public int fixPageReloadMessage() {
        if (response != null && response.fix_page_reload_message != null) {
            return response.fix_page_reload_message.result;
        }
        return 1;
    }

    public int getPhotoPickerType() {
        if (response != null && response.photo_picker_type != null) {
            return response.photo_picker_type.result;
        }
        return 0;
    }

    public boolean isNeedBaiduMap() {
        if (isUserBMapType()) {
            return true;
        }
        if (isUserBMapTypeV3()) {
            return true;
        }
        if (response != null) {
            if (response.geek_add_address != null && response.geek_add_address.result == 2) {
                return true;//强制用百度
            }
            if (response.sdkMapGrayAuthAddress != null && response.sdkMapGrayAuthAddress.result == 2) {
                return true;//强制用百度
            }
            if (response.sdkMapGrayJobAddress != null && response.sdkMapGrayJobAddress.result == 2) {
                return true;//强制用百度
            }
        }
        return false;
    }

    /**
     * int SDK_TYPE_AMAP = 0;      // 高德
     * int SDK_TYPE_BAIDU = 1;     // 百度
     *
     * @return
     */
    public int getLocationType() {
        if (response != null && response.location_type != null) {
            return response.location_type.result;
        }
        return ILocationProvider.SDK_TYPE_AMAP;
    }

    public int getF1MapFindJobLocationType() {
        if (response != null && response.F1_map_find_job != null) {
            return response.F1_map_find_job.result;
        }
        return 0;
    }

    public boolean openContactUpdateOptimize() {
        Boolean cache = getCache(CACHE_KEY_CONTACT_UPDATE_OPTIMIZE, () -> {
            if (response != null && response.contactUpdateOptimize != null) {
                return response.contactUpdateOptimize.result == 1;
            }
            return false;
        });
        return cache != null && cache;
    }

    public boolean isUploadWifiInfo() {
        if (response != null && response.upload_wifi_info != null) {
            return response.upload_wifi_info.result;
        }
        return false;
    }


    public boolean isUseNewGeekAddAddressType() {
        if (response != null && response.geek_add_address != null) {
            if (response.geek_add_address.result == 1) {
                return true;//强制用高德
            } else if (response.geek_add_address.result == 2) {
                return false;//强制用百度
            }
        }
        TLog.info("dataStarGray", "isUserAMapTypeV3ForJobAddress default");
        return isUserAMapTypeV3();
    }

    public boolean isNoUseGeekPoiTitleSearch() {
        if (response != null && response.geek_no_use_poi_title_search != null) {
            return response.geek_no_use_poi_title_search.result == 1;
        }
        TLog.info("dataStarGray", "isUserAMapTypeV3ForJobAddress default");
        return false;
    }

    public long getDnsTempCacheTime() {
        Long time = getCache(CACHE_KEY_DNS_TEMP_CACHE_TIME, new Callable<Long>() {
            @Override
            public Long call() {
                if (response != null && response.dnsTempCacheTime != null) {
                    return response.dnsTempCacheTime.result;
                }
                return SpManager.get().global(FILE_NAME).getLong(CACHE_KEY_DNS_TEMP_CACHE_TIME, 0);
            }
        });
        return time != null && time > 0 ? time : 60 * 1000;
    }
}
