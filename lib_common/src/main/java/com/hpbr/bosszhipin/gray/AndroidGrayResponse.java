package com.hpbr.bosszhipin.gray;

import com.google.gson.annotations.SerializedName;

import net.bosszhipin.base.HttpResponse;

/**
 * 【注意】 这里只处理，技术灰度需求
 * 【版本灰度】请使用 {$link BaseDataStartGray 、BossGrayResponse}
 */
public class AndroidGrayResponse extends HttpResponse {
    private static final long serialVersionUID = -5705195216263466956L;

    public DataResult<Integer> contact_update_gray_v3;
    public DataResult<Integer> fix_contact_cache;
    public DataResult<Boolean> net_retry;
    /*getBaseInfoList批量处理逻辑灰度*/
    public DataResult<Boolean> chat_gray_getBaseInfoList;
    public DataResult<Boolean> db_auto_repair;
    public DataResult<Integer> mqtt_monitor;
    public DataResult<Integer> mqtt_dns;
    public DataResult<Integer> mqtt_send_monitor;
    public DataResult<Integer> mqtt_receiver_monitor;
    public DataResult<Boolean> mqtt_support_ipv6_connect;

    public DataResult<Integer> minimum_storage;
    public DataResult<Boolean> fix_sending_msg;
    @Deprecated
    public DataResult<Integer> checkContactLastChatTextEmpty;
    public DataResult<Integer> checkF2ChatContactTextEmpty;
    public DataResult<Integer> SYNC_PC_READ_SWITCH;
    public DataResult<Integer> sync_pc_read_interrupt;
    public DataResult<Integer> default_refresh_lite;
    public DataResult<Integer> mqtt_protocol;
    public DataResult<Integer> mqtt_fix_net_change;
    public DataResult<Integer> mqtt_ssl_retry_count;
    public DataResult<Boolean> mqtt_ssl_ignore_bj;
    public DataResult<Integer> fixLauncher;
    public DataResult<Integer> fix_pull_history_msg;
    public DataResult<Integer> exp_autogen_subject_11802_number_1;
    public DataResult<Boolean> fix_old_cookie;
    public DataResult<Integer> fix_dark_theme;
    public DataResult<Integer> default_theme;
    public DataResult<Integer> supportFrescoWebp;
    public DataResult<Long> mqtt_pull_min_time;
    public DataResult<Integer> contact_handle_time_out;
    public DataResult<Integer> push_token_time_limit;
    public DataResult<Integer> push_token_time_out;
    public DataResult<Boolean> fast_msg_add_friend;
    public DataResult<Boolean> async_task_use_thread_pool;
    public DataResult<Integer> f2_sync_none_read_count;
    public DataResult<Boolean> send_msg_update_mid;
    public DataResult<Boolean> monitor_msg_handle_contact;
    public DataResult<Boolean> monitor_msg_handle_contact_v2;
    public DataResult<Boolean> contact_save_all_field;
    public DataResult<Boolean> message_sync_status;
    // 群聊页刷新聊天消息的延迟时间，单位毫秒
    public DataResult<Integer> delay_refresh_group_chat;
    public DataResult<Boolean> message_del_v2;
    public DataResult<Boolean> batch_retry_send_read_msg;
    public DataResult<Integer> batch_retry_send_read_msg_v2;
    public DataResult<Boolean> contact_delay_fix_data;
    public DataResult<Integer> contact_delay_fix_data_day;
    public DataResult<Boolean> chat_load_more_type;
    public DataResult<Boolean> contact_delay_fix_base_info;
    public DataResult<Boolean> contact_delay_fix_none_read;
    public DataResult<Integer> contact_delay_fix_data_step;
    public DataResult<Boolean> push_token_no_login;
    public DataResult<Boolean> message_delay_del_30_day;
    public DataResult<Boolean> webview_ssl_error_cancel;
    public DataResult<Boolean> web_url_force_https;

    //打开相机 、分享被系统强杀
    public DataResult<Integer> foreground_service_share;
    public DataResult<Integer> foreground_service_camera;

    //语音sdk修复灰度
    public DataResult<Boolean> audio_sdk_fix;
    public DataResult<String> bz_statistics_merge_time_gray_android;
    public DataResult<Boolean> check_webview_load_url;
    public DataResult<Integer> sdk_map_grayV2;
    public DataResult<Integer> sdk_map_grayV3;
    @SerializedName("sdk_map_gray_job_address")
    public DataResult<Integer> sdkMapGrayJobAddress;
    @SerializedName("sdk_map_gray_auth_address")
    public DataResult<Integer> sdkMapGrayAuthAddress;
    public DataResult<Integer> sdk_bmap_style;
    public DataResult<Integer> bmap_walk_dis_limit;
    public DataResult<Boolean> amap_poi_remove_province_city;
    @SerializedName("user_info_refresh_delay")
    public DataResult<String> userInfoRefreshDelay;
    @SerializedName("android_h5_domain_replace")
    public DataResult<Boolean> h5DomainGuardEnable;
    @SerializedName("photo_add_time_water")
    public DataResult<Integer> photo_add_time_water;

    // 新版面试管理灰度开关
    public DataResult<Integer> interview_arrange_v2_gray;
    //是否开启console日志
    public DataResult<Boolean> open_webview_console_log;
    // 1221 日志诊断功能开关
    public DataResult<Boolean> disableLogDiagnose;
    public DataResult<Boolean> cityIsWithSuffix;
    public DataResult<Boolean> f1BusinessJudgeByNet;
    public DataResult<Boolean> webview_in_second;

    //秒开
    public DataResult<Boolean> webview_in_second_blank;
    //秒开 内存限制
    public DataResult<Integer> webview_in_second_mem;
    //是否支持配置白名单
    @SerializedName("internal_domain_support")
    public DataResult<Integer> internalDomainSupport;

    public DataResult<Integer> ignore_company_update;

    public DataResult<Boolean> safe_dynamic_anti_control;
    public DataResult<String> mqtt_connect_config;
    public DataResult<Integer> exp_autogen_subject_12629_number_1;

    public DataResult<Integer> android_crash_protect_gray;
    public DataResult<Integer> android_gsp_close_load;
    public DataResult<String> crash_protect_gxt_config;
    public DataResult<String> sdk_amap_config;
    @SerializedName("webview_error_host_replace")
    public DataResult<Boolean> webViewErrorHostReplace;
    public DataResult<Integer> fresco_okhttp_dns;
    public DataResult<Boolean> disable_screenshot_dialog;
    public DataResult<Integer> sync_read_page_size;
    public DataResult<Integer> fixMsgSaveFail;
    public DataResult<Integer> ignore_diff_identity_message;
    public DataResult<Integer> auto_upgrade_market;
    @SerializedName("intercept_external_url_request_permission")
    public DataResult<Boolean> interceptExternalUrlRequestPermission;
    @SerializedName("f4_refresh_delay_time")
    public DataResult<Integer> f4RefreshDelayTime;
    @SerializedName("contacts_refresh_f4_limit")
    public DataResult<Boolean> contactsRefreshF4Limit;
    @SerializedName("job_address_custom_enhance")
    public DataResult<Boolean> jobAddressCustomEnhance;
    @SerializedName("boss_address_city_auto_fill_opt")
    public DataResult<Boolean> bossAddressCityAutoFillOpt;
    public DataResult<Integer> contact_f2_async_diff;
    public DataResult<Long> contact_refresh_throttle_time;
    public DataResult<Long> chat_location_throttle_time;
    public DataResult<Long> contact_refresh_async_count;

    public DataResult<Boolean> gallery_user_add;

    public DataResult<Integer> fix_page_reload_message;
    public DataResult<Integer> location_type;


    public DataResult<Integer> F1_map_find_job;

    public DataResult<Integer> geek_add_address;
    public DataResult<Integer> geek_no_use_poi_title_search;


    @SerializedName("gallery_picture_select_gray")
    public DataResult<Integer> photo_picker_type;

    /**
     * 消息丢失修复功能灰度
     */
    public DataResult<Boolean> fix_lost_message;
    /**
     * 图片格式为heif的转换为jpg
     * */
    @SerializedName("open_heif_2_jpg")
    public DataResult<Boolean> openHeif2Jpg;

    /**
     * 华为平行视界在BOSS F1中的实验修复
     */
    public DataResult<Integer> boss_f1_huawei_pad_parallel_refresh;

    /**
     * 修复双聊关系灰度开关
     */
    public DataResult<Integer> fixCheckISendMsgToFriend;

    /*联系人列表刷新优化*/
    public DataResult<Integer> contactUpdateOptimize;

    public DataResult<Long> dnsTempCacheTime;

    public DataResult<Boolean> upload_wifi_info;

}
