package com.hpbr.bosszhipin.gray;


import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.BuildInfoUtils;

/**
 * https://datastar.kanzhun-inc.com/sampling/sampling/sdk-list
 * zhipin-boss-client
 */
public class DataStarGray extends BaseDataStartGray<BossGrayResponse> {
    public static final String BOSS_GRAY = "62C6F0E417A643BEA0492A57DB97CE5D";
    private static final String SHOW_GET_VIDEO_JD_NEW_STYLE = "show_get_videojd_new_style";
    private static final String BOSS_CONTACT_MULTI_FILTER = "boss_contact_multi_filter";
    private static final String GEEK_CONTACT_ALL_SORT = "geek_contact_all_sort";

    private static final String SHOW_F1_PUSH_GUIDE_AB_TEST = "show_f1_push_guide_ab_test";
    private static final String SHOW_B_F1_PUSH_GUIDE_AB_TEST = "show_b_f1_push_guide_ab_test";
    private static final String F1_PUSH_GUIDE_CLICK_AB_TEST = "f1_push_guide_click_ab_test";
    private static final String B_F1_PUSH_GUIDE_CLICK_AB_TEST = "b_f1_push_guide_click_ab_test";
    private static final String EXP_AUTOGEN_SUBJECT_13017_NUMBER_1 = "exp_autogen_subject_13017_number_1";
    private static final String EXP_AUTOGEN_SUBJECT_13261_NUMBER_1 = "exp_autogen_subject_13261_number_1";
    private static final String EXP_AUTOGEN_SUBJECT_13016_NUMBER_1 = "exp_autogen_subject_13016_number_1";
    private static final String EXP_AUTOGEN_SUBJECT_13097_NUMBER_1 = "exp_autogen_subject_13097_number_1";
    private static final String EXP_AUTOGEN_SUBJECT_13205_NUMBER_1 = "exp_autogen_subject_13205_number_1";
    private static final String OPEN_V_UGC_EDITOR = "open_v_ugc_editor";
    public static final String KEY_B_PUSH_MQTT_CHANNEL = "b_push_mqtt_channel";
    public static final String KEY_C_push_mqtt_channel = "c_push_mqtt_channel";


    private static final DataStarGray instance = new DataStarGray();

    public static DataStarGray getInstance() {
        return instance;
    }

    @Override
    String getSystem() {
        return BOSS_GRAY;
    }

    @Override
    protected void handleData(BossGrayResponse data) {
        super.handleData(data);
        if (data.c_push_mqtt_channel != null) {
            SpManager.get().user(FILE_NAME).edit().putInt(KEY_C_push_mqtt_channel, data.c_push_mqtt_channel.result).apply();
        }
        if (data.b_push_mqtt_channel != null) {
            SpManager.get().user(FILE_NAME).edit().putInt(KEY_B_PUSH_MQTT_CHANNEL, data.b_push_mqtt_channel.result).apply();
        }
    }
    /**
     * <a href="https://datastar.kanzhun-inc.com/sampling/sampling/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=get_boss_show_publish_guide&mode=detail&tabEnv=TEST">
     * 社区boss发布器引导气泡</a>
     */
    public boolean isBossShowPublishGuide() {
        if (response != null && response.get_boss_show_publish_guide != null) {
            return response.get_boss_show_publish_guide.result;
        }
        return false;
    }



    /**
     * <a href="https://datastar.kanzhun-inc.com/sampling/sampling/publishing-detail?projectKey=xgxProjectPreFix_100&toggleKey=boss_tabtwo_name&mode=detail&tabEnv=PROD">
     * BOSSAPP第二tab名称</a>
     */
    public String getF2Name() {
        if (response != null && response.boss_tabtwo_name != null) {
            return response.boss_tabtwo_name.result;
        }
        return "有了";
    }


    /**
     * 是否展示职位视频推荐卡片新样式
     *
     * @return result
     * 0: 对照组
     * 1:实验组
     */
    public boolean isShowGetVideoJDNewStyle() {
        return Boolean.TRUE.equals(getCache(SHOW_GET_VIDEO_JD_NEW_STYLE, () -> {
            if (response != null && response.show_get_videojd_new_style != null) {
                return response.show_get_videojd_new_style.result == 1;
            }
            return false;
        }));
    }

    /**
     * https://zhishu.zhipin.com/wiki/8cdSGMHBb8u
     */
    public boolean getLive1208_713() {
        if (response != null && response.exp_autogen_subject_11715_number_1 != null) {
            return response.exp_autogen_subject_11715_number_1.result == 1;
        }
        return false;
    }

    /**
     * https://zhishu.zhipin.com/wiki/91bgyeFmE1z
     * JD左右滑动引导
     */
    public boolean getJDShowScrollGuide() {
        if (response != null && response.exp_autogen_subject_11881_number_1 != null) {
            return response.exp_autogen_subject_11881_number_1.result == 1;
        }
        return true;
    }


    /**
     * https://zhishu.zhipin.com/wiki/jkravX2e0B6
     * 1214.610【C】引导隐藏简历用户打开简历@芮琦@张群
     */
    public boolean isGuideDirectOpenResume() {
        if (response != null && response.exp_autogen_subject_11936_number_1 != null) {
            return response.exp_autogen_subject_11936_number_1.result == 1;
        }
        return true;
    }

    /**
     * <a href="https://zhishu.zhipin.com/wiki/kpt9YO6fdCy">...</a>
     * 1309.801【C】在线简历编辑与诊断新页面
     */
    public boolean isNewOnlineResume() {
        return response != null && response.newOnlineResumeGray != null && response.newOnlineResumeGray.result == 1;
    }

    public boolean isOpenJobRecommend() {
        if (response != null && response.exp_autogen_subject_11951_number_1 != null) {
            return response.exp_autogen_subject_11951_number_1.result == 1;
        }

        return false;
    }

    /**
     * 1214.605_C端_今日速配人群扩量人群_交互调整优化_燕鑫_参数1_11946
     */
    public boolean getDailyMatch1214Gray() {
        if (response != null && response.exp_autogen_subject_11946_number_1 != null) {
            return response.exp_autogen_subject_11946_number_1.result == 1;
        }
        return BuildInfoUtils.isDebug();
    }


    /**
     * 1218.618【C】蓝领工作经历填写流程优化
     * <a href="https://zhishu.zhipin.com/wiki/oZsmSHwDG73">...</a>
     */
    public boolean isBlueStepCompWorkExperiment() {
        return response != null && response.blueStepCompWorkExperiment != null && response.blueStepCompWorkExperiment.result == 1;
    }

    public boolean isGeekAudioCallTips() {
        if (response != null && response.exp_autogen_subject_12191_number_1 != null) {
            return response.exp_autogen_subject_12191_number_1.result == 1;
        }
        return false;
    }

    public int uploadPicCompress() {
        if (response != null && response.exp_autogen_subject_12239_number_1 != null) {
            return response.exp_autogen_subject_12239_number_1.result;
        }
        return 0;
    }

    public boolean isOpenVoiceToWord() {
        if (response != null && response.voice_to_word != null) {
            return response.voice_to_word.result == 1;
        }
        return false;
    }

    public int getCustomerEvaluateGray() {
        if (response != null && response.exp_autogen_subject_12317_number_1 != null) {
            return response.exp_autogen_subject_12317_number_1.result;
        }
        return 0;
    }

    public boolean isGeekChangeNotice() {
        if (response != null && response.exp_autogen_subject_12419_number_1 != null) {
            return response.exp_autogen_subject_12419_number_1.result == 1 || response.exp_autogen_subject_12419_number_1.result == 2;
        }

        return false;
    }


    public boolean isOpenPositionDraftGrayDialog() {
        if (response != null && response.exp_autogen_subject_12395_number_1 != null) {
            return response.exp_autogen_subject_12395_number_1.result == 1;
        }
        return false;
    }


    public boolean isBossChooseRecommendBrand() {
        if (response != null && response.bossCompleteChooseBrand != null) {
            return response.bossCompleteChooseBrand.result == 1;
        }
        return false;
    }

    public boolean isGeekContactAllSort() {
        return Boolean.TRUE.equals(getCache(GEEK_CONTACT_ALL_SORT, () -> {
            if (response != null && response.exp_autogen_subject_12534_number_1 != null) {
                return response.exp_autogen_subject_12534_number_1.result == 1;
            }
            return false;
        }));
    }

    public boolean isGetNewBossHome() {
        if (response != null && response.getNewBossHome != null) {
            return response.getNewBossHome.result == 1;
        }
        return false;
    }

    public boolean widgetGuideTipSwitch() {
        if (response != null && response.widgetGuideTipSwitch != null) {
            return response.widgetGuideTipSwitch.result;
        }
        return false;
    }

    public int firstPostPositionProcessOptimization() {
        if (response != null && response.firstPostPositionProcessOptimization != null) {
            return response.firstPostPositionProcessOptimization.result;
        }
        return 0;
    }

    public boolean interviewBeautyGray() {
        if (response != null && response.interviewBeautyGray != null) {
            return response.interviewBeautyGray.result == 1;
        }
        return false;
    }

    /**
     * 1303.613_C_多场景引导用户打开push_提升push覆盖率
     * F1 push 引导开启 是否是实验组
     *
     * @return
     */
    public boolean isShowF1NewGuideABTest() {
        return Boolean.TRUE.equals(getCache(SHOW_F1_PUSH_GUIDE_AB_TEST, () -> {
            if (response != null && response.exp_autogen_subject_12737_number_1 != null) {
                return response.exp_autogen_subject_12737_number_1.result > 0;
            }
            return false;
        }));
    }

    public boolean isNewBusinessArea() {
        if (response != null && response.isNewBusinessArea != null) {
            return response.isNewBusinessArea.result == 1;
        }
        return false;
    }

    public int getAiGuideStyle() {
        if (response != null && response.aiGuideStyle != null) {
            return response.aiGuideStyle.result;
        }
        return 0;
    }

    /**
     * 1303.613_C_多场景引导用户打开push_提升push覆盖率
     * F1 push 引导开启 实验组1
     *
     * @return
     */
    public boolean isShowF1NewGuideABTest1() {
        if (response != null && response.exp_autogen_subject_12737_number_1 != null) {
            return response.exp_autogen_subject_12737_number_1.result == 1;
        }
        return false;
    }


    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * B端F1是否走实验组
     *
     * @return
     */
    public boolean isShowBF1NewGuideABTest() {
        return Boolean.TRUE.equals(getCache(SHOW_B_F1_PUSH_GUIDE_AB_TEST, () -> {
            if (response != null && response.exp_autogen_subject_12754_number_1 != null) {
                return response.exp_autogen_subject_12754_number_1.result > 0;
            }
            return false;
        }));
    }


    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * B端F1是否走实验组
     *
     * @return
     */
    public boolean isShowBF1NewGuideABTest1() {
        if (response != null && response.exp_autogen_subject_12754_number_1 != null) {
            return response.exp_autogen_subject_12754_number_1.result == 1;
        }
        return false;
    }


    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * C端F1点击实验
     *
     * @return
     */
    public boolean f1PushGuideClickABTest() {
        return Boolean.TRUE.equals(getCache(F1_PUSH_GUIDE_CLICK_AB_TEST, () -> {
            if (response != null && response.exp_autogen_subject_12777_number_1 != null) {
                return response.exp_autogen_subject_12777_number_1.result > 0;
            }
            return false;
        }));
    }

    /**
     * 1302.615【B&C+admin】F1引导用户开启push弹窗优化@彭贺庭
     * C端F1点击实验
     *
     * @return
     */
    public boolean bF1PushGuideClickABTest() {
        return Boolean.TRUE.equals(getCache(B_F1_PUSH_GUIDE_CLICK_AB_TEST, () -> {
            if (response != null && response.exp_autogen_subject_12776_number_1 != null) {
                return response.exp_autogen_subject_12776_number_1.result > 0;
            }
            return false;
        }));
    }



    public boolean isToBossNewNotifySetPage() {
        return Boolean.TRUE.equals(getCache(EXP_AUTOGEN_SUBJECT_13017_NUMBER_1, () -> {
            if (response != null && response.exp_autogen_subject_13017_number_1 != null) {
                return response.exp_autogen_subject_13017_number_1.result > 0;
            }
            return false;
        })) ;
    }


    public boolean isF2NewJobAddressChangeCity() {
        return Boolean.TRUE.equals(getCache(EXP_AUTOGEN_SUBJECT_13261_NUMBER_1, () -> {
            if (response != null && response.exp_autogen_subject_13261_number_1 != null) {
                return response.exp_autogen_subject_13261_number_1.result > 0;
            }
            return false;
        })) ;
    }

    public boolean isToGeekNewNotifySetPage() {
        return   Boolean.TRUE.equals(getCache(EXP_AUTOGEN_SUBJECT_13016_NUMBER_1, () -> {
            if (response != null && response.exp_autogen_subject_13016_number_1 != null) {
                return response.exp_autogen_subject_13016_number_1.result > 0;
            }
            return false;
        })) ;
    }

    public boolean isShowGeekNewGuideCloseNotification() {
        if (response != null && response.exp_autogen_subject_12737_number_1 != null) {
            return response.exp_autogen_subject_12737_number_1.result > 0;
        }
        return false;
    }

    public boolean isBossFastHandleGray() {
        if (response != null && response.bossFastHandleGray != null) {
            return response.bossFastHandleGray.result > 0;
        }
        return false;
    }


    public boolean isShowBossNewGuideCloseNotification() {
        if (response != null && response.exp_autogen_subject_12754_number_1 != null) {
            return response.exp_autogen_subject_12754_number_1.result > 0;
        }
        return false;
    }


    /**
     * 是否开启v组ugc
     *
     * @return result
     * true:开启
     * false:关闭
     */
    public boolean openVEditor() {
        return Boolean.TRUE.equals(getCache(OPEN_V_UGC_EDITOR, () -> {
            if (response != null && response.openVEditor != null) {
                return response.openVEditor.result;
            }
            return false;
        }));
    }

    public int getContactListCardOptimize() {
        if (response != null && response.exp_autogen_subject_12772_number_1 != null) {
            return response.exp_autogen_subject_12772_number_1.result;
        }

        return 0;
    }

    /**
     * 语音转写按钮灰度实验
     *
     * @return 如果灰度配置为1则返回true, 否则返回false
     */
    public boolean isVoiceTranscriptionButtonEnabled() {
        if (response != null && response.exp_autogen_subject_12800_number_1 != null) {
            return response.exp_autogen_subject_12800_number_1.result == 1;
        }
        return false;
    }

    public int postJobDescBackDialogStyle() {
        if (response != null && response.postJobDescBackDialogStyle != null) {
            return response.postJobDescBackDialogStyle.result;
        }
        return 0;
    }

    public boolean isOpenAgreeExchangeDirectly() {
        if (response != null && response.exp_autogen_subject_12818_number_1 != null) {
            return response.exp_autogen_subject_12818_number_1.result == 1;
        }

        return false;
    }

    public boolean isNewHelloBannerNewStyle() {
        if (response != null && response.exp_autogen_subject_12924_number_1 != null) {
            return response.exp_autogen_subject_12924_number_1.result == 1;
        }
        return false;
    }

    public boolean isWaitReceiveGray() {
        if (response != null && response.exp_autogen_subject_12885_number_1 != null) {
            return response.exp_autogen_subject_12885_number_1.result == 1;
        }

        return false;
    }

    public boolean isOpenPostPositionIntermediarySug() {
        if (response != null && response.postPositionIntermediarySug != null) {
            return response.postPositionIntermediarySug.result == 1;
        }
        return false;
    }


    /**
     * 获取视频简历交换功能开关状态
     * @return 1-开启实验组 0-对照组
     */
    public boolean enableExchangeVideoResumes() {
        if (response != null && response.Exchange_video_resumes != null) {
            return response.Exchange_video_resumes.result == 1;
        }

        return false;
    }

    /**
     * 1306.46_招聘者_营业执照执照不清晰提示
     * @return 1-开启实验组 0-对照组
     */
    public boolean openLicenseDefinition(){
        if (response != null && response.openLicenseDefinition != null) {
            return response.openLicenseDefinition.result == 1;
        }
        return false;
    }

    /**
     * 1306.40_招聘者_APP端职务填写引导_肖舸_张群_参数1_12961
     * @return 0-对照组 1-实验组1 2-实验组2
     */
    public int getBossTitleSuggestGuideGroup() {
        if (response != null && response.bossTitleSuggestGuide != null) {
           return response.bossTitleSuggestGuide.result;
        }
        return 0;
    }

    /**
     * 获取社区问AI顶部图标配置
     * @return
     */
    public String getMomentAskAiTopIconConfig() {
        if (response != null && response.momentAskAiTopIconConfig != null) {
            return response.momentAskAiTopIconConfig.result;
        }
        return null;
    }

    /**
     * 1308.921【C-非版本】消息推荐排序小迭代
     * @return
     */
    public boolean isGeekContactAllSortAndItemSource() {
        if (response != null && response.geekSortItemSource != null) {
            return response.geekSortItemSource.result;
        }
        return false;
    }


    /**
     * 1307.612_push后台发送调用自建通道
     *
     * @return
     */
    public boolean geekPushMqttChannel() {
        if (response != null && response.c_push_mqtt_channel != null) {
            return response.c_push_mqtt_channel.result == 1;
        }

        return SpManager.get().user(FILE_NAME).getInt(KEY_C_push_mqtt_channel, 0) == 1;
    }


    /**
     * 1307.612_push后台发送调用自建通道
     *
     * @return
     */
    public boolean bossPushMqttChannel() {
        if (response != null && response.b_push_mqtt_channel != null) {
            return response.b_push_mqtt_channel.result == 1;
        }
        return SpManager.get().user(FILE_NAME).getInt(KEY_B_PUSH_MQTT_CHANNEL, 0) == 1;
    }

    /**
     * F1是否展示空图的实验
     * @return true-实验组 false-对照组
     */
    public boolean isNoShowF1EmptyImage() {
        return Boolean.TRUE.equals(getCache(EXP_AUTOGEN_SUBJECT_13097_NUMBER_1, () -> {
            if (response != null && response.exp_autogen_subject_13097_number_1 != null) {
                return response.exp_autogen_subject_13097_number_1.result > 0;
            }
            return false;
        }));
    }   /**
     * F1是否展示空图的实验
     * @return true-实验组 false-对照组
     */
    public boolean isJobDetailChangeCity() {
//        return true;
        return Boolean.TRUE.equals(getCache(EXP_AUTOGEN_SUBJECT_13205_NUMBER_1, () -> {
            if (response != null && response.exp_autogen_subject_13205_number_1 != null) {
                return response.exp_autogen_subject_13205_number_1.result > 0;
            }
            return false;
        }));
    }

    public boolean isNewHelloNewStyle() {
        if (response != null && response.newHelloNewStyle != null) {
            return response.newHelloNewStyle.result == 1;
        }
        return false;
    }


    public boolean isInterviewSearchHide(){
        if (response != null && response.interviewSearchHide != null) {
            return response.interviewSearchHide.result == 1;
        }
        return true;
    }


    /**
     * 1310.33【招聘者】F1添加职位按钮灰度策略
     * @return
     */
    public int getF1TopMenuStyleGray() {
        if (response != null && response.f1TopMenuStyleGray != null) {
            return response.f1TopMenuStyleGray.result;
        }
        return 0;
    }

    /**
     * 1310.111【招聘者】仅沟通改为沟通中
     * @return true-实验组 false-对照组
     */
    public boolean isBossContactOnlyContactToCommunicating() {
        if (response != null && response.bossContactOnlyContactToCommunicating != null) {
            return response.bossContactOnlyContactToCommunicating.result == 1;
        }
        return false;
    }

    /**
     * 1310.112【招聘者】未读列表展示
     * @return true-实验组 false-对照组
     */
    public int getBossShowUnreadGray() {
        if (response != null && response.bossShowUnreadListGray != null) {
            return response.bossShowUnreadListGray.result;
        }
        return 0;
    }

    public boolean isPopupWindowReport() {
        if (response != null && response.popupWindowReport != null) {
            return response.popupWindowReport.result == 1;
        }
        return false;
    }

    /**
     * 1311.111【招聘者】批量处理卡片UI优化：获取实验配置
     *
     * @return true-实验组 false-对照组
     */
    public boolean handleGeekNewContactCardGray() {
        if (response != null && response.handleGeekNewChatCard != null) {
            return response.handleGeekNewChatCard.result == 1;
        }
        return false;
    }
}
