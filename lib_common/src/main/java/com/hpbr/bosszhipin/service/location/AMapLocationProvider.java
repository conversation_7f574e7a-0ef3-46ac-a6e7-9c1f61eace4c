package com.hpbr.bosszhipin.service.location;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.ServiceSettings;
import com.amap.api.services.geocoder.GeocodeSearch;
import com.amap.api.services.geocoder.RegeocodeAddress;
import com.amap.api.services.geocoder.RegeocodeQuery;
import com.amap.api.services.geocoder.StreetNumber;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.LBase;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.ApmAnalyticsAction;


/**
 * 高德定位提供者实现
 */
public class AMapLocationProvider implements ILocationProvider {

    private static final String TAG = "AMapLocationProvider";
    private AMapLocationClient client;
    private final OnLocationResultCallback callback;
    private final Context context;
    private long startLocationTime;

    public AMapLocationProvider(Context context, OnLocationResultCallback callback) {
        this.context = context;
        this.callback = callback;
        initAMapClient();
    }

    private void initAMapClient() {
        AMapLocationClient.updatePrivacyShow(context, true, true);
        AMapLocationClient.updatePrivacyAgree(context, true);
        try {
            client = new AMapLocationClient(context);
            AMapLocationClientOption option = new AMapLocationClientOption();
            option.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
            option.setNeedAddress(true);
            option.setOnceLocation(true);
            option.setMockEnable(false);
            option.setInterval(1000 * 60);
            client.setLocationOption(option);
            client.setLocationListener(aMapLocationListener);
        } catch (Exception e) {
            TLog.error(TAG, e, "高德定位客户端初始化失败: ");
        }
    }

    @NonNull
    private final AMapLocationListener aMapLocationListener = new AMapLocationListener() {
        final long loc_time_threshold = AndroidDataStarGray.getInstance().getMapConfig().loc_time_threshold;

        @Override
        public void onLocationChanged(AMapLocation aMapLocation) {
            long totalTime = System.currentTimeMillis() - startLocationTime;
            LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.LOCATION_DATA).put("type", String.valueOf(getType())).put("location", aMapLocation).put("totalTime", totalTime).info();

            if (loc_time_threshold > 0 && totalTime > loc_time_threshold) {
                int errorCode = aMapLocation != null ? aMapLocation.getErrorCode() : -1;
                ApmAnalyzer.create()
                        .action("action_location", "type_location_time")
                        .p2(String.valueOf(errorCode))
                        .p3(String.valueOf(totalTime))
                        .p4(String.valueOf(aMapLocation))
                        .p5(String.valueOf(getType()))
                        .p6(client != null ? client.getVersion() : "")
                        .report();
            }

            if (aMapLocation == null || aMapLocation.getErrorCode() != 0) {
                String errorMsg = "高德定位失败";
                int errorCode = 0;
                if (aMapLocation != null) {
                    errorCode = aMapLocation.getErrorCode();
                    errorMsg = "高德定位失败，错误码: " + errorCode;
                }

                TLog.error(TAG, "定位失败: " + errorMsg + ", errorCode: " + errorCode);

                ApmAnalyzer.create()
                        .action("action_location_fail")
                        .p2("errorcode:" + errorCode)
                        .p3(errorMsg)
                        .p4(String.valueOf(LocationPermissionHelper.isOpenGpsService()))
                        .p5(String.valueOf(getType()))
                        .report();


                if (callback != null) {
                    callback.onLocationResult(false, null);
                }
                return;
            }

            // 转换高德定位结果为通用LocationBean
            LocationService.LocationBean locationBean = convertAMapLocation(aMapLocation);

            if (AndroidDataStarGray.getInstance().getMapConfig().is_fix_location_city) {
                asyncTransCityData(locationBean);
            } else {
                transCityData(locationBean);
                if (callback != null) {
                    callback.onLocationResult(true, locationBean);
                }
            }


            // 检查是否为模拟定位
            if (aMapLocation.isFromMockProvider()) {
                ApmAnalyzer.create(true)
                        .action(ApmAnalyticsAction.ACTION_CHEAT, "type_fake_location")
                        .p2(aMapLocation.toStr())
                        .p5(String.valueOf(getType()))
                        .report();
            }
        }
    };

    private void asyncTransCityData(LocationService.LocationBean locationBean) {
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(locationBean.city) || TextUtils.isEmpty(locationBean.cityCode)) {
                    TLog.info(TAG, "GeocodeSearch start");
                    GeocodeSearch geocodeSearch = createGeocodeSearch(Utils.getApp());
                    if (geocodeSearch != null) {
                        LatLonPoint latLonPoint = new LatLonPoint(locationBean.latitude, locationBean.longitude);
                        RegeocodeQuery query = new RegeocodeQuery(latLonPoint, 1000, GeocodeSearch.AMAP);
                        query.setExtensions("all");
                        try {
                            RegeocodeAddress fromLocation = geocodeSearch.getFromLocation(query);
                            if (fromLocation != null) {
                                TLog.info(TAG, "fromLocation = province = %s city = %s cityCode = %s district = %s %s", fromLocation.getProvince(), fromLocation.getCity(), fromLocation.getCityCode(), fromLocation.getDistrict(), fromLocation.getFormatAddress());
                                locationBean.address = fromLocation.getFormatAddress();
                                locationBean.province = fromLocation.getProvince();
                                locationBean.city = fromLocation.getCity();
                                locationBean.cityCode = fromLocation.getCityCode();
                                locationBean.district = fromLocation.getDistrict();
                                StreetNumber streetNumber = fromLocation.getStreetNumber();
                                if (streetNumber != null) {
                                    locationBean.street = streetNumber.getStreet();
                                    locationBean.streetNumber = streetNumber.getNumber();
                                }
                            }
                        } catch (Exception e) {
                            TLog.error(TAG, e, "asyncCityData ");
                        }
                    }
                }
                transCityData(locationBean);

                AppThreadFactory.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (callback != null) {
                            callback.onLocationResult(true, locationBean);
                        }
                    }
                });
            }
        });
    }

    GeocodeSearch createGeocodeSearch(Context context) {
        ServiceSettings.updatePrivacyShow(context, true, true);
        ServiceSettings.updatePrivacyAgree(context, true);
        try {
            return new GeocodeSearch(context);
        } catch (AMapException e) {
            TLog.error(TAG, e, "createGeocodeSearch");
        }
        return null;
    }

    private void transCityData(LocationService.LocationBean locationBean) {
        // 处理城市名称，去掉"市"字
        if (locationBean.city != null && locationBean.city.lastIndexOf("市") > -1) {
            locationBean.city = locationBean.city.substring(0, locationBean.city.lastIndexOf("市"));
        }

        // 【高德特殊逻辑】加载本地库的位置编码
        if (locationBean.city != null && !locationBean.city.isEmpty()) {
            try {
                //兜底处理，高德SDK 关于省直辖县级市的问题 ：https://zhishu.zhipin.com/docs/Hfigka6SC2f
                if (AndroidDataStarGray.getInstance().getMapConfig().is_fix_city_province) {
                    locationBean.localCityCode = LBase.getCityCode(locationBean.city, locationBean.district);
                } else {
                    locationBean.localCityCode = LBase.getCityCode(locationBean.city);
                }
            } catch (Exception e) {
                TLog.error(TAG, e, "获取城市编码失败: ");
            }
        }
    }

    /**
     * 将高德定位结果转换为通用LocationBean
     */
    private LocationService.LocationBean convertAMapLocation(AMapLocation aMapLocation) {
        LocationService.LocationBean locationBean = new LocationService.LocationBean();
        locationBean.radius = aMapLocation.getAccuracy();
        locationBean.latitude = aMapLocation.getLatitude();
        locationBean.longitude = aMapLocation.getLongitude();
        locationBean.poiTitle = aMapLocation.getPoiName();
        locationBean.province = aMapLocation.getProvince();
        locationBean.city = aMapLocation.getCity();
        locationBean.cityCode = aMapLocation.getCityCode();
        locationBean.district = aMapLocation.getDistrict();
        locationBean.street = aMapLocation.getStreet();
        locationBean.streetNumber = aMapLocation.getStreetNum();
        locationBean.address = aMapLocation.getAddress();
        locationBean.sdkType = getType();

        // 检查城市信息完整性
        if (TextUtils.isEmpty(aMapLocation.getCityCode()) || TextUtils.isEmpty(aMapLocation.getCity())) {
            ApmAnalyzer.create()
                    .action("action_location", "type_city_error")
                    .p3(String.valueOf(aMapLocation))
                    .p4(locationBean.localCityCode)
                    .p5(String.valueOf(getType()))
                    .report();
        }

        return locationBean;
    }

    @Override
    public void startLocation() {
        if (client != null) {
            startLocationTime = System.currentTimeMillis();
            client.startLocation();
            TLog.info(TAG, "开始高德定位");
        } else {
            TLog.error(TAG, "高德定位客户端为空");
            if (callback != null) {
                callback.onLocationResult(false, null);
            }
        }
    }

    @Override
    public void stopLocation() {
        if (client != null) {
            client.unRegisterLocationListener(aMapLocationListener);
            if (client.isStarted()) {
                client.stopLocation();
            }
            client.onDestroy();
            TLog.info(TAG, "停止高德定位");
        }
        client = null;
    }

    @Override
    public int getType() {
        return SDK_TYPE_AMAP;
    }
}