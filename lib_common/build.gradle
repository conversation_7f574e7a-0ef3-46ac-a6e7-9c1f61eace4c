apply from: "$rootDir/gradle/common_library.gradle"
android {
}

dependencies {
    testImplementation 'junit:junit:4.12'
    compileOnly project(':lib_depend')

    implementation project(':module_support:lib_mobile_mqtt_service_mars')
    api project(':lib_basic')
    api project(':lib_daemon')
    api project(':lib_http')
    api deps.signer
    api project(':lib_twl_ui')
    api project(':lib_twl_utils')
    api project(':module_support:lib-twl-map-wrapper')

    api deps.androidx.app_compat
    api deps.androidx.constraint_layout
    implementation deps.androidx.localbroadcastmanager

    implementation 'com.tencent:mmkv-static:1.2.11'
    implementation deps.crash_protect

    implementation deps.work.runtime

    implementation(deps.gallery)

    implementation deps.liteav
    // 美团读取渠道
    implementation deps.walle
    // 高德地图配置
    api deps.amap.map3d_location_search
    api deps.bmap.location
    api deps.bmap.search

    api deps.huawei
    api deps.honor

    api deps.tlog
    api deps.google.gson
    api deps.okhttp3
    api deps.bugly.report

    api deps.fresco.okhttp

    api(deps.apm.core)

    api deps.piasy.viewer
    api deps.piasy.indicator
    api deps.piasy.frescoImageLoader
    api deps.piasy.frescoImageViewFactory

    api deps.wmrouter
    api deps.xlog_rules

//    implementation deps.alicloud_httpdns

    implementation deps.tencent_httpdns

    //***************  融合SDK
    api deps.vgroup.zp_sdk_rtc
    api deps.vgroup.zp_sdk_im
    api deps.vgroup.zp_sdk_support
    api deps.vgroup.zp_sdk_eagle
    api deps.vgroup.zp_sdk_matrix
    api deps.vgroup.zp_sdk_ugc
    api deps.vgroup.zp_sdk_avmodule
    api deps.vgroup.zp_uikit_safety_face_detect_release
    api deps.vgroup.zp_sdk_media_kit

    api deps.work.runtime



}
