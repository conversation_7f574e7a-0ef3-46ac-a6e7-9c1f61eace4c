package com.basedata.core;

import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_BOSS_DISTRICT_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_BOSS_FILTER_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CITIES;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_DEGREE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_DISTANCE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_EXPERIENCELIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_EXTRA_DATA_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_GEEK_DISTRICK_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_GEEK_FILTER_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_HOT_CITY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_INDUSTRY_CONFIG2;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_INDUSTRY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_INTERNAL_POSITION_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_POSITIONCATEGORY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_POSITION_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SALARY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SCALE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SCHOOL_LEVEL_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_STAGE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SUBWAY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_WORKCATEGORY_LIST;
import static com.basedata.core.BasicDataConstants.FileName.BOSSFILTERCONFIG_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.BUSINESS_DISTRICT;
import static com.basedata.core.BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK;
import static com.basedata.core.BasicDataConstants.FileName.CITY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.DATA_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.DEGREE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.DISTANCE;
import static com.basedata.core.BasicDataConstants.FileName.EXPERIENCE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.EXTRA_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.GEEKFILTERCONFIG_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.INDUSTRY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.INTERNPOSITION_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.POSITION_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SALARY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SCALE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SCHOOLSEARCH_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SKILL_WORD_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.STAGE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SUBWAY;

import android.content.Context;
import android.content.Intent;

import com.basedata.businessdata.BossFilterDataFactory;
import com.basedata.businessdata.BusinessDataFactory;
import com.basedata.businessdata.BusinessDistrctDataFactory;
import com.basedata.businessdata.CityDataFactory;
import com.basedata.businessdata.DegreeDataFactory;
import com.basedata.businessdata.DistanceDataFactory;
import com.basedata.businessdata.ExperienceDataFactory;
import com.basedata.businessdata.ExtraDataFactory;
import com.basedata.businessdata.GeekFilterDataFactory;
import com.basedata.businessdata.IndustryDataFactory;
import com.basedata.businessdata.InternalPositionlDataFactory;
import com.basedata.businessdata.PositionDataFactory;
import com.basedata.businessdata.SalaryDataFactory;
import com.basedata.businessdata.ScaleDataFactory;
import com.basedata.businessdata.SchoolLevelDataFactory;
import com.basedata.businessdata.SkillWordDataFactory;
import com.basedata.businessdata.StageDataFactory;
import com.basedata.businessdata.SubwayDataFactory;
import com.basedata.network.VersionRequestCallback;
import com.basedata.network.response.Get1004BossFilterResponse;
import com.basedata.network.response.Get1004CityResponse;
import com.basedata.network.response.Get1004DegreeResponse;
import com.basedata.network.response.Get1004DistanceFilterResponse;
import com.basedata.network.response.Get1004ExperienceResponse;
import com.basedata.network.response.Get1004GeekFilterResponse;
import com.basedata.network.response.Get1004IndustryResponse;
import com.basedata.network.response.Get1004InternResponse;
import com.basedata.network.response.Get1004SalaryResponse;
import com.basedata.network.response.Get1004ScaleResponse;
import com.basedata.network.response.Get1004SchoolSearchResponse;
import com.basedata.network.response.Get1004StageResponse;
import com.basedata.network.response.GetBusinessDistrictResponse;
import com.basedata.network.response.GetCityPositionResponse;
import com.basedata.network.response.GetConfigResponse;
import com.basedata.network.response.GetDataResponse;
import com.basedata.network.response.GetDistanceResponse;
import com.basedata.network.response.GetSkillWordsResponse;
import com.basedata.network.response.GetSubwayResponse;
import com.hpbr.bosszhipin.common.basedata.ExtraDataBean;
import com.hpbr.bosszhipin.common.basedata.PositionWordsCategoryBean;
import com.hpbr.bosszhipin.common.basedata.WorkCategorywordsBean;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.twl.http.ApiData;
import com.twl.upgrade.AppVersionBean;

import net.bosszhipin.api.bean.CityBean;
import net.bosszhipin.api.bean.CodeAndNameBean;
import net.bosszhipin.api.bean.FilterConfigBean;
import net.bosszhipin.api.bean.IndustryConfigBean;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * IO: 用于读取/存储 基础数据
 **/
public class BasicDataIO implements VersionRequestCallback {


    private final Context context;
    private static final int CORE_SIZE = 6;
    private static final int MAX_SIZE = 8;
    private static final int TIME_WAIT = 10;
    private static final int BLOCK_SIZE = 8;

    public BasicDataIO(Context context) {
        this.context = context;
    }


    public boolean initAssetFile() {


//        BlockingQueue<Runnable> handleQueue = new ArrayBlockingQueue(BLOCK_SIZE);
//        Runnable handleDataBase = () -> handleDataBase(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.DATA_BEAN_FILENAME, context));
//        handleQueue.add(handleDataBase);
//        Runnable handleExtra = () -> handleExtra(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.EXTRA_BEAN_FILENAME, context));
//        handleQueue.add(handleExtra);
//        Runnable handleSkillWord = () -> handleSkillWord(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.SKILL_WORD_BEAN_FILENAME, context));
//        handleQueue.add(handleSkillWord);
//        Runnable handlePosition = () -> handlePosition(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.POSITION_BEAN_FILENAME, context));
//        handleQueue.add(handlePosition);
//        Runnable handleSubway = () -> handleSubway(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.SUBWAY, context));
//        handleQueue.add(handleSubway);
//        Runnable handleBossBusinessDistrict = () -> handleBossBusinessDistrict(AssetFileUtils.getAssectFileJsonObject(BUSINESS_DISTRICT, context));
//        handleQueue.add(handleBossBusinessDistrict);
//        Runnable handleGeekBusinessDistrict = () -> handleGeekBusinessDistrict(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK, context));
//        handleQueue.add(handleGeekBusinessDistrict);
//        Runnable handleDistance = () -> handleDistance(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.DISTANCE, context));
//        handleQueue.add(handleDistance);
//        ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_SIZE, MAX_SIZE, TIME_WAIT, TimeUnit.SECONDS, handleQueue);
//        executor.prestartAllCoreThreads();


//        handleDataBase(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.DATA_BEAN_FILENAME,context));
//        handleExtra(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.EXTRA_BEAN_FILENAME,context));
//        handleSkillWord(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.SKILL_WORD_BEAN_FILENAME,context));
//        handlePosition(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.POSITION_BEAN_FILENAME,context));
//        handleBossBusinessDistrict(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.BUSINESS_DISTRICT,context));
//        handleGeekBusinessDistrict(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK,context));
//        handleSubway(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.SUBWAY,context));
//        handleDistance(AssetFileUtils.getAssectFileJsonObject(BasicDataConstants.FileName.DISTANCE, context));

        return false;
    }

    public static void forceClearDataVersion() {

//        VersionController.forceClearData(DATA_BEAN_FILENAME);
        VersionController.forceClearData(EXTRA_BEAN_FILENAME);
        VersionController.forceClearData(SKILL_WORD_BEAN_FILENAME);
        VersionController.forceClearData(POSITION_BEAN_FILENAME);
        VersionController.forceClearData(BUSINESS_DISTRICT);
        VersionController.forceClearData(BUSINESS_DISTRICT_FOR_GEEK);
        VersionController.forceClearData(SUBWAY);

        VersionController.forceClearData(CITY_FILENAME);
        VersionController.forceClearData(SCALE_FILENAME);
        VersionController.forceClearData(DEGREE_FILENAME);
        VersionController.forceClearData(EXPERIENCE_FILENAME);
        VersionController.forceClearData(GEEKFILTERCONFIG_FILENAME);
        VersionController.forceClearData(BOSSFILTERCONFIG_FILENAME);
        VersionController.forceClearData(INDUSTRY_FILENAME);
        VersionController.forceClearData(INTERNPOSITION_FILENAME);
        VersionController.forceClearData(SCHOOLSEARCH_FILENAME);
        VersionController.forceClearData(STAGE_FILENAME);
        VersionController.forceClearData(SALARY_FILENAME);
        VersionController.forceClearData(DISTANCE);


        VersionController.forceClearData(KEY_MMKV_BOSS_FILTER_LIST);
        VersionController.forceClearData(KEY_MMKV_EXTRA_DATA_LIST);
        VersionController.forceClearData(KEY_MMKV_BOSS_DISTRICT_LIST);
        VersionController.forceClearData(KEY_MMKV_GEEK_DISTRICK_LIST);

        VersionController.forceClearData(KEY_MMKV_CITY_LIST);
        VersionController.forceClearData(KEY_MMKV_CITY_LIST_PROVINCE);
        VersionController.forceClearData(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL);
        VersionController.forceClearData(KEY_MMKV_CITY_LIST_PROVINCE_CITIES);
        VersionController.forceClearData(KEY_MMKV_HOT_CITY_LIST);

        VersionController.forceClearData(KEY_MMKV_DEGREE_LIST);
        VersionController.forceClearData(KEY_MMKV_DISTANCE_LIST);
        VersionController.forceClearData(KEY_MMKV_EXPERIENCELIST);
        VersionController.forceClearData(KEY_MMKV_GEEK_FILTER_LIST);
        VersionController.forceClearData(KEY_MMKV_INDUSTRY_LIST);
        VersionController.forceClearData(KEY_MMKV_INDUSTRY_CONFIG2);

        VersionController.forceClearData(KEY_MMKV_INTERNAL_POSITION_LIST);
        VersionController.forceClearData(KEY_MMKV_POSITION_LIST);
        VersionController.forceClearData(KEY_MMKV_SCALE_LIST);
        VersionController.forceClearData(KEY_MMKV_SCHOOL_LEVEL_LIST);
        VersionController.forceClearData(KEY_MMKV_WORKCATEGORY_LIST);
        VersionController.forceClearData(KEY_MMKV_POSITIONCATEGORY_LIST);
        VersionController.forceClearData(KEY_MMKV_SUBWAY_LIST);
        VersionController.forceClearData(KEY_MMKV_SALARY_LIST);
        VersionController.forceClearData(KEY_MMKV_STAGE_LIST);
    }

    /**
     * *   *   *   *   *   * *   *   *   *   *   处理本地.json文件 *   *   *   *   *   *   *   *   *   *   *   *   *   *
     **/
    // 处理database.json文件，转为业务数据，版本相关
    public static boolean handleDataBase(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            int versionCheckResultCode = VersionController.checkFileVersion(DATA_BEAN_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                /****************将Json 存储至mmkv LevelBean类型数据*********************/


//                if (VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_LIST, versionCode)) {
//                    JSONArray industry = jsonObject.optJSONArray("industry");
//                    ret &= IndustryDataFactory.getInstance().saveIndustryList(industry, versionCode);
//                }
//
//                if (VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2, versionCode)) {
//                    JSONArray industryConfig2 = jsonObject.optJSONArray("industryConfig2");
//                    ret &= IndustryDataFactory.getInstance().saveIndustryConfig2List(industryConfig2, versionCode);
//                }


                /****************将Json 存储至mmkv FilterBean类型数据*********************/
                if (ret) {
                    VersionController.updateVersionQueue(DATA_BEAN_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                }
            }
        }
        return ret;
    }

    // 1004后讲database.json拆分

    public static boolean handleCity(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(CITY_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(CITY_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_HOT_CITY_LIST, versionCode)) {
                    JSONArray hotCity = jsonObject.optJSONArray("hotCity");
                    ret = CityDataFactory.getInstance().saveHotCityList(hotCity, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_HOT_CITY_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_HOT_CITY_LIST);
                }
                if (VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST, versionCode)) {
                    JSONArray city = jsonObject.optJSONArray("city");
                    boolean ret0 = CityDataFactory.getInstance().saveCityListJson(city, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_CITY_LIST, ret0);
                    ret = ret && ret0;
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_CITY_LIST);
                }
                if (VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, versionCode)) {
                    boolean ret1 = CityDataFactory.getInstance().saveAllCities(versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, ret1);
                    ret = ret && ret1;
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CITIES);
                }
                if (VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE, versionCode)) {
                    boolean ret2 = CityDataFactory.getInstance().saveProvinceList(versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_CITY_LIST_PROVINCE, ret2);
                    ret = ret && ret2;
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE);
                }
                if (VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, versionCode)) {
                    boolean ret3 = CityDataFactory.getInstance().saveProvinceCapitalList(versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, ret3);
                    ret = ret && ret3;
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL);
                }
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(CITY_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(CITY_FILENAME, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(CITY_FILENAME);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(CITY_FILENAME);
        }
        return ret;
    }


    public static boolean handleIndustry(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(INDUSTRY_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(INDUSTRY_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2, versionCode)) {
                    JSONArray industryConfig2 = jsonObject.optJSONArray("industryConfig2");
                    ret = IndustryDataFactory.getInstance().saveIndustryConfig2List(industryConfig2, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_INDUSTRY_CONFIG2, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(INDUSTRY_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(INDUSTRY_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(INDUSTRY_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(INDUSTRY_FILENAME);
        }
        return ret;
    }

    public static boolean handleExperience(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(EXPERIENCE_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(EXPERIENCE_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_EXPERIENCELIST, versionCode)) {
                    JSONArray experience = jsonObject.optJSONArray("experience");
                    ret = ExperienceDataFactory.getInstance().saveExperienceList(experience, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_EXPERIENCELIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_EXPERIENCELIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(EXPERIENCE_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(EXPERIENCE_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(EXPERIENCE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(EXPERIENCE_FILENAME);
        }
        return ret;
    }

    public static boolean handleSchoolSearch(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(SCHOOLSEARCH_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(SCHOOLSEARCH_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_SCHOOL_LEVEL_LIST, versionCode)) {
                    JSONArray schoolSearch = jsonObject.optJSONArray("schoolSearch");
                    ret = SchoolLevelDataFactory.getInstance().saveSchooLevels(schoolSearch, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_SCHOOL_LEVEL_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_SCHOOL_LEVEL_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(SCHOOLSEARCH_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(SCHOOLSEARCH_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(SCHOOLSEARCH_FILENAME, updateResult);
            }

        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(SCHOOLSEARCH_FILENAME);
        }
        return ret;
    }

    public static boolean handleDegree(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(DEGREE_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(DEGREE_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_DEGREE_LIST, versionCode)) {
                    JSONArray degree = jsonObject.optJSONArray("degree");
                    ret = DegreeDataFactory.getInstance().saveDegreeList(degree, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_DEGREE_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_DEGREE_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(DEGREE_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(DEGREE_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(DEGREE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(DEGREE_FILENAME);
        }
        return ret;
    }

    public static boolean handleScale(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(SCALE_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(SCALE_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_SCALE_LIST, versionCode)) {
                    JSONArray scale = jsonObject.optJSONArray("scale");
                    ret = ScaleDataFactory.getInstance().saveScaleList(scale, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_SCALE_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_SCALE_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(SCALE_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(SCALE_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(SCALE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(SCALE_FILENAME);
        }
        return ret;
    }

    public static boolean handleInternPosition(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(INTERNPOSITION_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(INTERNPOSITION_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_INTERNAL_POSITION_LIST, versionCode)) {
                    JSONArray internPosition = jsonObject.optJSONArray("internPosition");
                    ret = InternalPositionlDataFactory.getInstance().saveInternalPositions(internPosition, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_INTERNAL_POSITION_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_INTERNAL_POSITION_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(INTERNPOSITION_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(INTERNPOSITION_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(INTERNPOSITION_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(INTERNPOSITION_FILENAME);
        }
        return ret;
    }

    public static boolean handleGeekFilter(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(GEEKFILTERCONFIG_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(GEEKFILTERCONFIG_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_GEEK_FILTER_LIST, versionCode)) {
                    JSONArray geekFilterConfig = jsonObject.optJSONArray("geekFilterConfig");
                    ret = GeekFilterDataFactory.getInstance().saveGeekFilterList(geekFilterConfig, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_GEEK_FILTER_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_GEEK_FILTER_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(GEEKFILTERCONFIG_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(GEEKFILTERCONFIG_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(GEEKFILTERCONFIG_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(GEEKFILTERCONFIG_FILENAME);
        }
        return ret;
    }

    public static boolean handleBossFilter(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(BOSSFILTERCONFIG_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(BOSSFILTERCONFIG_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                if (VersionController.checkNeedUpdate(KEY_MMKV_BOSS_FILTER_LIST, versionCode)) {
                    JSONArray geekFilterConfig = jsonObject.optJSONArray("bossFilterConfig");
                    ret = BossFilterDataFactory.getInstance().saveBossFilterList(geekFilterConfig, versionCode);
                    BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_BOSS_FILTER_LIST, ret);
                } else {
                    BasicDataActionHelper.onAssetsKeyNotNeedUpdate(KEY_MMKV_BOSS_FILTER_LIST);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(BOSSFILTERCONFIG_FILENAME);
            }

            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(BOSSFILTERCONFIG_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(BOSSFILTERCONFIG_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(BOSSFILTERCONFIG_FILENAME);
        }
        return ret;
    }


    // 处理extraData.json文件，转为业务数据，版本相关
    public static boolean handleExtra(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(EXTRA_BEAN_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(EXTRA_BEAN_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                List<ExtraDataBean> extraDataBeans = ExtraDataFactory.getInstance().handleExtraObject(jsonObject);
                if (!LList.isEmpty(extraDataBeans)) {
                    ret = ExtraDataFactory.getInstance().saveExtraDataList(extraDataBeans, versionCode);
                }
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_EXTRA_DATA_LIST, ret);
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(EXTRA_BEAN_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(EXTRA_BEAN_FILENAME, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(EXTRA_BEAN_FILENAME);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(EXTRA_BEAN_FILENAME);
        }
        return ret;
    }

    // 处理skillword.json文件，转为业务数据，版本相关
    public static boolean handleSkillWord(JSONObject jsonObject) {

        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(SKILL_WORD_BEAN_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(SKILL_WORD_BEAN_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {

                JSONObject workCategorywords = jsonObject.optJSONObject("workCategorywords");
                ret = SkillWordDataFactory.getInstance().saveWorkCategoryWords(workCategorywords, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_WORKCATEGORY_LIST, ret);
                JSONObject positionWordsCategory = jsonObject.optJSONObject("positionWordsCategory");
                ret = SkillWordDataFactory.getInstance().savePositionWordsCategory(positionWordsCategory, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_POSITIONCATEGORY_LIST, ret);
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(SKILL_WORD_BEAN_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(SKILL_WORD_BEAN_FILENAME, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(SKILL_WORD_BEAN_FILENAME);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(SKILL_WORD_BEAN_FILENAME);
        }
        return ret;
    }

    public static boolean handlePosition(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {

            float versionCode = (float) jsonObject.optDouble("positionVersion");
            BasicDataActionHelper.onAssetsVersionCode(POSITION_BEAN_FILENAME, versionCode);
            String city = jsonObject.optString("cityCode");
            int versionCheckResultCode = VersionController.checkFileVersion(POSITION_BEAN_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode) || VersionController.checkPositionCityChanged(city)) {
//                String city = jsonObject.optString("cityCode");
                JSONArray position = jsonObject.optJSONArray("position");
                ret = PositionDataFactory.getInstance().savePositionList(position, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_POSITION_LIST, ret);
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(POSITION_BEAN_FILENAME);
            }
            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(POSITION_BEAN_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(POSITION_BEAN_FILENAME, updateResult);
                VersionController.savePositionCode(city);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(POSITION_BEAN_FILENAME);
        }

        return ret;
    }

    public static boolean handleBossBusinessDistrict(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {

            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(BUSINESS_DISTRICT, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(BUSINESS_DISTRICT, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
//                String city = jsonObject.optString("cityCode");
                JSONArray businessDistrict = jsonObject.optJSONArray("businessDistrict");
                ret = BusinessDistrctDataFactory.getInstance().saveBossDistrictList(businessDistrict, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_BOSS_DISTRICT_LIST, ret);
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(BUSINESS_DISTRICT);
            }
            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(BUSINESS_DISTRICT, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(BUSINESS_DISTRICT, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(BUSINESS_DISTRICT);
        }

        return ret;
    }

    public static boolean handleGeekBusinessDistrict(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {

            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(BUSINESS_DISTRICT_FOR_GEEK, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
//                String city = jsonObject.optString("cityCode");
                JSONArray businessDistrict = jsonObject.optJSONArray("businessDistrict");
                ret = BusinessDistrctDataFactory.getInstance().saveGeekDistrictList(businessDistrict, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_GEEK_DISTRICK_LIST, ret);
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(BUSINESS_DISTRICT_FOR_GEEK);
            }
            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(BUSINESS_DISTRICT_FOR_GEEK, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(BUSINESS_DISTRICT_FOR_GEEK);
        }

        return ret;
    }

    public static boolean handleSubway(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {

            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(SUBWAY, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(SUBWAY, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
//                String city = jsonObject.optString("cityCode");
                JSONArray subwayList = jsonObject.optJSONArray("subway");
                ret = SubwayDataFactory.getInstance().saveSubwayList(subwayList, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_SUBWAY_LIST, ret);
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(SUBWAY);
            }
            if (ret) {
                boolean updateResult = VersionController.updateVersionQueue(SUBWAY, versionCode, versionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onAssetsFileUpdateResult(SUBWAY, updateResult);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(SUBWAY);
        }
        return ret;
    }

    public static boolean handleDistance(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(DISTANCE, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(DISTANCE, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                JSONArray jaData = jsonObject.optJSONArray("distanceFilter");
                ret = DistanceDataFactory.getInstance().saveDistanceList(jaData, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_DISTANCE_LIST, ret);
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(DISTANCE, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(DISTANCE, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(DISTANCE);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(DISTANCE);
        }
        return ret;
    }

    public static boolean handleStage(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(STAGE_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(STAGE_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                JSONArray jaData = jsonObject.optJSONArray("stage");
                ret = StageDataFactory.getInstance().saveStageList(jaData, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_STAGE_LIST, ret);
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(STAGE_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(STAGE_FILENAME, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(STAGE_FILENAME);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(STAGE_FILENAME);
        }
        return ret;
    }

    public static boolean handleSalary(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            float versionCode = (float) jsonObject.optDouble("dataVersion");
            BasicDataActionHelper.onAssetsVersionCode(SALARY_FILENAME, versionCode);
            int versionCheckResultCode = VersionController.checkFileVersion(SALARY_FILENAME, versionCode);
            if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
                JSONArray jaData = jsonObject.optJSONArray("salary");
                ret = SalaryDataFactory.getInstance().saveSalaryList(jaData, versionCode);
                BasicDataActionHelper.onAssetsSaveResult(KEY_MMKV_SALARY_LIST, ret);
                if (ret) {
                    boolean updateResult = VersionController.updateVersionQueue(SALARY_FILENAME, versionCode, versionCheckResultCode);// 初始化成功
                    BasicDataActionHelper.onAssetsFileUpdateResult(SALARY_FILENAME, updateResult);
                }
            } else {
                BasicDataActionHelper.onAssetsNotNeedUpdate(SALARY_FILENAME);
            }
        } else {
            BasicDataActionHelper.onAssetsNullJsonObj(SALARY_FILENAME);
        }
        return ret;
    }


    /**
     * *   *   *   *   *   * *   *   *   *   *   处理请求返回的基础数据 *   *   *   *   *   *   *   *   *   *   *   *   *   *
     **/

    @Override
    public void handleAppVersion(int status,
                                 String newVersion, int showTime,
                                 String url, String message,
                                 String versionShowName, int market) {
        ReceiverUtils.sendBroadcast(Utils.getApp(), new Intent(Constants.RECEIVER_APP_UPGRADE_ACTION));
        init(new AppVersionBean(status, showTime, url, message, newVersion, versionShowName, market));
    }


    // 之后 要 同步upgradmanager 中 的 init方法
    public void init(AppVersionBean appVersionBean) {
        SP.get().putInt(Constants.APP_UPGRADE_KEY, appVersionBean.status);
        appVersionBean.save();
    }

    @Override
    public boolean handleExtraResponse(ApiData<GetDataResponse> data) {
        boolean ret = false;
        GetDataResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(EXTRA_BEAN_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_EXTRA_DATA_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (response != null && !LText.isEmptyOrNull(response.dataValue)) {
                List<ExtraDataBean> extraDataBeans = ExtraDataFactory.getInstance().handleExtraObjectNet(AssetFileUtils.getJsonObject(response.dataValue, EXTRA_BEAN_FILENAME));
                if (!LList.isEmpty(extraDataBeans)) {
                    ret = ExtraDataFactory.getInstance().saveExtraDataList(extraDataBeans, version);
                }
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_EXTRA_DATA_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(EXTRA_BEAN_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(EXTRA_BEAN_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(EXTRA_BEAN_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_EXTRA_DATA_LIST);
        }

        return ret;
    }

    @Override
    public boolean handlePositionResponse(ApiData<GetCityPositionResponse> data) {

        boolean ret = false;
        GetCityPositionResponse response = data.resp;
        float version = (float) response.positionVersion;
        BasicDataActionHelper.onApiVersionCode(POSITION_BEAN_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_POSITION_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode) || VersionController.checkPositionCityChanged(response.cityCode)) {
            if (!LList.isEmpty(response.position)) {
                List<LevelBean> positionList = AssetFileUtils.parseCityBean(response.position);
                ret = BusinessDataFactory.saveListJson(positionList, KEY_MMKV_POSITION_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_POSITION_LIST, ret);

            if (ret) {
                VersionController.savePositionCode(response.cityCode);
                int fileVersionCheckResultCode = VersionController.checkFileVersion(POSITION_BEAN_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(POSITION_BEAN_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(POSITION_BEAN_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_POSITION_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleBusinessDistrictResponse(ApiData<GetBusinessDistrictResponse> data,
                                                  int role) {
        boolean ret = false;
        GetBusinessDistrictResponse response = data.resp;
        float version = (float) response.dataVersion;

        String listName = (role == 0 ? KEY_MMKV_GEEK_DISTRICK_LIST : KEY_MMKV_BOSS_DISTRICT_LIST);
        String fileName = (role == 0 ? BUSINESS_DISTRICT_FOR_GEEK : BUSINESS_DISTRICT);
        int versionCheckResultCode = VersionController.checkFileVersion(listName, version);

        BasicDataActionHelper.onApiVersionCode(fileName, version);

        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.businessDistrict)) {
                List<LevelBean> positionList = AssetFileUtils.parseCityBean(response.businessDistrict);
                if (role == 1) {
                    ret = BusinessDistrctDataFactory.saveListJson(positionList, KEY_MMKV_BOSS_DISTRICT_LIST, version);
                } else if (role == 0) {
                    ret = BusinessDistrctDataFactory.saveListJson(positionList, KEY_MMKV_GEEK_DISTRICK_LIST, version);
                }
            }
            BasicDataActionHelper.onApiSaveResult(listName, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(fileName, version);
                boolean updateResult = VersionController.updateVersionQueue(fileName, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(fileName, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(listName);
        }
        return ret;
    }

    @Override
    public boolean handleSubwayResponse(ApiData<GetSubwayResponse> data) {
        boolean ret = false;
        GetSubwayResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(SUBWAY, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_SUBWAY_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.subway)) {
                List<LevelBean> positionList = AssetFileUtils.parseCityBean(response.subway);
                ret = BusinessDataFactory.saveListJson(positionList, KEY_MMKV_SUBWAY_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_SUBWAY_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(SUBWAY, version);
                boolean updateResult = VersionController.updateVersionQueue(SUBWAY, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(SUBWAY, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_SUBWAY_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleDistanceResponse(ApiData<GetDistanceResponse> data) {
        boolean ret = false;
        GetDistanceResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(DISTANCE, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_DISTANCE_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.distanceFilter)) {
                List<LevelBean> positionList = AssetFileUtils.parseCityBean(response.distanceFilter);
                ret = BusinessDataFactory.saveListJson(positionList, KEY_MMKV_DISTANCE_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_DISTANCE_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(DISTANCE, version);
                boolean updateResult = VersionController.updateVersionQueue(DISTANCE, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(DISTANCE, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_DISTANCE_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleCityResponse(ApiData<Get1004CityResponse> data) {

        boolean ret = false;
        Get1004CityResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(CITY_FILENAME, version);

        List<LevelBean> cityBeans = response.city;
        List<LevelBean> hotCities = response.hotCity;

        if (VersionController.checkNeedUpdate(VersionController.checkFileVersion(KEY_MMKV_CITY_LIST, version))) {
            ret = LList.isNotEmpty(cityBeans) && BusinessDataFactory.saveListJson(cityBeans, KEY_MMKV_CITY_LIST, version);
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_CITY_LIST, ret);
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_CITY_LIST);
        }
        if (VersionController.checkNeedUpdate(VersionController.checkFileVersion(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, version))) {
            boolean ret00  = CityDataFactory.getInstance().saveAllCities(version);
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, ret00);
            ret = ret && ret00;
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CITIES);
        }
        if (VersionController.checkNeedUpdate(VersionController.checkFileVersion(KEY_MMKV_CITY_LIST_PROVINCE, version))) {
            boolean ret01 = CityDataFactory.getInstance().saveProvinceList(version);
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_CITY_LIST_PROVINCE, ret01);
            ret = ret && ret01;
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE);
        }
        if (VersionController.checkNeedUpdate(VersionController.checkFileVersion(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, version))) {
            boolean ret02 = CityDataFactory.getInstance().saveProvinceCapitalList(version);
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, ret02);
            ret = ret && ret02;
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL);
        }

        if (VersionController.checkNeedUpdate(VersionController.checkFileVersion(KEY_MMKV_HOT_CITY_LIST, version))) {
            boolean ret03 = LList.isNotEmpty(hotCities) && BusinessDataFactory.saveListJson(hotCities, KEY_MMKV_HOT_CITY_LIST, version);
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_HOT_CITY_LIST, ret03);
            ret = ret && ret03;
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_HOT_CITY_LIST);
        }
        if (ret) {
            int fileVersionCheckResultCode = VersionController.checkFileVersion(CITY_FILENAME, version);
            boolean updateResult = VersionController.updateVersionQueue(CITY_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
            BasicDataActionHelper.onApiFileUpdateResult(CITY_FILENAME, updateResult);
        }
        return ret;

    }

    @Override
    public boolean handelDegreeResponse(ApiData<Get1004DegreeResponse> data) {
        boolean ret = false;
        Get1004DegreeResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(DEGREE_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_DEGREE_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.degree)) {
                List<LevelBean> degree = response.degree;
                ret = BusinessDataFactory.saveListJson(degree, KEY_MMKV_DEGREE_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_DEGREE_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(DEGREE_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(DEGREE_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(DEGREE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_DEGREE_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleDistanceFilterResponse(ApiData<Get1004DistanceFilterResponse> data) {
        boolean ret = false;
        Get1004DistanceFilterResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(DISTANCE, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_DISTANCE_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.distanceFilter)) {
                List<LevelBean> distanceFilter = response.distanceFilter;
                ret = BusinessDataFactory.saveListJson(distanceFilter, KEY_MMKV_DISTANCE_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_DISTANCE_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(DISTANCE, version);
                boolean updateResult = VersionController.updateVersionQueue(DISTANCE, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(DISTANCE, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_DISTANCE_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleIndustry(ApiData<Get1004IndustryResponse> data) {
        boolean ret = false;
        Get1004IndustryResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(INDUSTRY_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_INDUSTRY_CONFIG2, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.industryConfig2)) {
                List<LevelBean> industryConfig2 = response.industryConfig2;
                ret = BusinessDataFactory.saveListJson(industryConfig2, KEY_MMKV_INDUSTRY_CONFIG2, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_INDUSTRY_CONFIG2, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(INDUSTRY_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(INDUSTRY_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(INDUSTRY_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2);
        }
        return ret;
    }

    @Override
    public boolean handleSchoolSearch(ApiData<Get1004SchoolSearchResponse> data) {
        boolean ret = false;
        Get1004SchoolSearchResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(SCHOOLSEARCH_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_SCHOOL_LEVEL_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.schoolSearch)) {
                List<LevelBean> schoolSearch = response.schoolSearch;
                ret = BusinessDataFactory.saveListJson(schoolSearch, KEY_MMKV_SCHOOL_LEVEL_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_SCHOOL_LEVEL_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(SCHOOLSEARCH_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(SCHOOLSEARCH_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(SCHOOLSEARCH_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_SCHOOL_LEVEL_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleInternalPosition(ApiData<Get1004InternResponse> data) {
        boolean ret = false;
        Get1004InternResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(INTERNPOSITION_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_INTERNAL_POSITION_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.internPosition)) {
                List<LevelBean> internPosition = response.internPosition;
                ret = BusinessDataFactory.saveListJson(internPosition, KEY_MMKV_INTERNAL_POSITION_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_INTERNAL_POSITION_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(INTERNPOSITION_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(INTERNPOSITION_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(INTERNPOSITION_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_INTERNAL_POSITION_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleScale(ApiData<Get1004ScaleResponse> data) {
        boolean ret = false;
        Get1004ScaleResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(SCALE_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_SCALE_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.scale)) {
                List<LevelBean> scale = response.scale;
                ret = BusinessDataFactory.saveListJson(scale, KEY_MMKV_SCALE_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_SCALE_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(SCALE_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(SCALE_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(SCALE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_SCALE_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleExperience(ApiData<Get1004ExperienceResponse> data) {
        boolean ret = false;
        Get1004ExperienceResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(EXPERIENCE_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_EXPERIENCELIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.experience)) {
                List<LevelBean> scale = response.experience;
                ret = BusinessDataFactory.saveListJson(scale, KEY_MMKV_EXPERIENCELIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_EXPERIENCELIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(EXPERIENCE_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(EXPERIENCE_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(EXPERIENCE_FILENAME, updateResult);
            }
        }  else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_EXPERIENCELIST);
        }
        return ret;
    }

    @Override
    public boolean handleGeekFiltler(ApiData<Get1004GeekFilterResponse> data) {
        boolean ret = false;
        Get1004GeekFilterResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(GEEKFILTERCONFIG_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_GEEK_FILTER_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.geekFilterConfig)) {
                List<FilterBean> geekFilterConfig = response.geekFilterConfig;
                ret = BusinessDataFactory.saveListJson(geekFilterConfig, KEY_MMKV_GEEK_FILTER_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_GEEK_FILTER_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(GEEKFILTERCONFIG_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(GEEKFILTERCONFIG_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(GEEKFILTERCONFIG_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_GEEK_FILTER_LIST);
        }
        return ret;
    }

    @Override
    public boolean handelBossFilter(ApiData<Get1004BossFilterResponse> data) {
        boolean ret = false;
        Get1004BossFilterResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(BOSSFILTERCONFIG_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_BOSS_FILTER_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.bossFilterConfig)) {
                List<FilterBean> bossFilterConfig = response.bossFilterConfig;
                ret = BusinessDataFactory.saveListJson(bossFilterConfig, KEY_MMKV_BOSS_FILTER_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_BOSS_FILTER_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(BOSSFILTERCONFIG_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(BOSSFILTERCONFIG_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(BOSSFILTERCONFIG_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_BOSS_FILTER_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleSalary(ApiData<Get1004SalaryResponse> data) {
        boolean ret = false;
        Get1004SalaryResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(SALARY_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_SALARY_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.salary)) {
                List<LevelBean> scale = response.salary;
                ret = BusinessDataFactory.saveListJson(scale, KEY_MMKV_SALARY_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_SALARY_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(SALARY_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(SALARY_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(SALARY_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_SALARY_LIST);
        }
        return ret;
    }

    @Override
    public boolean handleStage(ApiData<Get1004StageResponse> data) {
        boolean ret = false;
        Get1004StageResponse response = data.resp;
        float version = (float) response.dataVersion;
        BasicDataActionHelper.onApiVersionCode(STAGE_FILENAME, version);

        int versionCheckResultCode = VersionController.checkFileVersion(KEY_MMKV_STAGE_LIST, version);
        if (VersionController.checkNeedUpdate(versionCheckResultCode)) {
            if (!LList.isEmpty(response.stage)) {
                List<LevelBean> scale = response.stage;
                ret = BusinessDataFactory.saveListJson(scale, KEY_MMKV_STAGE_LIST, version);
            }
            BasicDataActionHelper.onApiSaveResult(KEY_MMKV_STAGE_LIST, ret);
            if (ret) {
                int fileVersionCheckResultCode = VersionController.checkFileVersion(STAGE_FILENAME, version);
                boolean updateResult = VersionController.updateVersionQueue(STAGE_FILENAME, version, fileVersionCheckResultCode);// 初始化成功
                BasicDataActionHelper.onApiFileUpdateResult(STAGE_FILENAME, updateResult);
            }
        } else {
            BasicDataActionHelper.onApiNotNeedUpdate(KEY_MMKV_STAGE_LIST);
        }
        return ret;
    }
}


