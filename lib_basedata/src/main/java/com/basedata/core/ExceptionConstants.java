package com.basedata.core;

/**
 * <AUTHOR>
 **/
public class ExceptionConstants {

    public static final String ACTION_BASEDATA_EXCEPTION = "action_basedata_exception";
    // 读取asset文件异常
    public static final String BASEDATA_EXCEPTION_GETFILE = "type_getfile_fail";
    // 存储本地文件异常 debug模式
    public static final String BASEDATA_EXCEPTION_SAVEFILE = "type_savefile_fail";

    public static final String BASEDATA_EXCEPTION_DOWNLOAD = "type_downloadfile_fail";
    // stringAssetFileUtils 转换 json异常
    public static final String BASEDATA_EXCEPTION_GETJSON = "type_getjson_fail";
    // json 解析异常
    public static final String BASEDATA_EXCEPTION_PARSEJSON = "type_parsejson_fail";
    // 解析 网络json失败
    public static final String BASEDATA_EXCEPTION_PARSEJSON_NET = "type_parsejson_net_fail";
    // 存储mmkv数据异常
    public static final String BASEDATA_EXCEPTION_SAVEMMKV = "type_savemmkv_fail";
    // 读取mmkv数据异常
    public static final String BASEDATA_EXCEPTION_READMMKV = "type_readmmkv_fail";
    // 存储mmkv业务数据异常
    public static final String BASEDATA_EXCEPTION_SAVEBUSINESS = "type_savebusiness_fail";
    // 读取 jsonList失败
    public static final String BASEDATA_EXCEPTION_READBUSINESS = "type_readbusiness_fail";
    public static final String BASEDATA_EXCEPTION_READBUSINESS_VERSION_NOT_FOUND = "type_readbusiness_fail_version_not_found";
    public static final String BASEDATA_EXCEPTION_READBUSINESS_GET_JSON_STRING = "type_readbusiness_fail_get_json_string";
    // 版本检查失败
    public static final String BASEDATA_EXCEPTION_VERSIONCHECK = "type_versioncheck_fail";
    // 更新版本失败
    public static final String BASEDATA_EXCEPTION_VERSIONUPDATE = "type_versionupdate_fail";
    // 数据降级
    public static final String BASEDATA_EXCEPTION_DESGRADE = "type_version_desgrade";
    // 数据更新流程上报
    public static final String BASEDATA_EXCEPTION_PROCESS = "type_update_process";
    // 城市数据存储失败上报
    public static final String BASEDATA_EXCEPTION_SAVE_CITY_DATA = "type_exp_save_city_data";
    // 城市数据获取失败上报
    public static final String BASEDATA_EXCEPTION_GET_CITY_DATA = "type_exp_get_city_data";
    // 重置并调用接口事件上报
    public static final String BASEDATA_ON_BASIC_DATA_FUNCTION = "type_on_basic_data_function";

    // 高德定位code和基础数据不匹配
    public static final String BASEDATA_AMAP_NOTMACH = "type_location_amap_notmatch";


    // 数据库里可以查到但是mmkv没查到
    public static final String BASEDATA_EXCEPTION_MMKV_NO_DATA = "type_mmkv_no_data";
    // 1311.92372:【基础数据】新增 assets 数据读取打点
    public static final String BASEDATA_ON_READ_ASSETS_RESULT = "type_read_business_assets_result";


    //获取版本号
    public static final String BASEDATA_LOG_GETCURRENTVERSION_NULL = "bd_version_get_null";
    //更新版本号
    public static final String BASEDATA_LOG_UPDATECURRENTVERSION_NULL = "bd_version_update_fail";
    public static final String BASEDATA_LOG_UPDATECURRENTVERSION_SUCCESS = "bd_version_update_ok";
    //对比检查版本号
    public static final String BASEDATA_LOG_VERSION_CHECK = "basedata_version_check";
    //降级版本号
    public static final String BASEDATA_LOG_VERSION_DESGRADE = "basedata_version_desg";

    public static final String BASEDATA_LOG_READ_BUSINESS = "basedata_readbusiness";
    public static final String BASEDATA_LOG_SAVE_BUSINESS = "basedata_savebusiness";

    public static final String BASEDATA_LOG_READ_MMKV = "basedata_readmmkv";
    public static final String BASEDATA_LOG_SAVE_MMKV = "basedata_savemmkv";
    public static final String BASEDATA_DISTRICT_NETERROR = "type_districtdata_error";



}
