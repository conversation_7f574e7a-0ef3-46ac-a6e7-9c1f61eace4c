package com.basedata.core;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2020-01-07
 **/
public class BasicDataConstants {


    /**
     * 基础数据文件名称
     **/
    public static class FileName {

        //        public static final String BASIC_DATA_FILE_INITIALIZED = "basic_data_file_initialized";
        public static final String DATA_BEAN_FILENAME = "dataBase.json";//6.8 对应服务端 basicDataVersion

        //1004拆分
        public static final String CITY_FILENAME = "city.json";
        public static final String SCALE_FILENAME = "scale.json";
        public static final String DEGREE_FILENAME = "degree.json";
        public static final String EXPERIENCE_FILENAME = "experience.json";
        public static final String GEEKFILTERCONFIG_FILENAME = "geekFilterConfig.json";
        public static final String BOSSFILTERCONFIG_FILENAME = "bossFilterConfig.json";
        public static final String INDUSTRY_FILENAME = "industry.json";
        public static final String INTERNPOSITION_FILENAME = "internPosition.json";
        public static final String SCHOOLSEARCH_FILENAME = "schoolSearch.json";
        public static final String STAGE_FILENAME = "stage.json";
        public static final String SALARY_FILENAME = "salary.json";


        //---------------------------//
        public static final String EXTRA_BEAN_FILENAME = "extraData.json"; //2.8 对应服务端 breakSilenceVersion
        public static final String SKILL_WORD_BEAN_FILENAME = "skillWord.json";//9.1 skillWordsVersion
        public static final String POSITION_BEAN_FILENAME = "position.json";// 3.4 positionVersion
        public static final String BUSINESS_DISTRICT = "business_district.json";
        public static final String BUSINESS_DISTRICT_FOR_GEEK = "business_district_for_geek.json";
        public static final String SUBWAY = "subway.json";
        public static final String DISTANCE = "distanceFilter.json";
    }


    /**
     * 解析后业务数据存储mmkv文件名
     **/
    public static class BusinessDataFileName {

        // 筛选数据
        public static final String KEY_MMKV_BOSS_FILTER_LIST = "key_mmvk_boss_filter_list";
        public static final String KEY_MMKV_EXTRA_DATA_LIST = "key_mmvk_extra_data_list";
        // 商圈数据
        public static final String KEY_MMKV_BOSS_DISTRICT_LIST = "key_mmvk_boss_district_list";
        public static final String KEY_MMKV_GEEK_DISTRICK_LIST = "key_mmvk_geek_district_list";
        // 城市数据
        public static final String KEY_MMKV_CITY_LIST = "key_mmvk_city_list";
        public static final String KEY_MMKV_CITY_LIST_PROVINCE = "key_mmvk_city_list_provinces";
        public static final String KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL = "key_mmvk_city_list_provinces_capital";
        public static final String KEY_MMKV_CITY_LIST_PROVINCE_CITIES = "key_mmvk_city_list_provinces_cities";
        public static final String KEY_MMKV_HOT_CITY_LIST = "key_mmvk_hot_city_list";
        // 学位数据
        public static final String KEY_MMKV_DEGREE_LIST = "key_mmvk_degree_list";
        // 周边距离数据
        public static final String KEY_MMKV_DISTANCE_LIST = "key_mmvk_distance_list";
        // 经验数据
        public static final String KEY_MMKV_EXPERIENCELIST = "key_mmvk_experience_list";
        // 牛人筛选数据
        public static final String KEY_MMKV_GEEK_FILTER_LIST = "key_mmvk_geek_filter_list";
        // 行业信息数据
        public static final String KEY_MMKV_INDUSTRY_LIST = "key_mmvk_industry_list";
        public static final String KEY_MMKV_INDUSTRY_CONFIG2 = "key_mmvk_industry_config2";
        // 实习职位数据
        public static final String KEY_MMKV_INTERNAL_POSITION_LIST = "key_mmvk_internal_position_list";
        // 职位数据
        public static final String KEY_MMKV_POSITION_LIST = "key_mmvk_position_list";
        // 公司规模
        public static final String KEY_MMKV_SCALE_LIST = "key_mmvk_scale_list";
        // 薪资
        public static final String KEY_MMKV_SALARY_LIST = "key_mmvk_salary_list";
        // 规模
        public static final String KEY_MMKV_STAGE_LIST = "key_mmvk_stage_list";
        // 学校级别
        public static final String KEY_MMKV_SCHOOL_LEVEL_LIST = "key_mmvk_school_level_list";
        // 策略
        public static final String KEY_MMKV_WORKCATEGORY_LIST = "key_mmvk_workCategorywords_list";
        public static final String KEY_MMKV_POSITIONCATEGORY_LIST = "key_mmvk_positionWordsCategory_list";
        // 地铁数据
        public static final String KEY_MMKV_SUBWAY_LIST = "key_mmvk_subway_list";
    }

    @Nullable
    public static String[] getMMKVKeyArray(@NonNull String fileName) {
        switch (fileName) {
            case FileName.CITY_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_CITY_LIST,
                        BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE,
                        BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL,
                        BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CITIES,
                        BusinessDataFileName.KEY_MMKV_HOT_CITY_LIST
                };
            case FileName.SCALE_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_SCALE_LIST
                };
            case FileName.DEGREE_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_DEGREE_LIST
                };
            case FileName.EXPERIENCE_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_EXPERIENCELIST
                };
            case FileName.GEEKFILTERCONFIG_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_GEEK_FILTER_LIST
                };
            case FileName.BOSSFILTERCONFIG_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_BOSS_FILTER_LIST
                };
            case FileName.INDUSTRY_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_INDUSTRY_LIST,
                        BusinessDataFileName.KEY_MMKV_INDUSTRY_CONFIG2
                };
            case FileName.INTERNPOSITION_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_INTERNAL_POSITION_LIST
                };
            case FileName.SCHOOLSEARCH_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_SCHOOL_LEVEL_LIST
                };
            case FileName.STAGE_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_STAGE_LIST
                };
            case FileName.SALARY_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_SALARY_LIST
                };
            case FileName.EXTRA_BEAN_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_EXTRA_DATA_LIST
                };
            case FileName.POSITION_BEAN_FILENAME:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_POSITION_LIST
                };
            case FileName.BUSINESS_DISTRICT:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_BOSS_DISTRICT_LIST
                };
            case FileName.BUSINESS_DISTRICT_FOR_GEEK:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_GEEK_DISTRICK_LIST
                };
            case FileName.SUBWAY:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_SUBWAY_LIST
                };
            case FileName.DISTANCE:
                return new String[]{
                        BusinessDataFileName.KEY_MMKV_DISTANCE_LIST
                };
            default:
                return null;
        }
    }
}
