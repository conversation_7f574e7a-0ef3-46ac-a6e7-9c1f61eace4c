package com.basedata.core;

import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_BOSS_DISTRICT_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_BOSS_FILTER_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CITIES;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_DEGREE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_DISTANCE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_EXPERIENCELIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_EXTRA_DATA_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_GEEK_DISTRICK_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_GEEK_FILTER_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_HOT_CITY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_INDUSTRY_CONFIG2;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_INTERNAL_POSITION_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_POSITION_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SALARY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SCALE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SCHOOL_LEVEL_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_STAGE_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SUBWAY_LIST;
import static com.basedata.core.BasicDataConstants.FileName.BOSSFILTERCONFIG_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.BUSINESS_DISTRICT;
import static com.basedata.core.BasicDataConstants.FileName.BUSINESS_DISTRICT_FOR_GEEK;
import static com.basedata.core.BasicDataConstants.FileName.CITY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.DEGREE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.DISTANCE;
import static com.basedata.core.BasicDataConstants.FileName.EXPERIENCE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.EXTRA_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.GEEKFILTERCONFIG_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.INDUSTRY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.INTERNPOSITION_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.POSITION_BEAN_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SALARY_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SCALE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SCHOOLSEARCH_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.STAGE_FILENAME;
import static com.basedata.core.BasicDataConstants.FileName.SUBWAY;
import static com.basedata.core.ExceptionConstants.ACTION_BASEDATA_EXCEPTION;
import static com.basedata.core.ExceptionConstants.BASEDATA_EXCEPTION_DOWNLOAD;

import com.basedata.network.VersionRequestCallback;
import com.basedata.network.request.BasicDataUpgradeCheckRequest;
import com.basedata.network.request.Get1004BossFilterRequest;
import com.basedata.network.request.Get1004CityRequest;
import com.basedata.network.request.Get1004DegreeRequest;
import com.basedata.network.request.Get1004DistanceFilterRequest;
import com.basedata.network.request.Get1004ExperienceRequest;
import com.basedata.network.request.Get1004GeekFilterRequest;
import com.basedata.network.request.Get1004IndustryRequest;
import com.basedata.network.request.Get1004InternRequest;
import com.basedata.network.request.Get1004SalaryRequest;
import com.basedata.network.request.Get1004ScaleRequest;
import com.basedata.network.request.Get1004SchoolSearchRequest;
import com.basedata.network.request.Get1004StageRequest;
import com.basedata.network.request.Get1121OverseasDurationRequest;
import com.basedata.network.request.Get1121OverseasLanguageRequest;
import com.basedata.network.request.GetBusinessDistrictRequest;
import com.basedata.network.request.GetCityPositionRequest;
import com.basedata.network.request.GetConfigRequest;
import com.basedata.network.request.GetDataRequest;
import com.basedata.network.request.GetDistanceRequest;
import com.basedata.network.request.GetSkillWordsRequest;
import com.basedata.network.request.GetSubwayRequest;
import com.basedata.network.response.BasicDataUpgradeCheckResponse;
import com.basedata.network.response.Get1004BossFilterResponse;
import com.basedata.network.response.Get1004CityResponse;
import com.basedata.network.response.Get1004DegreeResponse;
import com.basedata.network.response.Get1004DistanceFilterResponse;
import com.basedata.network.response.Get1004ExperienceResponse;
import com.basedata.network.response.Get1004GeekFilterResponse;
import com.basedata.network.response.Get1004IndustryResponse;
import com.basedata.network.response.Get1004InternResponse;
import com.basedata.network.response.Get1004SalaryResponse;
import com.basedata.network.response.Get1004ScaleResponse;
import com.basedata.network.response.Get1004SchoolSearchResponse;
import com.basedata.network.response.Get1004StageResponse;
import com.basedata.network.response.Get1121OverseasDurationResponse;
import com.basedata.network.response.Get1121OverseasLanguageResponse;
import com.basedata.network.response.GetBusinessDistrictResponse;
import com.basedata.network.response.GetCityPositionResponse;
import com.basedata.network.response.GetConfigResponse;
import com.basedata.network.response.GetDataResponse;
import com.basedata.network.response.GetDistanceResponse;
import com.basedata.network.response.GetSkillWordsResponse;
import com.basedata.network.response.GetSubwayResponse;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-03
 **/
public class BasicDataNetWrok {

    private static final String TAG = "lib_basic_data";
    private final VersionRequestCallback versionRequestCallback;

    public BasicDataNetWrok(VersionRequestCallback versionRequestCallback) {
        this.versionRequestCallback = versionRequestCallback;
    }


    /*
     * 1122 接口变更，需要传版本号
     *
     * */
    // 获取新版本以及新的文件
    public void requestMainVersion() {
        BasicDataUpgradeCheckRequest request = new BasicDataUpgradeCheckRequest(new ApiRequestCallback<BasicDataUpgradeCheckResponse>() {

            @Override
            public void handleInChildThread(ApiData<BasicDataUpgradeCheckResponse> data) {

                BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_API_SUCCESS);

                versionRequestCallback.handleAppVersion(data.resp.appUpgrade, data.resp.lastVersion, data.resp.cycleDay,
                        data.resp.upgradeUrl, data.resp.upgradeMessage, data.resp.lastVersionName, data.resp.market);

//                if (VersionController.checkNeedUpdate(DATA_BEAN_FILENAME, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_SCALE_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_HOT_CITY_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_DEGREE_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_EXPERIENCELIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_SCHOOL_LEVEL_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_BOSS_FILTER_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_GEEK_FILTER_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_INTERNAL_POSITION_LIST, data.resp.basicDataVersion)
//                        || VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2, data.resp.basicDataVersion)) {
//                    requestBasicDataNewVersion();
//                }

                BasicDataActionHelper.onApiPre(CITY_FILENAME);
                if (VersionController.checkNeedUpdate(CITY_FILENAME, data.resp.cityVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST, data.resp.cityVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE, data.resp.cityVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, data.resp.cityVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, data.resp.cityVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_HOT_CITY_LIST, data.resp.cityVersion)) {
                    BasicDataActionHelper.onApiStartReq(CITY_FILENAME);
                    request1004CityData();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(CITY_FILENAME);
                }

                BasicDataActionHelper.onApiPre(DEGREE_FILENAME);
                if (VersionController.checkNeedUpdate(DEGREE_FILENAME, data.resp.degreeVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_DEGREE_LIST, data.resp.degreeVersion)) {
                    BasicDataActionHelper.onApiStartReq(DEGREE_FILENAME);
                    requestDegree();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(DEGREE_FILENAME);
                }

                BasicDataActionHelper.onApiPre(INDUSTRY_FILENAME);
                if (VersionController.checkNeedUpdate(INDUSTRY_FILENAME, data.resp.industryVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_INDUSTRY_CONFIG2, data.resp.industryVersion)) {
                    BasicDataActionHelper.onApiStartReq(INDUSTRY_FILENAME);
                    requestIndustry();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(INDUSTRY_FILENAME);
                }

                BasicDataActionHelper.onApiPre(SCALE_FILENAME);
                if (VersionController.checkNeedUpdate(SCALE_FILENAME, data.resp.scaleVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_SCALE_LIST, data.resp.scaleVersion)) {
                    BasicDataActionHelper.onApiStartReq(SCALE_FILENAME);
                    requestScale();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(SCALE_FILENAME);
                }

                BasicDataActionHelper.onApiPre(INTERNPOSITION_FILENAME);
                if (VersionController.checkNeedUpdate(INTERNPOSITION_FILENAME, data.resp.internPositionVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_INTERNAL_POSITION_LIST, data.resp.internPositionVersion)) {
                    BasicDataActionHelper.onApiStartReq(INTERNPOSITION_FILENAME);
                    requestInternPosition();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(INTERNPOSITION_FILENAME);
                }

                BasicDataActionHelper.onApiPre(EXPERIENCE_FILENAME);
                if (VersionController.checkNeedUpdate(EXPERIENCE_FILENAME, data.resp.experienceVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_EXPERIENCELIST, data.resp.experienceVersion)) {
                    BasicDataActionHelper.onApiStartReq(EXPERIENCE_FILENAME);
                    requestExperience();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(EXPERIENCE_FILENAME);
                }

                BasicDataActionHelper.onApiPre(SCHOOLSEARCH_FILENAME);
                if (VersionController.checkNeedUpdate(SCHOOLSEARCH_FILENAME, data.resp.schoolSearchVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_SCHOOL_LEVEL_LIST, data.resp.schoolSearchVersion)) {
                    BasicDataActionHelper.onApiStartReq(SCHOOLSEARCH_FILENAME);
                    requestSchoolSearch();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(SCHOOLSEARCH_FILENAME);
                }

                BasicDataActionHelper.onApiPre(GEEKFILTERCONFIG_FILENAME);
                if (VersionController.checkNeedUpdate(GEEKFILTERCONFIG_FILENAME, data.resp.geekFilterConfigVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_GEEK_FILTER_LIST, data.resp.geekFilterConfigVersion)) {
                    BasicDataActionHelper.onApiStartReq(GEEKFILTERCONFIG_FILENAME);
                    requestGeekFitler();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(GEEKFILTERCONFIG_FILENAME);
                }

                BasicDataActionHelper.onApiPre(BOSSFILTERCONFIG_FILENAME);
                if (VersionController.checkNeedUpdate(BOSSFILTERCONFIG_FILENAME, data.resp.bossFilterConfigVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_BOSS_FILTER_LIST, data.resp.bossFilterConfigVersion)) {
                    BasicDataActionHelper.onApiStartReq(BOSSFILTERCONFIG_FILENAME);
                    requestBossFitler();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(BOSSFILTERCONFIG_FILENAME);
                }

                BasicDataActionHelper.onApiPre(SALARY_FILENAME);
                if (VersionController.checkNeedUpdate(SALARY_FILENAME, data.resp.salaryVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_SALARY_LIST, data.resp.salaryVersion)) {
                    BasicDataActionHelper.onApiStartReq(SALARY_FILENAME);
                    requestSalaryList();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(SALARY_FILENAME);
                }

                BasicDataActionHelper.onApiPre(STAGE_FILENAME);
                if (VersionController.checkNeedUpdate(STAGE_FILENAME, data.resp.stageVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_STAGE_LIST, data.resp.stageVersion)) {
                    BasicDataActionHelper.onApiStartReq(STAGE_FILENAME);
                    requestStage();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(STAGE_FILENAME);
                }

                BasicDataActionHelper.onApiPre(EXTRA_BEAN_FILENAME);
                if (VersionController.checkNeedUpdate(EXTRA_BEAN_FILENAME, data.resp.breakSilenceVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_EXTRA_DATA_LIST, data.resp.breakSilenceVersion)) {
                    BasicDataActionHelper.onApiStartReq(EXTRA_BEAN_FILENAME);
                    requestBreakSilenceNewVersion();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(EXTRA_BEAN_FILENAME);
                }
//                school.json 不再使用，故接口也不再使用
//                if (VersionController.checkNeedUpdate(BasicDataConstants.FileName.SKILL_WORD_BEAN_FILENAME, data.resp.skillWordsVersion)) {
//                    requestSkillWordNewVersion();
//                }

                BasicDataActionHelper.onApiPre(POSITION_BEAN_FILENAME);
                if (VersionController.checkNeedUpdate(POSITION_BEAN_FILENAME, data.resp.positionVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_POSITION_LIST, data.resp.positionVersion) ||
                        VersionController.checkPositionCityChanged(String.valueOf(data.resp.positionCityCode))) {
                    BasicDataActionHelper.onApiStartReq(POSITION_BEAN_FILENAME);
                    requestPositionNewVersion();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(POSITION_BEAN_FILENAME);
                }

                BasicDataActionHelper.onApiPre(BUSINESS_DISTRICT_FOR_GEEK);
                if (VersionController.checkNeedUpdate(BUSINESS_DISTRICT_FOR_GEEK, data.resp.businessDistrictVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_GEEK_DISTRICK_LIST, data.resp.businessDistrictVersion)) {
                    BasicDataActionHelper.onApiStartReq(BUSINESS_DISTRICT_FOR_GEEK);
                    requestBusinessDistrictDataNewVersion(0);
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(BUSINESS_DISTRICT_FOR_GEEK);
                }

                BasicDataActionHelper.onApiPre(BUSINESS_DISTRICT);
                if (VersionController.checkNeedUpdate(BUSINESS_DISTRICT, data.resp.businessDistrictVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_BOSS_DISTRICT_LIST, data.resp.businessDistrictVersion)) {
                    BasicDataActionHelper.onApiStartReq(BUSINESS_DISTRICT);
                    requestBusinessDistrictDataNewVersion(1);
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(BUSINESS_DISTRICT);
                }

                BasicDataActionHelper.onApiPre(SUBWAY);
                if (VersionController.checkNeedUpdate(SUBWAY, data.resp.subwayVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_SUBWAY_LIST, data.resp.subwayVersion)) {
                    BasicDataActionHelper.onApiStartReq(SUBWAY);
                    requestSubwayDataNewVersion();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(SUBWAY);
                }

                BasicDataActionHelper.onApiPre(DISTANCE);
                if (VersionController.checkNeedUpdate(DISTANCE, data.resp.distanceFilterVersion)
                        || VersionController.checkNeedUpdate(KEY_MMKV_DISTANCE_LIST, data.resp.distanceFilterVersion)) {
                    BasicDataActionHelper.onApiStartReq(DISTANCE);
                    requestDistanceDataNewVersion();
                } else {
                    BasicDataActionHelper.onApiCheckNotNeedUpdate(DISTANCE);
                }
            }

            @Override
            public void onSuccess(ApiData<BasicDataUpgradeCheckResponse> data) {


            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.info(TAG, "获取版本失败", reason.getErrReason());
                BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_API_FAILURE);
            }
        });
        request.status = 3;
        request.configVersion = getAllVersionsString();
        // {"cityVersion":"3.8","scaleVersion":"1.0","degreeVersion":"1.2","experienceVersion":"1.0","geekFilterConfigVersion":"1.1","bossFilterConfigVersion":"1.1","industryVersion":"2.6","internPositionVersion":"8.9","schoolSearchVersion":"1.2","stageVersion":"1.0","salaryVersion":"1.0","extraDataVersion":"6.1","skillWordVersion":"","positionVersion":"36.3","business_district_for_geekVersion":"18.3","business_districtVersion":"18.3","subwayVersion":"6.5","distanceFilterVersion":"1.5"}//
        HttpExecutor.execute(request);


    }


    private String getAllVersionsString() {
        final JSONObject jsonObject = new JSONObject();

        List<String> strings = Arrays.asList(

                BasicDataConstants.FileName.CITY_FILENAME,
                BasicDataConstants.FileName.SCALE_FILENAME,
                BasicDataConstants.FileName.DEGREE_FILENAME,
                BasicDataConstants.FileName.EXPERIENCE_FILENAME,
                BasicDataConstants.FileName.GEEKFILTERCONFIG_FILENAME,
                BasicDataConstants.FileName.BOSSFILTERCONFIG_FILENAME,
                BasicDataConstants.FileName.INDUSTRY_FILENAME,
                BasicDataConstants.FileName.INTERNPOSITION_FILENAME,
                BasicDataConstants.FileName.SCHOOLSEARCH_FILENAME,
                BasicDataConstants.FileName.STAGE_FILENAME,
                BasicDataConstants.FileName.SALARY_FILENAME,
                BasicDataConstants.FileName.EXTRA_BEAN_FILENAME,
//                BasicDataConstants.FileName.SKILL_WORD_BEAN_FILENAME,
                BasicDataConstants.FileName.POSITION_BEAN_FILENAME,
                BasicDataConstants.FileName.BUSINESS_DISTRICT,
                BasicDataConstants.FileName.SUBWAY,
                BasicDataConstants.FileName.DISTANCE


        );

        for (String string : strings) {
            String fileVersionStr = SpManager.get().global(string + VersionController.VERSION_LATEFIX).getString(string, "");
            try {
                String stringVersion = "";
                if (string.contains(".json")) {
                    stringVersion = string.replace(".json", "Version");
                }

                if (stringVersion.contains("business_district")) {
                    stringVersion = stringVersion.replace("business_district", "businessDistrict");
                }


                if (stringVersion.contains("extraData")) {
                    stringVersion = stringVersion.replace("extraData", "breakSilence");
                }


//                if (stringVersion.contains("skillWord")) {
//                    stringVersion = stringVersion.replace("skillWord", "skillWords");
//                }

                if (fileVersionStr.contains(VersionController.SPLIT_TAG)) {
                    List<String> array = AssetFileUtils.splitStrToList(fileVersionStr, VersionController.SPLIT_TAG);
                    if (array.size() == 2) {
                        jsonObject.put(stringVersion, array.get(1));
                    } else {
                        jsonObject.put(stringVersion, fileVersionStr);
                    }
                } else {
                    jsonObject.put(stringVersion, fileVersionStr);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return jsonObject.toString();
    }

    public void request1004CityData() {
        Get1004CityRequest request = new Get1004CityRequest(new ApiRequestCallback<Get1004CityResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004CityResponse> data) {
                super.handleInChildThread(data);
                Get1004CityResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(CITY_FILENAME);
                    versionRequestCallback.handleCityResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(CITY_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004CityResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "city.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "city.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "city.json").report();
                BasicDataActionHelper.onApiResultFailure(CITY_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestDegree() {
        Get1004DegreeRequest request = new Get1004DegreeRequest(new ApiRequestCallback<Get1004DegreeResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004DegreeResponse> data) {
                super.handleInChildThread(data);
                Get1004DegreeResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(DEGREE_FILENAME);
                    versionRequestCallback.handelDegreeResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(DEGREE_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004DegreeResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "degree.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "extraData.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "degree.json").report();
                BasicDataActionHelper.onApiResultFailure(DEGREE_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestDistanceFilterRequest() {
        Get1004DistanceFilterRequest request = new Get1004DistanceFilterRequest(new ApiRequestCallback<Get1004DistanceFilterResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004DistanceFilterResponse> data) {
                super.handleInChildThread(data);
                Get1004DistanceFilterResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    versionRequestCallback.handleDistanceFilterResponse(data);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004DistanceFilterResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "distanceFilter.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "distanceFilter.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "distanceFilter.json").report();
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestIndustry() {
        Get1004IndustryRequest request = new Get1004IndustryRequest(new ApiRequestCallback<Get1004IndustryResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004IndustryResponse> data) {
                super.handleInChildThread(data);
                Get1004IndustryResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(INDUSTRY_FILENAME);
                    versionRequestCallback.handleIndustry(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(INDUSTRY_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004IndustryResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "industry.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "extraData.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "industry.json").report();
                BasicDataActionHelper.onApiResultFailure(INDUSTRY_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestSchoolSearch() {
        Get1004SchoolSearchRequest request = new Get1004SchoolSearchRequest(new ApiRequestCallback<Get1004SchoolSearchResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004SchoolSearchResponse> data) {
                super.handleInChildThread(data);
                Get1004SchoolSearchResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(SCHOOLSEARCH_FILENAME);
                    versionRequestCallback.handleSchoolSearch(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(SCHOOLSEARCH_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004SchoolSearchResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "schoolSearch.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "extraData.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "schoolSearch.json").report();
                BasicDataActionHelper.onApiResultFailure(SCHOOLSEARCH_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestInternPosition() {
        Get1004InternRequest request = new Get1004InternRequest(new ApiRequestCallback<Get1004InternResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004InternResponse> data) {
                super.handleInChildThread(data);
                Get1004InternResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(INTERNPOSITION_FILENAME);
                    versionRequestCallback.handleInternalPosition(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(INTERNPOSITION_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004InternResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "internPosition.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "internPosition.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "internPosition.json").report();
                BasicDataActionHelper.onApiResultFailure(INTERNPOSITION_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestScale() {
        Get1004ScaleRequest request = new Get1004ScaleRequest(new ApiRequestCallback<Get1004ScaleResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004ScaleResponse> data) {
                super.handleInChildThread(data);
                Get1004ScaleResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(SCALE_FILENAME);
                    versionRequestCallback.handleScale(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(SCALE_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004ScaleResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "scale.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "scale.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "scale.json").report();
                BasicDataActionHelper.onApiResultFailure(SCALE_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestExperience() {
        Get1004ExperienceRequest request = new Get1004ExperienceRequest(new ApiRequestCallback<Get1004ExperienceResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004ExperienceResponse> data) {
                super.handleInChildThread(data);
                Get1004ExperienceResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(EXPERIENCE_FILENAME);
                    versionRequestCallback.handleExperience(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(EXPERIENCE_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004ExperienceResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "experience.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "experience.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "experience.json").report();
                BasicDataActionHelper.onApiResultFailure(EXPERIENCE_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestGeekFitler() {
        Get1004GeekFilterRequest request = new Get1004GeekFilterRequest(new ApiRequestCallback<Get1004GeekFilterResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004GeekFilterResponse> data) {
                super.handleInChildThread(data);
                Get1004GeekFilterResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(GEEKFILTERCONFIG_FILENAME);
                    versionRequestCallback.handleGeekFiltler(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(GEEKFILTERCONFIG_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004GeekFilterResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "geekfilter.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "geekfilter.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "geekfilter.json").report();
                BasicDataActionHelper.onApiResultFailure(GEEKFILTERCONFIG_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestBossFitler() {
        Get1004BossFilterRequest request = new Get1004BossFilterRequest(new ApiRequestCallback<Get1004BossFilterResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004BossFilterResponse> data) {
                super.handleInChildThread(data);
                Get1004BossFilterResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(BOSSFILTERCONFIG_FILENAME);
                    versionRequestCallback.handelBossFilter(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(BOSSFILTERCONFIG_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004BossFilterResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "bossFilter.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "bossFilter.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "bossFilter.json").report();
                BasicDataActionHelper.onApiResultFailure(BOSSFILTERCONFIG_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestSalaryList() {
        Get1004SalaryRequest request = new Get1004SalaryRequest(new ApiRequestCallback<Get1004SalaryResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004SalaryResponse> data) {
                super.handleInChildThread(data);
                Get1004SalaryResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(SALARY_FILENAME);
                    versionRequestCallback.handleSalary(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(SALARY_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004SalaryResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "salary.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "salary.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "salary.json").report();
                BasicDataActionHelper.onApiResultFailure(SALARY_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestStage() {
        Get1004StageRequest request = new Get1004StageRequest(new ApiRequestCallback<Get1004StageResponse>() {
            @Override
            public void handleInChildThread(ApiData<Get1004StageResponse> data) {
                super.handleInChildThread(data);
                Get1004StageResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(STAGE_FILENAME);
                    versionRequestCallback.handleStage(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(STAGE_FILENAME);
                }

            }

            @Override
            public void onSuccess(ApiData<Get1004StageResponse> data) {

            }

            @Override
            public void onComplete() {
                TLog.info(TAG, "stage.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "stage.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "stage.json").report();
                BasicDataActionHelper.onApiResultFailure(STAGE_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }


    /**
     * 请求最新的扩展数据类型数据
     */
    public void requestBreakSilenceNewVersion() {
        GetDataRequest request = new GetDataRequest(new ApiRequestCallback<GetDataResponse>() {

            @Override
            public void handleInChildThread(ApiData<GetDataResponse> data) {
                super.handleInChildThread(data);
                GetDataResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(EXTRA_BEAN_FILENAME);
                    versionRequestCallback.handleExtraResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(EXTRA_BEAN_FILENAME);
                }
            }

            @Override
            public void onSuccess(ApiData<GetDataResponse> data) {
            }

            @Override
            public void onComplete() {
                L.i("extraData.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "extraData.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "extraData.json").report();
                BasicDataActionHelper.onApiResultFailure(EXTRA_BEAN_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * 请求最新的扩展数据类型数据
     */
    public void requestPositionNewVersion() {
        GetCityPositionRequest request = new GetCityPositionRequest(new ApiRequestCallback<GetCityPositionResponse>() {

            @Override
            public void handleInChildThread(ApiData<GetCityPositionResponse> data) {
                super.handleInChildThread(data);
                GetCityPositionResponse resp = data.resp;
                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(POSITION_BEAN_FILENAME);
                    versionRequestCallback.handlePositionResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(POSITION_BEAN_FILENAME);
                }
            }

            @Override
            public void onSuccess(ApiData<GetCityPositionResponse> data) {
            }

            @Override
            public void onComplete() {
                L.i("position.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "position.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "position.json").report();
                BasicDataActionHelper.onApiResultFailure(POSITION_BEAN_FILENAME);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestBusinessDistrictDataNewVersion(int role) { // 0:geek 1:boss
        GetBusinessDistrictRequest request = new GetBusinessDistrictRequest(new ApiRequestCallback<GetBusinessDistrictResponse>() {
            @Override
            public void handleInChildThread(ApiData<GetBusinessDistrictResponse> data) {
                super.handleInChildThread(data);
                GetBusinessDistrictResponse resp = data.resp;

                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(role == 0 ? BUSINESS_DISTRICT_FOR_GEEK : BUSINESS_DISTRICT);
                    versionRequestCallback.handleBusinessDistrictResponse(data, role);
                } else {
                    BasicDataActionHelper.onApiResultNull(role == 0 ? BUSINESS_DISTRICT_FOR_GEEK : BUSINESS_DISTRICT);
                }
            }

            @Override
            public void onSuccess(ApiData<GetBusinessDistrictResponse> data) {
            }

            @Override
            public void onComplete() {
                L.i("role : " + role + "文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "role : " + role + "文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "version").report();
                BasicDataActionHelper.onApiResultFailure(role == 0 ? BUSINESS_DISTRICT_FOR_GEEK : BUSINESS_DISTRICT);
            }
        });
        request.identity = role;
        //noinspection unchecked
        HttpExecutor.execute(request);
    }

    public void requestSubwayDataNewVersion() {
        GetSubwayRequest request = new GetSubwayRequest(new ApiRequestCallback<GetSubwayResponse>() {

            @Override
            public void handleInChildThread(ApiData<GetSubwayResponse> data) {
                GetSubwayResponse resp = data.resp;
                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(SUBWAY);
                    versionRequestCallback.handleSubwayResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(SUBWAY);
                }
            }

            @Override
            public void onSuccess(ApiData<GetSubwayResponse> data) {
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "subway.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "subway.json").report();
                BasicDataActionHelper.onApiResultFailure(SUBWAY);
            }
        });
        HttpExecutor.execute(request);
    }

    public void requestDistanceDataNewVersion() {
        GetDistanceRequest request = new GetDistanceRequest(new ApiRequestCallback<GetDistanceResponse>() {

            @Override
            public void handleInChildThread(ApiData<GetDistanceResponse> data) {
                super.handleInChildThread(data);
                GetDistanceResponse resp = data.resp;
                if (resp != null && resp.code == 0) {
                    BasicDataActionHelper.onApiReqSuccess(DISTANCE);
                    versionRequestCallback.handleDistanceResponse(data);
                } else {
                    BasicDataActionHelper.onApiResultNull(DISTANCE);
                }
            }

            @Override
            public void onSuccess(ApiData<GetDistanceResponse> data) {
            }

            @Override
            public void onComplete() {
                L.i("distance.json文件下载成功");
            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "distance.json文件下载失败：%s", reason.getErrReason());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, BASEDATA_EXCEPTION_DOWNLOAD).param("p2", "distance.json").report();
                BasicDataActionHelper.onApiResultFailure(DISTANCE);
            }
        });
        HttpExecutor.execute(request);
    }


    //【驻外岗位偏好】获取地区语言数据
    public static void requestOverseasLanguage(String countryStr, int needAll, BaseDataReqeustCallback<Get1121OverseasLanguageResponse> callback) {

        Get1121OverseasLanguageRequest request = new Get1121OverseasLanguageRequest(new ApiRequestCallback<Get1121OverseasLanguageResponse>() {

            @Override
            public void onStart() {
                if (callback != null) {
                    callback.onStart();
                }
            }

            @Override
            public void onSuccess(ApiData<Get1121OverseasLanguageResponse> data) {
                if (callback != null) {
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                if (callback != null) {
                    callback.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onfailed(reason);
                }
            }
        });

        request.countryCodeList = countryStr;
        request.needAll = needAll;
        HttpExecutor.execute(request);
    }


    //【驻外岗位偏好】获取时长数据
    public static void requestOverseasDuration(BaseDataReqeustCallback<Get1121OverseasDurationResponse> callback) {
        Get1121OverseasDurationRequest request = new Get1121OverseasDurationRequest(new ApiRequestCallback<Get1121OverseasDurationResponse>() {


            @Override
            public void onStart() {
                if (callback != null) {
                    callback.onStart();
                }
            }

            @Override
            public void onSuccess(ApiData<Get1121OverseasDurationResponse> data) {
                if (callback != null) {
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                if (callback != null) {
                    callback.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onfailed(reason);
                }
            }
        });

        HttpExecutor.execute(request);
    }


    public interface BaseDataReqeustCallback<T> {


        void onStart();

        void onSuccess(ApiData<T> data);

        void onComplete();

        void onfailed(ErrorReason reason);
    }
}
