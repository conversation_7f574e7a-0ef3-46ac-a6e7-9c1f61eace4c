package com.basedata.core;

import android.text.TextUtils;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static com.basedata.core.ExceptionConstants.ACTION_BASEDATA_EXCEPTION;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_GETCURRENTVERSION_NULL;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_UPDATECURRENTVERSION_NULL;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_UPDATECURRENTVERSION_SUCCESS;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_VERSION_CHECK;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_VERSION_DESGRADE;

/**
 * <AUTHOR>
 * @date 2020-01-07
 **/
public class VersionController {


    public static final String SPLIT_TAG = "##";
    public static final int VERSION_BAK_SIZE = 2; // 备份数量
    public static final String VERSION_LATEFIX = "_versionlist";
    public static final String CODE_POSITION_CITY = "code_position_city";

    /*版本检查*/
    public static final int VERSION_CODE_ERROR = -1; // 版本错误 解析版本异常
    public static final int VERSION_CODE_EQUALS = 100; // 已包含当前版本
    public static final int VERSION_CODE_INIT_VERSION = 101; // 版本_初始化
    public static final int VERSION_CODE_NEW_VERSION = 102; // 版本_新增
    public static final int VERSION_CODE_COVER_VERSION = 103; // 版本_覆盖旧版本
    /*版本控制策略*/
    /*0：自动降级：向旧版本降级*/
    public static final int VERSION_CONTROL_POLICY_AUTO = 0;
    /*1：指定versionCode,降级到指定版本*/
    public static final int VERSION_CONTROL_POLICY_EXACT = 1;

    /**
     * 检查版本号: 维护版本号队列，队列容量 ： VERSION_BAK_SIZE
     **/
    public static synchronized int checkFileVersion(String fileName, float version) {

        String versionCode = Float.toString(version);
        String fileVersionStr = SpManager.get().global(fileName + VERSION_LATEFIX).getString(fileName, "");
        if (TextUtils.isEmpty(fileVersionStr)) {
            TLog.info(BASEDATA_LOG_VERSION_CHECK, "initVersion, filename=[%s], version=[%f]", fileName, version);
            return VERSION_CODE_INIT_VERSION;
        } else {
            List<String> strList = splitStringToList(fileVersionStr);
            if (LList.isEmpty(strList)) {
                //"版本解析异常 - 01 ： 转换数组"
                TLog.info(BASEDATA_LOG_VERSION_CHECK, "getVersionListFail, filename=[%s], version=[%f]", fileName, version);
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_VERSIONCHECK).param("p3", "解析版本列表失败").param("p2", fileName).report();
                return VERSION_CODE_ERROR;
            } else {
                if (strList.contains(versionCode)) {
                    // 包含该版本
                    TLog.info(BASEDATA_LOG_VERSION_CHECK, "containsVersion, filename=[%s], version=[%f]", fileName, version);
                    return VERSION_CODE_EQUALS;
                } else {
                    if (strList.size() < VERSION_BAK_SIZE) {
                        // 直接写入
                        TLog.info(BASEDATA_LOG_VERSION_CHECK, "writeVersion, filename=[%s], version=[%f]", fileName, version);
                        return VERSION_CODE_NEW_VERSION;
                    } else {
                        // 删除最老版本，加入新版本
                        TLog.info(BASEDATA_LOG_VERSION_CHECK, "converVersion, filename=[%s], version=[%f]", fileName, version);
                        return VERSION_CODE_COVER_VERSION;
                    }
                }
            }
        }
    }

    public static void forceClearData(String fileName) {
        SpManager.get().global(fileName + VERSION_LATEFIX).edit().clear().apply();
    }


    /* 检查是否需要更新*/
    public static boolean checkNeedUpdate(String fileName, float version) {
        int versionStatusCode = checkFileVersion(fileName, version);
        return checkNeedUpdate(versionStatusCode);
    }

    /**
     * 职类随城市变化，所以需要判断城市是否变化拉去最新数据
     */
    public static boolean checkPositionCityChanged(String updateCityCode) {
        String lastCityCode = SpManager.get().global(CODE_POSITION_CITY).getString(CODE_POSITION_CITY, "");
        if (!LText.equal(updateCityCode, lastCityCode)) {
            return true;
        }
        return false;
    }

    public static void savePositionCode(String updateCityCode) {
        SpManager.get().global(CODE_POSITION_CITY).edit().putString(CODE_POSITION_CITY, updateCityCode).apply();

    }


    public static boolean checkNeedUpdate(int versionCheckResultCode) {
        if (versionCheckResultCode == VersionController.VERSION_CODE_INIT_VERSION || versionCheckResultCode == VersionController.VERSION_CODE_NEW_VERSION ||
                versionCheckResultCode == VersionController.VERSION_CODE_COVER_VERSION) {
            return true;
        }
        if (versionCheckResultCode == VersionController.VERSION_CODE_ERROR) {
            return true;
        }
        return false;
    }


    /**
     * 更新版本号
     *
     * @param fileName          data.json extra.json
     * @param version           版本号
     * @param versionStutusCode checkFileVersion();
     **/

    public static synchronized boolean updateVersionQueue(String fileName, float version, int versionStutusCode) {

        try {
            String versionCode = Float.toString(version);
            if (versionStutusCode == VERSION_CODE_ERROR) { //版本号解析错误
                SpManager.get().global(fileName + VERSION_LATEFIX).edit().clear().apply();
                boolean success = SpManager.get().global(fileName + VERSION_LATEFIX).edit().putString(fileName, versionCode).commit();
                if (!success) {
                    TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_NULL, "[error] : versioncode update fail, filename=[%s] , version=[%f] ,status=[%s] ", fileName, version, String.valueOf(versionStutusCode));
                    return false;
                }
            }

            if (versionStutusCode == VERSION_CODE_INIT_VERSION) { // 首次写入版本号
                boolean success = SpManager.get().global(fileName + VERSION_LATEFIX).edit().putString(fileName, versionCode).commit();
                if (!success) {
                    TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_NULL, "[error] : versioncode update fail, filename=[%s] , version=[%f] ,status=[%s] ", fileName, version, String.valueOf(versionStutusCode));
                    return false;
                }
            }

            List<String> strList = getVersionList(fileName);

            if (versionStutusCode == VERSION_CODE_NEW_VERSION) {
                strList.add(versionCode);
                boolean success = SpManager.get().global(fileName + VERSION_LATEFIX).edit().putString(fileName, listConnectStr(strList)).commit();
                if (!success) {
                    TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_NULL, "[error] : versioncode update fail, filename=[%s] , version=[%f] ,status=[%s] ", fileName, version, String.valueOf(versionStutusCode));
                    return false;
                }
            }

            if (versionStutusCode == VERSION_CODE_COVER_VERSION) {
                LinkedList linkedList = new LinkedList(strList);
                if (linkedList.size() == VERSION_BAK_SIZE) {
                    linkedList.removeFirst(); // 去除最旧的版本号，加入最新版本号
                    linkedList.addLast(versionCode);
                }
                boolean success = SpManager.get().global(fileName + VERSION_LATEFIX).edit().putString(fileName, listConnectStr(linkedList)).commit();
                if (!success) {
                    TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_NULL, "[error] : versioncode update fail, filename=[%s] , version=[%f] ,status=[%s] ", fileName, version, String.valueOf(versionStutusCode));
                    return false;
                }
            }
        } catch (Throwable e) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_VERSIONUPDATE).param("p3", e.toString()).param("p2", fileName).report();
        }
        TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_SUCCESS, "[success] : versioncode update success!, filename=[%s] , version=[%f] ,status=[%s] ", fileName, version, String.valueOf(versionStutusCode));
        return true;
    }

//    public static String getVersionViaPolicy(int policyType) {
//        switch (policyType) {
//            case VERSION_CONTROL_POLICY_EXACT: // 精准降级
//            case VERSION_CONTROL_POLICY_AUTO: // 自动降级，从最新版本依次降级
//                return "";
//
//
//        }
//        return "";
//    }

    // 获取版本列表
    public static List<String> getVersionList(String fileName) {
        String fileVersionStr = SpManager.get().global(fileName + VERSION_LATEFIX).getString(fileName, "");
        List<String> strList = splitStringToList(fileVersionStr);
        return strList;
    }


    // 获取最老版本
    public static String getOldestVersion(String fileIndex) {
        LinkedList<String> linkedList = new LinkedList(getVersionList(fileIndex));
        return linkedList.getFirst();
    }

    // 获取最新版本
    public static String getCurrentVersion(String fileIndex) {
        try {
            List<String> strList = getVersionList(fileIndex);
            if (LList.isEmpty(strList)) {
                return "";
            }
            LinkedList<String> linkedList = new LinkedList<>(strList);
            // 如果做定制版本切换，需要从本地取相关版本号并看是否有缓存
//        String exactVersion = SpManager.get().global("exact_version_fileIndex").getString("versioncode", "");
//        if (!LText.isEmptyOrNull(exactVersion)) {
//            if (linkedList.contains(exactVersion)) {
//                return exactVersion;
//            }
//        }
            String versionCodeStr = linkedList.getLast();
            if (TextUtils.isEmpty(versionCodeStr)) {
                // 没有找到版本相关的信息
                TLog.info(BASEDATA_LOG_GETCURRENTVERSION_NULL, "[error] : versioncode not found, filename=[%s] ", fileIndex);
            }
            return versionCodeStr;
        } catch (Exception e) {
            return "";
        }
    }

    public static String deCreaseGrade(String fileIndex, String versionCode) {
        LinkedList<String> linkedList = new LinkedList(getVersionList(fileIndex));
        String toVersion = "";

        if (linkedList.size() > 1) {
            for (int i = 0; i < linkedList.size(); i++) {
                if (linkedList.get(i).equals(versionCode) && i > 0) {
                    toVersion = linkedList.get(i - 1);
                    ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_DESGRADE)
                            .param("p5", "数据降级")
                            .param("p2", versionCode)
                            .param("p3", toVersion)
                            .param("p4", fileIndex)
                            .report();
                }
            }
        }
        if (linkedList.contains(versionCode)) {
            linkedList.remove(versionCode);

            boolean success = SpManager.get().global(fileIndex + VERSION_LATEFIX).edit().putString(fileIndex, listConnectStr(linkedList)).commit();
            if (!success) {
                TLog.info(BASEDATA_LOG_UPDATECURRENTVERSION_NULL, "[error] : versioncode update fail, filename=[%s] , version=[%s]", fileIndex, versionCode);
            }

        }
        TLog.info(BASEDATA_LOG_VERSION_DESGRADE, "versionDesgrade, filename=[%s], fromVersion=[%s],toVersion=[%s]", fileIndex, versionCode, toVersion);
        return toVersion;
    }

//    // 获取指定版本
//    public static String getCurrentVersion(String fileIndex,String version) {
//        LinkedList<String> linkedList = new LinkedList(getVersionList(fileIndex));
//        linkedList.get(linkedList.indexOf(version));
//        return linkedList.get();Live
//    }

    private static List splitStringToList(String fileVersionStr) {
        return AssetFileUtils.splitStrToList(fileVersionStr, SPLIT_TAG);
    }

    private static String listConnectStr(List<String> strings) {
        return AssetFileUtils.listConnectStr(sortVersionStrings(strings), SPLIT_TAG);
    }

    private static List<String> sortVersionStrings(List<String> strings) {
        List<Float> floatList = new ArrayList();
        List<String> strSorted = new ArrayList<>();
        try {
            if (!LList.isEmpty(strings)) {
                for (String elem : strings) {
                    float elemFloat = Float.parseFloat(elem);
                    floatList.add(elemFloat);
                }
            }
            Collections.sort(floatList);
            if (!LList.isEmpty(floatList)) {
                for (Float elemFloat : floatList) {
                    String elemString = elemFloat.toString();
                    strSorted.add(elemString);
                }
            }
            return strSorted;
        } catch (Throwable e) {

        }

        return strings;
    }
}