
package com.basedata.businessdata;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.basedata.core.BasicDataActionHelper;
import com.basedata.core.BasicDataCompareUtils;
import com.basedata.core.BasicDataConstants;
import com.basedata.core.ExceptionConstants;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONArray;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_CITY_LIST_PROVINCE_CITIES;
import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_HOT_CITY_LIST;
import static com.basedata.core.ExceptionConstants.ACTION_BASEDATA_EXCEPTION;

import android.content.Context;
import android.text.TextUtils;

/**
 * <AUTHOR>
 * @date 2019-08-20
 * 城市相关数据
 * 省 市 区
 **/
public class CityDataFactory extends BusinessDataFactory<LevelBean> {

    public static final int CITY_LIST_CODE_BASE = 5;
    public static final int HOT_CITY_LIST_CODE = 3;
    public static final int CITY_LIST_CODE_BASE_PROVINCE = 501;
    public static final int CITY_LIST_CODE_BASE_PROVINCE_CITYS = 503;
    public static final int CITY_LIST_CODE_BASE_PROVINCE_CAPITAL = 502;

    private SoftReference<List<LevelBean>> mOriginCityListReference;
    private SoftReference<List<LevelBean>> mHotCityListReference;
    private SoftReference<List<LevelBean>> mSortCityListReference;
    private SoftReference<List<LevelBean>> mProvinceListReference;
    private SoftReference<List<LevelBean>> mCapitalListReference;
    private SoftReference<List<LevelBean>> mAllCitiesReference;

    private SoftReference<List<LevelBean>> mSortNormalCityListReference;
    private SoftReference<List<LevelBean>> cityListReference;

    private CityDataFactory() {
    }

    private static volatile CityDataFactory instance;

    public static final CityDataFactory getInstance() {

        if (instance == null) {
            synchronized (CityDataFactory.class) {
                if (instance == null) {
                    instance = new CityDataFactory();
                }
            }
        }
        return instance;
    }

    /**
     * 存储城市列表
     *
     * @param jsonArray : dataBase.json中的jsonArray
     * @code CITY_LIST_CODE_BASE = 5
     */
    @Nullable
    @WorkerThread
    public boolean saveCityListJson(JSONArray jsonArray, float versionCode) {
        List<LevelBean> list = convertToBusinessList(jsonArray, INDEX_STRUCT_FIRST, TYPE_LEVEL_BEAN, KEY_MMKV_CITY_LIST);
        return saveListJson(list, KEY_MMKV_CITY_LIST, versionCode);
    }

    /**
     * 存储热门城市列表
     *
     * @code HOT_CITY_LIST_CODE = 3
     */
    @Nullable
    @WorkerThread
    public boolean saveHotCityList(JSONArray jsonArray, float versionCode) {
        List<LevelBean> list = convertToBusinessList(jsonArray, INDEX_STRUCT_FIRST, TYPE_LEVEL_BEAN, KEY_MMKV_HOT_CITY_LIST);
        return saveListJson(list, KEY_MMKV_HOT_CITY_LIST, versionCode);
    }

    /**
     * 存储省份信息
     */
    @Nullable
    @WorkerThread
    public boolean saveProvinceList(float versionCode) {
        List<LevelBean> cityList = getCityList();
        List<LevelBean> provinceList = new ArrayList<>();
        if (!LList.isEmpty(cityList)) {
            for (LevelBean province : cityList) {
                if (!LList.isEmpty(province.subLevelModeList)) {
                    province.subLevelModeList.clear();
                }
                provinceList.add(province);
            }
            if (!LList.isEmpty(provinceList)) {
//                JSONArray jsonArray = new JSONArray(provinceList);
//                List<LevelBean> list = convertToBusinessList(jsonArray, CITY_LIST_CODE_BASE_PROVINCE, INDEX_STRUCT_FIRST);
                return saveListJson(provinceList, KEY_MMKV_CITY_LIST_PROVINCE, versionCode);
            } else {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                        .param("p3", KEY_MMKV_CITY_LIST_PROVINCE)
                        .param("p4", "provinceList_isEmpty")
                        .report();
            }
            return false;
        } else {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                    .param("p3", KEY_MMKV_CITY_LIST_PROVINCE)
                    .param("p4", "cityList_isEmpty")
                    .report();
        }
        return false;
    }


    /**
     * 存储所有市的信息包括地级市，方便查找
     */
    @Nullable
    @WorkerThread
    public boolean saveAllCities(float versionCode) {
        List<LevelBean> cityList = getCityList();
        List<LevelBean> allCityList = new ArrayList<>();
        if (!LList.isEmpty(cityList)) {
            for (LevelBean province : cityList) {
                if (!LList.isEmpty(province.subLevelModeList)) {
                    allCityList.addAll(province.subLevelModeList);
                }
            }
        } else {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                    .param("p3", KEY_MMKV_CITY_LIST_PROVINCE_CITIES)
                    .param("p4", "cityList_isEmpty")
                    .report();
        }

        if (!LList.isEmpty(allCityList)) {
            JSONArray jsonArray = new JSONArray(allCityList);

//                com.alibaba.fastjson.JSONArray jsonArray = new com.alibaba.fastjson.JSONArray();
//                List<LevelBean> list = convertToBusinessList(jsonArray, CITY_LIST_CODE_BASE_PROVINCE_CITYS, INDEX_STRUCT_FIRST);
            return saveListJson(allCityList, KEY_MMKV_CITY_LIST_PROVINCE_CITIES, versionCode);
        } else {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                    .param("p3", KEY_MMKV_CITY_LIST_PROVINCE_CITIES)
                    .param("p4", "allCityList_isEmpty")
                    .report();
        }
        return false;

    }

    /**
     * 存储直辖市，省会信息
     */
    @Nullable
    @WorkerThread
    public boolean saveProvinceCapitalList(float versionCode) {
        List<LevelBean> cityList = getCityList();
        List<LevelBean> provinceCapitalCities = new ArrayList<>();

        if (!LList.isEmpty(cityList)) {
            for (LevelBean province : cityList) {
                if (!LList.isEmpty(province.subLevelModeList)) {
                    LevelBean capitalCity = province.subLevelModeList.get(0);
                    provinceCapitalCities.add(capitalCity);
                }

            }
            if (!LList.isEmpty(provinceCapitalCities)) {
//                JSONArray jsonArray = new JSONArray(provinceCapitalCities);
                //CITY_LIST_CODE_BASE_PROVINCE_CAPITAL = 502
//                List<LevelBean> list = convertToBusinessList(jsonArray, INDEX_STRUCT_FIRST, TYPE_LEVEL_BEAN);
                return saveListJson(provinceCapitalCities, KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, versionCode);
            } else {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                        .param("p3", KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL)
                        .param("p4", "provinceCapitalCities_isEmpty")
                        .report();
            }
            return false;
        } else {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVE_CITY_DATA)
                    .param("p3", KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL)
                    .param("p4", "cityList_isEmpty")
                    .report();
        }
        return false;
    }

    /**
     * 获取省城市列表
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getCityList() {

        return getJsonList(mOriginCityListReference, KEY_MMKV_CITY_LIST, LevelBean.class);
    }

    /**
     * 获取热门城市列表
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getHotCityList() {

        return getJsonList(mHotCityListReference, KEY_MMKV_HOT_CITY_LIST, LevelBean.class);
    }

    /**
     * 获取所有城市，包括地级，县级市
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getAllCities(@Nullable Context context) {

        if (mAllCitiesReference != null && !LList.isEmpty(mAllCitiesReference.get())) {
            return mAllCitiesReference.get();
        }
        List<LevelBean> list = getJsonList(mAllCitiesReference, KEY_MMKV_CITY_LIST_PROVINCE_CITIES, LevelBean.class);

        if (LList.isEmpty(list)) {
            // 兜底逻辑一：从 KEY_MMKV_CITY_LIST 中获取源数据，组装业务需要的数据
            String catchFrom = "sourceData";
            list = tryGetAllCities(getCityList());

            if (LList.isEmpty(list)) {
                // 兜底逻辑二：从 Assets 中获取源数据，组装业务需要的数据
                catchFrom = "assetsData";
                List<LevelBean> cityList = getAssetsDataList(context, BasicDataConstants.FileName.CITY_FILENAME, "city", KEY_MMKV_CITY_LIST, TYPE_LEVEL_BEAN);
                list = tryGetAllCities(cityList);
            }
            BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, LList.isEmpty(list) ? 0 : 1 , catchFrom);
        }
        mAllCitiesReference = new SoftReference<>(list);
        return list;
    }

    /**
     * 1311.92372【基础数据】从源数据中，重新组装 KEY_MMKV_CITY_LIST_PROVINCE_CITIES 业务数据
     *
     * @param cityList 源数据
     * @return 业务数据
     */
    @NonNull
    private List<LevelBean> tryGetAllCities(@Nullable List<LevelBean> cityList) {
        List<LevelBean> resultList = new ArrayList<>();
        try {
            if (LList.isEmpty(cityList)) {
                BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, 2, "empty_cityList");
                return resultList;
            }
            for (LevelBean province : cityList) {
                if (!LList.isEmpty(province.subLevelModeList)) {
                    resultList.addAll(province.subLevelModeList);
                }
            }
        } catch (Exception e) {
            TLog.error("", e.getMessage());
            BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE_CITIES, 3,  e.toString());
        }
        return resultList;
    }

    /**
     * 按首字母firstChar排序后的城市列表
     */

    @Nullable
    @WorkerThread
    public List<LevelBean> getSortCityList(@Nullable Context context) {

        if (mSortCityListReference != null && !LList.isEmpty(mSortCityListReference.get())) {
            return mSortCityListReference.get();
        }
        List<LevelBean> tempCityList = getAllCities(context);
        Collections.sort(tempCityList, new Comparator<LevelBean>() {
            @Override
            public int compare(LevelBean o1, LevelBean o2) {
                return BasicDataCompareUtils.compare(o1, o2);
            }
        });
        mSortCityListReference = new SoftReference(tempCityList);
        return tempCityList;
    }

    /**
     * 获取所有省份信息
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getProvinces(@Nullable Context context) {
        List<LevelBean> list = getJsonList(mProvinceListReference, KEY_MMKV_CITY_LIST_PROVINCE, LevelBean.class);

        if (LList.isEmpty(list)) {
            // 兜底逻辑一：从 KEY_MMKV_CITY_LIST 中获取源数据，组装业务需要的数据
            String catchFrom = "sourceData";
            list = tryGetProvinceList(getCityList());

            if (LList.isEmpty(list)) {
                // 兜底逻辑二：从 Assets 中获取源数据，组装业务需要的数据
                catchFrom = "assetsData";
                List<LevelBean> cityList = getAssetsDataList(context, BasicDataConstants.FileName.CITY_FILENAME, "city", KEY_MMKV_CITY_LIST, TYPE_LEVEL_BEAN);
                list = tryGetAllCities(cityList);
            }
            BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE, LList.isEmpty(list) ? 0 : 1 , catchFrom);
        }
        return list;
    }

    /**
     * 1311.92372【基础数据】从源数据中，重新组装 KEY_MMKV_CITY_LIST_PROVINCE 业务数据
     *
     * @param cityList 源数据
     * @return 业务数据
     */
    @NonNull
    private List<LevelBean> tryGetProvinceList(@Nullable List<LevelBean> cityList) {
        List<LevelBean> resultList = new ArrayList<>();
        try {
            if (LList.isEmpty(cityList)) {
                BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE, 2, "empty_cityList");
                return resultList;
            }
            for (LevelBean province : cityList) {
                if (LList.isNotEmpty(province.subLevelModeList)) {
                    province.subLevelModeList.clear();
                }
                resultList.add(province);
            }
        } catch (Exception e) {
            TLog.error("", e.getMessage());
            BasicDataActionHelper.onCityDataGetAction(KEY_MMKV_CITY_LIST_PROVINCE, 3,  e.toString());
        }
        return resultList;
    }

    /**
     * 根据Id取得特定省份信息
     */
    @Nullable
    @WorkerThread
    public LevelBean getProvinceById(Long provinceId) {
        List<LevelBean> provinceList = getProvinces(null);
        if (!LList.isEmpty(provinceList)) {
            for (LevelBean province : provinceList) {
                if (province.code == provinceId) {
                    return province;
                }
            }
        }
        return null;
    }

    /**
     * 根据Id取得特定省份信息
     */
    @Nullable
    @WorkerThread
    public LevelBean getProvinceByName(@Nullable Context context, String name) {
        List<LevelBean> provinceList = getProvinces(context);
        if (!LList.isEmpty(provinceList)) {

            for (LevelBean province : provinceList) {
                if (!TextUtils.isEmpty(province.name)) {
                    if (province.name.equals(name) || province.name.contains(name) || name.contains(province.name)) {
                        return province;
                    }
                }

            }
        }
        return null;
    }

    /**
     * 获取所有省会，直辖市
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getProvincesCapitals() {
        return getJsonList(mCapitalListReference, KEY_MMKV_CITY_LIST_PROVINCE_CAPITAL, LevelBean.class);
    }

    public boolean isProvincialCapital(long cityCode) {
        boolean isProvincialCapital = false;

        List<LevelBean> provincesCapitals = getProvincesCapitals();
        if (provincesCapitals != null) {
            for (LevelBean provincesCapital : provincesCapitals) {
                if (cityCode == provincesCapital.code) {
                    return isProvincialCapital;
                }
            }
        }

        return isProvincialCapital;
    }

    /**
     * 获取某省的所有市
     *
     * @param provinceId
     * @return
     */
    public List<LevelBean> getCitiesById(long provinceId) {

        List<LevelBean> allProvinces = getCityList();
        List<LevelBean> theProvinceCities = new ArrayList<>();

        if (!LList.isEmpty(allProvinces)) {
            for (LevelBean provinceInfo : allProvinces) {
                if (provinceInfo.code == provinceId) {
                    theProvinceCities = provinceInfo.subLevelModeList;
                    break;
                }
            }
        }
        return theProvinceCities;
    }

    /**
     * 获取某市 所有区
     *
     * @param cityId
     * @param provinceId
     * @return
     */
    public List<LevelBean> getAreasById(long provinceId, long cityId) {

        List<LevelBean> theCityAreaList = new ArrayList<>();
        List<LevelBean> theProvinceCities = getCitiesById(provinceId);
        if (!LList.isEmpty(theProvinceCities)) {
            for (LevelBean cityInfo : theProvinceCities) {
                if (cityInfo.code == cityId) {
                    theCityAreaList = cityInfo.subLevelModeList;
                    break;
                }
            }
        }
        return theCityAreaList;
    }

    /**
     * 返回一个Map
     *
     * @param provinceName
     * @param cityName
     * @param areaName
     * @return
     */
    public Map<Integer, LevelBean> getProvinceCityArea(@Nullable Context context,
                                                       @Nullable String provinceName,
                                                       @Nullable String cityName,
                                                       @Nullable String areaName) {
        Map<Integer, LevelBean> beans = new HashMap<>();

        //---------------------------匹配省
        List<LevelBean> provinces = getProvinces(context);
        if (provinces == null) {
            return beans;
        }
        LevelBean province = null;
        for (LevelBean bean : provinces) {
            if (provinceName != null && provinceName.contains(bean.name)) {
                province = bean;
                break;
            }
        }
        beans.put(0, province);
        if (province == null) {
            return beans;
        }

        //-------------------匹配市
        List<LevelBean> cities = getCitiesById(province.code);
        if (cities == null) {
            return beans;
        }
        LevelBean city = null;
        for (LevelBean ci : cities) {
            if (cityName != null && cityName.contains(ci.name)) {
                city = ci;
                break;
            }
        }
        beans.put(1, city);
        if (city == null) {
            return beans;
        }

        //------------------匹配区
        List<LevelBean> areas = getAreasById(province.code, city.code);
        if (areas == null) {
            return beans;
        }
        LevelBean area = null;
        for (LevelBean ar : areas) {
            if (areaName != null && areaName.contains(ar.name)) {
                area = ar;
                break;
            }
        }
        beans.put(2, area);

        return beans;
    }


    /**
     * 只获取省份+热门
     */
    public List<LevelBean> getOnlyProvinceList(@Nullable Context context) {
        List<LevelBean> provinceList = getProvinces(context);
        List<LevelBean> hotList = getHotCityList();
        LevelBean hotBean = new LevelBean();
        hotBean.name = "推荐";
        hotBean.subLevelModeList = hotList;
        provinceList.add(0, hotBean);
        return provinceList;
    }


    /**
     * 处理高德返回的城市名
     * 过滤特殊城市
     */

    public String handleCityName(String name) {

        if ((LText.empty(name))) {
            return null;
        }

        if (name.endsWith("省") || name.endsWith("地区") || name.endsWith("自治州")
                || name.endsWith("旗") || name.endsWith("盟") || name.endsWith("特区")) {
            return name;
        }

        // 这种主要是处理有些带"市"有些不带"市"
        if (!name.contains("市")) {
            name += "市";
            return name;
        }
        return name;
    }

    public String getCityCode(@Nullable Context context, String name) {
        if (LText.empty(name)) return null;
        String code;
        List<LevelBean> levelBeans = getAllCities(context);
        code = getCityCode(name, levelBeans);
        return code;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @param list
     * @return
     */
    public String getCityCode(String name, List<LevelBean> list) {
        if (list == null || list.size() <= 0) return "0";
        String code = "0";
        for (LevelBean bean : list) {
            String cityName = bean.name;
            if (LText.empty(cityName)) continue;
            cityName = handleCityName(cityName);
            if (LText.equal(cityName, name)) {
                code = String.valueOf(bean.code);
            }
        }
        return code;
    }

    public String getSubCityCode(@Nullable Context context, String name, String subName, int type) {
        List<LevelBean> list = getAllCities(context);
        if (list == null || list.size() <= 0) return "0";
        String code = "0";
        for (LevelBean bean : list) {
            String cityName = bean.name;
            if (LText.empty(cityName)) continue;

            cityName = handleCityName(cityName);

            if (LText.equal(cityName, name)) {
                if (!LList.isEmpty(bean.subLevelModeList)) {
                    for (LevelBean subBean : bean.subLevelModeList) {
                        String subCityName = subBean.name;
                        if (LText.empty(subCityName)) continue;
                        if ((subCityName.contains(subName) || subName.contains(subCityName)) && subBean.cityType == type) {
                            code = String.valueOf(subBean.code);
                            break;

                        }
                    }
                }
            }
        }
        return code;
    }
}
