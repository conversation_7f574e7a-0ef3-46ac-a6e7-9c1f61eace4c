
package com.basedata.businessdata;


import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.monch.lbase.util.LList;

import org.json.JSONArray;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.List;

import static com.basedata.core.BasicDataConstants.BusinessDataFileName.KEY_MMKV_SUBWAY_LIST;

/**
 * <AUTHOR>
 * @date 2019-08-20
 * 地铁数据
 **/
public class SubwayDataFactory extends BusinessDataFactory<LevelBean> {

    public static final int SUBWAY_LIST_CODE_BASE = 12;
    SoftReference<List<LevelBean>> subwayListReference;


    private SubwayDataFactory() {
    }

    private static volatile SubwayDataFactory instance;

    public static final SubwayDataFactory getInstance() {

        if (instance == null) {
            synchronized (SubwayDataFactory.class) {
                if (instance == null) {
                    instance = new SubwayDataFactory();
                }
            }
        }
        return instance;
    }

    /**
     * 存储地铁数据列表
     *
     * @param jsonArray : dataBase.json中的jsonArray
     */
    public boolean saveSubwayList(JSONArray jsonArray, float versionCode) {
        List<LevelBean> list = convertToBusinessList(jsonArray, INDEX_STRUCT_FIRST, TYPE_LEVEL_BEAN, KEY_MMKV_SUBWAY_LIST);
        return saveListJson(list, KEY_MMKV_SUBWAY_LIST, versionCode);
    }

    /**
     * 获取地铁数据列表
     */
    public List<LevelBean> getSubwayList() {
        return getJsonList(subwayListReference, KEY_MMKV_SUBWAY_LIST, LevelBean.class);
    }

    /**
     * 获取某个城市下的地铁数据
     *
     * @param cityCode 城市码
     * @return
     */
    public LevelBean getSubwayOnSpecifiedCity(long cityCode) {

        List<LevelBean> tempList = new ArrayList<>();
        if (subwayListReference != null && !LList.isEmpty(subwayListReference.get())) {
            tempList = subwayListReference.get();
        } else {
            tempList = getSubwayList();
        }

        if (LList.isEmpty(tempList)) {
            return null;
        }

        for (LevelBean bean : tempList) {
            if (bean.code == cityCode) {
                return bean;
            }
        }
        return null;
    }
}
