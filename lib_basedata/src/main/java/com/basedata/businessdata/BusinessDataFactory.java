package com.basedata.businessdata;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.alibaba.fastjson.JSON;
import com.basedata.core.AssetFileUtils;
import com.basedata.core.BasicDataProcessAction;
import com.basedata.core.ExceptionConstants;
import com.basedata.core.StringZipUtils;
import com.basedata.core.VersionController;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.List;

import static com.basedata.core.ExceptionConstants.ACTION_BASEDATA_EXCEPTION;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_READ_BUSINESS;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_READ_MMKV;
import static com.basedata.core.ExceptionConstants.BASEDATA_LOG_SAVE_MMKV;

/**
 * <AUTHOR>
 * @date 2019-08-20
 * 基础数据整理优化 mmkv
 * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=48269533
 **/
public abstract class BusinessDataFactory<T> {

    public static final int INDEX_STRUCT_FIRST = 1;

    public static final int TYPE_LEVEL_BEAN = 101;
    public static final int TYPE_FILTRBEAN_BEAN = 102;


    /**
     * 存储 listString
     *
     * @param list
     */
    @Nullable
    @WorkerThread
    public static boolean saveListJson(List list, String mmkvKey, float versionCode) {

        boolean success = false;

        int versionStatus = VersionController.checkFileVersion(mmkvKey, versionCode);

        if (versionStatus == VersionController.VERSION_CODE_INIT_VERSION || versionStatus == VersionController.VERSION_CODE_NEW_VERSION || versionStatus == VersionController.VERSION_CODE_COVER_VERSION) {
            if (!LList.isEmpty(list)) {
                try {
                    com.alibaba.fastjson.JSONArray jsonArray = new com.alibaba.fastjson.JSONArray(list);
                    if (jsonArray != null) {
                        // 存储业务json : 业务key_版本号
                        if (saveToMMKV(createMmkvKey(mmkvKey, versionCode), jsonArray.toJSONString())) {
                            // 需要根据规则删除最旧版本的数据
                            if (versionStatus == VersionController.VERSION_CODE_COVER_VERSION) {
                                removeMMKVData(createMmkvKey(mmkvKey, VersionController.getOldestVersion(mmkvKey)));
                            }
                            // 更新版本索引
                            if (VersionController.updateVersionQueue(mmkvKey, versionCode, versionStatus)) {
                                success = true;
                            } else {
                                TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : update version failure : throwable, filename=[%s],versioncode=[%f]", mmkvKey, versionCode);
                                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                                        .param("p2", mmkvKey)
                                        .param("p3", "update_version_failure")
                                        .report();
                            }
                        } else {
                            TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : save mmkv failure : throwable, filename=[%s],versioncode=[%f]", mmkvKey, versionCode);
                            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                                    .param("p2", mmkvKey)
                                    .param("p3", "save_mmkv_failure")
                                    .report();
                        }
                    } else {
                        TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : jsonArray is null : throwable, filename=[%s],versioncode=[%f]", mmkvKey, versionCode);
                        ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                                .param("p2", mmkvKey)
                                .param("p3", "null_jsonArray")
                                .report();
                    }
                } catch (Throwable e) {
                    TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : saveListJson : throwable, filename=[%s],versioncode=[%f],erroinfo=[%s]", mmkvKey, versionCode, e.toString());
                    ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                            .param("p2", mmkvKey)
                            .param("p3", e.toString())
                            .report();
                }
            } else {
                TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : saveListJson : listempthy, filename=[%s],versioncode=[%f]", mmkvKey, versionCode);
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                        .param("p2", mmkvKey)
                        .param("p3", "业务数据为空")
                        .report();
            }
        }

        if (versionStatus == VersionController.VERSION_CODE_EQUALS) {
            success = true;
        } else if (versionStatus == VersionController.VERSION_CODE_ERROR) {
            TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : versionCodeError : listempthy, filename=[%s],versioncode=[%f]", mmkvKey, versionCode);
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEBUSINESS)
                    .param("p2", mmkvKey)
                    .param("p3", "version_code_error")
                    .report();
        }

        return success;
    }


    /**
     * 读取mmkv中的jsonString并转换为业务list
     *
     * @param mmkvKey
     * @param clazz
     */
    @Nullable
    @WorkerThread
    public List<T> getJsonList(SoftReference<List<T>> softReference, String mmkvKey, Class<T> clazz) {

        List<T> list = new ArrayList<>();

        try {
            String code = VersionController.getCurrentVersion(mmkvKey);
            if (!TextUtils.isEmpty(code)) {
                String listStr = getJsonStr(mmkvKey, code);
                if (TextUtils.isEmpty(listStr)) {
                    TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : getJsonStr(mmkvKey,code), filename=[%s]", mmkvKey);
                    BasicDataProcessAction.getInstance().onGetListFailure(mmkvKey,"onEmptyJsonString");
                } else {
                    list = JSON.parseArray(listStr, clazz);

                    if (LList.isEmpty(list)) {
                        TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : parseArray(listStr, clazz), filename=[%s],class=[%s]", mmkvKey, clazz.getName());
                        ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_READBUSINESS)
                                .param("p3", "业务数据读取失败")
                                .param("p2", mmkvKey)
                                .param("p4", "JSON.parseArray(listStr, clazz)")
                                .param("p5", clazz.getName())
                                .report();
                        BasicDataProcessAction.getInstance().onGetListFailure(mmkvKey,"onEmptyDataList");
                    }
                }
            } else {
                TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : VersionController.getCurrentVersion(mmkvKey), filename=[%s],class=[%s]", mmkvKey, clazz.getName());
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_READBUSINESS)
                        .param("p3", "业务数据读取失败")
                        .param("p2", mmkvKey)
                        .param("p4", "VersionController.getCurrentVersion(mmkvKey)")
                        .report();
                BasicDataProcessAction.getInstance().onGetListFailure(mmkvKey,"onEmptyVersionCode");
            }

        } catch (Throwable e) {
            // 从mmkv 读取 文件并转换为jsonlist
            TLog.error(BASEDATA_LOG_READ_BUSINESS, "erro : getJsonList throwable, filename=[%s],throwable=[%s]", mmkvKey, e.toString());
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_READBUSINESS)
                    .param("p3", "业务数据读取失败")
                    .param("p2", mmkvKey)
                    .param("p4", e.toString())
                    .param("p5", "尚未写入")
                    .report();
            BasicDataProcessAction.getInstance().onGetListFailure(mmkvKey,"onDispatchFailure");
        }
        return list;
    }

    // 递归获取上个version的数据，降级时使用
    public String getJsonStr(String mmkvKey, String versionCode) {

        String listStr = readMMKVData(createMmkvKey(mmkvKey, versionCode), "");
        if (TextUtils.isEmpty(listStr)) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_READBUSINESS)
                    .param("p3", "业务数据读取失败")
                    .param("p2", mmkvKey)
                    .param("p4", "getJsonStr(mmkvKey,code)")
                    .param("p5", versionCode)
                    .report();
            String decreaseCode = VersionController.deCreaseGrade(mmkvKey, versionCode);
            if (TextUtils.isEmpty(decreaseCode)) {
                // 降级失败,未找到备份
                return "";
            } else {
                return getJsonStr(mmkvKey, decreaseCode);
            }
        } else {
            return listStr;
        }
    }

    /**
     * @param key
     * @param jsonString
     * @apiNote mmkv存储json
     **/
    protected static boolean saveToMMKV(String key, String jsonString) {
        try {
            String zipStr = StringZipUtils.compress(jsonString, "UTF-8");
            boolean result = SpManager.get().global(key).edit().putString(key, zipStr).commit();
            if (!result) {
                TLog.error(BASEDATA_LOG_SAVE_MMKV, "erro : read mmkv fail, filename=[%s]", key);
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_SAVEMMKV)
                        .param("p3", "save mmkv fail")
                        .param("p4", "saveToMMKV(String key, String jsonString)")
                        .param("p5", key)
                        .report();
            } else {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * @param key
     * @param jsonString
     * @apiNote mmkv读取json
     **/
    protected String readMMKVData(String key, String jsonString) {

        String result = SpManager.get().global(key).getString(key, jsonString);
        if (TextUtils.isEmpty(result)) {
            TLog.error(BASEDATA_LOG_READ_MMKV, "erro : read mmkv fail, filename=[%s]", key);
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_READMMKV)
                    .param("p3", "read mmkv fail")
                    .param("p4", "readMMKVData(String key, String jsonString)")
                    .param("p5", key)
                    .report();
            return result;
        }
        String unzipresult = "";
        try {
            unzipresult = StringZipUtils.uncompress(result, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return unzipresult;
    }

    protected static boolean removeMMKVData(String key) {
        return SpManager.get().global(key).edit().clear().commit();
    }


    /**
     * 转换jsonArray为业务list集合
     **/
    public List<T> convertToBusinessList(JSONArray jsonArray, int structIndex, int beanType, String mmkvKey) {

        if (jsonArray == null) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_PARSEJSON)
                    .param("p3", "jsonArray为空")
                    .param("p4", mmkvKey)
                    .report();
        }
        List<T> businessList = new ArrayList<>();
        try {
            if (jsonArray != null && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jo = jsonArray.optJSONObject(i);
                    if (jo == null) continue;
                    T bean = (T) getBeanFromJson(jo, jo.optLong("code"), structIndex, beanType);
                    businessList.add(bean);
                }
            }
        } catch (Throwable e) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_PARSEJSON)
                    .param("p3", e.toString())
                    .param("p4", mmkvKey)
                    .report();
        }
        if (LList.isEmpty(businessList)) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_PARSEJSON)
                    .param("p3", "empty_list")
                    .param("p4", mmkvKey)
                    .report();
        }
        return businessList;
    }

    /**
     * 转换jsonObjcet为业务对象
     * 暂时没有转换为Object的需要
     **/
    public void convertToBusinessObject(JSONObject object, Class clazz) {
    }

    /**
     * 筛选所需要的集合
     * 暂时没有用到筛选功能
     **/
    public void filterList(List<T> originList, int filerType) {

    }


    /**
     * 根据JSONOBJECT获取LevelBean
     *
     * @param jsonObject
     * @return
     */
    protected Object getBeanFromJson(JSONObject jsonObject, long parentId, int structIndex, int beanType) {
        // 由于Levelbean 和 filterBean结构不同，且没有共同的superclass 和 interface ,所以用beanType区分来写
        if (beanType == TYPE_LEVEL_BEAN) {
            return getLevelBeanFromJson(jsonObject, parentId, structIndex);
        }
        if (beanType == TYPE_FILTRBEAN_BEAN) {
            return getFilterBeanFromJson(jsonObject, parentId, structIndex);
        }
        return null;
    }

    /**
     * 根据JSONOBJECT获取LevelBean
     *
     * @param jsonObject
     * @return
     */
    protected LevelBean getLevelBeanFromJson(JSONObject jsonObject, long parentId, int structIndex) {

        LevelBean bean = new LevelBean();
        bean.code = jsonObject.optLong("code");
        bean.name = jsonObject.optString("name");
        bean.firstChar = jsonObject.optString("firstChar");
        bean.mark = jsonObject.optInt("mark");
        bean.positionType = jsonObject.optInt("positionType");
        bean.cityType = jsonObject.optInt("cityType");
        bean.capital = jsonObject.optInt("capital");
        bean.color = jsonObject.optString("color");
        bean.value = jsonObject.optString("value");
        bean.parentId = parentId;
        bean.structIndex = structIndex;


        JSONArray jaSubLevelModeList = jsonObject.optJSONArray("subLevelModelList");

        if (jaSubLevelModeList != null) {
            int count = jaSubLevelModeList.length();
            List<LevelBean> list = new ArrayList<>(count);
            for (int i = 0; i < count; i++) {
                JSONObject joSubLevelMode = jaSubLevelModeList.optJSONObject(i);
                LevelBean e = getLevelBeanFromJson(joSubLevelMode, bean.code, structIndex + 1); // 递归,层级加一
                list.add(e);
            }
            bean.subLevelModeList = list;
        }
        return bean;
    }


    /**
     * 根据JSONOBJECT获取FilterBean
     *
     * @param jsonObject
     * @return
     */
    protected FilterBean getFilterBeanFromJson(JSONObject jsonObject, long parentId, int structIndex) {

        FilterBean bean = new FilterBean();
        bean.code = jsonObject.optLong("code");
        bean.name = jsonObject.optString("name");
        bean.paramName = jsonObject.optString("paramName");
        bean.parentId = parentId;
        bean.structIndex = structIndex;

        JSONArray subFilterConfigModel = jsonObject.optJSONArray("subFilterConfigModel");

        if (subFilterConfigModel != null) {
            int count = subFilterConfigModel.length();
            List<FilterBean> list = new ArrayList<>(count);
            for (int i = 0; i < count; i++) {
                JSONObject joSubLevelMode = subFilterConfigModel.optJSONObject(i);
                FilterBean e = getFilterBeanFromJson(joSubLevelMode, bean.code, structIndex + 1); // 递归,层级加一
                list.add(e);
            }
            bean.subFilterConfigModel = list;
        }
        return bean;
    }

    /**
     * 获取一个JsonObject
     */
    protected JSONObject getJsonObject(String json) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(json);
        } catch (JSONException e) {
            jsonObject = null;
        }
        return jsonObject;
    }

    /**
     * 获取一个JsonArray
     */
    protected JSONArray getJsonArray(String arrayString) {
        if (LText.isEmptyOrNull(arrayString)) {
            return null;
        } else {
            try {
                JSONArray array = new JSONArray(arrayString);
                return array;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String createMmkvKey(String key, float versionCode) {
        return key + "_" + versionCode;
    }

    public static String createMmkvKey(String key, String versionCode) {
        return createMmkvKey(key, Float.parseFloat(versionCode));
    }

    /**
     * 1311.92372:【基础数据】对地铁数据获取失败采用 assets 来兜底
     *
     * @param context        当前界面
     * @param assetsFileName assets文件名
     * @param mmkvKey        业务 Key 值
     * @param beanType       数据类型
     * @return 数据列表
     */
    @Nullable
    protected List<T> getAssetsDataList(@Nullable Context context, @NonNull String assetsFileName, @NonNull String assetsDataName, @NonNull String mmkvKey, int beanType) {
        try {
            if (context == null) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "context == null")
                        .report();
                return null;
            }
            String assetsJsonString = AssetFileUtils.getAssetFile(assetsFileName, context);
            if (LText.isEmptyOrNull(assetsJsonString)) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "assetsJsonString == empty")
                        .report();
                return null;
            }
            JSONObject jsonObject = new JSONObject(assetsJsonString);
            JSONArray jsonArray = jsonObject.optJSONArray(assetsDataName);
            if (jsonArray == null) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "jsonArray == null")
                        .report();
                return null;
            }
            if (jsonArray.length() <= 0) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "jsonArray isEmpty")
                        .report();
                return null;
            }
            List<T> resultList = convertToBusinessList(jsonArray, INDEX_STRUCT_FIRST, beanType, mmkvKey);
            if (LList.isEmpty(resultList)) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "resultList isEmpty")
                        .report();
                return null;
            } else {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                        .param("p2", mmkvKey)
                        .param("p3", "onSuccess")
                        .report();
                return resultList;
            }
        } catch (Exception e) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_ON_READ_ASSETS_RESULT)
                    .param("p2", mmkvKey)
                    .param("p3", "onException")
                    .param("p4", e.toString())
                    .report();
            return null;
        }
    }

}